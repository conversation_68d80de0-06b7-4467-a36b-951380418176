package com.sankuai.dzshoppingguide.product.detail.spi.dtos;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;

@Data
@MobileDo(id = 0xf43d)
public class AbConfig implements Serializable {
    /**
    * 实验上报信息
    */
    @MobileDo.MobileField(key = 0xddc3)
    private String expBiInfo;

    /**
    * 实验结果
    */
    @MobileDo.MobileField(key = 0x9263)
    private String expResult;

    /**
    * 实验ID
    */
    @MobileDo.MobileField(key = 0x85df)
    private String expId;

    public String getExpBiInfo() {
        return expBiInfo;
    }

    public void setExpBiInfo(String expBiInfo) {
        this.expBiInfo = expBiInfo;
    }

    public String getExpResult() {
        return expResult;
    }

    public void setExpResult(String expResult) {
        this.expResult = expResult;
    }

    public String getExpId() {
        return expId;
    }

    public void setExpId(String expId) {
        this.expId = expId;
    }
}