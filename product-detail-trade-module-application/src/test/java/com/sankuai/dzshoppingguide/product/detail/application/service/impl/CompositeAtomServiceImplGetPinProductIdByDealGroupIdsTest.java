package com.sankuai.dzshoppingguide.product.detail.application.service.impl;

import static org.junit.Assert.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import static org.powermock.api.mockito.PowerMockito.when;
import com.dianping.pigeon.remoting.invoker.concurrent.InvocationCallback;
import com.dianping.tpfun.product.api.sku.pintuan.PinFacadeService;
import com.dianping.tpfun.product.api.sku.pintuan.request.GetPinProductIdOptional;
import com.meituan.inf.xmdlog.XMDLogFormat;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonClient;
import com.sankuai.dzshoppingguide.product.detail.domain.utils.PigeonCallbackUtils;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

@RunWith(PowerMockRunner.class)
@PrepareForTest({ PigeonCallbackUtils.class, XMDLogFormat.class })
@PowerMockIgnore({ "javax.management.*", "javax.crypto.*" })
public class CompositeAtomServiceImplGetPinProductIdByDealGroupIdsTest {

    @Mock
    private PinFacadeService pinFacadeServiceFuture;

    @InjectMocks
    private CompositeAtomServiceImpl compositeAtomService;

    @Before
    public void setUp() {
        PowerMockito.mockStatic(PigeonCallbackUtils.class);
        PowerMockito.mockStatic(XMDLogFormat.class);
    }

    /**
     * Test case for empty input list
     */
    @Test
    public void testGetPinProductIdByDealGroupIdsWithEmptyList() throws Throwable {
        // arrange
        List<Long> emptyList = Collections.emptyList();
        // act & assert
        try {
            compositeAtomService.getPinProductIdByDealGroupIds(emptyList);
            org.junit.Assert.fail("Expected IllegalArgumentException");
        } catch (IllegalArgumentException e) {
            org.junit.Assert.assertEquals("dealGroupIds为空!!!", e.getMessage());
        }
    }

    /**
     * Test case for successful execution
     */
    @Test
    @SuppressWarnings("unchecked")
    public void testGetPinProductIdByDealGroupIdsSuccess() throws Throwable {
        // arrange
        List<Long> dealGroupIds = new ArrayList<>();
        dealGroupIds.add(1L);
        dealGroupIds.add(2L);
        dealGroupIds.add(3L);
        Map<Long, Long> expectedResult = new HashMap<>();
        expectedResult.put(1L, 1001L);
        expectedResult.put(2L, 1002L);
        expectedResult.put(3L, 1003L);
        CompletableFuture<Map<Long, Long>> future = new CompletableFuture<>();
        PowerMockito.doReturn(future).when(PigeonCallbackUtils.class);
        PigeonCallbackUtils.setPigeonCallback();
        // act
        CompletableFuture<Map<Long, Long>> resultFuture = compositeAtomService.getPinProductIdByDealGroupIds(dealGroupIds);
        // simulate callback success
        future.complete(expectedResult);
        // assert
        org.junit.Assert.assertEquals(expectedResult, resultFuture.get());
        verify(pinFacadeServiceFuture).getPinProductIdByLongDealGroupIds(eq(dealGroupIds), any(GetPinProductIdOptional.class));
    }

    /**
     * Test case for service exception
     */
    @Test
    @SuppressWarnings("unchecked")
    public void testGetPinProductIdByDealGroupIdsServiceThrowsException() throws Throwable {
        // arrange
        List<Long> dealGroupIds = new ArrayList<>();
        dealGroupIds.add(1L);
        dealGroupIds.add(2L);
        dealGroupIds.add(3L);
        RuntimeException serviceException = new RuntimeException("Service error");
        CompletableFuture<Map<Long, Long>> future = new CompletableFuture<>();
        PowerMockito.doReturn(future).when(PigeonCallbackUtils.class);
        PigeonCallbackUtils.setPigeonCallback();
        XMDLogFormat mockFormat = PowerMockito.mock(XMDLogFormat.class);
        PowerMockito.when(XMDLogFormat.build()).thenReturn(mockFormat);
        PowerMockito.when(mockFormat.putTag(any(), any())).thenReturn(mockFormat);
        PowerMockito.when(mockFormat.message(any())).thenReturn("");
        // act
        CompletableFuture<Map<Long, Long>> resultFuture = compositeAtomService.getPinProductIdByDealGroupIds(dealGroupIds);
        // simulate callback failure
        future.completeExceptionally(serviceException);
        // assert
        org.junit.Assert.assertNull(resultFuture.get());
        verify(pinFacadeServiceFuture).getPinProductIdByLongDealGroupIds(eq(dealGroupIds), any(GetPinProductIdOptional.class));
        verify(mockFormat).putTag("scene", "compositeAtomService");
        verify(mockFormat).putTag("method", "getPinProductIdByDealGroupIds");
    }

    /**
     * Test case for null result
     */
    @Test
    @SuppressWarnings("unchecked")
    public void testGetPinProductIdByDealGroupIdsReturnsNull() throws Throwable {
        // arrange
        List<Long> dealGroupIds = new ArrayList<>();
        dealGroupIds.add(1L);
        dealGroupIds.add(2L);
        dealGroupIds.add(3L);
        CompletableFuture<Map<Long, Long>> future = new CompletableFuture<>();
        PowerMockito.doReturn(future).when(PigeonCallbackUtils.class);
        PigeonCallbackUtils.setPigeonCallback();
        // act
        CompletableFuture<Map<Long, Long>> resultFuture = compositeAtomService.getPinProductIdByDealGroupIds(dealGroupIds);
        // simulate callback with null result
        future.complete(null);
        // assert
        org.junit.Assert.assertNull(resultFuture.get());
        verify(pinFacadeServiceFuture).getPinProductIdByLongDealGroupIds(eq(dealGroupIds), any(GetPinProductIdOptional.class));
    }

    /**
     * Test case for single item input
     */
    @Test
    @SuppressWarnings("unchecked")
    public void testGetPinProductIdByDealGroupIdsWithSingleItem() throws Throwable {
        // arrange
        List<Long> dealGroupIds = new ArrayList<>();
        dealGroupIds.add(1L);
        Map<Long, Long> expectedResult = Collections.singletonMap(1L, 1001L);
        CompletableFuture<Map<Long, Long>> future = new CompletableFuture<>();
        PowerMockito.doReturn(future).when(PigeonCallbackUtils.class);
        PigeonCallbackUtils.setPigeonCallback();
        // act
        CompletableFuture<Map<Long, Long>> resultFuture = compositeAtomService.getPinProductIdByDealGroupIds(dealGroupIds);
        // simulate callback success
        future.complete(expectedResult);
        // assert
        org.junit.Assert.assertEquals(expectedResult, resultFuture.get());
        verify(pinFacadeServiceFuture).getPinProductIdByLongDealGroupIds(eq(dealGroupIds), any(GetPinProductIdOptional.class));
    }
}
