package com.sankuai.dzshoppingguide.product.detail.application.fetcher.discountcard;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dz.product.detail.gateway.spi.enums.ProductTypeEnum;
import com.sankuai.dzcard.navigation.api.dto.QueryDiscountCardReq;
import com.sankuai.dzcard.navigation.api.enums.LiveStreamEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.shopid.ShopIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.shopid.ShopIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
import com.sankuai.dzshoppingguide.product.detail.application.utils.ParamsUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/2/28 17:49
 */
@Fetcher(previousLayerDependencies = {ShopIdMapperFetcher.class})
@Slf4j
public class DealDiscountCardFetcher extends NormalFetcherContext<DealDiscountCardResult> {
    @Autowired
    private CompositeAtomService compositeAtomService;

    private long mtShopId;
    private long dpShopId;

    @Override
    // @ProductTypeRequired(ProductTypeEnum.DEAL)
    protected CompletableFuture<DealDiscountCardResult> doFetch() {
        if (request.getProductTypeEnum() != ProductTypeEnum.DEAL) {
            return CompletableFuture.completedFuture(null);
        }
        ShopIdMapper shopIdMapper = getDependencyResult(ShopIdMapperFetcher.class);
        mtShopId = Optional.ofNullable(shopIdMapper).map(ShopIdMapper::getMtBestShopId).orElse(0L);
        dpShopId = Optional.ofNullable(shopIdMapper).map(ShopIdMapper::getDpBestShopId).orElse(0L);
        return compositeAtomService.queryDiscountCardInfo(buildReq()).thenApply(res -> {
            if (res == null) {
                return null;
            }
            return new DealDiscountCardResult(res);
        });
    }

    private QueryDiscountCardReq buildReq() {
        QueryDiscountCardReq req = new QueryDiscountCardReq();
        if (com.sankuai.dz.product.detail.RequestSourceEnum.fromLive(request.getPageSource())) {
            // 直播间的流量
            Map<String, List<String>> map = Maps.newHashMap();
            map.put(LiveStreamEnum.BLACKLIST.getCode(), Lists.newArrayList(LiveStreamEnum.LIVE_STREAMING.getCode()));
            Map<String,String> extMap = Maps.newHashMap();
            extMap.put(LiveStreamEnum.TRAFFICFLAG.getCode(),JsonCodec.encode(map));
            req.setExtMap(JsonCodec.encode(extMap));
        }
        req.setPlatform(request.getPlatformEnum().getCode());
        req.setUserId(request.getUserId());
        req.setUnionId(request.getShepherdGatewayParam().getUnionid());
        req.setShopId(request.getClientTypeEnum().isMtClientType() ? mtShopId : dpShopId);
        req.setDealGroupId((int)request.getProductId());
        req.setCityId(request.getCityId());
        req.setClientType(ParamsUtils.getClientType(request.getClientTypeEnum(), request.getMobileOSType()));
        req.setDpId(request.getShepherdGatewayParam().getDeviceId());
        req.setClientVersion(request.getShepherdGatewayParam().getAppVersion());
        // todo
        // req.setVersion();
        return req;
    }
}
