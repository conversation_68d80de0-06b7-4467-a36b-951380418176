package com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.handler.cepintuan;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.cepinpool.CostEffectivePinPoolFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.cepinpool.dto.CostEffectivePinTuan;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.BaseOrderPageUrlFetcher;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: guangyujie
 * @Date: 2025/3/20 16:48
 */
@Fetcher(
        previousLayerDependencies = {
                CostEffectivePinPoolFetcher.class
        }
)
@Slf4j
public class CEPinTuanOrderPageUrlFetcher extends BaseOrderPageUrlFetcher {

    private CostEffectivePinTuan costEffectivePinTuan;

    @Override
    protected boolean isContinue() {
        costEffectivePinTuan = getDependencyResult(CostEffectivePinPoolFetcher.class);
        if (costEffectivePinTuan == null) {
            return false;
        }
        return costEffectivePinTuan.isCePinTuanScene();
    }

    @Override
    protected Map<String, String> buildCustomExtParams() {
        Map<String, String> customExtParams = new HashMap<>();
        if (StringUtils.isNotBlank(costEffectivePinTuan.getShareToken())) {
            customExtParams.put("orderGroupId", costEffectivePinTuan.getShareToken());
        }
        if (costEffectivePinTuan.getPromotionId() > 0) {
            customExtParams.put("promotionId", String.valueOf(costEffectivePinTuan.getPromotionId()));
        }
        if (StringUtils.isNotBlank(costEffectivePinTuan.getPinTuanActivityId())) {
            customExtParams.put("pintuanActivityId", String.valueOf(costEffectivePinTuan.getPinTuanActivityId()));
        }
        customExtParams.put("groupType", "2");
        return customExtParams;
    }

}
