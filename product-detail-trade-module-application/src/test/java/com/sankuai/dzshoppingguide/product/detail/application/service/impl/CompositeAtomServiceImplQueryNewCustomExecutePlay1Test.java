package com.sankuai.dzshoppingguide.product.detail.application.service.impl;

import static org.junit.Assert.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import static org.powermock.api.mockito.PowerMockito.when;
import com.google.common.util.concurrent.SettableFuture;
import com.meituan.service.mobile.mtthrift.callback.OctoThriftCallback;
import com.meituan.service.mobile.mtthrift.netty.ContextStore;
import com.sankuai.dzshoppingguide.product.detail.domain.utils.ThriftAsyncUtils;
import com.sankuai.mktplay.center.mkt.play.center.client.PlayCenterService;
import com.sankuai.mktplay.center.mkt.play.center.client.PlayExecuteRequest;
import com.sankuai.mktplay.center.mkt.play.center.client.PlayExecuteResponse;
import com.sankuai.mktplay.center.mkt.play.center.client.UserInfo;
import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import org.apache.thrift.TException;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.modules.junit4.PowerMockRunnerDelegate;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(PowerMockRunner.class)
@PrepareForTest({ ContextStore.class })
@PowerMockIgnore({ "javax.management.*", "javax.crypto.*" })
public class CompositeAtomServiceImplQueryNewCustomExecutePlay1Test {

    private PlayCenterService.AsyncIface playCenterServiceFuture;

    private CompositeAtomServiceImpl compositeAtomService;

    private PlayExecuteRequest playExecuteRequest;

    private PlayExecuteResponse playExecuteResponse;

    private SettableFuture<Object> settableFuture;

    @Before
    public void setUp() throws Exception {
        // Mock static class
        PowerMockito.mockStatic(ContextStore.class);
        // Setup mocks
        playCenterServiceFuture = mock(PlayCenterService.AsyncIface.class);
        compositeAtomService = new CompositeAtomServiceImpl();
        // Use reflection to set private field
        Field field = CompositeAtomServiceImpl.class.getDeclaredField("playCenterServiceFuture");
        field.setAccessible(true);
        field.set(compositeAtomService, playCenterServiceFuture);
        // Setup request
        playExecuteRequest = new PlayExecuteRequest();
        playExecuteRequest.setPlayId(123L);
        UserInfo userInfo = new UserInfo();
        userInfo.setUserId(456L);
        userInfo.setUserSource(1);
        playExecuteRequest.setUserInfo(userInfo);
        Map<String, String> requestMap = new HashMap<>();
        requestMap.put("key", "value");
        playExecuteRequest.setRequest(requestMap);
        // Setup response
        playExecuteResponse = new PlayExecuteResponse();
        playExecuteResponse.setStatus(200);
        playExecuteResponse.setResult("success");
        // Setup SettableFuture
        settableFuture = SettableFuture.create();
        when(ContextStore.getSettableFuture()).thenReturn(settableFuture);
    }

    /**
     * Test successful execution with valid response
     */
    @Test
    public void testQueryNewCustomExecutePlaySuccess() throws Throwable {
        // arrange
        doAnswer(invocation -> {
            OctoThriftCallback callback = invocation.getArgument(1);
            callback.onComplete(playExecuteResponse);
            settableFuture.set(playExecuteResponse);
            return null;
        }).when(playCenterServiceFuture).executePlay(any(), any());
        // act
        CompletableFuture<PlayExecuteResponse> future = compositeAtomService.queryNewCustomExecutePlay(playExecuteRequest);
        PlayExecuteResponse result = future.get();
        // assert
        org.junit.jupiter.api.Assertions.assertNotNull(result);
        org.junit.jupiter.api.Assertions.assertEquals(200, result.getStatus());
        org.junit.jupiter.api.Assertions.assertEquals("success", result.getResult());
        verify(playCenterServiceFuture).executePlay(eq(playExecuteRequest), any());
    }

    /**
     * Test when service throws exception
     */
    @Test
    public void testQueryNewCustomExecutePlayServiceException() throws Throwable {
        // arrange
        TException expectedException = new TException("Service error");
        doThrow(expectedException).when(playCenterServiceFuture).executePlay(any(), any());
        // act & assert
        TException thrown = org.junit.jupiter.api.Assertions.assertThrows(TException.class, () -> compositeAtomService.queryNewCustomExecutePlay(playExecuteRequest));
        org.junit.jupiter.api.Assertions.assertEquals("Service error", thrown.getMessage());
    }

    /**
     * Test error logging when exception occurs
     */
    @Test
    public void testQueryNewCustomExecutePlayErrorLogging() throws Throwable {
        // arrange
        doAnswer(invocation -> {
            OctoThriftCallback callback = invocation.getArgument(1);
            callback.onComplete(null);
            settableFuture.set(null);
            return null;
        }).when(playCenterServiceFuture).executePlay(any(), any());
        // act
        CompletableFuture<PlayExecuteResponse> future = compositeAtomService.queryNewCustomExecutePlay(playExecuteRequest);
        PlayExecuteResponse result = future.get();
        // assert
        org.junit.jupiter.api.Assertions.assertNull(result);
        verify(playCenterServiceFuture).executePlay(eq(playExecuteRequest), any());
    }
}
