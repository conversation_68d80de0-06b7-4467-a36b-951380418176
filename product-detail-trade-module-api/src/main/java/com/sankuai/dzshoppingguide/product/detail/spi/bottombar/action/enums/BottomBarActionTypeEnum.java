package com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.enums;

import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.BottomBarActionDataVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.extend.buy.BuyActionVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.extend.buy.SuckBottomBuyActionVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.extend.cepintuanshare.CEPinTuanShareActionVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.extend.none.DoNothingActionVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.extend.none.SuckBottomDoNothingActionVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.extend.redirect.SimpleRedirectActionVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.extend.redirect.SuckBottomRedirectActionVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.extend.shopping.cart.AddShoppingCardActionVO;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: guangyujie
 * @Date: 2025/3/10 13:25
 */
@Getter
public enum BottomBarActionTypeEnum {

    UNKNOWN(-1, "未知", null),
    NOTHING(0, "无动作", DoNothingActionVO.class),
    REDIRECT(1, "普通跳转", SimpleRedirectActionVO.class),
    BUY(2, "购买，跳转提单页（浮层）", BuyActionVO.class),
    ADD_SHOPPING_CARD(3, "加入购物车", AddShoppingCardActionVO.class),
    CE_PIN_TUAN_SHARE(4, "特团拼团分享", CEPinTuanShareActionVO.class),
    FLOATING_LAYER(5, "浮层动效", SuckBottomRedirectActionVO.class),
    SUCK_BOTTOM_NOTHING(6, "吸底按钮 无动作", SuckBottomDoNothingActionVO.class),
    SUCK_BOTTOM_BUY(7, "吸底按钮 购买，跳转提单页（浮层）", SuckBottomBuyActionVO.class)
    ;

    private final int code;

    private final String desc;

    private final Class<? extends BottomBarActionDataVO> actionVOClass;

    BottomBarActionTypeEnum(int code, String desc, Class<? extends BottomBarActionDataVO> actionVOClass) {
        this.code = code;
        this.desc = desc;
        this.actionVOClass = actionVOClass;
    }

    public static BottomBarActionTypeEnum fromCode(int code) {
        for (BottomBarActionTypeEnum value : BottomBarActionTypeEnum.values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        return UNKNOWN;
    }

    public static boolean containsCode(int code) {
        for (BottomBarActionTypeEnum value : BottomBarActionTypeEnum.values()) {
            if (value.getCode() == code) {
                return true;
            }
        }
        return false;
    }

    public static Map<Integer, String> buildCodeDescMap() {
        return Arrays.stream(BottomBarActionTypeEnum.values()).collect(Collectors.toMap(
                BottomBarActionTypeEnum::getCode,
                BottomBarActionTypeEnum::getDesc
        ));
    }

    public static Map<String, String> buildNameDescMap() {
        return Arrays.stream(BottomBarActionTypeEnum.values()).collect(Collectors.toMap(
                BottomBarActionTypeEnum::name,
                BottomBarActionTypeEnum::getDesc
        ));
    }

}