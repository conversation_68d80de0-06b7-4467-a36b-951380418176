package com.sankuai.dzshoppingguide.product.detail.application.utils;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;
import com.google.common.collect.Lists;
import com.sankuai.dzshoppingguide.product.detail.application.utils.model.FilterPlayActivityModel;
import com.sankuai.dzshoppingguide.product.detail.application.utils.model.PlayActivityModel;
import com.sankuai.dzshoppingguide.product.detail.application.utils.model.PlayInfoModel;
import com.sankuai.dzshoppingguide.product.detail.application.utils.model.PrizeInfoModel;
import com.sankuai.dzshoppingguide.product.detail.application.utils.model.TaskInfoModel;
import com.sankuai.dzshoppingguide.product.detail.spi.promo.vo.DealGift;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.TimeZone;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
@DisplayName("DealGiftHandler ConvertTimestamp Tests")
class DealGiftHandlerBuildGiftsTest {

    @BeforeEach
    public void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @BeforeAll
    public static void setUp() {
        // Set default timezone to ensure consistent test results
        TimeZone.setDefault(TimeZone.getTimeZone("Asia/Shanghai"));
    }

    /**
     * Test case for null input list
     */
    @Test
    public void testBuildGiftsWithNullInput() {
        // arrange
        List<PlayActivityModel> nullList = null;
        // act
        List<DealGift> result = DealGiftHandler.buildGifts(nullList);
        // assert
        assertNull(result);
    }

    /**
     * Test case for empty input list
     */
    @Test
    public void testBuildGiftsWithEmptyInput() {
        // arrange
        List<PlayActivityModel> emptyList = new ArrayList<>();
        // act
        List<DealGift> result = DealGiftHandler.buildGifts(emptyList);
        // assert
        assertNull(result);
    }

    /**
     * Test case for null PlayInfo
     */
    @Test
    public void testBuildGiftsWithNullPlayInfo() {
        // arrange
        PlayActivityModel model = new PlayActivityModel();
        List<PlayActivityModel> list = Collections.singletonList(model);
        // act
        List<DealGift> result = DealGiftHandler.buildGifts(list);
        // assert
        assertNull(result);
    }

    /**
     * Test case for null TaskInfoList
     */
    @Test
    public void testBuildGiftsWithNullTaskInfoList() {
        // arrange
        PlayActivityModel model = new PlayActivityModel();
        PlayInfoModel playInfo = new PlayInfoModel();
        model.setPlayInfo(playInfo);
        List<PlayActivityModel> list = Collections.singletonList(model);
        // act
        List<DealGift> result = DealGiftHandler.buildGifts(list);
        // assert
        assertNull(result);
    }

    /**
     * Test case for empty TaskInfoList
     */
    @Test
    public void testBuildGiftsWithEmptyTaskInfoList() {
        // arrange
        PlayActivityModel model = new PlayActivityModel();
        PlayInfoModel playInfo = new PlayInfoModel();
        playInfo.setTaskInfoList(new ArrayList<>());
        model.setPlayInfo(playInfo);
        List<PlayActivityModel> list = Collections.singletonList(model);
        // act
        List<DealGift> result = DealGiftHandler.buildGifts(list);
        // assert
        assertNull(result);
    }

    /**
     * Test case for null PrizeInfoList
     */
    @Test
    public void testBuildGiftsWithNullPrizeInfoList() {
        // arrange
        PlayActivityModel model = new PlayActivityModel();
        PlayInfoModel playInfo = new PlayInfoModel();
        TaskInfoModel taskInfo = new TaskInfoModel();
        playInfo.setTaskInfoList(Collections.singletonList(taskInfo));
        model.setPlayInfo(playInfo);
        List<PlayActivityModel> list = Collections.singletonList(model);
        // act
        List<DealGift> result = DealGiftHandler.buildGifts(list);
        // assert
        assertNull(result);
    }

    /**
     * Test case for empty PrizeInfoList
     */
    @Test
    public void testBuildGiftsWithEmptyPrizeInfoList() {
        // arrange
        PlayActivityModel model = new PlayActivityModel();
        PlayInfoModel playInfo = new PlayInfoModel();
        TaskInfoModel taskInfo = new TaskInfoModel();
        taskInfo.setPrizeInfoList(new ArrayList<>());
        playInfo.setTaskInfoList(Collections.singletonList(taskInfo));
        model.setPlayInfo(playInfo);
        List<PlayActivityModel> list = Collections.singletonList(model);
        // act
        List<DealGift> result = DealGiftHandler.buildGifts(list);
        // assert
        assertNull(result);
    }

    /**
     * Test case for successful DealGift creation with prize count 1
     */
    @Test
    public void testBuildGiftsSuccessWithPrizeCountOne() {
        // arrange
        PlayActivityModel model = new PlayActivityModel();
        PlayInfoModel playInfo = new PlayInfoModel();
        TaskInfoModel taskInfo = new TaskInfoModel();
        PrizeInfoModel prizeInfo = new PrizeInfoModel();
        prizeInfo.setPrizeName("Test Prize");
        prizeInfo.setPrizeImage("test_image.jpg");
        taskInfo.setPrizeInfoList(Collections.singletonList(prizeInfo));
        taskInfo.setPrizeCount(1);
        playInfo.setTaskInfoList(Collections.singletonList(taskInfo));
        // 2021-07-01 00:00:00
        playInfo.setEndTime(1625097600000L);
        playInfo.setActivityId(12345L);
        model.setPlayInfo(playInfo);
        List<PlayActivityModel> list = Collections.singletonList(model);
        // act
        List<DealGift> result = DealGiftHandler.buildGifts(list);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        DealGift dealGift = result.get(0);
        assertEquals("Test Prize", dealGift.getTitle());
        assertEquals("test_image.jpg", dealGift.getThumbnail());
        assertEquals("核销后发放", dealGift.getProductTag());
        assertEquals("赠品", dealGift.getSpecificTag());
        assertEquals(1, dealGift.getCouponNum());
        assertEquals("活动有效期至2021.07.01", dealGift.getTimeDesc());
        assertEquals(12345L, dealGift.getActivityId());
    }

    /**
     * Test case for successful DealGift creation with prize count greater than 1
     */
    @Test
    public void testBuildGiftsSuccessWithPrizeCountGreaterThanOne() {
        // arrange
        PlayActivityModel model = new PlayActivityModel();
        PlayInfoModel playInfo = new PlayInfoModel();
        TaskInfoModel taskInfo = new TaskInfoModel();
        PrizeInfoModel prizeInfo = new PrizeInfoModel();
        prizeInfo.setPrizeName("Test Prize");
        prizeInfo.setPrizeImage("test_image.jpg");
        taskInfo.setPrizeInfoList(Collections.singletonList(prizeInfo));
        taskInfo.setPrizeCount(3);
        playInfo.setTaskInfoList(Collections.singletonList(taskInfo));
        // 2021-07-01 00:00:00
        playInfo.setEndTime(1625097600000L);
        playInfo.setActivityId(12345L);
        model.setPlayInfo(playInfo);
        List<PlayActivityModel> list = Collections.singletonList(model);
        // act
        List<DealGift> result = DealGiftHandler.buildGifts(list);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        DealGift dealGift = result.get(0);
        assertEquals("Test Prize", dealGift.getTitle());
        assertEquals("test_image.jpg", dealGift.getThumbnail());
        assertEquals("核销后发放", dealGift.getProductTag());
        assertEquals("赠品x3", dealGift.getSpecificTag());
        assertEquals(3, dealGift.getCouponNum());
        assertEquals("活动有效期至2021.07.01", dealGift.getTimeDesc());
        assertEquals(12345L, dealGift.getActivityId());
    }

    /**
     * Tests the scenario where newCustomGift is not empty.
     * In this case, the method should return the newCustomGift list directly
     * regardless of other parameters.
     */
    @Test
    public void testBuildGiftsNewCustomGiftNotEmpty() throws Throwable {
        // arrange
        long dealGroupId = 1L;
        long poiId = 1L;
        FilterPlayActivityModel filterPlayActivityModel = new FilterPlayActivityModel();
        List<PlayActivityModel> playActivityModels = new ArrayList<>();
        DealGift gift = new DealGift();
        gift.setTitle("Test Gift");
        List<DealGift> newCustomGift = Lists.newArrayList(gift);
        // act
        List<DealGift> result = DealGiftHandler.buildGifts(dealGroupId, poiId, filterPlayActivityModel, playActivityModels, newCustomGift);
        // assert
        assertNotNull(result);
        assertEquals(newCustomGift, result);
        assertEquals(1, result.size());
        assertEquals("Test Gift", result.get(0).getTitle());
    }

    /**
     * Tests the scenario where newCustomGift is empty and summer play result is empty.
     * In this case, the method should attempt to build gifts from playActivityModels.
     */
    @Test
    public void testBuildGiftsNewCustomGiftEmptyAndResultEmpty() throws Throwable {
        // arrange
        long dealGroupId = 1L;
        long poiId = 1L;
        FilterPlayActivityModel filterPlayActivityModel = new FilterPlayActivityModel();
        List<PlayActivityModel> playActivityModels = new ArrayList<>();
        List<DealGift> newCustomGift = new ArrayList<>();
        // act
        List<DealGift> result = DealGiftHandler.buildGifts(dealGroupId, poiId, filterPlayActivityModel, playActivityModels, newCustomGift);
        // assert
        assertNull(result);
    }

    /**
     * Tests the scenario where newCustomGift is empty and summer play result exists.
     * In this case, the method should return the summer play result.
     */
    @Test
    public void testBuildGiftsNewCustomGiftEmptyAndResultNotEmpty() throws Throwable {
        // arrange
        long dealGroupId = 1L;
        long poiId = 1L;
        FilterPlayActivityModel filterPlayActivityModel = null;
        List<PlayActivityModel> playActivityModels = new ArrayList<>();
        List<DealGift> newCustomGift = new ArrayList<>();
        // act
        List<DealGift> result = DealGiftHandler.buildGifts(dealGroupId, poiId, filterPlayActivityModel, playActivityModels, newCustomGift);
        // assert
        assertNull(result);
    }

    /**
     * Test normal case with valid timestamp and pattern
     */
    @Test
    @DisplayName("Should format timestamp correctly with valid input")
    public void testConvertTimestamp_WithValidInput() throws Throwable {
        // arrange
        // 2023-01-01 08:00:00 CST
        long timestamp = 1672531200000L;
        String pattern = "yyyy-MM-dd HH:mm:ss";
        // act
        String result = DealGiftHandler.convertTimestamp(timestamp, pattern);
        // assert
        assertNotNull(result);
        assertEquals("2023-01-01 08:00:00", result);
    }

    /**
     * Test case when timestamp is 0
     */
    @Test
    @DisplayName("Should return empty string when timestamp is 0")
    public void testConvertTimestamp_WithZeroTimestamp() throws Throwable {
        // arrange
        long timestamp = 0L;
        String pattern = "yyyy-MM-dd HH:mm:ss";
        // act
        String result = DealGiftHandler.convertTimestamp(timestamp, pattern);
        // assert
        assertNotNull(result);
        assertEquals("", result);
    }

    /**
     * Test case when timestamp is negative
     */
    @Test
    @DisplayName("Should return empty string when timestamp is negative")
    public void testConvertTimestamp_WithNegativeTimestamp() throws Throwable {
        // arrange
        long timestamp = -1000L;
        String pattern = "yyyy-MM-dd HH:mm:ss";
        // act
        String result = DealGiftHandler.convertTimestamp(timestamp, pattern);
        // assert
        assertNotNull(result);
        assertEquals("", result);
    }

    /**
     * Test with different date patterns
     */
    @Test
    @DisplayName("Should format timestamp correctly with different patterns")
    public void testConvertTimestamp_WithDifferentPatterns() throws Throwable {
        // arrange
        // 2023-01-01 08:00:00 CST
        long timestamp = 1672531200000L;
        // act & assert
        assertEquals("2023-01-01", DealGiftHandler.convertTimestamp(timestamp, "yyyy-MM-dd"));
        assertEquals("01/01/2023", DealGiftHandler.convertTimestamp(timestamp, "MM/dd/yyyy"));
        assertEquals("08:00:00", DealGiftHandler.convertTimestamp(timestamp, "HH:mm:ss"));
    }

    /**
     * Test with maximum timestamp value
     */
    @Test
    @DisplayName("Should handle maximum timestamp value")
    public void testConvertTimestamp_WithMaxTimestamp() throws Throwable {
        // arrange
        long timestamp = Long.MAX_VALUE;
        String pattern = "yyyy-MM-dd HH:mm:ss";
        // act
        String result = DealGiftHandler.convertTimestamp(timestamp, pattern);
        // assert
        assertNotNull(result);
        assertFalse(result.isEmpty());
    }

    /**
     * Test with minimum valid timestamp
     */
    @Test
    @DisplayName("Should handle minimum valid timestamp")
    public void testConvertTimestamp_WithMinValidTimestamp() throws Throwable {
        // arrange
        long timestamp = 1L;
        String pattern = "yyyy-MM-dd HH:mm:ss";
        // act
        String result = DealGiftHandler.convertTimestamp(timestamp, pattern);
        // assert
        assertNotNull(result);
        assertFalse(result.isEmpty());
    }

    /**
     * Test with boundary timestamp value
     */
    @Test
    @DisplayName("Should handle boundary timestamp value")
    public void testConvertTimestamp_WithBoundaryTimestamp() throws Throwable {
        // arrange
        long timestamp = 1L;
        String pattern = "yyyy-MM-dd HH:mm:ss";
        // act
        String result = DealGiftHandler.convertTimestamp(timestamp, pattern);
        // assert
        assertNotNull(result);
        assertTrue(result.matches("\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}"));
    }
}
