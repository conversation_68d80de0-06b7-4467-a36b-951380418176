package com.sankuai.dzshoppingguide.product.detail.application.utils;

import static org.junit.jupiter.api.Assertions.*;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import org.junit.jupiter.api.Test;

public class DealCtxHelperJudgeMainAppTest {

    /**
     * 测试 judgeMainApp 方法，当 clientTypeEnum 等于 MT_APP 时，应返回 true
     */
    @Test
    public void testJudgeMainAppWithMtApp() throws Throwable {
        // arrange
        ClientTypeEnum clientTypeEnum = ClientTypeEnum.MT_APP;
        // act
        boolean result = DealCtxHelper.judgeMainApp(clientTypeEnum);
        // assert
        assertTrue(result, "MT_APP should be considered as main app");
    }

    /**
     * 测试 judgeMainApp 方法，当 clientTypeEnum 等于 DP_APP 时，应返回 true
     */
    @Test
    public void testJudgeMainAppWithDpApp() throws Throwable {
        // arrange
        ClientTypeEnum clientTypeEnum = ClientTypeEnum.DP_APP;
        // act
        boolean result = DealCtxHelper.judgeMainApp(clientTypeEnum);
        // assert
        assertTrue(result, "DP_APP should be considered as main app");
    }

    /**
     * 测试 judgeMainApp 方法，当 clientTypeEnum 不等于 MT_APP 且不等于 DP_APP 时，应返回 false
     */
    @Test
    public void testJudgeMainAppWithOtherClientType() throws Throwable {
        // arrange
        ClientTypeEnum clientTypeEnum = ClientTypeEnum.UNKNOWN;
        // act
        boolean result = DealCtxHelper.judgeMainApp(clientTypeEnum);
        // assert
        assertFalse(result, "Other client types should not be considered as main app");
    }

    /**
     * 测试 judgeMainApp 方法，当 clientTypeEnum 为 null 时，应返回 false
     */
    @Test
    public void testJudgeMainAppWithNullClientType() throws Throwable {
        // arrange
        ClientTypeEnum clientTypeEnum = null;
        // act
        boolean result = DealCtxHelper.judgeMainApp(clientTypeEnum);
        // assert
        assertFalse(result, "Null client type should not be considered as main app");
    }
}
