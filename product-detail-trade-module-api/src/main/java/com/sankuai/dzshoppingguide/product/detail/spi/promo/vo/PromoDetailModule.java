package com.sankuai.dzshoppingguide.product.detail.spi.promo.vo;


import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 移动之家：https://mobile.sankuai.com/studio/model/info/41576
 */
@Data
@MobileDo(id = 0x3721)
public class PromoDetailModule implements Serializable {

    /**
     * poiid、shopid最佳门店id
     */
    @MobileDo.MobileField(key = 0x575b)
    private long poiId;

    /**
     * 接口返回时间戳，用于前端组件（神会员相关组件）的刷新
     */
    @MobileDo.MobileField(key = 0xfdb5)
    private long responseTimestamp;

    /**
     * 神会员点位id
     */
    @MobileDo.MobileField(key = 0xb5bb)
    private String position;

    /**
     * 券组件列表
     */
    @MobileDo.MobileField(key = 0x5db6)
    private List<DztgCouponInfo> couponList;

    /**
     * 曝光券列表
     */
    @MobileDo.MobileField(key = 0x5bf6)
    private List<DztgPromoExposureInfoVO> exposureList;

    /**
     * 优惠活动列表
     */
    @MobileDo.MobileField(key = 0x4278)
    private List<PromoActivityInfoVO> promoActivityList;

    /**
     * 团单关联赠品列表
     */
    @MobileDo.MobileField(key = 0x1a04)
    private List<DealGift> dealGifts;

    /**
     * 国补优惠详细信息
     */
    @MobileDo.MobileField(key = 0x2fd8)
    private NationalSubsidyInfo nationalSubsidyInfo;

    /**
     * 国补领券栏信息
     */
    @MobileDo.MobileField(key = 0x401f)
    private NationalSubsidyPromotionBar nationalSubsidyPromotionBar;

    /**
     * 优惠最优组合明细
     */
    @MobileDo.MobileField(key = 0x1245)
    private List<DealBestPromoDetail> bestPromoDetails;

    /**
     * 优惠减负,控制新样式的展示,true为新样式,false为旧样式
     */
    @MobileDo.MobileField(key = 0xc1f3)
    private final boolean promoNewStyle = true;

    /**
     * 门市价
     */
    @MobileDo.MobileField(key = 0x6242)
    private String marketPrice;

    /**
     * 减后价
     */
    @MobileDo.MobileField(key = 0x7031)
    private String promoPrice;

    /**
     * 最终的实付价
     */
    @MobileDo.MobileField(key = 0xe949)
    private String finalPrice;

    /**
     * 市场价优惠折扣
     */
    @MobileDo.MobileField(key = 0x725e)
    private String marketPromoDiscount;

    /**
     * 优惠摘要列表
     */
    @MobileDo.MobileField(key = 0x14c7)
    private List<String> promoAbstractList;

    /**
     * 神券模型
     */
    @MobileDo.MobileField(key = 0xd0a7)
    private InflateCounponDTO inflateCounpon;
}