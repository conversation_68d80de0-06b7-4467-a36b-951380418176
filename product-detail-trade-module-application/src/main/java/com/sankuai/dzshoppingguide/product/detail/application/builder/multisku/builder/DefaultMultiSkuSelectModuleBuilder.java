package com.sankuai.dzshoppingguide.product.detail.application.builder.multisku.builder;

import com.google.common.collect.Lists;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.BuilderTypeEnum;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.extend.BaseVariableBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.constants.Constants;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.abtest.AbTestFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.abtest.AbTestReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.sku.SkuPriceFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.sku.SkuPriceReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.defaultselect.SkuDefaultSelect;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.defaultselect.SkuDefaultSelectFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.FetcherDAGStarter;
import com.sankuai.dzshoppingguide.product.detail.application.utils.AbTestUtils;
import com.sankuai.dzshoppingguide.product.detail.application.utils.DealSkuUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.MultiSkuSelectItemVO;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.MultiSkuSelectRichTextVO;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.multisku.vo.MultiSkuSelectVO;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.deal.DealGroupDealDTO;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Author: <EMAIL>
 * @Date: 2025/2/25
 */
@Builder(
        builderType = BuilderTypeEnum.VARIABLE_BUILDER,
        moduleKey = ModuleKeyConstants.MODULE_DETAIL_DEAL_MULTI_SKU_SELECT,
        startFetcher = FetcherDAGStarter.class,
        dependentFetchers = {
                SkuPriceFetcher.class,
                SkuDefaultSelectFetcher.class,
                ProductBaseInfoFetcher.class,
                AbTestFetcher.class
        }
)
public class DefaultMultiSkuSelectModuleBuilder extends BaseVariableBuilder<MultiSkuSelectVO> {

    private long selectedSkuId;

    @Override
    public MultiSkuSelectVO doBuild() {
        selectedSkuId = getDependencyResult(SkuDefaultSelectFetcher.class, SkuDefaultSelect.class)
                .map(SkuDefaultSelect::getSelectedSkuId)
                .orElse(0L);
        String expModuleName = request.getClientTypeEnum().isMtClientType() ? "MtDealMassage" : "DpDealMassage";
        AbTestReturnValue abTestReturnValue = getDependencyResult(AbTestFetcher.class);
        if (AbTestUtils.hideMultiSkuOrMakeUpEntrance(ModuleKeyConstants.MODULE_DETAIL_DEAL_MULTI_SKU_SELECT, 
                abTestReturnValue.getAbTestExpResult(expModuleName))) {
            return null;
        }
        MultiSkuSelectVO multiSkuSelectVO = new MultiSkuSelectVO();
        ProductBaseInfo baseInfo = getDependencyResult(ProductBaseInfoFetcher.class);
        SkuPriceReturnValue priceReturnValue = getDependencyResult(SkuPriceFetcher.class);
        if (Objects.isNull(priceReturnValue) || CollectionUtils.isEmpty(priceReturnValue.getPriceDisplayDTOList())) {
            return null;
        }

        List<PriceDisplayDTO> priceDisplayDTOS = priceReturnValue.getPriceDisplayDTOList();
        Map<Long, BigDecimal> skuIdPriceMap = priceDisplayDTOS.stream().collect(Collectors.toMap(p -> (long)p.getIdentity().getSkuId(), p -> p.getPrice()));
        multiSkuSelectVO.setSkuSelectItems(assembleSkuSelectItems(baseInfo, skuIdPriceMap, request.getSkuId()));
        return multiSkuSelectVO;
    }

    private List<MultiSkuSelectItemVO> assembleSkuSelectItems(ProductBaseInfo baseInfo, Map<Long, BigDecimal> skuIdPriceMap, Long requestSkuId) {
        List<MultiSkuSelectItemVO> skuSelectItems = Lists.newArrayList();
        List<DealGroupDealDTO> dealGroupDealDTOS = Optional.ofNullable(baseInfo).map(ProductBaseInfo::getDeals)
                .filter(CollectionUtils::isNotEmpty)
                .orElse(Lists.newArrayList());
        if (CollectionUtils.isEmpty(dealGroupDealDTOS)) {
            return skuSelectItems;
        }

        // 有效skuId过滤
        List<DealGroupDealDTO> dealDTOS = DealSkuUtils.getValidDealSkuList(dealGroupDealDTOS);
        if (CollectionUtils.isEmpty(dealDTOS)) {
            return skuSelectItems;
        }

        // 排序
        dealDTOS.sort((d1, d2) -> {
            AttrDTO attrDTO = d1.getAttrs().stream().filter(a -> Objects.equals(a.getName(), Constants.SELL_DIFFERENT_GRADES))
                    .findFirst().orElse(null);
            // 上游代入skuId，置顶
            if (d1.getDealId().equals(requestSkuId)) {
                return -1;
            }

            if (Constants.DAY_NIGHT_RANGE_LIST.contains(attrDTO.getValue().get(0))) {
                // 夜间档 > 白天档
                if (Objects.equals(attrDTO.getValue().get(0), Constants.NIGHT_RANGE)) {
                    return -1;
                }
                return 1;
            } else if (Constants.WEEKDAY_WEEKEND_RANGE.contains(attrDTO.getValue().get(0))) {
                // 节假日(周末) > 工作日
                if (Objects.equals(attrDTO.getValue().get(0), Constants.WEEKEND)) {
                    return -1;
                }
                return 1;
            }
            return 0;
        });

        // 结果拼装
        dealDTOS.forEach(d  -> {
            Long skuId = d.getDealId();
            String skuName = d.getBasic().getTitle();
            if (!skuIdPriceMap.containsKey(skuId)) {
                return;
            }
            AttrDTO attrDTO = d.getAttrs().stream().filter(a -> Objects.equals(a.getName(), Constants.SELL_DIFFERENT_GRADES))
                    .findFirst().orElse(null);
            MultiSkuSelectRichTextVO richTextVO = new MultiSkuSelectRichTextVO();
            richTextVO.setSymbol("￥");
            richTextVO.setSalePrice(skuIdPriceMap.get(skuId).toPlainString());

            MultiSkuSelectItemVO itemVO = new MultiSkuSelectItemVO();
            itemVO.setProductId(request.getProductId());
            String title = Objects.nonNull(attrDTO) ? attrDTO.getValue().get(0) : skuName;
            itemVO.setTitle(title + "可用");
            itemVO.setSkuId(skuId);
            itemVO.setMultiSkuSelectRichTextVO(richTextVO);
            itemVO.setSelected(skuId.equals(selectedSkuId));
            skuSelectItems.add(itemVO);
        });
        return skuSelectItems;

    }
}
