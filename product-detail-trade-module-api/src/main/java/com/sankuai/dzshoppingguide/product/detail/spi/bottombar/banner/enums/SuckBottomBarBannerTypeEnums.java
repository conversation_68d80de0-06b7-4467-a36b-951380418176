package com.sankuai.dzshoppingguide.product.detail.spi.bottombar.banner.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/04/12
 * 神券感知增强-吸底条文案类型
 */
@Getter
public enum SuckBottomBarBannerTypeEnums {
    TEXT("TEXT", "文本"),
    AMOUNT("AMOUNT", "金额");

    private final String code;

    private final String desc;

    SuckBottomBarBannerTypeEnums(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static SuckBottomBarBannerTypeEnums fromCode(String code) {
        for (SuckBottomBarBannerTypeEnums value : SuckBottomBarBannerTypeEnums.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        throw new IllegalArgumentException("无效code:" + code);
    }
}
