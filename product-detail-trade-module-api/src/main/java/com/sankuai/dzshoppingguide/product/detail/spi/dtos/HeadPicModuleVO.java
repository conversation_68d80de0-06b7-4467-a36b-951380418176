package com.sankuai.dzshoppingguide.product.detail.spi.dtos;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: guangyujie
 * @Date: 2025/1/24 15:44
 */
@Data
@TypeDoc(description = "新版预订详情页（头图模块）")
@MobileDo(id = 0x76c9)
public class HeadPicModuleVO implements Serializable {

    public HeadPicModuleVO(int type, String content) {
        this.type = type;
        this.content = content;
    }

    /**
     * 可以先不要
     */
    @FieldDoc(description = "VR-icon链接")
    @MobileDo.MobileField(key = 0x116d)
    private String vrIconUrl;

    /**
     * 可以先不要
     */
    @FieldDoc(description = "VR链接")
    @MobileDo.MobileField(key = 0xf3a0)
    private String vrUrl;

    @FieldDoc(description = "视频ID")
    @MobileDo.MobileField(key = 0xf00a)
    private String videoId;

    @FieldDoc(description = "容器尺寸")
    @MobileDo.MobileField(key = 0xfc9)
    private String scale;

    @FieldDoc(description = "描述：2是视频弹窗提醒描述")
    @MobileDo.MobileField(key = 0xfebf)
    private String desc;

    /**
     * 可以先不要
     */
    @FieldDoc(description = "雪碧图")
    @MobileDo.MobileField(key = 0xd103)
    private SpritePicModule spritePic;

    @FieldDoc(description = "类型：0：文本；1：图片；2：视频")
    @MobileDo.MobileField(key = 0x8f0c)
    private int type;

    @FieldDoc(description = "视频链接")
    @MobileDo.MobileField(key = 0xe654)
    private String videoUrl;

    @FieldDoc(description = "内容：0是普通文本；1为图片地址；2为视频截图地址")
    @MobileDo.MobileField(key = 0xcce)
    private String content;

}
