package com.sankuai.dzshoppingguide.product.detail.application.utils;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.RequestSourceEnum;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * Unit tests for {@link DealCtxHelper#isMtLiveMinApp(ClientTypeEnum)}
 */
@ExtendWith(MockitoExtension.class)
public class DealCtxHelperTest {

    /**
     * Test scenario: When clientType is MT_LIVE_XCX
     * Expected: Returns true
     */
    @Test
    @DisplayName("Should return true when client type is MT_LIVE_XCX")
    public void testIsMtLiveMinApp_WhenMtLiveXcx_ReturnsTrue() throws Throwable {
        // arrange
        ClientTypeEnum clientTypeEnum = ClientTypeEnum.MT_LIVE_XCX;
        // act
        boolean result = DealCtxHelper.isMtLiveMinApp(clientTypeEnum);
        // assert
        assertTrue(result, "Should return true for MT_LIVE_XCX client type");
    }

    /**
     * Test scenario: When clientType is MT_LIVE_ORDER_XCX
     * Expected: Returns true
     */
    @Test
    @DisplayName("Should return true when client type is MT_LIVE_ORDER_XCX")
    public void testIsMtLiveMinApp_WhenMtLiveOrderXcx_ReturnsTrue() throws Throwable {
        // arrange
        ClientTypeEnum clientTypeEnum = ClientTypeEnum.MT_LIVE_ORDER_XCX;
        // act
        boolean result = DealCtxHelper.isMtLiveMinApp(clientTypeEnum);
        // assert
        assertTrue(result, "Should return true for MT_LIVE_ORDER_XCX client type");
    }

    /**
     * Test scenario: When clientType is different from MT_LIVE_XCX and MT_LIVE_ORDER_XCX
     * Expected: Returns false
     */
    @Test
    @DisplayName("Should return false when client type is not MT_LIVE_XCX or MT_LIVE_ORDER_XCX")
    public void testIsMtLiveMinApp_WhenOtherClientType_ReturnsFalse() throws Throwable {
        // arrange
        ClientTypeEnum clientTypeEnum = ClientTypeEnum.MT_APP;
        // act
        boolean result = DealCtxHelper.isMtLiveMinApp(clientTypeEnum);
        // assert
        assertFalse(result, "Should return false for other client types");
    }

    /**
     * Test scenario: When clientType is null
     * Expected: Returns false
     */
    @Test
    @DisplayName("Should return false when client type is null")
    public void testIsMtLiveMinApp_WhenNull_ReturnsFalse() throws Throwable {
        // arrange
        ClientTypeEnum clientTypeEnum = null;
        // act
        boolean result = DealCtxHelper.isMtLiveMinApp(clientTypeEnum);
        // assert
        assertFalse(result, "Should return false for null client type");
    }

    /**
     * Test scenario: When clientType is a custom enum value
     * Expected: Returns false
     */
    @Test
    @DisplayName("Should return false when client type is custom enum value")
    public void testIsMtLiveMinApp_WhenCustomClientType_ReturnsFalse() throws Throwable {
        // arrange
        // Using an actual enum value that is not MT_LIVE_XCX or MT_LIVE_ORDER_XCX
        ClientTypeEnum clientTypeEnum = ClientTypeEnum.MT_APP;
        // act
        boolean result = DealCtxHelper.isMtLiveMinApp(clientTypeEnum);
        // assert
        assertFalse(result, "Should return false for custom client type");
    }

    /**
     * Test case where the client type is not a main app.
     */
    @Test
    public void testIsPreOrderDealNotMainApp() throws Throwable {
        // Given
        DealGroupCategoryDTO categoryDTO = new DealGroupCategoryDTO();
        ClientTypeEnum clientTypeEnum = ClientTypeEnum.DP_M;
        String pagesource = RequestSourceEnum.PRE_ORDER_DEAL.getSource();
        // When
        boolean result = DealCtxHelper.isPreOrderDeal(categoryDTO, clientTypeEnum, pagesource);
        // Then
        assertFalse(result);
    }

    /**
     * Test case where the page source is not PRE_ORDER_DEAL.
     */
    @Test
    public void testIsPreOrderDealWrongPageSource() throws Throwable {
        // Given
        DealGroupCategoryDTO categoryDTO = new DealGroupCategoryDTO();
        ClientTypeEnum clientTypeEnum = ClientTypeEnum.MT_APP;
        String pagesource = RequestSourceEnum.CAI_XI.getSource();
        // When
        boolean result = DealCtxHelper.isPreOrderDeal(categoryDTO, clientTypeEnum, pagesource);
        // Then
        assertFalse(result);
    }

    /**
     * Test case where it's not a hit leads deal.
     * This test assumes that the LionConfigUtils.hitLeadsDeal method would return false under the given conditions.
     */
    @Test
    public void testIsPreOrderDealNotHitLeadsDeal() throws Throwable {
        // Given
        DealGroupCategoryDTO categoryDTO = new DealGroupCategoryDTO();
        // Assuming this category ID would not lead to a hit leads deal
        categoryDTO.setCategoryId(9999L);
        // Assuming this service type ID would not lead to a hit leads deal
        categoryDTO.setServiceTypeId(45678L);
        ClientTypeEnum clientTypeEnum = ClientTypeEnum.MT_APP;
        String pagesource = RequestSourceEnum.PRE_ORDER_DEAL.getSource();
        // When
        boolean result = DealCtxHelper.isPreOrderDeal(categoryDTO, clientTypeEnum, pagesource);
        // Then
        assertFalse(result);
    }

    /**
     * Test case with null clientTypeEnum.
     */
    @Test
    public void testIsPreOrderDealNullClientTypeEnum() throws Throwable {
        // Given
        DealGroupCategoryDTO categoryDTO = new DealGroupCategoryDTO();
        String pagesource = RequestSourceEnum.PRE_ORDER_DEAL.getSource();
        // When
        boolean result = DealCtxHelper.isPreOrderDeal(categoryDTO, null, pagesource);
        // Then
        assertFalse(result);
    }

    /**
     * Test case with null pagesource.
     */
    @Test
    public void testIsPreOrderDealNullPageSource() throws Throwable {
        // Given
        DealGroupCategoryDTO categoryDTO = new DealGroupCategoryDTO();
        ClientTypeEnum clientTypeEnum = ClientTypeEnum.MT_APP;
        // When
        boolean result = DealCtxHelper.isPreOrderDeal(categoryDTO, clientTypeEnum, null);
        // Then
        assertFalse(result);
    }

    /**
     * Test judgeMainApp method when clientTypeEnum is MT_APP
     */
    @Test
    public void testJudgeMainAppWithMtApp() throws Throwable {
        // arrange
        ClientTypeEnum clientTypeEnum = ClientTypeEnum.MT_APP;
        // act
        boolean result = DealCtxHelper.judgeMainApp(clientTypeEnum);
        // assert
        assertTrue(result, "MT_APP should be considered as main app");
    }

    /**
     * Test judgeMainApp method when clientTypeEnum is DP_APP
     */
    @Test
    public void testJudgeMainAppWithDpApp() throws Throwable {
        // arrange
        ClientTypeEnum clientTypeEnum = ClientTypeEnum.DP_APP;
        // act
        boolean result = DealCtxHelper.judgeMainApp(clientTypeEnum);
        // assert
        assertTrue(result, "DP_APP should be considered as main app");
    }

    /**
     * Test judgeMainApp method when clientTypeEnum is not MT_APP or DP_APP
     */
    @Test
    public void testJudgeMainAppWithOtherClientType() throws Throwable {
        // arrange
        ClientTypeEnum clientTypeEnum = ClientTypeEnum.MT_PC;
        // act
        boolean result = DealCtxHelper.judgeMainApp(clientTypeEnum);
        // assert
        assertFalse(result, "MT_PC should not be considered as main app");
    }

    /**
     * Test judgeMainApp method when clientTypeEnum is null
     */
    @Test
    public void testJudgeMainAppWithNullClientType() throws Throwable {
        // arrange
        ClientTypeEnum clientTypeEnum = null;
        // act
        boolean result = DealCtxHelper.judgeMainApp(clientTypeEnum);
        // assert
        assertFalse(result, "Null clientTypeEnum should not be considered as main app");
    }

    /**
     * Test judgeMainApp method with a mocked ClientTypeEnum
     * Note: Since ClientTypeEnum is an enum, mocking is unnecessary. We directly use the enum values.
     */
    @Test
    public void testJudgeMainAppWithMockedClientType() throws Throwable {
        // arrange
        // Using a real enum value instead of mocking
        ClientTypeEnum clientTypeEnum = ClientTypeEnum.MT_APP;
        // act
        boolean result = DealCtxHelper.judgeMainApp(clientTypeEnum);
        // assert
        assertTrue(result, "MT_APP should be considered as main app");
    }
}
