package com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.builder.handler.standard;

import com.google.common.collect.Lists;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.ParentBuilder;
import com.sankuai.dz.product.detail.RequestCustomParamEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.builder.BaseBottomBarBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.component.quick.entrance.im.IMQuickEntranceComponent;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.component.quick.entrance.shop.ShopQuickEntranceComponent;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.component.quick.entrance.shopping.cart.ShoppingCartQuickEntranceComponent;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.component.trade.block.banner.ButtonBannerComponent;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.component.trade.block.banner.CouponBannerComponent;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.component.trade.block.banner.QcpxCouponBannerComponent;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.component.trade.block.buy.button.DefaultTradeButtonComponent;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.component.trade.block.buy.button.shopping.cart.AddShoppingCardButtonComponent;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.exception.BottomBarFatalException;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.abtest.AbTestFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.abtest.AbTestReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.category.ProductCategoryFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.consult.DetailIMFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.consult.DetailIMResult;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.cityId.CityIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.cityId.CityIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.shopid.ShopIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.shopid.ShopIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.OrderPageUrlResult;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.handler.normal.NormalOrderPageUrlFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.pinpool.SimilarDealCacheFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.pinpool.SimilarDealCacheResult;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.ProductNormalPriceFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.ProductPromoPriceFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.dto.ProductPriceReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.purchase.coupon.PurchaseCouponFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.purchase.coupon.PurchaseCouponReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sale.info.ProductSaleInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sale.info.ProductSaleInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sale.info.enums.ProductSaleStatusEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.defaultselect.SkuDefaultSelect;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.defaultselect.SkuDefaultSelectFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.FetcherDAGStarter;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.ProductBuyBarModule;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.banner.BottomBarTopBannerVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.ProductBottomBarVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.block.quick.entrance.QuickEntranceBlockVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.block.trade.StandardTradeBlockVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.component.quick.entrance.QuickEntranceButtonVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.component.trade.button.BaseTradeButtonVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.scene.StandardTradeBottomBarVO;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;
import java.util.Objects;
import java.util.Optional;

/**
 * @Author: guangyujie
 * @Date: 2025/3/10 13:50
 */
@ParentBuilder(
        startFetcher = FetcherDAGStarter.class,
        dependentFetchers = {
                NormalOrderPageUrlFetcher.class,
                ProductCategoryFetcher.class,
                ProductNormalPriceFetcher.class,
                ProductPromoPriceFetcher.class,
                ShopIdMapperFetcher.class,
                DetailIMFetcher.class,
                SkuDefaultSelectFetcher.class,
                ProductSaleInfoFetcher.class,
                PurchaseCouponFetcher.class,
                ProductBaseInfoFetcher.class,
                SimilarDealCacheFetcher.class,
                CityIdMapperFetcher.class,
                AbTestFetcher.class
        }
)
@Slf4j
public abstract class StandardBottomBarBuilder extends BaseBottomBarBuilder {

    protected OrderPageUrlResult orderPageUrlResult;
    protected String orderPageUrl;
    protected ProductSaleInfo productSaleInfo;
    protected ProductPriceReturnValue normalPriceReturnValue;//普通价格
    protected PriceDisplayDTO normalPrice;
    protected ProductPriceReturnValue promoPriceReturnValue;//普通价格含优惠信息
    protected PriceDisplayDTO dealPromoPrice;
    protected ShopIdMapper shopIdMapper;
    protected ProductCategory productCategory;
    protected DetailIMResult detailIMResult;
    protected long selectedSkuId;
    protected PurchaseCouponReturnValue purchaseCouponReturnValue;
    protected SuckBottomBannerParam suckBottomBannerParam;

    @Override
    public ProductBuyBarModule buildBuyBarModule() {
        orderPageUrlResult = getDependencyResult(NormalOrderPageUrlFetcher.class);
        orderPageUrl = Optional.ofNullable(orderPageUrlResult).map(OrderPageUrlResult::getUrl).orElse(null);
        productSaleInfo = getDependencyResult(ProductSaleInfoFetcher.class);
        productCategory = getDependencyResult(ProductCategoryFetcher.class);
        normalPriceReturnValue = getDependencyResult(ProductNormalPriceFetcher.class);
        normalPrice = Optional.ofNullable(normalPriceReturnValue).map(ProductPriceReturnValue::getPriceDisplayDTO).orElse(null);
        promoPriceReturnValue = getDependencyResult(ProductPromoPriceFetcher.class);
        dealPromoPrice = Optional.ofNullable(promoPriceReturnValue).map(ProductPriceReturnValue::getPriceDisplayDTO).orElse(null);
        shopIdMapper = getDependencyResult(ShopIdMapperFetcher.class);
        detailIMResult = getDependencyResult(DetailIMFetcher.class);
        selectedSkuId = getDependencyResult(SkuDefaultSelectFetcher.class, SkuDefaultSelect.class)
                .map(SkuDefaultSelect::getSelectedSkuId).orElse(0L);
        purchaseCouponReturnValue = getDependencyResult(PurchaseCouponFetcher.class);
        AbTestReturnValue abTestResult = getDependencyResult(AbTestFetcher.class);
        suckBottomBannerParam = SuckBottomBannerParam.builder()
                .normalPrice(normalPrice)
                .dealPromoPrice(dealPromoPrice)
                .productBaseInfo(getDependencyResult(ProductBaseInfoFetcher.class))
                .similarDealCacheResult(getDependencyResult(SimilarDealCacheFetcher.class))
                .orderPageUrl(orderPageUrl)
                .cityIdMapper(getDependencyResult(CityIdMapperFetcher.class))
                .abTestReturnValue(abTestResult)
                .build();
        prepareData();
        ProductBuyBarModule dealBuyBarVO = new ProductBuyBarModule();
        dealBuyBarVO.setBottomBar(buildProductBottomBarVO());
        try {
            dealBuyBarVO.setTopBannerList(Optional.ofNullable(buildBanner()).map(Lists::newArrayList).orElse(new ArrayList<>()));
        } catch (Throwable throwable) {
            //Banner出现异常不要影响Button
            log.error("StandardBottomBarBuilder.buildBanner", new BottomBarFatalException(throwable));
        }
        return dealBuyBarVO;
    }


    /**
     * 准备数据，因为是多例的bean，所以可以存储在本地
     */
    protected abstract void prepareData();

    /**
     * 构建右边的按钮，标准样式下最多两个button
     */
    protected abstract ProductBottomBarVO buildProductBottomBarVO();

    /**
     * 构建banner，标准样式下最多一个横幅
     * 注意：banner的优先级顺序
     * 中低线会员免单>国补>神券>推广通QCPX>普通券
     */
    protected BottomBarTopBannerVO buildBanner() {
        String flowFlag = request.getCustomParam(RequestCustomParamEnum.flowFlag);
        BottomBarTopBannerVO magicCouponBanner = ButtonBannerComponent.buildSuckBottomBanner(flowFlag, suckBottomBannerParam);

        // 中低线会员免单
        BottomBarTopBannerVO zxdhymdBanner = ButtonBannerComponent.buildZdxhymdButtonBanner(request.getPageSource());
        if (Objects.nonNull(zxdhymdBanner)) {
            return zxdhymdBanner;
        }
        // 国家补贴
        BottomBarTopBannerVO countrySubsidiesBanner = ButtonBannerComponent.buildCountrySubsidiesButtonBanner(flowFlag,
                request.getGpsCityId(), request.getClientTypeEnum().isMtClientType());
        if (Objects.nonNull(countrySubsidiesBanner)) {
            return countrySubsidiesBanner;
        }
        // 神券优惠感知
        if (Objects.nonNull(magicCouponBanner)) {
            return magicCouponBanner;
        }
        // 推广通QCPX
        BottomBarTopBannerVO qcpxCouponBanner = QcpxCouponBannerComponent.build(request, productCategory, dealPromoPrice);
        if (Objects.nonNull(qcpxCouponBanner)) {
            return qcpxCouponBanner;
        }
        return CouponBannerComponent.build(request, productCategory, dealPromoPrice);
    }

    /**
     * 标准样式下构建基础的快捷入口
     */
    protected @NotNull QuickEntranceBlockVO buildQuickEntranceBlock() {
        QuickEntranceButtonVO shopQuickEntrance = ShopQuickEntranceComponent.build(
                shopIdMapper, request
        );
        QuickEntranceButtonVO IMQuickEntrance = IMQuickEntranceComponent.build(request, detailIMResult);
        return new QuickEntranceBlockVO(Lists.newArrayList(shopQuickEntrance, IMQuickEntrance));
    }

    /**
     * 标准样式下构建加入购物车按钮
     */
    protected void addShoppingCartButton(final StandardTradeBottomBarVO standardTradeBottomBarVO,
                                         final ProductSaleStatusEnum saleStatus) {
        if (standardTradeBottomBarVO == null
                || standardTradeBottomBarVO.getLeftBottomBar() == null
                || standardTradeBottomBarVO.getLeftBottomBar().getQuickEntrance() == null
                || standardTradeBottomBarVO.getRightBottomBar() == null
                || standardTradeBottomBarVO.getRightBottomBar().getButtonList() == null) {
            return;
        }
        if (saleStatus == null || !saleStatus.isForSale()) {
            //无售卖状态or售卖状态为不可交易时，不展示加入购物车按钮
            return;
        }
        int leftBottomBarButtonNum = standardTradeBottomBarVO.getLeftBottomBar().getQuickEntrance().size();
        BaseTradeButtonVO addShoppingCartButton = AddShoppingCardButtonComponent.build(
                request, productCategory, selectedSkuId, orderPageUrlResult, leftBottomBarButtonNum >= 2, saleStatus
        );
        if (addShoppingCartButton == null) {
            return;
        }
        QuickEntranceButtonVO shoppingCartQuickEntrance = ShoppingCartQuickEntranceComponent.build(
                request
        );
        if (shoppingCartQuickEntrance == null) {
            return;
        }
        standardTradeBottomBarVO.getRightBottomBar().getButtonList().add(0, addShoppingCartButton);
        standardTradeBottomBarVO.getLeftBottomBar().getQuickEntrance().add(shoppingCartQuickEntrance);
    }

    protected ProductBuyBarModule buildDefaultBuyBarModule() {
        StandardTradeBottomBarVO bottomBarVO = new StandardTradeBottomBarVO();
        bottomBarVO.setRightBottomBar(StandardTradeBlockVO.buildSingleButtonStyle(
                DefaultTradeButtonComponent.build(request, orderPageUrl)
        ));
        return ProductBuyBarModule.buildWithoutBanner(bottomBarVO);
    }

    @Data
    @lombok.Builder
    public static class SuckBottomBannerParam {
        private PriceDisplayDTO normalPrice;
        private PriceDisplayDTO dealPromoPrice;
        private ProductBaseInfo productBaseInfo;
        private SimilarDealCacheResult similarDealCacheResult;
        private PurchaseCouponReturnValue purchaseCouponReturnValue;
        private CityIdMapper cityIdMapper;
        private String orderPageUrl;
        private AbTestReturnValue abTestReturnValue;
    }

}
