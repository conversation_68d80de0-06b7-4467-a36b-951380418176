package com.sankuai.dzshoppingguide.product.detail.application.builder.timecard.list;

import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommendgoods.dtos.ProductM;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.timesdeal.dto.DealDetailBuildContext;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/14 16:02
 */
public abstract class ProductListVP {
    public abstract List<ProductM> compute(List<ProductM> productMS, DealDetailBuildContext buildContext);
    public abstract boolean isHit(ProductCategory productCategory);

}

