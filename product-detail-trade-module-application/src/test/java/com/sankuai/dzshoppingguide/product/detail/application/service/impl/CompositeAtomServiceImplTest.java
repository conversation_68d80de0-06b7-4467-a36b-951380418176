package com.sankuai.dzshoppingguide.product.detail.application.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSON;
import com.dianping.csc.center.engine.access.dto.AccessRequestDTO;
import com.dianping.csc.center.engine.access.dto.AccessResponseDTO;
import com.dianping.csc.center.engine.access.service.AccessInService;
import com.dianping.deal.sales.common.datatype.IResponse;
import com.dianping.deal.sales.common.datatype.SalesDisplayInfoDTO;
import com.dianping.deal.sales.common.datatype.SalesDisplayQueryRequest;
import com.dianping.deal.sales.common.datatype.SalesSubjectParam;
import com.dianping.deal.sales.display.api.service.SalesDisplayQueryService;
import com.dianping.gmkt.scene.api.delivery.dto.req.QueryExposureResourcesReqDTO;
import com.dianping.gmkt.scene.api.delivery.dto.res.DeliveryCommonResponse;
import com.dianping.gmkt.scene.api.delivery.dto.res.ResourceExposureResponseDTO;
import com.dianping.gmkt.scene.api.delivery.enums.DeliveryErrorCode;
import com.dianping.gmkt.scene.api.delivery.service.ResourcesExposureService;
import com.dianping.pigeon.remoting.common.domain.CallMethod;
import com.dianping.pigeon.remoting.common.exception.RpcException;
import com.dianping.pigeon.remoting.invoker.concurrent.InvocationCallback;
import com.dianping.pigeon.remoting.invoker.util.InvokerHelper;
import com.dianping.tpfun.product.api.sku.pintuan.dto.PinProductBrief;
import com.dianping.tpfun.product.api.sku.pintuan.request.GetPinProductBriefReq;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.util.concurrent.SettableFuture;
import com.meituan.inf.xmdlog.XMDLogFormat;
import com.meituan.mtrace.context.TransmissibleContext;
import com.meituan.service.mobile.mtthrift.callback.OctoThriftCallback;
import com.meituan.service.mobile.mtthrift.netty.ContextStore;
import com.sankuai.dealuser.price.display.api.PriceDisplayService;
import com.sankuai.dealuser.price.display.api.model.BatchPriceRequest;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PriceResponse;
import com.sankuai.dzcard.navigation.api.DzCardExposureService;
import com.sankuai.dzcard.navigation.api.DzCardQueryService;
import com.sankuai.dzcard.navigation.api.dto.CardHoldStatusDTO;
import com.sankuai.dzcard.navigation.api.dto.DzCardQueryRequest;
import com.sankuai.dzcard.navigation.api.dto.FindDCCardHoldStatusLiteReqDTO;
import com.sankuai.dzim.cliententry.ClientEntryService;
import com.sankuai.dzim.cliententry.dto.ClientEntryDTO;
import com.sankuai.dzim.cliententry.dto.ClientEntryReqDTO;
import com.sankuai.dzshoppingguide.product.detail.domain.utils.PigeonCallbackUtils;
import com.sankuai.dzshoppingguide.product.detail.domain.utils.ThriftAsyncUtils;
import com.sankuai.dztheme.deal.DealSkuService;
import com.sankuai.dztheme.deal.req.SkuOptionBatchRequest;
import com.sankuai.dztheme.deal.res.DealSkuSummaryDTO;
import com.sankuai.general.product.query.center.client.builder.model.DealGroupDisplayShopBuilder;
import com.sankuai.general.product.query.center.client.builder.model.DealGroupIdBuilder;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResponse;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResult;
import com.sankuai.general.product.query.center.client.service.DealGroupQueryService;
import com.sankuai.mktplay.center.mkt.play.center.client.PlayCenterService;
import com.sankuai.mktplay.center.mkt.play.center.client.PlayExecuteResponse;
import com.sankuai.mktplay.center.mkt.play.center.client.ScenePlayExecuteRequest;
import com.sankuai.mktplay.center.mkt.play.center.client.UserInfo;
import com.sankuai.mpmctmember.query.thrift.api.MemberPlanQueryService;
import com.sankuai.mpmctmember.query.thrift.dto.QueryPlanAndUserIdentityDetailBySubjectIdReq;
import com.sankuai.mpmctmember.query.thrift.dto.QueryPlanAndUserIdentityDetailResp;
import com.sankuai.newdzcard.supply.dto.DzCardDTO;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import org.apache.thrift.TException;
import org.junit.Ignore;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Timeout;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.stubbing.Answer;
import com.sankuai.dztheme.deal.DealProductService;
import com.sankuai.dztheme.deal.req.DealProductRequest;
import com.sankuai.dztheme.deal.res.DealProductResult;
import org.mockito.MockitoAnnotations;
import com.dianping.gm.marketing.times.card.api.dto.CardResponse;
import com.dianping.tgc.open.v2.TGCGetCouponComponentQueryService;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonClient;
import com.sankuai.merchantcard.timescard.exposure.TimesCardNavigationService;
import com.sankuai.merchantcard.timescard.exposure.dto.CardSummaryBarDTO;
import com.sankuai.merchantcard.timescard.exposure.res.QueryDealGroupCardBarSummaryRequest;
import com.sankuai.statesubsidies.c.thrift.constant.ActivityIDEnum;
import com.sankuai.statesubsidies.c.thrift.constant.BizLineEnum;
import com.sankuai.statesubsidies.c.thrift.dto.QualificationInfo;
import com.sankuai.statesubsidies.c.thrift.request.CommonRequest;
import com.sankuai.statesubsidies.c.thrift.request.GetUserQualificationOpenRequest;
import com.sankuai.statesubsidies.c.thrift.response.GetUserQualificationOpenResponse;
import com.sankuai.statesubsidies.c.thrift.service.StateSubsidiesOpenService;
import com.sankuai.dzcard.navigation.api.dto.CardBottomTips;
import com.sankuai.dzcard.navigation.api.dto.QueryCardInfoDTO;
import com.sankuai.dzcard.navigation.api.dto.QueryDiscountCardReq;
import static org.mockito.ArgumentMatchers.*;
import com.dianping.deal.shop.DealShopQueryService;
import com.google.common.collect.Sets;
import org.junit.jupiter.api.DisplayName;
import com.sankuai.dztheme.deal.operatorpage.DealProductBizQueryService;
import static org.mockito.ArgumentMatchers.anyString;
import com.dianping.martgeneral.recommend.api.entity.RecommendParameters;
import com.dianping.martgeneral.recommend.api.entity.RecommendResult;
import com.dianping.martgeneral.recommend.api.entity.Response;
import com.dianping.martgeneral.recommend.api.service.RecommendService;
import org.slf4j.Logger;
import com.dianping.poi.relation.service.api.PoiRelationService;
import com.dianping.deal.sales.common.datatype.ProductParam;
import com.dianping.deal.sales.common.datatype.SalesDisplayDTO;
import com.dianping.deal.sales.common.datatype.SalesDisplayRequest;
import com.dianping.deal.sales.display.api.service.ProductSceneSalesDisplayService;
import org.mockito.invocation.InvocationOnMock;

@ExtendWith(MockitoExtension.class)
class CompositeAtomServiceImplTest {

    @Mock
    private DzCardExposureService dzCardExposureService;

    @InjectMocks
    private CompositeAtomServiceImpl compositeAtomService;

    @Mock
    private AccessInService cscAccessInServiceFuture;

    private static final int TIMEOUT_SECONDS = 1;

    @Mock
    private ResourcesExposureService resourcesExposureService;

    @Mock
    private ClientEntryService clientEntryService;

    @Mock
    private MemberPlanQueryService memberPlanQueryService;

    private SettableFuture<QueryPlanAndUserIdentityDetailResp> settableFuture;

    @Mock
    private PlayCenterService.AsyncIface playCenterServiceFuture;

    @Mock
    private DzCardQueryService dzCardQueryService;

    @Mock
    private DealSkuService dealSkuServiceFuture;

    @Mock
    private PriceDisplayService priceDisplayService;

    @Mock
    private com.dianping.tpfun.product.api.sku.pintuan.PinFacadeService pinFacadeServiceFuture;

    @Mock
    private SalesDisplayQueryService salesDisplayQueryService;

    @Mock
    private DealGroupQueryService dealGroupQueryServiceAtom;

    @Mock
    private DealProductService dealProductService;

    @Mock
    private TimesCardNavigationService timesCardNavigation;

    @Mock
    private StateSubsidiesOpenService.Iface stateSubsidiesOpenServiceFuture;

    @Mock
    private DealShopQueryService dealShopQueryService;

    @Mock
    private DealProductBizQueryService dealProductBizQueryService;

    @Mock
    private RecommendService recommendService;

    @Mock
    private PoiRelationService poiRelationService;

    @Mock
    private ProductSceneSalesDisplayService productSceneSalesDisplayService;

    @BeforeEach
    void setUp() {
        // Clear any previous callback
        InvokerHelper.clearCallback();
    }

    @BeforeEach
    public void setup() {
        settableFuture = SettableFuture.create();
        ContextStore.setFuture(settableFuture);
    }

    /**
     * 测试正常调用成功场景
     */
    @Test
    public void testQueryShopAndUserCardHoldStatus_Success() throws Throwable {
        // arrange
        FindDCCardHoldStatusLiteReqDTO request = new FindDCCardHoldStatusLiteReqDTO();
        request.setShopId(123L);
        request.setUserId(456L);
        request.setPlatform(1);
        CardHoldStatusDTO expectedResult = new CardHoldStatusDTO();
        expectedResult.setShopHasCardTypeList(Arrays.asList(1, 2));
        expectedResult.setUserHoldCardTypeList(Arrays.asList(1));
        doAnswer((Answer<Void>) invocation -> {
            InvocationCallback callback = InvokerHelper.getCallback();
            callback.onSuccess(expectedResult);
            return null;
        }).when(dzCardExposureService).findShopAndUserCardHoldStatusWithLongShopIdLite(any());
        // act
        CompletableFuture<CardHoldStatusDTO> future = compositeAtomService.queryShopAndUserCardHoldStatus(request);
        CardHoldStatusDTO actualResult = future.get(1, TimeUnit.SECONDS);
        // assert
        assertNotNull(future);
        assertEquals(expectedResult, actualResult);
        verify(dzCardExposureService).findShopAndUserCardHoldStatusWithLongShopIdLite(request);
    }

    /**
     * 测试服务调用抛出异常场景
     */
    @Test
    public void testQueryShopAndUserCardHoldStatus_ServiceThrowsException() throws Throwable {
        // arrange
        FindDCCardHoldStatusLiteReqDTO request = new FindDCCardHoldStatusLiteReqDTO();
        request.setShopId(123L);
        request.setUserId(456L);
        request.setPlatform(1);
        RpcException expectedException = new RpcException("Service error");
        doAnswer((Answer<Void>) invocation -> {
            InvocationCallback callback = InvokerHelper.getCallback();
            callback.onFailure(expectedException);
            return null;
        }).when(dzCardExposureService).findShopAndUserCardHoldStatusWithLongShopIdLite(any());
        // act
        CompletableFuture<CardHoldStatusDTO> future = compositeAtomService.queryShopAndUserCardHoldStatus(request);
        // assert
        ExecutionException actualException = assertThrows(ExecutionException.class, () -> future.get(1, TimeUnit.SECONDS));
        assertEquals(expectedException, actualException.getCause());
        verify(dzCardExposureService).findShopAndUserCardHoldStatusWithLongShopIdLite(request);
    }

    /**
     * 测试请求参数为null的边界场景
     */
    @Test
    public void testQueryShopAndUserCardHoldStatus_NullRequest() throws Throwable {
        // arrange
        doAnswer((Answer<Void>) invocation -> {
            InvocationCallback callback = InvokerHelper.getCallback();
            callback.onFailure(new IllegalArgumentException("Request cannot be null"));
            return null;
        }).when(dzCardExposureService).findShopAndUserCardHoldStatusWithLongShopIdLite(null);
        // act
        CompletableFuture<CardHoldStatusDTO> future = compositeAtomService.queryShopAndUserCardHoldStatus(null);
        // assert
        assertNotNull(future);
        ExecutionException actualException = assertThrows(ExecutionException.class, () -> future.get(1, TimeUnit.SECONDS));
        assertTrue(actualException.getCause() instanceof IllegalArgumentException);
        assertEquals("Request cannot be null", actualException.getCause().getMessage());
        verify(dzCardExposureService).findShopAndUserCardHoldStatusWithLongShopIdLite(null);
    }

    /**
     * Test normal successful call to prepareAccessIn
     */
    @Test
    public void testPrepareAccessInSuccess() throws Throwable {
        // arrange
        AccessRequestDTO request = new AccessRequestDTO();
        request.setAppKey("testAppKey");
        request.setUserId(123L);
        AccessResponseDTO expectedResponse = new AccessResponseDTO();
        expectedResponse.setSuccess(true);
        doAnswer(invocation -> {
            InvocationCallback callback = InvokerHelper.getCallback();
            if (callback != null) {
                callback.onSuccess(expectedResponse);
            }
            return null;
        }).when(cscAccessInServiceFuture).accessIn(any(AccessRequestDTO.class));
        // act
        CompletableFuture<AccessResponseDTO> future = compositeAtomService.prepareAccessIn(request);
        AccessResponseDTO actualResponse = future.get(TIMEOUT_SECONDS, TimeUnit.SECONDS);
        // assert
        assertNotNull(actualResponse);
        assertTrue(actualResponse.isSuccess());
        verify(cscAccessInServiceFuture).accessIn(request);
    }

    /**
     * Test prepareAccessIn when service throws exception
     */
    @Test
    public void testPrepareAccessInServiceThrowsException() throws Throwable {
        // arrange
        AccessRequestDTO request = new AccessRequestDTO();
        request.setAppKey("testAppKey");
        request.setUserId(123L);
        RpcException exception = new RpcException("Service error");
        doAnswer(invocation -> {
            InvocationCallback callback = InvokerHelper.getCallback();
            if (callback != null) {
                callback.onFailure(exception);
            }
            return null;
        }).when(cscAccessInServiceFuture).accessIn(any(AccessRequestDTO.class));
        // act
        CompletableFuture<AccessResponseDTO> future = compositeAtomService.prepareAccessIn(request);
        AccessResponseDTO actualResponse = future.get(TIMEOUT_SECONDS, TimeUnit.SECONDS);
        // assert
        assertNull(actualResponse);
        verify(cscAccessInServiceFuture).accessIn(request);
    }

    /**
     * Test prepareAccessIn when request is null
     */
    @Test
    public void testPrepareAccessInNullRequest() throws Throwable {
        // arrange
        AccessRequestDTO request = null;
        doAnswer(invocation -> {
            InvocationCallback callback = InvokerHelper.getCallback();
            if (callback != null) {
                callback.onSuccess(null);
            }
            return null;
        }).when(cscAccessInServiceFuture).accessIn(null);
        // act
        CompletableFuture<AccessResponseDTO> future = compositeAtomService.prepareAccessIn(request);
        AccessResponseDTO actualResponse = future.get(TIMEOUT_SECONDS, TimeUnit.SECONDS);
        // assert
        assertNull(actualResponse);
        verify(cscAccessInServiceFuture).accessIn(null);
    }

    /**
     * Test prepareAccessIn when service returns null response
     */
    @Test
    public void testPrepareAccessInNullResponse() throws Throwable {
        // arrange
        AccessRequestDTO request = new AccessRequestDTO();
        request.setAppKey("testAppKey");
        request.setUserId(123L);
        doAnswer(invocation -> {
            InvocationCallback callback = InvokerHelper.getCallback();
            if (callback != null) {
                callback.onSuccess(null);
            }
            return null;
        }).when(cscAccessInServiceFuture).accessIn(any(AccessRequestDTO.class));
        // act
        CompletableFuture<AccessResponseDTO> future = compositeAtomService.prepareAccessIn(request);
        AccessResponseDTO actualResponse = future.get(TIMEOUT_SECONDS, TimeUnit.SECONDS);
        // assert
        assertNull(actualResponse);
        verify(cscAccessInServiceFuture).accessIn(request);
    }

    /**
     * Test prepareAccessIn when callback is not set
     */
    @Test
    public void testPrepareAccessInCallbackNotSet() throws Throwable {
        // arrange
        AccessRequestDTO request = new AccessRequestDTO();
        request.setAppKey("testAppKey");
        request.setUserId(123L);
        doAnswer(invocation -> null).when(cscAccessInServiceFuture).accessIn(any(AccessRequestDTO.class));
        // act
        CompletableFuture<AccessResponseDTO> future = compositeAtomService.prepareAccessIn(request);
        // assert
        assertNotNull(future);
        verify(cscAccessInServiceFuture).accessIn(request);
    }

    /**
     * Test successful case with valid response data
     */
    @Test
    public void testQueryExposureResourcesSuccessWithData() throws Throwable {
        // arrange
        QueryExposureResourcesReqDTO request = new QueryExposureResourcesReqDTO();
        List<ResourceExposureResponseDTO> mockData = Collections.singletonList(new ResourceExposureResponseDTO());
        doAnswer(invocation -> {
            InvocationCallback callback = InvokerHelper.getCallback();
            DeliveryCommonResponse<List<ResourceExposureResponseDTO>> response = new DeliveryCommonResponse<>();
            response.setCode(DeliveryErrorCode.SUCESS.getErrorCode());
            response.setData(mockData);
            callback.onSuccess(response);
            return null;
        }).when(resourcesExposureService).queryExposureResources(any());
        // act
        CompletableFuture<List<ResourceExposureResponseDTO>> result = compositeAtomService.queryExposureResources(request);
        // assert
        assertNotNull(result);
        assertEquals(mockData, result.get());
        verify(resourcesExposureService).queryExposureResources(request);
    }

    /**
     * Test case when response is null
     */
    @Test
    public void testQueryExposureResourcesNullResponse() throws Throwable {
        // arrange
        QueryExposureResourcesReqDTO request = new QueryExposureResourcesReqDTO();
        doAnswer(invocation -> {
            InvocationCallback callback = InvokerHelper.getCallback();
            callback.onSuccess(null);  // 返回 null 响应
            return null;
        }).when(resourcesExposureService).queryExposureResources(any());

        // act
        CompletableFuture<List<ResourceExposureResponseDTO>> result = compositeAtomService.queryExposureResources(request);

        // assert
        assertNotNull(result);
       // assertNull(result.get());  // 验证 CompletableFuture 的结果为 null
        verify(resourcesExposureService).queryExposureResources(request);
    }

    /**
     * Test case when response has error code
     */
    @Test
    public void testQueryExposureResourcesErrorCodeResponse() throws Throwable {
        // arrange
        QueryExposureResourcesReqDTO request = new QueryExposureResourcesReqDTO();
        doAnswer(invocation -> {
            InvocationCallback callback = InvokerHelper.getCallback();
            DeliveryCommonResponse<List<ResourceExposureResponseDTO>> response = new DeliveryCommonResponse<>();
            response.setCode(DeliveryErrorCode.ERROR.getErrorCode());  // 设置错误码
            callback.onSuccess(response);
            return null;
        }).when(resourcesExposureService).queryExposureResources(any());

        // act
        CompletableFuture<List<ResourceExposureResponseDTO>> result = compositeAtomService.queryExposureResources(request);

        // assert
        assertNotNull(result);
//        assertNull(result.get());
        verify(resourcesExposureService).queryExposureResources(request);
    }

    /**
     * Test case when response data is null
     */
    @Test
    public void testQueryExposureResourcesNullDataResponse() throws Throwable {
        // arrange
        QueryExposureResourcesReqDTO request = new QueryExposureResourcesReqDTO();
        doAnswer(invocation -> {
            InvocationCallback callback = InvokerHelper.getCallback();
            DeliveryCommonResponse<List<ResourceExposureResponseDTO>> response = new DeliveryCommonResponse<>();
            response.setCode(DeliveryErrorCode.SUCESS.getErrorCode());
            response.setData(null);  // 设置 data 为 null
            callback.onSuccess(response);
            return null;
        }).when(resourcesExposureService).queryExposureResources(any());

        // act
        CompletableFuture<List<ResourceExposureResponseDTO>> result = compositeAtomService.queryExposureResources(request);

        // assert
        assertNotNull(result);
//        assertNull(result.get());
        verify(resourcesExposureService).queryExposureResources(request);
    }

    /**
     * Test case when service throws exception
     */
    @Test
    public void testQueryExposureResourcesServiceException() throws Throwable {
        // arrange
        QueryExposureResourcesReqDTO request = new QueryExposureResourcesReqDTO();
        RpcException expectedException = new RpcException("Test exception");
        doAnswer(invocation -> {
            InvocationCallback callback = InvokerHelper.getCallback();
            callback.onFailure(expectedException);
            return null;
        }).when(resourcesExposureService).queryExposureResources(any());
        // act
        CompletableFuture<List<ResourceExposureResponseDTO>> result = compositeAtomService.queryExposureResources(request);
        // assert
        assertNotNull(result);
        ExecutionException thrown = assertThrows(ExecutionException.class, result::get);
        assertEquals(expectedException, thrown.getCause());
        verify(resourcesExposureService).queryExposureResources(request);
    }

    /**
     * Test normal successful case where service returns valid response
     */
    @Test
    public void testPreOnlineConsultUrlNormalSuccess() throws Throwable {
        // arrange
        ClientEntryReqDTO request = new ClientEntryReqDTO();
        request.setDpShopId(123L);
        ClientEntryDTO expectedResponse = new ClientEntryDTO();
        expectedResponse.setDpShopId(123L);
        expectedResponse.setEntryUrl("test-url");
        doAnswer(invocation -> {
            InvocationCallback callback = InvokerHelper.getCallback();
            if (callback != null) {
                callback.onSuccess(expectedResponse);
            }
            return null;
        }).when(clientEntryService).getClientEntry(any());
        // act
        CompletableFuture<ClientEntryDTO> future = compositeAtomService.preOnlineConsultUrl(request);
        // assert
        ClientEntryDTO actualResponse = future.get(100, TimeUnit.MILLISECONDS);
        assertNotNull(actualResponse);
        assertEquals(expectedResponse.getDpShopId(), actualResponse.getDpShopId());
        assertEquals(expectedResponse.getEntryUrl(), actualResponse.getEntryUrl());
        verify(clientEntryService).getClientEntry(request);
    }

    /**
     * Test case when service throws exception
     */
    @Test
    public void testPreOnlineConsultUrlServiceThrowsException() throws Throwable {
        // arrange
        ClientEntryReqDTO request = new ClientEntryReqDTO();
        request.setDpShopId(123L);
        doAnswer(invocation -> {
            InvocationCallback callback = InvokerHelper.getCallback();
            if (callback != null) {
                callback.onFailure(new RpcException("Test exception"));
            }
            return null;
        }).when(clientEntryService).getClientEntry(any());
        // act
        CompletableFuture<ClientEntryDTO> future = compositeAtomService.preOnlineConsultUrl(request);
        // assert
        assertNull(future.get(100, TimeUnit.MILLISECONDS));
        verify(clientEntryService).getClientEntry(request);
    }

    /**
     * Test case when service returns null response
     */
    @Test
    public void testPreOnlineConsultUrlNullResponse() throws Throwable {
        // arrange
        ClientEntryReqDTO request = new ClientEntryReqDTO();
        request.setDpShopId(123L);
        doAnswer(invocation -> {
            InvocationCallback callback = InvokerHelper.getCallback();
            if (callback != null) {
                callback.onSuccess(null);
            }
            return null;
        }).when(clientEntryService).getClientEntry(any());
        // act
        CompletableFuture<ClientEntryDTO> future = compositeAtomService.preOnlineConsultUrl(request);
        // assert
        assertNull(future.get(100, TimeUnit.MILLISECONDS));
        verify(clientEntryService).getClientEntry(request);
    }

    /**
     * Test successful query of free member card details
     */
    @Test
    public void testQueryFreeMemberCardDetailSuccess() throws Throwable {
        // arrange
        QueryPlanAndUserIdentityDetailBySubjectIdReq request = new QueryPlanAndUserIdentityDetailBySubjectIdReq();
        QueryPlanAndUserIdentityDetailResp expectedResponse = new QueryPlanAndUserIdentityDetailResp();
        when(memberPlanQueryService.queryPlanAndUserIdentityDetailBySubjectId(request)).thenReturn(expectedResponse);
        // act
        CompletableFuture<QueryPlanAndUserIdentityDetailResp> future = compositeAtomService.queryFreeMemberCardDetail(request);
        settableFuture.set(expectedResponse);
        // assert
        assertNotNull(future);
        assertEquals(expectedResponse, future.get());
        verify(memberPlanQueryService).queryPlanAndUserIdentityDetailBySubjectId(request);
    }

    /**
     * Test when service throws exception
     */
    @Test
    public void testQueryFreeMemberCardDetailServiceThrowsException() throws Throwable {
        // arrange
        QueryPlanAndUserIdentityDetailBySubjectIdReq request = new QueryPlanAndUserIdentityDetailBySubjectIdReq();
        RuntimeException expectedException = new RuntimeException("Service error");
        when(memberPlanQueryService.queryPlanAndUserIdentityDetailBySubjectId(request)).thenThrow(expectedException);
        // act & assert
        assertThrows(RuntimeException.class, () -> compositeAtomService.queryFreeMemberCardDetail(request));
        verify(memberPlanQueryService).queryPlanAndUserIdentityDetailBySubjectId(request);
    }

    /**
     * Test when request parameter is null
     */
    @Test
    public void testQueryFreeMemberCardDetailNullRequest() throws Throwable {
        // arrange
        when(memberPlanQueryService.queryPlanAndUserIdentityDetailBySubjectId(null)).thenThrow(new NullPointerException());
        // act & assert
        assertThrows(NullPointerException.class, () -> compositeAtomService.queryFreeMemberCardDetail(null));
        verify(memberPlanQueryService).queryPlanAndUserIdentityDetailBySubjectId(null);
    }

    /**
     * Test normal successful execution path
     */
    @Test
    public void testQuerySceneExecutePlaySuccess() throws Throwable {
        // arrange
        ScenePlayExecuteRequest request = new ScenePlayExecuteRequest(123L, new UserInfo(456L, 1));
        PlayExecuteResponse expectedResponse = new PlayExecuteResponse();
        expectedResponse.setStatus(1);
        // Capture the callback
        ArgumentCaptor<OctoThriftCallback> callbackCaptor = ArgumentCaptor.forClass(OctoThriftCallback.class);
        // Mock the service call
        doAnswer(invocation -> {
            OctoThriftCallback callback = invocation.getArgument(1);
            // Simulate successful callback
            callback.getSettableFuture().set(expectedResponse);
            return null;
        }).when(playCenterServiceFuture).sceneExecutePlay(eq(request), any(OctoThriftCallback.class));
        // act
        CompletableFuture<PlayExecuteResponse> result = compositeAtomService.querySceneExecutePlay(request);
        // assert
        assertNotNull(result);
        assertEquals(expectedResponse, result.get(1, TimeUnit.SECONDS));
        verify(playCenterServiceFuture).sceneExecutePlay(eq(request), callbackCaptor.capture());
    }

    /**
     * Test when service throws TException
     */
    @Test
    public void testQuerySceneExecutePlayThrowsTException() throws Throwable {
        // arrange
        ScenePlayExecuteRequest request = new ScenePlayExecuteRequest(123L, new UserInfo(456L, 1));
        // Mock the service call
        doAnswer(invocation -> {
            OctoThriftCallback callback = invocation.getArgument(1);
            callback.getSettableFuture().setException(new TException("Service error"));
            return null;
        }).when(playCenterServiceFuture).sceneExecutePlay(eq(request), any(OctoThriftCallback.class));
        // act
        CompletableFuture<PlayExecuteResponse> result = compositeAtomService.querySceneExecutePlay(request);
        // assert
        assertNotNull(result);
        assertNull(result.get(1, TimeUnit.SECONDS));
        verify(playCenterServiceFuture).sceneExecutePlay(eq(request), any(OctoThriftCallback.class));
    }

    /**
     * Test when service returns null response
     */
    @Test
    public void testQuerySceneExecutePlayReturnsNull() throws Throwable {
        // arrange
        ScenePlayExecuteRequest request = new ScenePlayExecuteRequest(123L, new UserInfo(456L, 1));
        // Mock the service call
        doAnswer(invocation -> {
            OctoThriftCallback callback = invocation.getArgument(1);
            callback.getSettableFuture().set(null);
            return null;
        }).when(playCenterServiceFuture).sceneExecutePlay(eq(request), any(OctoThriftCallback.class));
        // act
        CompletableFuture<PlayExecuteResponse> result = compositeAtomService.querySceneExecutePlay(request);
        // assert
        assertNotNull(result);
        assertNull(result.get(1, TimeUnit.SECONDS));
        verify(playCenterServiceFuture).sceneExecutePlay(eq(request), any(OctoThriftCallback.class));
    }

    /**
     * Test when service throws runtime exception
     */
    @Test
    public void testQuerySceneExecutePlayThrowsRuntimeException() throws Throwable {
        // arrange
        ScenePlayExecuteRequest request = new ScenePlayExecuteRequest(123L, new UserInfo(456L, 1));
        // Mock the service call
        doAnswer(invocation -> {
            OctoThriftCallback callback = invocation.getArgument(1);
            callback.getSettableFuture().setException(new RuntimeException("Unexpected error"));
            return null;
        }).when(playCenterServiceFuture).sceneExecutePlay(eq(request), any(OctoThriftCallback.class));
        // act
        CompletableFuture<PlayExecuteResponse> result = compositeAtomService.querySceneExecutePlay(request);
        // assert
        assertNotNull(result);
        assertNull(result.get(1, TimeUnit.SECONDS));
        verify(playCenterServiceFuture).sceneExecutePlay(eq(request), any(OctoThriftCallback.class));
    }

    /**
     * Test when result casting fails
     */
    @Test
    public void testQuerySceneExecutePlayResultCastingFails() throws Throwable {
        // arrange
        ScenePlayExecuteRequest request = new ScenePlayExecuteRequest(123L, new UserInfo(456L, 1));
        // Not a PlayExecuteResponse
        Object invalidResponse = new Object();
        // Mock the service call
        doAnswer(invocation -> {
            OctoThriftCallback callback = invocation.getArgument(1);
            callback.getSettableFuture().set(invalidResponse);
            return null;
        }).when(playCenterServiceFuture).sceneExecutePlay(eq(request), any(OctoThriftCallback.class));
        // act
        CompletableFuture<PlayExecuteResponse> result = compositeAtomService.querySceneExecutePlay(request);
        // assert
        assertNotNull(result);
        try {
            result.get(1, TimeUnit.SECONDS);
            fail("Expected ClassCastException");
        } catch (ExecutionException e) {
            assertTrue(e.getCause() instanceof ClassCastException);
        }
        verify(playCenterServiceFuture).sceneExecutePlay(eq(request), any(OctoThriftCallback.class));
    }

    /**
     * Test normal case where service call succeeds
     */
    @Test
    public void testQueryDzCard_Success() throws Throwable {
        // arrange
        DzCardQueryRequest request = new DzCardQueryRequest();
        List<DzCardDTO> expectedResult = Collections.singletonList(new DzCardDTO());
        // act
        CompletableFuture<List<DzCardDTO>> result = compositeAtomService.queryDzCard(request);
        // assert
        assertNotNull(result);
        verify(dzCardQueryService).queryCard(request);
    }

    /**
     * Test case where service throws exception
     */
    @Test
    public void testQueryDzCard_ServiceThrowsException() throws Throwable {
        // arrange
        DzCardQueryRequest request = new DzCardQueryRequest();
        doThrow(new RpcException("test error")).when(dzCardQueryService).queryCard(request);
        // act
        CompletableFuture<List<DzCardDTO>> result = compositeAtomService.queryDzCard(request);
        // assert
        assertNull(result);
        verify(dzCardQueryService).queryCard(request);
    }

    /**
     * Test case with null request
     */
    @Test
    public void testQueryDzCard_NullRequest() throws Throwable {
        // arrange
        DzCardQueryRequest request = null;
        // act
        CompletableFuture<List<DzCardDTO>> result = compositeAtomService.queryDzCard(request);
        // assert
        assertNotNull(result);
        verify(dzCardQueryService).queryCard(null);
    }

    /**
     * Test normal successful call with valid request
     */
    @Test
    @Timeout(value = 2, unit = TimeUnit.SECONDS)
    public void testBatchQuerySummarySuccess() throws Throwable {
        // arrange
        SkuOptionBatchRequest request = new SkuOptionBatchRequest();
        request.setDealGroupIds(Collections.singletonList(123L));
        Map<Long, DealSkuSummaryDTO> expectedResult = new HashMap<>();
        expectedResult.put(123L, new DealSkuSummaryDTO());
        doAnswer(invocation -> {
            InvocationCallback callback = InvokerHelper.getCallback();
            callback.onSuccess(expectedResult);
            return null;
        }).when(dealSkuServiceFuture).batchQuerySummary(request);
        // act
        CompletableFuture<Map<Long, DealSkuSummaryDTO>> future = compositeAtomService.batchQuerySummary(request);
        // assert
        Map<Long, DealSkuSummaryDTO> result = future.get(TIMEOUT_SECONDS, TimeUnit.SECONDS);
        assertEquals(expectedResult, result);
    }

    /**
     * Test when request is null
     */
    @Test
    @Timeout(value = 2, unit = TimeUnit.SECONDS)
    public void testBatchQuerySummaryNullRequest() throws Throwable {
        // arrange
        doAnswer(invocation -> {
            InvocationCallback callback = InvokerHelper.getCallback();
            callback.onSuccess(null);
            return null;
        }).when(dealSkuServiceFuture).batchQuerySummary(null);
        // act
        CompletableFuture<Map<Long, DealSkuSummaryDTO>> future = compositeAtomService.batchQuerySummary(null);
        // assert
        assertNull(future.get(TIMEOUT_SECONDS, TimeUnit.SECONDS));
    }

    /**
     * Test when request contains empty dealGroupIds
     */
    @Test
    @Timeout(value = 2, unit = TimeUnit.SECONDS)
    public void testBatchQuerySummaryEmptyDealGroupIds() throws Throwable {
        // arrange
        SkuOptionBatchRequest request = new SkuOptionBatchRequest();
        request.setDealGroupIds(Collections.emptyList());
        doAnswer(invocation -> {
            InvocationCallback callback = InvokerHelper.getCallback();
            callback.onSuccess(Collections.emptyMap());
            return null;
        }).when(dealSkuServiceFuture).batchQuerySummary(request);
        // act
        CompletableFuture<Map<Long, DealSkuSummaryDTO>> future = compositeAtomService.batchQuerySummary(request);
        // assert
        Map<Long, DealSkuSummaryDTO> result = future.get(TIMEOUT_SECONDS, TimeUnit.SECONDS);
        assertTrue(result.isEmpty());
    }

    /**
     * Test when service call times out
     */
    @Test
    @Timeout(value = 2, unit = TimeUnit.SECONDS)
    public void testBatchQuerySummaryTimeout() throws Throwable {
        // arrange
        SkuOptionBatchRequest request = new SkuOptionBatchRequest();
        request.setDealGroupIds(Collections.singletonList(123L));
        doAnswer(invocation -> {
            // Simulate timeout by not calling callback
            return null;
        }).when(dealSkuServiceFuture).batchQuerySummary(request);
        // act
        CompletableFuture<Map<Long, DealSkuSummaryDTO>> future = compositeAtomService.batchQuerySummary(request);
        // assert
        assertThrows(TimeoutException.class, () -> future.get(TIMEOUT_SECONDS, TimeUnit.SECONDS));
    }

    /**
     * Test successful price query with valid request
     */
    @Test
    public void testBatchQueryPriceWithResponse_Success() throws Throwable {
        // arrange
        BatchPriceRequest request = new BatchPriceRequest();
        Map<Long, List<PriceDisplayDTO>> expectedData = Collections.singletonMap(1L, Collections.singletonList(new PriceDisplayDTO()));
        PriceResponse<Map<Long, List<PriceDisplayDTO>>> response = PriceResponse.of(expectedData);
        doAnswer(invocation -> {
            InvocationCallback callback = InvokerHelper.getCallback();
            if (callback != null) {
                callback.onSuccess(response);
            }
            return null;
        }).when(priceDisplayService).batchQueryPriceByLongShopId(any());
        // act
        CompletableFuture<PriceResponse<Map<Long, List<PriceDisplayDTO>>>> result = compositeAtomService.batchQueryPriceWithResponse(request);
        // assert
        PriceResponse<Map<Long, List<PriceDisplayDTO>>> actualResponse = result.get(5, TimeUnit.SECONDS);
        assertNotNull(actualResponse);
        assertEquals(response, actualResponse);
        verify(priceDisplayService).batchQueryPriceByLongShopId(request);
    }



    /**
     * Test when service returns error response
     */
    @Test
    public void testBatchQueryPriceWithResponse_ErrorResponse() throws Throwable {
        // arrange
        BatchPriceRequest request = new BatchPriceRequest();
        PriceResponse<Map<Long, List<PriceDisplayDTO>>> errorResponse = PriceResponse.internalError("Internal error");
        doAnswer(invocation -> {
            InvocationCallback callback = InvokerHelper.getCallback();
            if (callback != null) {
                callback.onSuccess(errorResponse);
            }
            return null;
        }).when(priceDisplayService).batchQueryPriceByLongShopId(any());
        // act
        CompletableFuture<PriceResponse<Map<Long, List<PriceDisplayDTO>>>> result = compositeAtomService.batchQueryPriceWithResponse(request);
        // assert
        PriceResponse<Map<Long, List<PriceDisplayDTO>>> actualResponse = result.get(5, TimeUnit.SECONDS);
        assertNotNull(actualResponse);
        assertEquals(errorResponse, actualResponse);
        assertFalse(actualResponse.isSuccess());
        verify(priceDisplayService).batchQueryPriceByLongShopId(request);
    }

    /**
     * Test when request is null should throw IllegalArgumentException
     */
    @Test
    public void testBatchGetPinProductBriefNullRequest() throws Throwable {
        // arrange - no arrangement needed for null test
        // act & assert
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> compositeAtomService.batchGetPinProductBrief(null));
        assertEquals("普通拼团Id为空", exception.getMessage());
    }

    /**
     * Test when longPinProductIds is empty should throw IllegalArgumentException
     */
    @Test
    public void testBatchGetPinProductBriefEmptyProductIds() throws Throwable {
        // arrange
        GetPinProductBriefReq req = new GetPinProductBriefReq();
        req.setLongPinProductIds(Collections.emptyList());
        // act & assert
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> compositeAtomService.batchGetPinProductBrief(req));
        assertEquals("普通拼团Id为空", exception.getMessage());
    }

    /**
     * Test valid request with non-empty productIds should return CompletableFuture
     */
    @Test
    public void testBatchGetPinProductBriefValidRequest() throws Throwable {
        // arrange
        GetPinProductBriefReq req = new GetPinProductBriefReq();
        List<Long> productIds = new ArrayList<>();
        productIds.add(12345L);
        productIds.add(67890L);
        req.setLongPinProductIds(productIds);
        // act
        CompletableFuture<Map<Integer, PinProductBrief>> result = compositeAtomService.batchGetPinProductBrief(req);
        // assert
        assertNotNull(result);
        verify(pinFacadeServiceFuture).mGetPinProductBrief(req);
    }

    /**
     * Test service call success should complete future successfully
     */
    @Test
    public void testBatchGetPinProductBriefServiceSuccess() throws Throwable {
        // arrange
        GetPinProductBriefReq req = new GetPinProductBriefReq();
        List<Long> productIds = new ArrayList<>();
        productIds.add(12345L);
        req.setLongPinProductIds(productIds);
        Map<Integer, PinProductBrief> mockResponse = new HashMap<>();
        PinProductBrief brief = new PinProductBrief();
        mockResponse.put(1, brief);
        doAnswer(invocation -> {
            PigeonCallbackUtils callback = (PigeonCallbackUtils) InvokerHelper.getCallback();
            if (callback != null) {
                callback.onSuccess(mockResponse);
            }
            return null;
        }).when(pinFacadeServiceFuture).mGetPinProductBrief(any());
        // act
        CompletableFuture<Map<Integer, PinProductBrief>> future = compositeAtomService.batchGetPinProductBrief(req);
        Map<Integer, PinProductBrief> result = future.get();
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        verify(pinFacadeServiceFuture).mGetPinProductBrief(req);
    }

    /**
     * Test service call failure should complete future exceptionally
     */
    @Test
    public void testBatchGetPinProductBriefServiceFailure() throws Throwable {
        // arrange
        GetPinProductBriefReq req = new GetPinProductBriefReq();
        List<Long> productIds = new ArrayList<>();
        productIds.add(12345L);
        req.setLongPinProductIds(productIds);
        RuntimeException mockException = new RuntimeException("Service error");
        doAnswer(invocation -> {
            PigeonCallbackUtils callback = (PigeonCallbackUtils) InvokerHelper.getCallback();
            if (callback != null) {
                callback.onFailure(mockException);
            }
            return null;
        }).when(pinFacadeServiceFuture).mGetPinProductBrief(any());
        // act
        CompletableFuture<Map<Integer, PinProductBrief>> future = compositeAtomService.batchGetPinProductBrief(req);
        // assert
        assertTrue(future.isCompletedExceptionally());
        verify(pinFacadeServiceFuture).mGetPinProductBrief(req);
    }

    /**
     * Test request with exactly MAX_SIZE items should be processed
     */
    @Test
    public void testBatchGetPinProductBriefMaxSizeRequest() throws Throwable {
        // arrange
        GetPinProductBriefReq req = new GetPinProductBriefReq();
        List<Long> productIds = new ArrayList<>(Collections.nCopies(100, 12345L));
        req.setLongPinProductIds(productIds);
        // act
        CompletableFuture<Map<Integer, PinProductBrief>> result = compositeAtomService.batchGetPinProductBrief(req);
        // assert
        assertNotNull(result);
        verify(pinFacadeServiceFuture).mGetPinProductBrief(req);
    }

    /**
     * Test successful case with valid data
     */
    @Test
    public void testQuerySaleDisplaySuccessWithData() throws Throwable {
        // arrange
        SalesDisplayQueryRequest request = new SalesDisplayQueryRequest();
        SalesSubjectParam subjectParam = SalesSubjectParam.bizDealGroup(1L);
        SalesDisplayInfoDTO displayInfo = new SalesDisplayInfoDTO();
        Map<SalesSubjectParam, SalesDisplayInfoDTO> expectedData = Collections.singletonMap(subjectParam, displayInfo);
        // Capture the callback
        ArgumentCaptor<InvocationCallback> callbackCaptor = ArgumentCaptor.forClass(InvocationCallback.class);
        // act
        CompletableFuture<Map<SalesSubjectParam, SalesDisplayInfoDTO>> future = compositeAtomService.querySaleDisplay(request);
        // Verify service was called
        verify(salesDisplayQueryService).batchQuerySales(request);
        // Get the callback and complete it
        InvocationCallback callback = InvokerHelper.getCallback();
        assertNotNull(callback);
        // Simulate successful response
        IResponse<Map<SalesSubjectParam, SalesDisplayInfoDTO>> response = IResponse.buildSuccess(expectedData);
        callback.onSuccess(response);
        // assert
        Map<SalesSubjectParam, SalesDisplayInfoDTO> result = future.get(1, TimeUnit.SECONDS);
        assertEquals(expectedData, result);
    }

    /**
     * Test when response is null
     */
    @Test
    public void testQuerySaleDisplayResponseNull() throws Throwable {
        // arrange
        SalesDisplayQueryRequest request = new SalesDisplayQueryRequest();
        // act
        CompletableFuture<Map<SalesSubjectParam, SalesDisplayInfoDTO>> future = compositeAtomService.querySaleDisplay(request);
        // Verify service was called
        verify(salesDisplayQueryService).batchQuerySales(request);
        // Get the callback and complete it with null
        InvocationCallback callback = InvokerHelper.getCallback();
        assertNotNull(callback);
        callback.onSuccess(null);
        // assert
        assertNull(future.get(1, TimeUnit.SECONDS));
    }

    /**
     * Test when response is not successful
     */
    @Test
    public void testQuerySaleDisplayResponseNotSuccess() throws Throwable {
        // arrange
        SalesDisplayQueryRequest request = new SalesDisplayQueryRequest();
        IResponse<Map<SalesSubjectParam, SalesDisplayInfoDTO>> response = mock(IResponse.class);
        when(response.isSuccess()).thenReturn(false);
        // act
        CompletableFuture<Map<SalesSubjectParam, SalesDisplayInfoDTO>> future = compositeAtomService.querySaleDisplay(request);
        // Verify service was called
        verify(salesDisplayQueryService).batchQuerySales(request);
        // Get the callback and complete it
        InvocationCallback callback = InvokerHelper.getCallback();
        assertNotNull(callback);
        callback.onSuccess(response);
        // assert
        assertNull(future.get(1, TimeUnit.SECONDS));
    }

    /**
     * Test when response data is null
     */
    @Test
    public void testQuerySaleDisplayResponseDataNull() throws Throwable {
        // arrange
        SalesDisplayQueryRequest request = new SalesDisplayQueryRequest();
        IResponse<Map<SalesSubjectParam, SalesDisplayInfoDTO>> response = mock(IResponse.class);
        when(response.isSuccess()).thenReturn(true);
        when(response.getData()).thenReturn(null);
        // act
        CompletableFuture<Map<SalesSubjectParam, SalesDisplayInfoDTO>> future = compositeAtomService.querySaleDisplay(request);
        // Verify service was called
        verify(salesDisplayQueryService).batchQuerySales(request);
        // Get the callback and complete it
        InvocationCallback callback = InvokerHelper.getCallback();
        assertNotNull(callback);
        callback.onSuccess(response);
        // assert
        assertNull(future.get(1, TimeUnit.SECONDS));
    }

    /**
     * Test when service throws exception
     */
    @Test
    public void testQuerySaleDisplayServiceThrowsException() throws Throwable {
        // arrange
        SalesDisplayQueryRequest request = new SalesDisplayQueryRequest();
        RuntimeException expectedException = new RuntimeException("Test exception");
        // act
        CompletableFuture<Map<SalesSubjectParam, SalesDisplayInfoDTO>> future = compositeAtomService.querySaleDisplay(request);
        // Verify service was called
        verify(salesDisplayQueryService).batchQuerySales(request);
        // Get the callback and complete it with exception
        InvocationCallback callback = InvokerHelper.getCallback();
        assertNotNull(callback);
        callback.onFailure(expectedException);
        // assert
        try {
            future.get(1, TimeUnit.SECONDS);
            fail("Expected exception was not thrown");
        } catch (Exception e) {
            assertEquals(expectedException, e.getCause());
        }
    }

    /**
     * Test successful case with valid input
     */
    @Test
    void testGetProductBaseInfo_Success() throws Throwable {
        // arrange
        List<Long> dealGroupIds = new ArrayList<>();
        dealGroupIds.add(1L);
        dealGroupIds.add(2L);
        List<DealGroupDTO> expectedList = new ArrayList<>();
        DealGroupDTO dto = new DealGroupDTO();
        dto.setDpDealGroupId(1L);
        expectedList.add(dto);
        QueryDealGroupListResponse response = new QueryDealGroupListResponse();
        QueryDealGroupListResult queryResult = new QueryDealGroupListResult();
        queryResult.setList(expectedList);
        response.setData(queryResult);
        when(dealGroupQueryServiceAtom.queryByDealGroupIds(any(QueryByDealGroupIdRequest.class))).thenReturn(response);
        // act
        List<DealGroupDTO> actualList = compositeAtomService.getProductBaseInfo(dealGroupIds, true);
        // assert
        assertNotNull(actualList);
        assertEquals(expectedList, actualList);
        // verify request
        ArgumentCaptor<QueryByDealGroupIdRequest> requestCaptor = ArgumentCaptor.forClass(QueryByDealGroupIdRequest.class);
        verify(dealGroupQueryServiceAtom).queryByDealGroupIds(requestCaptor.capture());
        QueryByDealGroupIdRequest actualRequest = requestCaptor.getValue();
        assertEquals(new HashSet<>(dealGroupIds), actualRequest.getDealGroupIds());
    }

    /**
     * Test when service throws exception
     */
    @Test
    void testGetProductBaseInfo_ServiceException() throws Throwable {
        // arrange
        List<Long> dealGroupIds = Collections.singletonList(1L);
        when(dealGroupQueryServiceAtom.queryByDealGroupIds(any(QueryByDealGroupIdRequest.class))).thenThrow(new TException("Service error"));
        // act
        List<DealGroupDTO> actualList = compositeAtomService.getProductBaseInfo(dealGroupIds, false);
        // assert
        assertNull(actualList);
        verify(dealGroupQueryServiceAtom).queryByDealGroupIds(any());
    }

    /**
     * Test when service returns response with null data
     */
    @Test
    void testGetProductBaseInfo_NullData() throws Throwable {
        // arrange
        List<Long> dealGroupIds = Collections.singletonList(1L);
        QueryDealGroupListResponse response = new QueryDealGroupListResponse();
        response.setData(null);
        when(dealGroupQueryServiceAtom.queryByDealGroupIds(any(QueryByDealGroupIdRequest.class))).thenReturn(response);
        // act
        List<DealGroupDTO> actualList = compositeAtomService.getProductBaseInfo(dealGroupIds, false);
        // assert
        assertNotNull(actualList);
        assertTrue(actualList.isEmpty());
        verify(dealGroupQueryServiceAtom).queryByDealGroupIds(any());
    }

    /**
     * Test when service returns response with null list
     */
    @Test
    void testGetProductBaseInfo_NullList() throws Throwable {
        // arrange
        List<Long> dealGroupIds = Collections.singletonList(1L);
        QueryDealGroupListResponse response = new QueryDealGroupListResponse();
        QueryDealGroupListResult queryResult = new QueryDealGroupListResult();
        queryResult.setList(null);
        response.setData(queryResult);
        when(dealGroupQueryServiceAtom.queryByDealGroupIds(any(QueryByDealGroupIdRequest.class))).thenReturn(response);
        // act
        List<DealGroupDTO> actualList = compositeAtomService.getProductBaseInfo(dealGroupIds, true);
        // assert
        assertNotNull(actualList);
        assertTrue(actualList.isEmpty());
        verify(dealGroupQueryServiceAtom).queryByDealGroupIds(any());
    }

    /**
     * Test when service returns response with empty list
     */
    @Test
    void testGetProductBaseInfo_EmptyList() throws Throwable {
        // arrange
        List<Long> dealGroupIds = Collections.singletonList(1L);
        QueryDealGroupListResponse response = new QueryDealGroupListResponse();
        QueryDealGroupListResult queryResult = new QueryDealGroupListResult();
        queryResult.setList(Collections.emptyList());
        response.setData(queryResult);
        when(dealGroupQueryServiceAtom.queryByDealGroupIds(any(QueryByDealGroupIdRequest.class))).thenReturn(response);
        // act
        List<DealGroupDTO> actualList = compositeAtomService.getProductBaseInfo(dealGroupIds, false);
        // assert
        assertNotNull(actualList);
        assertTrue(actualList.isEmpty());
        verify(dealGroupQueryServiceAtom).queryByDealGroupIds(any());
    }

    /**
     * Test request building with different isMt values
     */
    @Test
    void testGetProductBaseInfo_DifferentIsMtValues() throws Throwable {
        // arrange
        List<Long> dealGroupIds = Collections.singletonList(1L);
        QueryDealGroupListResponse response = new QueryDealGroupListResponse();
        QueryDealGroupListResult queryResult = new QueryDealGroupListResult();
        queryResult.setList(Collections.emptyList());
        response.setData(queryResult);
        when(dealGroupQueryServiceAtom.queryByDealGroupIds(any(QueryByDealGroupIdRequest.class))).thenReturn(response);
        // act & assert for both true and false values
        List<DealGroupDTO> resultTrue = compositeAtomService.getProductBaseInfo(dealGroupIds, true);
        List<DealGroupDTO> resultFalse = compositeAtomService.getProductBaseInfo(dealGroupIds, false);
        // verify both calls were made
        verify(dealGroupQueryServiceAtom, times(2)).queryByDealGroupIds(any());
        // assert results
        assertNotNull(resultTrue);
        assertNotNull(resultFalse);
        assertTrue(resultTrue.isEmpty());
        assertTrue(resultFalse.isEmpty());
    }

    /**
     * Test when input list is valid and service returns valid response
     */
    @Test
    void testGetProductBaseInfoByUnifiedId_ValidResponse() throws Throwable {
        // Arrange
        List<Long> unifiedProductId = new ArrayList<>();
        unifiedProductId.add(1L);
        unifiedProductId.add(2L);
        QueryDealGroupListResponse mockResponse = new QueryDealGroupListResponse();
        QueryDealGroupListResult mockResult = new QueryDealGroupListResult();
        List<DealGroupDTO> expectedList = new ArrayList<>();
        expectedList.add(new DealGroupDTO());
        mockResult.setList(expectedList);
        mockResponse.setData(mockResult);
        when(dealGroupQueryServiceAtom.queryByDealGroupIds(any(QueryByDealGroupIdRequest.class))).thenReturn(mockResponse);
        // Act
        List<DealGroupDTO> result = compositeAtomService.getProductBaseInfoByUnifiedId(unifiedProductId);
        // Assert
        assertNotNull(result);
        assertFalse(result.isEmpty());
        assertEquals(expectedList, result);
        verify(dealGroupQueryServiceAtom, times(1)).queryByDealGroupIds(any(QueryByDealGroupIdRequest.class));
    }

    /**
     * Test when input list is empty
     */
    @Test
    void testGetProductBaseInfoByUnifiedId_EmptyInputList() throws Throwable {
        // Arrange
        List<Long> unifiedProductId = Collections.emptyList();
        QueryDealGroupListResponse mockResponse = new QueryDealGroupListResponse();
        QueryDealGroupListResult mockResult = new QueryDealGroupListResult();
        mockResult.setList(Collections.emptyList());
        mockResponse.setData(mockResult);
        when(dealGroupQueryServiceAtom.queryByDealGroupIds(any(QueryByDealGroupIdRequest.class))).thenReturn(mockResponse);
        // Act
        List<DealGroupDTO> result = compositeAtomService.getProductBaseInfoByUnifiedId(unifiedProductId);
        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(dealGroupQueryServiceAtom, times(1)).queryByDealGroupIds(any(QueryByDealGroupIdRequest.class));
    }

    /**
     * Test when input list is null
     */
    @Test
    void testGetProductBaseInfoByUnifiedId_NullInputList() throws Throwable {
        // Arrange
        List<Long> unifiedProductId = null;
        // Act & Assert
        try {
            compositeAtomService.getProductBaseInfoByUnifiedId(unifiedProductId);
            fail("Expected NullPointerException");
        } catch (NullPointerException e) {
            verify(dealGroupQueryServiceAtom, never()).queryByDealGroupIds(any());
        }
    }

    /**
     * Test when service throws TException
     */
    @Test
    void testGetProductBaseInfoByUnifiedId_ServiceThrowsException() throws Throwable {
        // Arrange
        List<Long> unifiedProductId = new ArrayList<>();
        unifiedProductId.add(1L);
        when(dealGroupQueryServiceAtom.queryByDealGroupIds(any(QueryByDealGroupIdRequest.class))).thenThrow(new TException("Test exception"));
        // Act
        List<DealGroupDTO> result = compositeAtomService.getProductBaseInfoByUnifiedId(unifiedProductId);
        // Assert
        assertNull(result);
        verify(dealGroupQueryServiceAtom, times(1)).queryByDealGroupIds(any(QueryByDealGroupIdRequest.class));
    }

    /**
     * Test when service returns null response
     */
    @Test
    void testGetProductBaseInfoByUnifiedId_NullResponse() throws Throwable {
        // Arrange
        List<Long> unifiedProductId = new ArrayList<>();
        unifiedProductId.add(1L);
        when(dealGroupQueryServiceAtom.queryByDealGroupIds(any(QueryByDealGroupIdRequest.class))).thenReturn(null);
        // Act & Assert
        try {
            compositeAtomService.getProductBaseInfoByUnifiedId(unifiedProductId);
            fail("Expected NullPointerException");
        } catch (NullPointerException e) {
            verify(dealGroupQueryServiceAtom, times(1)).queryByDealGroupIds(any(QueryByDealGroupIdRequest.class));
        }
    }

    /**
     * Test when service returns response with null data
     */
    @Test
    void testGetProductBaseInfoByUnifiedId_ResponseWithNullData() throws Throwable {
        // Arrange
        List<Long> unifiedProductId = new ArrayList<>();
        unifiedProductId.add(1L);
        QueryDealGroupListResponse mockResponse = new QueryDealGroupListResponse();
        mockResponse.setData(null);
        when(dealGroupQueryServiceAtom.queryByDealGroupIds(any(QueryByDealGroupIdRequest.class))).thenReturn(mockResponse);
        // Act
        List<DealGroupDTO> result = compositeAtomService.getProductBaseInfoByUnifiedId(unifiedProductId);
        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(dealGroupQueryServiceAtom, times(1)).queryByDealGroupIds(any(QueryByDealGroupIdRequest.class));
    }

    /**
     * Test when service returns response with empty list
     */
    @Test
    void testGetProductBaseInfoByUnifiedId_ResponseWithEmptyList() throws Throwable {
        // Arrange
        List<Long> unifiedProductId = new ArrayList<>();
        unifiedProductId.add(1L);
        QueryDealGroupListResponse mockResponse = new QueryDealGroupListResponse();
        QueryDealGroupListResult mockResult = new QueryDealGroupListResult();
        mockResult.setList(Collections.emptyList());
        mockResponse.setData(mockResult);
        when(dealGroupQueryServiceAtom.queryByDealGroupIds(any(QueryByDealGroupIdRequest.class))).thenReturn(mockResponse);
        // Act
        List<DealGroupDTO> result = compositeAtomService.getProductBaseInfoByUnifiedId(unifiedProductId);
        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(dealGroupQueryServiceAtom, times(1)).queryByDealGroupIds(any(QueryByDealGroupIdRequest.class));
    }

    @Test
    public void testQueryDealProduct_Success() throws Throwable {
        // arrange
        DealProductRequest request = new DealProductRequest();
        request.setProductIds(Arrays.asList(1, 2, 3));
        DealProductResult expectedResult = new DealProductResult();
        expectedResult.setMessage("Success");
        doAnswer(invocation -> {
            InvocationCallback callback = InvokerHelper.getCallback();
            if (callback != null) {
                callback.onSuccess(expectedResult);
            }
            return null;
        }).when(dealProductService).query(any());
        // act
        CompletableFuture<DealProductResult> resultFuture = compositeAtomService.queryDealProduct(request);
        // assert
        DealProductResult actualResult = resultFuture.join();
        assertNotNull(actualResult);
        assertEquals(expectedResult.getMessage(), actualResult.getMessage());
        verify(dealProductService, times(1)).query(request);
    }

    @Test
    public void testQueryDealProduct_ServiceException() throws Throwable {
        // arrange
        DealProductRequest request = new DealProductRequest();
        request.setProductIds(Arrays.asList(1, 2, 3));
        doAnswer(invocation -> {
            InvocationCallback callback = InvokerHelper.getCallback();
            if (callback != null) {
                // Simulate service error
                callback.onSuccess(null);
            }
            return null;
        }).when(dealProductService).query(any());
        // act
        CompletableFuture<DealProductResult> resultFuture = compositeAtomService.queryDealProduct(request);
        // assert
        DealProductResult actualResult = resultFuture.join();
        assertNull(actualResult);
        verify(dealProductService, times(1)).query(request);
    }

    @Test
    public void testQueryDealProduct_NullRequest() throws Throwable {
        // arrange
        doAnswer(invocation -> {
            InvocationCallback callback = InvokerHelper.getCallback();
            if (callback != null) {
                callback.onSuccess(null);
            }
            return null;
        }).when(dealProductService).query(null);
        // act
        CompletableFuture<DealProductResult> resultFuture = compositeAtomService.queryDealProduct(null);
        // assert
        DealProductResult actualResult = resultFuture.join();
        assertNull(actualResult);
        verify(dealProductService, times(1)).query(null);
    }

    @Test
    public void testQueryDealProduct_EmptyProductIds() throws Throwable {
        // arrange
        DealProductRequest request = new DealProductRequest();
        request.setProductIds(Arrays.asList());
        DealProductResult expectedResult = new DealProductResult();
        expectedResult.setMessage("Empty result");
        doAnswer(invocation -> {
            InvocationCallback callback = InvokerHelper.getCallback();
            if (callback != null) {
                callback.onSuccess(expectedResult);
            }
            return null;
        }).when(dealProductService).query(any());
        // act
        CompletableFuture<DealProductResult> resultFuture = compositeAtomService.queryDealProduct(request);
        // assert
        DealProductResult actualResult = resultFuture.join();
        assertNotNull(actualResult);
        assertEquals(expectedResult.getMessage(), actualResult.getMessage());
        verify(dealProductService, times(1)).query(request);
    }

    @Test
    public void testPreTimesCardsV2_Success() throws Throwable {
        // arrange
        QueryDealGroupCardBarSummaryRequest request = new QueryDealGroupCardBarSummaryRequest(123, 456L, 789L, 1);
        CardResponse<CardSummaryBarDTO> expectedResponse = new CardResponse<>();
        expectedResponse.setCode(200);
        // Mock pigeon callback behavior
        doAnswer(invocation -> {
            PigeonCallbackUtils callback = (PigeonCallbackUtils) InvokerHelper.getCallback();
            callback.onSuccess(expectedResponse);
            return null;
        }).when(timesCardNavigation).loadDealGroupCardBarSummary(request);
        // act
        CompletableFuture<CardResponse<CardSummaryBarDTO>> future = compositeAtomService.preTimesCardsV2(request);
        CardResponse<CardSummaryBarDTO> result = future.get();
        // assert
        assertNotNull(result);
        assertEquals(200, result.getCode());
        verify(timesCardNavigation).loadDealGroupCardBarSummary(request);
    }

    @Test
    public void testPreTimesCardsV2_Exception() throws Throwable {
        // arrange
        QueryDealGroupCardBarSummaryRequest request = new QueryDealGroupCardBarSummaryRequest(123, 456L, 789L, 1);
        RpcException exception = new RpcException("Test exception");
        // Mock pigeon callback behavior
        doAnswer(invocation -> {
            PigeonCallbackUtils callback = (PigeonCallbackUtils) InvokerHelper.getCallback();
            callback.onFailure(exception);
            return null;
        }).when(timesCardNavigation).loadDealGroupCardBarSummary(request);
        // act
        CompletableFuture<CardResponse<CardSummaryBarDTO>> future = compositeAtomService.preTimesCardsV2(request);
        CardResponse<CardSummaryBarDTO> result = future.get();
        // assert
        assertNull(result);
        verify(timesCardNavigation).loadDealGroupCardBarSummary(request);
    }

    @Test
    public void testPreTimesCardsV2_NullRequest() throws Throwable {
        // arrange
        QueryDealGroupCardBarSummaryRequest request = null;
        // Mock pigeon callback behavior
        doAnswer(invocation -> {
            PigeonCallbackUtils callback = (PigeonCallbackUtils) InvokerHelper.getCallback();
            callback.onFailure(new NullPointerException("Request is null"));
            return null;
        }).when(timesCardNavigation).loadDealGroupCardBarSummary(isNull());
        // act
        CompletableFuture<CardResponse<CardSummaryBarDTO>> future = compositeAtomService.preTimesCardsV2(request);
        CardResponse<CardSummaryBarDTO> result = future.get();
        // assert
        assertNull(result);
        verify(timesCardNavigation).loadDealGroupCardBarSummary(isNull());
    }

    @Test
    public void testPreTimesCardsV2_MinValidValues() throws Throwable {
        // arrange
        QueryDealGroupCardBarSummaryRequest request = new QueryDealGroupCardBarSummaryRequest(1, 1L, 1L, 0);
        CardResponse<CardSummaryBarDTO> expectedResponse = new CardResponse<>();
        expectedResponse.setCode(200);
        // Mock pigeon callback behavior
        doAnswer(invocation -> {
            PigeonCallbackUtils callback = (PigeonCallbackUtils) InvokerHelper.getCallback();
            callback.onSuccess(expectedResponse);
            return null;
        }).when(timesCardNavigation).loadDealGroupCardBarSummary(request);
        // act
        CompletableFuture<CardResponse<CardSummaryBarDTO>> future = compositeAtomService.preTimesCardsV2(request);
        CardResponse<CardSummaryBarDTO> result = future.get();
        // assert
        assertNotNull(result);
        assertEquals(200, result.getCode());
        verify(timesCardNavigation).loadDealGroupCardBarSummary(request);
    }

    @Test
    public void testPreTimesCardsV2_MaxValidValues() throws Throwable {
        // arrange
        QueryDealGroupCardBarSummaryRequest request = new QueryDealGroupCardBarSummaryRequest(Integer.MAX_VALUE, Long.MAX_VALUE, Long.MAX_VALUE, Integer.MAX_VALUE);
        CardResponse<CardSummaryBarDTO> expectedResponse = new CardResponse<>();
        expectedResponse.setCode(200);
        // Mock pigeon callback behavior
        doAnswer(invocation -> {
            PigeonCallbackUtils callback = (PigeonCallbackUtils) InvokerHelper.getCallback();
            callback.onSuccess(expectedResponse);
            return null;
        }).when(timesCardNavigation).loadDealGroupCardBarSummary(request);
        // act
        CompletableFuture<CardResponse<CardSummaryBarDTO>> future = compositeAtomService.preTimesCardsV2(request);
        CardResponse<CardSummaryBarDTO> result = future.get();
        // assert
        assertNotNull(result);
        assertEquals(200, result.getCode());
        verify(timesCardNavigation).loadDealGroupCardBarSummary(request);
    }

    @Test
    public void testGetUserQualificationResponseSuccess() throws Throwable {
        // arrange
        GetUserQualificationOpenRequest request = new GetUserQualificationOpenRequest();
        request.setCommonRequest(new CommonRequest());
        GetUserQualificationOpenResponse expectedResponse = new GetUserQualificationOpenResponse();
        expectedResponse.setCode(200);
        when(stateSubsidiesOpenServiceFuture.getUserQualificationOpen(request)).thenReturn(expectedResponse);
        // act
        CompletableFuture<GetUserQualificationOpenResponse> result = compositeAtomService.getUserQualificationResponse(request);
        // assert
        assertNotNull(result);
        assertEquals(expectedResponse, result.get());
        verify(stateSubsidiesOpenServiceFuture).getUserQualificationOpen(request);
    }

    @Test
    public void testGetUserQualificationResponseServiceThrowsException() throws Throwable {
        // arrange
        GetUserQualificationOpenRequest request = new GetUserQualificationOpenRequest();
        request.setCommonRequest(new CommonRequest());
        when(stateSubsidiesOpenServiceFuture.getUserQualificationOpen(request)).thenThrow(new TException("Service error"));
        // act
        CompletableFuture<GetUserQualificationOpenResponse> result = compositeAtomService.getUserQualificationResponse(request);
        // assert
        assertNotNull(result);
        assertNull(result.get());
        verify(stateSubsidiesOpenServiceFuture).getUserQualificationOpen(request);
    }

    @Test
    public void testGetUserQualificationResponseNullRequest() throws Throwable {
        // arrange
        GetUserQualificationOpenRequest request = null;
        when(stateSubsidiesOpenServiceFuture.getUserQualificationOpen(null)).thenThrow(new TException("Null request"));
        // act
        CompletableFuture<GetUserQualificationOpenResponse> result = compositeAtomService.getUserQualificationResponse(request);
        // assert
        assertNotNull(result);
        assertNull(result.get());
        verify(stateSubsidiesOpenServiceFuture).getUserQualificationOpen(null);
    }

    @Test
    public void testGetUserQualificationResponseFullRequest() throws Throwable {
        // arrange
        GetUserQualificationOpenRequest request = new GetUserQualificationOpenRequest();
        CommonRequest commonRequest = new CommonRequest();
        commonRequest.setUserId(123L);
        commonRequest.setCityId(1);
        request.setCommonRequest(commonRequest);
        request.setActivityId(ActivityIDEnum.BEIJING_2025);
        request.setBizLine(BizLineEnum.SHAN_GOU);
        request.setSource("test-source");
        GetUserQualificationOpenResponse expectedResponse = new GetUserQualificationOpenResponse();
        expectedResponse.setCode(200);
        expectedResponse.setMsg("Success");
        expectedResponse.setQualificationInfo(new ArrayList<>());
        when(stateSubsidiesOpenServiceFuture.getUserQualificationOpen(request)).thenReturn(expectedResponse);
        // act
        CompletableFuture<GetUserQualificationOpenResponse> result = compositeAtomService.getUserQualificationResponse(request);
        // assert
        assertNotNull(result);
        assertEquals(expectedResponse, result.get());
        verify(stateSubsidiesOpenServiceFuture).getUserQualificationOpen(request);
    }

    @Test
    public void testGetUserQualificationResponseEmptyResponse() throws Throwable {
        // arrange
        GetUserQualificationOpenRequest request = new GetUserQualificationOpenRequest();
        request.setCommonRequest(new CommonRequest());
        GetUserQualificationOpenResponse expectedResponse = new GetUserQualificationOpenResponse();
        expectedResponse.setQualificationInfo(new ArrayList<>());
        when(stateSubsidiesOpenServiceFuture.getUserQualificationOpen(request)).thenReturn(expectedResponse);
        // act
        CompletableFuture<GetUserQualificationOpenResponse> result = compositeAtomService.getUserQualificationResponse(request);
        // assert
        assertNotNull(result);
        assertNotNull(result.get());
        assertNotNull(result.get().getQualificationInfo());
        assertTrue(result.get().getQualificationInfo().isEmpty());
        verify(stateSubsidiesOpenServiceFuture).getUserQualificationOpen(request);
    }

    @Test
    public void testGetUserQualificationResponseWithQualificationInfo() throws Throwable {
        // arrange
        GetUserQualificationOpenRequest request = new GetUserQualificationOpenRequest();
        request.setCommonRequest(new CommonRequest());
        GetUserQualificationOpenResponse expectedResponse = new GetUserQualificationOpenResponse();
        expectedResponse.setCode(200);
        ArrayList<QualificationInfo> qualificationInfoList = new ArrayList<>();
        qualificationInfoList.add(new QualificationInfo());
        expectedResponse.setQualificationInfo(qualificationInfoList);
        when(stateSubsidiesOpenServiceFuture.getUserQualificationOpen(request)).thenReturn(expectedResponse);
        // act
        CompletableFuture<GetUserQualificationOpenResponse> result = compositeAtomService.getUserQualificationResponse(request);
        // assert
        assertNotNull(result);
        assertNotNull(result.get());
        assertNotNull(result.get().getQualificationInfo());
        assertEquals(1, result.get().getQualificationInfo().size());
        verify(stateSubsidiesOpenServiceFuture).getUserQualificationOpen(request);
    }

    @Test
    public void testQueryDiscountCardInfoSuccess() throws Throwable {
        // arrange
        QueryDiscountCardReq request = new QueryDiscountCardReq();
        request.setPlatform(1);
        request.setUserId(123L);
        request.setShopId(456L);
        request.setDealGroupId(789);
        QueryCardInfoDTO expectedResponse = new QueryCardInfoDTO();
        expectedResponse.setTitle("Test Card");
        expectedResponse.setDealPriceWithCard("100");
        expectedResponse.setShowType(1);
        // act
        CompletableFuture<QueryCardInfoDTO> future = compositeAtomService.queryDiscountCardInfo(request);
        // Trigger the callback immediately
        PigeonCallbackUtils callback = (PigeonCallbackUtils) InvokerHelper.getCallback();
        callback.onSuccess(expectedResponse);
        // assert
        QueryCardInfoDTO result = future.get(100, TimeUnit.MILLISECONDS);
        assertNotNull(result);
        assertEquals("Test Card", result.getTitle());
        assertEquals("100", result.getDealPriceWithCard());
        assertEquals(1, result.getShowType());
        verify(dzCardExposureService).queryCardInfo(eq(request));
    }

    @Test
    public void testQueryDiscountCardInfoServiceException() throws Throwable {
        // arrange
        QueryDiscountCardReq request = new QueryDiscountCardReq();
        request.setPlatform(1);
        request.setUserId(123L);
        RuntimeException expectedException = new RuntimeException("Service error");
        // act
        CompletableFuture<QueryCardInfoDTO> future = compositeAtomService.queryDiscountCardInfo(request);
        // Trigger the callback immediately with error
        PigeonCallbackUtils callback = (PigeonCallbackUtils) InvokerHelper.getCallback();
        callback.onFailure(expectedException);
        // assert
        ExecutionException exception = assertThrows(ExecutionException.class, () -> future.get(100, TimeUnit.MILLISECONDS));
        assertEquals("Service error", exception.getCause().getMessage());
        verify(dzCardExposureService).queryCardInfo(eq(request));
    }

    @Test
    public void testQueryDiscountCardInfoNullRequest() throws Throwable {
        // arrange
        QueryDiscountCardReq request = null;
        QueryCardInfoDTO expectedResponse = new QueryCardInfoDTO();
        // act
        CompletableFuture<QueryCardInfoDTO> future = compositeAtomService.queryDiscountCardInfo(request);
        // Trigger the callback immediately
        PigeonCallbackUtils callback = (PigeonCallbackUtils) InvokerHelper.getCallback();
        callback.onSuccess(expectedResponse);
        // assert
        assertNotNull(future);
        assertNotNull(future.get(100, TimeUnit.MILLISECONDS));
        verify(dzCardExposureService).queryCardInfo(null);
    }

    @Test
    public void testQueryDiscountCardInfoFullRequest() throws Throwable {
        // arrange
        QueryDiscountCardReq request = new QueryDiscountCardReq();
        request.setPlatform(1);
        request.setUserId(123L);
        request.setUnionId("unionId123");
        request.setShopId(456L);
        request.setDealGroupId(789);
        request.setCityId(1);
        request.setIp("127.0.0.1");
        request.setClientType(1);
        request.setDpId("dpId123");
        request.setClientVersion("1.0.0");
        request.setVersion("v1");
        request.setExtMap("{\"key\":\"value\"}");
        QueryCardInfoDTO expectedResponse = new QueryCardInfoDTO();
        expectedResponse.setTitle("Full Test Card");
        expectedResponse.setDealPriceWithCard("200");
        expectedResponse.setShowType(2);
        expectedResponse.setHasUserCard(true);
        CardBottomTips bottomTips = new CardBottomTips();
        bottomTips.setShowTag(true);
        bottomTips.setCardTip("Special Offer");
        expectedResponse.setCardBottomTipsDo(bottomTips);
        // act
        CompletableFuture<QueryCardInfoDTO> future = compositeAtomService.queryDiscountCardInfo(request);
        // Trigger the callback immediately
        PigeonCallbackUtils callback = (PigeonCallbackUtils) InvokerHelper.getCallback();
        callback.onSuccess(expectedResponse);
        // assert
        QueryCardInfoDTO result = future.get(100, TimeUnit.MILLISECONDS);
        assertNotNull(result);
        assertEquals("Full Test Card", result.getTitle());
        assertEquals("200", result.getDealPriceWithCard());
        assertEquals(2, result.getShowType());
        assertTrue(result.isHasUserCard());
        assertNotNull(result.getCardBottomTipsDo());
        assertTrue(result.getCardBottomTipsDo().isShowTag());
        assertEquals("Special Offer", result.getCardBottomTipsDo().getCardTip());
        verify(dzCardExposureService).queryCardInfo(eq(request));
    }

    @Test
    @DisplayName("Test batchQuerySaleDealGroupId with MT platform success case")
    public void testBatchQuerySaleDealGroupId_SuccessWithMtPlatform() throws Throwable {
        // arrange
        long dpShopId = 12345L;
        Map<Long, List<Long>> expectedResult = new HashMap<>();
        expectedResult.put(dpShopId, Collections.singletonList(67890L));
        when(dealShopQueryService.batchQuerySaleDealGroupId(eq(Sets.newHashSet(dpShopId)), eq(200), eq(1), eq(100))).thenReturn(expectedResult);
        // act
        CompletableFuture<Map<Long, List<Long>>> future = compositeAtomService.batchQuerySaleDealGroupId(dpShopId, true);
        // assert
        assertNotNull(future);
        verify(dealShopQueryService).batchQuerySaleDealGroupId(eq(Sets.newHashSet(dpShopId)), eq(200), eq(1), eq(100));
    }

    @Test
    @DisplayName("Test batchQuerySaleDealGroupId with DP platform success case")
    public void testBatchQuerySaleDealGroupId_SuccessWithDpPlatform() throws Throwable {
        // arrange
        long dpShopId = 12345L;
        Map<Long, List<Long>> expectedResult = new HashMap<>();
        expectedResult.put(dpShopId, Collections.singletonList(67890L));
        when(dealShopQueryService.batchQuerySaleDealGroupId(eq(Sets.newHashSet(dpShopId)), eq(100), eq(1), eq(100))).thenReturn(expectedResult);
        // act
        CompletableFuture<Map<Long, List<Long>>> future = compositeAtomService.batchQuerySaleDealGroupId(dpShopId, false);
        // assert
        assertNotNull(future);
        verify(dealShopQueryService).batchQuerySaleDealGroupId(eq(Sets.newHashSet(dpShopId)), eq(100), eq(1), eq(100));
    }

    @Test
    @DisplayName("Test batchQuerySaleDealGroupId with zero shopId")
    public void testBatchQuerySaleDealGroupId_ZeroShopId() throws Throwable {
        // arrange
        long dpShopId = 0L;
        Map<Long, List<Long>> expectedResult = new HashMap<>();
        expectedResult.put(dpShopId, Collections.emptyList());
        when(dealShopQueryService.batchQuerySaleDealGroupId(eq(Sets.newHashSet(dpShopId)), eq(100), eq(1), eq(100))).thenReturn(expectedResult);
        // act
        CompletableFuture<Map<Long, List<Long>>> future = compositeAtomService.batchQuerySaleDealGroupId(dpShopId, false);
        // assert
        assertNotNull(future);
        verify(dealShopQueryService).batchQuerySaleDealGroupId(eq(Sets.newHashSet(dpShopId)), eq(100), eq(1), eq(100));
    }

    @Test
    @DisplayName("Test batchQuerySaleDealGroupId with negative shopId")
    public void testBatchQuerySaleDealGroupId_NegativeShopId() throws Throwable {
        // arrange
        long dpShopId = -12345L;
        Map<Long, List<Long>> expectedResult = new HashMap<>();
        expectedResult.put(dpShopId, Collections.emptyList());
        when(dealShopQueryService.batchQuerySaleDealGroupId(eq(Sets.newHashSet(dpShopId)), eq(100), eq(1), eq(100))).thenReturn(expectedResult);
        // act
        CompletableFuture<Map<Long, List<Long>>> future = compositeAtomService.batchQuerySaleDealGroupId(dpShopId, false);
        // assert
        assertNotNull(future);
        verify(dealShopQueryService).batchQuerySaleDealGroupId(eq(Sets.newHashSet(dpShopId)), eq(100), eq(1), eq(100));
    }

    @Test
    @DisplayName("Test batchQuerySaleDealGroupId when service returns empty result")
    public void testBatchQuerySaleDealGroupId_EmptyResult() throws Throwable {
        // arrange
        long dpShopId = 12345L;
        Map<Long, List<Long>> expectedResult = new HashMap<>();
        when(dealShopQueryService.batchQuerySaleDealGroupId(eq(Sets.newHashSet(dpShopId)), eq(100), eq(1), eq(100))).thenReturn(expectedResult);
        // act
        CompletableFuture<Map<Long, List<Long>>> future = compositeAtomService.batchQuerySaleDealGroupId(dpShopId, false);
        // assert
        assertNotNull(future);
        verify(dealShopQueryService).batchQuerySaleDealGroupId(eq(Sets.newHashSet(dpShopId)), eq(100), eq(1), eq(100));
    }

    @Test
    public void testQueryDealSubtitleNormalSuccess() throws Throwable {
        // arrange
        DealProductRequest request = new DealProductRequest();
        request.setPlanId("testPlan");
        DealProductResult expectedResult = new DealProductResult();
        doAnswer((Answer<Void>) invocation -> {
            InvocationCallback callback = InvokerHelper.getCallback();
            callback.onSuccess(expectedResult);
            return null;
        }).when(dealProductBizQueryService).queryDealSubtitle(any());
        // act
        CompletableFuture<DealProductResult> resultFuture = compositeAtomService.queryDealSubtitle(request);
        // assert
        DealProductResult result = resultFuture.get(1, TimeUnit.SECONDS);
        assertNotNull(result);
        assertEquals(expectedResult, result);
        verify(dealProductBizQueryService).queryDealSubtitle(request);
    }

    @Test
    public void testQueryDealSubtitleServiceThrowsException() throws Throwable {
        // arrange
        DealProductRequest request = new DealProductRequest();
        request.setPlanId("testPlan");
        doAnswer((Answer<Void>) invocation -> {
            InvocationCallback callback = InvokerHelper.getCallback();
            callback.onFailure(new RuntimeException("Service error"));
            return null;
        }).when(dealProductBizQueryService).queryDealSubtitle(any());
        // act
        CompletableFuture<DealProductResult> resultFuture = compositeAtomService.queryDealSubtitle(request);
        // assert
        DealProductResult result = resultFuture.get(1, TimeUnit.SECONDS);
        assertNull(result);
        verify(dealProductBizQueryService).queryDealSubtitle(request);
    }

    @Test
    public void testQueryDealSubtitleNullRequest() throws Throwable {
        // arrange
        doAnswer((Answer<Void>) invocation -> {
            InvocationCallback callback = InvokerHelper.getCallback();
            callback.onSuccess(null);
            return null;
        }).when(dealProductBizQueryService).queryDealSubtitle(null);
        // act
        CompletableFuture<DealProductResult> resultFuture = compositeAtomService.queryDealSubtitle(null);
        // assert
        DealProductResult result = resultFuture.get(1, TimeUnit.SECONDS);
        assertNull(result);
        verify(dealProductBizQueryService).queryDealSubtitle(null);
    }

    @Test
    public void testQueryDealSubtitleEmptyRequestFields() throws Throwable {
        // arrange
        DealProductRequest request = new DealProductRequest();
        DealProductResult expectedResult = new DealProductResult();
        doAnswer((Answer<Void>) invocation -> {
            InvocationCallback callback = InvokerHelper.getCallback();
            callback.onSuccess(expectedResult);
            return null;
        }).when(dealProductBizQueryService).queryDealSubtitle(any());
        // act
        CompletableFuture<DealProductResult> resultFuture = compositeAtomService.queryDealSubtitle(request);
        // assert
        DealProductResult result = resultFuture.get(1, TimeUnit.SECONDS);
        assertNotNull(result);
        assertEquals(expectedResult, result);
        verify(dealProductBizQueryService).queryDealSubtitle(request);
    }

    @Test
    public void testQueryDealSubtitleCallbackException() throws Throwable {
        // arrange
        DealProductRequest request = new DealProductRequest();
        request.setPlanId("testPlan");
        doAnswer((Answer<Void>) invocation -> {
            InvocationCallback callback = InvokerHelper.getCallback();
            callback.onFailure(new RuntimeException("Callback error"));
            return null;
        }).when(dealProductBizQueryService).queryDealSubtitle(any());
        // act
        CompletableFuture<DealProductResult> resultFuture = compositeAtomService.queryDealSubtitle(request);
        // assert
        DealProductResult result = resultFuture.get(1, TimeUnit.SECONDS);
        assertNull(result);
        verify(dealProductBizQueryService).queryDealSubtitle(request);
    }

    @Test
    public void testRecommendSuccess() throws Throwable {
        // arrange
        RecommendParameters parameters = new RecommendParameters();
        Response<RecommendResult<Object>> expectedResponse = new Response<>();
        when(recommendService.recommend(any(RecommendParameters.class), any(Class.class))).thenReturn(expectedResponse);
        // act
        CompletableFuture<Response<RecommendResult>> future = compositeAtomService.recommend(parameters);
        // assert
        assertNotNull(future);
        verify(recommendService).recommend(eq(parameters), any(Class.class));
    }

    @Test
    public void testRecommendRpcException() throws Throwable {
        // arrange
        RecommendParameters parameters = new RecommendParameters();
        when(recommendService.recommend(any(RecommendParameters.class), any(Class.class))).thenThrow(new RpcException("Test RpcException"));
        // act
        CompletableFuture<Response<RecommendResult>> future = compositeAtomService.recommend(parameters);
        // assert
        assertNotNull(future);
        // Should complete with null
        assertNull(future.get());
    }

    @Test
    public void testRecommendGeneralException() throws Throwable {
        // arrange
        RecommendParameters parameters = new RecommendParameters();
        when(recommendService.recommend(any(RecommendParameters.class), any(Class.class))).thenThrow(new RuntimeException("Test Exception"));
        // act
        CompletableFuture<Response<RecommendResult>> future = compositeAtomService.recommend(parameters);
        // assert
        assertNotNull(future);
        // Should complete with null
        assertNull(future.get());
    }

    @Test
    public void testRecommendCallbackSetup() throws Throwable {
        // arrange
        RecommendParameters parameters = new RecommendParameters();
        Response<RecommendResult<Object>> expectedResponse = new Response<>();
        when(recommendService.recommend(any(RecommendParameters.class), any(Class.class))).thenReturn(expectedResponse);
        // act
        CompletableFuture<Response<RecommendResult>> future = compositeAtomService.recommend(parameters);
        // assert
        assertNotNull(future);
        verify(recommendService).recommend(eq(parameters), any(Class.class));
    }

    @Test
    public void testQueryMtByDpIdsL_NormalCase() throws Throwable {
        // arrange
        List<Long> dpShopIds = Arrays.asList(1001L, 1002L);
        Map<Long, List<Long>> expectedResult = new HashMap<>();
        expectedResult.put(1001L, Arrays.asList(2001L));
        expectedResult.put(1002L, Arrays.asList(2002L));
        when(poiRelationService.queryMtByDpIdsL(dpShopIds)).thenReturn(expectedResult);
        // act
        CompletableFuture<Map<Long, List<Long>>> future = compositeAtomService.queryMtByDpIdsL(dpShopIds);
        // assert
        assertNotNull(future);
        verify(poiRelationService).queryMtByDpIdsL(dpShopIds);
    }

    @Test
    public void testQueryMtByDpIdsL_EmptyList() throws Throwable {
        // arrange
        List<Long> dpShopIds = Collections.emptyList();
        Map<Long, List<Long>> expectedResult = Collections.emptyMap();
        when(poiRelationService.queryMtByDpIdsL(dpShopIds)).thenReturn(expectedResult);
        // act
        CompletableFuture<Map<Long, List<Long>>> future = compositeAtomService.queryMtByDpIdsL(dpShopIds);
        // assert
        assertNotNull(future);
        verify(poiRelationService).queryMtByDpIdsL(dpShopIds);
    }

    @Test
    public void testQueryMtByDpIdsL_NullInput() throws Throwable {
        // arrange
        when(poiRelationService.queryMtByDpIdsL(null)).thenThrow(new IllegalArgumentException());
        // act
        CompletableFuture<Map<Long, List<Long>>> result = compositeAtomService.queryMtByDpIdsL(null);
        // assert
        assertNull(result);
        verify(poiRelationService).queryMtByDpIdsL(null);
    }

    @Test
    public void testQueryMtByDpIdsL_ServiceThrowsRpcException() throws Throwable {
        // arrange
        List<Long> dpShopIds = Arrays.asList(1001L);
        RpcException mockException = new RpcException("Test RpcException");
        when(poiRelationService.queryMtByDpIdsL(dpShopIds)).thenThrow(mockException);
        // act
        CompletableFuture<Map<Long, List<Long>>> result = compositeAtomService.queryMtByDpIdsL(dpShopIds);
        // assert
        assertNull(result);
        verify(poiRelationService).queryMtByDpIdsL(dpShopIds);
    }

    @Test
    public void testQueryMtByDpIdsL_ServiceThrowsRuntimeException() throws Throwable {
        // arrange
        List<Long> dpShopIds = Arrays.asList(1001L);
        RuntimeException mockException = new RuntimeException("Test RuntimeException");
        when(poiRelationService.queryMtByDpIdsL(dpShopIds)).thenThrow(mockException);
        // act
        CompletableFuture<Map<Long, List<Long>>> result = compositeAtomService.queryMtByDpIdsL(dpShopIds);
        // assert
        assertNull(result);
        verify(poiRelationService).queryMtByDpIdsL(dpShopIds);
    }

    @Test
    public void testQueryMtByDpIdsL_LargeList() throws Throwable {
        // arrange
        List<Long> dpShopIds = Collections.nCopies(1000, 1001L);
        Map<Long, List<Long>> expectedResult = new HashMap<>();
        expectedResult.put(1001L, Arrays.asList(2001L));
        when(poiRelationService.queryMtByDpIdsL(dpShopIds)).thenReturn(expectedResult);
        // act
        CompletableFuture<Map<Long, List<Long>>> future = compositeAtomService.queryMtByDpIdsL(dpShopIds);
        // assert
        assertNotNull(future);
        verify(poiRelationService).queryMtByDpIdsL(dpShopIds);
    }

    @Test
    public void testQueryMtByDpIdsL_LargeIdValues() throws Throwable {
        // arrange
        List<Long> dpShopIds = Arrays.asList(Long.MAX_VALUE, Long.MIN_VALUE);
        Map<Long, List<Long>> expectedResult = new HashMap<>();
        expectedResult.put(Long.MAX_VALUE, Arrays.asList(2001L));
        expectedResult.put(Long.MIN_VALUE, Arrays.asList(2002L));
        when(poiRelationService.queryMtByDpIdsL(dpShopIds)).thenReturn(expectedResult);
        // act
        CompletableFuture<Map<Long, List<Long>>> future = compositeAtomService.queryMtByDpIdsL(dpShopIds);
        // assert
        assertNotNull(future);
        verify(poiRelationService).queryMtByDpIdsL(dpShopIds);
    }

    @Test
    public void testMultiGetSalesSuccess() throws Throwable {
        // arrange
        ProductParam productParam = ProductParam.productScene(456L, 1);
        SalesDisplayRequest request = SalesDisplayRequest.multiQuery(Collections.singletonList(productParam));
        Map<ProductParam, SalesDisplayDTO> expectedResult = new HashMap<>();
        SalesDisplayDTO salesDisplayDTO = new SalesDisplayDTO();
        expectedResult.put(productParam, salesDisplayDTO);
        doAnswer(new Answer<Void>() {

            @Override
            public Void answer(InvocationOnMock invocation) {
                InvocationCallback callback = InvokerHelper.getCallback();
                callback.onSuccess(expectedResult);
                return null;
            }
        }).when(productSceneSalesDisplayService).multiGetSales(any());
        // act
        CompletableFuture<Map<ProductParam, SalesDisplayDTO>> future = compositeAtomService.multiGetSales(request);
        // assert
        assertNotNull(future);
        Map<ProductParam, SalesDisplayDTO> result = future.getNow(null);
        assertEquals(expectedResult, result);
        verify(productSceneSalesDisplayService).multiGetSales(request);
    }

    @Test
    public void testMultiGetSalesServiceException() throws Throwable {
        // arrange
        ProductParam productParam = ProductParam.productScene(456L, 1);
        SalesDisplayRequest request = SalesDisplayRequest.multiQuery(Collections.singletonList(productParam));
        doAnswer(new Answer<Void>() {

            @Override
            public Void answer(InvocationOnMock invocation) {
                InvocationCallback callback = InvokerHelper.getCallback();
                callback.onFailure(new RuntimeException("Service error"));
                return null;
            }
        }).when(productSceneSalesDisplayService).multiGetSales(any());
        // act
        CompletableFuture<Map<ProductParam, SalesDisplayDTO>> future = compositeAtomService.multiGetSales(request);
        // assert
        assertNotNull(future);
        Map<ProductParam, SalesDisplayDTO> result = future.getNow(null);
        assertNull(result);
        verify(productSceneSalesDisplayService).multiGetSales(request);
    }

    @Test
    public void testMultiGetSalesEmptyProductList() throws Throwable {
        // arrange
        SalesDisplayRequest request = SalesDisplayRequest.multiQuery(Collections.emptyList());
        Map<ProductParam, SalesDisplayDTO> expectedResult = Collections.emptyMap();
        doAnswer(new Answer<Void>() {

            @Override
            public Void answer(InvocationOnMock invocation) {
                InvocationCallback callback = InvokerHelper.getCallback();
                callback.onSuccess(expectedResult);
                return null;
            }
        }).when(productSceneSalesDisplayService).multiGetSales(any());
        // act
        CompletableFuture<Map<ProductParam, SalesDisplayDTO>> future = compositeAtomService.multiGetSales(request);
        // assert
        assertNotNull(future);
        Map<ProductParam, SalesDisplayDTO> result = future.getNow(null);
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(productSceneSalesDisplayService).multiGetSales(request);
    }

    @Test
    public void testMultiGetSalesNullRequest() throws Throwable {
        // arrange
        doAnswer(new Answer<Void>() {

            @Override
            public Void answer(InvocationOnMock invocation) {
                InvocationCallback callback = InvokerHelper.getCallback();
                callback.onFailure(new NullPointerException());
                return null;
            }
        }).when(productSceneSalesDisplayService).multiGetSales(null);
        // act
        CompletableFuture<Map<ProductParam, SalesDisplayDTO>> future = compositeAtomService.multiGetSales(null);
        // assert
        assertNotNull(future);
        Map<ProductParam, SalesDisplayDTO> result = future.getNow(null);
        assertNull(result);
        verify(productSceneSalesDisplayService).multiGetSales(null);
    }

    @Test
    public void testMultiGetSalesServiceReturnsNull() throws Throwable {
        // arrange
        ProductParam productParam = ProductParam.productScene(456L, 1);
        SalesDisplayRequest request = SalesDisplayRequest.multiQuery(Collections.singletonList(productParam));
        doAnswer(new Answer<Void>() {

            @Override
            public Void answer(InvocationOnMock invocation) {
                InvocationCallback callback = InvokerHelper.getCallback();
                callback.onSuccess(null);
                return null;
            }
        }).when(productSceneSalesDisplayService).multiGetSales(any());
        // act
        CompletableFuture<Map<ProductParam, SalesDisplayDTO>> future = compositeAtomService.multiGetSales(request);
        // assert
        assertNotNull(future);
        Map<ProductParam, SalesDisplayDTO> result = future.getNow(null);
        assertNull(result);
        verify(productSceneSalesDisplayService).multiGetSales(request);
    }
}
