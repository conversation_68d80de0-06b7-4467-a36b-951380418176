package com.sankuai.dzshoppingguide.product.detail.application.fetcher.producttheme;

import com.alibaba.fastjson.JSONValidator;
import com.dianping.cat.Cat;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.cityId.CityIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.cityId.CityIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.shopid.ShopIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.shopid.ShopIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.best.shop.ShopInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.best.shop.ShopInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommendgoods.dtos.*;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommendgoods.ProductRecommendResult;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommendgoods.SimilarProductRecommendFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommendgoods.enums.ProductStatusEnums;
import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
import com.sankuai.dztheme.deal.dto.*;
import com.sankuai.dztheme.deal.dto.enums.ThemeReqSourceEnum;
import com.sankuai.dztheme.deal.req.DealProductRequest;
import com.sankuai.dztheme.deal.res.DealProductResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.BooleanUtils;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static com.sankuai.dz.product.detail.RequestCustomParamEnum.pass_param;
import static com.sankuai.dzshoppingguide.product.detail.spi.enums.RequestSourceEnum.COST_EFFECTIVE;

/**
 * <AUTHOR>
 * @create 2025/5/22 09:54
 */
@Fetcher(
        previousLayerDependencies = {
                SimilarProductRecommendFetcher.class,
                CityIdMapperFetcher.class,
                ShopIdMapperFetcher.class,
                ShopInfoFetcher.class,
        }
)
@Slf4j
public abstract class AbstractProductThemeFetcher extends NormalFetcherContext<ProductThemeResult> {

    @Resource
    protected CompositeAtomService compositeAtomService;
    protected CityIdMapper cityIdMapper;
    protected ProductRecommendResult productRecommendResult;
    protected ShopIdMapper shopIdMapper;
    protected ShopInfo shopInfo;

    abstract boolean enableNewService();
    abstract DealProductRequest buildDealProductRequest(List<ProductM> products,  Map<Long, Long> dealId2ShopId);


    @Override
    protected CompletableFuture<ProductThemeResult> doFetch() throws Exception {
        productRecommendResult = getDependencyResult(SimilarProductRecommendFetcher.class);
        cityIdMapper = getDependencyResult(CityIdMapperFetcher.class);
        shopIdMapper = getDependencyResult(ShopIdMapperFetcher.class);
        shopInfo = getDependencyResult(ShopInfoFetcher.class);

        ProductThemeResult result = new ProductThemeResult();
        if (Objects.isNull(productRecommendResult) || CollectionUtils.isEmpty(productRecommendResult.getProducts())) {
            return CompletableFuture.completedFuture(result);
        }
        List<ProductM> products = productRecommendResult.getProducts();
        Map<Long, Long> dealId2ShopId = productRecommendResult.getDealId2ShopId();

        List<CompletableFuture<DealProductResult>> paddingResultFutureList = new ArrayList<>();
        int startIndex = 0;
        int paddingDealLimit = 100;

        int fetchCount = (int) Math.ceil(products.size() / (paddingDealLimit * 1.0));
        for (int i = startIndex; i < fetchCount; i++) {
            int endIndex = Math.min((i + 1) * paddingDealLimit, products.size());
            List<ProductM> subProducts = products.subList(i * paddingDealLimit, endIndex);
            DealProductRequest dealProductRequest = buildDealProductRequest(subProducts, dealId2ShopId);
            CompletableFuture<DealProductResult> productResultCompletableFuture;
            if (enableNewService()) {
                productResultCompletableFuture = compositeAtomService.queryNewDealProductTheme(dealProductRequest);
            } else {
                productResultCompletableFuture = compositeAtomService.queryDealProductTheme(dealProductRequest);
            }

            paddingResultFutureList.add(productResultCompletableFuture);
        }
        CompletableFuture<List<DealProductResult>> productDetailList = assemble(paddingResultFutureList);

        return productDetailList.thenApply(dealProductResultList -> {
            // 填充
            dealProductResultList.forEach(dealProductResult -> paddingWithDealProductResult(productRecommendResult, dealProductResult));
            result.setProducts(productRecommendResult.getProducts());
            result.setTotal(productRecommendResult.getTotal());

            return result;
        });

    }

    protected void paddingWithDealProductResult(ProductRecommendResult productGroupM, DealProductResult dealProductResult) {
        if (dealProductResult == null || CollectionUtils.isEmpty(dealProductResult.getDeals())) {
            return;
        }
        List<DealProductDTO> dealProductDTOs = dealProductResult.getDeals();

        Map<Integer, DealProductDTO> dealProducts = dealProductDTOs.stream()
                .collect(HashMap::new, (map, dealDTO) -> map.put(dealDTO.getProductId(), dealDTO), HashMap::putAll);
        productGroupM.getProducts().forEach(productM -> paddingProductM(productM, dealProducts.get((int)productM.getProductId())));
    }


    private <T> CompletableFuture<List<T>> assemble(List<CompletableFuture<T>> futures) {
        CompletableFuture<Void> allDoneFuture = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
        return allDoneFuture.thenApply(v -> futures.stream().map(CompletableFuture::join).collect(Collectors.toList()));
    }
    
    public List<Integer> flexibleGettingDealIds(List<ProductM> products) {
        List<Integer> productIds = products.stream()
                .map(ProductM::getProductId)
                .filter(id -> id > 0)
                .map(Math::toIntExact)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(productIds)) {
            return productIds;
        }
        return Lists.newArrayList();
    }

    protected Map<String, Object> buildQueryExtParams(List<Integer> dealIds, Map<Long, Long> dealId2ShopId) {
        int platform = request.getPlatformEnum().getCode();
        int dpCityId = cityIdMapper.getDpCityId();
        int mtCityId = cityIdMapper.getMtCityId();
        long dpUserId = request.getDpUserId();
        long mtUserId = request.getMtUserId();
        long dpShopId = shopIdMapper.getDpBestShopId();
        long mtShopId = shopIdMapper.getMtBestShopId();
        Map<String, Object> extParams = new HashMap<>();
        extParams.put("dealIds", dealIds);
        // 1. 点评商户ID
        extParams.put("dpShopId", (int) dpShopId);
        extParams.put("dpShopIdForLong", dpShopId);

        // 2. 点评1, 美团2
        extParams.put("platform", platform);
        // 3. 城市ID, 点评侧为点评城市ID, 美团侧为美团城市ID
        extParams.put("cityId", request.getClientTypeEnum().isMtClientType() ? mtCityId : dpCityId);
        // 4. 用户ID, 点评侧为点评用户ID, 美团侧为美团用户ID
        extParams.put("userId", request.getClientTypeEnum().isMtClientType() ? mtUserId : dpUserId);
        // 5. 设备ID
        extParams.put("deviceId", request.getShepherdGatewayParam().getDeviceId());
        // 6. 经纬度
        extParams.put("lat", request.getUserLat());
        extParams.put("lng", request.getUserLng());
        // 7. app版本
        extParams.put("appVersion", request.getShepherdGatewayParam().getAppVersion());
        // 8. 门店ID
        extParams.put("shopId", request.getClientTypeEnum().isMtClientType() ? (int) mtShopId : (int) dpShopId);
        extParams.put("shopIdForLong", request.getClientTypeEnum().isMtClientType() ? mtShopId : dpShopId);
        // 9. 客户端类型
        extParams.put("clientType", request.getClientType());
        // 10. shopuuid
        extParams.put("shopUuid", safeGetUuId(shopInfo));
        // 11. unionId
        extParams.put("unionId", request.getShepherdGatewayParam().getUnionid());

        // 以下非标准字段跟着主题走
        //主题请求来源，后续可能细分列表、货架，当前默认传货架即可
        extParams.put("themeReqSource", ThemeReqSourceEnum.POI_SHELF.getType());
        // 查询用户购买次数
        List<Integer> dealSwanQueryType = Lists.newArrayList();
        dealSwanQueryType.add(3);
        extParams.put("dealSwanQueryType", dealSwanQueryType);

        // 当特价团购下拼接这 pageSource 用于继续在团详链接及提单页链接的透传
        if (COST_EFFECTIVE.getSource().equals(request.getPageSource())) {
            String passParam = request.getCustomParam().getParam(pass_param);
            // 如果没有encode 则需要强制 encode
            if (StringUtils.isNotEmpty(passParam) && JSONValidator.from(passParam).validate()) {
                passParam = encode(passParam);
            }
            String pageSource = String.format("source=%s&pass_param=%s", "cost_effective", passParam);
            extParams.put("pageSource", pageSource);
            extParams.put("themeReqSource", ThemeReqSourceEnum.COST_EFFECTIVE.getType());
            extParams.put("dealProductSource", "mainMeetingChannelPromo");
            extParams.put("orderPromotionChannel", "2");
            extParams.put("orderTrafficFlag", "costEffective");
        }
        if ("lemarketing".equals(request.getPageSource())) {
            extParams.put("pageSource", String.format("source=%s", "lemarketing"));
        }
        return extParams;
    }

    private String safeGetUuId(ShopInfo shopInfo){
        if (Objects.isNull(shopInfo) || Objects.isNull(shopInfo.getDpPoiDTO())) {
            return null;
        }
        return shopInfo.getDpPoiDTO().getUuid();
    }

    public static String encode(String urlParam) {
        if (org.apache.commons.lang.StringUtils.isBlank(urlParam)) {
            return null;
        }
        try {
            return URLEncoder.encode(urlParam, StandardCharsets.UTF_8.name());
        } catch (UnsupportedEncodingException e) {
            return "";
        }
    }

    public Map<Integer, Integer> deal2Shop(List<Integer> dealIds, Map<Long, Long> dealId2ShopId) {
        if (CollectionUtils.isEmpty(dealIds)) {
            return Collections.emptyMap();
        }
        if (MapUtils.isEmpty(dealId2ShopId)) {
            return Collections.emptyMap();
        }
        Map<Integer, Integer> deal2ShopIdMap = new HashMap<>();
        for (Integer dealId : dealIds) {
            Long shopId = dealId2ShopId.get((long) dealId) != null ? dealId2ShopId.get((long) dealId) : 0L;
            deal2ShopIdMap.put(dealId, Math.toIntExact(shopId));
        }
        deal2ShopIdMap.put((int) request.getProductId(), (int) getShopId());
        return deal2ShopIdMap;
    }

    public long getShopId() {
        return (request.getClientTypeEnum().isMtClientType() ? shopIdMapper.getMtBestShopId() : shopIdMapper.getDpBestShopId());
    }
    public Map<Integer, Long> deal2ShopForLong(List<Integer> dealIds, Map<Long, Long> dealId2ShopId) {
        if (CollectionUtils.isEmpty(dealIds)) {
            return Collections.emptyMap();
        }
        if (MapUtils.isEmpty(dealId2ShopId)) {
            return Collections.emptyMap();
        }
        Map<Integer, Long> deal2ShopIdMap = new HashMap<>();
        for (Integer dealId : dealIds) {
            Long shopId = dealId2ShopId.get((long) dealId) != null ? dealId2ShopId.get((long) dealId) : 0L;
            deal2ShopIdMap.put(dealId, shopId);
        }
        deal2ShopIdMap.put((int) request.getProductId(), getShopId());
        return deal2ShopIdMap;
    }

    private ProductSaleM buildProductSale(DealProductSaleDTO saleDTO) {
        if (saleDTO == null) {
            return null;
        }
        ProductSaleM productSaleM = new ProductSaleM();
        productSaleM.setSale(saleDTO.getSale());
        productSaleM.setSaleTag(saleDTO.getSaleTag());
        return productSaleM;
    }

    private List<ProductPromoPriceM> buildPromoPrice(ProductM productM, List<DealProductPromoDTO> promoDTOs) {
        //如果调用主题之前已经填充，则终止继续填充主题层返回的数据
        if (CollectionUtils.isNotEmpty(productM.getPromoPrices())) {
            return productM.getPromoPrices();
        }
        if (CollectionUtils.isEmpty(promoDTOs)) {
            return com.google.common.collect.Lists.newArrayList();
        }
        return promoDTOs.stream().filter(promoDTO -> promoDTO != null).map(promoDTO -> {
            ProductPromoPriceM productPromoPriceM = new ProductPromoPriceM();
            productPromoPriceM.setPromoType(promoDTO.getPromoType());
            productPromoPriceM.setPromoPrice(promoDTO.getPromoPrice());
            productPromoPriceM.setPromoPriceTag(promoDTO.getPromoPriceTag());
            productPromoPriceM.setDiscount(promoDTO.getDiscount());
            productPromoPriceM.setPromoTag(promoDTO.getPromoTag());
            productPromoPriceM.setDiscountTag(promoDTO.getDiscountTag());
            productPromoPriceM.setAvailableTime(promoDTO.getAvailableTime());
            productPromoPriceM.setTotalPromoPrice(promoDTO.getTotalPromoPrice());
            productPromoPriceM.setTotalPromoPriceTag(promoDTO.getTotalPromoPriceTag());
            productPromoPriceM.setPromoItemList(buildPromoItemList(promoDTO.getPromoItemList()));
            productPromoPriceM.setMarketPrice(productM.getMarketPrice());
            return productPromoPriceM;
        }).collect(Collectors.toList());
    }

    private List<PromoItemM> buildPromoItemList(List<DealPromoItemDTO> promoItemList) {
        if (CollectionUtils.isEmpty(promoItemList)) {
            return null;
        }
        return promoItemList.stream().map(promoItem -> {
            PromoItemM promoItemM = new PromoItemM();
            promoItemM.setPromoId(promoItem.getPromoId());
            promoItemM.setPromoTypeCode(promoItem.getPromoTypeCode());
            promoItemM.setDesc(promoItem.getDesc());
            promoItemM.setPromoPrice(promoItem.getPromoPrice());
            promoItemM.setPromoTag(promoItem.getPromoTag());
            promoItemM.setPromoType(promoItem.getPromoType());
            promoItemM.setIcon(promoItem.getIcon());
            return promoItemM;
        }).collect(Collectors.toList());
    }

    private ProductPriceM buildProductPriceM(DealProductPriceDTO productPriceDTO) {
        if (productPriceDTO == null) {
            return null;
        }
        ProductPriceM productPriceM = new ProductPriceM();
        productPriceM.setPriceTag(productPriceDTO.getPriceTag());
        productPriceM.setPriceDesc(productPriceDTO.getPriceDesc());
        return productPriceM;
    }

    private void paddingProductMAttrs(DealProductDTO themeProductDTO, ProductM productM) {
        List<AttrM> themeAttrs = collectThemeAttrs(themeProductDTO);
        mergeThemeAttrs2ProductMAttrs(productM, themeAttrs);
    }

    private List<AttrM> collectThemeAttrs(DealProductDTO themeProductDTO) {
        if (themeProductDTO == null || CollectionUtils.isEmpty(themeProductDTO.getAttrs())) {
            return com.google.common.collect.Lists.newArrayList();
        }
        return themeProductDTO.getAttrs().stream().filter(attr -> attr != null).map(attr -> new AttrM(attr.getName(), attr.getValue())).collect(Collectors.toList());
    }

    private void mergeThemeAttrs2ProductMAttrs(ProductM productM, List<AttrM> themeAttrs) {
        if (CollectionUtils.isEmpty(productM.getExtAttrs())) {
            productM.setExtAttrs(com.google.common.collect.Lists.newArrayList());
        }
        productM.getExtAttrs().addAll(themeAttrs);
    }

    /**
     * 获取poiId, poiIdL, poiId
     * <p>
     * 逻辑：先取 poiIdL ,取不到 在 获取 poiId
     *
     * @param poiIdL
     * @param poiId
     * @return
     */
    public static long getPoiId(Long poiIdL, Integer poiId) {
        if (poiIdL == null) {
            poiIdL = 0L;
        }
        if (poiId == null) {
            poiId = 0;
        }
        return poiIdL > 0L ? poiIdL : poiId;
    }

    private List<ShopM> buildShopMs(ProductM productM, DealProductDTO productDTO) {
        //如果调用主题之前已经填充，则终止继续填充主题层返回的数据
        if (CollectionUtils.isNotEmpty(productM.getShopMs())) {
            return productM.getShopMs();
        }
        if (CollectionUtils.isEmpty(productDTO.getShops())) {
            return com.google.common.collect.Lists.newArrayList();
        }
        return productDTO.getShops().stream().map(shop -> {
            ShopM shopM = new ShopM();
            shopM.setShopId((int) getPoiId(shop.getShopIdAsLong(), shop.getShopId()));
            shopM.setLongShopId( getPoiId(shop.getShopIdAsLong(), shop.getShopId()));
            shopM.setShopName(shop.getShopName());
            shopM.setShopUuid(shop.getShopUuid());
            shopM.setLat(shop.getLat());
            shopM.setLng(shop.getLng());
            shopM.setAddress(shop.getAddress());
            shopM.setDistance(shop.getDistance());
            shopM.setDistanceNum(shop.getDistanceNum());
            shopM.setPic(shop.getHeadPic());
            shopM.setLabels(buildShopLabels(shop.getLabels()));
            shopM.setDetailUrl(shop.getShopUrl());
            shopM.setMainRegionName(shop.getBusinessRegionName());
            shopM.setStarStr(shop.getStarStr());
            shopM.setUserEqShop(userEqShop(shop.getRemote()));
            shopM.setRecommendText(getShopRecommendText(shop.getFiveLineList()));
            shopM.setAvgPrice(shop.getAvgPrice());
            if (shop.getProductSale() != null) {
                ProductSaleM productSaleM = new ProductSaleM();
                productSaleM.setSale(shop.getProductSale().getSale());
                productSaleM.setSaleTag(shop.getProductSale().getSaleTag());
                shopM.setSale(productSaleM);
            }
            return shopM;
        }).collect(Collectors.toList());
    }

    private boolean userEqShop(RemoteDTO remoteDTO) {
        if (remoteDTO == null) {
            return false;
        }
        return remoteDTO.isUserEqShop();
    }

    private String getShopRecommendText(List<ShopFiveLineDTO> fiveLineList) {
        if (CollectionUtils.isEmpty(fiveLineList)) {
            return StringUtils.EMPTY;
        }
        return fiveLineList.get(0).getText();
    }

    private List<ShopLabelM> buildShopLabels(List<ShopLabelDTO> labels) {
        List<ShopLabelM> shopLabelMS = new ArrayList<>();
        if (CollectionUtils.isEmpty(labels)) {
            return shopLabelMS;
        }
        labels.forEach(shopLabelDTO -> {
            ShopLabelM shopLabelM = new ShopLabelM();
            shopLabelM.setType(shopLabelDTO.getType());
            shopLabelM.setTitle(shopLabelDTO.getTitle());
            shopLabelMS.add(shopLabelM);
        });
        return shopLabelMS;
    }

    private List<ProductCouponM> buildCoupons(List<DealProductCouponDTO> couponDTOS) {
        if (CollectionUtils.isEmpty(couponDTOS)) {
            return null;
        }
        return couponDTOS.stream().filter(couponDTO -> couponDTO != null).map(couponDTO -> {
            ProductCouponM productCouponM = new ProductCouponM();
            productCouponM.setCouponTag(couponDTO.getBonusTag());
            return productCouponM;
        }).collect(Collectors.toList());
    }

    private List<ProductActivityM> buildDealActivities(List<DealProductActivityDTO> dealProductActivityDTOs) {
        if (CollectionUtils.isEmpty(dealProductActivityDTOs)) {
            return com.google.common.collect.Lists.newArrayList();
        }
        return dealProductActivityDTOs.stream().filter(dealProductActivityDTO -> dealProductActivityDTO != null).map(dealProductActivityDTO -> {
            ProductActivityM productActivityM = new ProductActivityM();
            productActivityM.setRemainingTime(dealProductActivityDTO.getRemainingTime());
            productActivityM.setUrl(dealProductActivityDTO.getUrl());
            productActivityM.setLable(dealProductActivityDTO.getTitle());
            productActivityM.setUrlAspectRadio(dealProductActivityDTO.getUrlAspectRadio());
            productActivityM.setActivityBeginTime(dealProductActivityDTO.getActivityBeginTime());
            productActivityM.setActivityPriceColor(dealProductActivityDTO.getActivityPriceColor());
            productActivityM.setActivityPriceText(dealProductActivityDTO.getActivityPriceText());
            productActivityM.setShelfActivityType(dealProductActivityDTO.getShelfActivityType());
            productActivityM.setPreheat(dealProductActivityDTO.isPreheat());
            productActivityM.setPageId(dealProductActivityDTO.getPageId());
            return productActivityM;
        }).collect(Collectors.toList());
    }

    private void paddingProductMStock(DealProductDTO themeProductDTO, ProductM productM) {
        DealProductStockDTO productStockDTO = themeProductDTO.getStock();
        if (productStockDTO == null) {
            return;
        }
        ProductStockM stockM = new ProductStockM();
        stockM.setRemainStock(productStockDTO.getRemain());
        stockM.setTotalStock(productStockDTO.getTotal());
        stockM.setSoldOut(productStockDTO.isSoldOut());
        productM.setStock(stockM);
    }

    private int getProductStatusFromAttr(ProductM productM) {
        String dealStatusAttr = productM.getAttr("dealStatusAttr");
        return BooleanUtils.toBoolean(dealStatusAttr) ? ProductStatusEnums.ONLINE.getStatus() : ProductStatusEnums.OFFLINE.getStatus();
    }

    private List<ProductSaleM> buildProductSaleList(List<DealProductSaleDTO> sales) {
        if (CollectionUtils.isEmpty(sales)) {
            return com.google.common.collect.Lists.newArrayList();
        }
        try {
            List<ProductSaleM> list = new ArrayList<>();
            for (DealProductSaleDTO saleDTO : sales) {
                ProductSaleM productSaleM = new ProductSaleM();
                productSaleM.setSale(saleDTO.getSale());
                productSaleM.setSaleTag(saleDTO.getSaleTag());
                productSaleM.setType(saleDTO.getType());
                list.add(productSaleM);
            }
            return list;
        } catch (Exception e) {
            Cat.logError(e);
            return com.google.common.collect.Lists.newArrayList();
        }
    }

    private ReviewM buildReview(DealProductDTO dealProductDTO) {
        if (Objects.isNull(dealProductDTO) || CollectionUtils.isEmpty(dealProductDTO.getAttrs())) {
            return null;
        }
        try {
            String reviewNumStr = dealProductDTO.getAttrs()
                    .stream()
                    .filter(attr -> Objects.equals(attr.getName(), "attr_dealAllReview"))
                    .findFirst()
                    .map(DealProductAttrDTO::getValue)
                    .orElse("0");

            ReviewM reviewM = new ReviewM();
            reviewM.setReviewNum(Integer.parseInt(reviewNumStr));
            return reviewM;
        } catch (Exception e) {
            log.error("[DealGroupThemeHandler] buildReview err={}, attrs={}", e, JsonCodec.encode(dealProductDTO.getAttrs()));
            return null;
        }
    }

    protected void paddingProductM(ProductM productM, DealProductDTO dealProductDTO) {
        if (dealProductDTO == null) {
            // miss
            return;
        }
        // 1. 团单ID
        productM.setProductId(dealProductDTO.getProductId());
        // 2. 团单标题
        productM.setTitle(dealProductDTO.getName());
        // 3. 团单详情跳转URL
        productM.setJumpUrl(dealProductDTO.getDetailUr());
        // 4. 团单销量
        productM.setSale(buildProductSale(dealProductDTO.getSale()));
        // 5. 团单售卖价格标签
        productM.setBasePriceTag(dealProductDTO.getBasePriceTag());
        // 6. 团单售卖价格
        productM.setBasePriceTag(dealProductDTO.getBasePriceTag());
        // 7. 团单市场价格
        productM.setMarketPrice(dealProductDTO.getMarketPriceTag());
        // 8. 融合优惠啊
        productM.setPromoPrices(buildPromoPrice(productM, dealProductDTO.getPromoPrices()));
        // 9. 团单购买信息
        productM.setPurchase(dealProductDTO.getPurchase());
        // 10. 团单拼团标签
        productM.setPinPrice(buildProductPriceM(dealProductDTO.getPinPrice()));
        // 11. 团单次卡标签
        productM.setCardPrice(buildProductPriceM(dealProductDTO.getCardPrice()));
        // 12. 团单跳转链接
        productM.setJumpUrl(dealProductDTO.getDetailUr());
        // 13. 团单标准商品标签
        productM.setProductTags(dealProductDTO.getProductTags());
        // 13. 团单商品头图
        productM.setPicUrl(dealProductDTO.getHeadPic());
        // 14. 团单扩展属性
        paddingProductMAttrs(dealProductDTO, productM);
        // 15. 关联商户信息
        productM.setShopMs(buildShopMs(productM, dealProductDTO));
        // 16、消费返券
        productM.setCoupons(buildCoupons(dealProductDTO.getCoupons()));
        //17.商品原始售卖价格
        productM.setBasePrice(dealProductDTO.getBasePrice());
        //18.商品类目
        productM.setCategoryId(dealProductDTO.getCategoryId());
        //19.商品活动
        productM.setActivities(buildDealActivities(dealProductDTO.getActivities()));
        //20.团单售卖价格描述
        productM.setBasePriceDesc(dealProductDTO.getBasePriceDesc());
        //21.库存
        paddingProductMStock(dealProductDTO, productM);
        //22.商品状态
        productM.setProductStatus(getProductStatusFromAttr(productM));
        //23 团单列表销量
        productM.setSales(buildProductSaleList(dealProductDTO.getSales()));
        //24.提单页链接
        productM.setOrderUrl(dealProductDTO.getOrderUrl());
        //25.商品标签列表
        productM.setProductTagList(dealProductDTO.getProductTagList());
        //26.购物车价格
        productM.setShopCarPrice(dealProductDTO.getPromoPrice());
        //27.团单交易类型
        productM.setTradeType(dealProductDTO.getTradeType());
        //28.评价数
        productM.setReview(buildReview(dealProductDTO));
        //29.优惠价
        productM.setPayPrice(dealProductDTO.getPromoPrice());
    }
}
