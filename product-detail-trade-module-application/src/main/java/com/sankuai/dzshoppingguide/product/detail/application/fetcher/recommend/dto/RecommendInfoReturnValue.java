package com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommend.dto;


import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;


/**
 * @Author: zhangyuan103
 * @Date: 2025/5/23
 * @Description: 推荐信息返回值
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RecommendInfoReturnValue extends FetcherReturnValueDTO {
    /**
     * 推荐的团单id列表(已排序), 区分平台
     */
    private List<Long> sortedDealGroupIds;
}
