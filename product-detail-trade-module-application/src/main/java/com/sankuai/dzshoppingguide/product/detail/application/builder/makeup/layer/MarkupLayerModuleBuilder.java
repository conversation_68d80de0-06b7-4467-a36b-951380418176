package com.sankuai.dzshoppingguide.product.detail.application.builder.makeup.layer;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sankuai.dealuser.price.display.api.enums.AdjustPriceRuleTypeEnum;
import com.sankuai.dealuser.price.display.api.model.AdjustPriceRuleDTO;
//import com.sankuai.dealuser.price.display.api.model.AdjustPriceSelectionDTO;
//import com.sankuai.dealuser.price.display.api.model.AdjustPriceSelectionOptionDTO;
import com.sankuai.dealuser.price.display.api.model.AdjustPriceSelectionDTO;
import com.sankuai.dealuser.price.display.api.model.AdjustPriceSelectionOptionDTO;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.extend.BaseBuilder;
import com.sankuai.dz.product.detail.gateway.spi.enums.ProductTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.constants.Constants;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.shopid.ShopIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.OrderPageUrlResult;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.handler.normal.NormalOrderPageUrlFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.ProductNormalPriceFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.dto.ProductPriceReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttrFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.FetcherDAGStarter;
import com.sankuai.dzshoppingguide.product.detail.application.utils.TimeUtils;
import com.sankuai.dzshoppingguide.product.detail.domain.utils.LionConfigUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.MarkupDetailModuleConfig;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.MarkupModuleItem;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.MarkupValueAttr;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.makeup.vo.MarkupDetailVO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: <EMAIL>
 * @Date: 2025/3/10
 */
@Builder(
        moduleKey = ModuleKeyConstants.MODULE_DETAIL_DEAL_MARKUP_LAYER,
        startFetcher = FetcherDAGStarter.class,
        dependentFetchers = {
                ProductBaseInfoFetcher.class,
                ShopIdMapperFetcher.class,
                ProductNormalPriceFetcher.class,
                ProductAttrFetcher.class,
                NormalOrderPageUrlFetcher.class
        }
)
public class MarkupLayerModuleBuilder extends BaseBuilder<MarkupDetailVO> {

    @Override
    public MarkupDetailVO doBuild() {
        if (!request.getProductTypeEnum().equals(ProductTypeEnum.DEAL)) {
            return null;
        }
        MarkupDetailVO markupDetailVO = new MarkupDetailVO();
        OrderPageUrlResult orderPageUrlResult = getDependencyResult(NormalOrderPageUrlFetcher.class);
        ProductPriceReturnValue productPriceReturnValue = getDependencyResult(ProductNormalPriceFetcher.class);
        if (Objects.isNull(productPriceReturnValue) || Objects.isNull(productPriceReturnValue.getPriceDisplayDTO())) {
            return null;
        }
        // 获取加价详情
        PriceDisplayDTO priceDisplayDTO = productPriceReturnValue.getPriceDisplayDTO();
        if (CollectionUtils.isEmpty(priceDisplayDTO.getAdjustPriceRules())) {
            return null;
        }
        List<AdjustPriceRuleDTO> adjustPriceRuleDTOS = priceDisplayDTO.getAdjustPriceRules();
        // 读取加价配置信息
        Map<String, MarkupModuleItem> moduleItemMap = LionConfigUtils.getMarkupModuleItemList().stream()
                .collect(Collectors.toMap(MarkupModuleItem::getItemKey, Function.identity(), (o1, o2) -> o1));
        List<AdjustPriceSelectionDTO> adjustPriceSelectionDTOS = Lists.newArrayList();
        adjustPriceRuleDTOS.forEach(adjustPriceRuleDTO -> {
            if (Objects.isNull(adjustPriceRuleDTO)
                    || adjustPriceRuleDTO.getRuleType() != AdjustPriceRuleTypeEnum.STANDARD_PRICE_RULE_V2.getType()
                    || StringUtils.isBlank(adjustPriceRuleDTO.getRuleContent())) {
                return;
            }
            adjustPriceSelectionDTOS.add(JSON.parseObject(adjustPriceRuleDTO.getRuleContent(), AdjustPriceSelectionDTO.class));
        });

        ProductAttr productAttr = getDependencyResult(ProductAttrFetcher.class);
        String overnightServiceType = productAttr.getProductAttrFirstValue(Constants.OVER_NIGHT_SERVICE_TYPE);
        // 如果不是付费，展示团购详情中，此模块不展示
        if (!Objects.equals(overnightServiceType, Constants.OVER_NIGHT_SERVICE_TYPE_PAY)) {
            moduleItemMap.remove("overNightService");
        }
        List<MarkupModuleItem> moduleItems = Lists.newArrayList();
        adjustPriceSelectionDTOS.forEach(adjustPriceSelectionDTO -> {
            moduleItems.addAll(assembleModuleItemList(moduleItemMap, adjustPriceSelectionDTO, Objects.equals(overnightServiceType, Constants.OVER_NIGHT_SERVICE_TYPE_PAY)));
        });

        moduleItems.sort((item1, item2) -> {
            if (Constants.TECHNICIAN_LEVEL_RULE.equals(item1.getItemKey())) {
                return -1;
            } else if (Constants.TECHNICIAN_LEVEL_RULE.equals(item2.getItemKey())) {
                return 1;
            }
            return 0;
        });
        if (CollectionUtils.isEmpty(moduleItems)) {
            return null;
        }
        markupDetailVO.setModuleItems(moduleItems);
        markupDetailVO.setModuleConfig(buildModuleConfig(Objects.isNull(orderPageUrlResult) ? StringUtils.EMPTY : orderPageUrlResult.getUrl()));
        return markupDetailVO;
    }

    public List<MarkupModuleItem> assembleModuleItemList(Map<String, MarkupModuleItem> moduleItemMap, AdjustPriceSelectionDTO selectionDTO, boolean isPayable) {
        List<MarkupModuleItem> markupModuleItemList = Lists.newArrayList();
        selectionDTO.getAdjustPriceSelectionItems().forEach(dto -> {
            MarkupModuleItem markupModuleItem = moduleItemMap.get(dto.getItemKey());
            if (Objects.isNull(markupModuleItem)) {
                return;
            }
            List<MarkupValueAttr> valueAttrs = Lists.newArrayList();
            dto.getOptions().forEach(o -> {
                MarkupValueAttr valueAttr = new MarkupValueAttr();
                valueAttr.setIdentityKey(o.getIdentityKey());
                fillValueAttrText(o, valueAttr);
                valueAttr.setPrice("+￥" + o.getAmount().stripTrailingZeros().toPlainString());
                valueAttrs.add(valueAttr);
            });
            markupModuleItem.setMarkupValueAttrList(valueAttrs);
            markupModuleItemList.add(markupModuleItem);
        });

        return markupModuleItemList;
    }

    public void fillValueAttrText(AdjustPriceSelectionOptionDTO o, MarkupValueAttr valueAttr) {
        String result = "";
        Map<String, String> attributeMap = o.getAttributes();
        if (MapUtils.isEmpty(attributeMap)) {
            return;
        }
        if (Constants.OVER_NIGHT_SERVICE_EXIST_KEY.equals(o.getIdentityKey())) {
            if (attributeMap.containsKey(Constants.STAY_OVER_START_TIME_KEY)
                    && attributeMap.containsKey(Constants.STAY_OVER_END_TIME_KEY)
                    && org.apache.commons.lang3.StringUtils.isNotBlank(attributeMap.get(Constants.STAY_OVER_START_TIME_KEY))
                    && org.apache.commons.lang3.StringUtils.isNotBlank(attributeMap.get(Constants.STAY_OVER_END_TIME_KEY))) {
                result += Constants.STAY_OVER_SERVICE_PREFIX + attributeMap.get(Constants.STAY_OVER_START_TIME_KEY) + "-" + TimeUtils.formatOvernightTime(attributeMap.get(Constants.STAY_OVER_END_TIME_KEY)) + "，";
            }
            result += Constants.FREE_BREAKFAST_TRUE_KEY.equals(attributeMap.get(Constants.FREE_BREAKFAST_KEY)) ? Constants.FREE_BREAKFAST_TRUE_VALUE : Constants.FREE_BREAKFAST_FALSE_VALUE;
            if (attributeMap.containsKey(Constants.STAY_OVER_DESC)) {
                result += attributeMap.get(Constants.STAY_OVER_DESC);
            }
            if (result.endsWith("，")) {
                result = result.substring(0, result.length() - 1);
            }
            // 设置加价浮层模块的加价项标题
            valueAttr.setTitle("过夜");
            valueAttr.setDescList(Lists.newArrayList(result));
        } else {
            List<String> descList = Lists.newArrayList();
            if (attributeMap.containsKey(Constants.GRADE_EXPLANATION)) {
                descList.add(attributeMap.get(Constants.GRADE_EXPLANATION));
            }
            if (attributeMap.containsKey(Constants.TECHNICIAN_COUNT)) {
                descList.add("共" + attributeMap.get(Constants.TECHNICIAN_COUNT) + "位技师可到店选择");
            }
            // 设置技师等级为加价项的标题
            if (attributeMap.containsKey(Constants.SERVICE_TITLE)) {
                String title = Objects.isNull(attributeMap.get(Constants.SERVICE_TITLE)) ? StringUtils.EMPTY : attributeMap.get(Constants.SERVICE_TITLE);
                valueAttr.setTitle(title);
            }
            valueAttr.setDescList(descList);
        }
    }

    public MarkupDetailModuleConfig buildModuleConfig(String orderPageUrl) {
        MarkupDetailModuleConfig moduleConfig = new MarkupDetailModuleConfig();
        moduleConfig.setTitle("付费升级");
        moduleConfig.setJumpUrl(orderPageUrl);
        return moduleConfig;
    }
}
