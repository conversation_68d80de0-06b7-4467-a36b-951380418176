package com.sankuai.dzshoppingguide.product.detail.application.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.dianping.gmkt.scene.api.delivery.dto.res.DeliveryCommonResponse;
import com.dianping.gmkt.scene.api.delivery.dto.res.ResourceExposureResponseDTO;
import com.dianping.gmkt.scene.api.delivery.enums.DeliveryErrorCode;
import com.dianping.gmkt.scene.api.delivery.service.ResourcesExposureService;
import com.dianping.pigeon.remoting.common.domain.CallMethod;
import com.dianping.pigeon.remoting.common.exception.RpcException;
import com.dianping.tpfun.product.api.govsubsidy.model.GovSubsidyInfo;
import com.dianping.tpfun.product.api.govsubsidy.request.QueryGovSubsidyInfoRequest;
import com.dianping.tpfun.product.api.govsubsidy.service.GovSubsidyInfoQueryService;
import com.dianping.tpfun.product.api.sku.pintuan.PinFacadeService;
import com.dianping.tpfun.product.api.sku.pintuan.dto.PinProductBrief;
import com.dianping.tpfun.product.api.sku.pintuan.request.GetPinProductBriefReq;
import com.dianping.tpfun.product.api.sku.pintuan.request.GetPinProductIdOptional;
import com.dianping.tuangu.dztg.usercenter.api.CreateOrderPageUrlService;
import com.dianping.tuangu.dztg.usercenter.api.request.BatchGetCreateOrderPageUrlReq;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.common.util.concurrent.SettableFuture;
import com.meituan.inf.xmdlog.XMDLogFormat;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonClient;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftClient;
import com.meituan.service.mobile.mtthrift.callback.OctoThriftCallback;
import com.meituan.service.mobile.mtthrift.netty.ContextStore;
import com.sankuai.athena.inf.AthenaInf;
import com.sankuai.clr.content.process.gateway.thrift.api.LeadsQueryGatewayService;
import com.sankuai.clr.content.process.gateway.thrift.dto.leads.LoadLeadsInfoReqDTO;
import com.sankuai.clr.content.process.gateway.thrift.dto.leads.LoadLeadsInfoRespDTO;
import com.sankuai.dealuser.price.display.api.PriceDisplayService;
import com.sankuai.dealuser.price.display.api.model.BatchPriceRequest;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PriceResponse;
import com.sankuai.dzcard.navigation.api.DzCardExposureService;
import com.sankuai.dzcard.navigation.api.DzCardQueryService;
import com.sankuai.dzcard.navigation.api.dto.CardHoldStatusDTO;
import com.sankuai.dzcard.navigation.api.dto.DzCardQueryRequest;
import com.sankuai.dzcard.navigation.api.dto.FindDCCardHoldStatusLiteReqDTO;
import com.sankuai.dzcard.navigation.api.dto.QueryCardInfoDTO;
import com.sankuai.dzcard.navigation.api.dto.QueryDiscountCardReq;
import com.sankuai.dzim.cliententry.ClientEntryService;
import com.sankuai.dzim.cliententry.dto.ClientEntryDTO;
import com.sankuai.dzim.cliententry.dto.ClientEntryReqDTO;
import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
import com.sankuai.dzshoppingguide.product.detail.application.service.dto.ShopMemberDetailV;
import com.sankuai.dzshoppingguide.product.detail.application.service.dto.ShopMemberQueryResponse;
import com.sankuai.dzshoppingguide.product.detail.domain.utils.PigeonCallbackUtils;
import com.sankuai.dzshoppingguide.product.detail.domain.utils.ThriftAsyncUtils;
import com.sankuai.dztheme.deal.DealProductService;
import com.sankuai.dztheme.deal.DealSkuService;
import com.sankuai.dztheme.deal.operatorpage.DealProductBizQueryService;
import com.sankuai.dztheme.deal.req.DealProductRequest;
import com.sankuai.dztheme.deal.req.SkuOptionBatchRequest;
import com.sankuai.dztheme.deal.res.DealProductResult;
import com.sankuai.dztheme.deal.res.DealSkuSummaryDTO;
import com.sankuai.dztheme.massagebook.theme.ReserveThemeQueryService;
import com.sankuai.dztheme.massagebook.theme.req.ReserveQueryRequest;
import com.sankuai.dztheme.massagebook.theme.res.ReserveQueryResponse;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResponse;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResult;
import com.sankuai.general.product.query.center.client.service.DealGroupQueryService;
import com.sankuai.meituan.charity.merchant.main.sdk.WelfareDocFacade;
import com.sankuai.meituan.charity.merchant.main.sdk.dto.request.DzVpoiReq;
import com.sankuai.meituan.charity.merchant.main.sdk.dto.response.DzProductDocResp;
import com.sankuai.merchantcard.timescard.exposure.TimesCardNavigationService;
import com.sankuai.merchantcard.timescard.exposure.dto.CardSummaryBarDTO;
import com.sankuai.merchantcard.timescard.exposure.res.QueryDealGroupCardBarSummaryRequest;
import com.sankuai.mktplay.center.mkt.play.center.client.PlayCenterService;
import com.sankuai.mktplay.center.mkt.play.center.client.PlayExecuteRequest;
import com.sankuai.mktplay.center.mkt.play.center.client.PlayExecuteResponse;
import com.sankuai.mktplay.center.mkt.play.center.client.ScenePlayExecuteRequest;
import com.sankuai.mpmctmember.query.common.enums.MemberInterestCodeEnum;
import com.sankuai.mpmctmember.query.common.enums.MemberInterestSubjectTypeEnum;
import com.sankuai.mpmctmember.query.common.enums.MemberQueryFieldEnum;
import com.sankuai.mpmctmember.query.common.enums.UserMemberTypeEnum;
import com.sankuai.mpmctmember.query.thrift.api.MemberInterestQueryService;
import com.sankuai.mpmctmember.query.thrift.api.MemberPlanQueryService;
import com.sankuai.mppack.product.client.query.response.TyingBlackListResponse;
import com.sankuai.mppack.product.client.supply.service.PackBlackListService;
import com.sankuai.mpproduct.idservice.api.request.BizProductIdConvertRequest;
import com.sankuai.mpproduct.idservice.api.request.DpGroupIdConvertRequest;
import com.sankuai.mpproduct.idservice.api.request.MtGroupIdConvertRequest;
import com.sankuai.mpproduct.idservice.api.response.BizProductIdConvertResponse;
import com.sankuai.mpproduct.idservice.api.response.GroupIdConvertResponse;
import com.sankuai.mpproduct.idservice.api.service.IdService;
import com.sankuai.newdzcard.supply.dto.DzCardDTO;
import com.sankuai.nib.price.operation.api.common.dto.SessionContextDTO;
import com.sankuai.nib.price.operation.api.guarantee.guide.standard.dto.ObjectGuaranteeTagDTO;
import com.sankuai.nib.price.operation.api.guarantee.guide.standard.request.BatchQueryGuaranteeTagRequest;
import com.sankuai.nib.price.operation.api.guarantee.guide.standard.service.GuaranteeQueryService;
import com.sankuai.statesubsidies.c.thrift.request.GetUserQualificationOpenRequest;
import com.sankuai.statesubsidies.c.thrift.response.GetUserQualificationOpenResponse;
import com.sankuai.statesubsidies.c.thrift.service.StateSubsidiesOpenService;
import com.sankuai.tpfun.skuoperationapi.price.dto.request.BatchPriceRangeInfoRequest;
import com.sankuai.tpfun.skuoperationapi.price.dto.response.BatchPriceRangeInfoResponse;
import com.sankuai.tpfun.skuoperationapi.price.service.PriceRangeQueryService;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.thrift.TException;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.mockito.junit.*;
import org.springframework.stereotype.Component;

class CompositeAtomServiceImplQueryDzProductDoc2Test {

    @Mock
    private WelfareDocFacade welfareDocFacade;

    @InjectMocks
    private CompositeAtomServiceImpl compositeAtomService;

    private SettableFuture<Object> settableFuture;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        settableFuture = SettableFuture.create();
        ContextStore.setFuture(settableFuture);
    }

    @AfterEach
    void tearDown() {
        ContextStore.removeFuture();
    }

    /**
     * 测试正常返回DzProductDocResp的情况
     */
    @Test
    void testQueryDzProductDocWithValidResponse() throws Throwable {
        // arrange
        DzVpoiReq dzVpoiReq = new DzVpoiReq();
        DzProductDocResp expectedResponse = new DzProductDocResp();
        expectedResponse.setSignCharity(true);
        expectedResponse.setText("test text");
        doReturn(expectedResponse).when(welfareDocFacade).queryDzProductDoc(any(DzVpoiReq.class));
        // act
        CompletableFuture<DzProductDocResp> future = compositeAtomService.queryDzProductDoc(dzVpoiReq);
        settableFuture.set(expectedResponse);
        // assert
        DzProductDocResp actualResponse = future.get();
        assertNotNull(actualResponse, "Response should not be null");
        assertEquals(expectedResponse.getSignCharity(), actualResponse.getSignCharity(), "SignCharity should match");
        assertEquals(expectedResponse.getText(), actualResponse.getText(), "Text should match");
    }

    /**
     * 测试welfareDocFacade返回null的情况
     */
    @Test
    void testQueryDzProductDocWithNullResponse() throws Throwable {
        // arrange
        DzVpoiReq dzVpoiReq = new DzVpoiReq();
        doReturn(null).when(welfareDocFacade).queryDzProductDoc(any(DzVpoiReq.class));
        // act
        CompletableFuture<DzProductDocResp> future = compositeAtomService.queryDzProductDoc(dzVpoiReq);
        settableFuture.set(null);
        // assert
        assertNull(future.get(), "Response should be null");
    }

    /**
     * 测试welfareDocFacade抛出异常的情况
     */
    @Test
    void testQueryDzProductDocWithException() throws Throwable {
        // arrange
        DzVpoiReq dzVpoiReq = new DzVpoiReq();
        RuntimeException expectedException = new RuntimeException("Test exception");
        try {
            // Mock抛出异常
            doThrow(expectedException).when(welfareDocFacade).queryDzProductDoc(any(DzVpoiReq.class));
            // act
            CompletableFuture<DzProductDocResp> future = compositeAtomService.queryDzProductDoc(dzVpoiReq);
            // 设置异常结果
            settableFuture.setException(expectedException);
            // 等待future完成并验证异常
            ExecutionException actualException = assertThrows(ExecutionException.class, () -> future.get(), "Should throw ExecutionException");
            // 验证异常原因
            Throwable cause = actualException.getCause();
            assertEquals("Test exception", cause.getMessage(), "Exception message should match");
            assertTrue(cause instanceof RuntimeException, "Should be RuntimeException");
        } catch (Exception e) {
            // 验证捕获的异常
            assertEquals("Test exception", e.getMessage(), "Exception message should match");
            assertTrue(e instanceof RuntimeException, "Should be RuntimeException");
        }
    }

    /**
     * 测试请求参数为null的情况
     */
    @Test
    void testQueryDzProductDocWithNullRequest() throws Throwable {
        // arrange
        doReturn(null).when(welfareDocFacade).queryDzProductDoc(null);
        // act
        CompletableFuture<DzProductDocResp> future = compositeAtomService.queryDzProductDoc(null);
        settableFuture.set(null);
        // assert
        assertNull(future.get(), "Response should be null for null request");
        verify(welfareDocFacade).queryDzProductDoc(null);
    }
}
