package com.sankuai.dzshoppingguide.product.detail.application.fetcher.timesdeal.dto;

import lombok.Data;

import java.util.List;

/**
 * 卡模型
 * <AUTHOR>
 * @date 2022/4/13
 */
@Data
public class CardM {
    /**
     * 门店当前在线的卡类型
     * @see com.sankuai.dzcard.navigation.api.enums.CardTypeEnum
     */
    private List<Integer> shopCardList;

    /**
     * 用户在当前门店持有的卡类型(玩乐卡不校验当前门店是否有玩乐卡)
     * @see com.sankuai.dzcard.navigation.api.enums.CardTypeEnum
     */
    private List<Integer> userCardList;
}
