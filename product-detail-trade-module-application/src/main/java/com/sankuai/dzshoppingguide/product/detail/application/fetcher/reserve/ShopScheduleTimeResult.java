package com.sankuai.dzshoppingguide.product.detail.application.fetcher.reserve;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ShopScheduleTimeResult extends FetcherReturnValueDTO {
    // 获取门店今日排班开始时间
    private long todayScheduleStartTime;
    // 获取门店今日排班结束时间
    private long todayScheduleEndTime;
}