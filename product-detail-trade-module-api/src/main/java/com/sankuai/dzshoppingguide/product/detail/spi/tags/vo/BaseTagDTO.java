package com.sankuai.dzshoppingguide.product.detail.spi.tags.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.CommonRichTextDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/2/26 11:24
 */
@Data
@TypeDoc(description = "标签通用属性模型")
@MobileDo(id = 0xdc81)
public abstract class BaseTagDTO implements Serializable {
    @FieldDoc(description = "标签类型")
    @MobileDo.MobileField(key = 0x8f0c)
    private int type;

    @FieldDoc(description = "跳转链接")
    @MobileDo.MobileField(key = 0xafc8)
    private String link;

    @FieldDoc(description = "是否有前图标")
    @MobileDo.MobileField(key = 0x7a3c)
    private boolean prefixIcon;

    @FieldDoc(description = "是否有后图标")
    @MobileDo.MobileField(key = 0x26c8)
    private boolean suffixIcon;

    @FieldDoc(description = "背景色")
    @MobileDo.MobileField(key = 0xba62)
    private String backgroundColor;

    @FieldDoc(description = "描述内容")
    @MobileDo.MobileField(key = 0x8535)
    private List<CommonRichTextDTO> contents;

}
