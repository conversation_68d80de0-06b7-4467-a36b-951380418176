package com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.pricerange;

import com.sankuai.dealuser.price.display.api.enums.ProductTypeEnum;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.dealgroup.DealGroupIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.dealgroup.DealGroupIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.shopid.ShopIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.shopid.ShopIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.best.shop.ShopInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.best.shop.ShopInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
import com.sankuai.tpfun.skuoperationapi.price.dto.SubjectDTO;
import com.sankuai.tpfun.skuoperationapi.price.dto.enums.pricerange.PriceRangeSceneTypeEnum;
import com.sankuai.tpfun.skuoperationapi.price.dto.request.BatchPriceRangeInfoRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @create 2025/5/28 15:30
 */
@Fetcher(
        previousLayerDependencies = {
                ShopIdMapperFetcher.class,
                ShopInfoFetcher.class,
                DealGroupIdMapperFetcher.class})
@Slf4j
public class PriceRangeInfoFetcher extends NormalFetcherContext<PriceRangeInfoReturnValue> {

    @Autowired
    private CompositeAtomService compositeAtomService;

    protected ShopIdMapper shopIdMapper = null;
    protected ShopInfo shopInfo = null;
    protected DealGroupIdMapper dealGroupIdMapper = null;

    @Override
    protected CompletableFuture<PriceRangeInfoReturnValue> doFetch() throws Exception {
        try{
            boolean isMt = request.getClientTypeEnum().isMtClientType();
            shopIdMapper = getDependencyResult(ShopIdMapperFetcher.class);
            shopInfo = getDependencyResult(ShopInfoFetcher.class);
            dealGroupIdMapper = getDependencyResult(DealGroupIdMapperFetcher.class);
            int dpDealId = (int) dealGroupIdMapper.getDpDealGroupId();
            long dpShopId = shopIdMapper.getDpBestShopId();
            return compositeAtomService.batchGetPriceRangeInfo(buildQueryRangePriceRequest(dpDealId, dpShopId))
                    .thenApply(res->{
                        if (Objects.nonNull(res) && res.isSuccess()){
                            PriceRangeInfoReturnValue returnValue = new PriceRangeInfoReturnValue();
                            returnValue.setBatchPriceRangeInfoResponse(res.getResult());
                            return returnValue;
                        }
                        return null;
                    });
        }catch (Exception e){
            log.error("PriceRangeInfoFetcher.doFetch() error:", e);
        }
        return CompletableFuture.completedFuture(null);
    }

    private BatchPriceRangeInfoRequest buildQueryRangePriceRequest(Integer dpDealId, Long dpShopId) {
        BatchPriceRangeInfoRequest request = new BatchPriceRangeInfoRequest();
        SubjectDTO subjectDTO = new SubjectDTO();
        subjectDTO.setDpShopId(dpShopId);
        subjectDTO.setProductId((long)dpDealId);
        request.setSubjectDTOs(Collections.singletonList(subjectDTO));
        request.setPriceRangeSceneType(PriceRangeSceneTypeEnum.PRODUCT_SHOP_TYPE.getCode());
        request.setProductType(ProductTypeEnum.DEAL.getType());
        return request;
    }
}
