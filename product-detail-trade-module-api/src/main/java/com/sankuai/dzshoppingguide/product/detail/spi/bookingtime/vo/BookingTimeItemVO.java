package com.sankuai.dzshoppingguide.product.detail.spi.bookingtime.vo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

@Data
@Builder
public class BookingTimeItemVO implements Serializable {

    /**
     * 主要信息
     */
    @MobileField
    private String content;

    /**
     * 信息颜色
     */
    @MobileField
    private String contentColor;

    /**
     * 信息字体大小
     */
    @MobileField
    private String contentSize;

    /**
     * 信息字重
     */
    @MobileField
    private String contentWeight;

//    /**
//     * 展示类型
//     * @see com.sankuai.dzshoppingguide.product.detail.application.builder.bookingtime.enums.CardTextTypeEnum
//     */
//    @MobileField
//    private int type;

}