package com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommend.dto;


import com.dianping.deal.sales.common.datatype.ProductParam;
import com.dianping.deal.sales.common.datatype.SalesDisplayDTO;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Map;

/**
 * @Author: zhangyuan103
 * @Date: 2025/5/26
 * @Description 展示销量信息
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
public class SalesDisplayReturnValue extends FetcherReturnValueDTO {
    private Map<ProductParam, SalesDisplayDTO> salesDisplayDTOMap;
}
