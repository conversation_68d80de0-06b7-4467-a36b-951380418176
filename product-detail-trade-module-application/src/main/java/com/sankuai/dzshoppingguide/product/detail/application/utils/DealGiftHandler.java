package com.sankuai.dzshoppingguide.product.detail.application.utils;

import com.google.common.collect.Lists;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.dealgift.DealGiftResult;
import com.sankuai.dzshoppingguide.product.detail.application.utils.model.FilterPlayActivityModel;
import com.sankuai.dzshoppingguide.product.detail.application.utils.model.MaterialInfoModel;
import com.sankuai.dzshoppingguide.product.detail.application.utils.model.PlayActivityModel;
import com.sankuai.dzshoppingguide.product.detail.application.utils.model.PrizeInfoModel;
import com.sankuai.dzshoppingguide.product.detail.application.utils.model.TaskInfoModel;
import com.sankuai.dzshoppingguide.product.detail.spi.promo.vo.DealGift;
import com.sankuai.mktplay.center.mkt.play.center.client.PlayExecuteResponse;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/4/17 3:46 PM
 * 团详赠品查询
 */
public class DealGiftHandler {

    private static final String COST_EFFECTIVE_ICON = "https://p0.meituan.net/travelcube/f8b02bfbf00c465ad4a61ba5b8a685227006.png";
    private static final String SUMMER_PLAY_ID_PARAM = "summerPlayId";
    private static final String SURPRISE_GIFT_PLAY_ID_PARAM = "surpriseGiftPlayId";
    private static final String NEW_CUSTOMER_ACTIVITY = "newCustomerActivity";


    public static List<DealGift> buildDealGiftsFromPlay( long dealGroupId, long poiId, DealGiftResult dealGiftResult ) {
        if (Objects.isNull(dealGiftResult)) {
            return Lists.newArrayList();
        }
        PlayExecuteResponse playActivityResponse = dealGiftResult.getPlayActivityResponse();
        PlayExecuteResponse playResponse = dealGiftResult.getPlayResponse();
        String playActivityResult = getResult(playActivityResponse);
        String playResult = getResult(playResponse);
        FilterPlayActivityModel filterPlayActivityModel = PlayCenterWrapper.convert2FilterPlayActivityModel(playActivityResult);
        Map<Long, List<PlayActivityModel>> dealId2PlayModel = PlayCenterWrapper.convertPlayActivityModelMap(playResult);
        List<DealGift> newCustomGift = PlayCenterWrapper.buildGiftsForNewCustomActivity(playActivityResult);
        List<PlayActivityModel> activityModels = MapUtils.isEmpty(dealId2PlayModel) ? null : dealId2PlayModel.values().stream().filter(Objects::nonNull).findFirst().orElse(null);
        return buildGifts(dealGroupId, poiId, filterPlayActivityModel, activityModels,newCustomGift);
    }



    private static String getResult(PlayExecuteResponse response) {
        if (response == null || StringUtils.isEmpty(response.getResult())) {
            return null;
        }
        return response.getResult();
    }

    public static List<DealGift> buildGifts(long dealGroupId, long poiId, FilterPlayActivityModel filterPlayActivityModel, List<PlayActivityModel> playActivityModels, List<DealGift> newCustomGift) {
        List<DealGift> result = buildGiftsForSummerPlay(dealGroupId, poiId, filterPlayActivityModel);
        // 如果新客活动存在，则返回新客活动赠品信息
        if (CollectionUtils.isNotEmpty(newCustomGift)) {
            return newCustomGift;
        }
        // 如果暑促活动不存在，则返回惊喜买赠活动
        if (CollectionUtils.isEmpty(result)) {
            return buildGifts(playActivityModels);
        }
        // 如果暑促活动存在，则返回暑促活动
        return result;
    }

    private static List<DealGift> buildGiftsForSummerPlay(long dealGroupId, long poiId, FilterPlayActivityModel filterPlayActivityModel) {
        // 判断暑促活动是否存在，暑促活动不存在则直接返回
        if (!PlayCenterWrapper.validSummerPlayExist(dealGroupId, poiId, filterPlayActivityModel)) {
            return null;
        }
        TaskInfoModel taskInfoModel = filterPlayActivityModel.getTaskInfo();
        if (taskInfoModel == null || CollectionUtils.isEmpty(taskInfoModel.getPrizeInfoList())
                || taskInfoModel.getPrizeInfoList().get(0) == null) {
            return null;
        }
        // 当前奖品仅有1个，因此仅取第1个元素，或许如果奖品有扩展，则此处需要修改
        PrizeInfoModel prizeInfoModel = taskInfoModel.getPrizeInfoList().get(0);
        String activityTimeDesc = buildActivityTimeDesc(taskInfoModel.getTaskEndTime());
        String activityValidTimeDesc = buildActivityValidTimeDesc(taskInfoModel.getTaskEndTime());
        int prizeCount = taskInfoModel.getPrizeCount();
        DealGift dealGift = new DealGift();
        dealGift.setThumbnail(prizeInfoModel.getPrizeImage());
        dealGift.setTitle(prizeInfoModel.getPrizeName());
        dealGift.setProductTag("核销后发放");
        dealGift.setLeftIconTag(COST_EFFECTIVE_ICON);
        dealGift.setSpecificTag("暑促活动专享");
        dealGift.setCouponNum(prizeCount);
        dealGift.setTimeDesc(activityTimeDesc);
        dealGift.setValidTimeDesc(activityValidTimeDesc);
        dealGift.setActivityId(Long.parseLong(filterPlayActivityModel.getActivityId()));
        return Lists.newArrayList(dealGift);
    }


    public static List<DealGift> buildGifts(List<PlayActivityModel> playActivityModels) {
        if (CollectionUtils.isEmpty(playActivityModels) 
                || playActivityModels.get(0).getPlayInfo() == null
                || CollectionUtils.isEmpty(playActivityModels.get(0).getPlayInfo().getTaskInfoList())
                || playActivityModels.get(0).getPlayInfo().getTaskInfoList().get(0) == null
                || CollectionUtils.isEmpty(playActivityModels.get(0).getPlayInfo().getTaskInfoList().get(0).getPrizeInfoList())) {
            return null;
        }
        String activityTimeDesc = buildActivityTimeDesc(playActivityModels.get(0).getPlayInfo().getEndTime());
        String activityValidTimeDesc = buildActivityValidTimeDesc(playActivityModels.get(0).getPlayInfo().getEndTime());
        TaskInfoModel taskInfoModel = playActivityModels.get(0).getPlayInfo().getTaskInfoList().get(0);
        // 目前一次团单只有一个，如果以后有多个不同赠品，需要修改
        PrizeInfoModel onlyPrizeInfoModel = taskInfoModel.getPrizeInfoList().get(0);
        if (onlyPrizeInfoModel == null) {
            return null;
        }
        int prizeCount = taskInfoModel.getPrizeCount();
        DealGift dealGift = new DealGift();
        dealGift.setThumbnail(onlyPrizeInfoModel.getPrizeImage());
        dealGift.setTitle(onlyPrizeInfoModel.getPrizeName());
        dealGift.setProductTag("核销后发放");
        dealGift.setSpecificTag(prizeCount == 1 ? "赠品" : String.format("赠品x%s", prizeCount));
        dealGift.setCouponNum(prizeCount);
        dealGift.setTimeDesc(activityTimeDesc);
        dealGift.setValidTimeDesc(activityValidTimeDesc);
        dealGift.setActivityId(playActivityModels.get(0).getPlayInfo().getActivityId());
        // 买赠规则
        dealGift.setUseRule(getBuyGiftUseRule(playActivityModels.get(0).getPlayInfo().getMaterialInfoList()));
        return Lists.newArrayList(dealGift);
    }

    private static String getBuyGiftUseRule(List<MaterialInfoModel> materialInfoList) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(materialInfoList)) {
            return null;
        }
        return materialInfoList.stream()
                .filter(materialInfoModel -> "rewardRulesExplanation".equals(materialInfoModel.getFieldKey()))
                .map(MaterialInfoModel::getFieldValue)
                .findFirst()
                .orElse(null);
    }

    public static String convertTimestamp(long timestamp, String pattern) {
        if (timestamp <= 0) {
            return "";
        }
        Date date = new Date(timestamp);
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        return sdf.format(date);
    }

    public static String buildActivityTimeDesc(Long endTime) {
        if (endTime == null) {
            return "";
        }
        String endTimeStr = convertTimestamp(endTime, "yyyy.MM.dd");
        return String.format("活动有效期至%s", endTimeStr);
    }

    public static String buildActivityValidTimeDesc(Long endTime) {
        if (endTime == null) {
            return "";
        }
        String endTimeStr = convertTimestamp(endTime, "yyyy.MM.dd");
        return String.format("%s到期", endTimeStr);
    }
}
