package com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommendgoods.dtos;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @auther: liweilong06
 * @date: 2022/10/18 下午7:43
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MediaM {

    private String pic;

    private int picWeight;

    private int picHeight;

    private String video;

    private int type;

    public MediaM(String picUrl, String videoUrl, int type) {
        this.pic = picUrl;
        this.video = videoUrl;
        this.type = type;
    }

    public String getPicUrl() {
        return pic;
    }

    public void setPicUrl(String picUrl) {
        this.pic = picUrl;
    }

    public String getVideoUrl() {
        return video;
    }

    public void setVideoUrl(String videoUrl) {
        this.video = videoUrl;
    }
}
