package com.sankuai.dzshoppingguide.product.detail.application.utils;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.sankuai.dzshoppingguide.product.detail.application.utils.PlayCenterWrapper;
import com.sankuai.dzshoppingguide.product.detail.application.utils.model.FilterPlayActivityModel;
import com.sankuai.dzshoppingguide.product.detail.application.utils.model.PrizeInfoModel;
import com.sankuai.dzshoppingguide.product.detail.application.utils.model.TaskInfoModel;
import com.sankuai.mktplay.center.mkt.play.center.client.play.entity.ActiveRequestUnitV2;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class PlayCenterWrapperValidSummerPlayExistTest {

    @Test
    public void testValidSummerPlayExistFilterPlayActivityModelIsNull() {
        assertFalse(PlayCenterWrapper.validSummerPlayExist(1L, 1L, null));
    }

    @Test
    public void testValidSummerPlayExistActiveRequestUnitIsEmpty() {
        FilterPlayActivityModel filterPlayActivityModel = new FilterPlayActivityModel();
        filterPlayActivityModel.setActiveRequestUnit(null);
        assertFalse(PlayCenterWrapper.validSummerPlayExist(1L, 1L, filterPlayActivityModel));
    }

    @Test
    public void testValidSummerPlayExistTaskInfoIsNull() {
        FilterPlayActivityModel filterPlayActivityModel = new FilterPlayActivityModel();
        filterPlayActivityModel.setActiveRequestUnit(Arrays.asList(new ActiveRequestUnitV2()));
        filterPlayActivityModel.setTaskInfo(null);
        assertFalse(PlayCenterWrapper.validSummerPlayExist(1L, 1L, filterPlayActivityModel));
    }

    @Test
    public void testValidSummerPlayExistPrizeInfoListIsEmpty() {
        FilterPlayActivityModel filterPlayActivityModel = new FilterPlayActivityModel();
        filterPlayActivityModel.setActiveRequestUnit(Arrays.asList(new ActiveRequestUnitV2()));
        TaskInfoModel taskInfoModel = new TaskInfoModel();
        taskInfoModel.setPrizeInfoList(null);
        filterPlayActivityModel.setTaskInfo(taskInfoModel);
        assertFalse(PlayCenterWrapper.validSummerPlayExist(1L, 1L, filterPlayActivityModel));
    }

    @Test
    public void testValidSummerPlayExistActiveRequestUnitContainsValidActiveRequestUnit() {
        FilterPlayActivityModel filterPlayActivityModel = new FilterPlayActivityModel();
        filterPlayActivityModel.setActiveRequestUnit(Arrays.asList(new ActiveRequestUnitV2()));
        TaskInfoModel taskInfoModel = new TaskInfoModel();
        PrizeInfoModel prizeInfoModel = new PrizeInfoModel();
        prizeInfoModel.setPrizeName("test");
        taskInfoModel.setPrizeInfoList(Arrays.asList(prizeInfoModel));
        filterPlayActivityModel.setTaskInfo(taskInfoModel);
        ActiveRequestUnitV2 activeRequestUnitV2 = new ActiveRequestUnitV2();
        Map<String, String> properties = new HashMap<>();
        properties.put("dealId", "1");
        properties.put("poiId", "1");
        activeRequestUnitV2.setProperties(properties);
        filterPlayActivityModel.setActiveRequestUnit(Arrays.asList(activeRequestUnitV2));
        assertTrue(PlayCenterWrapper.validSummerPlayExist(1L, 1L, filterPlayActivityModel));
    }

    @Test
    public void testValidSummerPlayExistActiveRequestUnitNotContainsValidActiveRequestUnit() {
        FilterPlayActivityModel filterPlayActivityModel = new FilterPlayActivityModel();
        filterPlayActivityModel.setActiveRequestUnit(Arrays.asList(new ActiveRequestUnitV2()));
        TaskInfoModel taskInfoModel = new TaskInfoModel();
        PrizeInfoModel prizeInfoModel = new PrizeInfoModel();
        prizeInfoModel.setPrizeName("test");
        taskInfoModel.setPrizeInfoList(Arrays.asList(prizeInfoModel));
        filterPlayActivityModel.setTaskInfo(taskInfoModel);
        ActiveRequestUnitV2 activeRequestUnitV2 = new ActiveRequestUnitV2();
        Map<String, String> properties = new HashMap<>();
        properties.put("dealId", "2");
        properties.put("poiId", "2");
        activeRequestUnitV2.setProperties(properties);
        filterPlayActivityModel.setActiveRequestUnit(Arrays.asList(activeRequestUnitV2));
        assertFalse(PlayCenterWrapper.validSummerPlayExist(1L, 1L, filterPlayActivityModel));
    }

    @Test
    public void testConvert2FilterPlayActivityModelResultIsNull() throws Throwable {
        String result = null;
        FilterPlayActivityModel model = PlayCenterWrapper.convert2FilterPlayActivityModel(result);
        assertNull(model);
    }

    @Test
    public void testConvert2FilterPlayActivityModelParseException() throws Throwable {
        String result = "invalid json";
        FilterPlayActivityModel model = PlayCenterWrapper.convert2FilterPlayActivityModel(result);
        assertNull(model);
    }

    @Test
    public void testConvert2FilterPlayActivityModelGetFieldException() throws Throwable {
        // Providing a minimal valid taskInfo object to avoid NullPointerException
        String result = "{\"activityId\":\"123\", \"subTitle\":null, \"playId\":null, \"materialInfoList\":[], \"taskInfo\":{\"prizeCount\":0, \"taskEndTime\":0, \"prizeInfoList\":[]}, \"activeRequestUnit\":[]}";
        FilterPlayActivityModel model = PlayCenterWrapper.convert2FilterPlayActivityModel(result);
        assertNotNull(model);
        assertEquals("123", model.getActivityId());
        assertNull(model.getSubTitle());
        assertNull(model.getPlayId());
        assertTrue(model.getMaterialInfoList().isEmpty());
        assertNotNull(model.getTaskInfo());
        assertTrue(model.getActiveRequestUnit().isEmpty());
    }

    @Test
    public void testConvert2FilterPlayActivityModelSuccess() throws Throwable {
        // Providing a minimal valid taskInfo object to avoid NullPointerException
        String result = "{\"activityId\":\"123\",\"subTitle\":\"test\",\"playId\":123,\"materialInfoList\":[],\"taskInfo\":{\"prizeCount\":0,\"taskEndTime\":0,\"prizeInfoList\":[]},\"activeRequestUnit\":[]}";
        FilterPlayActivityModel model = PlayCenterWrapper.convert2FilterPlayActivityModel(result);
        assertNotNull(model);
        assertEquals("123", model.getActivityId());
        assertEquals("test", model.getSubTitle());
        assertEquals(Long.valueOf(123), model.getPlayId());
        assertTrue(model.getMaterialInfoList().isEmpty());
        assertNotNull(model.getTaskInfo());
        assertTrue(model.getActiveRequestUnit().isEmpty());
    }
}
