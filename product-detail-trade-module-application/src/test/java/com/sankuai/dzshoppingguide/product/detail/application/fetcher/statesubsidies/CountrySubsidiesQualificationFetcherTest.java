package com.sankuai.dzshoppingguide.product.detail.application.fetcher.statesubsidies;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.BaseFetcherContext;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.attr.SkuAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.attr.SkuAttrFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.defaultselect.SkuDefaultSelect;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.defaultselect.SkuDefaultSelectFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.tag.guarantee.ProductGuaranteeTagInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.tag.guarantee.SkuGuaranteeTagFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.nib.price.operation.api.guarantee.common.dto.TagDTO;

import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class CountrySubsidiesQualificationFetcherTest {

    @Mock
    private CompositeAtomService compositeAtomService;

    private TestableCountrySubsidiesQualificationFetcher fetcher;

    private static class TestableCountrySubsidiesQualificationFetcher extends CountrySubsidiesQualificationFetcher {

        private Map<Class<?>, Object> dependencyResults = new HashMap<>();

        public void setTestDependencyResult(Class<? extends BaseFetcherContext> fetcherClass, Object result) {
            dependencyResults.put(fetcherClass, result);
        }

        @Override
        protected <Result extends FetcherReturnValueDTO> Optional<Result> getDependencyResult(Class<? extends BaseFetcherContext> dependencyFetcherClass, Class<Result> tClass) {
            return Optional.ofNullable((Result) dependencyResults.get(dependencyFetcherClass));
        }

        @Override
        protected <Result extends FetcherReturnValueDTO> Result getDependencyResult(Class<? extends BaseFetcherContext> dependencyFetcherClass) {
            return (Result) dependencyResults.get(dependencyFetcherClass);
        }

        public void setCompositeAtomService(CompositeAtomService service) throws Exception {
            Field field = CountrySubsidiesQualificationFetcher.class.getDeclaredField("compositeAtomService");
            field.setAccessible(true);
            field.set(this, service);
        }
    }

    @BeforeEach
    public void setup() throws Exception {
        fetcher = new TestableCountrySubsidiesQualificationFetcher();
        fetcher.setCompositeAtomService(compositeAtomService);
    }

    private void setupBasicDependencies() {
        ProductGuaranteeTagInfo tagInfo = new ProductGuaranteeTagInfo();
        tagInfo.setStateSubsidies(new TagDTO(1, "test"));
        fetcher.setTestDependencyResult(SkuGuaranteeTagFetcher.class, tagInfo);
        SkuDefaultSelect skuDefaultSelect = new SkuDefaultSelect(1L);
        fetcher.setTestDependencyResult(SkuDefaultSelectFetcher.class, skuDefaultSelect);
        Map<Long, Map<String, AttrDTO>> skuAttrMap = new HashMap<>();
        Map<String, AttrDTO> attrMap = new HashMap<>();
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setValue(Collections.singletonList("123456"));
        attrMap.put("Barcode", attrDTO);
        skuAttrMap.put(1L, attrMap);
        SkuAttr skuAttr = new SkuAttr(skuAttrMap);
        fetcher.setTestDependencyResult(SkuAttrFetcher.class, skuAttr);
    }

    /**
     * 测试当国家补贴标签为空时返回null
     */
    @Test
    public void testDoFetch_WhenCountrySubsidiesTagIsNull() throws Throwable {
        CompletableFuture<CountrySubsidiesQualificationDTO> result = fetcher.doFetch();
        assertNull(result.get());
        verifyNoInteractions(compositeAtomService);
    }

    /**
     * 测试当UPC码为空时返回null
     */
    @Test
    public void testDoFetch_WhenUpcCodeIsBlank() throws Throwable {
        ProductGuaranteeTagInfo tagInfo = new ProductGuaranteeTagInfo();
        tagInfo.setStateSubsidies(new TagDTO(1, "test"));
        fetcher.setTestDependencyResult(SkuGuaranteeTagFetcher.class, tagInfo);
        SkuDefaultSelect skuDefaultSelect = new SkuDefaultSelect(1L);
        fetcher.setTestDependencyResult(SkuDefaultSelectFetcher.class, skuDefaultSelect);
        Map<Long, Map<String, AttrDTO>> skuAttrMap = new HashMap<>();
        SkuAttr skuAttr = new SkuAttr(skuAttrMap);
        fetcher.setTestDependencyResult(SkuAttrFetcher.class, skuAttr);
        CompletableFuture<CountrySubsidiesQualificationDTO> result = fetcher.doFetch();
        assertNull(result.get());
        verifyNoInteractions(compositeAtomService);
    }

    /**
     * 测试当UPC码不是数字时返回null
     */
    @Test
    public void testDoFetch_WhenUpcCodeIsNotNumeric() throws Throwable {
        ProductGuaranteeTagInfo tagInfo = new ProductGuaranteeTagInfo();
        tagInfo.setStateSubsidies(new TagDTO(1, "test"));
        fetcher.setTestDependencyResult(SkuGuaranteeTagFetcher.class, tagInfo);
        SkuDefaultSelect skuDefaultSelect = new SkuDefaultSelect(1L);
        fetcher.setTestDependencyResult(SkuDefaultSelectFetcher.class, skuDefaultSelect);
        Map<Long, Map<String, AttrDTO>> skuAttrMap = new HashMap<>();
        Map<String, AttrDTO> attrMap = new HashMap<>();
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setValue(Collections.singletonList("abc"));
        attrMap.put("Barcode", attrDTO);
        skuAttrMap.put(1L, attrMap);
        SkuAttr skuAttr = new SkuAttr(skuAttrMap);
        fetcher.setTestDependencyResult(SkuAttrFetcher.class, skuAttr);
        CompletableFuture<CountrySubsidiesQualificationDTO> result = fetcher.doFetch();
        assertNull(result.get());
        verifyNoInteractions(compositeAtomService);
    }
}
