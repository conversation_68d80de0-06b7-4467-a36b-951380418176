package com.sankuai.dzshoppingguide.product.detail.application.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.mockito.quality.Strictness.LENIENT;
import com.dianping.pay.promo.display.api.PromoDisplayService;
import com.dianping.pay.promo.display.api.dto.PromoDisplayDTO;
import com.dianping.pay.promo.display.api.dto.QueryPromoDisplayRequest;
import com.dianping.pay.promo.rule.api.dto.Response;
import com.dianping.pigeon.remoting.invoker.concurrent.InvocationCallback;
import com.dianping.pigeon.remoting.invoker.util.InvokerHelper;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.meituan.inf.xmdlog.XMDLogFormat;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonClient;
import com.sankuai.dzshoppingguide.product.detail.domain.utils.PigeonCallbackUtils;
import com.sankuai.dztheme.massagebook.theme.ReserveThemeQueryService;
import com.sankuai.dztheme.massagebook.theme.req.ReserveQueryRequest;
import com.sankuai.dztheme.massagebook.theme.res.ReserveQueryResponse;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.stubbing.Answer;
import org.slf4j.Logger;
import com.sankuai.dztheme.deal.DealProductService;
import com.sankuai.dztheme.deal.req.DealProductRequest;
import com.sankuai.dztheme.deal.res.DealProductResult;
import java.util.Arrays;

@ExtendWith(MockitoExtension.class)
class CompositeAtomServiceImplQueryReserveProduct11Test {

    @Mock
    private ReserveThemeQueryService reserveThemeQueryService;

    @Mock
    private Logger log;

    @InjectMocks
    private CompositeAtomServiceImpl compositeAtomService;

    @Mock
    private PromoDisplayService promoDisplayServiceFuture;

    @Mock
    private DealProductService dealProductService;

    @BeforeEach
    void setUp() {
        InvokerHelper.clearCallback();
    }

    @AfterEach
    void tearDown() {
        InvokerHelper.clearCallback();
    }

    /**
     * 测试正常调用成功场景
     */
    @Test
    void testQueryReserveProductSuccess() throws Throwable {
        // arrange
        ReserveQueryRequest request = new ReserveQueryRequest();
        ReserveQueryResponse expectedResponse = new ReserveQueryResponse();
        CountDownLatch latch = new CountDownLatch(1);
        doAnswer(invocation -> {
            InvocationCallback callback = InvokerHelper.getCallback();
            if (callback != null) {
                callback.onSuccess(expectedResponse);
            }
            latch.countDown();
            return null;
        }).when(reserveThemeQueryService).query(any(ReserveQueryRequest.class));
        // act
        CompletableFuture<ReserveQueryResponse> future = compositeAtomService.queryReserveProduct(request);
        // assert
        assertTrue(latch.await(5, TimeUnit.SECONDS));
        assertNotNull(future);
        ReserveQueryResponse actualResponse = future.get(5, TimeUnit.SECONDS);
        assertEquals(expectedResponse, actualResponse);
        verify(reserveThemeQueryService, times(1)).query(request);
        verify(log, never()).error(any(String.class));
    }

    /**
     * 测试请求参数为null的边界场景
     */
    @Test
    void testQueryReserveProductWithNullRequest() throws Throwable {
        // arrange
        CountDownLatch latch = new CountDownLatch(1);
        doAnswer(invocation -> {
            InvocationCallback callback = InvokerHelper.getCallback();
            if (callback != null) {
                callback.onSuccess(null);
            }
            latch.countDown();
            return null;
        }).when(reserveThemeQueryService).query(isNull());
        // act
        CompletableFuture<ReserveQueryResponse> future = compositeAtomService.queryReserveProduct(null);
        // assert
        assertTrue(latch.await(5, TimeUnit.SECONDS));
        assertNotNull(future);
        assertNull(future.get(5, TimeUnit.SECONDS));
        verify(reserveThemeQueryService, times(1)).query(null);
        verify(log, never()).error(any(String.class));
    }

    /**
     * 测试服务返回null的边界场景
     */
    @Test
    void testQueryReserveProductReturnsNull() throws Throwable {
        // arrange
        ReserveQueryRequest request = new ReserveQueryRequest();
        CountDownLatch latch = new CountDownLatch(1);
        doAnswer(invocation -> {
            InvocationCallback callback = InvokerHelper.getCallback();
            if (callback != null) {
                callback.onSuccess(null);
            }
            latch.countDown();
            return null;
        }).when(reserveThemeQueryService).query(any(ReserveQueryRequest.class));
        // act
        CompletableFuture<ReserveQueryResponse> future = compositeAtomService.queryReserveProduct(request);
        // assert
        assertTrue(latch.await(5, TimeUnit.SECONDS));
        assertNotNull(future);
        assertNull(future.get(5, TimeUnit.SECONDS));
        verify(reserveThemeQueryService, times(1)).query(request);
        verify(log, never()).error(any(String.class));
    }

    /**
     * 测试正常流程 - 服务返回成功且有结果
     */
    @Test
    void testQueryPromoDisplayDTO_NormalSuccessWithResult() throws Throwable {
        // arrange
        QueryPromoDisplayRequest request = new QueryPromoDisplayRequest();
        Response<List<PromoDisplayDTO>> response = new Response<>();
        List<PromoDisplayDTO> promoList = Collections.singletonList(new PromoDisplayDTO());
        response.setSuccess(true);
        response.setResult(promoList);
        doAnswer((Answer<Void>) invocation -> {
            InvocationCallback callback = InvokerHelper.getCallback();
            if (callback != null) {
                callback.onSuccess(response);
            }
            return null;
        }).when(promoDisplayServiceFuture).queryPromoDisplayDTO(any(QueryPromoDisplayRequest.class));
        // act
        CompletableFuture<List<PromoDisplayDTO>> result = compositeAtomService.queryPromoDisplayDTO(request);
        // assert
        List<PromoDisplayDTO> resultList = result.get(1000, TimeUnit.MILLISECONDS);
        assertNotNull(resultList);
        assertEquals(1, resultList.size());
    }

    /**
     * 测试服务返回成功但结果为null
     */
    @Test
    void testQueryPromoDisplayDTO_SuccessButNullResult() throws Throwable {
        // arrange
        QueryPromoDisplayRequest request = new QueryPromoDisplayRequest();
        Response<List<PromoDisplayDTO>> response = new Response<>();
        response.setSuccess(true);
        response.setResult(null);
        doAnswer((Answer<Void>) invocation -> {
            InvocationCallback callback = InvokerHelper.getCallback();
            if (callback != null) {
                callback.onSuccess(response);
            }
            return null;
        }).when(promoDisplayServiceFuture).queryPromoDisplayDTO(any(QueryPromoDisplayRequest.class));
        // act
        CompletableFuture<List<PromoDisplayDTO>> result = compositeAtomService.queryPromoDisplayDTO(request);
        // assert
        assertNull(result.get(1000, TimeUnit.MILLISECONDS));
    }

    /**
     * 测试服务返回失败
     */
    @Test
    void testQueryPromoDisplayDTO_ServiceReturnFailure() throws Throwable {
        // arrange
        QueryPromoDisplayRequest request = new QueryPromoDisplayRequest();
        Response<List<PromoDisplayDTO>> response = new Response<>();
        response.setSuccess(false);
        doAnswer((Answer<Void>) invocation -> {
            InvocationCallback callback = InvokerHelper.getCallback();
            if (callback != null) {
                callback.onSuccess(response);
            }
            return null;
        }).when(promoDisplayServiceFuture).queryPromoDisplayDTO(any(QueryPromoDisplayRequest.class));
        // act
        CompletableFuture<List<PromoDisplayDTO>> result = compositeAtomService.queryPromoDisplayDTO(request);
        // assert
        assertNull(result.get(1000, TimeUnit.MILLISECONDS));
    }

    /**
     * 测试服务调用抛出异常
     */
    @Test
    void testQueryPromoDisplayDTO_ServiceThrowException() throws Throwable {
        // arrange
        QueryPromoDisplayRequest request = new QueryPromoDisplayRequest();
        RuntimeException exception = new RuntimeException("Service error");
        doAnswer((Answer<Void>) invocation -> {
            InvocationCallback callback = InvokerHelper.getCallback();
            if (callback != null) {
                callback.onFailure(exception);
            }
            return null;
        }).when(promoDisplayServiceFuture).queryPromoDisplayDTO(any(QueryPromoDisplayRequest.class));
        // act
        CompletableFuture<List<PromoDisplayDTO>> result = compositeAtomService.queryPromoDisplayDTO(request);
        // assert
        assertNull(result.get(1000, TimeUnit.MILLISECONDS));
        verify(promoDisplayServiceFuture).queryPromoDisplayDTO(any(QueryPromoDisplayRequest.class));
    }

    /**
     * 测试回调处理异常情况
     */
    @Test
    void testQueryPromoDisplayDTO_CallbackProcessingException() throws Throwable {
        // arrange
        QueryPromoDisplayRequest request = new QueryPromoDisplayRequest();
        doAnswer((Answer<Void>) invocation -> {
            InvocationCallback callback = InvokerHelper.getCallback();
            if (callback != null) {
                callback.onFailure(new RuntimeException("Callback processing error"));
            }
            return null;
        }).when(promoDisplayServiceFuture).queryPromoDisplayDTO(any(QueryPromoDisplayRequest.class));
        // act
        CompletableFuture<List<PromoDisplayDTO>> result = compositeAtomService.queryPromoDisplayDTO(request);
        // assert
        assertNull(result.get(1000, TimeUnit.MILLISECONDS));
        verify(promoDisplayServiceFuture).queryPromoDisplayDTO(any(QueryPromoDisplayRequest.class));
    }

    @Test
    void testQueryDealProduct_Success() throws Throwable {
        // arrange
        DealProductRequest request = new DealProductRequest();
        request.setProductIds(Arrays.asList(Integer.valueOf(1), Integer.valueOf(2), Integer.valueOf(3)));
        DealProductResult expectedResult = new DealProductResult();
        expectedResult.setMessage("Success");
        doAnswer(invocation -> {
            InvocationCallback callback = InvokerHelper.getCallback();
            if (callback != null) {
                callback.onSuccess(expectedResult);
            }
            return null;
        }).when(dealProductService).query(any());
        // act
        CompletableFuture<DealProductResult> resultFuture = compositeAtomService.queryDealProduct(request);
        // assert
        DealProductResult actualResult = resultFuture.join();
        assertNotNull(actualResult);
        assertEquals(expectedResult.getMessage(), actualResult.getMessage());
        verify(dealProductService, times(1)).query(request);
    }

    @Test
    void testQueryDealProduct_ServiceThrowsException() throws Throwable {
        // arrange
        DealProductRequest request = new DealProductRequest();
        request.setProductIds(Arrays.asList(Integer.valueOf(1), Integer.valueOf(2), Integer.valueOf(3)));
        RuntimeException expectedException = new RuntimeException("Service error");
        doAnswer(invocation -> {
            InvocationCallback callback = InvokerHelper.getCallback();
            if (callback != null) {
                callback.onFailure(expectedException);
            }
            return null;
        }).when(dealProductService).query(any());
        // act
        CompletableFuture<DealProductResult> resultFuture = compositeAtomService.queryDealProduct(request);
        // assert
        assertThrows(RuntimeException.class, () -> resultFuture.join());
        verify(dealProductService, times(1)).query(request);
    }

    @Test
    void testQueryDealProduct_NullRequest() throws Throwable {
        // arrange
        DealProductResult expectedResult = new DealProductResult();
        expectedResult.setMessage("Default result");
        doAnswer(invocation -> {
            InvocationCallback callback = InvokerHelper.getCallback();
            if (callback != null) {
                callback.onSuccess(expectedResult);
            }
            return null;
        }).when(dealProductService).query(isNull());
        // act
        CompletableFuture<DealProductResult> resultFuture = compositeAtomService.queryDealProduct(null);
        // assert
        DealProductResult actualResult = resultFuture.join();
        assertNotNull(actualResult);
        assertEquals(expectedResult.getMessage(), actualResult.getMessage());
        verify(dealProductService, times(1)).query(null);
    }

    @Test
    void testQueryDealProduct_NullProductIds() throws Throwable {
        // arrange
        DealProductRequest request = new DealProductRequest();
        request.setProductIds(null);
        DealProductResult expectedResult = new DealProductResult();
        expectedResult.setMessage("Empty result");
        doAnswer(invocation -> {
            InvocationCallback callback = InvokerHelper.getCallback();
            if (callback != null) {
                callback.onSuccess(expectedResult);
            }
            return null;
        }).when(dealProductService).query(any());
        // act
        CompletableFuture<DealProductResult> resultFuture = compositeAtomService.queryDealProduct(request);
        // assert
        DealProductResult actualResult = resultFuture.join();
        assertNotNull(actualResult);
        assertEquals(expectedResult.getMessage(), actualResult.getMessage());
        verify(dealProductService, times(1)).query(request);
    }

    @Test
    void testQueryDealProduct_EmptyProductIds() throws Throwable {
        // arrange
        DealProductRequest request = new DealProductRequest();
        request.setProductIds(Collections.emptyList());
        DealProductResult expectedResult = new DealProductResult();
        expectedResult.setMessage("Empty result");
        doAnswer(invocation -> {
            InvocationCallback callback = InvokerHelper.getCallback();
            if (callback != null) {
                callback.onSuccess(expectedResult);
            }
            return null;
        }).when(dealProductService).query(any());
        // act
        CompletableFuture<DealProductResult> resultFuture = compositeAtomService.queryDealProduct(request);
        // assert
        DealProductResult actualResult = resultFuture.join();
        assertNotNull(actualResult);
        assertEquals(expectedResult.getMessage(), actualResult.getMessage());
        verify(dealProductService, times(1)).query(request);
    }
}
