package com.sankuai.dzshoppingguide.product.detail.application.builder.promodetail;

import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.dianping.tgc.open.entity.BatchExProxyCouponResponseDTO;
import com.dianping.tgc.open.entity.PromoCouponInfo;
import com.dianping.tgc.open.entity.PromoReturnInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.dealuser.price.display.api.enums.ExtendDisplayInfoKeyEnum;
import com.sankuai.dealuser.price.display.api.enums.PricePromoInfoTypeEnum;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.extend.BaseBuilder;
import com.sankuai.dz.product.detail.RequestCustomParamEnum;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.builder.pricebar.builder.enums.CouponGuideTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.pricebar.builder.enums.PromoBarTagEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.pricebar.component.InflateCouponTipsComponent;
import com.sankuai.dzshoppingguide.product.detail.application.builder.promodetail.config.PromotionBarConfig;
import com.sankuai.dzshoppingguide.product.detail.application.builder.promodetail.config.PromotionPopUpConfig;
import com.sankuai.dzshoppingguide.product.detail.application.builder.promodetail.dto.PromotionDetailParams;
import com.sankuai.dzshoppingguide.product.detail.application.builder.promodetail.enums.CouponStyleEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.promodetail.enums.CouponTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.constants.Constants;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.LeadsInfoFetcher.LeadsInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.LeadsInfoFetcher.LeadsInfoResult;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.abtest.AbTestFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.abtest.AbTestReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.category.ProductCategoryFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.cepinpool.CostEffectivePinPoolFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.cepinpool.dto.CostEffectivePinTuan;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.coupon.CouponFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.coupon.CouponResult;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.dealgift.DealGiftFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.dealgift.DealGiftResult;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.cityId.CityIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.cityId.CityIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.shopid.ShopIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.shopid.ShopIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.membercard.card.discount.MemberCardDiscountFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.membercard.card.discount.MemberCardDiscountReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.pinpool.PricePinPoolFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.pinpool.PricePinPoolResult;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.pinpool.PromoDisplayFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.pinpool.PromoDisplayResult;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.best.shop.ShopInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.best.shop.ShopInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.*;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.dto.ProductPriceReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttrFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.defaultselect.SkuDefaultSelect;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.defaultselect.SkuDefaultSelectFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.FetcherDAGStarter;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.statesubsidies.CountrySubsidiesQualificationDTO;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.statesubsidies.CountrySubsidiesQualificationFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.service.promo.PromoDetailService;
import com.sankuai.dzshoppingguide.product.detail.application.utils.*;
import com.sankuai.dzshoppingguide.product.detail.application.utils.context.DealCtxUtils;
import com.sankuai.dzshoppingguide.product.detail.domain.utils.LionConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.promo.CouponItemPackageDTO;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.PromoTagEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.promo.vo.*;
import com.sankuai.nibmkt.promotion.api.common.enums.PromotionPropertyEnum;
import com.sankuai.nibmkt.promotion.api.common.enums.magicalmember.MMCTooltipGuideTypeEnum;
import com.sankuai.nibmkt.promotion.api.common.utils.MagicalMemberTagTextUtils;
import com.sankuai.nibmkt.promotion.api.query.model.magicalmember.response.MMCTooltipTextDTO;
import com.sankuai.nibmkt.promotion.api.query.model.magicalmember.response.MagicalMemberTagTextDTO;
import com.sankuai.nibscp.common.flow.identify.util.SptDyeUtil;
import com.sankuai.statesubsidies.c.thrift.constant.QualificationStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Nullable;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.sankuai.dzshoppingguide.product.detail.application.builder.pricebar.builder.enums.CouponGuideTypeEnum.GUIDE_PURCHASE;
import static com.sankuai.dzshoppingguide.product.detail.application.builder.pricebar.component.InflateCouponTipsComponent.getDiscountAmount;


/**
 * <AUTHOR>
 * @create 2025/3/3 14:57
 */
@Builder(
        moduleKey = ModuleKeyConstants.PRICE_DISCOUNT_DETAIL_MODULE,
        startFetcher = FetcherDAGStarter.class,
        dependentFetchers = {
                ProductBaseInfoFetcher.class,
                ShopIdMapperFetcher.class,
                CityIdMapperFetcher.class,
                ProductAttrFetcher.class,
                ShopInfoFetcher.class,
                CouponFetcher.class,
                PricePinPoolFetcher.class,
                LeadsInfoFetcher.class,
                PromoDisplayFetcher.class,
                DealGiftFetcher.class,
                SkuDefaultSelectFetcher.class,
                ProductIdlePromoPriceFetcher.class,
                ProductNormalPriceFetcher.class,
                ProductCostEffectivePriceFetcher.class,
                ProductPromoPriceFetcher.class,
                CostEffectivePinPoolFetcher.class,
                FinalPriceFetcher.class,
                ProductCategoryFetcher.class,
                MemberCardDiscountFetcher.class,
                CountrySubsidiesQualificationFetcher.class,
                AbTestFetcher.class,
        }
)
@Slf4j
public class PromoDetailBuilder extends BaseBuilder<PromoDetailVO> {

    private final List<String> MAGIC_ENHANCEMENT_TEST_RESULTS = Arrays.asList("d"); // 二期实验结果
    // 神券标签
    private final String MAGIC_COUPON_LABEL_ICON = "https://p0.meituan.net/ingee/1da8db164cd56eadda8ae81bfe679b622372.png";
    private final String PURCHASE_MAGIC_COUPON_PACKAGE_LABEL_ICON = "https://p0.meituan.net/dztgdetailimages/12418be8fe0f88d7b1ab81e143c99dba3991.png";

    @Resource
    private PromoDetailService promoDetailService;
    private ProductBaseInfo baseInfo = null;
    private ShopInfo shopInfo = null;
    private CouponResult couponResult = null;
    private LeadsInfoResult leadsInfoResult = null;
    private PricePinPoolResult pricePinPoolResult = null;
    private ShopIdMapper shopIdMapper = null;
    private CityIdMapper cityIdMapper = null;
    private PromoDisplayResult promoDisplayResult = null;
    private SkuDefaultSelect dealSkuResult = null;
    private DealGiftResult dealGiftResult = null;
    private ProductPriceReturnValue idlePromoPrice = null;
    private ProductPriceReturnValue normalPrice = null;
    private ProductPriceReturnValue costEffectivePrice = null;
    private ProductPriceReturnValue dealPromoPrice = null;
    private CostEffectivePinTuan costEffectivePinTuan = null;
    private @Nullable ProductAttr productAttr = null;
    private MemberCardDiscountReturnValue memberCardDiscountReturnValue = null;
    private AbTestReturnValue abTestReturnValue = null;

    @Override
    public PromoDetailVO doBuild() {
        baseInfo = getDependencyResult(ProductBaseInfoFetcher.class);
        shopInfo = getDependencyResult(ShopInfoFetcher.class);
        couponResult = getDependencyResult(CouponFetcher.class);
        leadsInfoResult = getDependencyResult(LeadsInfoFetcher.class);
        pricePinPoolResult = getDependencyResult(PricePinPoolFetcher.class);
        shopIdMapper = getDependencyResult(ShopIdMapperFetcher.class);
        cityIdMapper = getDependencyResult(CityIdMapperFetcher.class);
        promoDisplayResult = getDependencyResult(PromoDisplayFetcher.class);
        dealSkuResult = getDependencyResult(SkuDefaultSelectFetcher.class);
        dealGiftResult = getDependencyResult(DealGiftFetcher.class);
        idlePromoPrice = getDependencyResult(ProductIdlePromoPriceFetcher.class);
        normalPrice = getDependencyResult(ProductNormalPriceFetcher.class);
        costEffectivePrice = getDependencyResult(ProductCostEffectivePriceFetcher.class);
        dealPromoPrice = getDependencyResult(ProductPromoPriceFetcher.class);
        costEffectivePinTuan = getDependencyResult(CostEffectivePinPoolFetcher.class);
        productAttr = getDependencyResult(ProductAttrFetcher.class);
        memberCardDiscountReturnValue = getDependencyResult(MemberCardDiscountFetcher.class);
        abTestReturnValue = getDependencyResult(AbTestFetcher.class);
        QualificationStatusEnum qualificationStatusEnum = getDependencyResult(CountrySubsidiesQualificationFetcher.class, CountrySubsidiesQualificationDTO.class)
                .map(CountrySubsidiesQualificationDTO::getQualificationStatusEnum)
                .orElse(QualificationStatusEnum.UNBIND);
        PromoDetailVO result = new PromoDetailVO();
        try {
            List<DealGift> dealGifts = DealGiftHandler.buildDealGiftsFromPlay(request.getProductId(),
                    request.getClientTypeEnum().isMtClientType() ? shopIdMapper.getMtBestShopId()
                            : shopIdMapper.getDpBestShopId(),
                    dealGiftResult);
            // first
            PromoDetailModule promoDetailModule = promoDetailService.buildPromoDetailInfo(
                    request,
                    costEffectivePinTuan,
                    baseInfo,
                    normalPrice != null ? normalPrice.getPriceDisplayDTO() : null,
                    costEffectivePrice != null ? costEffectivePrice.getPriceDisplayDTO() : null,
                    productAttr
            );
            // second
            promoDetailService.buildDealPromoDetailInfo(
                    baseInfo,
                    costEffectivePinTuan,
                    request.getClientTypeEnum(),
                    dealPromoPrice != null ? dealPromoPrice.getPriceDisplayDTO() : null,
                    promoDetailModule,
                    costEffectivePrice != null ? costEffectivePrice.getPriceDisplayDTO() : null,
                    leadsInfoResult,
                    dealGiftResult,
                    dealGifts,
                    qualificationStatusEnum
            );
            // third
            promoDetailService.buildCardStylePromoDetailInfo(
                    request,
                    cityIdMapper,
                    baseInfo,
                    promoDetailModule,
                    normalPrice != null ? normalPrice.getPriceDisplayDTO() : null,
                    dealGifts,
                    couponResult != null ? couponResult.getExProxyCouponResponseDTO() : null,
                    leadsInfoResult,
                    costEffectivePinTuan,
                    costEffectivePrice != null ? costEffectivePrice.getPriceDisplayDTO() : null,
                    idlePromoPrice != null ? idlePromoPrice.getPriceDisplayDTO() : null,
                    pricePinPoolResult != null ? pricePinPoolResult.getPinProductBrief() : null,
                    promoDisplayResult != null ? promoDisplayResult.getPinPoolPromoAmount() : null,
                    dealPromoPrice != null ? dealPromoPrice.getPriceDisplayDTO() : null,
                    shopInfo,
                    shopIdMapper,
                    dealSkuResult
            );
            if (Objects.nonNull(promoDetailModule)){
                // 设置接口返回的时间戳，用于前端刷新组件
                promoDetailModule.setResponseTimestamp(System.currentTimeMillis());
                // 设置神会员点位
                promoDetailModule.setPosition(getPosition());
                promoDetailModule.setPoiId(request.getPoiId());
                result.setPromoDetails(promoDetailModule);
            }
            String flowFlag = request.getCustomParam(RequestCustomParamEnum.flowFlag);
            if (Objects.equals(flowFlag, Constants.MAGIC_COUPON_ENHANCEMENT_FLOW_FLAG)) {
                // 新的优惠浮层模型
                PromotionDetailParams promotionDetailParams = new PromotionDetailParams();
                // 价格信息（含优惠）
                promotionDetailParams.setDealPromDisplayDTO(dealPromoPrice != null ? dealPromoPrice.getPriceDisplayDTO() : null);
                promotionDetailParams.setProductPromoPriceReturnValue(dealPromoPrice);
                // 商品分类
                ProductCategory productCategory = getDependencyResult(ProductCategoryFetcher.class);
                promotionDetailParams.setProductCategory(productCategory);
                // 券信息
                BatchExProxyCouponResponseDTO batchExProxyCouponResponseDTO = couponResult.getExProxyCouponResponseDTO();
                if (Objects.nonNull(batchExProxyCouponResponseDTO)) {
                    promotionDetailParams.setOptPromoCoupons(batchExProxyCouponResponseDTO.getPromoCouponInfoList());
                    promotionDetailParams.setFinancialCoupons(batchExProxyCouponResponseDTO.getFinancialCouponInfoList());
                    // 投放券(曝光券)
                    promotionDetailParams.setExposureCoupons(batchExProxyCouponResponseDTO.getPromoExposureInfoList());
                    // OPT券与金融券合并券列表
                    List<PromoCouponInfo> optAndFinancialCoupons = Stream.of(promotionDetailParams.getOptPromoCoupons(), promotionDetailParams.getFinancialCoupons())
                            .filter(Objects::nonNull)
                            .flatMap(Collection::stream)
                            .collect(Collectors.toList());
                    promotionDetailParams.setOptAndFinancialCoupons(optAndFinancialCoupons);
                    // 下单返礼、返券、金融券活动
                    List<PromoReturnInfo> promoReturnActivities = batchExProxyCouponResponseDTO.getPromoReturnInfoList();
                    promotionDetailParams.setPromoReturnActivities(promoReturnActivities);
                }
                // 买赠
                promotionDetailParams.setDealGifts(dealGifts);
                // 拼团活动
                if (Objects.nonNull(pricePinPoolResult)) {
                    promotionDetailParams.setPinProductBrief(pricePinPoolResult.getPinProductBrief());
                }
                if (Objects.nonNull(promoDisplayResult)) {
                    promotionDetailParams.setPinPoolPromoAmount(promoDisplayResult.getPinPoolPromoAmount());
                }
                promotionDetailParams.setPageRequest(request);
                promotionDetailParams.setNormalPriceDisplayDTO(Optional.ofNullable(normalPrice).map(ProductPriceReturnValue::getPriceDisplayDTO).orElse(null));
                promotionDetailParams.setShopInfo(shopInfo);
                // 会员卡信息
                promotionDetailParams.setMemberCardDiscount(memberCardDiscountReturnValue);
                // 闲时价格
                promotionDetailParams.setIdleTimePriceDisplayDTO(Optional.ofNullable(idlePromoPrice).map(ProductPriceReturnValue::getPriceDisplayDTO).orElse(null));
                // 默认sku
                promotionDetailParams.setSkuDefaultSelect(dealSkuResult);
                promotionDetailParams.setShopIdMapper(shopIdMapper);
                promotionDetailParams.setProductBaseInfo(baseInfo);
                promotionDetailParams.setCityIdMapper(cityIdMapper);
                promotionDetailParams.setAbTestReturnValue(abTestReturnValue);
                PromotionPopUpModule promotionPopUpModule = buildPromotionPopUpModule(promotionDetailParams);
                result.setPromotionPopUpModule(promotionPopUpModule);
                // 构造领券栏
                List<PromotionBarModule> promotionBar = buildPromotionBar(promotionDetailParams);
                result.setPromotionBar(promotionBar);
            }
            return result;
        } catch (Exception e) {
            log.error("PromoDetailBuilder.doBuild() error:", e);
            return result;
        }
    }

    private PromotionPopUpModule buildPromotionPopUpModule(PromotionDetailParams promotionDetailParams) {
        PromotionPopUpModule promotionPopUpModule = new PromotionPopUpModule();
        // 神券-入口流量打点
        catMagicCouponAll(promotionDetailParams);
        PromotionPopUpConfig config = LionConfigUtils.getPromotionPopUpConfig();
        // 浮层名称
        promotionPopUpModule.setModuleName(config.getModuleName());
        // 最佳神券优惠券
        InflateCounponDTO bestMagicCoupon = buildBestMagicCoupon(promotionDetailParams);
        promotionPopUpModule.setBestInflateCoupon(bestMagicCoupon);
        // 其他神券优惠券
        InflateCounponDTO otherMagicCoupon = buildOtherMagicCoupon(promotionDetailParams);
        promotionPopUpModule.setOtherInflateCoupon(otherMagicCoupon);
        // 神券-有数据打点
        if (Objects.nonNull(bestMagicCoupon) || Objects.nonNull(otherMagicCoupon)) {
            catMagicCouponHasData(promotionDetailParams);
            ProductDetailPageRequest pageRequest = promotionDetailParams.getPageRequest();
            putDyeTraceParams4MagicCoupon(pageRequest);
        }
        // 优惠券列表
        List<PromotionCoupon> promotionCouponList = buildPromotionCouponList(promotionDetailParams, bestMagicCoupon,
                otherMagicCoupon, promotionDetailParams.getMrnVersion(), config);
        promotionPopUpModule.setPromotionCouponList(promotionCouponList);
        return promotionPopUpModule;
    }

    private List<PromotionCoupon> buildPromotionCouponList(PromotionDetailParams promotionDetailParams,
            InflateCounponDTO bestMagicCoupon, InflateCounponDTO otherMagicCoupon, String mrnVersion, PromotionPopUpConfig config) {
        // 1. 已享优惠
        PromotionCoupon usedCoupon = new PromotionCoupon();
        List<CouponModule> usedCouponList = buildUsedCoupons(promotionDetailParams, bestMagicCoupon, mrnVersion);
        // 过滤券类型&排序
        usedCouponList = filterAndSortCoupon(usedCouponList, config.getExcludeUsedCouponTypes(), config.getSortUsedCouponTypes(), true);
        if (CollectionUtils.isNotEmpty(usedCouponList)) {
            usedCoupon.setModuleName(config.getUsedCouponModuleName());
        }
        usedCoupon.setCouponList(usedCouponList);
        // 2. 更多优惠
        PromotionCoupon moreCoupon = new PromotionCoupon();
        List<CouponModule> moreCouponList = buildMoreCoupons(promotionDetailParams, otherMagicCoupon);
        // 过滤券类型&排序
        moreCouponList = filterAndSortCoupon(moreCouponList, config.getExcludeMoreCouponTypes(), config.getSortMoreCouponTypes(), true);
        if (CollectionUtils.isNotEmpty(moreCouponList)) {
            moreCoupon.setModuleName(config.getMoreCouponModuleName());
        }
        moreCoupon.setCouponList(moreCouponList);
        return Lists.newArrayList(usedCoupon, moreCoupon);
    }

    /**
     * 过滤&排序券
     * @param couponModules 券列表
     * @param excludeCouponTypes 排除的券类型
     * @param sortCouponTypes 排序的券类型
     * @return 过滤&排序后的券列表
     */
    private List<CouponModule> filterAndSortCoupon(List<CouponModule> couponModules, List<CouponTypeEnum> excludeCouponTypes, List<CouponTypeEnum> sortCouponTypes, boolean promotionPopUp) {
        if (CollectionUtils.isEmpty(couponModules)) {
            return couponModules;
        }
        // 先排除
        if (CollectionUtils.isNotEmpty(excludeCouponTypes)) {
            Stream<CouponModule> couponModuleStream = couponModules
                    .stream()
                    .filter(couponModule -> !excludeCouponTypes.contains(CouponTypeEnum.ofCode(couponModule.getCouponType())));
            if (promotionPopUp) {
                // 因为promotionBar要展示，所以会员卡不为null，如果是优惠浮层，要排除开通后的会员卡
                couponModules = couponModuleStream
                        .filter(couponModule -> !Objects.equals(couponModule.getCouponType(), CouponTypeEnum.MEMBER_CARD.getCode())
                                                                || CollectionUtils.isNotEmpty(couponModule.getCouponAmount()))
                        .collect(Collectors.toList());
            } else {
                couponModules = couponModuleStream.collect(Collectors.toList());
            }
        }
        // 后排序
        if (CollectionUtils.isNotEmpty(sortCouponTypes)) {
            // 按sortKeys顺序排序，不在sortKeys中的券，排在最后
            couponModules.sort((coupon1, coupon2) -> {
                if (sortCouponTypes.contains(CouponTypeEnum.ofCode(coupon1.getCouponType()))) {
                    return sortCouponTypes.indexOf(CouponTypeEnum.ofCode(coupon1.getCouponType())) - sortCouponTypes.indexOf(CouponTypeEnum.ofCode(coupon2.getCouponType()));
                }
                return 1;
            });
        }
        return couponModules;
    }

    // 构造更多优惠券（除神券）
    private List<CouponModule> buildMoreCoupons(PromotionDetailParams promotionDetailParams,
                                                InflateCounponDTO otherMagicCoupon) {
        List<CouponModule> result = Lists.newArrayList();
        // 神券包
        CouponModule magicCouponPackage = buildMagicCouponPackage(promotionDetailParams);
        result.add(magicCouponPackage);
        // 其他神券
        CouponModule otherMagicCouponModule = buildOtherMagicCouponModule(otherMagicCoupon, promotionDetailParams.getDealPromDisplayDTO(), promotionDetailParams.getAbTestReturnValue());
        result.add(otherMagicCouponModule);
        // 普通优惠券（平台券、商家券、政府券）
        List<CouponModule> normalCoupon = buildNormalCoupon4MorePromos(promotionDetailParams);
        if (CollectionUtils.isNotEmpty(normalCoupon)) {
            result.addAll(normalCoupon);
        }
        // 商家拼团
        CouponModule merchantGroupBuyCoupon = buildMerchantGroupBuyCoupon4MorePromos(promotionDetailParams);
        result.add(merchantGroupBuyCoupon);
        // 限时特惠
         CouponModule idleTimeCoupon = buildIdleTimeCoupon4MorePromos(promotionDetailParams);
         result.add(idleTimeCoupon);
        // 多买多减 BUY_MORE_REDUCTION
        CouponModule buyMoreReduction = buildBuyMoreReduction4MorePromos(promotionDetailParams);
        result.add(buyMoreReduction);
        // 多买多折 BUY_MORE_DISCOUNT
        CouponModule buyMoreDiscount = buildBuyMoreDiscount4MorePromos(promotionDetailParams);
        result.add(buyMoreDiscount);
        // 会员卡
        CouponModule memberCard = buildMemberCard4MorePromos(promotionDetailParams);
        result.add(memberCard);
        // 曝光券
        List<CouponModule> exposureCoupon = buildExposureCoupon4MorePromos(promotionDetailParams);
        if (CollectionUtils.isNotEmpty(exposureCoupon)) {
            result.addAll(exposureCoupon);
        }
        // 去除空值
        result.removeIf(coupon -> Objects.isNull(coupon) || coupon.getCouponStyle() < 1);
        return result;
    }

    /**
     * 构造
     *
     * @param promotionDetailParams
     * @param couponPackageIconConfig
     * @return
     */
    public CouponModule buildMagicCouponPromotionBarByMemberCouponLabel(PromotionDetailParams promotionDetailParams, Map<String, Object> couponPackageIconConfig) {
        if (Objects.isNull(promotionDetailParams.getAbTestReturnValue())) {
            return null;
        }
        String expResult = promotionDetailParams.getAbTestReturnValue().getAbTestExpResult("MtMagicalCouponStyleEnhancementV2");
        // 判断是否命中实验
        if (!Lists.newArrayList("c", "d").contains(expResult)) {
            return null;
        }
        PriceDisplayDTO dealPromDisplayDTO = promotionDetailParams.getDealPromDisplayDTO();
        if (Objects.isNull(dealPromDisplayDTO)) {
            return null;
        }
        Map<String, String> extendDisplayInfoMap = dealPromDisplayDTO.getExtendDisplayInfo();
        if (MapUtils.isEmpty(extendDisplayInfoMap)) {
            return null;
        }
        //神会员标签文案
        String magicalMemberCouponLabel = extendDisplayInfoMap.get(ExtendDisplayInfoKeyEnum.MAGICAL_MEMBER_COUPON_LABEL.getKey());
        MagicalMemberTagTextDTO memberTagText = MagicalMemberTagTextUtils.bestMagicalMemberTagTextDTO(Lists.newArrayList(magicalMemberCouponLabel));
        if (memberTagText == null) {
            return null;
        }
        // 卡控膨胀状态 只有抢购是下发
        if (!org.apache.commons.lang.StringUtils.equals("抢购", memberTagText.getStatus())) {
            return null;
        }
        CouponModule couponModule = new CouponModule();
        // 券类型
        couponModule.setCouponType(CouponTypeEnum.MAGIC_COUPON_PACKAGE.getCode());
        // 券样式
        couponModule.setCouponStyle(CouponStyleEnum.BIG_CARD.getCode());
        // 已享优惠模块的神券领券栏
        PromotionBarModule promotionBarModule = new PromotionBarModule();
        //膨胀券文案 //膨胀金额文案
        String barLabelDesc = String.format("%s%s", memberTagText.getInflateShowText(), memberTagText.getReduceMoney());
        if (org.apache.commons.lang3.StringUtils.isNotBlank(barLabelDesc)) {
            String labelIcon = MapUtils.getString(couponPackageIconConfig, "icon", PURCHASE_MAGIC_COUPON_PACKAGE_LABEL_ICON);
            int iconHeight = MapUtils.getIntValue(couponPackageIconConfig, "iconHeight", 11);
            int iconWidth = MapUtils.getIntValue(couponPackageIconConfig, "iconWeight", 35);
            promotionBarModule.setLabelDesc(barLabelDesc);
            promotionBarModule.setLabelIcon(labelIcon);
            promotionBarModule.setIconHeight(iconHeight);
            promotionBarModule.setIconWidth(iconWidth);
        }
        couponModule.setPromotionBarModule(promotionBarModule);
        return couponModule;
    }


    // 构造曝光券
    private List<CouponModule> buildExposureCoupon4MorePromos(PromotionDetailParams promotionDetailParams) {
        if (CollectionUtils.isEmpty(promotionDetailParams.getExposureCoupons())) {
            return null;
        }
        return promoDetailService.buildExposureCoupon(promotionDetailParams);
    }

    // 构造普通优惠券
    private List<CouponModule> buildNormalCoupon4MorePromos(PromotionDetailParams promotionDetailParams) {
        if (CollectionUtils.isEmpty(promotionDetailParams.getOptAndFinancialCoupons()) || Objects.isNull(promotionDetailParams.getDealPromDisplayDTO())) {
            return null;
        }
        return promoDetailService.buildNormalCoupon(promotionDetailParams.getOptAndFinancialCoupons(), promotionDetailParams.getDealPromDisplayDTO());
    }

    // 构造神券包
    private CouponModule buildMagicCouponPackage(PromotionDetailParams promotionDetailParams) {
        if (Objects.isNull(promotionDetailParams) || Objects.isNull(promotionDetailParams.getDealPromDisplayDTO())) {
            return null;
        }
        return promoDetailService.buildMagicCouponPackage(promotionDetailParams);
    }

    // 构造会员卡
    private CouponModule buildMemberCard4MorePromos(PromotionDetailParams promotionDetailParams) {
        if (Objects.isNull(promotionDetailParams.getDealPromDisplayDTO())) {
            return null;
        }
        return promoDetailService.buildMemberCard(promotionDetailParams, false);
    }

    // 构造多买多折
    private CouponModule buildBuyMoreDiscount4MorePromos(PromotionDetailParams promotionDetailParams) {
        if (Objects.isNull(promotionDetailParams.getNormalPriceDisplayDTO())) {
            return null;
        }
        return promoDetailService.buildBuyMoreDiscount(promotionDetailParams);
    }

    // 构造多买多减
    private CouponModule buildBuyMoreReduction4MorePromos(PromotionDetailParams promotionDetailParams) {
        if (Objects.isNull(promotionDetailParams.getNormalPriceDisplayDTO())) {
            return null;
        }
        return promoDetailService.buildBuyMoreReduction(promotionDetailParams);
    }

    // 构造闲时特惠
     private CouponModule buildIdleTimeCoupon4MorePromos(PromotionDetailParams promotionDetailParams) {
         if (Objects.isNull(promotionDetailParams.getIdleTimePriceDisplayDTO())) {
             return null;
         }
         return promoDetailService.buildIdleTimeCoupon(
                 promotionDetailParams.getIdleTimePriceDisplayDTO(),
                 promotionDetailParams
         );
     }

    // 构造商家拼团
    private CouponModule buildMerchantGroupBuyCoupon4MorePromos(PromotionDetailParams promotionDetailParams) {
        if (Objects.isNull(promotionDetailParams.getPinProductBrief())
                || Objects.isNull(promotionDetailParams.getPinPoolPromoAmount())) {
            return null;
        }
        ProductDetailPageRequest pageRequest = promotionDetailParams.getPageRequest();
        boolean validPinPool = BuyButtonHelper.isValidPinPool(pageRequest.getProductId(),
                promotionDetailParams.getPinProductBrief(), promotionDetailParams.getPinPoolPromoAmount(),
                promotionDetailParams.getNormalPriceDisplayDTO());
        if (!validPinPool) {
            return null;
        }
        return promoDetailService.buildMerchantGroupBuyCouponModule(promotionDetailParams);
    }

    // 构造最佳神券
    private CouponModule buildBestMagicCouponModule(InflateCounponDTO bestMagicCoupon, PriceDisplayDTO dealPrice, AbTestReturnValue abTestReturnValue) {
        if (Objects.isNull(bestMagicCoupon) || CollectionUtils.isEmpty(bestMagicCoupon.getCouponItems())) {
            return null;
        }
        CouponModule couponModule = new CouponModule();
        // 券类型
        couponModule.setCouponType(CouponTypeEnum.BEST_MAGIC_COUPON.getCode());
        // 券样式
        couponModule.setCouponStyle(CouponStyleEnum.BIG_CARD.getCode());
        // 已享优惠模块的神券领券栏
        PromotionBarModule promotionBarModule = buildMagicCouponPromotionBarModule(dealPrice, true, abTestReturnValue);
        couponModule.setPromotionBarModule(promotionBarModule);
        return couponModule;
    }

    private PromotionBarModule buildMagicCouponPromotionBarModule(PriceDisplayDTO dealPrice, boolean isUsedPromo, AbTestReturnValue abTestReturnValue) {
        MagicalMemberTagTextDTO magicalCouponTagText = getMagicalCouponTagText(dealPrice);
        if (Objects.isNull(magicalCouponTagText)) {
            return null;
        }
        // 屏蔽「神券立减X」
        if (Objects.equals(magicalCouponTagText.getInflateShowText(), "立减")) {
            return null;
        }
        // 仅未用优惠展示神券标签
        return buildPromotionBarModule(magicalCouponTagText.getInflateShowText(), magicalCouponTagText.getReduceMoney(), MAGIC_COUPON_LABEL_ICON);
    }

    private PromotionBarModule buildPromotionBarModule(String showText, String reduceMoney, String labelIcon) {
        PromotionBarModule promotionBarModule = new PromotionBarModule();
        String barLabelDesc = String.format("%s%s", showText, reduceMoney);
        if (StringUtils.isNotBlank(barLabelDesc)) {
            promotionBarModule.setLabelIcon(labelIcon);
            promotionBarModule.setLabelDesc(barLabelDesc);
        }
        return promotionBarModule;
    }

    // 构造其他神券
    private CouponModule buildOtherMagicCouponModule(InflateCounponDTO otherMagicCoupon,
                                                     PriceDisplayDTO dealPrice,
                                                     AbTestReturnValue abTestReturnValue) {
        if (Objects.isNull(otherMagicCoupon) || CollectionUtils.isEmpty(otherMagicCoupon.getCouponItems())) {
            return null;
        }
        CouponModule couponModule = new CouponModule();
        // 券类型
        couponModule.setCouponType(CouponTypeEnum.MAGIC_COUPON.getCode());
        // 券样式
        couponModule.setCouponStyle(CouponStyleEnum.BIG_CARD.getCode());
        // 更多优惠中神券的领券栏
        PromotionBarModule promotionBarModule = buildMagicCouponPromotionBarModule(dealPrice, false, abTestReturnValue);
        couponModule.setPromotionBarModule(promotionBarModule);
        return couponModule;
    }

    // 构造已享优惠券（除神券）
    private List<CouponModule> buildUsedCoupons(PromotionDetailParams promotionDetailParams,
            InflateCounponDTO bestMagicCoupon, String mrnVersion) {
        if (Objects.isNull(promotionDetailParams.getDealPromDisplayDTO())
                || CollectionUtils.isEmpty(promotionDetailParams.getDealPromDisplayDTO().getUsedPromos())) {
            return null;
        }
        List<CouponModule> result = Lists.newArrayList();
        // 神券
        CouponModule magiCouponModule = buildBestMagicCouponModule(bestMagicCoupon, promotionDetailParams.getDealPromDisplayDTO(), promotionDetailParams.getAbTestReturnValue());
        result.add(magiCouponModule);
        // 普通优惠券（平台券、商家券、政府券）
        List<CouponModule> normalCoupons = promoDetailService.buildNormalCoupons(promotionDetailParams.getDealPromDisplayDTO().getUsedPromos(), mrnVersion);
        if (CollectionUtils.isNotEmpty(normalCoupons)) {
            result.addAll(normalCoupons);
        }
        // 买赠活动
        List<CouponModule> buyGiftActivities = promoDetailService.buildBuyGiftActivity(promotionDetailParams);
        if (CollectionUtils.isNotEmpty(buyGiftActivities)) {
            result.addAll(buyGiftActivities);
        }
        // 下单返礼活动
        List<CouponModule> orderGiftActivities = promoDetailService.buildOrderGiftActivity(promotionDetailParams);
        if (CollectionUtils.isNotEmpty(orderGiftActivities)) {
            result.addAll(orderGiftActivities);
        }
        // 下单返券活动
        List<CouponModule> orderCouponActivities = promoDetailService.buildOrderCouponActivity(promotionDetailParams);
        if (CollectionUtils.isNotEmpty(orderCouponActivities)) {
            result.addAll(orderCouponActivities);
        }
        // 过滤空值
        result.removeIf(coupon -> Objects.isNull(coupon) || coupon.getCouponStyle() < 1);
        return result;
    }

    // 构造其他神券
    private InflateCounponDTO buildOtherMagicCoupon(PromotionDetailParams promotionDetailParams) {
        PriceDisplayDTO dealPromDisplayDTO = promotionDetailParams.getDealPromDisplayDTO();
        if (Objects.isNull(dealPromDisplayDTO)) {
            return null;
        }
        // 不在usedPromos中的神券属于其他神券
        InflateCounponDTO otherMagicCoupon = new InflateCounponDTO();
        ProductCategory productCategory = promotionDetailParams.getProductCategory();
        // 业务ID
        otherMagicCoupon.setNibBiz(PromoDetailService.getNibBiz(productCategory.getProductSecondCategoryId()));
        // 券列表
        CouponItemPackageDTO couponItemPackageDTO = promoDetailService.buildOtherMagicCouponItems(dealPromDisplayDTO,
                request.getUserId());
        if (Objects.isNull(couponItemPackageDTO)) {
            return null;
        }
        otherMagicCoupon.setCouponItems(couponItemPackageDTO.getCouponItems());
        otherMagicCoupon.setCouponIds(couponItemPackageDTO.getCouponIds());
        return otherMagicCoupon;
    }

    // 构造最佳优惠中神券的信息
    private InflateCounponDTO buildBestMagicCoupon(PromotionDetailParams promotionDetailParams) {
        PriceDisplayDTO dealPromDisplayDTO = promotionDetailParams.getDealPromDisplayDTO();
        if (Objects.isNull(dealPromDisplayDTO)) {
            return null;
        }
        InflateCounponDTO bestMagicCoupon = new InflateCounponDTO();
        ProductCategory productCategory = promotionDetailParams.getProductCategory();
        // 业务ID
        bestMagicCoupon.setNibBiz(PromoDetailService.getNibBiz(productCategory.getProductSecondCategoryId()));
        CouponItemPackageDTO couponItemPackageDTO = promoDetailService.buildBestMagicCouponItems(dealPromDisplayDTO,
                request.getUserId());
        if (Objects.isNull(couponItemPackageDTO)) {
            return null;
        }
        // 券列表
        bestMagicCoupon.setCouponItems(couponItemPackageDTO.getCouponItems());
        // 券ID列表
        bestMagicCoupon.setCouponIds(couponItemPackageDTO.getCouponIds());
        return bestMagicCoupon;
    }

    private List<PromotionBarModule> buildPromotionBar(PromotionDetailParams promotionDetailParams) {
        // 已享优惠
        InflateCounponDTO bestMagicCoupon = buildBestMagicCoupon(promotionDetailParams);
        List<CouponModule> usedCoupons = buildUsedCoupons(promotionDetailParams, bestMagicCoupon, promotionDetailParams.getMrnVersion());
        // 更多优惠
        InflateCounponDTO otherMagicCoupon = buildOtherMagicCoupon(promotionDetailParams);
        List<CouponModule> moreCoupons = buildMoreCoupons(promotionDetailParams, otherMagicCoupon);
        // 神券强化感知2期 购神券包标签
        CouponModule magicCouponPackagePurchaseModule = buildMagicCouponPromotionBarByMemberCouponLabel(promotionDetailParams, LionConfigUtils.getMagicPackageCouponIconConfig());
        if (Objects.nonNull(magicCouponPackagePurchaseModule)) {
            moreCoupons.add(magicCouponPackagePurchaseModule);
        }
        // 特殊处理神券，已享优惠和未用优惠中的神券标签互斥
        if (CollectionUtils.isNotEmpty(usedCoupons) && Objects.nonNull(bestMagicCoupon) && Objects.nonNull(otherMagicCoupon)) {
            usedCoupons.removeIf(coupon -> Objects.equals(coupon.getCouponType(), CouponTypeEnum.BEST_MAGIC_COUPON.getCode()));
        }
        return buildPromotionBar(usedCoupons, moreCoupons);
    }

    private List<PromotionBarModule> buildPromotionBar(List<CouponModule> usedCoupons, List<CouponModule> moreCoupons) {
        // 特殊处理买赠
        CouponModule buyGiveGift = CollectionUtils.emptyIfNull(usedCoupons).stream()
                .filter(coupon -> Objects.equals(coupon.getCouponType(), CouponTypeEnum.BUY_GIVE_ACTIVITY.getCode()))
                .findFirst()
                .orElse(null);
        if (Objects.nonNull(buyGiveGift)) {
            usedCoupons.remove(buyGiveGift);
        }
        if (moreCoupons != null && buyGiveGift != null) {
            moreCoupons.add(buyGiveGift);
        }
        // 过滤&排序
        return filterAndSortPromotionBar(usedCoupons, moreCoupons);
    }

    private List<PromotionBarModule> filterAndSortPromotionBar(List<CouponModule> usedCoupons, List<CouponModule> moreCoupons) {
        PromotionBarConfig promotionBarConfig = LionConfigUtils.getPromotionBarConfig();
        usedCoupons = filterAndSortCoupon(usedCoupons, promotionBarConfig.getExcludeUsedCouponTypes(), promotionBarConfig.getSortUsedCouponTypes(), false);
        moreCoupons = filterAndSortCoupon(moreCoupons, promotionBarConfig.getExcludeMoreCouponTypes(), promotionBarConfig.getSortMoreCouponTypes(), false);
        List<PromotionBarModule> result = Lists.newArrayList();
        // 未享优惠
        List<PromotionBarModule> morePromotionBar = CollectionUtils.emptyIfNull(moreCoupons)
                .stream()
                .filter(Objects::nonNull)
                .map(CouponModule::getPromotionBarModule)
                .filter(Objects::nonNull)
                .filter(bar -> StringUtils.isNotEmpty(bar.getLabelDesc()))
                .collect(Collectors.toList());
        result.addAll(morePromotionBar);
        // 已享优惠
        List<PromotionBarModule> usedPromotionBar = CollectionUtils.emptyIfNull(usedCoupons)
                .stream()
                .filter(Objects::nonNull)
                .map(CouponModule::getPromotionBarModule)
                .filter(Objects::nonNull)
                .filter(bar -> StringUtils.isNotEmpty(bar.getLabelDesc()))
                .collect(Collectors.toList());
        result.addAll(usedPromotionBar);
        return result;
    }

    /**
     * 获取有效的优惠条列表（过滤掉promotionBarModule为空的券）
     * @param couponList 更多优惠券列表
     * @param promoTags 用来排序标签
     * @return 有效的优惠条列表
     */
    private List<PromotionBarModule> getValidPromotionBars(List<CouponModule> couponList, List<PromoBarTagEnum> promoTags) {
        if (CollectionUtils.isEmpty(couponList)) {
            return Collections.emptyList();
        }

        // 1.从优惠券模块，获取优惠栏模块
        List<PromotionBarModule> result = couponList.stream()
                .map(CouponModule::getPromotionBarModule)
                .filter(Objects::nonNull)
                .filter(bar -> StringUtils.isNotEmpty(bar.getLabelTheme()) || StringUtils.isNotEmpty(bar.getLabelDesc()))
                .collect(Collectors.toList());

        // 2. 过滤掉不在 promoTags 中的标签
        if (CollectionUtils.isNotEmpty(result) && CollectionUtils.isNotEmpty(promoTags)) {
            result = result.stream()
                    .filter(bar -> {
                        PromoBarTagEnum tag = PromoBarTagEnum.getByLabelTheme(bar.getLabelTheme());
                        return tag != null && promoTags.contains(tag);
                    })
                    .collect(Collectors.toList());

            // 3. 按照 promoTags 列表的顺序排序
            result.sort((o1, o2) -> {
                PromoBarTagEnum tag1 = PromoBarTagEnum.getByLabelTheme(o1.getLabelTheme());
                PromoBarTagEnum tag2 = PromoBarTagEnum.getByLabelTheme(o2.getLabelTheme());
                return Integer.compare(promoTags.indexOf(tag1), promoTags.indexOf(tag2));
            });
        }

        return result;
    }

    private String getPosition() {
        if (DealCtxUtils.isHarmony(request.getShepherdGatewayParam().getUserAgent())) {
            return "";
        }
        if (request.getClientTypeEnum() == ClientTypeEnum.MT_APP) {
            return "2104";
        }
        if (request.getClientTypeEnum() == ClientTypeEnum.DP_APP
                && isMagicMemberValid(request.getShepherdGatewayParam().getMrnVersion())) {
            return "3104";
        }
        if (request.getClientTypeEnum() == ClientTypeEnum.MT_XCX) {
            return "1104";
        }
        return "";
    }

    // 神会员优惠生效开关
    public static boolean isMagicMemberValid(String mrnVersion) {
        if (StringUtils.isBlank(mrnVersion)) {
            return false;
        }
        return VersionUtils.isGreaterThanOrEqual(mrnVersion, Lion.getString("com.sankuai.dzu.tpbase.dztgdetailweb",
                LionConstants.MAGICAL_DP_MRN_MIN_VERSION, "0.5.11"));
    }

    private MagicalMemberTagTextDTO getMagicalCouponTagText(PriceDisplayDTO dealPrice) {
        if (Objects.isNull(dealPrice) || MapUtils.isEmpty(dealPrice.getExtendDisplayInfo())) {
            return null;
        }
        //神会员标签文案
        String magicalMemberCouponLabel = dealPrice.getExtendDisplayInfo().get(ExtendDisplayInfoKeyEnum.MAGICAL_MEMBER_COUPON_LABEL.getKey());
        return MagicalMemberTagTextUtils.bestMagicalMemberTagTextDTO(Lists.newArrayList(magicalMemberCouponLabel));
    }


    private void putDyeTraceParams4MagicCoupon(ProductDetailPageRequest pageRequest) {
        List<SptDyeUtil.DyeTraceParam> traceParams = Lists.newArrayList();
        // step1 创建染色场景参数对象 (主场景、子场景)
        SptDyeUtil.DyeTraceParam flowEntranceParam = SptDyeUtil.DyeTraceParam.ofFlowEntrance("MAGIC_MEMBER", "default");
        traceParams.add(flowEntranceParam);
        // step1.5 根据定位城市id创建染色对象
        if (pageRequest.getClientTypeEnum().isMtClientType()) {
            SptDyeUtil.DyeTraceParam magicMemberMtCityParam = SptDyeUtil.DyeTraceParam.ofMagicMemberMtCity(
                    String.valueOf(pageRequest.getGpsCityId()));
            traceParams.add(magicMemberMtCityParam);
        } else {
            SptDyeUtil.DyeTraceParam magicMemberDpCityParam = SptDyeUtil.DyeTraceParam.ofMagicMemberDpCity(
                    String.valueOf(pageRequest.getGpsCityId()));
            traceParams.add(magicMemberDpCityParam);
        }
        // step2 调用工具类方法染色
        SptDyeUtil.putDyeTraceParams(traceParams);
    }

    private void catMagicCouponAll(PromotionDetailParams params) {
        Map<String, String> metricTags = buildMagicCouponMetricData(params);
        Cat.logMetricForCount("magicalCoupon_all", metricTags);
    }

    private void catMagicCouponHasData(PromotionDetailParams params) {
        Map<String, String> metricTags = buildMagicCouponMetricData(params);
        Cat.logMetricForCount("magicalCoupon_hasData", metricTags);
    }

    private Map<String, String> buildMagicCouponMetricData(PromotionDetailParams params) {
        ProductDetailPageRequest pageRequest = params.getPageRequest();
        ProductCategory productCategory = params.getProductCategory();
        Map<String, String> metricTags = Maps.newHashMap();
        metricTags.put("pageSource", pageRequest.getPageSource());

        metricTags.put("categoryId", String.valueOf(productCategory.getProductSecondCategoryId()));
        metricTags.put("cityId", String.valueOf(pageRequest.getGpsCityId()));
        metricTags.put("isMt", String.valueOf(pageRequest.getClientTypeEnum().isMtClientType()));
        metricTags.put("clientType", String.valueOf(PromoDetailService.getCatBusinessClientType(pageRequest.getClientTypeEnum())));
        return metricTags;
    }
}
