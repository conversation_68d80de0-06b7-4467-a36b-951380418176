package com.sankuai.dzshoppingguide.product.detail.application.utils;

import static org.junit.Assert.assertEquals;
import java.util.Calendar;
import java.util.Date;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class TimeUtilsGetDateStringTest {

    /**
     * 测试 getDateString 方法，当 date 为 null 时
     */
    @Test
    public void testGetDateStringWhenDateIsNull() throws Throwable {
        // arrange
        Date date = null;
        // act
        String result = TimeUtils.getDateString(date);
        // assert
        assertEquals("", result);
    }

    /**
     * 测试 getDateString 方法，当时间差小于 60 秒时返回"刚刚"
     */
    @Test
    public void testGetDateStringWhenLessThanOneMinute() throws Throwable {
        // arrange
        // 30秒前
        Date date = new Date(System.currentTimeMillis() - 30 * 1000);
        // act
        String result = TimeUtils.getDateString(date);
        // assert
        assertEquals("刚刚", result);
    }

    /**
     * 测试 getDateString 方法，当时间差大于等于 60 秒且小于 1 小时时返回"X分钟前"
     */
    @Test
    public void testGetDateStringWhenLessThanOneHour() throws Throwable {
        // arrange
        // 10分钟前
        Date date = new Date(System.currentTimeMillis() - 10 * 60 * 1000);
        // act
        String result = TimeUtils.getDateString(date);
        // assert
        assertEquals("10分钟前", result);
    }

    /**
     * 测试 getDateString 方法，当时间差大于等于 1 小时且小于 24 小时时返回"X小时前"
     */
    @Test
    public void testGetDateStringWhenLessThanOneDay() throws Throwable {
        // arrange
        // 2小时前
        Date date = new Date(System.currentTimeMillis() - 2 * 3600 * 1000);
        // act
        String result = TimeUtils.getDateString(date);
        // assert
        assertEquals("2小时前", result);
    }

    /**
     * 测试 getDateString 方法，当时间差大于等于 1 天且小于 4 天时返回"X天前"
     */
    @Test
    public void testGetDateStringWhenLessThanFourDays() throws Throwable {
        // arrange
        // 2天前
        Date date = new Date(System.currentTimeMillis() - 2 * 86400 * 1000);
        // act
        String result = TimeUtils.getDateString(date);
        // assert
        assertEquals("2天前", result);
    }

    /**
     * 测试 getDateString 方法，当时间差大于等于 4 天且是当年时返回"X月X日"
     */
    @Test
    public void testGetDateStringWhenMoreThanFourDaysInCurrentYear() throws Throwable {
        // arrange
        Calendar cal = Calendar.getInstance();
        // 10天前
        cal.add(Calendar.DAY_OF_MONTH, -10);
        Date date = cal.getTime();
        // act
        String result = TimeUtils.getDateString(date);
        // assert
        Calendar expectedCal = Calendar.getInstance();
        expectedCal.setTime(date);
        String expected = (expectedCal.get(Calendar.MONTH) + 1) + "月" + expectedCal.get(Calendar.DAY_OF_MONTH) + "日";
        assertEquals(expected, result);
    }

    /**
     * 测试 getDateString 方法，当时间是往年时返回"X年X月X日"
     */
    @Test
    public void testGetDateStringWhenPreviousYear() throws Throwable {
        // arrange
        Calendar cal = Calendar.getInstance();
        // 去年
        cal.add(Calendar.YEAR, -1);
        Date date = cal.getTime();
        // act
        String result = TimeUtils.getDateString(date);
        // assert
        Calendar expectedCal = Calendar.getInstance();
        expectedCal.setTime(date);
        String expected = expectedCal.get(Calendar.YEAR) + "年" + (expectedCal.get(Calendar.MONTH) + 1) + "月" + expectedCal.get(Calendar.DAY_OF_MONTH) + "日";
        assertEquals(expected, result);
    }

    /**
     * 测试 getDateString 方法，当 date 不为 null，且 date 的时间戳与当前时间的时间戳之差小于 60 秒时
     */
    @Test
    public void testGetDateStringWhenDateIsNotNulAndTimeDifferenceLessThan60Seconds() throws Throwable {
        // arrange
        Date date = new Date(System.currentTimeMillis() - 10000);
        // act
        String result = TimeUtils.getDateString(date);
        // assert
        assertEquals("刚刚", result);
    }

    /**
     * 测试 getDateString 方法，当 date 不为 null，且 date 的时间戳与当前时间的时间戳之差大于等于 60 秒，且小于 3600 秒时
     */
    @Test
    public void testGetDateStringWhenDateIsNotNulAndTimeDifferenceBetween60SecondsAnd1Hour() throws Throwable {
        // arrange
        Date date = new Date(System.currentTimeMillis() - 60000);
        // act
        String result = TimeUtils.getDateString(date);
        // assert
        assertEquals("1分钟前", result);
    }

    /**
     * 测试 getDateString 方法，当 date 不为 null，且 date 的时间戳与当前时间的时间戳之差大于等于 3600 秒，且小于 86400 秒时
     */
    @Test
    public void testGetDateStringWhenDateIsNotNulAndTimeDifferenceBetween1HourAnd1Day() throws Throwable {
        // arrange
        Date date = new Date(System.currentTimeMillis() - 3600000);
        // act
        String result = TimeUtils.getDateString(date);
        // assert
        assertEquals("1小时前", result);
    }

    /**
     * 测试 getDateString 方法，当 date 不为 null，且 date 的时间戳与当前时间的时间戳之差大于等于 86400 秒，且小于 86400*4 秒时
     */
    @Test
    public void testGetDateStringWhenDateIsNotNulAndTimeDifferenceBetween1DayAnd4Days() throws Throwable {
        // arrange
        Date date = new Date(System.currentTimeMillis() - 86400000);
        // act
        String result = TimeUtils.getDateString(date);
        // assert
        assertEquals("1天前", result);
    }
}
