package com.sankuai.dzshoppingguide.product.detail.application.builder.similargoods;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.mock;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.producttheme.ProductThemeResult;
import com.sankuai.dzshoppingguide.product.detail.spi.similargoods.vo.DealTinyInfoVO;
import com.sankuai.dzshoppingguide.product.detail.spi.similargoods.vo.dtos.DzProductVO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import static org.mockito.Mockito.*;
import com.sankuai.dzshoppingguide.product.detail.spi.similargoods.vo.SimilarStyleGoods;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.apache.commons.collections4.CollectionUtils;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import javax.validation.constraints.AssertTrue;

@ExtendWith(MockitoExtension.class)
class ListingResponseBuilderTest {

    private final ListingResponseBuilder builder = new ListingResponseBuilder();

    @InjectMocks
    private ListingResponseBuilder listingResponseBuilder;

    @Mock
    private ProductDetailPageRequest mockRequest;

    /**
     * 测试当productVO为null时，方法应返回null
     */
    @Test
    public void testBuildDealTinyInfoVO_WhenProductVONull_ShouldReturnNull() {
        // arrange
        ProductDetailPageRequest mockRequest = mock(ProductDetailPageRequest.class);
        // act
        DealTinyInfoVO result = builder.buildDealTinyInfoVO(mockRequest, null);
        // assert
        assertNull(result, "当productVO为null时，返回值应为null");
    }

    /**
     * 测试当productVO所有字段都有值时，正确构建DealTinyInfoVO
     */
    @Test
    public void testBuildDealTinyInfoVO_WhenAllFieldsPresent_ShouldBuildCorrectly() {
        // arrange
        ProductDetailPageRequest mockRequest = mock(ProductDetailPageRequest.class);
        DzProductVO productVO = new DzProductVO();
        productVO.setLongProductId(12345L);
        productVO.setTitle("测试商品标题");
        productVO.setMarketPrice("100.00");
        productVO.setSalePrice("80.00");
        productVO.setDiscountTag("8折");
        productVO.setButtonName("立即购买");
        productVO.setPic("http://test.com/pic.jpg");
        productVO.setButtonJumpUrl("http://test.com/buy");
        productVO.setSale("热卖中");
        // act
        DealTinyInfoVO result = builder.buildDealTinyInfoVO(mockRequest, productVO);
        // assert
        assertNotNull(result, "返回值不应为null");
        assertEquals(12345, result.getDealGroupId(), "dealGroupId转换不正确");
        assertEquals("测试商品标题", result.getTitle(), "title字段不匹配");
        assertEquals("100.00", result.getMarketPrice(), "marketPrice字段不匹配");
        assertEquals("80.00", result.getFinalPrice(), "finalPrice字段不匹配");
        assertEquals("8折", result.getDiscount(), "discount字段不匹配");
        assertEquals("立即购买", result.getBtnText(), "btnText字段不匹配");
        assertEquals("http://test.com/pic.jpg", result.getHeadPic(), "headPic字段不匹配");
        assertEquals("http://test.com/buy", result.getDirectBuyJumpUrl(), "directBuyJumpUrl字段不匹配");
        assertEquals("热卖中", result.getSaleTag(), "saleTag字段不匹配");
    }

    /**
     * 测试当productVO部分字段为null时，对应字段应为null
     */
    @Test
    public void testBuildDealTinyInfoVO_WhenPartialFieldsNull_ShouldSetNullForMissingFields() {
        // arrange
        ProductDetailPageRequest mockRequest = mock(ProductDetailPageRequest.class);
        DzProductVO productVO = new DzProductVO();
        productVO.setLongProductId(12345L);
        productVO.setTitle("测试商品标题");
        // 其他字段保持null
        // act
        DealTinyInfoVO result = builder.buildDealTinyInfoVO(mockRequest, productVO);
        // assert
        assertNotNull(result, "返回值不应为null");
        assertEquals(12345, result.getDealGroupId(), "dealGroupId转换不正确");
        assertEquals("测试商品标题", result.getTitle(), "title字段不匹配");
        assertNull(result.getMarketPrice(), "marketPrice应为null");
        assertNull(result.getFinalPrice(), "finalPrice应为null");
        assertNull(result.getDiscount(), "discount应为null");
        assertNull(result.getBtnText(), "btnText应为null");
        assertNull(result.getHeadPic(), "headPic应为null");
        assertNull(result.getDirectBuyJumpUrl(), "directBuyJumpUrl应为null");
        assertNull(result.getSaleTag(), "saleTag应为null");
    }

    /**
     * 测试当longProductId超过Integer最大值时，应抛出ArithmeticException
     */
    @Test
    public void testBuildDealTinyInfoVO_WhenLongProductIdOverflow_ShouldThrowException() {
        // arrange
        ProductDetailPageRequest mockRequest = mock(ProductDetailPageRequest.class);
        DzProductVO productVO = new DzProductVO();
        productVO.setLongProductId(Integer.MAX_VALUE + 1L);
        // act & assert
        assertThrows(ArithmeticException.class, () -> builder.buildDealTinyInfoVO(mockRequest, productVO), "当longProductId超过Integer最大值时应抛出ArithmeticException");
    }

    /**
     * 测试request参数未被使用时，方法应正常工作
     */
    @Test
    public void testBuildDealTinyInfoVO_WhenRequestNotUsed_ShouldWorkNormally() {
        // arrange
        DzProductVO productVO = new DzProductVO();
        productVO.setLongProductId(12345L);
        productVO.setTitle("测试商品标题");
        // act
        DealTinyInfoVO result = builder.buildDealTinyInfoVO(null, productVO);
        // assert
        assertNotNull(result, "返回值不应为null");
        assertEquals(12345, result.getDealGroupId(), "dealGroupId转换不正确");
        assertEquals("测试商品标题", result.getTitle(), "title字段不匹配");
    }

    @Test
    public void testBuildResponseWithValidParameters() throws Throwable {
        // arrange
        DzProductVO originalProduct = new DzProductVO();
        originalProduct.setLongProductId(1L);
        originalProduct.setTitle("Product 1");
        List<DzProductVO> productList = Arrays.asList(createDzProductVO(2L), createDzProductVO(3L));
        boolean hasNext = true;
        ProductThemeResult productThemeResult = new ProductThemeResult();
        productThemeResult.setHasNext(hasNext);
        productThemeResult.setTotal(2);
        // act
        SimilarStyleGoods result = listingResponseBuilder.buildResponse(mockRequest, originalProduct, productList, productThemeResult);
        // assert
        assertNotNull(result);
        assertEquals(hasNext, result.isHasNext());
        assertEquals(productList.size(), result.getTotalCount());
        assertEquals("查看更多商品", result.getMoreText());
        assertNotNull(result.getCurrentDealInfo());
        assertEquals(productList, result.getProductItems());
        assertEquals("同款好价", result.getModuleTitle());
        assertEquals("比价助手", result.getPopUpTitle());
        assertEquals("查看更多商品", result.getPopUpModuleTitle());
    }

    @Test
    public void testBuildResponseWithNullOriginalProduct() throws Throwable {
        // arrange
        List<DzProductVO> productList = Arrays.asList(createDzProductVO(2L), createDzProductVO(3L));
        boolean hasNext = false;
        ProductThemeResult productThemeResult = new ProductThemeResult();
        productThemeResult.setHasNext(hasNext);
        productThemeResult.setTotal(2);
        // act
        SimilarStyleGoods result = listingResponseBuilder.buildResponse(mockRequest, null, productList, productThemeResult);
        // assert
        assertNotNull(result);
        assertEquals(hasNext, result.isHasNext());
        assertEquals(productList.size(), result.getTotalCount());
        assertEquals("查看更多商品", result.getMoreText());
        assertNull(result.getCurrentDealInfo());
        assertEquals(productList, result.getProductItems());
        assertEquals("同款好价", result.getModuleTitle());
        assertEquals("比价助手", result.getPopUpTitle());
        assertEquals("查看更多商品", result.getPopUpModuleTitle());
    }

    @Test
    public void testBuildResponseWithEmptyProductList() throws Throwable {
        // arrange
        DzProductVO originalProduct = createDzProductVO(1L);
        List<DzProductVO> productList = Collections.emptyList();
        boolean hasNext = false;
        ProductThemeResult productThemeResult = new ProductThemeResult();
        productThemeResult.setHasNext(hasNext);
        productThemeResult.setTotal(2);
        // act
        SimilarStyleGoods result = listingResponseBuilder.buildResponse(mockRequest, originalProduct, productList, productThemeResult);
        // assert
        assertNotNull(result);
        assertEquals(hasNext, result.isHasNext());
        assertEquals(2, result.getTotalCount());
        assertEquals("查看更多商品", result.getMoreText());
        assertNotNull(result.getCurrentDealInfo());
        assertTrue(result.getProductItems().isEmpty());
        assertEquals("同款好价", result.getModuleTitle());
        assertEquals("比价助手", result.getPopUpTitle());
        assertEquals("查看更多商品", result.getPopUpModuleTitle());
    }

    @Test
    public void testBuildResponseWithNullProductList() throws Throwable {
        // arrange
        DzProductVO originalProduct = createDzProductVO(1L);
        boolean hasNext = true;
        ProductThemeResult productThemeResult = new ProductThemeResult();
        productThemeResult.setHasNext(hasNext);
        productThemeResult.setTotal(2);
        // act
        SimilarStyleGoods result = listingResponseBuilder.buildResponse(mockRequest, originalProduct, null, productThemeResult);
        // assert
        assertNotNull(result);
        assertEquals(hasNext, result.isHasNext());
        assertEquals(2, result.getTotalCount());
        assertEquals("查看更多商品", result.getMoreText());
        assertNotNull(result.getCurrentDealInfo());
        assertNull(result.getProductItems());
        assertEquals("同款好价", result.getModuleTitle());
        assertEquals("比价助手", result.getPopUpTitle());
        assertEquals("查看更多商品", result.getPopUpModuleTitle());
    }

    private DzProductVO createDzProductVO(Long productId) {
        DzProductVO product = new DzProductVO();
        product.setLongProductId(productId);
        product.setTitle("Product " + productId);
        return product;
    }

    @Test
    public void testBuildMoreText(){
        String result = listingResponseBuilder.buildMoreText(51);
        assertEquals("查看更多50+商品", result);
        result = listingResponseBuilder.buildMoreText(40);
        assertEquals("查看更多40+商品", result);
        result = listingResponseBuilder.buildMoreText(10);
        assertEquals("查看更多商品", result);
    }
}
