package com.sankuai.dzshoppingguide.product.detail.application.utils;

import com.dianping.cat.Cat;
import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Type;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019/2/24.
 */
@Slf4j
public class GsonUtils {

    public static Gson GSON = new Gson();

    public static String toJsonString(Object object) {
        return GSON.toJson(object);
    }

    public static<T> T fromJsonString(String objectStr, Class<T> clazz) {
        return StringUtils.isEmpty(objectStr) ? null : GSON.fromJson(objectStr, clazz);
    }

    public static<T> T fromJsonString(String objectStr, Type type) {
        return StringUtils.isEmpty(objectStr) ? null : GSON.fromJson(objectStr, type);
    }

    public static<T, E> T getParamFromMapJson(String mapJson, E key, Class<T> clazz) {
        try {
            if (StringUtils.isEmpty(mapJson) || key == null || JsonParser.parseString(mapJson) == null) {
                return null;
            }
            JsonObject jsonObject = JsonParser.parseString(mapJson).getAsJsonObject();
            JsonElement jsonElement = jsonObject.get(key.toString());
            if (jsonElement == null) {
                return null;
            }
            return GSON.fromJson(jsonElement, clazz);
        } catch (Exception e) {
            log.error("[GsonUtils] getParamFromMapJson error, mapJson={}", mapJson, e);
        }
        return null;
    }
}
