package com.sankuai.dzshoppingguide.product.detail.application.builder.welfare;

import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.BaseBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.FetcherDAGStarter;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.welfare.WelfareDocFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.welfare.WelfareDocInfo;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.welfare.vo.WelfareMerchantBO;

import java.util.Objects;

/**
 * @Author: <EMAIL>
 * @Date: 2025/3/9
 */
@Builder(
        moduleKey = ModuleKeyConstants.WELFARE_INFO_MODULE,
        startFetcher = FetcherDAGStarter.class,
        dependentFetchers = {WelfareDocFetcher.class}
)
public class WelfareDocBuilder extends BaseBuilder<WelfareMerchantBO> {
    @Override
    public WelfareMerchantBO doBuild() {
        WelfareDocInfo welfareDocInfo = getDependencyResult(WelfareDocFetcher.class);
        if (Objects.isNull(welfareDocInfo)) {
            return null;
        }
        WelfareMerchantBO welfareMerchant = new WelfareMerchantBO();
        welfareMerchant.setDesc(welfareDocInfo.getDzProductDocResp().getText());
        welfareMerchant.setTitle("公益商家");
        welfareMerchant.setIcon("https://p0.meituan.net/travelcube/ab2944835b44a6388458340d007d97a61464.png");
        return welfareMerchant;
    }
}
