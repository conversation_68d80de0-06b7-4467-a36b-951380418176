package com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.component.quick.entrance.shopping.cart;

import com.alibaba.fastjson.JSON;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.enums.OpenTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.extend.redirect.SimpleRedirectActionVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.component.quick.entrance.QuickEntranceButtonVO;
import lombok.extern.slf4j.Slf4j;

/**
 * @Author: guangyujie
 * @Date: 2025/3/18 20:18
 */
@Slf4j
public class ShoppingCartQuickEntranceComponent {

    public static QuickEntranceButtonVO build(final ProductDetailPageRequest request) {
        try {
            QuickEntranceButtonVO button = new QuickEntranceButtonVO();
            button.setButtonText("购物车");
            button.setButtonPic("https://img.meituan.net/beautyimg/5ce31fae18f00524499ee09877f323d01275.png");
//            button.setTopRightSubscript();
            button.setActionData(new SimpleRedirectActionVO(OpenTypeEnum.redirect,
                    "imeituan://www.meituan.com/mrn?mrn_biz=meishi&mrn_entry=food-shopping-cart-list&mrn_component=ShoppingCartList&cartType=2&mrn_min_version=1.7.12"
            ));
            return button;
        } catch (Throwable throwable) {
            log.error("ShoppingCartQuickEntranceComponent.build,request:{}", JSON.toJSONString(request), throwable);
            return null;
        }
    }

}
