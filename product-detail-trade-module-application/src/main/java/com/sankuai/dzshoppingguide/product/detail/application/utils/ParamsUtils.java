package com.sankuai.dzshoppingguide.product.detail.application.utils;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.dianping.vc.sdk.dp.client.ClientType;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.enums.MobileOSTypeEnum;
import joptsimple.internal.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.*;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/2/8 16:30
 */
@Slf4j
public class ParamsUtils {

    public static int getClientType(final ClientTypeEnum clientType,
                                    final MobileOSTypeEnum mobileOSType) {
        boolean isIos = mobileOSType == MobileOSTypeEnum.IOS;
        switch (clientType) {
            case DP_APP: // 点评APP
                return isIos ? ClientType.DP_IPHONE_NATIVE : ClientType.DP_ANDROID_NATIVE;
            case DP_PC: // 点评PC
            case DP_M: // 点评M站
                return ClientType.M_WEB;
            case DP_XCX: // 点评小程序
                return ClientType.DP_WEIXIN_API;
            case DP_BAIDU_MAP_XCX:
                return isIos ? ClientType.DP_IPHONE_WEB : ClientType.DP_ANDROID_WEB;
            case DP_WX: // 点评微信
            case MT_WX: // 美团微信
                return ClientType.WEIXIN;
            case MT_WAI_MAI_APP:
            case MT_MAP:
            case MT_MAO_YAN_APP:
            case MT_APP: // 美团APP
                return isIos ? ClientType.MT_IPHONE_NATIVE : ClientType.MT_ANDROID_NATIVE;
            case MT_PC: // 美团PC
            case MT_I: // 美团I站
                return ClientType.I_WEB;
            case MT_XCX: // 美团小程序
                return ClientType.MT_WEIXIN_API;
            case MT_ZJ_XCX:
            case MT_WWJKZ_XCX:
            case MT_KUAI_SHOU_XCX:
            case MT_MAO_YAN_XCX:
                return isIos ? ClientType.MT_IPHONE_WEB : ClientType.MT_ANDROID_WEB;
            case KAI_DIAN_BAO:
                return ClientType.DP_ANDROID_NATIVE;
        }
        // TODO 兜底先传点评安卓native的枚举信息
        return ClientType.DP_ANDROID_NATIVE;
    }

    /**
     * 判断params是否为空（empty、null）、map是否包含name、value是否为空
     *
     * @param params 参数的map
     * @param name   参数的key
     * @return true：params为空（empty、null）、map不包含name、value是空；否则返回false；
     */
    public static boolean isEmpty(Map<String, Object> params, String name) {
        return MapUtils.isEmpty(params) || params.get(name) == null;
    }

    /**
     * 从params里获取value；
     * 参数的value；如果params是空（empty or null）、或者map不包含name、value为空时，返回值为defaultValue。
     *
     * @param params       参数的map
     * @param name         参数的key
     * @param defaultValue 默认值
     * @return 参数的value；如果params是空（empty or null）、或者map不包含name、value为空时，返回值为defaultValue。
     */
    public static Object getValue(Map<String, Object> params, String name, Object defaultValue) {
        if (isEmpty(params, name)) {
            return defaultValue;
        }
        return params.get(name);
    }
}
