package com.sankuai.dzshoppingguide.product.detail.spi.facilities.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.FacilitiesVO;
import com.sankuai.dz.product.detail.gateway.spi.vo.AbstractModuleVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

import static com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants.DEAL_DETAIL_FACILITIES;

/**
 * <AUTHOR>
 * @create 2025/2/7 16:16
 * 移动之家：https://mobile.sankuai.com/studio/model/info/41272
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TypeDoc(description = "新版预订详情页（头图模块）")
@MobileDo(id = 0xc0ad)
public class DetailServiceFacilitiesVO extends AbstractModuleVO {
    @Override
    public String getModuleKey() {
        return DEAL_DETAIL_FACILITIES;
    }

    @FieldDoc(description = "服务设施模块详情")
    @MobileDo.MobileField(key = 0x62fe)
    private List<FacilitiesVO> detailServiceFacilities;
}
