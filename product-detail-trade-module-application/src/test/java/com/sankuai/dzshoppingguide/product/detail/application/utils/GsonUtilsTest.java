package com.sankuai.dzshoppingguide.product.detail.application.utils;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import java.lang.reflect.Type;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class GsonUtilsTest {

    /**
     * 测试输入的JSON字符串为空的情况
     */
    @Test
    public void testGetParamFromMapJsonEmptyJson() throws Throwable {
        // arrange
        String mapJson = "";
        String key = "key";
        Class<String> clazz = String.class;
        // act
        String result = GsonUtils.getParamFromMapJson(mapJson, key, clazz);
        // assert
        assertNull(result);
    }

    /**
     * 测试输入的键为空的情况
     */
    @Test
    public void testGetParamFromMapJsonNullKey() throws Throwable {
        // arrange
        String mapJson = "{\"key\":\"value\"}";
        String key = null;
        Class<String> clazz = String.class;
        // act
        String result = GsonUtils.getParamFromMapJson(mapJson, key, clazz);
        // assert
        assertNull(result);
    }

    /**
     * 测试解析后的JSON对象为空的情况
     */
    @Test
    public void testGetParamFromMapJsonNullJsonObject() throws Throwable {
        // arrange
        String mapJson = "{}";
        String key = "key";
        Class<String> clazz = String.class;
        // act
        String result = GsonUtils.getParamFromMapJson(mapJson, key, clazz);
        // assert
        assertNull(result);
    }

    /**
     * 测试JSON对象中不存在键对应的值的情况
     */
    @Test
    public void testGetParamFromMapJsonNoKey() throws Throwable {
        // arrange
        String mapJson = "{\"otherKey\":\"value\"}";
        String key = "key";
        Class<String> clazz = String.class;
        // act
        String result = GsonUtils.getParamFromMapJson(mapJson, key, clazz);
        // assert
        assertNull(result);
    }

    /**
     * 测试键对应的值可以成功转换为指定的类对象的情况
     */
    @Test
    public void testGetParamFromMapJsonSuccess() throws Throwable {
        // arrange
        String mapJson = "{\"key\":\"value\"}";
        String key = "key";
        Class<String> clazz = String.class;
        // act
        String result = GsonUtils.getParamFromMapJson(mapJson, key, clazz);
        // assert
        assertEquals("value", result);
    }

    /**
     * Test fromJsonString method with null input
     */
    @Test
    public void testFromJsonStringWithNullInput() {
        // arrange
        String nullInput = null;
        Type type = new TypeToken<Map<String, String>>() {
        }.getType();
        // act
        Map<String, String> result = GsonUtils.fromJsonString(nullInput, type);
        // assert
        assertNull(result);
    }

    /**
     * Test fromJsonString method with empty input
     */
    @Test
    public void testFromJsonStringWithEmptyInput() {
        // arrange
        String emptyInput = "";
        Type type = new TypeToken<Map<String, String>>() {
        }.getType();
        // act
        Map<String, String> result = GsonUtils.fromJsonString(emptyInput, type);
        // assert
        assertNull(result);
    }

    /**
     * Test fromJsonString method with valid JSON input
     */
    @Test
    public void testFromJsonStringWithValidInput() {
        // arrange
        String validInput = "{\"key\":\"value\"}";
        Type type = new TypeToken<Map<String, String>>() {
        }.getType();
        // act
        Map<String, String> result = GsonUtils.fromJsonString(validInput, type);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("value", result.get("key"));
    }

    /**
     * Test fromJsonString method with invalid JSON input
     */
    @Test
    public void testFromJsonStringWithInvalidInput() {
        // arrange
        String invalidInput = "{invalid json}";
        Type type = new TypeToken<Map<String, String>>() {
        }.getType();
        // act & assert
        assertThrows(com.google.gson.JsonSyntaxException.class, () -> {
            GsonUtils.fromJsonString(invalidInput, type);
        });
    }

    /**
     * Helper DTO class for testing simple objects
     */
    private static class TestDto {

        private String name;

        private int value;

        public String getName() {
            return name;
        }

        public int getValue() {
            return value;
        }
    }

    /**
     * Helper DTO class for testing nested objects
     */
    private static class TestDtoWithNested {

        private String name;

        private int value;

        private TestDto child;

        public String getName() {
            return name;
        }

        public int getValue() {
            return value;
        }

        public TestDto getChild() {
            return child;
        }
    }

    /**
     * Test when input string is null
     * Expected: Returns null
     */
    @Test
    public void testFromJsonString_WhenInputNull() {
        // arrange
        String jsonString = null;
        // act
        TestDto result = GsonUtils.fromJsonString(jsonString, TestDto.class);
        // assert
        assertNull(result);
    }

    /**
     * Test when input string is empty
     * Expected: Returns null
     */
    @Test
    public void testFromJsonString_WhenInputEmpty() {
        // arrange
        String jsonString = "";
        // act
        TestDto result = GsonUtils.fromJsonString(jsonString, TestDto.class);
        // assert
        assertNull(result);
    }

    /**
     * Test when input string is blank (spaces only)
     * Expected: Returns null
     */
    @Test
    public void testFromJsonString_WhenInputBlank() {
        // arrange
        String jsonString = "   ";
        // act
        TestDto result = GsonUtils.fromJsonString(jsonString, TestDto.class);
        // assert
        assertNull(result);
    }

    /**
     * Test with valid JSON string for simple object
     * Expected: Returns correctly parsed object
     */
    @Test
    public void testFromJsonString_WhenValidJson() {
        // arrange
        String jsonString = "{\"name\":\"test\",\"value\":100}";
        // act
        TestDto result = GsonUtils.fromJsonString(jsonString, TestDto.class);
        // assert
        assertNotNull(result);
        assertEquals("test", result.getName());
        assertEquals(100, result.getValue());
    }

    /**
     * Test with malformed JSON string
     * Expected: Throws JsonSyntaxException
     */
    @Test
    public void testFromJsonString_WhenMalformedJson() {
        // arrange
        // missing closing brace
        String jsonString = "{\"name\":\"test\",\"value\":100";
        // act & assert
        assertThrows(com.google.gson.JsonSyntaxException.class, () -> GsonUtils.fromJsonString(jsonString, TestDto.class));
    }

    /**
     * Test with JSON string containing type mismatch
     * Expected: Throws JsonSyntaxException
     */
    @Test
    public void testFromJsonString_WhenTypeMismatch() {
        // arrange
        String jsonString = "{\"name\":123,\"value\":\"invalid\"}";
        // act & assert
        assertThrows(com.google.gson.JsonSyntaxException.class, () -> GsonUtils.fromJsonString(jsonString, TestDto.class));
    }

    /**
     * Test with complex nested object
     * Expected: Returns correctly parsed nested object
     */
    @Test
    public void testFromJsonString_WhenNestedObject() {
        // arrange
        String jsonString = "{\"name\":\"parent\",\"value\":100,\"child\":{\"name\":\"child\",\"value\":50}}";
        // act
        TestDtoWithNested result = GsonUtils.fromJsonString(jsonString, TestDtoWithNested.class);
        // assert
        assertNotNull(result);
        assertEquals("parent", result.getName());
        assertEquals(100, result.getValue());
        assertNotNull(result.getChild());
        assertEquals("child", result.getChild().getName());
        assertEquals(50, result.getChild().getValue());
    }
}
