package com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.scene;

import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.ProductBottomBarVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.block.quick.entrance.QuickEntranceBlockVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.block.trade.StandardTradeBlockVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.component.trade.button.BaseTradeButtonVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.enums.BottomBarStyleEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * @Author: guangyujie
 * @Date: 2025/3/16 14:15
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class StandardTradeBottomBarVO extends ProductBottomBarVO {

    /**
     * 左侧快捷入口区域
     */
    private QuickEntranceBlockVO leftBottomBar;

    /**
     * 右侧交易按钮区域
     */
    private StandardTradeBlockVO rightBottomBar;

    @Override
    public List<BaseTradeButtonVO> queryAllTradeButtons() {
        return Optional.ofNullable(rightBottomBar)
                .map(StandardTradeBlockVO::getButtonList)
                .orElse(new ArrayList<>());
    }

    @Override
    public int getBottomBarStyle() {
        return BottomBarStyleEnum.STANDARD_TRADE_STYLE.getCode();
    }

}
