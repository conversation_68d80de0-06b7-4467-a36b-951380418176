package com.sankuai.dzshoppingguide.product.detail.application.service.promo;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.utils.PromoHelper;
import com.sankuai.dzshoppingguide.product.detail.application.utils.context.DealCtxUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.promo.vo.DealBestPromoDetail;
import com.sankuai.dzshoppingguide.product.detail.spi.promo.vo.DealGift;
import com.sankuai.dzshoppingguide.product.detail.spi.promo.vo.PromoActivityInfoVO;
import com.sankuai.dzshoppingguide.product.detail.spi.promo.vo.PromoDetailModule;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.sankuai.dealuser.price.display.api.enums.PricePowerTagEnum.*;

/**
 * <AUTHOR>
 * @date 2024-07-03
 * @desc 优惠明&券模块
 */
@Slf4j
@Component
public class PromoDetailModuleBuilderService {

    /**
     * 时间价格力标签
     */
    public static final Set<Integer> TIME_PRICE_POWER_TAG_VALUES = Sets.newHashSet(
            LOWEST_PRICE_IN_RECENT_30_DAYS.getType(),
            LOWEST_PRICE_IN_RECENT_60_DAYS.getType(),
            LOWEST_PRICE_IN_RECENT_90_DAYS.getType(),
            LOWEST_PRICE_IN_RECENT_180_DAYS.getType(),
            LOWEST_PRICE_IN_RECENT_365_DAYS.getType()
    );

    /**
     * 丽人价格力标签排序
     */
    private static final List<Integer> BEAUTY_PRICE_POWER_TAG_SORT_ORDER = Lists.newArrayList(
            LOWEST_PRICE_IN_RECENT_365_DAYS.getType(),
            LOWEST_PRICE_IN_RECENT_180_DAYS.getType(),
            LOWEST_PRICE_IN_RECENT_90_DAYS.getType(),
            LOWEST_PRICE_IN_RECENT_60_DAYS.getType(),
            LOWEST_PRICE_IN_RECENT_30_DAYS.getType(),
            NETWORK_LOW_PRICE.getType()
    );

    /**
     * 足疗价格力排序标签
     */
    private static final List<Integer> FOOT_MASSAGE_PRICE_POWER_TAG_SORT_ORDER = Lists.newArrayList(
            NETWORK_LOW_PRICE.getType(),
            LOWEST_PRICE_IN_RADIUS_3KM_RANGE.getType(),
            LOWEST_PRICE_IN_CITY.getType(),
            LOWEST_PRICE_IN_DISTRICT.getType(),
            LOWEST_PRICE_IN_REGION.getType(),
            LOWEST_PRICE_IN_RECENT_365_DAYS.getType(),
            LOWEST_PRICE_IN_RECENT_180_DAYS.getType(),
            LOWEST_PRICE_IN_RECENT_90_DAYS.getType(),
            LOWEST_PRICE_IN_RECENT_60_DAYS.getType(),
            LOWEST_PRICE_IN_RECENT_30_DAYS.getType()
    );

    /**
     * 休娱价格力排序标签
     */
    public static final List<Integer> XIUYU_PRICE_POWER_TAG_SORT_ORDER = Lists.newArrayList(
            NETWORK_LOW_PRICE.getType(),
            LOWEST_PRICE_IN_RECENT_365_DAYS.getType(),
            LOWEST_PRICE_IN_RECENT_180_DAYS.getType(),
            LOWEST_PRICE_IN_RECENT_90_DAYS.getType(),
            LOWEST_PRICE_IN_RECENT_60_DAYS.getType(),
            LOWEST_PRICE_IN_RECENT_30_DAYS.getType()
    );

    /**
     * 展示的价格力标签列表
     */
    private static final Set<Integer> SHOW_PRICE_POWER_TAG_VALUES = Sets.newHashSet(
            LOWEST_PRICE_IN_RECENT_30_DAYS.getType(),
            LOWEST_PRICE_IN_RECENT_60_DAYS.getType(),
            LOWEST_PRICE_IN_RECENT_90_DAYS.getType(),
            LOWEST_PRICE_IN_RECENT_180_DAYS.getType(),
            LOWEST_PRICE_IN_RECENT_365_DAYS.getType(),
            LOWEST_PRICE_IN_RADIUS_3KM_RANGE.getType(),
            LOWEST_PRICE_IN_CITY.getType(),
            LOWEST_PRICE_IN_DISTRICT.getType(),
            LOWEST_PRICE_IN_REGION.getType(),
            NETWORK_LOW_PRICE.getType()
    );




    public List<String> buildPromoAbstractList(PromoDetailModule promoDetailModule, ClientTypeEnum clientTypeEnum, PriceDisplayDTO normalPrice, List<DealGift> dealGifts) {
        try {
            if (promoDetailModule == null) {
                return null;
            }
            List<String> promoAbstractListBest = new ArrayList<>();

            // 实验已全量 仅APP侧
            boolean couponAlleviate2ExpResult =  DealCtxUtils.judgeMainApp(clientTypeEnum);

            // 最佳优惠
            if (CollectionUtils.isNotEmpty(promoDetailModule.getBestPromoDetails())) {
                for (DealBestPromoDetail bestPromoDetail : promoDetailModule.getBestPromoDetails()) {
                    if (bestPromoDetail == null || StringUtils.isBlank(bestPromoDetail.getPromoName()) || StringUtils.equals(bestPromoDetail.getPromoTag(), "国家补贴")) {
                        continue;
                    }
                    // 优惠减负二期 强化会员渲染
                    if (bestPromoDetail.getPromoName().contains("会员") && couponAlleviate2ExpResult ) {
                        promoAbstractListBest.add(highlightMemberPromoName(bestPromoDetail.getPromoName()));
                    } else {
                        promoAbstractListBest.add(bestPromoDetail.getPromoName());
                    }
                }
            } else if (normalPrice != null) {
                // 兜底用 normalPrice
                List<PromoDTO> usedPromos = Optional.ofNullable(normalPrice.getUsedPromos()).orElse(Collections.emptyList());
                for (PromoDTO promoDTO : usedPromos) {
                    if (promoDTO.getIdentity() == null || promoDTO.getAmount() == null || StringUtils.isEmpty(promoDTO.getIdentity().getPromoTypeDesc())) {
                        continue;
                    }
                    promoAbstractListBest.add(promoDTO.getIdentity().getPromoTypeDesc());
                }
            }

            final List<String> promoOrder = couponAlleviate2ExpResult
                    ? Arrays.asList("会员", "秒杀", "补贴", "新客", "特惠", "券", "减", "团购优惠", "工作日", "人团", "返")
                    : Arrays.asList("会员", "补贴", "新客", "秒杀", "特惠", "券", "减", "团购优惠", "工作日", "人团", "返");

            // 最佳优惠排序
            List<String> sortedPromoAbstractListBest = promoAbstractListBest.stream()
                    .filter(StringUtils::isNotBlank)
                    .sorted((promo1, promo2) -> {
                        int index1 = getIndex(promo1, promoOrder);
                        int index2 = getIndex(promo2, promoOrder);
                        return Integer.compare(index1, index2);
                    })
                    .collect(Collectors.toList());
            // 活动
            List<String> activities = new ArrayList<>();
            if (promoDetailModule.getPromoActivityList() != null) {
                for (PromoActivityInfoVO activityInfoVO : promoDetailModule.getPromoActivityList()) {
                    if (activityInfoVO == null || StringUtils.isBlank(activityInfoVO.getShortText())) {
                        continue;
                    }
                    activities.add(PromoHelper.getTextFromJson(activityInfoVO.getShortText(),"text"));
                }
            }

            // 活动排序
            List<String> sortedActivities = activities.stream()
                    .filter(StringUtils::isNotBlank)
                    .sorted((promo1, promo2) -> {
                        int index1 = getIndex(promo1, promoOrder);
                        int index2 = getIndex(promo2, promoOrder);
                        return Integer.compare(index1, index2);
                    })
                    .collect(Collectors.toList());

            // 合并sortedPromoAbstractListBest和sortedActivities
            List<String> result = new ArrayList<>();
            // 插入赠品
            if (couponAlleviate2ExpResult) {
                // 优惠减负,跨界营销惊喜买赠拍平展示
                insert2ResultIfHasGiftAndAlleviate(result, dealGifts);
            } else {
                insert2ResultIfHasGift(result, dealGifts);
            }
            result.addAll(sortedPromoAbstractListBest);
            result.addAll(sortedActivities);
            return result;
        } catch (Exception e) {
            log.error("buildPromoAbstractList error,", e);
        }
        return null;
    }

    private int getIndex(String promoName, List<String> promoOrder) {
        if (StringUtils.isEmpty(promoName) || CollectionUtils.isEmpty(promoOrder)) {
            return Integer.MAX_VALUE;
        }
        // 正则表达式匹配严格格式的Unicode转义序列
        Pattern pattern = Pattern.compile("\\\\u[0-9a-fA-F]{4}");
        Matcher matcher = pattern.matcher(promoName);

        if (matcher.find()) {
            Map<String,String> decodePromoName = Maps.newHashMap();
            try {
                decodePromoName = JSONObject.parseObject(promoName, new TypeReference<Map<String, String>>() {});
            } catch (Exception e) {
                log.error("parse richText error,promoName:{}",promoName,e);
            }
            String target = decodePromoName.getOrDefault("text","");
            return promoOrder.stream().filter(target::contains).findFirst().map(promoOrder::indexOf).orElse(Integer.MAX_VALUE);
        } else {
            return promoOrder.stream().filter(promoName::contains).findFirst().map(promoOrder::indexOf).orElse(Integer.MAX_VALUE);
        }
    }

    private String highlightMemberPromoName(String promoName) {
        List<Map<String, Object>> encodeList = Lists.newArrayList();
        Map<String, Object> map = new HashMap<>();
        map.put("containercolor", "#FFEDDE");
        map.put("strikethrough", false);
        map.put("text", promoName);
        map.put("textcolor", "#8e3c13");
        map.put("textsize", 10);
        map.put("textstyle", "Bold");
        map.put("underline", false);
        map.put("customize", true);
        encodeList.add(map);
        return JSON.toJSONString(encodeList);
    }

    private void insert2ResultIfHasGiftAndAlleviate(List<String> result, List<DealGift> dealGifts) {
        if ( CollectionUtils.isEmpty(dealGifts)) {
            return;
        }
        for (DealGift dealGift : dealGifts) {
            if (StringUtils.isEmpty(dealGift.getTitle())) {
                continue;
            }
            if (StringUtils.isNotEmpty(dealGift.getCustomerActivityPrefix())) {
                result.add(dealGift.getCustomerActivityPrefix() + dealGift.getTitle());
            } else {
                result.add("赠" + dealGift.getTitle());
            }
        }
    }

    private void insert2ResultIfHasGift(List<String> result,  List<DealGift> dealGifts) {
        if (CollectionUtils.isEmpty(dealGifts)) {
            return;
        }
        List<Map<String, Object>> encodeList = Lists.newArrayList();
        for (DealGift dealGift : dealGifts) {
            if (StringUtils.isEmpty(dealGift.getTitle())) {
                continue;
            }
            Map<String, Object> map = new HashMap<>();
            map.put("containercolor", "#FF4B10CC");
            map.put("strikethrough", false);
            if (StringUtils.isNotEmpty(dealGift.getCustomerActivityPrefix())) {
                map.put("text", new StringBuilder(dealGift.getCustomerActivityPrefix()).append(dealGift.getTitle()).toString());
            } else {
                map.put("text", new StringBuilder("赠").append(dealGift.getTitle()).toString());
            }
            map.put("textcolor", "#FFFFFF");
            map.put("textsize", 10);
            map.put("textstyle", "Bold");
            map.put("underline", false);
            map.put("customize", true);
            encodeList.add(map);
        }
        if (CollectionUtils.isNotEmpty(encodeList)) {
            result.add(JsonCodec.encode(encodeList));
        }
    }



    @Getter
    private enum ComparePriceIndustryEnum {
        DEFAULT(1),
        MEDICAL(2),
        ;
        final int code;

        ComparePriceIndustryEnum(int code) {
            this.code = code;
        }
    }
}
