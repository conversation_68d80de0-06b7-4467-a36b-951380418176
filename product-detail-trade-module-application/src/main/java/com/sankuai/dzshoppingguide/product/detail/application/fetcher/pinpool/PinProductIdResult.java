package com.sankuai.dzshoppingguide.product.detail.application.fetcher.pinpool;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * <AUTHOR>
 * @create 2025/3/10 15:35
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PinProductIdResult extends FetcherReturnValueDTO {
    private Map<Long, Long> dealGroupId2PinProductIdMap;
}
