package com.sankuai.dzshoppingguide.product.detail.application.fetcher.bookingtime;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import com.sankuai.spt.statequery.api.dto.BaseStateQueryResultDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DealBookingTimeReturnValue extends FetcherReturnValueDTO {

    private BaseStateQueryResultDTO baseStateQueryResultDTO;
}
