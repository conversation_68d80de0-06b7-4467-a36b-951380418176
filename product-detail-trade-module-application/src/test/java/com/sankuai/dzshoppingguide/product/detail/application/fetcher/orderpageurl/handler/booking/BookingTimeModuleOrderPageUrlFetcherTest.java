package com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.handler.booking;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import java.util.HashMap;
import java.util.Map;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class BookingTimeModuleOrderPageUrlFetcherTest {

    /**
     * Test that buildCustomExtParams() returns a non-null HashMap instance
     */
    @Test
    void testBuildCustomExtParamsReturnsNonNullMap() {
        // arrange
        BookingTimeModuleOrderPageUrlFetcher fetcher = new BookingTimeModuleOrderPageUrlFetcher();
        // act
        Map<String, String> result = fetcher.buildCustomExtParams();
        // assert
        assertNotNull(result, "Returned map should not be null");
    }

    /**
     * Test that buildCustomExtParams() returns an empty map
     */
    @Test
    void testBuildCustomExtParamsReturnsEmptyMap() {
        // arrange
        BookingTimeModuleOrderPageUrlFetcher fetcher = new BookingTimeModuleOrderPageUrlFetcher();
        // act
        Map<String, String> result = fetcher.buildCustomExtParams();
        // assert
        assertTrue(result.isEmpty(), "Returned map should be empty");
    }

    /**
     * Test that buildCustomExtParams() returns a new instance each time
     */
    @Test
    void testBuildCustomExtParamsReturnsNewInstanceEachCall() {
        // arrange
        BookingTimeModuleOrderPageUrlFetcher fetcher = new BookingTimeModuleOrderPageUrlFetcher();
        // act
        Map<String, String> firstCall = fetcher.buildCustomExtParams();
        Map<String, String> secondCall = fetcher.buildCustomExtParams();
        // assert
        assertNotSame(firstCall, secondCall, "Each call should return a new instance");
    }

    /**
     * Test that buildCustomExtParams() returns a HashMap instance
     */
    @Test
    void testBuildCustomExtParamsReturnsHashMapInstance() {
        // arrange
        BookingTimeModuleOrderPageUrlFetcher fetcher = new BookingTimeModuleOrderPageUrlFetcher();
        // act
        Map<String, String> result = fetcher.buildCustomExtParams();
        // assert
        assertEquals(HashMap.class, result.getClass(), "Should return HashMap instance");
    }
}
