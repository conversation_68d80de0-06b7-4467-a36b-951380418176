package com.sankuai.dzshoppingguide.product.detail.application.utils;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import com.dianping.vc.enums.VCClientTypeEnum;
import com.dianping.vc.enums.VCPlatformEnum;
import com.google.common.collect.Lists;
import com.sankuai.dealuser.price.display.api.enums.RequestSceneEnum;
import com.sankuai.dztheme.massagebook.theme.req.ReserveQueryRequest;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDisplayShopDTO;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * Test cases for AtomServiceUtils.getProductId2ShopIdMap method
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("AtomServiceUtils BuildParams Test")
public class AtomServiceUtilsTest {

    private static final String EXPECTED_PLAN_ID = "11900007";

    private static final double VALID_LAT = 39.9087;

    private static final double VALID_LNG = 116.3975;

    private static final long VALID_USER_ID = 12345L;

    /**
     * Test when input list is empty
     */
    @Test
    public void testGetProductId2ShopIdMap_EmptyList() {
        // arrange
        List<DealGroupDTO> emptyList = new ArrayList<>();
        // act
        Map<Long, Long> result = AtomServiceUtils.getProductId2ShopIdMap(emptyList, true);
        // assert
        assertNull(result, "Result should be null for empty input list");
    }

    /**
     * Test when input list contains items but all have null displayShopInfo for MT
     */
    @Test
    public void testGetProductId2ShopIdMap_NullDisplayShopInfoMT() {
        // arrange
        DealGroupDTO dto = spy(DealGroupDTO.class);
        when(dto.getDisplayShopInfo()).thenReturn(null);
        List<DealGroupDTO> list = Arrays.asList(dto);
        // act
        Map<Long, Long> result = AtomServiceUtils.getProductId2ShopIdMap(list, true);
        // assert
        assertNull(result, "Result should be null when displayShopInfo is null");
    }

    /**
     * Test when displayShopInfo exists but shop IDs list is empty for MT
     */
    @Test
    public void testGetProductId2ShopIdMap_EmptyShopIdsMT() {
        // arrange
        DealGroupDTO dto = spy(DealGroupDTO.class);
        DealGroupDisplayShopDTO displayShopDTO = spy(DealGroupDisplayShopDTO.class);
        when(dto.getDisplayShopInfo()).thenReturn(displayShopDTO);
        when(displayShopDTO.getMtDisplayShopIds()).thenReturn(new ArrayList<>());
        List<DealGroupDTO> list = Arrays.asList(dto);
        // act
        Map<Long, Long> result = AtomServiceUtils.getProductId2ShopIdMap(list, true);
        // assert
        assertNull(result, "Result should be null when shop IDs list is empty");
    }

    /**
     * Test successful case for MT with valid shop info
     */
    @Test
    public void testGetProductId2ShopIdMap_ValidMTShopInfo() {
        // arrange
        DealGroupDTO dto = spy(DealGroupDTO.class);
        DealGroupDisplayShopDTO displayShopDTO = spy(DealGroupDisplayShopDTO.class);
        when(dto.getBizProductId()).thenReturn(1L);
        when(dto.getDisplayShopInfo()).thenReturn(displayShopDTO);
        when(displayShopDTO.getMtDisplayShopIds()).thenReturn(Arrays.asList(100L));
        List<DealGroupDTO> list = Arrays.asList(dto);
        // act
        Map<Long, Long> result = AtomServiceUtils.getProductId2ShopIdMap(list, true);
        // assert
        assertNotNull(result, "Result should not be null");
        assertEquals(1, result.size(), "Map should contain one entry");
        assertEquals(100L, result.get(1L), "Shop ID should match");
    }

    /**
     * Test successful case for DP with valid shop info
     */
    @Test
    public void testGetProductId2ShopIdMap_ValidDPShopInfo() {
        // arrange
        DealGroupDTO dto = spy(DealGroupDTO.class);
        DealGroupDisplayShopDTO displayShopDTO = spy(DealGroupDisplayShopDTO.class);
        when(dto.getBizProductId()).thenReturn(1L);
        when(dto.getDisplayShopInfo()).thenReturn(displayShopDTO);
        when(displayShopDTO.getDpDisplayShopIds()).thenReturn(Arrays.asList(200L));
        List<DealGroupDTO> list = Arrays.asList(dto);
        // act
        Map<Long, Long> result = AtomServiceUtils.getProductId2ShopIdMap(list, false);
        // assert
        assertNotNull(result, "Result should not be null");
        assertEquals(1, result.size(), "Map should contain one entry");
        assertEquals(200L, result.get(1L), "Shop ID should match");
    }

    /**
     * Test with multiple valid entries for MT
     */
    @Test
    public void testGetProductId2ShopIdMap_MultipleValidEntriesMT() {
        // arrange
        DealGroupDTO dto1 = spy(DealGroupDTO.class);
        DealGroupDisplayShopDTO displayShopDTO1 = spy(DealGroupDisplayShopDTO.class);
        when(dto1.getBizProductId()).thenReturn(1L);
        when(dto1.getDisplayShopInfo()).thenReturn(displayShopDTO1);
        when(displayShopDTO1.getMtDisplayShopIds()).thenReturn(Arrays.asList(100L));
        DealGroupDTO dto2 = spy(DealGroupDTO.class);
        DealGroupDisplayShopDTO displayShopDTO2 = spy(DealGroupDisplayShopDTO.class);
        when(dto2.getBizProductId()).thenReturn(2L);
        when(dto2.getDisplayShopInfo()).thenReturn(displayShopDTO2);
        when(displayShopDTO2.getMtDisplayShopIds()).thenReturn(Arrays.asList(200L));
        List<DealGroupDTO> list = Arrays.asList(dto1, dto2);
        // act
        Map<Long, Long> result = AtomServiceUtils.getProductId2ShopIdMap(list, true);
        // assert
        assertNotNull(result, "Result should not be null");
        assertEquals(2, result.size(), "Map should contain two entries");
        assertEquals(100L, result.get(1L), "First shop ID should match");
        assertEquals(200L, result.get(2L), "Second shop ID should match");
    }

    /**
     * Test with mixed valid and invalid entries for MT
     */
    @Test
    public void testGetProductId2ShopIdMap_MixedValidInvalidEntriesMT() {
        // arrange
        DealGroupDTO validDto = spy(DealGroupDTO.class);
        DealGroupDisplayShopDTO validDisplayShop = spy(DealGroupDisplayShopDTO.class);
        when(validDto.getBizProductId()).thenReturn(1L);
        when(validDto.getDisplayShopInfo()).thenReturn(validDisplayShop);
        when(validDisplayShop.getMtDisplayShopIds()).thenReturn(Arrays.asList(100L));
        DealGroupDTO invalidDto = spy(DealGroupDTO.class);
        when(invalidDto.getDisplayShopInfo()).thenReturn(null);
        List<DealGroupDTO> list = Arrays.asList(validDto, invalidDto);
        // act
        Map<Long, Long> result = AtomServiceUtils.getProductId2ShopIdMap(list, true);
        // assert
        assertNotNull(result, "Result should not be null");
        assertEquals(1, result.size(), "Map should contain one entry");
        assertEquals(100L, result.get(1L), "Valid shop ID should match");
        assertNull(result.get(2L), "Invalid entry should not be present");
    }

    @Test
    public void testBuildReserveQueryRequestWithMtPlatform() throws Throwable {
        List<Integer> productIds = Arrays.asList(1, 2, 3);
        double lat = 39.9042;
        double lng = 116.4074;
        long userId = 12345L;
        Map<Long, Long> productIdShopIdMap = new HashMap<>();
        productIdShopIdMap.put(1L, 100L);
        productIdShopIdMap.put(2L, 200L);
        productIdShopIdMap.put(3L, 300L);
        boolean isMt = true;
        ReserveQueryRequest result = AtomServiceUtils.buildReserveQueryRequest(productIds, lat, lng, userId, productIdShopIdMap, isMt);
        assertNotNull(result);
        assertEquals(EXPECTED_PLAN_ID, result.getPlanId());
        assertEquals(productIds, result.getProductIds());
        Map<String, Object> extParams = result.getExtParams();
        assertNotNull(extParams);
        assertEquals(lat, extParams.get("lat"));
        assertEquals(lng, extParams.get("lng"));
        assertEquals(400200, extParams.get("scene"));
        assertEquals(userId, extParams.get("userId"));
        assertEquals(2, extParams.get("platform"));
        assertEquals(200, extParams.get("uaCode"));
        assertEquals(400200, extParams.get("promoScene"));
        assertNotNull(extParams.get("productId2ShopIdLMap"));
    }

    @Test
    public void testBuildReserveQueryRequestWithDpPlatform() throws Throwable {
        List<Integer> productIds = Arrays.asList(1, 2, 3);
        double lat = 39.9042;
        double lng = 116.4074;
        long userId = 12345L;
        Map<Long, Long> productIdShopIdMap = new HashMap<>();
        productIdShopIdMap.put(1L, 100L);
        productIdShopIdMap.put(2L, 200L);
        productIdShopIdMap.put(3L, 300L);
        boolean isMt = false;
        ReserveQueryRequest result = AtomServiceUtils.buildReserveQueryRequest(productIds, lat, lng, userId, productIdShopIdMap, isMt);
        assertNotNull(result);
        assertEquals(EXPECTED_PLAN_ID, result.getPlanId());
        assertEquals(productIds, result.getProductIds());
        Map<String, Object> extParams = result.getExtParams();
        assertNotNull(extParams);
        assertEquals(lat, extParams.get("lat"));
        assertEquals(lng, extParams.get("lng"));
        assertEquals(400200, extParams.get("scene"));
        assertEquals(userId, extParams.get("userId"));
        assertEquals(1, extParams.get("platform"));
        assertEquals(100, extParams.get("uaCode"));
        assertEquals(400200, extParams.get("promoScene"));
        assertNotNull(extParams.get("productId2ShopIdLMap"));
    }

    @Test
    public void testBuildReserveQueryRequestWithEmptyProductIds() throws Throwable {
        List<Integer> productIds = Collections.emptyList();
        double lat = 39.9042;
        double lng = 116.4074;
        long userId = 12345L;
        Map<Long, Long> productIdShopIdMap = new HashMap<>();
        boolean isMt = true;
        ReserveQueryRequest result = AtomServiceUtils.buildReserveQueryRequest(productIds, lat, lng, userId, productIdShopIdMap, isMt);
        assertNotNull(result);
        assertEquals(EXPECTED_PLAN_ID, result.getPlanId());
        assertTrue(result.getProductIds().isEmpty());
        Map<String, Object> extParams = result.getExtParams();
        assertNotNull(extParams);
        assertTrue(((Map<?, ?>) extParams.get("productId2ShopIdLMap")).isEmpty());
    }

    @Test
    public void testBuildReserveQueryRequestWithNullProductIdShopIdMap() throws Throwable {
        List<Integer> productIds = Arrays.asList(1, 2, 3);
        double lat = 39.9042;
        double lng = 116.4074;
        long userId = 12345L;
        boolean isMt = true;
        // Expect NullPointerException when productIdShopIdMap is null
        assertThrows(NullPointerException.class, () -> {
            AtomServiceUtils.buildReserveQueryRequest(productIds, lat, lng, userId, null, isMt);
        });
    }

    @Test
    public void testBuildReserveQueryRequestWithExtremeLatLng() throws Throwable {
        List<Integer> productIds = Arrays.asList(1, 2, 3);
        double lat = 90.0;
        double lng = 180.0;
        long userId = 12345L;
        Map<Long, Long> productIdShopIdMap = new HashMap<>();
        boolean isMt = true;
        ReserveQueryRequest result = AtomServiceUtils.buildReserveQueryRequest(productIds, lat, lng, userId, productIdShopIdMap, isMt);
        assertNotNull(result);
        Map<String, Object> extParams = result.getExtParams();
        assertEquals(lat, extParams.get("lat"));
        assertEquals(lng, extParams.get("lng"));
    }

    @Test
    public void testBuildReserveQueryRequestWithExtremeUserId() throws Throwable {
        List<Integer> productIds = Arrays.asList(1, 2, 3);
        double lat = 39.9042;
        double lng = 116.4074;
        long userId = Long.MAX_VALUE;
        Map<Long, Long> productIdShopIdMap = new HashMap<>();
        boolean isMt = true;
        ReserveQueryRequest result = AtomServiceUtils.buildReserveQueryRequest(productIds, lat, lng, userId, productIdShopIdMap, isMt);
        assertNotNull(result);
        Map<String, Object> extParams = result.getExtParams();
        assertEquals(userId, extParams.get("userId"));
    }

    /**
     * Test building parameters for MT platform with valid inputs
     */
    @Test
    @DisplayName("Should build parameters correctly for MT platform")
    void testBuildParamsMtPlatform() throws Throwable {
        // arrange
        List<Integer> productIds = Lists.newArrayList(1001, 1002);
        Map<Long, Long> productIdShopIdMap = new HashMap<>();
        productIdShopIdMap.put(1001L, 5001L);
        productIdShopIdMap.put(1002L, 5002L);
        boolean isMt = true;
        // act
        Map<String, Object> result = AtomServiceUtils.buildParams(VALID_LAT, VALID_LNG, VALID_USER_ID, productIds, productIdShopIdMap, isMt);
        // assert
        assertNotNull(result, "Result map should not be null");
        assertEquals(VALID_LAT, result.get("lat"), "Latitude should match input");
        assertEquals(VALID_LNG, result.get("lng"), "Longitude should match input");
        assertEquals(VALID_USER_ID, result.get("userId"), "User ID should match input");
        assertEquals(VCPlatformEnum.MT.getType(), result.get("platform"), "Platform should be MT");
        assertEquals(VCClientTypeEnum.MT_APP.getCode(), result.get("uaCode"), "UA code should be MT_APP");
        assertEquals(RequestSceneEnum.PROMO_DETAIL_DESC_WithDealPromo.getScene(), result.get("scene"), "Scene should match");
        assertEquals(RequestSceneEnum.PROMO_DETAIL_DESC_WithDealPromo.getScene(), result.get("promoScene"), "Promo scene should match");
        assertNotNull(result.get("selectDates"), "Select dates should not be null");
        @SuppressWarnings("unchecked")
        Map<Integer, Long> productIdMap = (Map<Integer, Long>) result.get("productId2ShopIdLMap");
        assertNotNull(productIdMap, "Product ID map should not be null");
        assertEquals(5001L, productIdMap.get(1001), "Shop ID mapping should match for product 1001");
        assertEquals(5002L, productIdMap.get(1002), "Shop ID mapping should match for product 1002");
    }

    /**
     * Test building parameters for DP platform with valid inputs
     */
    @Test
    @DisplayName("Should build parameters correctly for DP platform")
    void testBuildParamsDpPlatform() throws Throwable {
        // arrange
        List<Integer> productIds = Lists.newArrayList(1001, 1002);
        Map<Long, Long> productIdShopIdMap = new HashMap<>();
        productIdShopIdMap.put(1001L, 5001L);
        productIdShopIdMap.put(1002L, 5002L);
        boolean isMt = false;
        // act
        Map<String, Object> result = AtomServiceUtils.buildParams(VALID_LAT, VALID_LNG, VALID_USER_ID, productIds, productIdShopIdMap, isMt);
        // assert
        assertNotNull(result, "Result map should not be null");
        assertEquals(VCPlatformEnum.DP.getType(), result.get("platform"), "Platform should be DP");
        assertEquals(VCClientTypeEnum.DP_APP.getCode(), result.get("uaCode"), "UA code should be DP_APP");
        @SuppressWarnings("unchecked")
        Map<Integer, Long> productIdMap = (Map<Integer, Long>) result.get("productId2ShopIdLMap");
        assertNotNull(productIdMap, "Product ID map should not be null");
        assertEquals(5001L, productIdMap.get(1001), "Shop ID mapping should match for product 1001");
        assertEquals(5002L, productIdMap.get(1002), "Shop ID mapping should match for product 1002");
    }

    /**
     * Test building parameters with empty product IDs list
     */
    @Test
    @DisplayName("Should handle empty product IDs list")
    void testBuildParamsEmptyProductIds() throws Throwable {
        // arrange
        List<Integer> productIds = new ArrayList<>();
        Map<Long, Long> productIdShopIdMap = new HashMap<>();
        boolean isMt = true;
        // act
        Map<String, Object> result = AtomServiceUtils.buildParams(VALID_LAT, VALID_LNG, VALID_USER_ID, productIds, productIdShopIdMap, isMt);
        // assert
        assertNotNull(result, "Result map should not be null");
        @SuppressWarnings("unchecked")
        Map<Integer, Long> productIdMap = (Map<Integer, Long>) result.get("productId2ShopIdLMap");
        assertNotNull(productIdMap, "Product ID map should not be null");
        assertTrue(productIdMap.isEmpty(), "Product ID map should be empty");
    }

    /**
     * Test building parameters with null productIdShopIdMap
     */
    @Test
    @DisplayName("Should throw NullPointerException when productIdShopIdMap is null")
    void testBuildParamsNullProductIdShopIdMap() throws Throwable {
        // arrange
        List<Integer> productIds = Lists.newArrayList(1001, 1002);
        boolean isMt = true;
        // act & assert
        assertThrows(NullPointerException.class, () -> AtomServiceUtils.buildParams(VALID_LAT, VALID_LNG, VALID_USER_ID, productIds, null, isMt), "Should throw NullPointerException when productIdShopIdMap is null");
    }

    /**
     * Test building parameters with boundary coordinate values
     */
    @Test
    @DisplayName("Should handle boundary coordinate values")
    void testBuildParamsBoundaryCoordinates() throws Throwable {
        // arrange
        double maxLat = 90.0;
        double maxLng = 180.0;
        long maxUserId = Long.MAX_VALUE;
        List<Integer> productIds = Lists.newArrayList(Integer.MAX_VALUE);
        Map<Long, Long> productIdShopIdMap = new HashMap<>();
        productIdShopIdMap.put((long) Integer.MAX_VALUE, Long.MAX_VALUE);
        boolean isMt = true;
        // act
        Map<String, Object> result = AtomServiceUtils.buildParams(maxLat, maxLng, maxUserId, productIds, productIdShopIdMap, isMt);
        // assert
        assertNotNull(result, "Result map should not be null");
        assertEquals(maxLat, result.get("lat"), "Maximum latitude should be handled");
        assertEquals(maxLng, result.get("lng"), "Maximum longitude should be handled");
        assertEquals(maxUserId, result.get("userId"), "Maximum user ID should be handled");
        @SuppressWarnings("unchecked")
        Map<Integer, Long> productIdMap = (Map<Integer, Long>) result.get("productId2ShopIdLMap");
        assertNotNull(productIdMap, "Product ID map should not be null");
        assertEquals(Long.MAX_VALUE, productIdMap.get(Integer.MAX_VALUE), "Maximum shop ID should be handled");
    }

    /**
     * Test building parameters with null product IDs list
     */
    @Test
    @DisplayName("Should throw NullPointerException when product IDs list is null")
    void testBuildParamsNullProductIds() throws Throwable {
        // arrange
        Map<Long, Long> productIdShopIdMap = new HashMap<>();
        boolean isMt = true;
        // act & assert
        assertThrows(NullPointerException.class, () -> AtomServiceUtils.buildParams(VALID_LAT, VALID_LNG, VALID_USER_ID, null, productIdShopIdMap, isMt), "Should throw NullPointerException when product IDs list is null");
    }
}
