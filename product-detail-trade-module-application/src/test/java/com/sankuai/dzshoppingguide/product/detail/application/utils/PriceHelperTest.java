package com.sankuai.dzshoppingguide.product.detail.application.utils;

import static org.junit.Assert.assertEquals;
import java.math.BigDecimal;
import java.util.HashSet;
import java.util.Set;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class PriceHelperTest {

    /**
     * 测试 format 方法，当 price 是整数时
     */
    @Test
    public void testFormatInteger() throws Throwable {
        // arrange
        BigDecimal price = new BigDecimal(10);
        // act
        String result = PriceHelper.format(price);
        // assert
        assertEquals("10", result);
    }

    /**
     * 测试 format 方法，当 price 不是整数时
     */
    @Test
    public void testFormatNotInteger() throws Throwable {
        // arrange
        BigDecimal price = new BigDecimal(10.5);
        // act
        String result = PriceHelper.format(price);
        // assert
        assertEquals("10.5", result);
    }

    /**
     * 测试价格字符串为空的情况
     */
    @Test
    public void testHidePriceWithQuestionMarkPriceIsNull() throws Throwable {
        String price = null;
        Set<Integer> posSet = new HashSet<>();
        String result = PriceHelper.hidePriceWithQuestionMark(price, posSet);
        assertEquals(price, result);
    }

    /**
     * 测试位置集合为空的情况
     */
    @Test
    public void testHidePriceWithQuestionMarkPosSetIsNull() throws Throwable {
        String price = "123.45";
        Set<Integer> posSet = null;
        String result = PriceHelper.hidePriceWithQuestionMark(price, posSet);
        assertEquals(price, result);
    }

    /**
     * 测试价格字符串无法转换为double类型的情况
     */
    @Test
    public void testHidePriceWithQuestionMarkPriceIsNotNumber() throws Throwable {
        String price = "abc";
        Set<Integer> posSet = new HashSet<>();
        String result = PriceHelper.hidePriceWithQuestionMark(price, posSet);
        assertEquals(price, result);
    }

    /**
     * 测试价格字符串中的数字小于10的情况
     */
    @Test
    public void testHidePriceWithQuestionMarkPriceLessThan10() throws Throwable {
        String price = "9.99";
        Set<Integer> posSet = new HashSet<>();
        String result = PriceHelper.hidePriceWithQuestionMark(price, posSet);
        // Corrected expectation based on method logic
        assertEquals(price, result);
    }

    /**
     * 测试价格字符串中的数字大于等于10，且位置集合为空的情况
     */
    @Test
    public void testHidePriceWithQuestionMarkPriceGreaterThan10AndPosSetIsNull() throws Throwable {
        String price = "10.00";
        Set<Integer> posSet = null;
        String result = PriceHelper.hidePriceWithQuestionMark(price, posSet);
        assertEquals(price, result);
    }

    /**
     * 测试价格字符串中的数字大于等于10，且位置集合不为空，但位置集合中的位置不在价格字符串中的情况
     */
    @Test
    public void testHidePriceWithQuestionMarkPriceGreaterThan10AndPosSetIsNotEmptyButNotInPrice() throws Throwable {
        String price = "10.00";
        Set<Integer> posSet = new HashSet<>();
        posSet.add(10);
        String result = PriceHelper.hidePriceWithQuestionMark(price, posSet);
        assertEquals(price, result);
    }

    /**
     * 测试价格字符串中的数字大于等于10，且位置集合不为空，且位置集合中的位置在价格字符串中的情况
     */
    @Test
    public void testHidePriceWithQuestionMarkPriceGreaterThan10AndPosSetIsNotEmptyAndInPrice() throws Throwable {
        String price = "10.00";
        Set<Integer> posSet = new HashSet<>();
        posSet.add(1);
        String result = PriceHelper.hidePriceWithQuestionMark(price, posSet);
        // Corrected expectation based on method logic
        assertEquals("?0.00", result);
    }
}
