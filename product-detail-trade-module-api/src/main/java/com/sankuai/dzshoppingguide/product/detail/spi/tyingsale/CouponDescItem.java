package com.sankuai.dzshoppingguide.product.detail.spi.tyingsale;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import lombok.Data;

import java.io.Serializable;

@MobileDo(id = 0xae78)
@Data
public class CouponDescItem implements Serializable {
    /**
     * 团购id
     */
    @MobileField(key = 0x9a1c)
    private String dealGroupId;

    /**
     * 商家券：券批次ID；平台券：券码ID，多个按照逗号隔开
     */
    @MobileField(key = 0xc7b5)
    private String unifiedcoupongroupids;

    /**
     * 加密抵用券ID
     */
    @MobileField(key = 0x8625)
    private String encryptCouponGroupId;

    /**
     * 已领取Icon url
     */
    @MobileField(key = 0xd9b8)
    private String issuedIconUrl;

    /**
     * 按钮
     */
    @MobileField(key = 0x8ad0)
    private ComBtn button;

    /**
     * 领券状态 0：可领取 1：已领取
     */
    @MobileField(key = 0x53f)
    private int status;

    /**
     * 抵用券金额描述
     */
    @MobileField(key = 0x7c03)
    private String amountDesc;

    /**
     * 抵用券金额
     */
    @MobileField(key = 0xfbe2)
    private String amount;

    /**
     * e.g. 领取后8天有效
     */
    @MobileField(key = 0xfebf)
    private String desc;

    /**
     * e.g. 限【团购】指定商品
     */
    @MobileField(key = 0x24cc)
    private String title;

    /**
     * 抵用券id
     */
    @MobileField(key = 0x2488)
    private int couponGroupId;

}
