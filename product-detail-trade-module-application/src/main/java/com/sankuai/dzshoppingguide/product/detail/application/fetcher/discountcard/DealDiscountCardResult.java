package com.sankuai.dzshoppingguide.product.detail.application.fetcher.discountcard;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import com.sankuai.dzcard.navigation.api.dto.QueryCardInfoDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/3/3 12:40
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DealDiscountCardResult extends FetcherReturnValueDTO {
    private QueryCardInfoDTO queryCardInfoDTO;
}
