package com.sankuai.dzshoppingguide.product.detail.application.fetcher.tying;

import com.alibaba.fastjson.JSONObject;
import com.dianping.martgeneral.recommend.api.dto.RecommendDTO;
import com.google.common.collect.Lists;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.BindingSourceEnum;
import com.sankuai.mppack.product.client.query.model.TyingCombineItem;
import com.sankuai.mppack.product.client.query.request.TyingBlackListQueryRequest;
import com.sankuai.mppack.product.client.query.response.TyingBlackListResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Fetcher(previousLayerDependencies = {
        TyingRecallFetcher.class,
        ShopIdMapperFetcher.class,
}
)
@Slf4j
public class BlackListFilterFetcher extends NormalFetcherContext<TyingIdResult> {

    private static final int MODULE_LIMIT = 10;
    public static final String MAIN_DEAL_KEY = "productId_a";
    public static final String BINDING_DEAL_KEY = "productId_b";

    @Autowired
    private CompositeAtomService compositeAtomService;

    @Override
    protected CompletableFuture<TyingIdResult> doFetch() throws Exception {
        TyingRecallResult tyingRecallResult = getDependencyResult(TyingRecallFetcher.class);
        if (tyingRecallResult == null) {
            return CompletableFuture.completedFuture(null);
        }
        List<CombinationDealInfo> combinationAfterMerge = getCombination(tyingRecallResult);
        if (CollectionUtils.isEmpty(combinationAfterMerge)) {
            return CompletableFuture.completedFuture(null);
        }
        List<CombinationDealInfo> combinationDealInfos = combinationAfterMerge.subList(0, Math.min(MODULE_LIMIT, combinationAfterMerge.size()));

        //搭售黑名单过滤
        ShopIdMapper shopIdMapper = getDependencyResult(ShopIdMapperFetcher.class);
        Long mtShopId = shopIdMapper.getMtBestShopId();
        CompletableFuture<TyingBlackListResponse> response = compositeAtomService.batchQueryTyingBlackList(buildRequest(combinationDealInfos, mtShopId));

        return response.thenApply(r -> buildTyingIdResult(r, combinationDealInfos));
    }

    private TyingIdResult buildTyingIdResult(TyingBlackListResponse blackListResponse, List<CombinationDealInfo> combinationDealInfos) {
        TyingIdResult tyingIdResult = new TyingIdResult();
        if (blackListResponse == null || MapUtils.isEmpty(blackListResponse.getData())) {
            tyingIdResult.setCombinationDealInfoList(combinationDealInfos);
            return tyingIdResult;
        }
        List<CombinationDealInfo> finalCombinationDealInfos = combinationDealInfos.stream()
                .filter(combinationDealInfo -> !blackListResponse.getData().getOrDefault(combinationDealInfo.getMainDealId() + "_" + combinationDealInfo.getBindingDealId(), false))
                .collect(Collectors.toList());
        tyingIdResult.setCombinationDealInfoList(finalCombinationDealInfos);
        List<Integer> allDealGroupIds = finalCombinationDealInfos.stream().map(CombinationDealInfo::getBindingDealId).collect(Collectors.toList());
        allDealGroupIds.add(finalCombinationDealInfos.get(0).getMainDealId());
        tyingIdResult.setMainDealGroupId(finalCombinationDealInfos.get(0).getMainDealId());
        tyingIdResult.setAllDealGroupIds(allDealGroupIds);
        tyingIdResult.setBindingDealGroupIds(finalCombinationDealInfos.stream().map(CombinationDealInfo::getBindingDealId).collect(Collectors.toList()));
        return tyingIdResult;
    }

    private TyingBlackListQueryRequest buildRequest(List<CombinationDealInfo> partition, Long mtShopId) {
        // 构建请求逻辑
        TyingBlackListQueryRequest request = new TyingBlackListQueryRequest();
        //GENERAL_MAIN(2,"综主渠道")
        request.setChannel(2);
        List<TyingCombineItem> tyingCombineItemList = new ArrayList<>();
        partition.forEach(combinationDealInfo -> {
            TyingCombineItem tyingCombineItem = new TyingCombineItem();
            String uniqueKey = combinationDealInfo.getMainDealId() + "_" + combinationDealInfo.getBindingDealId();
            tyingCombineItem.setUniqueKey(uniqueKey);
            tyingCombineItem.setMainPoiId(mtShopId);
            tyingCombineItem.setMainProductId(Long.valueOf(combinationDealInfo.getMainDealId()));
            tyingCombineItem.setSecondPoiId(mtShopId);
            tyingCombineItem.setSecondProductId(Long.valueOf(combinationDealInfo.getBindingDealId()));
            tyingCombineItemList.add(tyingCombineItem);
        });
        request.setTyingCombineItemList(tyingCombineItemList);
        return request;
    }

    private List<CombinationDealInfo> getCombination(TyingRecallResult tyingRecallResult) {
        // 1.获取运营品
        String recommendJson = tyingRecallResult.getPlayResponse() != null ? tyingRecallResult.getPlayResponse().getResult() : "";
        List<CombinationDealInfo> combinationFromOperation = convertToCombinationDealInfo(recommendJson);

        // 2.获取算法品
        List<RecommendDTO> recommendDTOS = tyingRecallResult.getRecommendResponse() != null && tyingRecallResult.getRecommendResponse().getResult() != null ? tyingRecallResult.getRecommendResponse().getResult().getSortedResult()  : Lists.newArrayList();
        List<CombinationDealInfo> combinationFromAlgorithm = convertToCombinationDealInfo(recommendDTOS);

        // 3.merge（运营品优先+去重）
        if (CollectionUtils.isEmpty(combinationFromOperation)) {
            return combinationFromAlgorithm;
        }
        if (CollectionUtils.isEmpty(combinationFromAlgorithm)) {
            return combinationFromOperation;
        }

        combinationFromOperation.addAll(combinationFromAlgorithm);
        return removeDuplicates(combinationFromOperation);
    }

    // 去重，LinkedHashMap可保证插入顺序
    private List<CombinationDealInfo> removeDuplicates(List<CombinationDealInfo> combinationFromOperation) {
        Map<Integer, CombinationDealInfo> map = new LinkedHashMap<>();

        for (CombinationDealInfo info : combinationFromOperation) {
            map.putIfAbsent(info.getBindingDealId(), info);
        }

        return new ArrayList<>(map.values());
    }

    public List<CombinationDealInfo> convertToCombinationDealInfo(String json){
        List<CombinationDealInfo> combinationDealInfos = Lists.newArrayList();
        if (StringUtils.isBlank(json)){
            return combinationDealInfos;
        }
        try{
            JSONObject jsonObject = JSONObject.parseObject(json);
            if (Objects.isNull(jsonObject) || Objects.isNull(jsonObject.get("sortedResult"))){
                return combinationDealInfos;
            }
            List<Map<String, String>> sortedResult = (List<Map<String, String>>) jsonObject.get("sortedResult");
            sortedResult.forEach(e->{
                CombinationDealInfo combinationDealInfo = new CombinationDealInfo();
                String bizDataStr = JSONObject.toJSONString(e.get("bizData"));
                Map<String, String> bizData = JSONObject.parseObject(bizDataStr, Map.class);
                // 剔除无效推荐数据
                if (!String.valueOf(bizData.get("is_valid")).equals("1")) {
                    return;
                }
                // 兜底逻辑 只返回同店数据
                // isSameShop： 1 - 同店、0 - 跨店,见红色部分  2同店&跨店
                if (org.apache.commons.lang3.math.NumberUtils.toInt(bizData.get("isSameShop")) != 1 ){
                    return;
                }
                combinationDealInfo.setItemId(bizData.get("combinationId"));
                combinationDealInfo.setMainDealId(org.apache.commons.lang3.math.NumberUtils.toInt(String.valueOf(bizData.get(MAIN_DEAL_KEY))));
                combinationDealInfo.setBindingDealId(org.apache.commons.lang3.math.NumberUtils.toInt(String.valueOf(bizData.get(BINDING_DEAL_KEY))));
                combinationDealInfo.setBindingSource(BindingSourceEnum.MANUAL.getValue());
                combinationDealInfos.add(combinationDealInfo);
            });
            return combinationDealInfos;
        } catch (Exception e){
            log.error("com.dianping.mobile.mapi.dztgdetail.facade.BuyMoreSaveMoreFacade.convertToCombinationDealInfo(java.lang.String) error:", e);
            return combinationDealInfos;
        }
    }

    public List<CombinationDealInfo> convertToCombinationDealInfo(List<RecommendDTO> recommendDTOS) {
        List<CombinationDealInfo> combinationDealInfos = Lists.newArrayList();
        if (CollectionUtils.isEmpty(recommendDTOS)) {
            return combinationDealInfos;
        }

        recommendDTOS.forEach(recommendDTO -> {
            CombinationDealInfo combinationDealInfo = new CombinationDealInfo();
            Map<String, Object> bizDataMap = recommendDTO.getBizData();
            // 如果map为空或者不包含主品和搭售品id，直接跳过
            if (MapUtils.isEmpty(bizDataMap) || !bizDataMap.containsKey(MAIN_DEAL_KEY) || !bizDataMap.containsKey(BINDING_DEAL_KEY)) {
                return;
            }
            // 剔除无效推荐数据
            if (!String.valueOf(bizDataMap.get("is_valid")).equals("1")) {
                return;
            }
            combinationDealInfo.setItemId(recommendDTO.getItem());
            combinationDealInfo.setMainDealId(NumberUtils.toInt(String.valueOf(bizDataMap.get(MAIN_DEAL_KEY))));
            combinationDealInfo.setBindingDealId(NumberUtils.toInt(String.valueOf(bizDataMap.get(BINDING_DEAL_KEY))));
            combinationDealInfo.setBindingSource(BindingSourceEnum.ALGORITHM.getValue());
            combinationDealInfos.add(combinationDealInfo);
        });
        return combinationDealInfos;
    }
}
