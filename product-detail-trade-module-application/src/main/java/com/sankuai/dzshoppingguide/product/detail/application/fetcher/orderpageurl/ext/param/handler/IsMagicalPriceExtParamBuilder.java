package com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.ext.param.handler;

import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.enums.OrderPageExtParamEnums;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.ext.param.BaseSingleExtParamBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.ext.param.request.ExtParamBuilderRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.dto.PriceReturnValue;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * @Author: guangyujie
 * @Date: 2025/4/7 11:12
 */
@Component
public class IsMagicalPriceExtParamBuilder extends BaseSingleExtParamBuilder {

    @Override
    protected OrderPageExtParamEnums buildKey() {
        return OrderPageExtParamEnums.isMagicalPrice;
    }

    @Override
    protected String doBuildExtParam(ProductDetailPageRequest request, ExtParamBuilderRequest builderRequest) throws Exception {
        PriceDisplayDTO priceDisplayDTO = Optional.ofNullable(builderRequest)
                .map(ExtParamBuilderRequest::getFinalPrice)
                .map(PriceReturnValue::getPriceDisplayDTO)
                .orElse(null);
        if (priceDisplayDTO == null || CollectionUtils.isEmpty(priceDisplayDTO.getUsedPromos())) {
            return null;
        }
        return String.valueOf(priceDisplayDTO.getUsedPromos().stream().anyMatch(this::matchMagicPrice));
    }

    private boolean matchMagicPrice(PromoDTO promoDTO) {
        if (promoDTO == null) {
            return false;
        }
        if (promoDTO.getIdentity() == null || promoDTO.getIdentity().getPromoShowType() == null) {
            return false;
        }
        return "MAGICAL_MEMBER_PLATFORM_COUPON".equals(promoDTO.getIdentity().getPromoShowType());
    }

}
