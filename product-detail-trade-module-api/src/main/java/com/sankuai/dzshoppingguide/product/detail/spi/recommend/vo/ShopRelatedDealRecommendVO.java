package com.sankuai.dzshoppingguide.product.detail.spi.recommend.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.dz.product.detail.gateway.spi.vo.AbstractModuleVO;
import lombok.Data;

import java.util.List;

import static com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants.MODULE_DETAIL_DEAL_IN_SHOP_RECOMMEND;

/**
 * @Author: zhangyuan103
 * @Date: 2025/5/19
 * 同店推荐返回 https://mobile.sankuai.com/studio/model/info/42723
 */
@Data
@MobileDo
public class ShopRelatedDealRecommendVO extends AbstractModuleVO {
    @FieldDoc(description = "推荐团单信息列表")
    @MobileField
    private List<RelatedDealPBO> dealPBOList;

    @FieldDoc(description = "按钮文案")
    @MobileField
    private String btnText;

    @FieldDoc(description = "门店跳转链接")
    @MobileField
    private String shopJumpUrl;

    @Override
    public String getModuleKey() {
        return MODULE_DETAIL_DEAL_IN_SHOP_RECOMMEND;
    }
}
