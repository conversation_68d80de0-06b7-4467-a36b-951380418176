package com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.dto;

import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;


/**
 * <AUTHOR>
 * @date 2025-03-04
 * @desc 商品价格返回值
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class PriceReturnValue extends FetcherReturnValueDTO {

    /**
     * 门市价
     */
    private String marketPrice;

    /**
     * 减后价
     */
    private String promoPrice;

    /**
     * 最终的实付价
     */
    private String finalPrice;

    /**
     * 市场价优惠折扣
     */
    private String marketPromoDiscount;

    /**
     * 使用到的priceDisplayDTO
     */
    PriceDisplayDTO priceDisplayDTO;

}
