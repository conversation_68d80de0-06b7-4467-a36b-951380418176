package com.sankuai.dzshoppingguide.product.detail.spi.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @Author: zhen<PERSON><PERSON><PERSON><EMAIL>
 * @Date: 2025/2/12
 */
@Getter
@AllArgsConstructor
public enum NavBarItemTypeEnum {
    NAV_BAR_FAVOR(1, "收藏"),
    NAV_BAR_SHARE(2, "分享"),
    NAV_BAR_MORE(3, "更多"),
    NAV_BAR_HOME_PAGE(4, "首页"),
    NAV_BAR_MSG(5, "消息"),
    NAV_BAR_POI(6, "门店"),
    NAV_BAR_CART(7, "举报"),
    ;

    private int code;
    private String desc;

    public static NavBarItemTypeEnum from(int code) {
        for (NavBarItemTypeEnum value : NavBarItemTypeEnum.values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        return null;
    }
}
