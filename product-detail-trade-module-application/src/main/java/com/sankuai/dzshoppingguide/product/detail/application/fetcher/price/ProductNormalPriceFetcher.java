package com.sankuai.dzshoppingguide.product.detail.application.fetcher.price;

import com.sankuai.dealuser.price.display.api.enums.RequestSceneEnum;
import com.sankuai.dealuser.price.display.api.model.BatchPriceRequest;
import com.sankuai.dealuser.price.display.api.model.ClientEnv;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.ProductIdentity;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategoryFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.membercard.card.integration.IntegrationMemberCard;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.membercard.card.integration.IntegrationMemberCardFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.membercard.card.integration.IntegrationMemberCardNoEntranceFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.best.shop.ShopInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.best.shop.ShopInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.dto.ProductPriceReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.defaultselect.SkuDefaultSelect;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.defaultselect.SkuDefaultSelectFetcher;
import com.sankuai.general.product.query.center.client.dto.PriceDTO;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @date 2025-03-06
 * @desc normalPrice查询
 */
@Fetcher(
        previousLayerDependencies = {
                ProductBaseInfoFetcher.class,
                ProductCategoryFetcher.class,
                ShopInfoFetcher.class,
                SkuDefaultSelectFetcher.class,
                IntegrationMemberCardFetcher.class
        }
)
public class ProductNormalPriceFetcher extends AbstractProductPriceFetcher<ProductPriceReturnValue> {


    @Override
    protected CompletableFuture<ProductPriceReturnValue> doFetch() {
        if (!SUPPORT_CLIENT_TYPE.contains(request.getClientTypeEnum())) {
            ProductBaseInfo productBaseInfo = getDependencyResult(ProductBaseInfoFetcher.class);
            PriceDisplayDTO defaultPriceDisplayDTO = buildDefaultPriceDisplayDTO(productBaseInfo);
            return CompletableFuture.completedFuture(new ProductPriceReturnValue(defaultPriceDisplayDTO));
        }
        ProductCategory productCategory = getDependencyResult(ProductCategoryFetcher.class);
        SkuDefaultSelect skuDefaultSelect = getDependencyResult(SkuDefaultSelectFetcher.class);
        IntegrationMemberCard integrationMemberCard = getDependencyResult(IntegrationMemberCardFetcher.class);
        // 1. 分销场景
        if (isODP(request.getClientTypeEnum(), request.getPageSource())) {
            return buildODPPriceDisplayReturnValue(request, skuDefaultSelect);
        }
        // 2. 点评百度微信小程序
        if (isDpBaiduMapXcx(request.getClientTypeEnum())) {
            return buildDpBaiDuMapXcxPriceDisplayReturnValue(request, skuDefaultSelect);
        }
        // 3. 美团美播小程序
        if (isMtLiveXcx(request.getClientTypeEnum())) {
            return buildMtLiveMiniAppPriceDisplayReturnValue(request, skuDefaultSelect);
        }
        ShopInfo shopInfo = getDependencyResult(ShopInfoFetcher.class, ShopInfo.class).orElse(new ShopInfo());
        Set<Integer> poiCategoryIds = getPoiCategoryIds(request.getClientTypeEnum(), shopInfo);
        CompletableFuture<ProductPriceReturnValue> defaultNormalPriceResult = buildDefaultNormalPriceDisplayReturnValue(request, skuDefaultSelect, poiCategoryIds, productCategory.getProductSecondCategoryId());
        CompletableFuture<ProductPriceReturnValue> merchantMemberNormalPriceResult = buildMerchantMemberPriceDisplayReturnValue(request, skuDefaultSelect);
        return CompletableFuture.allOf(defaultNormalPriceResult, merchantMemberNormalPriceResult).thenApply(v -> {
            ProductPriceReturnValue defaultNormalPriceDisplayReturnValue = defaultNormalPriceResult.join();
            ProductPriceReturnValue merchantMemberNormalPriceDisplayReturnValue = merchantMemberNormalPriceResult.join();
            // 门店类目和团单类目限制
            if (isMerchantMember(request.getClientTypeEnum(), productCategory.getProductSecondCategoryId()) && needCompare(integrationMemberCard)) {
                // 比价
                if (useMerchantMemberPrice(merchantMemberNormalPriceDisplayReturnValue, defaultNormalPriceDisplayReturnValue, request.getProductId())) {
                    merchantMemberNormalPriceDisplayReturnValue.setMemberPromoPrice(true);
                    merchantMemberNormalPriceDisplayReturnValue.setOriginPriceDisplayDTO(defaultNormalPriceDisplayReturnValue.getPriceDisplayDTO());
                    return merchantMemberNormalPriceDisplayReturnValue;
                }
            }
            defaultNormalPriceDisplayReturnValue.setMemberPromoPrice(false);
            defaultNormalPriceDisplayReturnValue.setOriginPriceDisplayDTO(merchantMemberNormalPriceDisplayReturnValue.getPriceDisplayDTO());
            return defaultNormalPriceDisplayReturnValue;
        });
    }



    private CompletableFuture<ProductPriceReturnValue> buildODPPriceDisplayReturnValue(ProductDetailPageRequest pageRequest, SkuDefaultSelect skuDefaultSelect) {
        BatchPriceRequest priceRequest = buildBatchPriceRequest(pageRequest, skuDefaultSelect);
        priceRequest.setScene(RequestSceneEnum.PROMO_DETAIL_DESC_RETAIL.getScene());
        return getProductPriceDisplayDTO(priceRequest).thenApply(ProductPriceReturnValue::new);
    }

    private CompletableFuture<ProductPriceReturnValue> buildDpBaiDuMapXcxPriceDisplayReturnValue(ProductDetailPageRequest pageRequest, SkuDefaultSelect skuDefaultSelect) {
        BatchPriceRequest priceRequest = buildBatchPriceRequest(pageRequest, skuDefaultSelect);
        priceRequest.setScene(RequestSceneEnum.PROMO_DETAIL_NO_ITEM_DESC.getScene());
        return getProductPriceDisplayDTO(priceRequest).thenApply(ProductPriceReturnValue::new);
    }

    private CompletableFuture<ProductPriceReturnValue> buildMtLiveMiniAppPriceDisplayReturnValue(ProductDetailPageRequest pageRequest, SkuDefaultSelect skuDefaultSelect) {
        BatchPriceRequest priceRequest = buildBatchPriceRequest(pageRequest, skuDefaultSelect);
        priceRequest.setScene(RequestSceneEnum.NO_PROMO.getScene());
        return getProductPriceDisplayDTO(priceRequest).thenApply(ProductPriceReturnValue::new);
    }

    private CompletableFuture<ProductPriceReturnValue> buildMerchantMemberPriceDisplayReturnValue(ProductDetailPageRequest pageRequest, SkuDefaultSelect skuDefaultSelect) {
        BatchPriceRequest priceRequest = buildBatchPriceRequest(pageRequest, skuDefaultSelect);
        priceRequest.setScene(RequestSceneEnum.PROMO_DETAIL_NO_ITEM_DESC_WithMerchantMember.getScene());
        return getProductPriceDisplayDTO(priceRequest).thenApply(ProductPriceReturnValue::new);
    }

    private CompletableFuture<ProductPriceReturnValue> buildDefaultNormalPriceDisplayReturnValue(ProductDetailPageRequest pageRequest, SkuDefaultSelect skuDefaultSelect, Set<Integer> poiCategoryIds, int secondCategoryId) {
        BatchPriceRequest priceRequest = buildBatchPriceRequest(pageRequest, skuDefaultSelect);
        if (isFootMassageScene(pageRequest.getClientTypeEnum(), poiCategoryIds, secondCategoryId)) {
            priceRequest.setScene(RequestSceneEnum.PROMO_DETAIL_DESC_WithDealPromo.getScene());
        } else {
            priceRequest.setScene(RequestSceneEnum.PROMO_DETAIL_NO_ITEM_DESC.getScene());
        }
        return getProductPriceDisplayDTO(priceRequest).thenApply(ProductPriceReturnValue::new);
    }

    @Override
    protected void postProcessPriceRequest(ClientEnv clientEnv, List<ProductIdentity> productIdentities, Map<String, String> extension) {

    }

    private PriceDisplayDTO buildDefaultPriceDisplayDTO(ProductBaseInfo productBaseInfo) {
        if (Objects.isNull(productBaseInfo ) || Objects.isNull(productBaseInfo.getPrice())) {
            return null;
        }
        PriceDTO price = productBaseInfo.getPrice();
        PriceDisplayDTO priceDisplayDTO = new PriceDisplayDTO();
        priceDisplayDTO.setPrice(StringUtils.isNotBlank(price.getSalePrice()) ? new BigDecimal(price.getSalePrice()) : null);
        priceDisplayDTO.setMarketPrice(StringUtils.isNotBlank(price.getMarketPrice()) ? new BigDecimal(price.getMarketPrice()) : null);
        return priceDisplayDTO;
    }
}
