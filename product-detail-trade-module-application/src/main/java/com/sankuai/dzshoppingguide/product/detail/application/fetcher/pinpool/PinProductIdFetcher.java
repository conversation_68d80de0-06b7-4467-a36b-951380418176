package com.sankuai.dzshoppingguide.product.detail.application.fetcher.pinpool;

import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.sankuai.athena.inf.cache2.CacheClient;
import com.sankuai.athena.inf.cache2.CacheKey;
import com.sankuai.athena.inf.cache2.loader.DataLoader;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.dealgroup.DealGroupIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.dealgroup.DealGroupIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
import com.sankuai.dzshoppingguide.product.detail.domain.utils.RedisClientUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @create 2025/3/10 15:35
 */
@Fetcher(
        previousLayerDependencies = {DealGroupIdMapperFetcher.class}
)
@Slf4j
public class PinProductIdFetcher extends NormalFetcherContext<PinProductIdResult> {

    private static final String REDIS_DEAL_PINPOOL_PRODUCT_ID = "pinpool_product_id";
    private static final int CACHE_EXPIRE_TIME = 1296000;
    private static final int CACHE_REFRESH_TIME = 0;
    private static final String MAPPER_CACHE_KEY = "mapperCacheError";
    private static TypeReference<Map<Long, Long>> pinPoolCacheTypeReference = new TypeReference<Map<Long, Long>>() {};

    @Autowired
    private CompositeAtomService compositeAtomService;

    private static CacheClient cacheClient = RedisClientUtils.getRedisCacheClient();


    @Override
    protected CompletableFuture<PinProductIdResult> doFetch() {
        DealGroupIdMapper dealGroupIdMapper = getDependencyResult(DealGroupIdMapperFetcher.class);
        if (Lion.getBooleanValue("com.sankuai.dzshoppingguide.detail.trademodule.pin.pool.switch",false)) {
            return fetchPinProductId(dealGroupIdMapper.getDpDealGroupId()).thenApply((res)->{
                return new PinProductIdResult(res);
            });
        } else {
            List<Long> dealGroupIds = Lists.newArrayList();
            dealGroupIds.add(dealGroupIdMapper.getDpDealGroupId());
            return compositeAtomService.getPinProductIdByDealGroupIds(dealGroupIds).thenApply(res ->{
                return new PinProductIdResult(res);
            });
        }

    }

    private CompletableFuture<Map<Long, Long>> fetchPinProductId(long dpId) {
        try {
            return fetchPinProductFromCache(dpId);
        } catch (Exception e) {
            Cat.logError(e);
            Cat.logEvent(MAPPER_CACHE_KEY, "pinProductError");
            return CompletableFuture.completedFuture(null);
        }
    }

    private CompletableFuture<Map<Long, Long>> fetchPinProductFromCache(long dpId) {
        CacheKey cacheKey = new CacheKey(REDIS_DEAL_PINPOOL_PRODUCT_ID, dpId);
        DataLoader<Map<Long, Long>> dataLoader = key -> {
            if(key == null) {
                return CompletableFuture.completedFuture(null);
            }
            return compositeAtomService.getPinProductIdByDealGroupIds(Lists.newArrayList(dpId));
        };
        return cacheClient.asyncGetReadThrough(cacheKey,pinPoolCacheTypeReference, dataLoader, CACHE_EXPIRE_TIME + new Random().nextInt(300) , CACHE_REFRESH_TIME);
    }
}
