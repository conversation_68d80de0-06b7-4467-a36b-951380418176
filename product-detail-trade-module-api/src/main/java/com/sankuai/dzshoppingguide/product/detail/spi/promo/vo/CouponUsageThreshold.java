package com.sankuai.dzshoppingguide.product.detail.spi.promo.vo;

import java.io.Serializable;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025-04-02
 * @desc
 */
@TypeDoc(description = "优惠券使用门槛")
@Data
@MobileDo(id = 0x2050)
public class CouponUsageThreshold implements Serializable {
    
    @FieldDoc(description = "字体颜色")
    @MobileField(key = 0x2ac4)
    private String color;

    @FieldDoc(description = "字体大小")
    @MobileField(key = 0xb53a)
    private int fontSize;

    @FieldDoc(description = "门槛")
    @MobileField(key = 0x478a)
    private String threshold;
}
