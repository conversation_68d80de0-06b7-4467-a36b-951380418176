package com.sankuai.dzshoppingguide.product.detail.application.fetcher.reserve;

import com.dianping.lion.client.Lion;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftClient;
import com.sankuai.clr.content.process.thrift.api.ResvQueryService;
import com.sankuai.clr.content.process.thrift.dto.common.ProjectDTO;
import com.sankuai.clr.content.process.thrift.dto.common.TimeDTO;
import com.sankuai.clr.content.process.thrift.dto.req.QueryStockReqDTO;
import com.sankuai.clr.content.process.thrift.dto.resp.QueryStockRespDTO;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.dealgroup.DealGroupIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.dealgroup.DealGroupIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.shopid.ShopIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.shopid.ShopIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.domain.utils.ThriftAsyncUtils;
import com.sankuai.general.product.query.center.client.enums.TradeTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;

import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

/**
 * @Author: guangyujie
 * @Date: 2025/3/19 22:50
 */
@Fetcher(
        timeout = 500L,
        previousLayerDependencies = {
                DealGroupIdMapperFetcher.class,
                ShopIdMapperFetcher.class,
                ProductBaseInfoFetcher.class
        }
)
@Slf4j
public class ReserveStatusOfReservationDealFetcher extends NormalFetcherContext<ReserveStatusOfReservationDealResult> {

    @MdpThriftClient(remoteAppKey = "com.sankuai.leads.content.process", timeout = 1500, testTimeout = 5000, async = true)
    private ResvQueryService resvQueryService;

    @Override
    protected CompletableFuture<ReserveStatusOfReservationDealResult> doFetch() throws TException {
        ProductBaseInfo productBaseInfo = getDependencyResult(ProductBaseInfoFetcher.class);
        if (!isEnable(productBaseInfo)) {
            return CompletableFuture.completedFuture(new ReserveStatusOfReservationDealResult(false));
        }
        QueryStockReqDTO queryStockReqDTO = buildQueryStockReqDTO();
        resvQueryService.queryStock(queryStockReqDTO);
        CompletableFuture<QueryStockRespDTO> cf = ThriftAsyncUtils.getThriftFuture();
        return cf.thenApply(respDTO -> {
            boolean isCanReserve = Optional.ofNullable(respDTO)
                    .map(QueryStockRespDTO::getStockItemStatus)
                    .map(status -> status == 1)
                    .orElse(false);
            return new ReserveStatusOfReservationDealResult(isCanReserve);
        });
    }

    private boolean isEnable(ProductBaseInfo productBaseInfo) {
        if (Objects.isNull(productBaseInfo) || Objects.isNull(productBaseInfo.getBasic())) {
            return false;
        }
        boolean zeroVaccineSwitch = Lion.getBoolean("com.sankuai.dzu.tpbase.dztgdetailweb","com.sankuai.dzu.tpbase.dztgdetailweb.zeroVaccine.switch",false);
        return zeroVaccineSwitch && Objects.equals(productBaseInfo.getBasic().getTradeType(), TradeTypeEnum.RESERVATION.getCode());
    }

    private QueryStockReqDTO buildQueryStockReqDTO() {
        ShopIdMapper shopIdMapper = getDependencyResult(ShopIdMapperFetcher.class);
        DealGroupIdMapper dealGroupIdMapper = getDependencyResult(DealGroupIdMapperFetcher.class);
        QueryStockReqDTO queryStockReqDTO = new QueryStockReqDTO();
        if (request.getClientTypeEnum().isMtClientType()) {
            queryStockReqDTO.setMtShopId(String.valueOf(shopIdMapper.getMtBestShopId()));
            queryStockReqDTO.setPlatform(2);
        } else {
            queryStockReqDTO.setDpShopId(String.valueOf(shopIdMapper.getDpBestShopId()));
            queryStockReqDTO.setPlatform(1);
        }
        queryStockReqDTO.setEntranceCode(1);

        ProjectDTO projectDTO = new ProjectDTO();
        projectDTO.setProjectType(1);
        projectDTO.setProjectCode(dealGroupIdMapper.getDpDealGroupId());
        queryStockReqDTO.setProject(projectDTO);
        TimeDTO timeDTO = new TimeDTO();
        timeDTO.setTimeType(1);
        timeDTO.setTimeUnit(1);
        timeDTO.setTimeValue(String.valueOf(60));
        queryStockReqDTO.setTime(timeDTO);
        queryStockReqDTO.setReservationScene(1);
        return queryStockReqDTO;
    }

}
