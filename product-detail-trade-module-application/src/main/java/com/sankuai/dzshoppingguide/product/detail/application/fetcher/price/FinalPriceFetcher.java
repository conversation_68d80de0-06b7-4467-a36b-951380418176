package com.sankuai.dzshoppingguide.product.detail.application.fetcher.price;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.LeadsInfoFetcher.LeadsInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.LeadsInfoFetcher.LeadsInfoResult;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.cepinpool.CostEffectivePinPoolFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.cepinpool.dto.CostEffectivePinTuan;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.dto.PriceReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.dto.ProductPriceReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.service.promo.PromoPriceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 */
@Fetcher(previousLayerDependencies = {
        ProductBaseInfoFetcher.class,
        LeadsInfoFetcher.class,
        ProductNormalPriceFetcher.class,
        ProductPromoPriceFetcher.class,
        ProductCostEffectivePriceFetcher.class,
        CostEffectivePinPoolFetcher.class
})
@Slf4j
public class FinalPriceFetcher extends NormalFetcherContext<PriceReturnValue> {
    @Autowired
    private PromoPriceService promoPriceService;
    private ProductBaseInfo baseInfo = null;
    private LeadsInfoResult leadsInfoResult = null;
    private ProductPriceReturnValue normalPrice = null;
    private ProductPriceReturnValue costEffectivePrice = null;
    private ProductPriceReturnValue dealPromoPrice = null;
    private CostEffectivePinTuan costEffectivePinTuan = null;

    @Override
    protected CompletableFuture<PriceReturnValue> doFetch() {
        try{
            baseInfo = getDependencyResult(ProductBaseInfoFetcher.class);
            leadsInfoResult = getDependencyResult(LeadsInfoFetcher.class);
            normalPrice = getDependencyResult(ProductNormalPriceFetcher.class);
            costEffectivePrice = getDependencyResult(ProductCostEffectivePriceFetcher.class);
            dealPromoPrice = getDependencyResult(ProductPromoPriceFetcher.class);
            costEffectivePinTuan = getDependencyResult(CostEffectivePinPoolFetcher.class);

            PriceReturnValue priceReturnValue = promoPriceService.buildPrice(
                    request,
                    costEffectivePinTuan,
                    baseInfo,
                    dealPromoPrice != null ? dealPromoPrice.getPriceDisplayDTO() : null,
                    normalPrice != null ? normalPrice.getPriceDisplayDTO() : null,
                    costEffectivePrice != null ? costEffectivePrice.getPriceDisplayDTO() : null,
                    request.getClientTypeEnum(),
                    leadsInfoResult
            );
            return CompletableFuture.completedFuture(priceReturnValue);
        }catch (Exception e){
            log.error("FinalPriceFetcher.doFetch error", e);
            return CompletableFuture.completedFuture(new PriceReturnValue());
        }
    }

}
