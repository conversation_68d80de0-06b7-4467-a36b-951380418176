package com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommendgoods;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.dianping.martgeneral.recommend.api.dto.RecommendDTO;
import com.dianping.martgeneral.recommend.api.entity.RecommendParameters;
import com.dianping.martgeneral.recommend.api.entity.RecommendResult;
import com.dianping.martgeneral.recommend.api.enums.PlatformEnum;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.BaseFetcherContext;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dz.product.detail.gateway.spi.dto.ShepherdGatewayParam;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

@ExtendWith(MockitoExtension.class)
public class AbstractProductRecommendFetcherDoFetchTest {

    @Mock
    private CompositeAtomService atomService;

    private TestProductRecommendFetcher fetcher;

    private static class TestProductRecommendFetcher extends AbstractProductRecommendFetcher {

        @Override
        Map<String, Object> buildBizParams() {
            return new HashMap<>();
        }

        @Override
        int getBizId() {
            return 1;
        }

        @Override
        int getMinQueryNum() {
            return 0;
        }
    }

    @BeforeEach
    void setUp() {
        fetcher = new TestProductRecommendFetcher();
        ReflectionTestUtils.setField(fetcher, "atomService", atomService);
    }

    private ProductDetailPageRequest createMockRequest(boolean isMtClient) {
        ProductDetailPageRequest request = mock(ProductDetailPageRequest.class);
        ShepherdGatewayParam gatewayParam = mock(ShepherdGatewayParam.class);
        // Use real ClientTypeEnum instance
        when(request.getClientTypeEnum()).thenReturn(isMtClient ? ClientTypeEnum.MT_APP : ClientTypeEnum.DP_APP);
        when(request.getCityId()).thenReturn(1);
        when(request.getUserId()).thenReturn(123L);
        when(request.getUserLat()).thenReturn(31.2);
        when(request.getUserLng()).thenReturn(121.5);
        when(request.getShepherdGatewayParam()).thenReturn(gatewayParam);
        when(gatewayParam.getDeviceId()).thenReturn("test-device-id");
        return request;
    }

    @Test
    void testDoFetch_Success() throws Throwable {
        // arrange
        ProductDetailPageRequest request = createMockRequest(true);
        ReflectionTestUtils.setField(fetcher, "request", request);
        RecommendResult<RecommendDTO> recommendResult = new RecommendResult<>();
        RecommendDTO dto = new RecommendDTO();
        dto.setItem("test-item");
        recommendResult.setSortedResult(Collections.singletonList(dto));
        Map<String, Object> bizData = new HashMap<>();
        bizData.put("recallSize", 10);
        recommendResult.setBizData(bizData);
        when(atomService.getRecommendResult(any())).thenReturn(CompletableFuture.completedFuture(recommendResult));
        // act
        CompletableFuture<ProductRecommendResult> future = fetcher.doFetch();
        ProductRecommendResult result = future.get();
        // assert
        assertNotNull(result);
        verify(atomService).getRecommendResult(argThat(params -> {
            assertEquals(PlatformEnum.MT, params.getPlatformEnum());
            return true;
        }));
    }

    @Test
    void testDoFetch_NullResult() throws Throwable {
        // arrange
        ProductDetailPageRequest request = createMockRequest(true);
        ReflectionTestUtils.setField(fetcher, "request", request);
        when(atomService.getRecommendResult(any())).thenReturn(CompletableFuture.completedFuture(null));
        // act
        CompletableFuture<ProductRecommendResult> future = fetcher.doFetch();
        ProductRecommendResult result = future.get();
        // assert
        assertNull(result);
        verify(atomService).getRecommendResult(any());
    }

    @Test
    void testDoFetch_EmptySortedResult() throws Throwable {
        // arrange
        ProductDetailPageRequest request = createMockRequest(true);
        ReflectionTestUtils.setField(fetcher, "request", request);
        RecommendResult<RecommendDTO> recommendResult = new RecommendResult<>();
        recommendResult.setSortedResult(new ArrayList<>());
        when(atomService.getRecommendResult(any())).thenReturn(CompletableFuture.completedFuture(recommendResult));
        // act
        CompletableFuture<ProductRecommendResult> future = fetcher.doFetch();
        ProductRecommendResult result = future.get();
        // assert
        assertNull(result);
        verify(atomService).getRecommendResult(any());
    }

    @Test
    void testDoFetch_ServiceException() throws Throwable {
        // arrange
        ProductDetailPageRequest request = createMockRequest(true);
        ReflectionTestUtils.setField(fetcher, "request", request);
        CompletableFuture<RecommendResult<RecommendDTO>> errorFuture = new CompletableFuture<>();
        errorFuture.completeExceptionally(new RuntimeException("Service error"));
        when(atomService.getRecommendResult(any())).thenReturn(errorFuture);
        // act & assert
        CompletableFuture<ProductRecommendResult> future = fetcher.doFetch();
        Exception exception = assertThrows(Exception.class, future::get);
        assertTrue(exception.getCause() instanceof RuntimeException);
        assertEquals("Service error", exception.getCause().getMessage());
    }

    @Test
    void testDoFetch_NullSortedResult() throws Throwable {
        // arrange
        ProductDetailPageRequest request = createMockRequest(true);
        ReflectionTestUtils.setField(fetcher, "request", request);
        RecommendResult<RecommendDTO> recommendResult = new RecommendResult<>();
        recommendResult.setSortedResult(null);
        when(atomService.getRecommendResult(any())).thenReturn(CompletableFuture.completedFuture(recommendResult));
        // act
        CompletableFuture<ProductRecommendResult> future = fetcher.doFetch();
        ProductRecommendResult result = future.get();
        // assert
        assertNull(result);
        verify(atomService).getRecommendResult(any());
    }

    @Test
    void testDoFetch_RolePlaySpuRecommend() throws Throwable {
        // arrange
        ProductDetailPageRequest request = createMockRequest(true);
        ReflectionTestUtils.setField(fetcher, "request", request);
        RecommendResult<RecommendDTO> recommendResult = new RecommendResult<>();
        recommendResult.setSortedResult(Collections.singletonList(new RecommendDTO()));
        TestProductRecommendFetcher spyFetcher = spy(new TestProductRecommendFetcher() {

            @Override
            int getBizId() {
                // ROLE_PLAY_SPU_RECOMMEND_BIZ
                return 114;
            }

            @Override
            int getMinQueryNum() {
                return 5;
            }
        });
        ReflectionTestUtils.setField(spyFetcher, "atomService", atomService);
        ReflectionTestUtils.setField(spyFetcher, "request", request);
        when(atomService.getRecommendResult(any())).thenReturn(CompletableFuture.completedFuture(recommendResult));
        // act
        CompletableFuture<ProductRecommendResult> future = spyFetcher.doFetch();
        ProductRecommendResult result = future.get();
        // assert
        assertNotNull(result);
        assertTrue(result.getProducts() == null || result.getProducts().isEmpty());
        verify(atomService).getRecommendResult(argThat(params -> {
            assertEquals(114, params.getBizId());
            return true;
        }));
    }
}
