package com.sankuai.dzshoppingguide.product.detail.application.builder.promodetail.enums;

import lombok.Getter;

/**
 * 优惠券类型
 * <AUTHOR>
 */
@Getter
public enum CouponTypeEnum {
    BEST_MAGIC_COUPON("best_magic_coupon", "最佳优惠中的神券优惠券", "MAGICAL_MEMBER_PLATFORM_COUPON"),
    MAGIC_COUPON("magic_coupon", "其他神券优惠券", "MAGICAL_MEMBER_PLATFORM_COUPON"),
    PLATFORM_COUPON("platform_coupon", "美团券", "PLATFORM_COUPON"),
    MAGIC_COUPON_PACKAGE("magic_coupon_package", "神券包", ""),
    MEMBER_CARD("member_card", "会员优惠", "MEMBER_BENEFITS"),
    NEW_MEMBER_CARD("member_card", "新会员优惠", "NEW_MEMBER_BENEFITS"),
    GOVERNMENT_CONSUMER_COUPON("government_consumer_coupon", "政府券", "GOVERNMENT_CONSUME_COUPON"),
    BUY_GIVE_ACTIVITY("buy_give_activity", "买赠活动", ""),
    ORDER_GIFT_ACTIVITY("order_gift_activity", "下单返礼活动", ""),
    ORDER_COUPON_ACTIVITY("order_coupon_activity", "下单返券活动", ""),
    MERCHANT_COUPON("merchant_coupon", "商家券", "MERCHANT_COUPON"),
    MERCHANT_PIN_TUAN("merchant_pin_tuan", "商家拼团", ""),
    IDLE_TIME_COUPON("idle_time_coupon", "闲时特惠", ""),
    BUY_MORE_REDUCTION("buy_more_reduction", "多买多减", ""),
    BUY_MORE_DISCOUNT("buy_more_discount", "多买多折", ""),
    FINANCIAL_COUPON("financial_coupon", "金融券", ""),
    EXPOSURE_COUPON("exposure_coupon", "投放券（曝光券）", ""),
    OTHER_COUPON("other_coupon", "优惠券", ""),
    // 特惠促销
    PROMOTION("promotion", "特惠促销", "DISCOUNT_SELL"),
    // 美团补贴
    MT_SUBSIDY("mt_subsidy", "美团补贴", "MT_SUBSIDY"),
    // 新客特惠
    NEW_CUSTOMER_PROMOTION("new_customer_promotion", "新客特惠", "NEW_CUSTOMER_DISCOUNT"),
    // 团购优惠
    DEAL_PROMOTION("group_purchase_promotion", "团购优惠", "DEAL_PROMO"),
    //预售优惠
    PRESALE_PROMOTION("presale_promotion", "预售优惠", "PRESALE_PROMO"),
    // 限时秒杀
    SECOND_KILL("second_kill","限时秒杀", "SECOND_KILL"),
    ;

    final String code;
    final String desc;
    final String showType;

    CouponTypeEnum(String code, String desc, String showType) {
        this.code = code;
        this.desc = desc;
        this.showType = showType;
    }

    public static CouponTypeEnum ofCode(String code) {
        for (CouponTypeEnum couponTypeEnum : CouponTypeEnum.values()) {
            if (couponTypeEnum.getCode().equals(code)) {
                return couponTypeEnum;
            }
        }
        return OTHER_COUPON;
    }

    public static CouponTypeEnum ofDesc(String desc) {
        for (CouponTypeEnum couponTypeEnum : CouponTypeEnum.values()) {
            if (couponTypeEnum.getDesc().equals(desc)) {
                return couponTypeEnum;
            }
        }
        return OTHER_COUPON;
    }
}
