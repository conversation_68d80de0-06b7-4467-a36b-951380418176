package com.sankuai.dzshoppingguide.product.detail.application.fetcher.haima;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.haima.dtos.BlackListShopConfig;
import java.util.Arrays;
import java.util.Collections;
import java.util.concurrent.CompletableFuture;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

@ExtendWith(MockitoExtension.class)
class HaiMaFetcherDoFetchTest {

    @Mock
    private ProductDetailPageRequest request;

    @InjectMocks
    private HaiMaFetcher haiMaFetcher;

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(haiMaFetcher, "request", request);
    }

    /**
     * Test normal case when shop is in MT blacklist
     */
    @Test
    public void testDoFetch_ShopInMtBlackList() throws Throwable {
        // arrange
        when(request.getPoiId()).thenReturn(12345L);
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.MT_APP);
        // act
        CompletableFuture<HaiMaReturnValue> future = haiMaFetcher.doFetch();
        HaiMaReturnValue result = future.get();
        // assert
        assertNotNull(result);
        assertFalse(result.isBlackListShop());
    }

    /**
     * Test normal case when shop is in DP blacklist
     */
    @Test
    public void testDoFetch_ShopInDpBlackList() throws Throwable {
        // arrange
        when(request.getPoiId()).thenReturn(67890L);
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.DP_APP);
        // act
        CompletableFuture<HaiMaReturnValue> future = haiMaFetcher.doFetch();
        HaiMaReturnValue result = future.get();
        // assert
        assertNotNull(result);
        assertFalse(result.isBlackListShop());
    }

    /**
     * Test case when shop is not in blacklist
     */
    @Test
    public void testDoFetch_ShopNotInBlackList() throws Throwable {
        // arrange
        when(request.getPoiId()).thenReturn(99999L);
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.MT_APP);
        // act
        CompletableFuture<HaiMaReturnValue> future = haiMaFetcher.doFetch();
        HaiMaReturnValue result = future.get();
        // assert
        assertNotNull(result);
        assertFalse(result.isBlackListShop());
    }

    /**
     * Test case when blacklist config is null
     */
    @Test
    public void testDoFetch_NullBlackListConfig() throws Throwable {
        // arrange
        when(request.getPoiId()).thenReturn(12345L);
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.MT_APP);
        // act
        CompletableFuture<HaiMaReturnValue> future = haiMaFetcher.doFetch();
        HaiMaReturnValue result = future.get();
        // assert
        assertNotNull(result);
        assertFalse(result.isBlackListShop());
    }

    /**
     * Test case when poiId is 0
     */
    @Test
    public void testDoFetch_ZeroPoiId() throws Throwable {
        // arrange
        when(request.getPoiId()).thenReturn(0L);
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.MT_APP);
        // act
        CompletableFuture<HaiMaReturnValue> future = haiMaFetcher.doFetch();
        HaiMaReturnValue result = future.get();
        // assert
        assertNotNull(result);
        assertFalse(result.isBlackListShop());
    }

    /**
     * Test case when clientTypeEnum is null
     */
    @Test
    public void testDoFetch_NullClientTypeEnum() throws Throwable {
        // arrange
        when(request.getPoiId()).thenReturn(12345L);
        when(request.getClientTypeEnum()).thenReturn(null);
        // act
        CompletableFuture<HaiMaReturnValue> future = haiMaFetcher.doFetch();
        HaiMaReturnValue result = future.get();
        // assert
        assertNotNull(result);
        assertFalse(result.isBlackListShop());
    }

    /**
     * Test case when exception occurs during processing
     */
    @Test
    public void testDoFetch_ExceptionThrown() throws Throwable {
        // arrange
        when(request.getPoiId()).thenReturn(12345L);
        when(request.getClientTypeEnum()).thenThrow(new RuntimeException("Test exception"));
        // act
        CompletableFuture<HaiMaReturnValue> future = haiMaFetcher.doFetch();
        HaiMaReturnValue result = future.get();
        // assert
        assertNotNull(result);
        assertFalse(result.isBlackListShop());
    }

    /**
     * Test case when request is null
     */
    @Test
    public void testDoFetch_NullRequest() throws Throwable {
        // arrange
        ReflectionTestUtils.setField(haiMaFetcher, "request", null);
        // act
        CompletableFuture<HaiMaReturnValue> future = haiMaFetcher.doFetch();
        HaiMaReturnValue result = future.get();
        // assert
        assertNotNull(result);
        assertFalse(result.isBlackListShop());
    }
}
