package com.sankuai.dzshoppingguide.product.detail.application.builder.bookingtime.enums;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

@Getter
@AllArgsConstructor
public enum MultiTradeTypeEnum {

    /**
     * 非一品多态（团详默认逻辑）
     */
    DEFAULT(1, Lists.newArrayList("a", "b"), false),

    /**
     * 一品多态-先屯后订
     */
    BUY_FIRST(2, Lists.newArrayList("c"), true),

    /**
     * 一品多态-即买即订
     */
    DIRECT_BOOKING(3, Lists.newArrayList("d"), true),
    ;

    private final int value;
    private final List<String> exps;
    private final boolean showMultiTradeFeature;

    public static MultiTradeTypeEnum getMultiTradeEnum(String expResult) {
        for (MultiTradeTypeEnum multiTradeEnum : MultiTradeTypeEnum.values()) {
            if (multiTradeEnum.exps.contains(expResult)) {
                return multiTradeEnum;
            }
        }
        return DEFAULT;
    }
}
