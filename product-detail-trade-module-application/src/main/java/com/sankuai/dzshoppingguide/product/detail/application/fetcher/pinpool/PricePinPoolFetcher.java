package com.sankuai.dzshoppingguide.product.detail.application.fetcher.pinpool;

import com.dianping.tpfun.product.api.sku.common.enums.FunChannel;
import com.dianping.tpfun.product.api.sku.common.enums.FunClientType;
import com.dianping.tpfun.product.api.sku.common.enums.FunPlatform;
import com.dianping.tpfun.product.api.sku.pintuan.dto.PinProductBrief;
import com.dianping.tpfun.product.api.sku.pintuan.request.GetPinProductBriefReq;
import com.google.common.collect.Lists;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.enums.MobileOSTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.cityId.CityIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.cityId.CityIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Iterator;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @create 2025/3/10 15:35
 */
@Fetcher(
        previousLayerDependencies = {PinProductIdFetcher.class,
                CityIdMapperFetcher.class}
)
@Slf4j
public class PricePinPoolFetcher extends NormalFetcherContext<PricePinPoolResult> {
    
    @Autowired
    private CompositeAtomService compositeAtomService;

    @Override
    protected CompletableFuture<PricePinPoolResult> doFetch() {
        // 取数
        CityIdMapper cityIdMapper = getDependencyResult(CityIdMapperFetcher.class);
        PinProductIdResult pinProductIdResult = getDependencyResult(PinProductIdFetcher.class);

        if (pinProductIdResult == null || MapUtils.isEmpty(pinProductIdResult.getDealGroupId2PinProductIdMap())) {
            return CompletableFuture.completedFuture(null);
        }
        return compositeAtomService.batchGetPinProductBrief(buildGetPinProductBriefReq(cityIdMapper, pinProductIdResult)).thenApply((res)->{
            PinProductBrief pinProductBrief = getPinProductBrief(res);
            return new PricePinPoolResult(pinProductBrief);
        });
    }

    public PinProductBrief getPinProductBrief(Map<Integer, PinProductBrief> pinProductId2BriefMap){
        if (MapUtils.isEmpty(pinProductId2BriefMap) || CollectionUtils.isEmpty(pinProductId2BriefMap.values())) {
            return null;
        }
        Iterator<PinProductBrief> iterator = pinProductId2BriefMap.values().iterator();
        if (iterator.hasNext()) {
            return iterator.next();
        }
        return null;
    }
    public GetPinProductBriefReq buildGetPinProductBriefReq(CityIdMapper cityIdMapper, PinProductIdResult pinProductIdResult){
        GetPinProductBriefReq request = new GetPinProductBriefReq();
        request.setLongPinProductIds(Lists.newArrayList(pinProductIdResult.getDealGroupId2PinProductIdMap().values()));
        if (this.request.getClientTypeEnum().isMtClientType()) {
            request.setCityId(cityIdMapper.getMtCityId());
            request.setFunChannel(FunChannel.MT.code);
            request.setUserId(this.request.getMtUserId());//已确认判断平台后再使用
        } else {
            request.setFunChannel(FunChannel.DP.code);
            request.setCityId(cityIdMapper.getDpCityId());
            request.setUserId(this.request.getDpUserId());
        }
        if (this.request.getClientTypeEnum().isInApp()) {
            request.setClientVersion(this.request.getShepherdGatewayParam().getAppVersion());
            request.setFunClientType(FunClientType.NATIVE.code);
        } else if (isMiniProgram(this.request.getClientTypeEnum())) {
            request.setFunClientType(FunClientType.MINIPROGRAM.code);
        } else {
            request.setFunClientType(FunClientType.M.code);
        }
        if (MobileOSTypeEnum.ANDROID.equals(this.request.getMobileOSType())) {
            request.setFunPlatform(FunPlatform.ANDROID.code);
        } else {
            request.setFunPlatform(FunPlatform.IPHONE.code);
        }
        //分平台透传门店id, 获取拼团下单页url
        request.setLongShopId(this.request.getPoiId());
        return request;
    }

    public boolean isMiniProgram(ClientTypeEnum clientTypeEnum) {
        return ClientTypeEnum.DP_XCX.equals(clientTypeEnum) || ClientTypeEnum.MT_XCX.equals(clientTypeEnum);
    }
}
