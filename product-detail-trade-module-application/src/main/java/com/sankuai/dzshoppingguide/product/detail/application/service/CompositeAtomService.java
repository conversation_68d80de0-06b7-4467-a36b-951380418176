package com.sankuai.dzshoppingguide.product.detail.application.service;

import com.dianping.csc.center.engine.access.dto.AccessRequestDTO;
import com.dianping.csc.center.engine.access.dto.AccessResponseDTO;
import com.dianping.deal.sales.common.datatype.*;
import com.dianping.gm.marketing.times.card.api.dto.CardResponse;
import com.dianping.gmkt.scene.api.delivery.dto.req.QueryExposureResourcesReqDTO;
import com.dianping.gmkt.scene.api.delivery.dto.res.ResourceExposureResponseDTO;
import com.dianping.martgeneral.recommend.api.dto.RecommendDTO;
import com.dianping.martgeneral.recommend.api.entity.RecommendParameters;
import com.dianping.martgeneral.recommend.api.entity.RecommendResult;
import com.dianping.martgeneral.recommend.api.entity.Response;
import com.dianping.pay.promo.display.api.dto.PromoDisplayDTO;
import com.dianping.pay.promo.display.api.dto.QueryPromoDisplayRequest;
import com.dianping.product.shelf.common.dto.activity.ActivityDetailDTO;
import com.dianping.product.shelf.common.request.activities.ActivityProductQueryRequest;
import com.dianping.tgc.open.entity.BatchExProxyCouponRequest;
import com.dianping.tgc.open.entity.BatchExProxyCouponResponseDTO;
import com.dianping.tpfun.product.api.govsubsidy.model.GovSubsidyInfo;
import com.dianping.tpfun.product.api.govsubsidy.request.QueryGovSubsidyInfoRequest;
import com.dianping.tpfun.product.api.sku.pintuan.dto.PinProductBrief;
import com.dianping.tpfun.product.api.sku.pintuan.request.GetPinProductBriefReq;
import com.dianping.tuangu.dztg.usercenter.api.request.BatchGetCreateOrderPageUrlReq;
import com.sankuai.clr.content.process.gateway.thrift.dto.leads.LoadLeadsInfoReqDTO;
import com.sankuai.clr.content.process.gateway.thrift.dto.leads.LoadLeadsInfoRespDTO;
import com.sankuai.dealuser.price.display.api.model.BatchPriceRequest;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PriceResponse;
import com.sankuai.dzcard.navigation.api.dto.*;
import com.sankuai.dzim.cliententry.dto.ClientEntryDTO;
import com.sankuai.dzim.cliententry.dto.ClientEntryReqDTO;
import com.sankuai.dzshoppingguide.product.detail.application.service.dto.ShopMemberQueryResponse;
import com.sankuai.dztheme.deal.req.DealProductRequest;
import com.sankuai.dztheme.deal.req.SkuOptionBatchRequest;
import com.sankuai.dztheme.deal.res.DealProductResult;
import com.sankuai.dztheme.deal.res.DealSkuSummaryDTO;
import com.sankuai.dztheme.massagebook.theme.req.ReserveQueryRequest;
import com.sankuai.dztheme.massagebook.theme.res.ReserveQueryResponse;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResponse;
import com.sankuai.meituan.charity.merchant.main.sdk.dto.request.DzVpoiReq;
import com.sankuai.meituan.charity.merchant.main.sdk.dto.response.DzProductDocResp;
import com.sankuai.merchantcard.timescard.exposure.dto.CardSummaryBarDTO;
import com.sankuai.merchantcard.timescard.exposure.res.QueryDealGroupCardBarSummaryRequest;
import com.sankuai.mktplay.center.mkt.play.center.client.PlayExecuteRequest;
import com.sankuai.mktplay.center.mkt.play.center.client.PlayExecuteResponse;
import com.sankuai.mktplay.center.mkt.play.center.client.ScenePlayExecuteRequest;
import com.sankuai.mpmctmember.query.thrift.dto.GetShopMemberEntranceReqDTO;
import com.sankuai.mpmctmember.query.thrift.dto.GetShopMemberEntranceRespDTO;
import com.sankuai.mpmctmember.query.thrift.dto.QueryPlanAndUserIdentityDetailBySubjectIdReq;
import com.sankuai.mpmctmember.query.thrift.dto.QueryPlanAndUserIdentityDetailResp;
import com.sankuai.mppack.product.client.query.request.TyingBlackListQueryRequest;
import com.sankuai.mppack.product.client.query.response.TyingBlackListResponse;
import com.sankuai.mpproduct.idservice.api.request.BizProductIdConvertRequest;
import com.sankuai.newdzcard.supply.dto.DzCardDTO;
import com.sankuai.nib.price.operation.api.common.dto.SessionContextDTO;
import com.sankuai.nib.price.operation.api.guarantee.guide.standard.dto.ObjectGuaranteeTagDTO;
import com.sankuai.nib.price.operation.api.guarantee.guide.standard.request.BatchQueryGuaranteeTagRequest;
import com.sankuai.statesubsidies.c.thrift.request.GetUserQualificationOpenRequest;
import com.sankuai.statesubsidies.c.thrift.response.GetUserQualificationOpenResponse;
import com.sankuai.tpfun.skuoperationapi.price.dto.request.BatchPriceRangeInfoRequest;
import com.sankuai.tpfun.skuoperationapi.price.dto.response.BatchPriceRangeInfoResponse;
import org.apache.thrift.TException;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @date 2025-02-13
 * @desc
 */
public interface CompositeAtomService {
    /**
     * 获取门店是否是商家会员范围
     * 
     * @param request
     * @return
     */
    CompletableFuture<GetShopMemberEntranceRespDTO> getShopMemberEntrance(GetShopMemberEntranceReqDTO request);

    /**
     * 查询免费商家会员卡详情
     * @param request
     * @return
     */
    CompletableFuture<QueryPlanAndUserIdentityDetailResp> queryFreeMemberCardDetail(QueryPlanAndUserIdentityDetailBySubjectIdReq request);

    /**
     * 查询用户和门店的折扣卡状态
     * 
     * @param request
     * @return
     */
    CompletableFuture<CardHoldStatusDTO> queryShopAndUserCardHoldStatus(FindDCCardHoldStatusLiteReqDTO request);

    /**
     * 查询折扣卡信息
     * @param request
     * @return
     */
    CompletableFuture<QueryCardInfoDTO> queryDiscountCardInfo(QueryDiscountCardReq request);
    /**
     * 查询预订信息
     * @param request 预订信息查询入参
     * @return 预订信息查询结果
     */
    CompletableFuture<ReserveQueryResponse> queryReserveProduct(ReserveQueryRequest request);

    /**
     * 查询商品基础信息
     * @param dealGroupIds
     * @param isMt
     * @return
     */
    List<DealGroupDTO> getProductBaseInfo(List<Long> dealGroupIds, boolean isMt);

    /**
     * 根据统一ID查询商品基础信息
     * @param unifiedProductId
     * @return
     */
    List<DealGroupDTO> getProductBaseInfoByUnifiedId(List<Long> unifiedProductId);

    /**
     * 商家会员信息查询
     * @param mtVirtualUserId
     * @param platform
     * @param dpDealGroupIds
     * @param needPageUrl
     * @return
     */
    CompletableFuture<ShopMemberQueryResponse> getMemberDiscountInfoList(final Long mtVirtualUserId,
                                                                         final com.sankuai.mpmctmember.query.common.enums.PlatformEnum platform,
                                                                         final List<Long> dpDealGroupIds,
                                                                         final boolean needPageUrl
    );

    /**
     * 报价查询
     */
    CompletableFuture<PriceResponse<Map<Long, List<PriceDisplayDTO>>>> batchQueryPriceWithResponse(BatchPriceRequest request);

    /**
     * 查询抵用券
     * @param request
     * @return
     */
    CompletableFuture<BatchExProxyCouponResponseDTO> queryExcludeProxyCouponList(BatchExProxyCouponRequest request);

    /**
     * 查询单个留资信息
     * @param request
     * @return
     */
    CompletableFuture<LoadLeadsInfoRespDTO> loadLeadsInfo(LoadLeadsInfoReqDTO request) throws TException;

    /**
     * 查询团购/泛商品关联的有效活动
     * @param request
     * @return
     */
    CompletableFuture<ActivityDetailDTO> querySpecialValueDeal(ActivityProductQueryRequest request);


    /**
     * 特团渠道，需要查询暑促活动接口
     * @param playExecuteRequest
     * @return
     */
    CompletableFuture<PlayExecuteResponse> queryExecutePlay(PlayExecuteRequest playExecuteRequest) throws TException;

    /**
     * 新客渠道，需要查询新客活动接口
     * @param playExecuteRequest
     * @return
     */
    CompletableFuture<PlayExecuteResponse> queryNewCustomExecutePlay(PlayExecuteRequest playExecuteRequest) throws TException;

    /**
     * 所有渠道均需查询惊喜买赠活动接口
     * @param scenePlayExecuteRequest
     * @return
     */
    CompletableFuture<PlayExecuteResponse> querySceneExecutePlay(ScenePlayExecuteRequest scenePlayExecuteRequest) throws TException;

    /**
     * 查询到综推荐接口
     * @return
     */
    CompletableFuture<Response<RecommendResult>> recommend(RecommendParameters recommendParameters) throws TException;

    /**
     * 查询到综推荐接口
     * @param recommendParameters
     * @return Response<RecommendResult<RecommendDTO>>
     */
    CompletableFuture<Response<RecommendResult<RecommendDTO>>> recommendRecommendDTO(RecommendParameters recommendParameters);

    /**
     * 查询搭售黑名单
     */
    CompletableFuture<TyingBlackListResponse> batchQueryTyingBlackList(TyingBlackListQueryRequest tyingBlackListQueryRequest);

    /**
     * 查询团单Id和拼团id的映射
     * @param dealGroupIds
     * @return
     */
    CompletableFuture<Map<Long, Long>> getPinProductIdByDealGroupIds(List<Long> dealGroupIds);
    CompletableFuture<Map<Integer, PinProductBrief>> batchGetPinProductBrief(GetPinProductBriefReq req);
    CompletableFuture<List<PromoDisplayDTO>> queryPromoDisplayDTO(QueryPromoDisplayRequest request);
    /**
     * 销量查询
     * @param request 销量查询入参
     * @return 销量查询结果
     */
    CompletableFuture<Map<SalesSubjectParam, SalesDisplayInfoDTO>> querySaleDisplay(SalesDisplayQueryRequest request);

    /**
     * 查询公益信息
     * @param dzVpoiReq
     * @return
     */
    CompletableFuture<DzProductDocResp> queryDzProductDoc(DzVpoiReq dzVpoiReq);

    /**
     * 查询氛围条资源
     * @param request 查询入参
     * @return 查询结果
     */
    CompletableFuture<List<ResourceExposureResponseDTO>> queryExposureResources(QueryExposureResourcesReqDTO request);

    /**
     *
     * @param skuOptionRequest
     * @return
     */
    CompletableFuture<Map<Long, DealSkuSummaryDTO>> batchQuerySummary(SkuOptionBatchRequest skuOptionRequest);

    /**
     * 太平洋智能客服
     * @param request
     * @return
     */
    CompletableFuture<AccessResponseDTO> prepareAccessIn(AccessRequestDTO request);

    /**
     * 获取在线咨询的URL
     * @param request
     * @return
     */
    CompletableFuture<ClientEntryDTO> preOnlineConsultUrl(ClientEntryReqDTO request);

    /**
     * 营销次卡
     * @return
     */
    CompletableFuture<CardResponse<CardSummaryBarDTO>> preTimesCardsV2(QueryDealGroupCardBarSummaryRequest request);
    
    /**
     * 查询商家会员卡
     * @param request 查询入参
     * @return 查询结果
     */
    CompletableFuture<List<DzCardDTO>> queryDzCard(DzCardQueryRequest request);

    /**
     * 批量查询保障标签
     * @param sessionContext 用户信息
     * @param request 团单id
     * @return 保障标签
     */
    CompletableFuture<List<ObjectGuaranteeTagDTO>> batchQueryGuaranteeTag(SessionContextDTO sessionContext, BatchQueryGuaranteeTagRequest request);

    /**
     * 批量查询平台商品id
     * @param request 点评团单ID
     * @return 平台商品id
     */
    CompletableFuture<Map<Long, Long>> batchPlatformProductId(BizProductIdConvertRequest request);

    /**
     * 批量查询 美团团购id和点评团购id的映射关系
     */
    CompletableFuture<Map<Long, Long>> convertMtGroupIdsToDpGroupIds(List<Long> mtGroupIds);


    /**
     * 批量查询 点评团购id和美团团购id的映射关系
     */
    CompletableFuture<Map<Long, Long>> convertDpGroupIdsToMtGroupIds(List<Long> dpGroupIds);

    /**
     * 批量查询销量
     */
    CompletableFuture<Map<ProductParam, SalesDisplayDTO>> multiGetSales(SalesDisplayRequest request);

    /**
     * 批量查询提单跳链
     */
    CompletableFuture<Map<String, String>> batchGetCreateOrderPageUrl(BatchGetCreateOrderPageUrlReq request);

    /**
     * 查询中心
     */
    CompletableFuture<QueryDealGroupListResponse> queryByDealGroupIds(QueryByDealGroupIdRequest request);

    /**
     * 批量查询副标题
     */
    CompletableFuture<DealProductResult> queryDealSubtitle(DealProductRequest request);


    /**
     * 查询用户是否领取国补资格
     * @param request 查询入参
     * @return 查询结果
     */
    CompletableFuture<GetUserQualificationOpenResponse> getUserQualificationResponse(GetUserQualificationOpenRequest request);

    /**
     * 团单主题查询
     *
     * @param dealProductRequest
     * @return
     */
    CompletableFuture<DealProductResult> queryDealProductTheme(DealProductRequest dealProductRequest);


    CompletableFuture<DealProductResult> queryDealProductByTheme(DealProductRequest dealProductRequest);

    /**
     * 团单主题查询
     *
     * @param dealProductRequest
     * @return
     */
    CompletableFuture<DealProductResult> queryNewDealProductTheme(DealProductRequest dealProductRequest);

    /**
     * 查询比价助手推荐的商品
     * @param recommendParameters
     * @return
     */
    CompletableFuture<RecommendResult<RecommendDTO>> getRecommendResult(RecommendParameters recommendParameters);

    /**
     * 查询政府补贴信息
     * @param request 查询入参
     * @return 查询结果
     */
    CompletableFuture<GovSubsidyInfo> queryGovSubsidyInfo(QueryGovSubsidyInfoRequest request);


    /**
     * 查询团单所属竞争圈的价格带
     */
    CompletableFuture<com.sankuai.tpfun.skuoperationapi.price.dto.response.Response<BatchPriceRangeInfoResponse>> batchGetPriceRangeInfo(BatchPriceRangeInfoRequest request);


    /**
     * 预处理查询团单信息-团购主题
     * @param req 团购主题查询入参
     * @return 查询结果
     */
    CompletableFuture<DealProductResult> queryDealProduct(DealProductRequest req);

    /**
     * 通过dpShopId查询mtShopId
     * @param dpShopIds
     * @return
     */
    CompletableFuture<Map<Long, List<Long>>> queryMtByDpIdsL(List<Long> dpShopIds);

    /**
     * 根据dpShopId查询可售卖的dp团单
     * @param dpShopId
     * @param isMt
     * @return
     */
    CompletableFuture<Map<Long, List<Long>>> batchQuerySaleDealGroupId(long dpShopId, boolean isMt);
}
