package com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.dto;

import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import lombok.Getter;

/**
 * @Author: guangyujie
 * @Date: 2025/4/7 15:06
 */
@Getter
public class LocalPriceDisplayDTO {

    private final PriceDisplayDTO priceDisplayDTO;
    /**
     * 价格密文,用于计算价格一致率
     */
    private final String priceSecretInfo;

    public LocalPriceDisplayDTO(PriceDisplayDTO priceDisplayDTO, String priceSecretInfo) {
        this.priceDisplayDTO = priceDisplayDTO;
        this.priceSecretInfo = priceSecretInfo;
    }

}
