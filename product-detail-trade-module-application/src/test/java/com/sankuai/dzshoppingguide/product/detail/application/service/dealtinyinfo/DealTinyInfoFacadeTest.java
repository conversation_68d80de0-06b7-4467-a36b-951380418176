package com.sankuai.dzshoppingguide.product.detail.application.service.dealtinyinfo;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import com.alibaba.fastjson.JSON;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.enums.ProductTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.builder.similargoods.dtos.ShowLowPriceItemRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.dealgroup.DealGroupIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.best.shop.ShopInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.service.dealtinyinfo.dto.DealLowPriceItemEntranceVO;
import com.sankuai.dzshoppingguide.product.detail.application.service.douhu.DouHuBiz;
import com.sankuai.dzshoppingguide.product.detail.domain.douhu.AbTestResult;
import com.sankuai.dzshoppingguide.product.detail.spi.similargoods.vo.DealTinyInfoVO;
import com.sankuai.dztheme.deal.res.DealProductResult;
import com.sankuai.dztheme.deal.res.DealSkuSummaryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.tpfun.skuoperationapi.price.dto.response.BatchPriceRangeInfoResponse;
import org.junit.Test;

import java.lang.reflect.Field;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class DealTinyInfoFacadeTest {

    @Mock
    private DouHuBiz douHuBiz;

    @InjectMocks
    private DealTinyInfoFacade dealTinyInfoFacade;

    private ShowLowPriceItemRequest request;

    private ProductDetailPageRequest pageRequest;

    private ShopIdMapper shopIdMapper;

    private DealGroupIdMapper dealGroupIdMapper;

    private ProductBaseInfo productBaseInfo;

    private BatchPriceRangeInfoResponse priceResponse;

    private DealTinyInfoVO dealTinyInfo;

    private ShopInfo shopInfo;

    private DealSkuSummaryDTO skuSummary;

    private DealProductResult dealProductResult;

    private DealGroupCategoryDTO category;

    @BeforeEach
    void setUp() {
        request = mock(ShowLowPriceItemRequest.class);
        pageRequest = mock(ProductDetailPageRequest.class);
        shopIdMapper = mock(ShopIdMapper.class);
        dealGroupIdMapper = mock(DealGroupIdMapper.class);
        productBaseInfo = mock(ProductBaseInfo.class);
        priceResponse = mock(BatchPriceRangeInfoResponse.class);
        dealTinyInfo = new DealTinyInfoVO();
        shopInfo = mock(ShopInfo.class);
        skuSummary = mock(DealSkuSummaryDTO.class);
        dealProductResult = mock(DealProductResult.class);
        category = new DealGroupCategoryDTO();
        category.setCategoryId(505L);
        // Basic setup
        when(request.getRequest()).thenReturn(pageRequest);
        when(request.getShopIdMapper()).thenReturn(shopIdMapper);
    }

    /**
     * Test when shop is in MT blacklist
     */
    @Test
    public void testDealTinyInfoFacade() throws Exception {
        String shopInfoJson = "{\"backCategoryIds\":[1853,51,2159],\"dpPoiDTO\":{\"backMainCategoryPath\":[{\"categoryId\":1853,\"categoryLevel\":1,\"categoryName\":\"K歌\",\"hot\":1,\"leaf\":false,\"parentId\":0},{\"categoryId\":51,\"categoryLevel\":2,\"categoryName\":\"KTV\",\"hot\":0,\"leaf\":false,\"parentId\":1853},{\"categoryId\":2159,\"categoryLevel\":3,\"categoryName\":\"量贩式KTV\",\"hot\":0,\"leaf\":true,\"main\":true,\"parentId\":51}],\"cityId\":1,\"fiveScore\":0.0,\"fiveSub1\":0.0,\"fiveSub2\":0.0,\"fiveSub3\":0.0,\"fiveSub4\":0.0,\"fiveSub5\":0.0,\"fiveSub6\":0.0,\"mainCategoryId\":2890,\"mainRegionId\":89794,\"refinedScore1\":\"\",\"refinedScore2\":\"\",\"refinedScore3\":\"\",\"shopId\":573742736,\"shopType\":15,\"sub5\":0,\"sub6\":0},\"mtPoiDTO\":{\"dpBackCategoryList\":[{\"categoryId\":2159,\"main\":true}],\"dpPoiId\":573742736,\"isForeign\":false,\"isGangAoTai\":false,\"mtPoiId\":573742736,\"typeHierarchy\":[{\"id\":2159,\"name\":\"量贩式KTV\"},{\"id\":51,\"name\":\"KTV\"},{\"id\":1853,\"name\":\"K歌\"}]}}";
        String shopIdMapperJson = "{\"dpBestShopId\":573742736,\"mtBestShopId\":573742736}";
        String dealGroupIdMapperJson = "{\"dpDealGroupId\":1125222826,\"mtDealGroupId\":1125222826}";
        String abTestResultJson = "{\"expBiInfo\":\"{\\\"query_id\\\":\\\"c2fbb7a1-0adc-44b6-923f-314a7cb530d2\\\",\\\"ab_id\\\":\\\"exp002836_c\\\"}\",\"expId\":\"exp002836\",\"expResult\":\"c\",\"expStrategy\":\"exp002836_c\"}";
        String batchPriceRangeInfoResponseJson = "{\"subjectDTO2PriceRangesMap\":{{\"dpShopId\":573742736,\"productId\":1125222826}:[{\"priceRangeDO\":{\"extra\":{\"topTwentyPercentPrice\":\"68.0000\",\"topFiftyPercentPrice\":\"78.0000\"}},\"priceRangeItemType\":12}]}}";
        String skuSummaryJson = "{\"defaultSkuId\":1343263479,\"sameSalePrice\":true,\"unifyProduct\":true,\"withSaleAttr\":false}";
        String dealProductResultJson = "{\"deals\":[{\"attrs\":[{\"name\":\"dzShopCarOrderUrl\",\"value\":\"imeituan://www.meituan.com/mrn?mrn_biz=meishi&mrn_entry=c-group-order-submit-old&mrn_component=GroupOrderSubmitOld&scene=shopCart&_referer=comparePopup&products=%5B%7B%22dealid%22%3A1125222826%2C%22quantity%22%3A1%2C%22skuId%22%3A1343263479%7D%5D&bizType=2&mrn_min_version=0.10.0\"},{\"name\":\"dealSubTitle\",\"value\":\"[\\\"周一至周四可用\\\",\\\"小包\\\",\\\"软饮美食欢唱\\\"]\"}],\"beginDate\":0,\"categoryId\":0,\"endDate\":0,\"headPic\":\"https://p0.meituan.net/dpmerchantpic/4648b86caffcef9a5ef99abc55d4c458102789.jpg\",\"marketPriceTag\":\"350\",\"name\":\"下午档3小时房时＋小吃＋饮品套餐\",\"orderUrl\":\"imeituan://www.meituan.com/mrn?mrn_biz=gc&mrn_entry=group-order-submit&mrn_component=GroupOrderSubmit&dealid=1125222826&skuid=&shopid=573742736&isTransparent=true&mrn_transparent=true&hideLoading=true&mrn_hideloading=true&mrn_hideNextNavBar=true&shopidEncrypt=qB4r137f7ba00aba9be066b024945c70711afcbe64359ae9791ea7d73169a17779cb9a3545ee99a1f5a8vxu5&pagesource=dealGroupDetail&expid=directPage\",\"productId\":1125222826,\"productIdL\":1125222826,\"promoPrice\":82.00,\"sale\":{\"sale\":1309,\"saleTag\":\"年售1000+\",\"type\":0},\"serviceTypeId\":0,\"unifyProduct\":false}],\"traceId\":\"1243565640565759998\"}";

        String dealGroupDtoJson = "{\"basic\":{\"addTime\":\"2024-07-26 10:30:25\",\"beginSaleDate\":\"2024-07-26 10:30:24\",\"brandName\":\"星聚会KTV\",\"categoryId\":301,\"endSaleDate\":\"2025-10-30 00:00:00\",\"platformCategoryId\":80301,\"saleChannel\":0,\"salePlatform\":3,\"sourceId\":102,\"status\":1,\"title\":\"下午档3小时房时＋小吃＋饮品套餐\",\"titleDesc\":\"仅售88元，价值350元下午档3小时房时＋小吃＋饮品套餐！\",\"tradeType\":3,\"updateTime\":\"2025-02-11 11:05:24\"},\"category\":{\"categoryId\":301,\"platformCategoryId\":80301,\"serviceType\":\"欢唱和软饮类套餐\",\"serviceTypeId\":116006},\"channel\":{\"channelCn\":\"KTV\",\"channelEn\":\"ktv\",\"channelGroupCn\":\"到店综合\",\"channelGroupEn\":\"general\",\"channelGroupId\":2,\"channelId\":18},\"customer\":{\"originCustomerId\":40289319,\"platformCustomerId\":1021414173},\"deals\":[{\"basic\":{\"originTitle\":\"下午档3小时房时＋小吃＋饮品套餐\",\"status\":1,\"title\":\"下午档3小时房时＋小吃＋饮品套餐\"},\"bizDealId\":1343263479,\"bizDealIdInt\":1343263479,\"dealId\":1343263479,\"dealIdInt\":1343263479}],\"displayShopInfo\":{\"dpDisplayShopIds\":[573742736,1614137863,957843519,1423070545,1281956370,120104455],\"mtDisplayShopIds\":[573742736,1614137863,957843519,1423070545,1281956370,182589713]},\"dpDealGroupId\":1125222826,\"dpDealGroupIdInt\":1125222826,\"image\":{\"allPicPaths\":\"https://p0.meituan.net/dpmerchantpic/1845cff93d67e47b94a20b519d1e697256784.jpg\",\"defaultPicPath\":\"https://p0.meituan.net/dpmerchantpic/1845cff93d67e47b94a20b519d1e697256784.jpg\"},\"mtDealGroupId\":1125222826,\"mtDealGroupIdInt\":1125222826,\"price\":{\"marketPrice\":\"350.00\",\"salePrice\":\"88.00\",\"version\":12584395140},\"regions\":[{\"dpCityId\":1,\"mtCityId\":10}],\"rule\":{\"buyRule\":{\"maxPerUser\":0,\"minPerUser\":1},\"refundRule\":{\"supportOverdueAutoRefund\":true,\"supportRefundType\":1},\"useRule\":{\"disableDate\":{\"disableDateRangeDTOS\":[{\"from\":\"2024-12-31 00:00:00\",\"to\":\"2024-12-31 00:00:00\"}],\"disableDays\":[5,101,6,102,7,103,104,105,201,106,202,107,203,108,109,110,111]},\"receiptEffectiveDate\":{\"receiptBeginDate\":\"2025-06-13 21:13:45\",\"receiptDateType\":1,\"receiptEndDate\":\"2025-08-12 23:59:59\",\"receiptValidDays\":60,\"showText\":\"购买后60天内有效\"}}},\"saleChannelAggregation\":{\"allSupport\":true,\"notSupportChannels\":[],\"supportChannels\":[]},\"tags\":[{\"id\":100065119,\"tagName\":\"餐食\"},{\"id\":100065120,\"tagName\":\"软饮\"},{\"id\":1000000246,\"tagName\":\"KTV\"},{\"id\":100019841,\"tagName\":\"包厢\"},{\"id\":1000000251,\"tagName\":\"小包\"},{\"id\":100207340,\"tagName\":\"百亿补贴会场商品\"},{\"id\":100205353,\"tagName\":\"小包\"},{\"id\":100213206,\"tagName\":\"春节不打烊「商品活动采集」标签\"}]}";

         ShopInfo shopInfo = JSON.parseObject(shopInfoJson, ShopInfo.class);
        DealGroupDTO dealGroupDTO = JSON.parseObject(dealGroupDtoJson, DealGroupDTO .class);
         ProductBaseInfo productBaseInfo = new ProductBaseInfo(ProductTypeEnum.DEAL, dealGroupDTO);
         ShopIdMapper shopIdMapper = JSON.parseObject(shopIdMapperJson, ShopIdMapper.class);
         DealGroupIdMapper dealGroupIdMapper = JSON.parseObject(dealGroupIdMapperJson, DealGroupIdMapper.class);
         AbTestResult abTestResult = JSON.parseObject(abTestResultJson, AbTestResult.class);
         BatchPriceRangeInfoResponse batchPriceRangeInfoResponse = JSON.parseObject(batchPriceRangeInfoResponseJson, BatchPriceRangeInfoResponse.class);
         DealSkuSummaryDTO skuSummary = JSON.parseObject(skuSummaryJson, DealSkuSummaryDTO.class);
         DealProductResult dealProductResult = JSON.parseObject(dealProductResultJson, DealProductResult.class);

        ProductDetailPageRequest request = new ProductDetailPageRequest();

        request.setClientType(200);
        request.setCityId(10);
        request.setCityLat(10);
        request.setCityLng(10);



        ShowLowPriceItemRequest lowPriceItemRequest = ShowLowPriceItemRequest.builder()
                .request(request)
                .shopInfo(shopInfo)
                .productBaseInfo(productBaseInfo)
                .shopIdMapper(shopIdMapper)
                .dealGroupIdMapper(dealGroupIdMapper)
                .abTestResult(abTestResult)
                .batchPriceRangeInfoResponse(batchPriceRangeInfoResponse)
                .skuSummary(skuSummary)
                .dealProductResult(dealProductResult)
                .hitShopInBlackList(false)
                .build();

        DealTinyInfoFacade facade = new DealTinyInfoFacade();

        Class clazz = facade.getClass();

            Field field = clazz.getDeclaredField("douHuBiz");
            field.setAccessible(true);
            DouHuBiz douHuBiz = new DouHuBiz();
            field.set(facade, douHuBiz);
            DealLowPriceItemEntranceVO result = facade.isShowLowPriceItemEntrance(lowPriceItemRequest);
            assertTrue(result.isShowLowPriceDealList(), "true");

    }

    /**
     * Test when shop is in MT blacklist
     */
    @org.junit.jupiter.api.Test
    void testIsShowLowPriceItemEntranceWhenShopInMtBlacklist() throws Throwable {
        // arrange
        when(pageRequest.getClientTypeEnum()).thenReturn(ClientTypeEnum.fromCode(200));
        when(shopIdMapper.getMtBestShopId()).thenReturn(123L);
        when(request.isHitShopInBlackList()).thenReturn(true);
        // act
        DealLowPriceItemEntranceVO result = dealTinyInfoFacade.isShowLowPriceItemEntrance(request);
        // assert
        assertFalse(result.isShowLowPriceDealList());
    }
}
