package com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.ext.param.handler;

import com.sankuai.dz.product.detail.RequestCustomParamEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.enums.OrderPageExtParamEnums;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.ext.param.BaseSingleExtParamBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.ext.param.request.ExtParamBuilderRequest;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.io.UnsupportedEncodingException;

/**
 * @Author: guangyujie
 * @Date: 2025/3/12 17:09
 */
@Component
public class LyyExtParamBuilder extends BaseSingleExtParamBuilder {

    @Override
    public OrderPageExtParamEnums buildKey() {
        return OrderPageExtParamEnums.lyyuserid;
    }

    @Override
    protected String doBuildExtParam(final ProductDetailPageRequest request,
                                     final ExtParamBuilderRequest builderRequest) throws UnsupportedEncodingException {
        String lyyuserid = request.getCustomParam(RequestCustomParamEnum.lyyuserid);
        if (StringUtils.isBlank(lyyuserid)) {
            return null;
        }
        return lyyuserid;
    }

}
