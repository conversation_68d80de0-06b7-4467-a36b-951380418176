package com.sankuai.dzshoppingguide.product.detail.application.builder.timecard.list;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommendgoods.dtos.ProductM;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommendgoods.dtos.ShopM;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.timesdeal.dto.DealDetailBuildContext;
import com.sankuai.dzshoppingguide.product.detail.application.utils.DealDetailUtils;
import com.sankuai.dzshoppingguide.product.detail.application.utils.TimesDealUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: created by hang.yu on 2024/4/2 15:09
 */
@Slf4j
@Component
public abstract class AbstractTimesDealListOpt extends ProductListVP {

    public static final String SERVICE_TYPE = "service_type";

    /**
     * 猜喜
     */
    public static final String GUESS = "guess";

    /**
     * 货架
     */
    public static final String SHELF = "shelf";

    private static final String ATTR_SEARCH_HIDDEN_STATUS = "attr_search_hidden_status";

    private static final String DEAL_STATUS_ATTR = "dealStatusAttr";

    private static final String PRODUCT_TYPE = "productType";


    @Override
    public List<ProductM> compute(List<ProductM> productMS,DealDetailBuildContext buildContext) {
        if (CollectionUtils.isEmpty(productMS)) {
            return Lists.newArrayList();
        }
        List<ProductM> filterProductList = getFilterList(productMS);
        // 单个商品不展示
        if (CollectionUtils.isEmpty(filterProductList) || filterProductList.size() == 1) {
            return Lists.newArrayList();
        }
        long currentDealId = Optional.ofNullable(buildContext).map(DealDetailBuildContext::getRequest).map(ProductDetailPageRequest::getProductId).orElse(0L);
        // 列表必须包含当前商品
        ProductM currentProduct = getCurrentProductM(filterProductList, currentDealId);
        if (currentProduct == null) {
            return Lists.newArrayList();
        }
        List<ProductM> result = Lists.newArrayList();
        // 当前商品置顶
        result.add(currentProduct);
        // 多次卡团单
        result.addAll(getTimesDealList(filterProductList, currentDealId));
        // 相似团购团单
        // List<ProductM> otherDeals = filterProductList.stream().filter(productM -> productM.getProductId() != currentDealId)
        //         .filter(productM -> !productM.isTimesDealQueryFlag())
        //         .collect(Collectors.toList());
        // result.addAll(getSimilarDealList(currentProduct, otherDeals));

        ClientTypeEnum clientTypeEnum = Optional.ofNullable(buildContext).map(DealDetailBuildContext::getRequest).map(ProductDetailPageRequest::getClientTypeEnum).orElse(ClientTypeEnum.UNKNOWN);
        long shopId;
        if (clientTypeEnum.isMtClientType()) {
            shopId = Optional.ofNullable(buildContext).map(DealDetailBuildContext::getShopIdMapper).map(
                    ShopIdMapper::getMtBestShopId).orElse(0L);
        } else {
            shopId = Optional.ofNullable(buildContext).map(DealDetailBuildContext::getShopIdMapper).map(
                    ShopIdMapper::getDpBestShopId).orElse(0L);
        }

        // 过滤出当前门店适用的团购次卡
        result = result.stream().filter(productM -> {
            List<ShopM> shopMs = productM.getShopMs();
            if (CollectionUtils.isEmpty(shopMs)) {
                // 兜底还是展示
                return true;
            }
            List<Long> suitableShopIds = shopMs.stream().filter(Objects::nonNull).map(ShopM::getLongShopId)
                    .collect(Collectors.toList());
            return CollectionUtils.isNotEmpty(suitableShopIds) && suitableShopIds.contains(shopId);
        }).collect(Collectors.toList());

        if (result.size() <= 1) {
            return Lists.newArrayList();
        }
        return result;
    }

    protected abstract List<ProductM> getSimilarDealList(ProductM currentProduct, List<ProductM> list);

    protected List<ProductM> getFilterList(List<ProductM> list) {
        return list.stream().filter(this::isValid).collect(Collectors.toList());
    }

    protected boolean isValid(ProductM productM) {
        return productM != null && productM.getSalePrice() != null
                && "deal".equals(productM.getAttr(PRODUCT_TYPE))
                && "true".equals(productM.getAttr(DEAL_STATUS_ATTR))
                && "false".equals(productM.getAttr(ATTR_SEARCH_HIDDEN_STATUS))
                && productM.getSale() != null;
    }

    protected ProductM getCurrentProductM(List<ProductM> list, long currentDealId) {
        return list.stream().filter(productM -> productM.getProductId() == currentDealId).findAny().orElse(null);
    }

    /**
     * 多次卡团单按照团购价从低到高排序
     */
    protected List<ProductM> getTimesDealList(List<ProductM> timesDealQueryList, long currentDealId) {
        // List<ProductM> timesDealQueryList = list.stream().filter(ProductM::isTimesDealQueryFlag).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(timesDealQueryList)) {
            return Lists.newArrayList();
        }
        // 列表中必须有多次卡团单
        if (timesDealQueryList.stream().noneMatch(ProductM::isTimesDeal)) {
            return Lists.newArrayList();
        }
        // 列表中必须有单次卡团单
        if (timesDealQueryList.stream().allMatch(ProductM::isTimesDeal)) {
            return Lists.newArrayList();
        }
        // 当前团单是多次卡团单，但是发生了倒挂,整个多次卡模块都不展示
        ProductM currentProductM = getCurrentProductM(timesDealQueryList, currentDealId);
        return timesDealQueryList.stream()
                .filter(productM -> productM.getProductId() != currentDealId)
                // 过滤掉倒挂的团单
                .filter(productM -> !TimesDealUtil.timesDealHangUpDown(currentProductM, productM))
                .sorted(Comparator.comparing(ProductM::getSalePrice))
                .collect(Collectors.toList());
    }

    
    protected boolean compareServiceType(ProductM productM, String comparedServiceType) {
        String serviceType = DealDetailUtils.getAttrSingleValueByAttrName(productM.getExtAttrs(), SERVICE_TYPE);
        return StringUtils.equals(comparedServiceType, serviceType);
    }

    /**
     * 按照销量排序，由高到低
     */
    protected Comparator<ProductM> sortSale() {
        return (o1, o2) -> o2.getSale().getSale() - o1.getSale().getSale();
    }

    protected List<ProductM> sortProductBySale(List<ProductM> list) {
        return list.stream().sorted(sortSale()).collect(Collectors.toList());
    }

    @Getter
    @AllArgsConstructor
    enum AvailableEnum {

        ALL_TIME_AVAILABLE("全部时间可用"),

        ONLY_MID_WEEK_AVAILABLE("仅周中可用"),

        ONLY_WEEK_AVAILABLE("仅周末可用");

        private final String text;
    }

    static class WeekConstants {

        public static final Set<Integer> ALL_DAYS = Sets.newHashSet(1, 2, 3, 4, 5, 6, 7);

        public static final Set<Integer> MID_WEEK_DAYS = Sets.newHashSet(1, 2, 3, 4, 5);

        public static final Set<Integer> WEEK_DAYS = Sets.newHashSet(6, 7);

    }
}
