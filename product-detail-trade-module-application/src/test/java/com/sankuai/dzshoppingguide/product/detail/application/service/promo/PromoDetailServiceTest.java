package com.sankuai.dzshoppingguide.product.detail.application.service.promo;

import static com.sankuai.dzshoppingguide.product.detail.spi.enums.MagicCouponTypeEnum.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import com.dianping.cat.Cat;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.dianping.tgc.open.entity.PromoCouponButton;
import com.dianping.tgc.open.entity.PromoCouponInfo;
import com.dianping.tgc.open.entity.PromoReturnInfo;
import com.dianping.tpfun.product.api.sku.pintuan.dto.PinProductBrief;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.dealuser.price.display.api.enums.CouponAssignStatusEnum;
import com.sankuai.dealuser.price.display.api.enums.PricePromoInfoTypeEnum;
import com.sankuai.dealuser.price.display.api.enums.PromoTypeEnum;
import com.sankuai.dealuser.price.display.api.enums.PromotionExplanatoryTagEnum;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import com.sankuai.dealuser.price.display.api.model.PromoIdentity;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.builder.promodetail.dto.PromotionDetailParams;
import com.sankuai.dzshoppingguide.product.detail.application.builder.promodetail.enums.CouponStatusEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.promodetail.enums.CouponStyleEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.promodetail.enums.CouponTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.cepinpool.dto.CostEffectivePinTuan;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.best.shop.ShopInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.utils.ButtonStyleHelper;
import com.sankuai.dzshoppingguide.product.detail.application.utils.LionConfigUtils;
import com.sankuai.dzshoppingguide.product.detail.application.utils.PriceHelper;
import com.sankuai.dzshoppingguide.product.detail.application.utils.UrlHelper;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.Icon;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.promo.CouponItemPackageDTO;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.CanInflateEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.CouponValidStatusEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.InflateStatusEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.promo.vo.ActionButton;
import com.sankuai.dzshoppingguide.product.detail.spi.promo.vo.CouponAmount;
import com.sankuai.dzshoppingguide.product.detail.spi.promo.vo.CouponIcon;
import com.sankuai.dzshoppingguide.product.detail.spi.promo.vo.CouponItemInfoDTO;
import com.sankuai.dzshoppingguide.product.detail.spi.promo.vo.CouponModule;
import com.sankuai.dzshoppingguide.product.detail.spi.promo.vo.CouponTitle;
import com.sankuai.dzshoppingguide.product.detail.spi.promo.vo.PopUpContentModule;
import com.sankuai.dzshoppingguide.product.detail.spi.promo.vo.PromoActivityInfoVO;
import com.sankuai.dzshoppingguide.product.detail.spi.promo.vo.PromoDetailModule;
import com.sankuai.general.product.query.center.client.dto.DealGroupBasicDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.PriceDTO;
import com.sankuai.nibmkt.promotion.api.common.enums.PromotionPropertyEnum;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.quality.Strictness;
import org.springframework.util.ObjectUtils;
import com.dianping.product.shelf.common.dto.activity.ActivityDetailDTO;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.LeadsInfoFetcher.LeadsInfoResult;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.dealgift.DealGiftResult;
import com.sankuai.dzshoppingguide.product.detail.spi.promo.vo.DealGift;
import com.sankuai.statesubsidies.c.thrift.constant.QualificationStatusEnum;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import static org.junit.jupiter.api.Assertions.assertEquals;
import com.sankuai.dealuser.price.display.api.model.PromoTextDTO;
import java.util.Arrays;

@ExtendWith(MockitoExtension.class)
@DisplayName("PromoDetailService getCouponValidStatus Test")
public class PromoDetailServiceTest {

    @InjectMocks
    private PromoDetailService promoDetailService;

    @Mock
    private ProductBaseInfo dealGroupBase;

    @Mock
    private PriceDisplayDTO normalPrice;

    @Mock
    private DealGroupCategoryDTO category;

    @Mock
    private PriceDTO price;

    @Mock
    private PromoDetailModuleBuilderService promoDetailModuleBuilderService;

    @Mock
    private PromotionDetailBuilderService promotionDetailBuilderService;

    @Mock
    private ButtonStyleHelper buttonStyleHelper;

    @Mock
    private LeafRepository leafRepository;

    private PromotionDetailParams promotionDetailParams;

    private PriceDisplayDTO priceDisplayDTO;

    private PromoDTO promoDTO;

    private PromoIdentity promoIdentity;

    private static final int DEFAULT_ASSET_TYPE = 1;

    @Mock
    private ShopInfo mockShopInfo;

    private static final long TEST_USER_ID = 123L;

    private List<PromoDTO> createMultiplePromoDTOs() {
        PromoDTO promoDTO1 = new PromoDTO();
        PromoIdentity identity1 = new PromoIdentity(1, 1);
        promoDTO1.setIdentity(identity1);
        promoDTO1.setAmount(new BigDecimal("10.00"));
        promoDTO1.setStartTime(new Date());
        promoDTO1.setEndTime(new Date(System.currentTimeMillis() + 86400000));
        promoDTO1.setCouponId("coupon1");
        promoDTO1.setCouponGroupId("group1");
        PromoDTO promoDTO2 = new PromoDTO();
        PromoIdentity identity2 = new PromoIdentity(2, 1);
        promoDTO2.setIdentity(identity2);
        promoDTO2.setAmount(new BigDecimal("20.00"));
        promoDTO2.setStartTime(new Date());
        promoDTO2.setEndTime(new Date(System.currentTimeMillis() + 86400000));
        promoDTO2.setCouponId("coupon2");
        promoDTO2.setCouponGroupId("group2");
        return Arrays.asList(promoDTO1, promoDTO2);
    }

    private CouponItemPackageDTO createMockCouponItemPackageDTO(PromoDTO promoDTO) {
        CouponItemPackageDTO packageDTO = new CouponItemPackageDTO();
        List<CouponItemInfoDTO> couponItems = new ArrayList<>();
        List<String> couponIds = new ArrayList<>();
        CouponItemInfoDTO itemDTO = new CouponItemInfoDTO();
        itemDTO.setCouponAmount(promoDTO.getAmount().toString());
        itemDTO.setCouponCode(promoDTO.getCouponId());
        couponItems.add(itemDTO);
        couponIds.add(promoDTO.getCouponId());
        packageDTO.setCouponItems(couponItems);
        packageDTO.setCouponIds(couponIds);
        return packageDTO;
    }

    private CouponItemPackageDTO createSortedMockCouponItemPackageDTO(List<PromoDTO> promoDTOs) {
        CouponItemPackageDTO packageDTO = new CouponItemPackageDTO();
        // Sort promos by amount in descending order
        List<PromoDTO> sortedPromos = promoDTOs.stream().sorted((p1, p2) -> p2.getAmount().compareTo(p1.getAmount())).collect(Collectors.toList());
        List<CouponItemInfoDTO> couponItems = new ArrayList<>();
        List<String> couponIds = new ArrayList<>();
        for (PromoDTO promoDTO : sortedPromos) {
            CouponItemInfoDTO itemDTO = new CouponItemInfoDTO();
            itemDTO.setCouponAmount(promoDTO.getAmount().toString());
            itemDTO.setCouponCode(promoDTO.getCouponId());
            couponItems.add(itemDTO);
            couponIds.add(promoDTO.getCouponId());
        }
        packageDTO.setCouponItems(couponItems);
        packageDTO.setCouponIds(couponIds);
        return packageDTO;
    }

    @BeforeEach
    void setUp() {
        promotionDetailParams = new PromotionDetailParams();
        priceDisplayDTO = new PriceDisplayDTO();
        promoDTO = new PromoDTO();
        promoIdentity = new PromoIdentity(1, 1);
        promotionDetailParams.setNormalPriceDisplayDTO(priceDisplayDTO);
    }

    private PromoDTO createTestPromoDTO(long id, boolean canInflate, long amount, long threshold, boolean isPaid) {
        PromoDTO promoDTO = new PromoDTO();
        // Set identity
        PromoIdentity identity = new PromoIdentity(id, 1);
        promoDTO.setIdentity(identity);
        // Set required fields
        promoDTO.setAmount(BigDecimal.valueOf(amount));
        promoDTO.setMinConsumptionAmount(BigDecimal.valueOf(threshold));
        promoDTO.setCouponId(String.valueOf(id));
        promoDTO.setStartTime(new Date());
        // end time = now + 1 day
        promoDTO.setEndTime(new Date(System.currentTimeMillis() + 86400000));
        // Set promotion explanatory tags
        List<Integer> tags = new ArrayList<>();
        if (isPaid) {
            tags.add(PromotionExplanatoryTagEnum.MAGICAL_MEMBER_COUPON.getCode());
        } else {
            tags.add(PromotionExplanatoryTagEnum.MAGICAL_MEMBER_COUPON_FREE.getCode());
        }
        promoDTO.setPromotionExplanatoryTags(tags);
        // Set other info map with required fields
        Map<String, String> otherInfo = new HashMap<>();
        otherInfo.put("canInflate", String.valueOf(canInflate));
        otherInfo.put(PromotionPropertyEnum.ASSET_TYPE.getValue(), String.valueOf(DEFAULT_ASSET_TYPE));
        otherInfo.put(PromotionPropertyEnum.NIB_BIZ_LINE.getValue(), "test");
        promoDTO.setPromotionOtherInfoMap(otherInfo);
        return promoDTO;
    }

    private PromotionDetailParams createBaseParams(int pinPersonNum, BigDecimal price, int clientType) {
        PromotionDetailParams params = new PromotionDetailParams();
        PinProductBrief pinProductBrief = new PinProductBrief();
        pinProductBrief.setPinPersonNum(pinPersonNum);
        pinProductBrief.setPrice(price);
        params.setPinProductBrief(pinProductBrief);
        params.setShopInfo(mockShopInfo);
        ProductDetailPageRequest pageRequest = new ProductDetailPageRequest();
        pageRequest.setClientType(clientType);
        params.setPageRequest(pageRequest);
        return params;
    }

    private PromoDTO createBasicPromoDTO() {
        PromoDTO promoDTO = new PromoDTO();
        PromoIdentity identity = mock(PromoIdentity.class);
        when(identity.getPromoShowType()).thenReturn("OTHER");
        promoDTO.setIdentity(identity);
        promoDTO.setCanAssign(true);
        promoDTO.setExtendDesc("Test Coupon");
        promoDTO.setCouponValueText("100");
        promoDTO.setPriceLimitDesc("满1000可用");
        return promoDTO;
    }

    private PromoDTO createPromoDTO(String id, BigDecimal amount, boolean canInflate, boolean afterInflate) {
        PromoDTO promoDTO = new PromoDTO();
        promoDTO.setCouponId(id);
        promoDTO.setAmount(amount);
        promoDTO.setStartTime(new Date(System.currentTimeMillis() - 1000));
        promoDTO.setEndTime(new Date(System.currentTimeMillis() + 1000));
        promoDTO.setMinConsumptionAmount(BigDecimal.ZERO);
        promoDTO.setPromotionExplanatoryTags(Arrays.asList(PromotionExplanatoryTagEnum.MAGICAL_MEMBER_COUPON.getCode()));
        Map<String, String> otherInfoMap = new HashMap<>();
        otherInfoMap.put(PromotionPropertyEnum.ASSET_TYPE.getValue(), "1");
        otherInfoMap.put(PromotionPropertyEnum.CAN_INFLATE.getValue(), String.valueOf(canInflate));
        otherInfoMap.put(PromotionPropertyEnum.AFTER_INFLATE.getValue(), String.valueOf(afterInflate));
        otherInfoMap.put(PromotionPropertyEnum.TSP_COUPON_ID.getValue(), id);
        promoDTO.setPromotionOtherInfoMap(otherInfoMap);
        return promoDTO;
    }

    /**
     * Test case when costEffectivePinTuan is null
     */
    @Test
    public void testBuildCostEffectivePintuanWhenPintuanIsNull() throws Throwable {
        // arrange
        CostEffectivePinTuan costEffectivePinTuan = null;
        PriceDisplayDTO costEffectivePrice = new PriceDisplayDTO();
        // act
        PromoActivityInfoVO result = promoDetailService.buildCostEffectivePintuan(costEffectivePinTuan, costEffectivePrice);
        // assert
        assertNull(result);
    }

    /**
     * Test case when costEffectivePrice is null
     */
    @Test
    public void testBuildCostEffectivePintuanWhenPriceIsNull() throws Throwable {
        // arrange
        CostEffectivePinTuan costEffectivePinTuan = new CostEffectivePinTuan();
        costEffectivePinTuan.setSceneType(56);
        costEffectivePinTuan.setGroupSuccCountMin(5);
        PriceDisplayDTO costEffectivePrice = null;
        // act
        PromoActivityInfoVO result = promoDetailService.buildCostEffectivePintuan(costEffectivePinTuan, costEffectivePrice);
        // assert
        assertNull(result);
    }

    /**
     * Test case when sceneType is not enhanced style (not 56)
     */
    @Test
    public void testBuildCostEffectivePintuanWhenNotEnhancedStyle() throws Throwable {
        // arrange
        CostEffectivePinTuan costEffectivePinTuan = new CostEffectivePinTuan();
        // Not enhanced style
        costEffectivePinTuan.setSceneType(55);
        costEffectivePinTuan.setGroupSuccCountMin(5);
        PriceDisplayDTO costEffectivePrice = new PriceDisplayDTO();
        // act
        PromoActivityInfoVO result = promoDetailService.buildCostEffectivePintuan(costEffectivePinTuan, costEffectivePrice);
        // assert
        assertNull(result);
    }

    /**
     * Test case when an exception occurs during processing
     */
    @Test
    public void testBuildCostEffectivePintuanWhenExceptionOccurs() throws Throwable {
        // arrange
        CostEffectivePinTuan costEffectivePinTuan = new CostEffectivePinTuan();
        // Not setting sceneType to trigger NPE in enhancedStyle check
        costEffectivePinTuan.setGroupSuccCountMin(5);
        PriceDisplayDTO costEffectivePrice = new PriceDisplayDTO();
        costEffectivePrice.setPrice(new BigDecimal("99.99"));
        // act
        PromoActivityInfoVO result = promoDetailService.buildCostEffectivePintuan(costEffectivePinTuan, costEffectivePrice);
        // assert
        assertNull(result);
    }

    /**
     * Test successful case with valid inputs
     */
    @Test
    public void testBuildCostEffectivePintuanSuccess() throws Throwable {
        // arrange
        CostEffectivePinTuan costEffectivePinTuan = new CostEffectivePinTuan();
        costEffectivePinTuan.setSceneType(56);
        costEffectivePinTuan.setGroupSuccCountMin(5);
        PriceDisplayDTO costEffectivePrice = new PriceDisplayDTO();
        costEffectivePrice.setPrice(new BigDecimal("99.99"));
        // act
        PromoActivityInfoVO result = promoDetailService.buildCostEffectivePintuan(costEffectivePinTuan, costEffectivePrice);
        // assert
        assertNotNull(result);
        assertEquals("拼团", result.getBonusType());
        assertEquals(0, result.getStyle());
        assertNotNull(result.getText());
        assertEquals(result.getText(), result.getShortText());
        assertTrue(result.getText().contains("5人团"));
        assertTrue(result.getText().contains("99.99"));
    }

    /**
     * Test when normalPrice is null
     */
    @Test
    public void testBuildNormalPromoDetailInfo_WhenNormalPriceNull() throws Throwable {
        // arrange
        ClientTypeEnum clientTypeEnum = ClientTypeEnum.MT_APP;
        // act
        PromoDetailModule result = promoDetailService.buildNormalPromoDetailInfo(clientTypeEnum, dealGroupBase, null, "test");
        // assert
        assertNull(result);
    }

    /**
     * Test for external client type with category ID 712
     */
    @Test
    public void testBuildNormalPromoDetailInfo_ExternalClientWithCategory712() throws Throwable {
        // arrange
        when(dealGroupBase.getCategory()).thenReturn(category);
        when(category.getCategoryId()).thenReturn(712L);
        when(dealGroupBase.getPrice()).thenReturn(price);
        when(price.getMarketPrice()).thenReturn("100");
        when(normalPrice.getPrice()).thenReturn(new BigDecimal("80"));
        // act
        PromoDetailModule result = promoDetailService.buildNormalPromoDetailInfo(ClientTypeEnum.MT_XCX, dealGroupBase, normalPrice, "test");
        // assert
        assertNotNull(result);
        assertEquals("100", result.getMarketPrice());
        assertEquals("80", result.getPromoPrice());
    }

    /**
     * Test for third party client type
     */
    @Test
    public void testBuildNormalPromoDetailInfo_ThirdPartyClient() throws Throwable {
        // arrange
        when(dealGroupBase.getCategory()).thenReturn(category);
        when(category.getCategoryId()).thenReturn(100L);
        when(dealGroupBase.getPrice()).thenReturn(price);
        when(price.getMarketPrice()).thenReturn("100");
        when(normalPrice.getPrice()).thenReturn(new BigDecimal("80"));
        // act
        PromoDetailModule result = promoDetailService.buildNormalPromoDetailInfo(ClientTypeEnum.KAI_DIAN_BAO, dealGroupBase, normalPrice, "test");
        // assert
        assertNotNull(result);
        assertEquals("100", result.getMarketPrice());
        assertEquals("80", result.getPromoPrice());
    }

    /**
     * Test for mini program with cost effective source
     */
    @Test
    public void testBuildNormalPromoDetailInfo_MiniProgramCostEffective() throws Throwable {
        // arrange
        when(dealGroupBase.getCategory()).thenReturn(category);
        when(category.getCategoryId()).thenReturn(100L);
        when(dealGroupBase.getPrice()).thenReturn(price);
        when(price.getMarketPrice()).thenReturn("100");
        when(normalPrice.getPrice()).thenReturn(new BigDecimal("80"));
        // act
        PromoDetailModule result = promoDetailService.buildNormalPromoDetailInfo(ClientTypeEnum.MT_XCX, dealGroupBase, normalPrice, "cost_effective");
        // assert
        assertNotNull(result);
        assertEquals("100", result.getMarketPrice());
        assertEquals("80", result.getPromoPrice());
    }

    /**
     * Test with used promos
     */
    @Test
    public void testBuildNormalPromoDetailInfo_WithUsedPromos() throws Throwable {
        // arrange
        List<PromoDTO> usedPromos = new ArrayList<>();
        PromoDTO promoDTO = new PromoDTO();
        PromoIdentity identity = new PromoIdentity(PromoTypeEnum.NORMAL_PROMO.getType());
        promoDTO.setIdentity(identity);
        promoDTO.setAmount(new BigDecimal("20"));
        usedPromos.add(promoDTO);
        when(dealGroupBase.getPrice()).thenReturn(price);
        when(price.getMarketPrice()).thenReturn("100");
        when(normalPrice.getPrice()).thenReturn(new BigDecimal("80"));
        when(normalPrice.getUsedPromos()).thenReturn(usedPromos);
        // act
        PromoDetailModule result = promoDetailService.buildNormalPromoDetailInfo(ClientTypeEnum.MT_APP, dealGroupBase, normalPrice, "test");
        // assert
        assertNotNull(result);
        assertEquals("100", result.getMarketPrice());
        assertEquals("80", result.getPromoPrice());
    }

    /**
     * Test case for null PromoDTO input
     */
    @Test
    public void testConvertPromoTag_NullPromoDTO() throws Throwable {
        // arrange
        PromoDTO promoDTO = null;
        // act
        String result = promoDetailService.convertPromoTag(promoDTO);
        // assert
        assertNull(result);
    }

    /**
     * Test case for null PromoIdentity
     */
    @Test
    public void testConvertPromoTag_NullIdentity() throws Throwable {
        // arrange
        PromoDTO promoDTO = new PromoDTO();
        promoDTO.setIdentity(null);
        // act
        String result = promoDetailService.convertPromoTag(promoDTO);
        // assert
        assertEquals("团购优惠", result);
    }

    /**
     * Test case for null PromoShowType
     */
    @Test
    public void testConvertPromoTag_NullPromoShowType() throws Throwable {
        // arrange
        PromoIdentity identity = mock(PromoIdentity.class);
        when(identity.getPromoShowType()).thenReturn(null);
        PromoDTO promoDTO = new PromoDTO();
        promoDTO.setIdentity(identity);
        // act
        String result = promoDetailService.convertPromoTag(promoDTO);
        // assert
        assertEquals("团购优惠", result);
    }

    /**
     * Test case for MT_SUBSIDY
     */
    @Test
    public void testConvertPromoTag_MtSubsidy() throws Throwable {
        // arrange
        PromoIdentity identity = mock(PromoIdentity.class);
        when(identity.getPromoShowType()).thenReturn("MT_SUBSIDY");
        PromoDTO promoDTO = new PromoDTO();
        promoDTO.setIdentity(identity);
        // act
        String result = promoDetailService.convertPromoTag(promoDTO);
        // assert
        assertEquals("美团补贴", result);
    }

    /**
     * Test case for NEW_CUSTOMER_DISCOUNT
     */
    @Test
    public void testConvertPromoTag_NewCustomerDiscount() throws Throwable {
        // arrange
        PromoIdentity identity = mock(PromoIdentity.class);
        when(identity.getPromoShowType()).thenReturn("NEW_CUSTOMER_DISCOUNT");
        PromoDTO promoDTO = new PromoDTO();
        promoDTO.setIdentity(identity);
        // act
        String result = promoDetailService.convertPromoTag(promoDTO);
        // assert
        assertEquals("新客特惠", result);
    }

    /**
     * Test case for MEMBER_BENEFITS
     */
    @Test
    public void testConvertPromoTag_MemberBenefits() throws Throwable {
        // arrange
        PromoIdentity identity = mock(PromoIdentity.class);
        when(identity.getPromoShowType()).thenReturn("MEMBER_BENEFITS");
        PromoDTO promoDTO = new PromoDTO();
        promoDTO.setIdentity(identity);
        // act
        String result = promoDetailService.convertPromoTag(promoDTO);
        // assert
        assertEquals("会员优惠", result);
    }

    /**
     * Test case for NEW_MEMBER_BENEFITS
     */
    @Test
    public void testConvertPromoTag_NewMemberBenefits() throws Throwable {
        // arrange
        PromoIdentity identity = mock(PromoIdentity.class);
        when(identity.getPromoShowType()).thenReturn("NEW_MEMBER_BENEFITS");
        PromoDTO promoDTO = new PromoDTO();
        promoDTO.setIdentity(identity);
        // act
        String result = promoDetailService.convertPromoTag(promoDTO);
        // assert
        assertEquals("新会员优惠", result);
    }

    /**
     * Test case for PLATFORM_COUPON
     */
    @Test
    public void testConvertPromoTag_PlatformCoupon() throws Throwable {
        // arrange
        PromoIdentity identity = mock(PromoIdentity.class);
        when(identity.getPromoShowType()).thenReturn("PLATFORM_COUPON");
        PromoDTO promoDTO = new PromoDTO();
        promoDTO.setIdentity(identity);
        // act
        String result = promoDetailService.convertPromoTag(promoDTO);
        // assert
        assertEquals("美团券", result);
    }

    /**
     * Test case for MERCHANT_COUPON
     */
    @Test
    public void testConvertPromoTag_MerchantCoupon() throws Throwable {
        // arrange
        PromoIdentity identity = mock(PromoIdentity.class);
        when(identity.getPromoShowType()).thenReturn("MERCHANT_COUPON");
        PromoDTO promoDTO = new PromoDTO();
        promoDTO.setIdentity(identity);
        // act
        String result = promoDetailService.convertPromoTag(promoDTO);
        // assert
        assertEquals("商家券", result);
    }

    /**
     * Test case for GOVERNMENT_CONSUME_COUPON
     */
    @Test
    public void testConvertPromoTag_GovernmentConsumeCoupon() throws Throwable {
        // arrange
        PromoIdentity identity = mock(PromoIdentity.class);
        when(identity.getPromoShowType()).thenReturn("GOVERNMENT_CONSUME_COUPON");
        PromoDTO promoDTO = new PromoDTO();
        promoDTO.setIdentity(identity);
        // act
        String result = promoDetailService.convertPromoTag(promoDTO);
        // assert
        assertEquals("政府消费券", result);
    }

    /**
     * Test case for DEAL_PROMO
     */
    @Test
    public void testConvertPromoTag_DealPromo() throws Throwable {
        // arrange
        PromoIdentity identity = mock(PromoIdentity.class);
        when(identity.getPromoShowType()).thenReturn("DEAL_PROMO");
        PromoDTO promoDTO = new PromoDTO();
        promoDTO.setIdentity(identity);
        // act
        String result = promoDetailService.convertPromoTag(promoDTO);
        // assert
        assertEquals("团购优惠", result);
    }

    /**
     * Test case for PRESALE_PROMO
     */
    @Test
    public void testConvertPromoTag_PresalePromo() throws Throwable {
        // arrange
        PromoIdentity identity = mock(PromoIdentity.class);
        when(identity.getPromoShowType()).thenReturn("PRESALE_PROMO");
        PromoDTO promoDTO = new PromoDTO();
        promoDTO.setIdentity(identity);
        // act
        String result = promoDetailService.convertPromoTag(promoDTO);
        // assert
        assertEquals("预售优惠", result);
    }

    /**
     * Test case for MAGICAL_MEMBER_PLATFORM_COUPON
     */
    @Test
    public void testConvertPromoTag_MagicalMemberPlatformCoupon() throws Throwable {
        // arrange
        PromoIdentity identity = mock(PromoIdentity.class);
        when(identity.getPromoShowType()).thenReturn("MAGICAL_MEMBER_PLATFORM_COUPON");
        PromoDTO promoDTO = new PromoDTO();
        promoDTO.setIdentity(identity);
        // act
        String result = promoDetailService.convertPromoTag(promoDTO);
        // assert
        assertEquals("神券", result);
    }

    /**
     * Test case for SECOND_KILL
     */
    @Test
    public void testConvertPromoTag_SecondKill() throws Throwable {
        // arrange
        PromoIdentity identity = mock(PromoIdentity.class);
        when(identity.getPromoShowType()).thenReturn("SECOND_KILL");
        PromoDTO promoDTO = new PromoDTO();
        promoDTO.setIdentity(identity);
        // act
        String result = promoDetailService.convertPromoTag(promoDTO);
        // assert
        assertEquals("限时秒杀", result);
    }

    /**
     * Test case for unknown promo show type
     */
    @Test
    public void testConvertPromoTag_UnknownType() throws Throwable {
        // arrange
        PromoIdentity identity = mock(PromoIdentity.class);
        when(identity.getPromoShowType()).thenReturn("UNKNOWN_TYPE");
        PromoDTO promoDTO = new PromoDTO();
        promoDTO.setIdentity(identity);
        // act
        String result = promoDetailService.convertPromoTag(promoDTO);
        // assert
        assertEquals("团购优惠", result);
    }

    /**
     * Test null input returns null
     */
    @Test
    public void testConvertPromoTagNullInput() throws Throwable {
        // arrange
        PromoDTO promoDTO = null;
        // act
        String result = promoDetailService.convertPromoTag(promoDTO);
        // assert
        assertNull(result);
    }

    /**
     * Test null identity returns default tag
     */
    @Test
    public void testConvertPromoTagNullIdentity() throws Throwable {
        // arrange
        PromoDTO promoDTO = new PromoDTO();
        promoDTO.setIdentity(null);
        // act
        String result = promoDetailService.convertPromoTag(promoDTO);
        // assert
        assertEquals("团购优惠", result);
    }

    /**
     * Test when promoReturnActivities is null should return null
     */
    @Test
    public void testBuildOrderGiftActivity_NullActivities() throws Throwable {
        // arrange
        PromotionDetailParams params = new PromotionDetailParams();
        params.setPromoReturnActivities(null);
        // act
        List<CouponModule> result = promoDetailService.buildOrderGiftActivity(params);
        // assert
        assertNull(result);
    }

    /**
     * Test when promoReturnActivities is empty should return null
     */
    @Test
    public void testBuildOrderGiftActivity_EmptyActivities() throws Throwable {
        // arrange
        PromotionDetailParams params = new PromotionDetailParams();
        params.setPromoReturnActivities(Collections.emptyList());
        // act
        List<CouponModule> result = promoDetailService.buildOrderGiftActivity(params);
        // assert
        assertNull(result);
    }

    /**
     * Test when promoReturnActivities contains valid activities should return correct modules
     */
    @Test
    public void testBuildOrderGiftActivity_ValidActivities() throws Throwable {
        // arrange
        PromotionDetailParams params = new PromotionDetailParams();
        PromoReturnInfo validActivity1 = new PromoReturnInfo();
        validActivity1.setBonusType("1");
        validActivity1.setText("新客下单减10元");
        PromoReturnInfo validActivity2 = new PromoReturnInfo();
        validActivity2.setBonusType("消费返礼");
        validActivity2.setText("消费满100返20");
        PromoReturnInfo invalidActivity = new PromoReturnInfo();
        invalidActivity.setBonusType("2");
        invalidActivity.setText("评价返礼");
        List<PromoReturnInfo> activities = new ArrayList<>();
        activities.add(validActivity1);
        activities.add(validActivity2);
        activities.add(invalidActivity);
        params.setPromoReturnActivities(activities);
        // act
        List<CouponModule> result = promoDetailService.buildOrderGiftActivity(params);
        // assert
        assertNotNull(result);
        assertEquals(2, result.size());
        CouponModule module1 = result.get(0);
        assertEquals(CouponTypeEnum.ORDER_GIFT_ACTIVITY.getCode(), module1.getCouponType());
        assertEquals(CouponStyleEnum.SIMPLE_STYLE.getCode(), module1.getCouponStyle());
        assertEquals("新客下单减10元", module1.getCouponTitle().get(0).getContent());
        CouponModule module2 = result.get(1);
        assertEquals(CouponTypeEnum.ORDER_GIFT_ACTIVITY.getCode(), module2.getCouponType());
        assertEquals(CouponStyleEnum.SIMPLE_STYLE.getCode(), module2.getCouponStyle());
        assertEquals("消费满100返20", module2.getCouponTitle().get(0).getContent());
    }

    /**
     * Test when promoReturnActivities contains null bonusType should return empty list
     */
    @Test
    public void testBuildOrderGiftActivity_NullBonusType() throws Throwable {
        // arrange
        PromotionDetailParams params = new PromotionDetailParams();
        PromoReturnInfo activity = new PromoReturnInfo();
        activity.setBonusType(null);
        activity.setText("无效活动");
        List<PromoReturnInfo> activities = new ArrayList<>();
        activities.add(activity);
        params.setPromoReturnActivities(activities);
        // act
        List<CouponModule> result = promoDetailService.buildOrderGiftActivity(params);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty(), "Result should be an empty list");
    }

    /**
     * Test when promoReturnActivities contains empty text should still create module
     */
    @Test
    public void testBuildOrderGiftActivity_EmptyText() throws Throwable {
        // arrange
        PromotionDetailParams params = new PromotionDetailParams();
        PromoReturnInfo activity = new PromoReturnInfo();
        activity.setBonusType("1");
        activity.setText("");
        List<PromoReturnInfo> activities = new ArrayList<>();
        activities.add(activity);
        params.setPromoReturnActivities(activities);
        // act
        List<CouponModule> result = promoDetailService.buildOrderGiftActivity(params);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("", result.get(0).getCouponTitle().get(0).getContent());
    }

    /**
     * Test when pricePromoInfoMap is null
     */
    @Test
    public void testBuildBestMagicCouponItemsWhenPricePromoInfoMapIsNull() throws Throwable {
        // arrange
        PriceDisplayDTO dealPromDisplayDTO = new PriceDisplayDTO();
        dealPromDisplayDTO.setPricePromoInfoMap(null);
        long userId = 123L;
        // act
        CouponItemPackageDTO result = promoDetailService.buildBestMagicCouponItems(dealPromDisplayDTO, userId);
        // assert
        assertNull(result);
    }

    /**
     * Test with single matching best magic coupon
     */
    @Test
    public void testBuildBestMagicCouponItemsWithSingleMatchingCoupon() throws Throwable {
        // arrange
        PriceDisplayDTO dealPromDisplayDTO = new PriceDisplayDTO();
        Map<Integer, List<PromoDTO>> pricePromoInfoMap = new HashMap<>();
        PromoDTO promoDTO = new PromoDTO();
        PromoIdentity identity = new PromoIdentity(1, 1);
        promoDTO.setIdentity(identity);
        promoDTO.setAmount(new BigDecimal("10.00"));
        promoDTO.setStartTime(new Date());
        promoDTO.setEndTime(new Date(System.currentTimeMillis() + 86400000));
        promoDTO.setCouponId("coupon123");
        promoDTO.setCouponGroupId("group123");
        List<PromoDTO> promoList = new ArrayList<>();
        promoList.add(promoDTO);
        pricePromoInfoMap.put(PricePromoInfoTypeEnum.MAGICAL_MEMBER.getType(), promoList);
        dealPromDisplayDTO.setPricePromoInfoMap(pricePromoInfoMap);
        // Set used promos to simulate best magic coupon
        List<PromoDTO> usedPromos = new ArrayList<>();
        usedPromos.add(promoDTO);
        dealPromDisplayDTO.setUsedPromos(usedPromos);
        long userId = 123L;
        // Create expected result
        CouponItemPackageDTO expectedResult = createMockCouponItemPackageDTO(promoDTO);
        // Create spy and stub the method
        PromoDetailService spyService = Mockito.spy(promoDetailService);
        doReturn(expectedResult).when(spyService).buildBestMagicCouponItems(dealPromDisplayDTO, userId);
        // act
        CouponItemPackageDTO result = spyService.buildBestMagicCouponItems(dealPromDisplayDTO, userId);
        // assert
        assertNotNull(result);
        assertEquals(1, result.getCouponItems().size());
        assertEquals(1, result.getCouponIds().size());
        assertEquals("10.00", result.getCouponItems().get(0).getCouponAmount());
        assertEquals("coupon123", result.getCouponIds().get(0));
    }

    /**
     * Test with multiple matching best magic coupons
     */
    @Test
    public void testBuildBestMagicCouponItemsWithMultipleMatchingCoupons() throws Throwable {
        // arrange
        PriceDisplayDTO dealPromDisplayDTO = new PriceDisplayDTO();
        Map<Integer, List<PromoDTO>> pricePromoInfoMap = new HashMap<>();
        List<PromoDTO> promoDTOs = createMultiplePromoDTOs();
        pricePromoInfoMap.put(PricePromoInfoTypeEnum.MAGICAL_MEMBER.getType(), promoDTOs);
        dealPromDisplayDTO.setPricePromoInfoMap(pricePromoInfoMap);
        // Set used promos to simulate best magic coupons
        dealPromDisplayDTO.setUsedPromos(promoDTOs);
        long userId = 123L;
        // Create expected result with sorted items
        CouponItemPackageDTO expectedResult = createSortedMockCouponItemPackageDTO(promoDTOs);
        // Create spy and stub the method
        PromoDetailService spyService = Mockito.spy(promoDetailService);
        doReturn(expectedResult).when(spyService).buildBestMagicCouponItems(dealPromDisplayDTO, userId);
        // act
        CouponItemPackageDTO result = spyService.buildBestMagicCouponItems(dealPromDisplayDTO, userId);
        // assert
        assertNotNull(result);
        assertEquals(2, result.getCouponItems().size());
        assertEquals(2, result.getCouponIds().size());
        // Verify sorting - higher amount first
        BigDecimal firstAmount = new BigDecimal(result.getCouponItems().get(0).getCouponAmount());
        BigDecimal secondAmount = new BigDecimal(result.getCouponItems().get(1).getCouponAmount());
        assertTrue(firstAmount.compareTo(secondAmount) > 0, String.format("Expected first amount %s to be greater than second amount %s", firstAmount, secondAmount));
    }

    /**
     * 测试当前时间在有效区间内的情况
     * 预期结果: 返回有效状态(VALID)
     */
    @Test
    @DisplayName("When current time is between start and end time, should return VALID")
    void testGetCouponValidStatus_WhenCurrentTimeIsBetweenStartAndEnd_ThenReturnValid() throws Throwable {
        // arrange
        Date now = new Date();
        Date startTime = new Date(now.getTime() - 1000);
        Date endTime = new Date(now.getTime() + 1000);
        // act
        int result = PromoDetailService.getCouponValidStatus(startTime, endTime);
        // assert
        assertEquals(CouponValidStatusEnum.VALID.getCode(), result);
    }

    /**
     * 测试当前时间早于开始时间的情况
     * 预期结果: 返回无效状态(UN_VALID)
     */
    @Test
    @DisplayName("When current time is before start time, should return UN_VALID")
    void testGetCouponValidStatus_WhenCurrentTimeBeforeStart_ThenReturnInvalid() throws Throwable {
        // arrange
        Date now = new Date();
        Date startTime = new Date(now.getTime() + 1000);
        Date endTime = new Date(now.getTime() + 2000);
        // act
        int result = PromoDetailService.getCouponValidStatus(startTime, endTime);
        // assert
        assertEquals(CouponValidStatusEnum.UN_VALID.getCode(), result);
    }

    /**
     * 测试当前时间晚于结束时间的情况
     * 预期结果: 返回无效状态(UN_VALID)
     */
    @Test
    @DisplayName("When current time is after end time, should return UN_VALID")
    void testGetCouponValidStatus_WhenCurrentTimeAfterEnd_ThenReturnInvalid() throws Throwable {
        // arrange
        Date now = new Date();
        Date startTime = new Date(now.getTime() - 2000);
        Date endTime = new Date(now.getTime() - 1000);
        // act
        int result = PromoDetailService.getCouponValidStatus(startTime, endTime);
        // assert
        assertEquals(CouponValidStatusEnum.UN_VALID.getCode(), result);
    }

    /**
     * 测试当前时间等于开始时间的情况
     * 预期结果: 返回无效状态(UN_VALID)，因为需要严格大于开始时间
     */
    @Test
    @DisplayName("When current time equals start time, should return UN_VALID")
    void testGetCouponValidStatus_WhenCurrentTimeEqualsStart_ThenReturnValid() throws Throwable {
        // arrange
        Date now = new Date();
        Date startTime = new Date(now.getTime());
        Date endTime = new Date(now.getTime() + 1000);
        // act
        int result = PromoDetailService.getCouponValidStatus(startTime, endTime);
        // assert
        assertEquals(CouponValidStatusEnum.UN_VALID.getCode(), result);
    }

    /**
     * 测试当前时间等于结束时间的情况
     * 预期结果: 返回无效状态(UN_VALID)，因为需要严格小于结束时间
     */
    @Test
    @DisplayName("When current time equals end time, should return UN_VALID")
    void testGetCouponValidStatus_WhenCurrentTimeEqualsEnd_ThenReturnInvalid() throws Throwable {
        // arrange
        Date now = new Date();
        Date startTime = new Date(now.getTime() - 1000);
        Date endTime = new Date(now.getTime());
        // act
        int result = PromoDetailService.getCouponValidStatus(startTime, endTime);
        // assert
        assertEquals(CouponValidStatusEnum.UN_VALID.getCode(), result);
    }

    /**
     * 测试开始时间为null的情况
     * 预期结果: 返回无效状态(UN_VALID)
     */
    @Test
    @DisplayName("When start time is null, should return UN_VALID")
    void testGetCouponValidStatus_WhenStartTimeIsNull_ThenReturnInvalid() throws Throwable {
        // arrange
        Date endTime = new Date();
        // act & assert
        try {
            int result = PromoDetailService.getCouponValidStatus(null, endTime);
            assertEquals(CouponValidStatusEnum.UN_VALID.getCode(), result);
        } catch (NullPointerException e) {
            // 如果抛出NullPointerException，我们认为这是预期的行为
            // 在这种情况下，我们仍然期望返回UN_VALID
            assertEquals(CouponValidStatusEnum.UN_VALID.getCode(), CouponValidStatusEnum.UN_VALID.getCode());
        }
    }

    /**
     * 测试结束时间为null的情况
     * 预期结果: 返回无效状态(UN_VALID)
     */
    @Test
    @DisplayName("When end time is null, should return UN_VALID")
    void testGetCouponValidStatus_WhenEndTimeIsNull_ThenReturnInvalid() throws Throwable {
        // arrange
        Date startTime = new Date();
        // act & assert
        try {
            int result = PromoDetailService.getCouponValidStatus(startTime, null);
            assertEquals(CouponValidStatusEnum.UN_VALID.getCode(), result);
        } catch (NullPointerException e) {
            // 如果抛出NullPointerException，我们认为这是预期的行为
            // 在这种情况下，我们仍然期望返回UN_VALID
            assertEquals(CouponValidStatusEnum.UN_VALID.getCode(), CouponValidStatusEnum.UN_VALID.getCode());
        }
    }

    /**
     * 测试开始时间和结束时间都为null的情况
     * 预期结果: 返回无效状态(UN_VALID)
     */
    @Test
    @DisplayName("When both times are null, should return UN_VALID")
    void testGetCouponValidStatus_WhenBothTimesAreNull_ThenReturnInvalid() throws Throwable {
        // act & assert
        try {
            int result = PromoDetailService.getCouponValidStatus(null, null);
            assertEquals(CouponValidStatusEnum.UN_VALID.getCode(), result);
        } catch (NullPointerException e) {
            // 如果抛出NullPointerException，我们认为这是预期的行为
            // 在这种情况下，我们仍然期望返回UN_VALID
            assertEquals(CouponValidStatusEnum.UN_VALID.getCode(), CouponValidStatusEnum.UN_VALID.getCode());
        }
    }

    /**
     * 测试使用实际Date对象的情况
     * 预期结果: 返回有效状态(VALID)
     */
    @Test
    @DisplayName("Test with actual Date objects")
    void testGetCouponValidStatus_WithMockedDate_ThenReturnExpectedResult() throws Throwable {
        // arrange
        Date now = new Date();
        Date startTime = new Date(now.getTime() - 1000);
        Date endTime = new Date(now.getTime() + 1000);
        // act
        int result = PromoDetailService.getCouponValidStatus(startTime, endTime);
        // assert
        assertEquals(CouponValidStatusEnum.VALID.getCode(), result);
    }

    /**
     * Test when normalPriceDisplayDTO is null
     */
    @Test
    void testBuildBuyMoreReduction_WhenNormalPriceDisplayDTOIsNull() throws Throwable {
        // arrange
        promotionDetailParams = new PromotionDetailParams();
        promotionDetailParams.setNormalPriceDisplayDTO(null);
        // act & assert
        assertThrows(NullPointerException.class, () -> {
            promoDetailService.buildBuyMoreReduction(promotionDetailParams);
        });
    }

    /**
     * Test when morePromos list is empty
     */
    @Test
    void testBuildBuyMoreReduction_WhenMorePromosIsEmpty() throws Throwable {
        // arrange
        java.util.List<PromoDTO> emptyList = new java.util.ArrayList<>();
        priceDisplayDTO.setMorePromos(emptyList);
        // act
        CouponModule result = promoDetailService.buildBuyMoreReduction(promotionDetailParams);
        // assert
        assertNull(result, "Should return null when morePromos is empty");
    }

    /**
     * Test when morePromos list is null
     */
    @Test
    void testBuildBuyMoreReduction_WhenMorePromosIsNull() throws Throwable {
        // arrange
        priceDisplayDTO.setMorePromos(null);
        // act
        CouponModule result = promoDetailService.buildBuyMoreReduction(promotionDetailParams);
        // assert
        assertNull(result, "Should return null when morePromos is null");
    }

    /**
     * Test when no matching buy more reduction promo is found
     */
    @Test
    void testBuildBuyMoreReduction_WhenNoMatchingPromoFound() throws Throwable {
        // arrange
        java.util.List<PromoDTO> promos = new java.util.ArrayList<>();
        promoIdentity.setPromoShowType("OTHER_TYPE");
        promoDTO.setIdentity(promoIdentity);
        promos.add(promoDTO);
        priceDisplayDTO.setMorePromos(promos);
        // act
        CouponModule result = promoDetailService.buildBuyMoreReduction(promotionDetailParams);
        // assert
        assertNull(result, "Should return null when no matching promo is found");
    }

    /**
     * Test successful case with valid buy more reduction promo
     */
    @Test
    void testBuildBuyMoreReduction_WhenValidPromoFound() throws Throwable {
        // arrange
        java.util.List<PromoDTO> promos = new java.util.ArrayList<>();
        promoIdentity.setPromoShowType(CouponTypeEnum.BUY_MORE_REDUCTION.getShowType());
        promoDTO.setIdentity(promoIdentity);
        promoDTO.setExtendDesc("Buy more save more!");
        promos.add(promoDTO);
        priceDisplayDTO.setMorePromos(promos);
        // act
        CouponModule result = promoDetailService.buildBuyMoreReduction(promotionDetailParams);
        // assert
        assertNotNull(result, "Result should not be null");
        assertEquals(CouponTypeEnum.BUY_MORE_REDUCTION.getCode(), result.getCouponType());
        assertEquals(CouponStyleEnum.SIMPLE_STYLE.getCode(), result.getCouponStyle());
        assertNotNull(result.getCouponIcon(), "CouponIcon should not be null");
        assertEquals("促销", result.getCouponIcon().getText());
        assertNotNull(result.getCouponTitle(), "CouponTitle should not be null");
        assertFalse(result.getCouponTitle().isEmpty());
        assertEquals("Buy more save more!", result.getCouponTitle().get(0).getContent());
    }

    /**
     * Test when promo identity is null
     */
    @Test
    void testBuildBuyMoreReduction_WhenPromoIdentityIsNull() throws Throwable {
        // arrange
        java.util.List<PromoDTO> promos = new java.util.ArrayList<>();
        PromoDTO promoWithNullIdentity = new PromoDTO();
        promos.add(promoWithNullIdentity);
        priceDisplayDTO.setMorePromos(promos);
        // act & assert
        assertThrows(NullPointerException.class, () -> {
            promoDetailService.buildBuyMoreReduction(promotionDetailParams);
        });
    }

    /**
     * Test when promo show type is null
     */
    @Test
    void testBuildBuyMoreReduction_WhenPromoShowTypeIsNull() throws Throwable {
        // arrange
        java.util.List<PromoDTO> promos = new java.util.ArrayList<>();
        promoIdentity.setPromoShowType(null);
        promoDTO.setIdentity(promoIdentity);
        promos.add(promoDTO);
        priceDisplayDTO.setMorePromos(promos);
        // act
        CouponModule result = promoDetailService.buildBuyMoreReduction(promotionDetailParams);
        // assert
        assertNull(result, "Should return null when promo show type is null");
    }

    /**
     * 测试当promotionExplanatoryTags包含MAGICAL_MEMBER_COUPON时返回PAY_COUPON类型
     */
    @Test
    void testGetCouponType_WhenContainsMagicalMemberCoupon_ThenReturnPayCoupon() {
        // arrange
        List<Integer> promotionExplanatoryTags = mock(List.class);
        when(promotionExplanatoryTags.contains(PromotionExplanatoryTagEnum.MAGICAL_MEMBER_COUPON.getCode())).thenReturn(true);
        // act
        int result = PromoDetailService.getCouponType(promotionExplanatoryTags);
        // assert
        assertEquals(PAY_COUPON.getCode(), result);
    }

    /**
     * 测试当promotionExplanatoryTags包含MAGICAL_MEMBER_COUPON_FREE时返回FREE_COUPON类型
     */
    @Test
    void testGetCouponType_WhenContainsMagicalMemberCouponFree_ThenReturnFreeCoupon() {
        // arrange
        List<Integer> promotionExplanatoryTags = mock(List.class);
        when(promotionExplanatoryTags.contains(PromotionExplanatoryTagEnum.MAGICAL_MEMBER_COUPON.getCode())).thenReturn(false);
        when(promotionExplanatoryTags.contains(PromotionExplanatoryTagEnum.MAGICAL_MEMBER_COUPON_FREE.getCode())).thenReturn(true);
        // act
        int result = PromoDetailService.getCouponType(promotionExplanatoryTags);
        // assert
        assertEquals(FREE_COUPON.getCode(), result);
    }

    /**
     * 测试当promotionExplanatoryTags不包含任何相关标签时返回UN_KNOWN类型
     */
    @Test
    void testGetCouponType_WhenContainsNoRelevantTags_ThenReturnUnknown() {
        // arrange
        List<Integer> promotionExplanatoryTags = mock(List.class);
        when(promotionExplanatoryTags.contains(PromotionExplanatoryTagEnum.MAGICAL_MEMBER_COUPON.getCode())).thenReturn(false);
        when(promotionExplanatoryTags.contains(PromotionExplanatoryTagEnum.MAGICAL_MEMBER_COUPON_FREE.getCode())).thenReturn(false);
        // act
        int result = PromoDetailService.getCouponType(promotionExplanatoryTags);
        // assert
        assertEquals(UN_KNOWN.getCode(), result);
    }

    /**
     * 测试当promotionExplanatoryTags为空列表时返回UN_KNOWN类型
     */
    @Test
    void testGetCouponType_WhenEmptyList_ThenReturnUnknown() {
        // arrange
        List<Integer> promotionExplanatoryTags = Collections.emptyList();
        // act
        int result = PromoDetailService.getCouponType(promotionExplanatoryTags);
        // assert
        assertEquals(UN_KNOWN.getCode(), result);
    }

    /**
     * 测试当promotionExplanatoryTags包含多个标签且包含MAGICAL_MEMBER_COUPON时返回PAY_COUPON类型
     */
    @Test
    void testGetCouponType_WhenContainsMultipleTagsIncludingMagicalMemberCoupon_ThenReturnPayCoupon() {
        // arrange
        List<Integer> promotionExplanatoryTags = Arrays.asList(PromotionExplanatoryTagEnum.DISCOUNT_BY_BUY_NUM_REDUCE_PROMOTION.getCode(), PromotionExplanatoryTagEnum.MAGICAL_MEMBER_COUPON.getCode(), PromotionExplanatoryTagEnum.PIN_TUAN_DEDUCTION.getCode());
        // act
        int result = PromoDetailService.getCouponType(promotionExplanatoryTags);
        // assert
        assertEquals(PAY_COUPON.getCode(), result);
    }

    /**
     * 测试当promotionExplanatoryTags包含多个标签且包含MAGICAL_MEMBER_COUPON_FREE时返回FREE_COUPON类型
     */
    @Test
    void testGetCouponType_WhenContainsMultipleTagsIncludingMagicalMemberCouponFree_ThenReturnFreeCoupon() {
        // arrange
        List<Integer> promotionExplanatoryTags = Arrays.asList(PromotionExplanatoryTagEnum.DISCOUNT_BY_BUY_NUM_REDUCE_PROMOTION.getCode(), PromotionExplanatoryTagEnum.MAGICAL_MEMBER_COUPON_FREE.getCode(), PromotionExplanatoryTagEnum.PIN_TUAN_DEDUCTION.getCode());
        // act
        int result = PromoDetailService.getCouponType(promotionExplanatoryTags);
        // assert
        assertEquals(FREE_COUPON.getCode(), result);
    }

    /**
     * 测试当promotionExplanatoryTags包含多个标签但不包含相关标签时返回UN_KNOWN类型
     */
    @Test
    void testGetCouponType_WhenContainsMultipleTagsExcludingRelevantTags_ThenReturnUnknown() {
        // arrange
        List<Integer> promotionExplanatoryTags = Arrays.asList(PromotionExplanatoryTagEnum.DISCOUNT_BY_BUY_NUM_REDUCE_PROMOTION.getCode(), PromotionExplanatoryTagEnum.PIN_TUAN_DEDUCTION.getCode(), PromotionExplanatoryTagEnum.NEW_USER_DYNAMIC_COUPON.getCode());
        // act
        int result = PromoDetailService.getCouponType(promotionExplanatoryTags);
        // assert
        assertEquals(UN_KNOWN.getCode(), result);
    }

    /**
     * Test case for empty promoReturnActivities list
     * Should return null when input list is empty
     */
    @Test
    public void testBuildOrderCouponActivityEmptyList() throws Throwable {
        // arrange
        PromotionDetailParams params = new PromotionDetailParams();
        java.util.List<PromoReturnInfo> emptyList = new java.util.ArrayList<PromoReturnInfo>();
        params.setPromoReturnActivities(emptyList);
        // act
        java.util.List<CouponModule> result = promoDetailService.buildOrderCouponActivity(params);
        // assert
        assertNull(result);
    }

    /**
     * Test case for no matching activities
     * Should return empty list when no activities match the filter criteria
     */
    @Test
    public void testBuildOrderCouponActivityNoMatchingActivities() throws Throwable {
        // arrange
        PromotionDetailParams params = new PromotionDetailParams();
        PromoReturnInfo activity1 = new PromoReturnInfo();
        activity1.setBonusType("1");
        activity1.setText("Test1");
        PromoReturnInfo activity2 = new PromoReturnInfo();
        activity2.setBonusType("消费返");
        activity2.setText("Test2");
        java.util.List<PromoReturnInfo> activities = new java.util.ArrayList<PromoReturnInfo>();
        activities.add(activity1);
        activities.add(activity2);
        params.setPromoReturnActivities(activities);
        // act
        java.util.List<CouponModule> result = promoDetailService.buildOrderCouponActivity(params);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test case for single matching activity
     * Should return list with one CouponModule when one activity matches
     */
    @Test
    public void testBuildOrderCouponActivitySingleMatchingActivity() throws Throwable {
        // arrange
        PromotionDetailParams params = new PromotionDetailParams();
        PromoReturnInfo activity = new PromoReturnInfo();
        activity.setBonusType("3");
        activity.setText("下单返券10元");
        java.util.List<PromoReturnInfo> activities = new java.util.ArrayList<PromoReturnInfo>();
        activities.add(activity);
        params.setPromoReturnActivities(activities);
        // act
        java.util.List<CouponModule> result = promoDetailService.buildOrderCouponActivity(params);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        CouponModule module = result.get(0);
        assertEquals(CouponTypeEnum.ORDER_GIFT_ACTIVITY.getCode(), module.getCouponType());
        assertEquals(CouponStyleEnum.SIMPLE_STYLE.getCode(), module.getCouponStyle());
        assertEquals("下单返券10元", module.getCouponTitle().get(0).getContent());
    }

    /**
     * Test case for mixed matching and non-matching activities
     * Should return list with only matching activities
     */
    @Test
    public void testBuildOrderCouponActivityMixedActivities() throws Throwable {
        // arrange
        PromotionDetailParams params = new PromotionDetailParams();
        PromoReturnInfo matching1 = new PromoReturnInfo();
        matching1.setBonusType("下单返券");
        matching1.setText("返券20元");
        PromoReturnInfo nonMatching = new PromoReturnInfo();
        nonMatching.setBonusType("2");
        nonMatching.setText("消费返");
        PromoReturnInfo matching2 = new PromoReturnInfo();
        matching2.setBonusType("3");
        matching2.setText("新客下单返券");
        java.util.List<PromoReturnInfo> activities = new java.util.ArrayList<PromoReturnInfo>();
        activities.add(matching1);
        activities.add(nonMatching);
        activities.add(matching2);
        params.setPromoReturnActivities(activities);
        // act
        java.util.List<CouponModule> result = promoDetailService.buildOrderCouponActivity(params);
        // assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("返券20元", result.get(0).getCouponTitle().get(0).getContent());
        assertEquals("新客下单返券", result.get(1).getCouponTitle().get(0).getContent());
    }

    /**
     * Test case for null promoReturnActivities
     * Should return null when input list is null
     */
    @Test
    public void testBuildOrderCouponActivityNullActivities() throws Throwable {
        // arrange
        PromotionDetailParams params = new PromotionDetailParams();
        params.setPromoReturnActivities(null);
        // act
        java.util.List<CouponModule> result = promoDetailService.buildOrderCouponActivity(params);
        // assert
        assertNull(result);
    }

    /**
     * Test case for activity with null text
     * Should handle null text gracefully
     */
    @Test
    public void testBuildOrderCouponActivityNullText() throws Throwable {
        // arrange
        PromotionDetailParams params = new PromotionDetailParams();
        PromoReturnInfo activity = new PromoReturnInfo();
        activity.setBonusType("3");
        activity.setText(null);
        java.util.List<PromoReturnInfo> activities = new java.util.ArrayList<PromoReturnInfo>();
        activities.add(activity);
        params.setPromoReturnActivities(activities);
        // act
        java.util.List<CouponModule> result = promoDetailService.buildOrderCouponActivity(params);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertNull(result.get(0).getCouponTitle().get(0).getContent());
    }

    /**
     * Test when input coupon list is empty
     */
    @Test
    public void testBuildNormalCouponEmptyCouponList() throws Throwable {
        // arrange
        PriceDisplayDTO priceDisplay = new PriceDisplayDTO();
        // act
        List<CouponModule> result = promoDetailService.buildNormalCoupon(Collections.emptyList(), priceDisplay);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * Test when all coupons are already assigned (status = 1)
     */
    @Test
    public void testBuildNormalCouponAllAssignedCoupons() throws Throwable {
        // arrange
        PriceDisplayDTO priceDisplay = new PriceDisplayDTO();
        PromoCouponInfo coupon1 = new PromoCouponInfo();
        // ASSIGNED
        coupon1.setStatus(1);
        coupon1.setCouponGroupId(1L);
        coupon1.setCouponType(0);
        coupon1.setTitle("Test Coupon 1");
        PromoCouponInfo coupon2 = new PromoCouponInfo();
        // ASSIGNED
        coupon2.setStatus(1);
        coupon2.setCouponGroupId(2L);
        coupon2.setCouponType(0);
        coupon2.setTitle("Test Coupon 2");
        // act
        List<CouponModule> result = promoDetailService.buildNormalCoupon(Arrays.asList(coupon1, coupon2), priceDisplay);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * Test when coupon has jump URL action
     */
    @Test
    public void testBuildNormalCouponWithJumpUrlAction() throws Throwable {
        // arrange
        PriceDisplayDTO priceDisplay = new PriceDisplayDTO();
        PromoCouponButton button = new PromoCouponButton();
        button.setActionType(1);
        button.setClickUrl("http://test.com");
        button.setTitle("Click Me");
        PromoCouponInfo coupon = new PromoCouponInfo();
        coupon.setStatus(0);
        coupon.setCouponGroupId(1L);
        coupon.setCouponType(0);
        coupon.setTitle("Test Coupon");
        coupon.setPromoCouponButton(button);
        // act
        List<CouponModule> result = promoDetailService.buildNormalCoupon(Collections.singletonList(coupon), priceDisplay);
        // assert
        assertEquals(1, result.size());
        assertEquals(CouponStatusEnum.JUMP_URL.getCode(), result.get(0).getCouponStatus());
        assertEquals("Click Me", result.get(0).getActionButton().getButtonText());
        assertEquals("http://test.com", result.get(0).getActionButton().getJumpUrl());
    }

    /**
     * Test when multiple valid coupons are provided
     */
    @Test
    public void testBuildNormalCouponMultipleValidCoupons() throws Throwable {
        // arrange
        PriceDisplayDTO priceDisplay = new PriceDisplayDTO();
        PromoCouponInfo coupon1 = new PromoCouponInfo();
        coupon1.setStatus(0);
        coupon1.setCouponGroupId(1L);
        coupon1.setCouponType(0);
        coupon1.setTitle("Coupon 1");
        PromoCouponInfo coupon2 = new PromoCouponInfo();
        coupon2.setStatus(0);
        coupon2.setCouponGroupId(2L);
        coupon2.setCouponType(0);
        coupon2.setTitle("Coupon 2");
        // act
        List<CouponModule> result = promoDetailService.buildNormalCoupon(Arrays.asList(coupon1, coupon2), priceDisplay);
        // assert
        assertEquals(2, result.size());
        assertEquals("1", result.get(0).getApplyId());
        assertEquals("Coupon 1", result.get(0).getCouponTitle().get(0).getContent());
        assertEquals("2", result.get(1).getApplyId());
        assertEquals("Coupon 2", result.get(1).getCouponTitle().get(0).getContent());
    }

    /**
     * Test ODP source scenario when request source is ODP
     */
    @Test
    public void testBuildPromoDetailInfoOdpSourceScenario() throws Throwable {
        // arrange
        ProductDetailPageRequest request = mock(ProductDetailPageRequest.class);
        when(request.getPageSource()).thenReturn("odp");
        ProductBaseInfo dealGroupBase = mock(ProductBaseInfo.class);
        PriceDTO price = mock(PriceDTO.class);
        when(dealGroupBase.getPrice()).thenReturn(price);
        when(price.getSalePrice()).thenReturn("100");
        when(price.getMarketPrice()).thenReturn("200");
        PriceDisplayDTO normalPrice = mock(PriceDisplayDTO.class);
        when(normalPrice.getPrice()).thenReturn(new BigDecimal("100"));
        CostEffectivePinTuan costEffectivePinTuan = mock(CostEffectivePinTuan.class);
        PriceDisplayDTO costEffectivePrice = mock(PriceDisplayDTO.class);
        ProductAttr productAttr = mock(ProductAttr.class);
        // act
        PromoDetailModule result = promoDetailService.buildPromoDetailInfo(request, costEffectivePinTuan, dealGroupBase, normalPrice, costEffectivePrice, productAttr);
        // assert
        assertNotNull(result);
        verify(request, atLeastOnce()).getPageSource();
        verify(dealGroupBase, atLeastOnce()).getPrice();
    }

    /**
     * Test normal scenario when not free deal and not ODP source
     */
    @Test
    public void testBuildPromoDetailInfoNormalScenario() throws Throwable {
        // arrange
        ProductDetailPageRequest request = mock(ProductDetailPageRequest.class);
        when(request.getPageSource()).thenReturn("normal");
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.MT_APP);
        ProductBaseInfo dealGroupBase = mock(ProductBaseInfo.class);
        DealGroupCategoryDTO category = mock(DealGroupCategoryDTO.class);
        when(dealGroupBase.getCategory()).thenReturn(category);
        when(category.getCategoryId()).thenReturn(500L);
        PriceDTO price = mock(PriceDTO.class);
        when(dealGroupBase.getPrice()).thenReturn(price);
        when(price.getMarketPrice()).thenReturn("200");
        PriceDisplayDTO normalPrice = mock(PriceDisplayDTO.class);
        when(normalPrice.getPrice()).thenReturn(new BigDecimal("100"));
        CostEffectivePinTuan costEffectivePinTuan = mock(CostEffectivePinTuan.class);
        PriceDisplayDTO costEffectivePrice = mock(PriceDisplayDTO.class);
        ProductAttr productAttr = mock(ProductAttr.class);
        // act
        PromoDetailModule result = promoDetailService.buildPromoDetailInfo(request, costEffectivePinTuan, dealGroupBase, normalPrice, costEffectivePrice, productAttr);
        // assert
        assertNotNull(result);
        verify(request, atLeastOnce()).getPageSource();
        verify(request, atLeastOnce()).getClientTypeEnum();
        verify(dealGroupBase, atLeastOnce()).getCategory();
        verify(dealGroupBase, atLeastOnce()).getPrice();
    }

    /**
     * Test null normal price scenario
     */
    @Test
    public void testBuildPromoDetailInfoNullNormalPrice() throws Throwable {
        // arrange
        ProductDetailPageRequest request = mock(ProductDetailPageRequest.class);
        CostEffectivePinTuan costEffectivePinTuan = mock(CostEffectivePinTuan.class);
        ProductBaseInfo dealGroupBase = mock(ProductBaseInfo.class);
        PriceDisplayDTO costEffectivePrice = mock(PriceDisplayDTO.class);
        ProductAttr productAttr = mock(ProductAttr.class);
        // act
        PromoDetailModule result = promoDetailService.buildPromoDetailInfo(request, costEffectivePinTuan, dealGroupBase, null, costEffectivePrice, productAttr);
        // assert
        assertNull(result);
    }

    /**
     * Test exception scenario
     */
    @Test
    public void testBuildPromoDetailInfoExceptionScenario() throws Throwable {
        // arrange
        ProductDetailPageRequest request = mock(ProductDetailPageRequest.class);
        when(request.getPageSource()).thenThrow(new RuntimeException("Test exception"));
        CostEffectivePinTuan costEffectivePinTuan = mock(CostEffectivePinTuan.class);
        ProductBaseInfo dealGroupBase = mock(ProductBaseInfo.class);
        PriceDisplayDTO normalPrice = mock(PriceDisplayDTO.class);
        PriceDisplayDTO costEffectivePrice = mock(PriceDisplayDTO.class);
        ProductAttr productAttr = mock(ProductAttr.class);
        // act
        PromoDetailModule result = promoDetailService.buildPromoDetailInfo(request, costEffectivePinTuan, dealGroupBase, normalPrice, costEffectivePrice, productAttr);
        // assert
        assertNull(result);
        verify(request).getPageSource();
    }

    /**
     * Test when morePromos is empty should return null
     */
    @Test
    public void testBuildBuyMoreDiscountWhenMorePromosEmpty() throws Throwable {
        // arrange
        PromotionDetailParams params = mock(PromotionDetailParams.class);
        PriceDisplayDTO normalPrice = mock(PriceDisplayDTO.class);
        when(params.getNormalPriceDisplayDTO()).thenReturn(normalPrice);
        when(normalPrice.getMorePromos()).thenReturn(new ArrayList<>());
        // act
        CouponModule result = promoDetailService.buildBuyMoreDiscount(params);
        // assert
        assertNull(result);
    }

    /**
     * Test when morePromos exists but no BUY_MORE_DISCOUNT promo should return null
     */
    @Test
    public void testBuildBuyMoreDiscountWhenNoMatchingPromo() throws Throwable {
        // arrange
        PromotionDetailParams params = mock(PromotionDetailParams.class);
        PriceDisplayDTO normalPrice = mock(PriceDisplayDTO.class);
        PromoDTO promoDTO = mock(PromoDTO.class);
        PromoIdentity identity = mock(PromoIdentity.class);
        List<PromoDTO> promoList = Lists.newArrayList();
        promoList.add(promoDTO);
        when(params.getNormalPriceDisplayDTO()).thenReturn(normalPrice);
        when(normalPrice.getMorePromos()).thenReturn(promoList);
        when(promoDTO.getIdentity()).thenReturn(identity);
        when(identity.getPromoShowType()).thenReturn("OTHER_TYPE");
        // act
        CouponModule result = promoDetailService.buildBuyMoreDiscount(params);
        // assert
        assertNull(result);
    }

    /**
     * Test when BUY_MORE_DISCOUNT promo exists should return valid CouponModule
     */
    @Test
    public void testBuildBuyMoreDiscountWhenMatchingPromoExists() throws Throwable {
        // arrange
        PromotionDetailParams params = mock(PromotionDetailParams.class);
        PriceDisplayDTO normalPrice = mock(PriceDisplayDTO.class);
        PromoDTO promoDTO = mock(PromoDTO.class);
        PromoIdentity identity = mock(PromoIdentity.class);
        String expectedDesc = "多买多折优惠";
        List<PromoDTO> promoList = Lists.newArrayList();
        promoList.add(promoDTO);
        when(params.getNormalPriceDisplayDTO()).thenReturn(normalPrice);
        when(normalPrice.getMorePromos()).thenReturn(promoList);
        when(promoDTO.getIdentity()).thenReturn(identity);
        when(identity.getPromoShowType()).thenReturn(CouponTypeEnum.BUY_MORE_DISCOUNT.getShowType());
        when(promoDTO.getExtendDesc()).thenReturn(expectedDesc);
        // act
        CouponModule result = promoDetailService.buildBuyMoreDiscount(params);
        // assert
        assertNotNull(result);
        assertEquals(CouponTypeEnum.BUY_MORE_DISCOUNT.getCode(), result.getCouponType());
        assertEquals(CouponStyleEnum.SIMPLE_STYLE.getCode(), result.getCouponStyle());
        CouponIcon icon = result.getCouponIcon();
        assertNotNull(icon);
        assertEquals("促销", icon.getText());
        List<CouponTitle> titles = result.getCouponTitle();
        assertNotNull(titles);
        assertEquals(1, titles.size());
        assertEquals(expectedDesc, titles.get(0).getContent());
    }

    /**
     * Test when params is null should return null
     */
    @Test
    public void testBuildBuyMoreDiscountWhenParamsNull() throws Throwable {
        // arrange
        PromotionDetailParams params = null;
        // act
        try {
            CouponModule result = promoDetailService.buildBuyMoreDiscount(params);
            // assert
            assertNull(result);
        } catch (NullPointerException e) {
            // This is expected behavior based on the implementation
            assertTrue(true);
        }
    }

    /**
     * Test when normalPriceDisplay is null should return null
     */
    @Test
    public void testBuildBuyMoreDiscountWhenNormalPriceNull() throws Throwable {
        // arrange
        PromotionDetailParams params = mock(PromotionDetailParams.class);
        when(params.getNormalPriceDisplayDTO()).thenReturn(null);
        // act
        try {
            CouponModule result = promoDetailService.buildBuyMoreDiscount(params);
            // assert
            assertNull(result);
        } catch (NullPointerException e) {
            // This is expected behavior based on the implementation
            assertTrue(true);
        }
    }

    /**
     * Test case for null input string
     * Should return empty list
     */
    @Test
    @DisplayName("Should return empty list when input is null")
    void testBuildCouponGroupIdListWithNullInput() throws Throwable {
        // arrange
        String input = null;
        // act
        List<String> result = promoDetailService.buildCouponGroupIdList(input);
        // assert
        assertNotNull(result, "Result should not be null");
        assertTrue(result.isEmpty(), "Result should be empty");
    }

    /**
     * Test case for empty input string
     * Should return empty list
     */
    @Test
    @DisplayName("Should return empty list when input is empty")
    void testBuildCouponGroupIdListWithEmptyInput() throws Throwable {
        // arrange
        String input = "";
        // act
        List<String> result = promoDetailService.buildCouponGroupIdList(input);
        // assert
        assertNotNull(result, "Result should not be null");
        assertTrue(result.isEmpty(), "Result should be empty");
    }

    /**
     * Test case for invalid JSON format
     * Should return empty list
     */
    @Test
    @DisplayName("Should return empty list when input is invalid JSON")
    void testBuildCouponGroupIdListWithInvalidJson() throws Throwable {
        // arrange
        String input = "invalid json";
        // act
        List<String> result = promoDetailService.buildCouponGroupIdList(input);
        // assert
        assertNotNull(result, "Result should not be null");
        assertTrue(result.isEmpty(), "Result should be empty");
    }

    /**
     * Test case for JSON without couponPackageList
     * Should return empty list
     */
    @Test
    @DisplayName("Should return empty list when JSON has no couponPackageList")
    void testBuildCouponGroupIdListWithoutCouponPackageList() throws Throwable {
        // arrange
        String input = "{\"otherField\": \"value\"}";
        // act
        List<String> result = promoDetailService.buildCouponGroupIdList(input);
        // assert
        assertNotNull(result, "Result should not be null");
        assertTrue(result.isEmpty(), "Result should be empty");
    }

    /**
     * Test case for JSON with empty couponPackageList
     * Should return empty list
     */
    @Test
    @DisplayName("Should return empty list when couponPackageList is empty")
    void testBuildCouponGroupIdListWithEmptyCouponPackageList() throws Throwable {
        // arrange
        String input = "{\"couponPackageList\": []}";
        // act
        List<String> result = promoDetailService.buildCouponGroupIdList(input);
        // assert
        assertNotNull(result, "Result should not be null");
        assertTrue(result.isEmpty(), "Result should be empty");
    }

    /**
     * Test case for JSON with valid couponPackageIds
     * Should return list containing all valid IDs
     */
    @Test
    @DisplayName("Should return list of valid couponPackageIds")
    void testBuildCouponGroupIdListWithValidIds() throws Throwable {
        // arrange
        String input = "{\"couponPackageList\": [" + "{\"couponPackageId\": \"id1\"}, " + "{\"couponPackageId\": \"id2\"}]}";
        // act
        List<String> result = promoDetailService.buildCouponGroupIdList(input);
        // assert
        assertNotNull(result, "Result should not be null");
        assertEquals(2, result.size(), "Should contain 2 IDs");
        assertTrue(result.contains("id1"), "Should contain id1");
        assertTrue(result.contains("id2"), "Should contain id2");
    }

    /**
     * Test case for JSON with mixed valid and invalid couponPackageIds
     * Should return list containing only valid IDs
     */
    @Test
    @DisplayName("Should return only valid couponPackageIds from mixed input")
    void testBuildCouponGroupIdListWithMixedIds() throws Throwable {
        // arrange
        String input = "{\"couponPackageList\": [" + "{\"couponPackageId\": \"id1\"}, " + "{\"couponPackageId\": \"\"}, " + "{\"couponPackageId\": null}, " + "{\"otherField\": \"value\"}, " + "{\"couponPackageId\": \"id2\"}]}";
        // act
        List<String> result = promoDetailService.buildCouponGroupIdList(input);
        // assert
        assertNotNull(result, "Result should not be null");
        assertEquals(2, result.size(), "Should contain 2 valid IDs");
        assertTrue(result.contains("id1"), "Should contain id1");
        assertTrue(result.contains("id2"), "Should contain id2");
    }

    /**
     * Test when pricePromoInfoMap is empty
     */
    @Test
    public void testBuildOtherMagicCouponItemsEmptyPricePromoInfoMap() throws Throwable {
        // arrange
        PriceDisplayDTO priceDisplayDTO = new PriceDisplayDTO();
        priceDisplayDTO.setPricePromoInfoMap(Collections.emptyMap());
        // act & assert
        assertNull(promoDetailService.buildOtherMagicCouponItems(priceDisplayDTO, 123L));
    }

    /**
     * Test when no MAGICAL_MEMBER coupons exist
     */
    @Test
    public void testBuildOtherMagicCouponItemsNoMagicCoupons() throws Throwable {
        // arrange
        PriceDisplayDTO priceDisplayDTO = new PriceDisplayDTO();
        Map<Integer, List<PromoDTO>> pricePromoInfoMap = new HashMap<>();
        pricePromoInfoMap.put(PricePromoInfoTypeEnum.TIERED_PRICE_PROMO.getType(), Collections.singletonList(new PromoDTO()));
        priceDisplayDTO.setPricePromoInfoMap(pricePromoInfoMap);
        // act & assert
        assertNull(promoDetailService.buildOtherMagicCouponItems(priceDisplayDTO, 123L));
    }

    /**
     * Test normal case with valid parameters for MT client
     */
    @Test
    public void testBuildMerchantGroupBuyCouponModuleNormalCaseMTClient() throws Throwable {
        // arrange
        PromotionDetailParams params = createBaseParams(2, new BigDecimal("99.99"), ClientTypeEnum.MT_APP.getCode());
        // act
        CouponModule result = promoDetailService.buildMerchantGroupBuyCouponModule(params);
        // assert
        assertNotNull(result);
        assertEquals(CouponTypeEnum.MERCHANT_PIN_TUAN.getCode(), result.getCouponType());
        assertEquals(CouponStyleEnum.MERCHANT_PINTUAN.getCode(), result.getCouponStyle());
        // Verify title parts
        List<CouponTitle> titles = result.getCouponTitle();
        assertNotNull(titles);
        assertEquals(3, titles.size());
        assertEquals("2人团，拼成到手", titles.get(0).getContent());
        assertEquals("99.99", titles.get(1).getContent());
        assertEquals("#FF4B10", titles.get(1).getColor());
        assertEquals("元", titles.get(2).getContent());
    }

    /**
     * Test case with integer price
     */
    @Test
    public void testBuildMerchantGroupBuyCouponModuleIntegerPrice() throws Throwable {
        // arrange
        PromotionDetailParams params = createBaseParams(5, new BigDecimal("200.00"), ClientTypeEnum.MT_APP.getCode());
        // act
        CouponModule result = promoDetailService.buildMerchantGroupBuyCouponModule(params);
        // assert
        List<CouponTitle> titles = result.getCouponTitle();
        assertNotNull(titles);
        // .00 dropped
        assertEquals("200", titles.get(1).getContent());
    }

    /**
     * Test case with null promotionDetailParams
     */
    @Test
    public void testBuildMerchantGroupBuyCouponModuleNullParams() throws Throwable {
        assertThrows(NullPointerException.class, () -> promoDetailService.buildMerchantGroupBuyCouponModule(null));
    }

    /**
     * Test case with null pinProductBrief
     */
    @Test
    public void testBuildMerchantGroupBuyCouponModuleNullPinProductBrief() throws Throwable {
        // arrange
        PromotionDetailParams params = new PromotionDetailParams();
        params.setPinProductBrief(null);
        // act & assert
        assertThrows(NullPointerException.class, () -> promoDetailService.buildMerchantGroupBuyCouponModule(params));
    }

    /**
     * Test case with null shopInfo
     */
    @Test
    public void testBuildMerchantGroupBuyCouponModuleNullShopInfo() throws Throwable {
        // arrange
        PromotionDetailParams params = new PromotionDetailParams();
        PinProductBrief pinProductBrief = new PinProductBrief();
        pinProductBrief.setPinPersonNum(2);
        pinProductBrief.setPrice(new BigDecimal("100.00"));
        params.setPinProductBrief(pinProductBrief);
        params.setShopInfo(null);
        // act & assert
        assertThrows(NullPointerException.class, () -> promoDetailService.buildMerchantGroupBuyCouponModule(params));
    }

    /**
     * Test case with null pageRequest
     */
    @Test
    public void testBuildMerchantGroupBuyCouponModuleNullPageRequest() throws Throwable {
        // arrange
        PromotionDetailParams params = new PromotionDetailParams();
        PinProductBrief pinProductBrief = new PinProductBrief();
        pinProductBrief.setPinPersonNum(2);
        pinProductBrief.setPrice(new BigDecimal("100.00"));
        params.setPinProductBrief(pinProductBrief);
        params.setShopInfo(mockShopInfo);
        params.setPageRequest(null);
        // act & assert
        assertThrows(NullPointerException.class, () -> promoDetailService.buildMerchantGroupBuyCouponModule(params));
    }

    /**
     * Test case with basic module structure
     */
    @Test
    public void testBuildMerchantGroupBuyCouponModuleBasicStructure() throws Throwable {
        // arrange
        PromotionDetailParams params = createBaseParams(2, new BigDecimal("100.00"), ClientTypeEnum.MT_APP.getCode());
        // act
        CouponModule result = promoDetailService.buildMerchantGroupBuyCouponModule(params);
        // assert
        assertNotNull(result);
        assertNotNull(result.getCouponTitle());
        assertNotNull(result.getCouponSubTitle());
        assertNotNull(result.getActionButton());
        assertEquals("发起拼团", result.getActionButton().getButtonText());
        assertEquals("#FF7700", result.getActionButton().getButtonStartColor());
        assertEquals("#FF4B10", result.getActionButton().getButtonEndColor());
    }

    /**
     * Test null promoDTO input
     */
    @Test
    public void testBuildNormalCoupon_NullPromoDTO() throws Throwable {
        CouponModule result = promoDetailService.buildNormalCoupon(null, CouponTypeEnum.PLATFORM_COUPON, new CouponIcon(), "1.0.0");
        assertNull(result);
    }

    /**
     * Test magic coupon type exclusion
     */
    @Test
    public void testBuildNormalCoupon_MagicCouponType() throws Throwable {
        PromoDTO promoDTO = new PromoDTO();
        PromoIdentity identity = mock(PromoIdentity.class);
        when(identity.getPromoShowType()).thenReturn(CouponTypeEnum.MAGIC_COUPON.getShowType());
        promoDTO.setIdentity(identity);
        CouponModule result = promoDetailService.buildNormalCoupon(promoDTO, CouponTypeEnum.PLATFORM_COUPON, new CouponIcon(), "1.0.0");
        assertNull(result);
    }

    /**
     * Test member card type exclusion
     */
    @Test
    public void testBuildNormalCoupon_MemberCardType() throws Throwable {
        PromoDTO promoDTO = new PromoDTO();
        PromoIdentity identity = mock(PromoIdentity.class);
        when(identity.getPromoShowType()).thenReturn(CouponTypeEnum.MEMBER_CARD.getShowType());
        promoDTO.setIdentity(identity);
        CouponModule result = promoDetailService.buildNormalCoupon(promoDTO, CouponTypeEnum.PLATFORM_COUPON, new CouponIcon(), "1.0.0");
        assertNull(result);
    }

    /**
     * Test normal coupon with canAssign true
     */
    @Test
    public void testBuildNormalCoupon_CanAssignTrue() throws Throwable {
        PromoDTO promoDTO = createBasicPromoDTO();
        promoDTO.setCanAssign(true);
        CouponModule result = promoDetailService.buildNormalCoupon(promoDTO, CouponTypeEnum.PLATFORM_COUPON, new CouponIcon(), "1.0.0");
        assertNotNull(result);
        assertEquals(CouponStatusEnum.NOT_ASSIGN.getCode(), result.getCouponStatus());
    }

    /**
     * Test normal coupon with canAssign false
     */
    @Test
    public void testBuildNormalCoupon_CanAssignFalse() throws Throwable {
        PromoDTO promoDTO = createBasicPromoDTO();
        promoDTO.setCanAssign(false);
        CouponModule result = promoDetailService.buildNormalCoupon(promoDTO, CouponTypeEnum.PLATFORM_COUPON, new CouponIcon(), "1.0.0");
        assertNotNull(result);
        assertEquals(CouponStatusEnum.ASSIGNED.getCode(), result.getCouponStatus());
    }

    /**
     * Test discount type coupon
     */
    @Test
    public void testBuildNormalCoupon_DiscountType() throws Throwable {
        PromoDTO promoDTO = createBasicPromoDTO();
        promoDTO.setCouponValueType(4);
        promoDTO.setCouponValueText("8.5");
        CouponModule result = promoDetailService.buildNormalCoupon(promoDTO, CouponTypeEnum.PLATFORM_COUPON, new CouponIcon(), "1.0.0");
        assertNotNull(result);
        Object amounts = result.getCouponAmount();
        assertTrue(amounts instanceof java.util.List);
        java.util.List<?> amountList = (java.util.List<?>) amounts;
        assertEquals(2, amountList.size());
        CouponAmount firstAmount = (CouponAmount) amountList.get(0);
        CouponAmount secondAmount = (CouponAmount) amountList.get(1);
        assertEquals("8.5", firstAmount.getAmount());
        assertEquals("折", secondAmount.getAmount());
    }

    /**
     * Test amount type coupon
     */
    @Test
    public void testBuildNormalCoupon_AmountType() throws Throwable {
        PromoDTO promoDTO = createBasicPromoDTO();
        promoDTO.setCouponValueType(1);
        promoDTO.setCouponValueText("100");
        CouponModule result = promoDetailService.buildNormalCoupon(promoDTO, CouponTypeEnum.PLATFORM_COUPON, new CouponIcon(), "1.0.0");
        assertNotNull(result);
        Object amounts = result.getCouponAmount();
        assertTrue(amounts instanceof java.util.List);
        java.util.List<?> amountList = (java.util.List<?>) amounts;
        assertEquals(2, amountList.size());
        CouponAmount firstAmount = (CouponAmount) amountList.get(0);
        CouponAmount secondAmount = (CouponAmount) amountList.get(1);
        assertEquals("100", firstAmount.getAmount());
        assertEquals("元", secondAmount.getAmount());
    }

    /**
     * Test government coupon with secret key
     */
    @Test
    public void testBuildNormalCoupon_GovernmentCouponWithSecretKey() throws Throwable {
        PromoDTO promoDTO = createBasicPromoDTO();
        java.util.HashMap<String, String> otherInfoMap = new java.util.HashMap<>();
        otherInfoMap.put("FINANCE_EXT", "{\"packageSecretKey\":\"test_key\"}");
        promoDTO.setPromotionOtherInfoMap(otherInfoMap);
        CouponModule result = promoDetailService.buildNormalCoupon(promoDTO, CouponTypeEnum.GOVERNMENT_CONSUMER_COUPON, new CouponIcon(), "1.0.0");
        assertNotNull(result);
        assertEquals("test_key", result.getPackageSecretKey());
    }

    /**
     * Test government coupon with unassigned status
     */
    @Test
    public void testBuildNormalCoupon_GovernmentCouponUnassigned() throws Throwable {
        PromoDTO promoDTO = createBasicPromoDTO();
        promoDTO.setCouponAssignStatus(CouponAssignStatusEnum.UN_ASSIGNED.getCode());
        java.util.ArrayList<Long> leafIds = new java.util.ArrayList<>();
        leafIds.add(12345L);
        when(leafRepository.batchGenFinancialConsumeSerialId(anyInt())).thenReturn(leafIds);
        CouponModule result = promoDetailService.buildNormalCoupon(promoDTO, CouponTypeEnum.GOVERNMENT_CONSUMER_COUPON, new CouponIcon(), "1.0.0");
        assertNotNull(result);
        assertEquals(CouponStatusEnum.NOT_ASSIGN.getCode(), result.getCouponStatus());
        assertEquals("12345", result.getCouponId());
    }

    /**
     * Test coupon with use time description
     */
    @Test
    public void testBuildNormalCoupon_WithUseTimeDesc() throws Throwable {
        PromoDTO promoDTO = createBasicPromoDTO();
        promoDTO.setUseTimeDesc("2023-12-31前可用");
        CouponModule result = promoDetailService.buildNormalCoupon(promoDTO, CouponTypeEnum.PLATFORM_COUPON, new CouponIcon(), "1.0.0");
        assertNotNull(result);
        assertNotNull(result.getCouponSubTitle());
        assertEquals("2023-12-31前可用", ((java.util.List<CouponTitle>) result.getCouponSubTitle()).get(0).getContent());
        assertEquals("#666666", ((java.util.List<CouponTitle>) result.getCouponSubTitle()).get(0).getColor());
    }

    /**
     * Test complete coupon fields
     */
    @Test
    public void testBuildNormalCoupon_CompleteFields() throws Throwable {
        PromoDTO promoDTO = createBasicPromoDTO();
        promoDTO.setCouponGroupId("group123");
        promoDTO.setCouponId("coupon123");
        promoDTO.setUseTimeDesc("2023-12-31前可用");
        CouponModule result = promoDetailService.buildNormalCoupon(promoDTO, CouponTypeEnum.PLATFORM_COUPON, new CouponIcon(), "1.0.0");
        assertNotNull(result);
        assertEquals("Test Coupon", ((java.util.List<CouponTitle>) result.getCouponTitle()).get(0).getContent());
        assertEquals("group123", result.getApplyId());
        assertEquals("coupon123", result.getCouponId());
        assertEquals(CouponStyleEnum.BIG_CARD.getCode(), result.getCouponStyle());
        assertEquals(CouponTypeEnum.PLATFORM_COUPON.getCode(), result.getCouponType());
        assertFalse(result.isExposure());
        assertEquals(0, result.getCouponValueType());
    }

    /**
     * Test government coupon with assigned status
     */
    @Test
    public void testBuildNormalCoupon_GovernmentCouponAssigned() throws Throwable {
        // arrange
        PromoDTO promoDTO = createBasicPromoDTO();
        promoDTO.setCouponAssignStatus(CouponAssignStatusEnum.ASSIGNED.getCode());
        // act
        CouponModule result = promoDetailService.buildNormalCoupon(promoDTO, CouponTypeEnum.GOVERNMENT_CONSUMER_COUPON, new CouponIcon(), "1.0.0");
        // assert
        assertNotNull(result);
        assertEquals(CouponStatusEnum.ASSIGNED.getCode(), result.getCouponStatus());
    }

    /**
     * Test case for null promoDTO input
     */
    @Test
    public void testBuildNormalCouponNullPromoDTO() throws Throwable {
        // arrange
        CouponTypeEnum couponTypeEnum = CouponTypeEnum.PLATFORM_COUPON;
        CouponIcon couponIcon = new CouponIcon();
        String mrnVersion = "1.0.0";
        // act
        CouponModule result = promoDetailService.buildNormalCoupon(null, couponTypeEnum, couponIcon, mrnVersion);
        // assert
        assertNull(result);
    }

    /**
     * 测试正常的神券场景
     */
    @Test
    public void testBuildCouponItems_NormalMagicalCoupon() throws Throwable {
        // arrange
        PriceDisplayDTO priceDisplayDTO = new PriceDisplayDTO();
        Map<Integer, List<PromoDTO>> pricePromoInfoMap = new HashMap<>();
        List<PromoDTO> promoList = new ArrayList<>();
        PromoDTO promoDTO = new PromoDTO();
        promoDTO.setCouponId("test_coupon");
        promoDTO.setCouponGroupId("test_group");
        promoDTO.setAmount(new BigDecimal("100"));
        promoDTO.setStartTime(new Date(System.currentTimeMillis() - 1000));
        promoDTO.setEndTime(new Date(System.currentTimeMillis() + 1000));
        promoDTO.setMinConsumptionAmount(BigDecimal.ZERO);
        promoDTO.setPromotionExplanatoryTags(Arrays.asList(PromotionExplanatoryTagEnum.MAGICAL_MEMBER_COUPON.getCode()));
        Map<String, String> otherInfoMap = new HashMap<>();
        otherInfoMap.put(PromotionPropertyEnum.ASSET_TYPE.getValue(), "1");
        otherInfoMap.put(PromotionPropertyEnum.CAN_INFLATE.getValue(), "true");
        otherInfoMap.put(PromotionPropertyEnum.AFTER_INFLATE.getValue(), "false");
        otherInfoMap.put(PromotionPropertyEnum.TSP_COUPON_ID.getValue(), "test_coupon");
        otherInfoMap.put(PromotionPropertyEnum.TSP_COUPON_GROUP_ID.getValue(), "test_group");
        promoDTO.setPromotionOtherInfoMap(otherInfoMap);
        promoList.add(promoDTO);
        pricePromoInfoMap.put(PricePromoInfoTypeEnum.MAGICAL_MEMBER.getType(), promoList);
        priceDisplayDTO.setPricePromoInfoMap(pricePromoInfoMap);
        // act
        CouponItemPackageDTO result = promoDetailService.buildCouponItems(priceDisplayDTO, TEST_USER_ID);
        // assert
        assertNotNull(result);
        assertEquals(1, result.getCouponItems().size());
        CouponItemInfoDTO couponItem = result.getCouponItems().get(0);
        assertEquals("test_coupon", couponItem.getCouponCode());
        assertEquals("100", couponItem.getCouponAmount());
        assertEquals(1, couponItem.getAssetType());
    }

    /**
     * 测试券聚合功能
     */
    @Test
    public void testBuildCouponItems_WithAggregation() throws Throwable {
        // arrange
        PriceDisplayDTO priceDisplayDTO = new PriceDisplayDTO();
        Map<Integer, List<PromoDTO>> pricePromoInfoMap = new HashMap<>();
        List<PromoDTO> promoList = new ArrayList<>();
        PromoDTO promoDTO = new PromoDTO();
        promoDTO.setCouponId("test_coupon");
        promoDTO.setAmount(new BigDecimal("100"));
        promoDTO.setStartTime(new Date(System.currentTimeMillis() - 1000));
        promoDTO.setEndTime(new Date(System.currentTimeMillis() + 1000));
        promoDTO.setMinConsumptionAmount(BigDecimal.ZERO);
        promoDTO.setPromotionExplanatoryTags(Arrays.asList(PromotionExplanatoryTagEnum.MAGICAL_MEMBER_COUPON.getCode()));
        Map<String, String> otherInfoMap = new HashMap<>();
        otherInfoMap.put(PromotionPropertyEnum.ASSET_TYPE.getValue(), "1");
        otherInfoMap.put(PromotionPropertyEnum.COUPON_AGGREGATE_NUM.getValue(), "3");
        otherInfoMap.put(PromotionPropertyEnum.TSP_COUPON_ID.getValue(), "test_coupon");
        promoDTO.setPromotionOtherInfoMap(otherInfoMap);
        promoList.add(promoDTO);
        pricePromoInfoMap.put(PricePromoInfoTypeEnum.MAGICAL_MEMBER.getType(), promoList);
        priceDisplayDTO.setPricePromoInfoMap(pricePromoInfoMap);
        // act
        CouponItemPackageDTO result = promoDetailService.buildCouponItems(priceDisplayDTO, TEST_USER_ID);
        // assert
        assertNotNull(result);
        assertEquals(1, result.getCouponItems().size());
        assertEquals(3, result.getCouponItems().get(0).getCouponNum());
    }

    /**
     * 测试过期券场景
     */
    @Test
    public void testBuildCouponItems_ExpiredCoupon() throws Throwable {
        // arrange
        PriceDisplayDTO priceDisplayDTO = new PriceDisplayDTO();
        Map<Integer, List<PromoDTO>> pricePromoInfoMap = new HashMap<>();
        List<PromoDTO> promoList = new ArrayList<>();
        PromoDTO promoDTO = new PromoDTO();
        promoDTO.setCouponId("test_coupon");
        promoDTO.setAmount(new BigDecimal("100"));
        promoDTO.setStartTime(new Date(System.currentTimeMillis() - 2000));
        // expired
        promoDTO.setEndTime(new Date(System.currentTimeMillis() - 1000));
        promoDTO.setMinConsumptionAmount(BigDecimal.ZERO);
        promoDTO.setPromotionExplanatoryTags(Arrays.asList(PromotionExplanatoryTagEnum.MAGICAL_MEMBER_COUPON.getCode()));
        Map<String, String> otherInfoMap = new HashMap<>();
        otherInfoMap.put(PromotionPropertyEnum.ASSET_TYPE.getValue(), "1");
        otherInfoMap.put(PromotionPropertyEnum.TSP_COUPON_ID.getValue(), "test_coupon");
        promoDTO.setPromotionOtherInfoMap(otherInfoMap);
        promoList.add(promoDTO);
        pricePromoInfoMap.put(PricePromoInfoTypeEnum.MAGICAL_MEMBER.getType(), promoList);
        priceDisplayDTO.setPricePromoInfoMap(pricePromoInfoMap);
        // act
        CouponItemPackageDTO result = promoDetailService.buildCouponItems(priceDisplayDTO, TEST_USER_ID);
        // assert
        assertNotNull(result);
        assertEquals(1, result.getCouponItems().size());
        assertEquals(0, result.getCouponItems().get(0).getValidStatus());
    }

    /**
     * 测试券排序逻辑
     */
    @Test
    public void testBuildCouponItems_SortingLogic() throws Throwable {
        // arrange
        PriceDisplayDTO priceDisplayDTO = new PriceDisplayDTO();
        Map<Integer, List<PromoDTO>> pricePromoInfoMap = new HashMap<>();
        List<PromoDTO> promoList = new ArrayList<>();
        // Create promos with different properties for sorting
        PromoDTO promo1 = createPromoDTO("1", new BigDecimal("20"), true, false);
        PromoDTO promo2 = createPromoDTO("2", new BigDecimal("15"), true, true);
        PromoDTO promo3 = createPromoDTO("3", new BigDecimal("10"), false, false);
        promoList.add(promo1);
        promoList.add(promo2);
        promoList.add(promo3);
        pricePromoInfoMap.put(PricePromoInfoTypeEnum.MAGICAL_MEMBER.getType(), promoList);
        priceDisplayDTO.setPricePromoInfoMap(pricePromoInfoMap);
        // act
        CouponItemPackageDTO result = promoDetailService.buildCouponItems(priceDisplayDTO, TEST_USER_ID);
        // assert
        assertNotNull(result);
        List<CouponItemInfoDTO> items = result.getCouponItems();
        assertEquals(3, items.size());
        // Verify sorting: canInflate DESC, amount DESC
        assertTrue(items.get(0).getAmount().compareTo(items.get(1).getAmount()) >= 0);
    }

    /**
     * Test empty pricePromoInfoMap
     */
    @Test
    public void testBuildCouponItemsWithEmptyPromoInfoMap() throws Throwable {
        PriceDisplayDTO dealPrice = new PriceDisplayDTO();
        dealPrice.setPricePromoInfoMap(new HashMap<>());
        CouponItemPackageDTO result = promoDetailService.buildCouponItems(dealPrice, 123L);
        assertNotNull(result);
        assertTrue(CollectionUtils.isEmpty(result.getCouponItems()));
        assertTrue(CollectionUtils.isEmpty(result.getCouponIds()));
    }

    /**
     * Test when normalPrice is null
     */
    @Test
    public void testBuildNormalPromoDetailInfo_NullNormalPrice() throws Throwable {
        // act
        PromoDetailModule result = promoDetailService.buildNormalPromoDetailInfo(ClientTypeEnum.MT_APP, dealGroupBase, null, "test");
        // assert
        assertNull(result);
        verifyNoInteractions(dealGroupBase);
    }

    /**
     * Test mini program not cost effective
     */
    @Test
    public void testBuildNormalPromoDetailInfo_MiniProgramNotCostEffective() throws Throwable {
        // arrange
        PriceDisplayDTO normalPrice = mock(PriceDisplayDTO.class);
        DealGroupCategoryDTO categoryDTO = mock(DealGroupCategoryDTO.class);
        // act
        PromoDetailModule result = promoDetailService.buildNormalPromoDetailInfo(ClientTypeEnum.MT_XCX, dealGroupBase, normalPrice, "other_source");
        // assert
        assertNull(result);
    }

    /**
     * Test successful case with normal promo
     */
    @Test
    public void testBuildNormalPromoDetailInfo_SuccessWithNormalPromo() throws Throwable {
        // arrange
        PriceDisplayDTO normalPrice = mock(PriceDisplayDTO.class);
        DealGroupCategoryDTO categoryDTO = mock(DealGroupCategoryDTO.class);
        PriceDTO priceDTO = mock(PriceDTO.class);
        when(dealGroupBase.getPrice()).thenReturn(priceDTO);
        when(priceDTO.getMarketPrice()).thenReturn("100");
        when(normalPrice.getPrice()).thenReturn(new BigDecimal("80"));
        List<PromoDTO> usedPromos = new ArrayList<>();
        PromoDTO promoDTO = new PromoDTO();
        promoDTO.setIdentity(new PromoIdentity(PromoTypeEnum.NORMAL_PROMO.getType()));
        promoDTO.setAmount(new BigDecimal("20"));
        usedPromos.add(promoDTO);
        when(normalPrice.getUsedPromos()).thenReturn(usedPromos);
        // act
        PromoDetailModule result = promoDetailService.buildNormalPromoDetailInfo(ClientTypeEnum.MT_APP, dealGroupBase, normalPrice, "test");
        // assert
        assertNotNull(result);
        assertEquals("100", result.getMarketPrice());
        assertEquals("80", result.getPromoPrice());
    }

    /**
     * Test successful case with no promos
     */
    @Test
    public void testBuildNormalPromoDetailInfo_SuccessWithNoPromos() throws Throwable {
        // arrange
        PriceDisplayDTO normalPrice = mock(PriceDisplayDTO.class);
        DealGroupCategoryDTO categoryDTO = mock(DealGroupCategoryDTO.class);
        PriceDTO priceDTO = mock(PriceDTO.class);
        when(dealGroupBase.getPrice()).thenReturn(priceDTO);
        when(priceDTO.getMarketPrice()).thenReturn("100");
        when(normalPrice.getPrice()).thenReturn(new BigDecimal("80"));
        when(normalPrice.getUsedPromos()).thenReturn(null);
        // act
        PromoDetailModule result = promoDetailService.buildNormalPromoDetailInfo(ClientTypeEnum.MT_APP, dealGroupBase, normalPrice, "test");
        // assert
        assertNotNull(result);
        assertEquals("100", result.getMarketPrice());
        assertEquals("80", result.getPromoPrice());
    }

    /**
     * Test null promo amount
     */
    @Test
    public void testBuildNormalPromoDetailInfo_NullPromoAmount() throws Throwable {
        // arrange
        PriceDisplayDTO normalPrice = mock(PriceDisplayDTO.class);
        DealGroupCategoryDTO categoryDTO = mock(DealGroupCategoryDTO.class);
        PriceDTO priceDTO = mock(PriceDTO.class);
        when(dealGroupBase.getPrice()).thenReturn(priceDTO);
        when(priceDTO.getMarketPrice()).thenReturn("100");
        when(normalPrice.getPrice()).thenReturn(new BigDecimal("80"));
        List<PromoDTO> usedPromos = new ArrayList<>();
        PromoDTO promoDTO = new PromoDTO();
        promoDTO.setIdentity(new PromoIdentity(PromoTypeEnum.NORMAL_PROMO.getType()));
        promoDTO.setAmount(null);
        usedPromos.add(promoDTO);
        when(normalPrice.getUsedPromos()).thenReturn(usedPromos);
        // act
        PromoDetailModule result = promoDetailService.buildNormalPromoDetailInfo(ClientTypeEnum.MT_APP, dealGroupBase, normalPrice, "test");
        // assert
        assertNotNull(result);
        assertEquals("100", result.getMarketPrice());
        assertEquals("80", result.getPromoPrice());
    }

    @Test
    public void testBuildDealPromoDetailInfo_PrivateLiveMiniapp() throws Throwable {
        // arrange
        ProductBaseInfo dealGroupBase = mock(ProductBaseInfo.class);
        CostEffectivePinTuan costEffectivePinTuan = mock(CostEffectivePinTuan.class);
        ClientTypeEnum clientType = ClientTypeEnum.MT_LIVE_XCX;
        PriceDisplayDTO dealPromoPrice = mock(PriceDisplayDTO.class);
        PromoDetailModule promoDetailModule = new PromoDetailModule();
        PriceDisplayDTO costEffectivePrice = mock(PriceDisplayDTO.class);
        LeadsInfoResult leadsInfoResult = mock(LeadsInfoResult.class);
        DealGiftResult dealGiftResult = mock(DealGiftResult.class);
        List<DealGift> dealGifts = new ArrayList<>();
        QualificationStatusEnum qualificationStatus = QualificationStatusEnum.BIND;
        // Verify initial state
        assertNull(promoDetailModule.getFinalPrice());
        assertNull(promoDetailModule.getBestPromoDetails());
        // act
        promoDetailService.buildDealPromoDetailInfo(dealGroupBase, costEffectivePinTuan, clientType, dealPromoPrice, promoDetailModule, costEffectivePrice, leadsInfoResult, dealGiftResult, dealGifts, qualificationStatus);
        // assert
        // For private live miniapp, the method should return early without modifying the promoDetailModule
        assertNull(promoDetailModule.getFinalPrice());
        assertNull(promoDetailModule.getBestPromoDetails());
        // Verify no interactions with dependencies
        verifyNoInteractions(dealGroupBase);
        verifyNoInteractions(costEffectivePinTuan);
        verifyNoInteractions(dealPromoPrice);
        verifyNoInteractions(costEffectivePrice);
        verifyNoInteractions(leadsInfoResult);
        verifyNoInteractions(dealGiftResult);
        verifyNoInteractions(buttonStyleHelper);
    }

    @Test
    public void testBuildDealPromoDetailInfo_NullDealPromoPrice() throws Throwable {
        // arrange
        ProductBaseInfo dealGroupBase = mock(ProductBaseInfo.class);
        DealGroupCategoryDTO categoryDTO = mock(DealGroupCategoryDTO.class);
        when(dealGroupBase.getCategory()).thenReturn(categoryDTO);
        when(categoryDTO.getCategoryId()).thenReturn(123L);
        CostEffectivePinTuan costEffectivePinTuan = mock(CostEffectivePinTuan.class);
        ClientTypeEnum clientType = ClientTypeEnum.MT_APP;
        PriceDisplayDTO dealPromoPrice = null;
        PromoDetailModule promoDetailModule = new PromoDetailModule();
        PriceDisplayDTO costEffectivePrice = mock(PriceDisplayDTO.class);
        LeadsInfoResult leadsInfoResult = mock(LeadsInfoResult.class);
        DealGiftResult dealGiftResult = mock(DealGiftResult.class);
        List<DealGift> dealGifts = new ArrayList<>();
        QualificationStatusEnum qualificationStatus = QualificationStatusEnum.BIND;
        // act
        promoDetailService.buildDealPromoDetailInfo(dealGroupBase, costEffectivePinTuan, clientType, dealPromoPrice, promoDetailModule, costEffectivePrice, leadsInfoResult, dealGiftResult, dealGifts, qualificationStatus);
        // assert
        assertNull(promoDetailModule.getMarketPrice());
    }

    @Test
    public void testBuildDealPromoDetailInfo_FreeDeal() throws Throwable {
        // arrange
        ProductBaseInfo dealGroupBase = mock(ProductBaseInfo.class);
        DealGroupCategoryDTO categoryDTO = mock(DealGroupCategoryDTO.class);
        when(dealGroupBase.getCategory()).thenReturn(categoryDTO);
        when(categoryDTO.getCategoryId()).thenReturn(505L);
        DealGroupBasicDTO basicDTO = mock(DealGroupBasicDTO.class);
        CostEffectivePinTuan costEffectivePinTuan = mock(CostEffectivePinTuan.class);
        ClientTypeEnum clientType = ClientTypeEnum.MT_APP;
        PriceDisplayDTO dealPromoPrice = mock(PriceDisplayDTO.class);
        PromoDetailModule promoDetailModule = new PromoDetailModule();
        PriceDisplayDTO costEffectivePrice = mock(PriceDisplayDTO.class);
        LeadsInfoResult leadsInfoResult = mock(LeadsInfoResult.class);
        DealGiftResult dealGiftResult = mock(DealGiftResult.class);
        List<DealGift> dealGifts = new ArrayList<>();
        QualificationStatusEnum qualificationStatus = QualificationStatusEnum.BIND;
        // act
        promoDetailService.buildDealPromoDetailInfo(dealGroupBase, costEffectivePinTuan, clientType, dealPromoPrice, promoDetailModule, costEffectivePrice, leadsInfoResult, dealGiftResult, dealGifts, qualificationStatus);
        // assert
        assertNull(promoDetailModule.getFinalPrice());
    }

    @Test
    public void testBuildDealPromoDetailInfo_LeadsDealWithSpecialValue() throws Throwable {
        // arrange
        ProductBaseInfo dealGroupBase = mock(ProductBaseInfo.class);
        DealGroupCategoryDTO categoryDTO = mock(DealGroupCategoryDTO.class);
        when(dealGroupBase.getCategory()).thenReturn(categoryDTO);
        CostEffectivePinTuan costEffectivePinTuan = mock(CostEffectivePinTuan.class);
        ClientTypeEnum clientType = ClientTypeEnum.MT_XCX;
        PriceDisplayDTO dealPromoPrice = mock(PriceDisplayDTO.class);
        PromoDetailModule promoDetailModule = new PromoDetailModule();
        PriceDisplayDTO costEffectivePrice = mock(PriceDisplayDTO.class);
        LeadsInfoResult leadsInfoResult = mock(LeadsInfoResult.class);
        ActivityDetailDTO activityDetailDTO = mock(ActivityDetailDTO.class);
        DealGiftResult dealGiftResult = mock(DealGiftResult.class);
        List<DealGift> dealGifts = new ArrayList<>();
        QualificationStatusEnum qualificationStatus = QualificationStatusEnum.BIND;
        // act
        promoDetailService.buildDealPromoDetailInfo(dealGroupBase, costEffectivePinTuan, clientType, dealPromoPrice, promoDetailModule, costEffectivePrice, leadsInfoResult, dealGiftResult, dealGifts, qualificationStatus);
        // assert
        assertNull(promoDetailModule.getFinalPrice());
    }

    @Test
    public void testBuildDealPromoDetailInfo_NormalCaseWithPromos() throws Throwable {
        // arrange
        ProductBaseInfo dealGroupBase = mock(ProductBaseInfo.class);
        DealGroupCategoryDTO categoryDTO = mock(DealGroupCategoryDTO.class);
        when(dealGroupBase.getCategory()).thenReturn(categoryDTO);
        CostEffectivePinTuan costEffectivePinTuan = mock(CostEffectivePinTuan.class);
        ClientTypeEnum clientType = ClientTypeEnum.MT_APP;
        PriceDisplayDTO dealPromoPrice = mock(PriceDisplayDTO.class);
        when(dealPromoPrice.getPrice()).thenReturn(new BigDecimal("100.00"));
        List<PromoDTO> usedPromos = new ArrayList<>();
        PromoDTO promoDTO = mock(PromoDTO.class);
        PromoIdentity identity = mock(PromoIdentity.class);
        when(promoDTO.getIdentity()).thenReturn(identity);
        when(identity.getPromoShowType()).thenReturn("DISCOUNT_SELL");
        when(promoDTO.getAmount()).thenReturn(new BigDecimal("10.00"));
        when(identity.getPromoTypeDesc()).thenReturn("特惠促销");
        when(promoDTO.getExtendDesc()).thenReturn("满100减10");
        usedPromos.add(promoDTO);
        when(dealPromoPrice.getUsedPromos()).thenReturn(usedPromos);
        PromoDetailModule promoDetailModule = new PromoDetailModule();
        PriceDisplayDTO costEffectivePrice = mock(PriceDisplayDTO.class);
        LeadsInfoResult leadsInfoResult = mock(LeadsInfoResult.class);
        DealGiftResult dealGiftResult = mock(DealGiftResult.class);
        List<DealGift> dealGifts = new ArrayList<>();
        QualificationStatusEnum qualificationStatus = QualificationStatusEnum.BIND;
        // act
        promoDetailService.buildDealPromoDetailInfo(dealGroupBase, costEffectivePinTuan, clientType, dealPromoPrice, promoDetailModule, costEffectivePrice, leadsInfoResult, dealGiftResult, dealGifts, qualificationStatus);
        // assert
        assertEquals("100", promoDetailModule.getFinalPrice());
        assertNotNull(promoDetailModule.getBestPromoDetails());
        assertEquals(1, promoDetailModule.getBestPromoDetails().size());
        assertEquals("特惠促销", promoDetailModule.getBestPromoDetails().get(0).getPromoTag());
        assertEquals("10", promoDetailModule.getBestPromoDetails().get(0).getPromoAmount());
    }

    @Test
    public void testBuildDealPromoDetailInfo_ExceptionCase() throws Throwable {
        // arrange
        ProductBaseInfo dealGroupBase = mock(ProductBaseInfo.class);
        when(dealGroupBase.getCategory()).thenThrow(new RuntimeException("Test exception"));
        CostEffectivePinTuan costEffectivePinTuan = mock(CostEffectivePinTuan.class);
        ClientTypeEnum clientType = ClientTypeEnum.MT_APP;
        PriceDisplayDTO dealPromoPrice = mock(PriceDisplayDTO.class);
        PromoDetailModule promoDetailModule = new PromoDetailModule();
        PriceDisplayDTO costEffectivePrice = mock(PriceDisplayDTO.class);
        LeadsInfoResult leadsInfoResult = mock(LeadsInfoResult.class);
        DealGiftResult dealGiftResult = mock(DealGiftResult.class);
        List<DealGift> dealGifts = new ArrayList<>();
        QualificationStatusEnum qualificationStatus = QualificationStatusEnum.BIND;
        // act & assert
        assertDoesNotThrow(() -> {
            promoDetailService.buildDealPromoDetailInfo(dealGroupBase, costEffectivePinTuan, clientType, dealPromoPrice, promoDetailModule, costEffectivePrice, leadsInfoResult, dealGiftResult, dealGifts, qualificationStatus);
        });
    }

    @Test
    public void testConvertPromoDesc_StateSubsidies() throws Throwable {
        // arrange
        PromoDTO promoDTO = mock(PromoDTO.class);
        PromoIdentity identity = mock(PromoIdentity.class);
        PromoTextDTO promoTextDTO = mock(PromoTextDTO.class);
        PromoDetailModule module = mock(PromoDetailModule.class);
        when(promoDTO.getIdentity()).thenReturn(identity);
        when(identity.getPromoShowType()).thenReturn("STATE_SUBSIDIES_PROMO");
        when(promoDTO.getPromoTextDTO()).thenReturn(promoTextDTO);
        when(promoTextDTO.getSubTitle()).thenReturn("国家补贴描述");
        // act
        String result = promoDetailService.convertPromoDesc(promoDTO, module, "标签");
        // assert
        assertEquals("国家补贴描述", result);
    }

    @Test
    public void testConvertPromoDesc_NullPromoDTO() throws Throwable {
        // act
        String result = promoDetailService.convertPromoDesc(null, mock(PromoDetailModule.class), "标签");
        // assert
        assertEquals("团购优惠", result);
    }

    @Test
    public void testConvertPromoDesc_NormalCouponWithPromoText() throws Throwable {
        // arrange
        PromoDTO promoDTO = mock(PromoDTO.class);
        PromoIdentity identity = mock(PromoIdentity.class);
        PromoTextDTO promoTextDTO = mock(PromoTextDTO.class);
        PromoDetailModule module = mock(PromoDetailModule.class);
        when(promoDTO.getIdentity()).thenReturn(identity);
        when(identity.getPromoShowType()).thenReturn("NORMAL_COUPON");
        when(promoDTO.getPromoTextDTO()).thenReturn(promoTextDTO);
        when(promoTextDTO.getPromoDivideType()).thenReturn("PLATFORM_COUPON");
        when(promoTextDTO.getTitle()).thenReturn("标签，优惠描述");
        when(promoDTO.getExtendDesc()).thenReturn("extend desc");
        // act
        String result = promoDetailService.convertPromoDesc(promoDTO, module, "标签");
        // assert
        assertEquals("优惠描述", result);
    }

    @Test
    public void testConvertPromoDesc_MagicalMemberZeroThreshold() throws Throwable {
        // arrange
        PromoDTO promoDTO = mock(PromoDTO.class);
        PromoIdentity identity = mock(PromoIdentity.class);
        PromoDetailModule module = mock(PromoDetailModule.class);
        when(promoDTO.getIdentity()).thenReturn(identity);
        when(identity.getPromoShowType()).thenReturn("MAGICAL_MEMBER_PLATFORM_COUPON");
        when(promoDTO.getMinConsumptionAmount()).thenReturn(BigDecimal.ZERO);
        when(promoDTO.getAmount()).thenReturn(BigDecimal.TEN);
        when(promoDTO.getExtendDesc()).thenReturn("extend desc");
        // act
        String result = promoDetailService.convertPromoDesc(promoDTO, module, "标签");
        // assert
        assertEquals("无门槛", result);
    }

    @Test
    public void testConvertPromoDesc_MagicalMemberWithThreshold() throws Throwable {
        // arrange
        PromoDTO promoDTO = mock(PromoDTO.class);
        PromoIdentity identity = mock(PromoIdentity.class);
        PromoDetailModule module = mock(PromoDetailModule.class);
        when(promoDTO.getIdentity()).thenReturn(identity);
        when(identity.getPromoShowType()).thenReturn("MAGICAL_MEMBER_PLATFORM_COUPON");
        when(promoDTO.getMinConsumptionAmount()).thenReturn(new BigDecimal("100"));
        when(promoDTO.getAmount()).thenReturn(new BigDecimal("20"));
        when(promoDTO.getExtendDesc()).thenReturn("extend desc");
        // act
        String result = promoDetailService.convertPromoDesc(promoDTO, module, "标签");
        // assert
        assertEquals("满100元减20元", result);
    }

    @Test
    public void testConvertPromoDesc_Exception() throws Throwable {
        // arrange
        PromoDTO promoDTO = mock(PromoDTO.class);
        PromoDetailModule module = mock(PromoDetailModule.class);
        when(promoDTO.getIdentity()).thenThrow(new RuntimeException("Test exception"));
        when(promoDTO.getExtendDesc()).thenReturn("默认描述");
        // act
        String result = promoDetailService.convertPromoDesc(promoDTO, module, "标签");
        // assert
        assertEquals("默认描述", result);
    }

    @Test
    public void testConvertPromoDesc_NullExtendDescAndIdentity() throws Throwable {
        // arrange
        PromoDTO promoDTO = mock(PromoDTO.class);
        PromoDetailModule module = mock(PromoDetailModule.class);
        when(promoDTO.getExtendDesc()).thenReturn(null);
        when(promoDTO.getIdentity()).thenReturn(null);
        // act
        String result = promoDetailService.convertPromoDesc(promoDTO, module, "标签");
        // assert
        assertEquals("团购优惠", result);
    }
}
