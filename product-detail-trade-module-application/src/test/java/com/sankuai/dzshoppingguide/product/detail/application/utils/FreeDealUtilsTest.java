package com.sankuai.dzshoppingguide.product.detail.application.utils;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.promo.FreeDealConfig;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.FreeDealEnum;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.NullSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * Test class for FreeDealUtils
 */
@ExtendWith(MockitoExtension.class)
public class FreeDealUtilsTest {

    private static final String FREE_DEAL_INFO_CONFIG_LION_KEY = "free.deal.info.config";

    private static final String FREE_DEAL_INFO_CONFIG_LIST_LION_KEY = "free.deal.category.list";

    /**
     * Test case: Test with invalid categoryIds (zero or negative)
     * Expected: Should return false for invalid values
     */
    @ParameterizedTest(name = "Should return false when categoryId is {0}")
    @ValueSource(longs = { 0L, -1L, -999L, Long.MIN_VALUE })
    @DisplayName("Should return false for invalid categoryIds")
    public void testInCategoryList_WithInvalidCategoryId(Long categoryId) {
        // act
        boolean result = FreeDealUtils.inCategoryList(categoryId);
        // assert
        assertFalse(result, "Should return false for invalid categoryId: " + categoryId);
    }

    /**
     * Test case for null input FreeDealEnum
     */
    @Test
    @DisplayName("Should return null when FreeDealEnum is null")
    public void testGetFreeDealConfig_WhenFreeDealTypeIsNull() throws Throwable {
        // arrange & act
        FreeDealConfig result = FreeDealUtils.getFreeDealConfig(null);
        // assert
        assertNull(result, "Result should be null when input FreeDealEnum is null");
    }

    /**
     * Test case for non-null FreeDealEnum
     */
    @Test
    @DisplayName("Should not throw exception when FreeDealEnum is valid")
    public void testGetFreeDealConfig_WithValidFreeDealType() throws Throwable {
        // arrange
        FreeDealEnum freeDealType = FreeDealEnum.VACCINES;
        // act & assert
        assertDoesNotThrow(() -> {
            FreeDealConfig result = FreeDealUtils.getFreeDealConfig(freeDealType);
            // Additional assertions based on actual environment configuration
            if (result != null) {
                assertNotNull(result.getButtonText());
                assertNotNull(result.getPriceTags());
                assertTrue(result.getPriceTags() instanceof List);
            }
        });
    }

    /**
     * Test case for FreeDealEnum with LIFE_HOUSEKEEPING_BOOKING type
     */
    @Test
    @DisplayName("Should handle LIFE_HOUSEKEEPING_BOOKING type correctly")
    public void testGetFreeDealConfig_WithLifeHousekeepingBooking() throws Throwable {
        // arrange
        FreeDealEnum freeDealType = FreeDealEnum.LIFE_HOUSEKEEPING_BOOKING;
        // act
        FreeDealConfig result = FreeDealUtils.getFreeDealConfig(freeDealType);
        // assert
        assertDoesNotThrow(() -> {
            if (result != null) {
                assertNotNull(result.getButtonText());
                assertNotNull(result.getMtAppSchema());
                assertNotNull(result.getMtH5Schema());
            }
        });
    }

    /**
     * Test case for FreeDealEnum with EDU_TRIAL_BOOKING type
     */
    @Test
    @DisplayName("Should handle EDU_TRIAL_BOOKING type correctly")
    public void testGetFreeDealConfig_WithEduTrialBooking() throws Throwable {
        // arrange
        FreeDealEnum freeDealType = FreeDealEnum.EDU_TRIAL_BOOKING;
        // act
        FreeDealConfig result = FreeDealUtils.getFreeDealConfig(freeDealType);
        // assert
        assertDoesNotThrow(() -> {
            if (result != null) {
                assertNotNull(result.getButtonText());
                assertNotNull(result.getLimit());
                assertTrue(result.getLimit() instanceof List);
            }
        });
    }

    /**
     * Test case for FreeDealEnum with RECYCLE type
     */
    @Test
    @DisplayName("Should handle RECYCLE type correctly")
    public void testGetFreeDealConfig_WithRecycle() throws Throwable {
        // arrange
        FreeDealEnum freeDealType = FreeDealEnum.RECYCLE;
        // act
        FreeDealConfig result = FreeDealUtils.getFreeDealConfig(freeDealType);
        // assert
        assertDoesNotThrow(() -> {
            if (result != null) {
                assertNotNull(result.getButtonText());
                assertNotNull(result.getEmptyButtonText());
            }
        });
    }

    /**
     * Test case: Test with null categoryId
     * Expected: Should return false when input is null
     */
    @Test
    @DisplayName("Should return false when categoryId is null")
    public void testInCategoryList_WhenCategoryIdIsNull() throws Throwable {
        // arrange
        Long categoryId = null;
        // act
        boolean result = FreeDealUtils.inCategoryList(categoryId);
        // assert
        assertFalse(result, "Should return false when categoryId is null");
    }

    /**
     * Test case: Test with non-existent categoryId
     * Expected: Should return false when categoryId is not in whitelist
     */
    @Test
    @DisplayName("Should return false when categoryId is not in whitelist")
    public void testInCategoryList_WithNonExistentCategoryId() throws Throwable {
        // arrange
        Long categoryId = Long.MAX_VALUE;
        // act
        boolean result = FreeDealUtils.inCategoryList(categoryId);
        // assert
        assertFalse(result, "Should return false when categoryId is not in whitelist");
    }

    /**
     * Test case: Test with boundary value
     * Expected: Should handle boundary value correctly
     */
    @Test
    @DisplayName("Should handle boundary value correctly")
    public void testInCategoryList_WithBoundaryValue() throws Throwable {
        // arrange
        Long categoryId = Long.MAX_VALUE;
        // act
        boolean result = FreeDealUtils.inCategoryList(categoryId);
        // assert
        assertFalse(result, "Should handle boundary value correctly");
    }

    /**
     * Test useIndependentTab with null categoryId
     */
    @Test
    public void testUseIndependentTab_WhenCategoryIdIsNull() throws Throwable {
        // act
        boolean result = FreeDealUtils.useIndependentTab(null);
        // assert
        assertFalse(result);
    }

    /**
     * Test useIndependentTab with negative categoryId
     */
    @Test
    public void testUseIndependentTab_WhenCategoryIdIsNegative() throws Throwable {
        // act
        boolean result = FreeDealUtils.useIndependentTab(-1L);
        // assert
        assertFalse(result);
    }

    /**
     * Test useIndependentTab with zero categoryId
     */
    @Test
    public void testUseIndependentTab_WhenCategoryIdIsZero() throws Throwable {
        // act
        boolean result = FreeDealUtils.useIndependentTab(0L);
        // assert
        assertFalse(result);
    }

    /**
     * Test useIndependentTab with positive categoryId
     */
    @Test
    public void testUseIndependentTab_WhenCategoryIdIsPositive() throws Throwable {
        // act
        boolean result = FreeDealUtils.useIndependentTab(1L);
        // assert
        assertFalse(result);
    }

    /**
     * Test case when category in ProductBaseInfo is null
     */
    @Test
    public void testIsLeadsDeal_WhenCategoryIsNull() throws Throwable {
        // arrange
        ProductBaseInfo dealGroupBase = Mockito.mock(ProductBaseInfo.class);
        DealGroupCategoryDTO category = new DealGroupCategoryDTO();
        Mockito.when(dealGroupBase.getCategory()).thenReturn(category);
        // act
        boolean result = DealUtils.isLeadsDeal(dealGroupBase);
        // assert
        assertFalse(result);
    }

    /**
     * Test case when category exists with valid data
     */
    @Test
    public void testIsLeadsDeal_WhenCategoryExists() throws Throwable {
        // arrange
        ProductBaseInfo dealGroupBase = Mockito.mock(ProductBaseInfo.class);
        DealGroupCategoryDTO category = new DealGroupCategoryDTO();
        category.setCategoryId(505L);
        category.setServiceTypeId(12312L);
        Mockito.when(dealGroupBase.getCategory()).thenReturn(category);
        // act
        boolean result = DealUtils.isLeadsDeal(dealGroupBase);
        // assert
        // Note: The actual result will depend on the Lion configuration in the test environment
        // We can only verify that the method executes without throwing exceptions
        // Verifying the method returns a value
        assertNotNull(result);
    }

    /**
     * Test case when category exists with empty category ID
     */
    @Test
    public void testIsLeadsDeal_WhenCategoryExistsWithEmptyCategoryId() throws Throwable {
        // arrange
        ProductBaseInfo dealGroupBase = Mockito.mock(ProductBaseInfo.class);
        DealGroupCategoryDTO category = new DealGroupCategoryDTO();
        // Only setting serviceTypeId
        category.setServiceTypeId(12312L);
        Mockito.when(dealGroupBase.getCategory()).thenReturn(category);
        // act
        boolean result = DealUtils.isLeadsDeal(dealGroupBase);
        // assert
        assertFalse(result);
    }

    /**
     * Test case when category exists with empty service type ID
     */
    @Test
    public void testIsLeadsDeal_WhenCategoryExistsWithEmptyServiceTypeId() throws Throwable {
        // arrange
        ProductBaseInfo dealGroupBase = Mockito.mock(ProductBaseInfo.class);
        DealGroupCategoryDTO category = new DealGroupCategoryDTO();
        // Only setting categoryId
        category.setCategoryId(505L);
        Mockito.when(dealGroupBase.getCategory()).thenReturn(category);
        // act
        boolean result = DealUtils.isLeadsDeal(dealGroupBase);
        // assert
        assertFalse(result);
    }
}
