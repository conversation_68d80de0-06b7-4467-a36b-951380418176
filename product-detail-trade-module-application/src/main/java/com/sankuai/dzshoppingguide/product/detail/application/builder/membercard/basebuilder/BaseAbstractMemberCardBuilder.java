package com.sankuai.dzshoppingguide.product.detail.application.builder.membercard.basebuilder;

import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.BuilderTypeEnum;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.extend.BaseVariableBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.FetcherDAGStarter;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.membercard.vo.BasicCardModuleVO;
import lombok.extern.slf4j.Slf4j;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/2/28 16:18
 */
@Builder(
        builderType = BuilderTypeEnum.ABSTRACT_BUILDER,
        moduleKey = ModuleKeyConstants.MEMBER_CARD,
        startFetcher = FetcherDAGStarter.class,
        dependentFetchers = {}
)
@Slf4j
public abstract class BaseAbstractMemberCardBuilder extends BaseVariableBuilder<BasicCardModuleVO> {

    @Override
    public BasicCardModuleVO doBuild() {
        return null;
    }
}
