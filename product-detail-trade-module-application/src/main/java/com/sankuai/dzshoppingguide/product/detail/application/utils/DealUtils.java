package com.sankuai.dzshoppingguide.product.detail.application.utils;

import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;

import java.util.Objects;

import static com.sankuai.dzshoppingguide.product.detail.application.constants.LionConstants.LEADS_DEAL_CATE_CONFIG;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 16/4/19.
 */
public class DealUtils {


    /**
     * 判断是否为留资型团单
     */
    public static boolean isLeadsDeal(ProductBaseInfo dealGroupBase) {
        /**
         * 判断团单是否展示预约和购买按钮
         */
        if (Objects.isNull(dealGroupBase) || Objects.isNull(dealGroupBase.getCategory())) {
            return false;
        }
        return LionConfigUtils.hitLeadsDeal(dealGroupBase.getCategory(), LEADS_DEAL_CATE_CONFIG);

    }

    /**
     * 判断团单是否展示预约、留资、购买按钮
     */
    public static boolean isWeddingLeadsDeal(ProductBaseInfo productBaseInfo) {
        if (Objects.isNull(productBaseInfo) || Objects.isNull(productBaseInfo.getCategory())) {
            return false;
        }
        DealGroupCategoryDTO categoryDTO = productBaseInfo.getCategory();
        return LionConfigUtils.hitWeddingLeadsDeal(categoryDTO);
    }

}
