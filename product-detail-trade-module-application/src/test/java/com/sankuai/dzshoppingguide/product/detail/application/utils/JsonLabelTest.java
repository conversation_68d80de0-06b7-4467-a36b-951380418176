package com.sankuai.dzshoppingguide.product.detail.application.utils;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSON;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class JsonLabelTest {

    /**
     * 测试 buildJsonLabel 方法，所有参数都是正常值
     */
    @Test
    public void testBuildJsonLabelNormal() {
        // arrange
        String text = "test";
        int textsize = 16;
        String textcolor = "#000000";
        // act
        JsonLabel result = JsonLabel.buildJsonLabel(text, textsize, textcolor);
        // assert
        assertEquals(text, result.getText());
        assertEquals(textsize, result.getTextsize());
        assertEquals(textcolor, result.getTextcolor());
    }

    /**
     * 测试 buildJsonLabel 方法，text 是空字符串
     */
    @Test
    public void testBuildJsonLabelEmptyText() {
        // arrange
        String text = "";
        int textsize = 16;
        String textcolor = "#000000";
        // act
        JsonLabel result = JsonLabel.buildJsonLabel(text, textsize, textcolor);
        // assert
        assertEquals(text, result.getText());
        assertEquals(textsize, result.getTextsize());
        assertEquals(textcolor, result.getTextcolor());
    }

    /**
     * 测试 buildJsonLabel 方法，textsize 是边界值
     */
    @Test
    public void testBuildJsonLabelBoundaryTextsize() {
        // arrange
        String text = "test";
        int textsize = Integer.MAX_VALUE;
        String textcolor = "#000000";
        // act
        JsonLabel result = JsonLabel.buildJsonLabel(text, textsize, textcolor);
        // assert
        assertEquals(text, result.getText());
        assertEquals(textsize, result.getTextsize());
        assertEquals(textcolor, result.getTextcolor());
    }

    /**
     * 测试 buildJsonLabel 方法，textcolor 是空字符串
     */
    @Test
    public void testBuildJsonLabelEmptyTextcolor() {
        // arrange
        String text = "test";
        int textsize = 16;
        String textcolor = "";
        // act
        JsonLabel result = JsonLabel.buildJsonLabel(text, textsize, textcolor);
        // assert
        assertEquals(text, result.getText());
        assertEquals(textsize, result.getTextsize());
        assertEquals(textcolor, result.getTextcolor());
    }

    /**
     * Test building JsonLabel with valid parameters
     */
    @Test
    public void testBuildJsonLabel_WithValidParameters() {
        // arrange
        String text = "Sample Text";
        int textsize = 14;
        String textcolor = "#000000";
        String textstyle = "Normal";
        // act
        JsonLabel result = JsonLabel.buildJsonLabel(text, textsize, textcolor, textstyle);
        // assert
        assertNotNull(result);
        assertEquals(text, result.getText());
        assertEquals(textsize, result.getTextsize());
        assertEquals(textcolor, result.getTextcolor());
        assertEquals(textstyle, result.getTextstyle());
        assertEquals("#00FFFFFF", result.getBackgroundcolor());
        assertFalse(result.isStrikethrough());
        assertEquals(0, result.getFontweight());
    }

    /**
     * Test building JsonLabel with null text parameter
     */
    @Test
    public void testBuildJsonLabel_WithNullText() {
        // arrange
        String text = null;
        int textsize = 16;
        String textcolor = JsonLabel.BLACK;
        String textstyle = JsonLabel.BOLD;
        // act
        JsonLabel result = JsonLabel.buildJsonLabel(text, textsize, textcolor, textstyle);
        // assert
        assertNotNull(result);
        assertNull(result.getText());
        assertEquals(textsize, result.getTextsize());
        assertEquals(textcolor, result.getTextcolor());
        assertEquals(textstyle, result.getTextstyle());
    }

    /**
     * Test building JsonLabel with empty text parameter
     */
    @Test
    public void testBuildJsonLabel_WithEmptyText() {
        // arrange
        String text = "";
        int textsize = JsonLabel.TITLE_TEXT_SIZE;
        String textcolor = JsonLabel.RED;
        String textstyle = JsonLabel.BOLD;
        // act
        JsonLabel result = JsonLabel.buildJsonLabel(text, textsize, textcolor, textstyle);
        // assert
        assertNotNull(result);
        assertEquals("", result.getText());
        assertEquals(textsize, result.getTextsize());
        assertEquals(textcolor, result.getTextcolor());
        assertEquals(textstyle, result.getTextstyle());
    }

    /**
     * Test building JsonLabel with zero text size
     */
    @Test
    public void testBuildJsonLabel_WithZeroTextSize() {
        // arrange
        String text = "Test Text";
        int textsize = 0;
        String textcolor = JsonLabel.WHITE;
        String textstyle = JsonLabel.BOLD;
        // act
        JsonLabel result = JsonLabel.buildJsonLabel(text, textsize, textcolor, textstyle);
        // assert
        assertNotNull(result);
        assertEquals(text, result.getText());
        assertEquals(0, result.getTextsize());
        assertEquals(textcolor, result.getTextcolor());
        assertEquals(textstyle, result.getTextstyle());
    }

    /**
     * Test building JsonLabel with null color and style
     */
    @Test
    public void testBuildJsonLabel_WithNullColorAndStyle() {
        // arrange
        String text = "Test Text";
        int textsize = JsonLabel.TEXT_SIZE_MEDICAL_BEAUTY;
        String textcolor = null;
        String textstyle = null;
        // act
        JsonLabel result = JsonLabel.buildJsonLabel(text, textsize, textcolor, textstyle);
        // assert
        assertNotNull(result);
        assertEquals(text, result.getText());
        assertEquals(textsize, result.getTextsize());
        assertNull(result.getTextcolor());
        assertNull(result.getTextstyle());
    }

    /**
     * Test building JsonLabel with predefined constants
     */
    @Test
    public void testBuildJsonLabel_WithPredefinedConstants() {
        // arrange
        String text = "Test Text";
        int textsize = JsonLabel.YELLOW_TIPS_TEXT_SIZE;
        String textcolor = JsonLabel.YELLOW_TEXT_COLOR;
        String textstyle = JsonLabel.BOLD;
        // act
        JsonLabel result = JsonLabel.buildJsonLabel(text, textsize, textcolor, textstyle);
        // assert
        assertNotNull(result);
        assertEquals(text, result.getText());
        assertEquals(JsonLabel.YELLOW_TIPS_TEXT_SIZE, result.getTextsize());
        assertEquals(JsonLabel.YELLOW_TEXT_COLOR, result.getTextcolor());
        assertEquals(JsonLabel.BOLD, result.getTextstyle());
    }

    /**
     * Test convertToTitleJsonLabelStr with normal title string
     */
    @Test
    public void testConvertToTitleJsonLabelStr_WithNormalTitle() throws Throwable {
        // arrange
        String title = "Test Title";
        // act
        String result = JsonLabel.convertToTitleJsonLabelStr(title);
        // assert
        assertNotNull(result);
        assertTrue(result.contains("\"text\":\"Test Title\""));
        assertTrue(result.contains("\"textcolor\":\"#ECD8BC\""));
        assertTrue(result.contains("\"textsize\":13"));
        assertTrue(result.startsWith("["));
        assertTrue(result.endsWith("]"));
    }

    /**
     * Test convertToTitleJsonLabelStr with empty string
     */
    @Test
    public void testConvertToTitleJsonLabelStr_WithEmptyTitle() throws Throwable {
        // arrange
        String title = "";
        // act
        String result = JsonLabel.convertToTitleJsonLabelStr(title);
        // assert
        assertNotNull(result);
        assertTrue(result.contains("\"text\":\"\""));
        assertTrue(result.contains("\"textcolor\":\"#ECD8BC\""));
        assertTrue(result.contains("\"textsize\":13"));
        assertTrue(result.startsWith("["));
        assertTrue(result.endsWith("]"));
    }

    /**
     * Test convertToTitleJsonLabelStr with null title
     */
    @Test
    public void testConvertToTitleJsonLabelStr_WithNullTitle() throws Throwable {
        // arrange
        String title = null;
        // act
        String result = JsonLabel.convertToTitleJsonLabelStr(title);
        // assert
        assertNotNull(result);
        // Since the JsonLabel is created with null text, we just verify the other properties
        assertTrue(result.contains("\"textcolor\":\"#ECD8BC\""));
        assertTrue(result.contains("\"textsize\":13"));
        assertTrue(result.startsWith("["));
        assertTrue(result.endsWith("]"));
        // Verify it's a valid JSON array with at least one object
        assertTrue(result.contains("{"));
        assertTrue(result.contains("}"));
    }

    /**
     * Test convertToTitleJsonLabelStr JSON structure
     */
    @Test
    public void testConvertToTitleJsonLabelStr_VerifyLabelCreation() throws Throwable {
        // arrange
        String title = "Test";
        // act
        String result = JsonLabel.convertToTitleJsonLabelStr(title);
        // assert
        assertNotNull(result);
        assertTrue(result.contains("\"text\":\"Test\""));
        assertTrue(result.contains("\"textcolor\":\"#ECD8BC\""));
        assertTrue(result.contains("\"textsize\":13"));
        assertTrue(result.contains("\"backgroundcolor\":\"#00FFFFFF\""));
        assertTrue(result.contains("\"strikethrough\":false"));
        assertTrue(result.startsWith("["));
        assertTrue(result.endsWith("]"));
    }

    /**
     * Test convertToTitleJsonLabelStr when JSON conversion fails
     */
    @Test
    public void testConvertToTitleJsonLabelStr_WhenJsonConversionFails() throws Throwable {
        // arrange
        String title = "Test Title";
        // act
        String result = JsonLabel.convertToTitleJsonLabelStr(title);
        // assert
        assertNotNull(result);
        assertTrue(result.startsWith("["));
        assertTrue(result.endsWith("]"));
        assertTrue(result.contains("\"text\":\"Test Title\""));
        assertTrue(result.contains("\"textcolor\":\"#ECD8BC\""));
        assertTrue(result.contains("\"textsize\":13"));
    }

    /**
     * Test getJsonString with valid inputs
     */
    @Test
    public void testGetJsonStringWithValidInputs() throws Throwable {
        // arrange
        String text = "Sample Text";
        int textSize = 14;
        String color = "#000000";
        // act
        String result = JsonLabel.getJsonString(text, textSize, color);
        // assert
        assertNotNull(result);
        assertTrue(result.contains("\"text\":\"Sample Text\""));
        assertTrue(result.contains("\"textsize\":14"));
        assertTrue(result.contains("\"textcolor\":\"#000000\""));
    }

    /**
     * Test getJsonString with empty text
     */
    @Test
    public void testGetJsonStringWithEmptyText() throws Throwable {
        // arrange
        String text = "";
        int textSize = 14;
        String color = "#000000";
        // act
        String result = JsonLabel.getJsonString(text, textSize, color);
        // assert
        assertNotNull(result);
        assertTrue(result.contains("\"text\":\"\""));
        assertTrue(result.contains("\"textsize\":14"));
        assertTrue(result.contains("\"textcolor\":\"#000000\""));
    }

    /**
     * Test getJsonString with zero textSize
     */
    @Test
    public void testGetJsonStringWithZeroTextSize() throws Throwable {
        // arrange
        String text = "Sample Text";
        int textSize = 0;
        String color = "#000000";
        // act
        String result = JsonLabel.getJsonString(text, textSize, color);
        // assert
        assertNotNull(result);
        assertTrue(result.contains("\"text\":\"Sample Text\""));
        assertTrue(result.contains("\"textsize\":0"));
        assertTrue(result.contains("\"textcolor\":\"#000000\""));
    }

    /**
     * Test getJsonString with negative textSize
     */
    @Test
    public void testGetJsonStringWithNegativeTextSize() throws Throwable {
        // arrange
        String text = "Sample Text";
        int textSize = -5;
        String color = "#000000";
        // act
        String result = JsonLabel.getJsonString(text, textSize, color);
        // assert
        assertNotNull(result);
        assertTrue(result.contains("\"text\":\"Sample Text\""));
        assertTrue(result.contains("\"textsize\":-5"));
        assertTrue(result.contains("\"textcolor\":\"#000000\""));
    }

    /**
     * Test getJsonString with null color
     */
    @Test
    public void testGetJsonStringWithNullColor() throws Throwable {
        // arrange
        String text = "Sample Text";
        int textSize = 14;
        String color = null;
        // act
        String result = JsonLabel.getJsonString(text, textSize, color);
        // assert
        assertNotNull(result);
        assertTrue(result.contains("\"text\":\"Sample Text\""));
        assertTrue(result.contains("\"textsize\":14"));
        // FastJSON might omit null values or format them differently
        assertFalse(result.contains("\"textcolor\":\"null\""));
    }

    /**
     * Test getJsonString with empty color
     */
    @Test
    public void testGetJsonStringWithEmptyColor() throws Throwable {
        // arrange
        String text = "Sample Text";
        int textSize = 14;
        String color = "";
        // act
        String result = JsonLabel.getJsonString(text, textSize, color);
        // assert
        assertNotNull(result);
        assertTrue(result.contains("\"text\":\"Sample Text\""));
        assertTrue(result.contains("\"textsize\":14"));
        assertTrue(result.contains("\"textcolor\":\"\""));
    }

    /**
     * Test getJsonString with null text
     * Note: FastJSON handles null values according to its configuration
     */
    @Test
    public void testGetJsonStringWithNullText() throws Throwable {
        // arrange
        String text = null;
        int textSize = 14;
        String color = "#000000";
        // act
        String result = JsonLabel.getJsonString(text, textSize, color);
        // assert
        assertNotNull(result);
        // FastJSON might omit null values or format them differently
        assertFalse(result.contains("\"text\":\"null\""));
        assertTrue(result.contains("\"textsize\":14"));
        assertTrue(result.contains("\"textcolor\":\"#000000\""));
    }
}
