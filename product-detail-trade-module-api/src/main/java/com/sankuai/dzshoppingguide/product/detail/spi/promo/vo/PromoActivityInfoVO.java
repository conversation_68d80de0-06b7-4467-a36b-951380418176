package com.sankuai.dzshoppingguide.product.detail.spi.promo.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;
@Data
@MobileDo(id = 0xf607)
public class PromoActivityInfoVO implements Serializable {
    /**
    * 活动类型：消费返、下单返、评价返等
    */
    @MobileDo.MobileField(key = 0xae1c)
    private String bonusType;

    /**
    * 描述文案，e.g. 新客下单减10元，每人可享1次
    */
    @MobileDo.MobileField(key = 0x451b)
    private String text;

    /**
    * 样式类型，0：普通文案型，1：标签型 现在应该都是传的0
    */
    @MobileDo.MobileField(key = 0x1b3a)
    private int style;

    /**
    * 跳转URL
    */
    @MobileDo.MobileField(key = 0x2373)
    private String leadUrl;

    /**
    * 短文案
    */
    @MobileDo.MobileField(key = 0x18d7)
    private String shortText;

}