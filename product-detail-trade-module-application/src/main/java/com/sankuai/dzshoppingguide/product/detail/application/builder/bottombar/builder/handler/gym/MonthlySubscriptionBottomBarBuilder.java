package com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.builder.handler.gym;

import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.BuilderTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.builder.BaseBottomBarBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.component.trade.block.buy.button.gym.MonthlySubscriptionBottomComponent;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.exception.BottomBarFatalException;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.consult.DetailIMFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.consult.DetailIMResult;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.OrderPageUrlResult;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.handler.normal.NormalOrderPageUrlFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.FetcherDAGStarter;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.ProductBuyBarModule;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.block.trade.StandardTradeBlockVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.scene.StandardTradeBottomBarVO;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import lombok.extern.slf4j.Slf4j;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/05/10
 * 次卡-> 连续包月
 */
@Builder(
        builderType = BuilderTypeEnum.VARIABLE_BUILDER,
        moduleKey = ModuleKeyConstants.BOTTOM_BAR,
        startFetcher = FetcherDAGStarter.class,
        dependentFetchers = {
                NormalOrderPageUrlFetcher.class,
                ShopIdMapperFetcher.class,
                DetailIMFetcher.class,
        }
)
@Slf4j
public class MonthlySubscriptionBottomBarBuilder extends BaseBottomBarBuilder {

    protected OrderPageUrlResult orderPageUrlResult;
    protected String orderPageUrl;
    protected ShopIdMapper shopIdMapper;
    protected DetailIMResult detailIMResult;


    @Override
    public ProductBuyBarModule buildBuyBarModule() {
        orderPageUrlResult = getDependencyResult(NormalOrderPageUrlFetcher.class);
        orderPageUrl = Optional.ofNullable(orderPageUrlResult).map(OrderPageUrlResult::getUrl).orElse(null);
        shopIdMapper = getDependencyResult(ShopIdMapperFetcher.class);
        detailIMResult = getDependencyResult(DetailIMFetcher.class);
        ProductBuyBarModule dealBuyBarVO = new ProductBuyBarModule();
        try {
            StandardTradeBottomBarVO bottomBarVO = new StandardTradeBottomBarVO();
            bottomBarVO.setRightBottomBar(StandardTradeBlockVO.buildSingleButtonStyle(
                    MonthlySubscriptionBottomComponent.build(request, orderPageUrl)
            ));
            dealBuyBarVO = ProductBuyBarModule.buildWithoutBanner(bottomBarVO);
            return dealBuyBarVO;
        } catch (Throwable throwable) {
            log.error("MonthlySubscriptionBottomBarBuilder.buildBuyBarModule", new BottomBarFatalException(throwable));
        }
        return dealBuyBarVO;
    }

    @Override
    protected ProductBuyBarModule buildDefaultBuyBarModule() {
        StandardTradeBottomBarVO bottomBarVO = new StandardTradeBottomBarVO();
        bottomBarVO.setRightBottomBar(StandardTradeBlockVO.buildSingleButtonStyle(
                MonthlySubscriptionBottomComponent.build(request, orderPageUrl)
        ));
        return ProductBuyBarModule.buildWithoutBanner(bottomBarVO);
    }

}
