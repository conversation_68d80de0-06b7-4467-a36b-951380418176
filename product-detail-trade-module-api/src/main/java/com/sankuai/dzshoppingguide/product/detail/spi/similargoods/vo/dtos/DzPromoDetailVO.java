package com.sankuai.dzshoppingguide.product.detail.spi.similargoods.vo.dtos;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 优惠细节
 * @auther: liweilong06
 * @date: 2021/5/5 下午4:32
 */
@Data
@MobileDo(id = 0xe9a1)
public class DzPromoDetailVO implements Serializable {
    /**
     * 优惠标签
     */
    @MobileDo.MobileField(key = 0x74ac)
    private String promoTag;

    /**
     * 优惠条目
     */
    @MobileDo.MobileField(key = 0x4718)
    private List<DzPromoPerItemVO> promoItems;

    /**
     * 总优惠金额, 例如-¥10
     */
    @MobileDo.MobileField(key = 0x9f03)
    private String totalPromoPrice;

    /**
     * 总优惠Lab
     */
    @MobileDo.MobileField(key = 0x8b59)
    private String totalPromoLab;

    /**
     * 优惠弹窗标题
     */
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    public String getPromoTag() {
        return promoTag;
    }

    public void setPromoTag(String promoTag) {
        this.promoTag = promoTag;
    }

    public List<DzPromoPerItemVO> getPromoItems() {
        return promoItems;
    }

    public void setPromoItems(List<DzPromoPerItemVO> promoItems) {
        this.promoItems = promoItems;
    }

    public String getTotalPromoPrice() {
        return totalPromoPrice;
    }

    public void setTotalPromoPrice(String totalPromoPrice) {
        this.totalPromoPrice = totalPromoPrice;
    }

    public String getTotalPromoLab() {
        return totalPromoLab;
    }

    public void setTotalPromoLab(String totalPromoLab) {
        this.totalPromoLab = totalPromoLab;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }
}
