package com.sankuai.dzshoppingguide.product.detail.application.fetcher.cepinpool.dto;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * Unit tests for {@link CostEffectivePinTuan#getNeedMemberCount()}
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("CostEffectivePinTuan Tests")
public class CostEffectivePinTuanTest {

    /**
     * Test scenario: helpSuccCountMin is less than hasHelpCount
     * Expected: Should return 0 as no additional members are needed
     */
    @Test
    @DisplayName("When helpSuccCountMin < hasHelpCount, should return 0")
    public void testGetNeedMemberCount_WhenHelpSuccCountMinLessThanHasHelpCount() {
        // arrange
        CostEffectivePinTuan costEffectivePinTuan = new CostEffectivePinTuan();
        costEffectivePinTuan.setHelpSuccCountMin(2);
        costEffectivePinTuan.setHasHelpCount(3);
        // act
        int result = costEffectivePinTuan.getNeedMemberCount();
        // assert
        assertEquals(0, result, "Should return 0 when helpSuccCountMin(2) is less than hasHelpCount(3)");
    }

    /**
     * Test scenario: helpSuccCountMin equals hasHelpCount
     * Expected: Should return 0 as the required number of members is met
     */
    @Test
    @DisplayName("When helpSuccCountMin = hasHelpCount, should return 0")
    public void testGetNeedMemberCount_WhenHelpSuccCountMinEqualsHasHelpCount() {
        // arrange
        CostEffectivePinTuan costEffectivePinTuan = new CostEffectivePinTuan();
        costEffectivePinTuan.setHelpSuccCountMin(3);
        costEffectivePinTuan.setHasHelpCount(3);
        // act
        int result = costEffectivePinTuan.getNeedMemberCount();
        // assert
        assertEquals(0, result, "Should return 0 when helpSuccCountMin(3) equals hasHelpCount(3)");
    }

    /**
     * Test scenario: helpSuccCountMin is greater than hasHelpCount
     * Expected: Should return the difference between helpSuccCountMin and hasHelpCount
     */
    @Test
    @DisplayName("When helpSuccCountMin > hasHelpCount, should return difference")
    public void testGetNeedMemberCount_WhenHelpSuccCountMinGreaterThanHasHelpCount() {
        // arrange
        CostEffectivePinTuan costEffectivePinTuan = new CostEffectivePinTuan();
        costEffectivePinTuan.setHelpSuccCountMin(5);
        costEffectivePinTuan.setHasHelpCount(2);
        // act
        int result = costEffectivePinTuan.getNeedMemberCount();
        // assert
        assertEquals(3, result, "Should return 3 when helpSuccCountMin(5) is greater than hasHelpCount(2)");
    }

    /**
     * Test scenario: Both helpSuccCountMin and hasHelpCount are zero
     * Expected: Should return 0 as boundary case
     */
    @Test
    @DisplayName("When both values are zero, should return 0")
    public void testGetNeedMemberCount_WhenBothValuesAreZero() {
        // arrange
        CostEffectivePinTuan costEffectivePinTuan = new CostEffectivePinTuan();
        costEffectivePinTuan.setHelpSuccCountMin(0);
        costEffectivePinTuan.setHasHelpCount(0);
        // act
        int result = costEffectivePinTuan.getNeedMemberCount();
        // assert
        assertEquals(0, result, "Should return 0 when both helpSuccCountMin and hasHelpCount are 0");
    }

    /**
     * Test scenario: Maximum possible values
     * Expected: Should handle large numbers correctly
     */
    @Test
    @DisplayName("When using maximum values, should calculate correctly")
    public void testGetNeedMemberCount_WithMaximumValues() {
        // arrange
        CostEffectivePinTuan costEffectivePinTuan = new CostEffectivePinTuan();
        costEffectivePinTuan.setHelpSuccCountMin(Integer.MAX_VALUE);
        costEffectivePinTuan.setHasHelpCount(Integer.MAX_VALUE - 1);
        // act
        int result = costEffectivePinTuan.getNeedMemberCount();
        // assert
        assertEquals(1, result, "Should return 1 when using maximum values");
    }

    /**
     * Test enhancedStyle() when sceneType is 56
     * Expected: should return true
     */
    @Test
    @DisplayName("Test enhancedStyle when sceneType is 56")
    public void testEnhancedStyleWhenSceneTypeIs56() throws Throwable {
        // arrange
        CostEffectivePinTuan costEffectivePinTuan = new CostEffectivePinTuan();
        costEffectivePinTuan.setSceneType(56);
        // act
        boolean result = costEffectivePinTuan.enhancedStyle();
        // assert
        assertTrue(result, "enhancedStyle should return true when sceneType is 56");
    }

    /**
     * Test enhancedStyle() when sceneType is 0
     * Expected: should return false
     */
    @Test
    @DisplayName("Test enhancedStyle when sceneType is 0")
    public void testEnhancedStyleWhenSceneTypeIsZero() throws Throwable {
        // arrange
        CostEffectivePinTuan costEffectivePinTuan = new CostEffectivePinTuan();
        costEffectivePinTuan.setSceneType(0);
        // act
        boolean result = costEffectivePinTuan.enhancedStyle();
        // assert
        assertFalse(result, "enhancedStyle should return false when sceneType is 0");
    }

    /**
     * Test enhancedStyle() with boundary value 55
     * Expected: should return false
     */
    @Test
    @DisplayName("Test enhancedStyle with boundary value 55")
    public void testEnhancedStyleWithBoundaryValue55() throws Throwable {
        // arrange
        CostEffectivePinTuan costEffectivePinTuan = new CostEffectivePinTuan();
        costEffectivePinTuan.setSceneType(55);
        // act
        boolean result = costEffectivePinTuan.enhancedStyle();
        // assert
        assertFalse(result, "enhancedStyle should return false when sceneType is 55");
    }

    /**
     * Test enhancedStyle() with boundary value 57
     * Expected: should return false
     */
    @Test
    @DisplayName("Test enhancedStyle with boundary value 57")
    public void testEnhancedStyleWithBoundaryValue57() throws Throwable {
        // arrange
        CostEffectivePinTuan costEffectivePinTuan = new CostEffectivePinTuan();
        costEffectivePinTuan.setSceneType(57);
        // act
        boolean result = costEffectivePinTuan.enhancedStyle();
        // assert
        assertFalse(result, "enhancedStyle should return false when sceneType is 57");
    }

    /**
     * Tests the inPinTuan method when isPinTuanOpened returns true and inPinTuan returns true, it should return true.
     */
    @Test
    public void testInPinTuanWhenIsPinTuanOpenedAndInPinTuanAreTrue() throws Throwable {
        // arrange
        CostEffectivePinTuan costEffectivePinTuan = new CostEffectivePinTuan();
        costEffectivePinTuan.setPinTuanOpened(true);
        costEffectivePinTuan.setInPinTuan(true);
        // act
        boolean result = costEffectivePinTuan.inPinTuan();
        // assert
        assertTrue(result);
    }

    /**
     * Tests the inPinTuan method when isPinTuanOpened returns true and inPinTuan returns false, it should return false.
     */
    @Test
    public void testInPinTuanWhenIsPinTuanOpenedAndInPinTuanAreFalseFirstCase() throws Throwable {
        // arrange
        CostEffectivePinTuan costEffectivePinTuan = new CostEffectivePinTuan();
        costEffectivePinTuan.setPinTuanOpened(true);
        costEffectivePinTuan.setInPinTuan(false);
        // act
        boolean result = costEffectivePinTuan.inPinTuan();
        // assert
        assertFalse(result);
    }

    /**
     * Tests the inPinTuan method when isPinTuanOpened returns false and inPinTuan returns true, it should return false.
     */
    @Test
    public void testInPinTuanWhenIsPinTuanOpenedIsFalseAndInPinTuanIsTrue() throws Throwable {
        // arrange
        CostEffectivePinTuan costEffectivePinTuan = new CostEffectivePinTuan();
        costEffectivePinTuan.setPinTuanOpened(false);
        costEffectivePinTuan.setInPinTuan(true);
        // act
        boolean result = costEffectivePinTuan.inPinTuan();
        // assert
        assertFalse(result);
    }

    /**
     * Tests the inPinTuan method when isPinTuanOpened returns false and inPinTuan returns false, it should return false.
     */
    @Test
    public void testInPinTuanWhenIsPinTuanOpenedAndInPinTuanAreFalseSecondCase() throws Throwable {
        // arrange
        CostEffectivePinTuan costEffectivePinTuan = new CostEffectivePinTuan();
        costEffectivePinTuan.setPinTuanOpened(false);
        costEffectivePinTuan.setInPinTuan(false);
        // act
        boolean result = costEffectivePinTuan.inPinTuan();
        // assert
        assertFalse(result);
    }

    /**
     * Tests the inPinTuan method when isPinTuanOpened returns true and inPinTuan returns false, it should return false.
     */
    @Test
    public void testInPinTuanWhenIsPinTuanOpenedAndInPinTuanAreFalse() throws Throwable {
        // arrange
        CostEffectivePinTuan costEffectivePinTuan = new CostEffectivePinTuan();
        costEffectivePinTuan.setPinTuanOpened(true);
        costEffectivePinTuan.setInPinTuan(false);
        // act
        boolean result = costEffectivePinTuan.inPinTuan();
        // assert
        assertFalse(result);
    }
}
