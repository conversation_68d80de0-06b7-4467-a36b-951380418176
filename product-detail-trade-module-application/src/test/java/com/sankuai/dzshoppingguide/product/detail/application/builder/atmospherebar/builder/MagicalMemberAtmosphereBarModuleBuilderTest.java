package com.sankuai.dzshoppingguide.product.detail.application.builder.atmospherebar.builder;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.google.common.collect.Lists;
import com.sankuai.dealuser.price.display.api.enums.ExtendDisplayInfoKeyEnum;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import com.sankuai.dealuser.price.display.api.model.PromoIdentity;
import com.sankuai.dzshoppingguide.product.detail.application.builder.pricebar.builder.enums.CouponGuideTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.pricebar.component.InflateCouponTipsComponent;
import com.sankuai.dzshoppingguide.product.detail.application.builder.pricebar.config.IconConfig;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.abtest.AbTestReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.utils.LionConfigUtils;
import com.sankuai.dzshoppingguide.product.detail.application.utils.PriceHelper;
import com.sankuai.dzshoppingguide.product.detail.application.utils.PriceUtils;
import com.sankuai.dzshoppingguide.product.detail.application.utils.PromoHelper;
import com.sankuai.dzshoppingguide.product.detail.domain.douhu.AbTestResult;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.ProductBestPromoFormula;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.PromoTagEnum;
import com.sankuai.nibmkt.promotion.api.common.enums.PromotionPropertyEnum;
import com.sankuai.nibmkt.promotion.api.query.model.magicalmember.response.MMCTooltipTextDTO;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@ExtendWith(MockitoExtension.class)
public class MagicalMemberAtmosphereBarModuleBuilderTest {
    Map<String, AbTestResult> abTestResultMap = new HashMap<>();
    AbTestReturnValue abTestReturnValue = new AbTestReturnValue(abTestResultMap);


    @InjectMocks
    private MagicalMemberAtmosphereBarModuleBuilder builder;

    private static final String MARKET_PRICE_TEXT = "商品/服务展示的门市价为参考价，该价格可能是品牌专柜标价、商品吊牌价或由品牌供应商提供的正品零售价（如厂商指导价、建议零售价等）或该商品/服务在美团点评平台上曾经展示过的销售价或商品/服务的实体门店指导价、实体门店展示销售价、实体门店曾经销售过的价格等；由于地区、时间的差异性和市场行情波动，品牌专柜标价、商品吊牌价、实体门店指导价、实体门店展示销售价等可能会与用户购物/消费时展示的不一致，该价格仅供参考。";

    /**
     * Test when promoPriceDisplayDTO is null
     */
    @Test
    public void testBuildBestPromoFormula_NullInput() throws Throwable {
        // arrange
        PriceDisplayDTO promoPriceDisplayDTO = null;
        String finalPrice = "100";
        // act
        List<ProductBestPromoFormula> result = builder.buildBestPromoFormula(promoPriceDisplayDTO, finalPrice, abTestReturnValue);
        // assert
        assertNull(result);
    }

    @Test
    public void testBuildBestPromoFormula_WhenIdentityIsNull() throws Throwable {
        // arrange
        PriceDisplayDTO promoPriceDisplayDTO = new PriceDisplayDTO();
        promoPriceDisplayDTO.setMarketPrice(new BigDecimal("100"));
        List<PromoDTO> usedPromos = new ArrayList<>();
        PromoDTO promoDTO = new PromoDTO();
        promoDTO.setAmount(new BigDecimal("10"));
        usedPromos.add(promoDTO);
        promoPriceDisplayDTO.setUsedPromos(usedPromos);
        // act
        List<ProductBestPromoFormula> result = builder.buildBestPromoFormula(promoPriceDisplayDTO, "90", abTestReturnValue);
        // assert
        assertNotNull(result);
        // Only market price formula should be present
        assertEquals(1, result.size());
    }

    @Test
    public void testBuildBestPromoFormula_WhenPromoTypeDescIsEmpty() throws Throwable {
        // arrange
        PriceDisplayDTO promoPriceDisplayDTO = new PriceDisplayDTO();
        promoPriceDisplayDTO.setMarketPrice(new BigDecimal("100"));
        List<PromoDTO> usedPromos = new ArrayList<>();
        PromoDTO promoDTO = new PromoDTO();
        // Empty promoTypeDesc
        PromoIdentity identity = new PromoIdentity(1234L, 0, "");
        promoDTO.setIdentity(identity);
        promoDTO.setAmount(new BigDecimal("10"));
        usedPromos.add(promoDTO);
        promoPriceDisplayDTO.setUsedPromos(usedPromos);
        // act
        List<ProductBestPromoFormula> result = builder.buildBestPromoFormula(promoPriceDisplayDTO, "90", abTestReturnValue);
        // assert
        assertNotNull(result);
        // Only market price formula should be present
        assertEquals(1, result.size());
    }
}
