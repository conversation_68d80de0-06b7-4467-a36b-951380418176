package com.sankuai.dzshoppingguide.product.detail.spi.consult.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.sankuai.dz.product.detail.gateway.spi.vo.AbstractModuleVO;

import static com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants.MODULE_DETAIL_DEAL_CONSULT_MODULE;

/**
 * <AUTHOR>
 */
@MobileDo(id = 0x2af7)
public class DealDetailIMVO extends AbstractModuleVO {
    /**
    * 原quickconsult.bin接口的返回结果
    */
    @MobileDo.MobileField(key = 0x58ac)
    private DzCommonQuickConsultVO quickConsult;

    /**
    * 无协议头、未编码版本的imURL
    */
    @MobileDo.MobileField(key = 0xf33e)
    private String originalOnlineConsultUrl;

    /**
    * 团详页底部bar咨询按钮展示
    */
    @MobileDo.MobileField(key = 0xc1d0)
    private String onlineConsultUrl;

    public DzCommonQuickConsultVO getQuickConsult() {
        return quickConsult;
    }

    public void setQuickConsult(DzCommonQuickConsultVO quickConsult) {
        this.quickConsult = quickConsult;
    }

    public String getOriginalOnlineConsultUrl() {
        return originalOnlineConsultUrl;
    }

    public void setOriginalOnlineConsultUrl(String originalOnlineConsultUrl) {
        this.originalOnlineConsultUrl = originalOnlineConsultUrl;
    }

    public String getOnlineConsultUrl() {
        return onlineConsultUrl;
    }

    public void setOnlineConsultUrl(String onlineConsultUrl) {
        this.onlineConsultUrl = onlineConsultUrl;
    }

    @Override
    public String getModuleKey() {
        return MODULE_DETAIL_DEAL_CONSULT_MODULE;
    }
}