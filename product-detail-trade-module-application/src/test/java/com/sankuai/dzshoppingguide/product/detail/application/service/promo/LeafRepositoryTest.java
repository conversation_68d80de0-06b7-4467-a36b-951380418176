package com.sankuai.dzshoppingguide.product.detail.application.service.promo;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;
import com.sankuai.inf.leaf.thrift.IDGen;
import com.sankuai.inf.leaf.thrift.Result;
import com.sankuai.inf.leaf.thrift.Status;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.apache.thrift.TException;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class LeafRepositoryTest {

    @InjectMocks
    private LeafRepository leafRepository;

    @Mock
    private IDGen.Iface idGen;

    private Result createResult(Status status, long id) {
        Result result = new Result();
        result.setStatus(status);
        result.setId(id);
        return result;
    }

    /**
     * Test case for batchNum exceeding MAX_LEAF_BATCH_NUM
     */
    @Test
    public void testBatchGenFinancialConsumeSerialIdBatchNumExceedMax() throws Throwable {
        int batchNum = 101;
        List<Long> result = leafRepository.batchGenFinancialConsumeSerialId(batchNum);
        assertNull(result);
    }

    /**
     * Test case for empty batch result
     */
    @Test
    public void testBatchGenFinancialConsumeSerialIdBatchResultEmpty() throws Throwable {
        int batchNum = 100;
        when(idGen.getSnowFlakeBatch(anyString(), eq(batchNum))).thenReturn(Collections.emptyList());
        List<Long> result = leafRepository.batchGenFinancialConsumeSerialId(batchNum);
        assertNull(result);
    }

    /**
     * Test case for batch result with all successful results
     */
    @Test
    public void testBatchGenFinancialConsumeSerialIdBatchResultAllSuccess() throws Throwable {
        int batchNum = 2;
        List<Result> results = new ArrayList<>();
        results.add(createResult(Status.SUCCESS, 1L));
        results.add(createResult(Status.SUCCESS, 2L));
        when(idGen.getSnowFlakeBatch(anyString(), eq(batchNum))).thenReturn(results);
        List<Long> resultList = leafRepository.batchGenFinancialConsumeSerialId(batchNum);
        assertNotNull(resultList);
        assertEquals(2, resultList.size());
        assertEquals(Long.valueOf(1), resultList.get(0));
        assertEquals(Long.valueOf(2), resultList.get(1));
    }

    /**
     * Test case for exception with timeout
     */
    @Test
    public void testBatchGenFinancialConsumeSerialIdExceptionWithTimeout() throws Throwable {
        int batchNum = 100;
        when(idGen.getSnowFlakeBatch(anyString(), eq(batchNum))).thenThrow(new RuntimeException("timeout"));
        List<Long> resultList = leafRepository.batchGenFinancialConsumeSerialId(batchNum);
        assertNull(resultList);
    }

    /**
     * Test case for exception without timeout and max retry reached
     */
    @Test
    public void testBatchGenFinancialConsumeSerialIdExceptionWithoutTimeoutAndMaxRetry() throws Throwable {
        int batchNum = 100;
        when(idGen.getSnowFlakeBatch(anyString(), eq(batchNum))).thenThrow(new RuntimeException("no timeout"));
        List<Long> resultList = leafRepository.batchGenFinancialConsumeSerialId(batchNum);
        assertNull(resultList);
    }

    /**
     * Test case for exception without timeout and retry not maxed out
     */
    @Test
    public void testBatchGenFinancialConsumeSerialIdExceptionWithoutTimeoutAndNotMaxRetry() throws Throwable {
        int batchNum = 100;
        when(idGen.getSnowFlakeBatch(anyString(), eq(batchNum))).thenThrow(new RuntimeException("no timeout"));
        List<Long> resultList = leafRepository.batchGenFinancialConsumeSerialId(batchNum);
        assertNull(resultList);
    }
}
