package com.sankuai.dzshoppingguide.product.detail.application.fetcher.pinpool;

import static org.junit.jupiter.api.Assertions.*;
import com.dianping.tpfun.product.api.sku.pintuan.dto.PinProductBrief;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import java.util.HashMap;
import java.util.Map;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class PricePinPoolFetcherTest {

    @InjectMocks
    private PricePinPoolFetcher pricePinPoolFetcher;

    /**
     * Test case for null input map
     */
    @Test
    public void testGetPinProductBriefWithNullMap() {
        // arrange
        Map<Integer, PinProductBrief> nullMap = null;
        // act
        PinProductBrief result = pricePinPoolFetcher.getPinProductBrief(nullMap);
        // assert
        assertNull(result, "Result should be null for null input map");
    }

    /**
     * Test case for empty input map
     */
    @Test
    public void testGetPinProductBriefWithEmptyMap() {
        // arrange
        Map<Integer, PinProductBrief> emptyMap = new HashMap<>();
        // act
        PinProductBrief result = pricePinPoolFetcher.getPinProductBrief(emptyMap);
        // assert
        assertNull(result, "Result should be null for empty input map");
    }

    /**
     * Test case for input map with one entry
     */
    @Test
    public void testGetPinProductBriefWithOneEntry() {
        // arrange
        Map<Integer, PinProductBrief> map = new HashMap<>();
        PinProductBrief expectedBrief = new PinProductBrief();
        map.put(1, expectedBrief);
        // act
        PinProductBrief result = pricePinPoolFetcher.getPinProductBrief(map);
        // assert
        assertSame(expectedBrief, result, "Result should be the only entry in the map");
    }

    /**
     * Test case for input map with multiple entries
     */
    @Test
    public void testGetPinProductBriefWithMultipleEntries() {
        // arrange
        Map<Integer, PinProductBrief> map = new HashMap<>();
        PinProductBrief firstBrief = new PinProductBrief();
        PinProductBrief secondBrief = new PinProductBrief();
        map.put(1, firstBrief);
        map.put(2, secondBrief);
        // act
        PinProductBrief result = pricePinPoolFetcher.getPinProductBrief(map);
        // assert
        assertNotNull(result, "Result should not be null");
        assertTrue(result == firstBrief || result == secondBrief, "Result should be one of the entries in the map");
    }

    /**
     * Test scenario: When client type is DP_XCX
     * Expected: Should return true as DP_XCX is a mini program type
     */
    @Test
    @DisplayName("Should return true when client type is DP_XCX")
    void testIsMiniProgram_WhenDpXcx_ShouldReturnTrue() {
        // arrange
        ClientTypeEnum clientType = ClientTypeEnum.DP_XCX;
        // act
        boolean result = pricePinPoolFetcher.isMiniProgram(clientType);
        // assert
        assertTrue(result, "DP_XCX should be identified as mini program");
    }

    /**
     * Test scenario: When client type is MT_XCX
     * Expected: Should return true as MT_XCX is a mini program type
     */
    @Test
    @DisplayName("Should return true when client type is MT_XCX")
    void testIsMiniProgram_WhenMtXcx_ShouldReturnTrue() {
        // arrange
        ClientTypeEnum clientType = ClientTypeEnum.MT_XCX;
        // act
        boolean result = pricePinPoolFetcher.isMiniProgram(clientType);
        // assert
        assertTrue(result, "MT_XCX should be identified as mini program");
    }

    /**
     * Test scenario: When client type is DP_APP
     * Expected: Should return false as DP_APP is not a mini program type
     */
    @Test
    @DisplayName("Should return false when client type is DP_APP")
    void testIsMiniProgram_WhenDpApp_ShouldReturnFalse() {
        // arrange
        ClientTypeEnum clientType = ClientTypeEnum.DP_APP;
        // act
        boolean result = pricePinPoolFetcher.isMiniProgram(clientType);
        // assert
        assertFalse(result, "DP_APP should not be identified as mini program");
    }

    /**
     * Test scenario: When client type is MT_APP
     * Expected: Should return false as MT_APP is not a mini program type
     */
    @Test
    @DisplayName("Should return false when client type is MT_APP")
    void testIsMiniProgram_WhenMtApp_ShouldReturnFalse() {
        // arrange
        ClientTypeEnum clientType = ClientTypeEnum.MT_APP;
        // act
        boolean result = pricePinPoolFetcher.isMiniProgram(clientType);
        // assert
        assertFalse(result, "MT_APP should not be identified as mini program");
    }

    /**
     * Test scenario: When client type is null
     * Expected: Should return false as null is not a valid mini program type
     */
    @Test
    @DisplayName("Should return false when client type is null")
    void testIsMiniProgram_WhenNull_ShouldReturnFalse() {
        // arrange
        ClientTypeEnum clientType = null;
        // act
        boolean result = pricePinPoolFetcher.isMiniProgram(clientType);
        // assert
        assertFalse(result, "Null client type should not be identified as mini program");
    }
}
