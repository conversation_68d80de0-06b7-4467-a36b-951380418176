package com.sankuai.dzshoppingguide.product.detail.application.utils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

public class DateTimeUtils {

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final DateTimeFormatter MONTH_DATE_FORMATTER = DateTimeFormatter.ofPattern("MM-dd");


    public static String getDate(LocalDateTime time) {
        return time.format(DATE_FORMATTER);
    }

    public static String getOffsetDate(int offsetDay) {
        return LocalDateTime.now().plusDays(offsetDay).format(DATE_FORMATTER);
    }

    public static String getDateTime(LocalDateTime time) {
        return time.format(DATE_TIME_FORMATTER);
    }

    public static String getMonthDate(LocalDateTime time) {
        return time.format(MONTH_DATE_FORMATTER);
    }

    public static String getCurrentDateTime() {
        LocalDateTime now = LocalDateTime.now();
        return getDateTime(now);
    }

    public static String getWeekStr(LocalDateTime time) {
        switch (time.getDayOfWeek()) {
            case MONDAY:
                return "周一";
            case TUESDAY:
                return "周二";
            case WEDNESDAY:
                return "周三";
            case THURSDAY:
                return "周四";
            case FRIDAY:
                return "周五";
            case SATURDAY:
                return "周六";
            case SUNDAY:
                return "周日";
            default:
                return "";
        }
    }

    public static String getCrossWeekStr(LocalDateTime time) {
        LocalDateTime now = LocalDateTime.now();
        // 获取本周的周日
        LocalDateTime weekEnd = now.plusDays(7 - now.getDayOfWeek().getValue()).withHour(23).withMinute(59).withSecond(59).withNano(999999999);
        // 跨周返回日期
        if (time.isAfter(weekEnd)) {
            return getMonthDate(time);
        }
        return getWeekStr(time);
    }

}
