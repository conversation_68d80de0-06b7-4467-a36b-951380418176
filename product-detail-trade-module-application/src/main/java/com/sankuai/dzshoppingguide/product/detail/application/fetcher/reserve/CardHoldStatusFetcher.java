package com.sankuai.dzshoppingguide.product.detail.application.fetcher.reserve;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dz.product.detail.gateway.spi.enums.ProductTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzcard.navigation.api.dto.FindDCCardHoldStatusLiteReqDTO;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/2/7 15:52
 */
@Fetcher(previousLayerDependencies = {ShopIdMapperFetcher.class})
@Slf4j
public class CardHoldStatusFetcher extends NormalFetcherContext<CardHoldStatusResult> {

    @Autowired
    private CompositeAtomService compositeAtomService;

    @Override
    protected CompletableFuture<CardHoldStatusResult> doFetch() {
        try {
            if (!Objects.equals(ProductTypeEnum.RESERVE, request.getProductTypeEnum())) {
                return CompletableFuture.completedFuture(null);
            }

            ShopIdMapper shopIdMapper = getDependencyResult(ShopIdMapperFetcher.class);
            if (request == null || request.getShepherdGatewayParam() == null || shopIdMapper == null) {
                return CompletableFuture.completedFuture(null);
            }
            return compositeAtomService.queryShopAndUserCardHoldStatus(buildRequest(request, shopIdMapper))
                    .thenApply(res -> {
                        if (res == null) {
                            return null;
                        }
                        return new CardHoldStatusResult(res);
                    });

        } catch (Exception e) {
            log.error("CardHoldStatusFetcher error,request:{}", JsonCodec.encodeWithUTF8(request), e);
        }
        return CompletableFuture.completedFuture(null);
    }

    private FindDCCardHoldStatusLiteReqDTO buildRequest(ProductDetailPageRequest request, ShopIdMapper idMapperResult) {
        int platform = request.getPlatformEnum().getCode();
        FindDCCardHoldStatusLiteReqDTO findCardHoldStatusReqDTO = new FindDCCardHoldStatusLiteReqDTO();
        findCardHoldStatusReqDTO.setPlatform(platform);
        findCardHoldStatusReqDTO
                .setDpShopId(Optional.ofNullable(idMapperResult).map(ShopIdMapper::getDpBestShopId).orElse(0L));
        if (request.getClientTypeEnum().isMtClientType()) {
            long mtUserId = request.getMtUserId();
            findCardHoldStatusReqDTO.setUserId(mtUserId);
            findCardHoldStatusReqDTO
                    .setShopId(Optional.ofNullable(idMapperResult).map(ShopIdMapper::getMtBestShopId).orElse(0L));
        } else {
            long dpUserId = request.getDpUserId();
            findCardHoldStatusReqDTO.setUserId(dpUserId);
            findCardHoldStatusReqDTO
                    .setShopId(Optional.ofNullable(idMapperResult).map(ShopIdMapper::getDpBestShopId).orElse(0L));
        }
        return findCardHoldStatusReqDTO;
    }
}
