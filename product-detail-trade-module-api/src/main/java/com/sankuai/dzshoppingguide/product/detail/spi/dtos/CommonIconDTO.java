package com.sankuai.dzshoppingguide.product.detail.spi.dtos;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/2/28 15:14
 */
@Data
@Builder
@TypeDoc(description = "通用icon模型")
@MobileDo(id = 0xb8f4)
public class CommonIconDTO implements Serializable {
    @FieldDoc(description = "icon切图链接")
    @MobileDo.MobileField(key = 0xf39b)
    private String iconUrl;

    @FieldDoc(description = "高度")
    @MobileDo.MobileField(key = 0x261f)
    private int height;

    @FieldDoc(description = "宽度")
    @MobileDo.MobileField(key = 0x2b78)
    private int width;

}
