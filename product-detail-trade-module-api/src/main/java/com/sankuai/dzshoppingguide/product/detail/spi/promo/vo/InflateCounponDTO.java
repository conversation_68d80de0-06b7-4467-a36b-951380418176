package com.sankuai.dzshoppingguide.product.detail.spi.promo.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
@Data
@MobileDo(id = 0xfdfa)
public class InflateCounponDTO implements Serializable {

    private List<String> couponIds;


    /**
    * 券logo图片
    */
    @MobileDo.MobileField(key = 0xd010)
    private String logoIcon;

    /**
    * 券膨胀文案
    */
    @MobileDo.MobileField(key = 0xe399)
    private String couponDesc;

    /**
    * 券包模块信息
    */
    @MobileDo.MobileField(key = 0xda64)
    private String couponWalletInfo;

    /**
    * 券状态 字符串展示
    */
    @MobileDo.MobileField(key = 0x3998)
    private String couponStatus;

    /**
    * 最高可膨胀金额
    */
    @MobileDo.MobileField(key = 0xb622)
    private String maxInflateMoney;

    /**
    * 券包id列表
    */
    @MobileDo.MobileField(key = 0x55bf)
    private List<String> couponGroupIdList;

    /**
    * 已领神券列表
    */
    @MobileDo.MobileField(key = 0x22ed)
    private List<CouponItemInfoDTO> couponItems;

    /**
    * 展示标签类型 文档：https://km.sankuai.com/collabpage/2150258795
    */
    @MobileDo.MobileField(key = 0xfc19)
    private String showType;

    /**
    * 标签类型主题
    */
    @MobileDo.MobileField(key = 0xa23b)
    private String labelTheme;

    /**
    * 业务线（区分休闲娱乐，丽人等）
    */
    @MobileDo.MobileField(key = 0x8826)
    private String nibBiz;

    /**
     * 券组件版本实验结果
     */
    @MobileDo.MobileField(key = 0xfeb9)
    private List<Integer> mmcPkgResult;

}