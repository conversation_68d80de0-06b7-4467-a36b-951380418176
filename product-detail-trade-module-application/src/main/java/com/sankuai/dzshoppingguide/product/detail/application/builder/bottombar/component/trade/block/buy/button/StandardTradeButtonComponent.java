package com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.component.trade.block.buy.button;

import com.alibaba.fastjson.JSON;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.component.trade.block.utils.PriceFormatUtils;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.component.trade.block.utils.TradeButtonBuildUtils;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.exception.TradeButtonBuildFatalException;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.dto.ProductPriceReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.purchase.coupon.PurchaseCouponReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sale.info.enums.ProductSaleStatusEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.enums.OpenTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.extend.buy.BuyActionVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.component.trade.button.StandardTradeButtonVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.curator.shaded.com.google.common.collect.Lists;

import javax.annotation.Nullable;
import java.util.List;
import java.util.Optional;

import static com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.constant.ButtonStyleConstant.STANDARD_TRADE_BUTTON_STYLE;

/**
 * @Author: guangyujie
 * @Date: 2025/3/17 15:17
 */
@Slf4j
public class StandardTradeButtonComponent {

    public static StandardTradeButtonVO build(final ProductDetailPageRequest request,
                                              final ProductCategory productCategory,
                                              @Nullable final ProductPriceReturnValue normalPriceReturnValue,
                                              @Nullable final String orderPageUrl,
                                              @Nullable final PurchaseCouponReturnValue purchaseCouponReturnValue,
                                              final ProductSaleStatusEnum saleStatus) {
        try {
            PriceDisplayDTO normalPrice = Optional.ofNullable(normalPriceReturnValue)
                    .map(ProductPriceReturnValue::getPriceDisplayDTO)
                    .orElse(null);
            return TradeButtonBuildUtils.buildTradeButtonVO(
                    saleStatus,
                    PriceFormatUtils.formatPrice("团购", normalPrice),
                    buildSubTitle(purchaseCouponReturnValue),
                    STANDARD_TRADE_BUTTON_STYLE,
                    new BuyActionVO(
                            getOrderPageOverlayType(request, productCategory.getProductSecondCategoryId()),
                            orderPageUrl,
                            Optional.ofNullable(purchaseCouponReturnValue)
                                    .map(PurchaseCouponReturnValue::getCouponGroupIdsForPurchase)
                                    .orElse(null)
                    )
            );
        } catch (Throwable throwable) {
            log.error("StandardTradeButtonComponent.build,request:{}",
                    JSON.toJSONString(request), new TradeButtonBuildFatalException(throwable));
            return null;
        }
    }

    public static String buildSubTitle(final PurchaseCouponReturnValue purchaseCouponReturnValue) {
        if (purchaseCouponReturnValue != null) {
            if (purchaseCouponReturnValue.hasCouponForPurchase()) {
                return "领券购买";
            }
        }
        return "立即抢购";
    }

    public static OpenTypeEnum getOrderPageOverlayType(final ProductDetailPageRequest request,
                                                       final int productSecondCategoryId) {
        boolean isApp = request.getClientTypeEnum().isInApp();
        List<Integer> categoryIds = Lion.getList(Environment.getAppName(),
                "com.sankuai.dzu.tpbase.dztgdetailweb.pop.overlay.category",
                Integer.class, Lists.newArrayList(303, 304));
        if (categoryIds.contains(productSecondCategoryId) && isApp) {
            return OpenTypeEnum.modal;
        } else {
            return OpenTypeEnum.redirect;
        }
    }

}
