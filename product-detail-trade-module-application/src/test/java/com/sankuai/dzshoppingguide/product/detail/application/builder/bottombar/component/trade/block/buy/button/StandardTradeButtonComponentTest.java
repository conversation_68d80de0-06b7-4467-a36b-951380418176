package com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.component.trade.block.buy.button;

import com.sankuai.dzshoppingguide.product.detail.application.fetcher.purchase.coupon.PurchaseCouponReturnValue;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.when;

/**
 * Unit tests for StandardTradeButtonComponent.buildSubTitle method
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("StandardTradeButtonComponent buildSubTitle Tests")
public class StandardTradeButtonComponentTest {

    @Mock
    private PurchaseCouponReturnValue purchaseCouponReturnValue;

    /**
     * Test scenario: Input parameter is null
     * Expected: Should return default text "立即抢购"
     */
    @Test
    @DisplayName("Should return default text when input is null")
    public void testBuildSubTitle_WhenInputNull() {
        // arrange
        PurchaseCouponReturnValue nullValue = null;
        // act
        String result = StandardTradeButtonComponent.buildSubTitle(nullValue);
        // assert
        assertEquals("立即抢购", result);
    }

    /**
     * Test scenario: Government consume coupon promo is true
     * Expected: Should return "领券购买"
     */
    @Test
    @DisplayName("Should return coupon text when government consume coupon promo is true")
    public void testBuildSubTitle_WhenGovernmentConsumeCouponPromoTrue() {
        // arrange
        when(purchaseCouponReturnValue.hasCouponForPurchase()).thenReturn(true);
        // act
        String result = StandardTradeButtonComponent.buildSubTitle(purchaseCouponReturnValue);
        // assert
        assertEquals("领券购买", result);
    }

    /**
     * Test scenario: Both promo flags are false
     * Expected: Should return default text "立即抢购"
     */
    @Test
    @DisplayName("Should return default text when both promo flags are false")
    public void testBuildSubTitle_WhenBothPromoFlagsFalse() {
        // arrange
        when(purchaseCouponReturnValue.hasCouponForPurchase()).thenReturn(false);
        // act
        String result = StandardTradeButtonComponent.buildSubTitle(purchaseCouponReturnValue);
        // assert
        assertEquals("立即抢购", result);
    }

}
