package com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.extend.shopping.cart;

import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.BottomBarActionDataVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.enums.BottomBarActionTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * @Author: guangyujie
 * @Date: 2025/3/16 15:40
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AddShoppingCardActionVO extends BottomBarActionDataVO {

    private final int actionType = BottomBarActionTypeEnum.ADD_SHOPPING_CARD.getCode();

    private int productType;

    private long productId;

    private long skuId;

}
