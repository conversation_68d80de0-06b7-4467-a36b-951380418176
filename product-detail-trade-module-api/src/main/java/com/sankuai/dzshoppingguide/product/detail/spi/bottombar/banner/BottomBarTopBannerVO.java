package com.sankuai.dzshoppingguide.product.detail.spi.bottombar.banner;

import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.BottomBarActionDataVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.extend.none.DoNothingActionVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.banner.enums.BottomBarBannerTypeEnums;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.backgroud.BottomBarBackgroundVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.rich.content.RichContentVO;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: guangyujie
 * @Date: 2025/3/5 18:28
 */
@Data
public class BottomBarTopBannerVO implements Serializable {

    /**
     * 1=通用样式（目前只有1，以后也可能只有1）
     *
     * @see BottomBarBannerTypeEnums
     */
    private int bannerType = BottomBarBannerTypeEnums.COMMON_TYPE.getCode();

    /**
     * bannerData会根据bannerType改编模型，参考https://km.sankuai.com/collabpage/2704076169
     */
    private List<RichContentVO> bannerData = new ArrayList<>();

    /**
     * 背景色
     */
    private BottomBarBackgroundVO background;

    /**
     * banner点击事件
     */
    private BottomBarActionDataVO actionData = new DoNothingActionVO();

    public BottomBarTopBannerVO add(RichContentVO richContentVO) {
        if (richContentVO != null) {
            this.bannerData.add(richContentVO);
        }
        return this;
    }

}
