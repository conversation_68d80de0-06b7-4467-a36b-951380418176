package com.sankuai.dzshoppingguide.product.detail.application.fetcher.discountcard;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.BaseFetcherContext;
import com.sankuai.dz.product.detail.gateway.spi.enums.ProductTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzcard.navigation.api.dto.QueryCardInfoDTO;
import com.sankuai.dzcard.navigation.api.dto.QueryDiscountCardReq;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.concurrent.CompletableFuture;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

@RunWith(PowerMockRunner.class)
@PrepareForTest(DealDiscountCardFetcher.class)
public class DealDiscountCardFetcherTest {

    private CompositeAtomService compositeAtomService;

    private ShopIdMapper shopIdMapper;

    private DealDiscountCardFetcher dealDiscountCardFetcher;

    private ProductDetailPageRequest request;

    @BeforeEach
    void setUp() throws Exception {
        compositeAtomService = mock(CompositeAtomService.class);
        shopIdMapper = mock(ShopIdMapper.class);
        dealDiscountCardFetcher = PowerMockito.spy(new DealDiscountCardFetcher());
        Whitebox.setInternalState(dealDiscountCardFetcher, "compositeAtomService", compositeAtomService);
        request = new ProductDetailPageRequest();
        request.setProductId(12345L);
        request.setCityId(1);
        Whitebox.setInternalState(dealDiscountCardFetcher, "request", request);
    }

    /**
     * Test when product type is not DEAL should return null
     */
    @Test
    public void testDoFetchWhenProductTypeNotDeal() throws Throwable {
        // arrange
        request.setProductType(ProductTypeEnum.RESERVE.getCode());
        // act
        CompletableFuture<DealDiscountCardResult> result = dealDiscountCardFetcher.doFetch();
        // assert
        assertNull(result.get());
    }
}
