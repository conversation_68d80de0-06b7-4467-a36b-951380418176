package com.sankuai.dzshoppingguide.product.detail.application.utils;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.sankuai.dealuser.price.display.api.enums.PromoTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommendgoods.dtos.ProductM;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommendgoods.dtos.ProductPromoPriceM;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommendgoods.dtos.PromoItemM;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.timesdeal.dto.CardM;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.timesdeal.dto.MerchantMemberProductPromoData;
import com.sankuai.mpmctmember.process.common.enums.MemberDiscountTypeEnum;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public class MerchantMemberPromoUtils {

    private static final String MERCHANT_MEMBER_ATTR_KEY="MERCHANT_MEMBER_DEAL";

    private static final String ATTR_NAME = "MEMBER_EXCLUSIVE";

    private static final int FREE_TYPE = 1;

    private static final int PAY_TYPE = 2;



    /**
     * @param productM
     * @return
     */

    public static MerchantMemberProductPromoData getHasMerchantMemberPromo(ProductM productM) {
        MerchantMemberProductPromoData merchantMemberPromo =  getMerchantMemberProductPromoData(productM);
        //用户权益校验
        if (hasMerchantMembershipBenefits(productM)) {
            return merchantMemberPromo;
        }
        return null;
    }

    /**
     * 获取付费会员优惠信息
     * @param productM 商品信息
     * @return 付费后可享有的优惠主要用于用户会员价展示
     */
    public static MerchantMemberProductPromoData getPaidMerchantMemberPromo(ProductM productM) {
        MerchantMemberProductPromoData merchantMemberPromo =  getMerchantMemberProductPromoData(productM);
        //付费会员但当前还不是会员，付费后可享受的优惠
        MerchantMemberDealModel merchantMemberDealModel = getMerchantMemberDealModel(productM);
        if (Objects.nonNull(merchantMemberPromo) && Objects.nonNull(merchantMemberDealModel.getChargeType())
                && merchantMemberDealModel.chargeType == PAY_TYPE && !merchantMemberDealModel.getMerchantMember().isMember) {
            return merchantMemberPromo;
        }
        return null;
    }

    private static  MerchantMemberProductPromoData getMerchantMemberProductPromoData(ProductM productM) {
        // 无会员信息
        MerchantMemberDealModel merchantMemberDealModel = getMerchantMemberDealModel(productM);
        List<ProductPromoPriceM> promoPrices = productM.getPromoPrices();
        if (Objects.isNull(merchantMemberDealModel) || CollectionUtils.isEmpty(promoPrices)) {
            return null;
        }
        // 商家会员优惠
        ProductPromoPriceM productPromoPriceM = promoPrices.stream().filter(item -> hasMerchantMemberPromo(item))
                .findFirst().orElse(null);
        if (Objects.isNull(productPromoPriceM)) {
            return null;
        }
        MerchantMemberProductPromoData merchantMemberPromo = new MerchantMemberProductPromoData();
        merchantMemberPromo.setProductPromoPrice(productPromoPriceM);
        merchantMemberPromo.setMemberDiscountType(merchantMemberDealModel.getMemberDiscountType());
        return merchantMemberPromo;
    }


    /**
     * 获取会员价优惠
     * @param productM
     * @param productM
     * @return
     */
    public static MerchantMemberProductPromoData getMerchantMemberPromo(ProductM productM){
        MerchantMemberProductPromoData promoModel = new MerchantMemberProductPromoData();
        //1.获取不包含会员价以及卡的其他优惠
        List<ProductPromoPriceM> promoPrices = getPromoPrices(productM);
        if(CollectionUtils.isEmpty(promoPrices)){
            return promoModel;
        }
        //1.获取会员价商品，如果为null表示没有会员价，则展示其他的价格
        String merchantMemberDiscountStr = productM.getAttr(MERCHANT_MEMBER_ATTR_KEY);
        if(org.apache.commons.lang3.StringUtils.isEmpty(merchantMemberDiscountStr)){
            return promoModel;
        }
        //2.判断是否含有商家会员价优惠,不包含则直接返回
        Boolean isContainMemberPromoResult  = hasMerchantMemberPromoByPromoDetailList(promoPrices);
        if(!isContainMemberPromoResult){
            return promoModel;
        }
        //3.根据人的会员身份和商品的会员价/新会员价属性获取出会员价的优惠信息
        MerchantMemberDealModel merchantMemberDealModel = JsonCodec.decode(merchantMemberDiscountStr, MerchantMemberDealModel.class);
        //（1）会员商品则展示会员价 2.新会员商品->只要是新会员或者非会员
        Integer memberDiscountType =  merchantMemberDealModel.getMemberDiscountType();
        MerchantMemberModel merchantMemberModel = merchantMemberDealModel.getMerchantMember();
        Boolean isDisplayMemberPromo = memberDiscountType.equals(MemberDiscountTypeEnum.MEMBER_PROMO.getType())
                || (memberDiscountType.equals(MemberDiscountTypeEnum.NEW_MEMBER_PROMO.getType()) && (Objects.isNull(merchantMemberModel) || merchantMemberModel.isNewMember || !merchantMemberModel.getIsMember()));
        //判断其他的优惠价格是否低于会员价，不低于也不展示会员价
        Boolean isOtherPromoLowerThenMerchantMember = isOtherPromoLowerThenMerchantMember(promoPrices);
        if(isDisplayMemberPromo && !isOtherPromoLowerThenMerchantMember){
            ProductPromoPriceM productPromoPriceM = promoPrices.stream().filter(item-> hasMerchantMemberPromo(item)).findFirst().orElse(null);
            promoModel.setProductPromoPrice(productPromoPriceM);
            promoModel.setMemberDiscountType(memberDiscountType);
            return promoModel;
        }
        return promoModel;
    }

    /**
     * 只判断报价里面是否有商家会员优惠，未做商家会员类型及用户身份判断
     * @param productPromoPriceM
     * @return
     */
    private static boolean hasMerchantMemberPromo(ProductPromoPriceM productPromoPriceM) {
        if(Objects.isNull(productPromoPriceM) || CollectionUtils.isEmpty(productPromoPriceM.getPromoItemList())){
            return false;
        }
        List<Integer> promoTypeCodeList = productPromoPriceM.getPromoItemList().stream().filter(item->Objects.nonNull(item)).map(item->item.getPromoTypeCode()).collect(Collectors.toList());
        return CollectionUtils.isNotEmpty(promoTypeCodeList)
                && promoTypeCodeList.contains(PromoTypeEnum.MERCHANT_MEMBER.getType());
    }

    private static boolean hasMerchantMembershipBenefits(ProductM productM) {
        MerchantMemberDealModel merchantMemberDealModel = getMerchantMemberDealModel(productM);
        // 会员类型及用户身份判断
        if (Objects.isNull(merchantMemberDealModel) || Objects.isNull(merchantMemberDealModel.getMerchantMember())
                || Objects.isNull(merchantMemberDealModel.getChargeType())) {
            return false;
        }
        Integer memberDiscountType = merchantMemberDealModel.getMemberDiscountType();
        MerchantMemberModel merchantMemberModel = merchantMemberDealModel.getMerchantMember();
        // 免费入会
        if (merchantMemberDealModel.chargeType == FREE_TYPE) {
            // 进一步校验新会员专属
            return !memberDiscountType.equals(MemberDiscountTypeEnum.NEW_MEMBER_PROMO.getType())
                    || (merchantMemberDealModel.getMerchantMember().isNewMember || !merchantMemberModel.getIsMember());
        }
        // 付费入会，一定是会员，同时如果是新会员专属必须是新会员。
        return merchantMemberModel.getIsMember()
                && (!memberDiscountType.equals(MemberDiscountTypeEnum.NEW_MEMBER_PROMO.getType())
                        || merchantMemberDealModel.getMerchantMember().isNewMember);
    }

    /**
     * 去除玩乐卡，折扣卡，会员日，主要是400201包含了卡相关的信息
     * @param productM
     * @return
     */
    private static List<ProductPromoPriceM> getPromoPrices(ProductM productM){
        List<ProductPromoPriceM> allPromoPrices = productM.getPromoPrices();
        if(CollectionUtils.isEmpty(allPromoPrices)){
            return Lists.newArrayList();
        }
        List<ProductPromoPriceM> promoPriceMS = allPromoPrices.stream().filter(productPromoPriceM -> !isCardPromoType(productPromoPriceM.getPromoType())).collect(Collectors.toList());
        return promoPriceMS;
    }

    private static Boolean isCardPromoType(int promoType){
        if(promoType == PromoTypeEnum.DISCOUNT_CARD.getType() || promoType == PromoTypeEnum.JOY_CARD.getType() || promoType == PromoTypeEnum.MEMBER_DAY.getType()){
               return true;
        }
        return false;
    }

    private static Boolean isOtherPromoLowerThenMerchantMember(List<ProductPromoPriceM> promoPrices ){
        ProductPromoPriceM directPromo = promoPrices.stream().filter(p -> p.getPromoType() != PromoTypeEnum.MERCHANT_MEMBER.getType()).findFirst().orElse(null);
        ProductPromoPriceM merchantMemberPromo = promoPrices.stream().filter(p -> p.getPromoType() == PromoTypeEnum.MERCHANT_MEMBER.getType()).findFirst().orElse(null);
        if(null == directPromo || null == merchantMemberPromo ){
            return false;
        }
        //优惠后实际的售卖金额
        BigDecimal directPromoPrice = directPromo.getPromoPrice();
        BigDecimal merchantMemberPromoPrice = merchantMemberPromo.getPromoPrice();
        return directPromoPrice.compareTo(merchantMemberPromoPrice) < 0;

    }

    public static boolean hasMerchantMemberPromoByPromoDetailList(List<ProductPromoPriceM> promoPrices) {
        if (CollectionUtils.isEmpty(promoPrices)) {
            return false;
        }
        List<PromoItemM> promoItemMList = promoPrices.stream().filter(item->Objects.nonNull(item) && CollectionUtils.isNotEmpty(item.getPromoItemList()))
                .map(ProductPromoPriceM::getPromoItemList).flatMap(List::stream).collect(Collectors.toList());
        PromoItemM merchantMemberPromo = promoItemMList.stream().filter(promoItemM -> promoItemM.getPromoTypeCode() == PromoTypeEnum.MERCHANT_MEMBER.getType())
                .findFirst().orElse(null);
        return Objects.nonNull(merchantMemberPromo);
    }

    public static boolean isMemberExclusive(ProductM productM){
       return Objects.nonNull(productM.getAttr(ATTR_NAME)) && Boolean.parseBoolean(productM.getAttr(ATTR_NAME));
    }

    public static MerchantMemberDealModel getMerchantMemberDealModel(ProductM productM){
        String merchantMemberDiscountStr = productM.getAttr(MERCHANT_MEMBER_ATTR_KEY);
        if(StringUtils.isBlank(merchantMemberDiscountStr)){
            return null;
        }
        return JsonCodec.decode(merchantMemberDiscountStr, MerchantMemberDealModel.class);
    }

    /**
     * 判断是否使用了商家会员优惠
     */
    public static boolean usedMerchantMemberDiscount(ProductM productM, CardM cardM) {
        ProductPromoPriceM bestPromoPrice = PriceUtils.getUserHasPromoPrice(productM, cardM);
        MerchantMemberProductPromoData merchantMemberPromo = MerchantMemberPromoUtils.getHasMerchantMemberPromo(productM);

        if (bestPromoPrice == null || bestPromoPrice.getPromoPrice() == null ||
                merchantMemberPromo == null || merchantMemberPromo.getProductPromoPrice() == null ||
                merchantMemberPromo.getProductPromoPrice().getPromoPrice() == null) {
            return false;
        }

        return bestPromoPrice.getPromoPrice().equals(merchantMemberPromo.getProductPromoPrice().getPromoPrice());
    }

    /**
     * 判断是否使用了免费商家会员优惠
     */
    public static boolean usedFreeMerchantMemberDiscount(ProductM productM, CardM cardM) {
        if (!usedMerchantMemberDiscount(productM, cardM)) {
            return false;
        }

        MerchantMemberDealModel merchantMemberPromo = MerchantMemberPromoUtils.getMerchantMemberDealModel(productM);
        return isFreeTypeMemberPromo(merchantMemberPromo);
    }

    /**
     * 判断是否使用了付费商家会员优惠
     */
    public static boolean usedPaidMerchantMemberDiscount(ProductM productM, CardM cardM) {
        if (!usedMerchantMemberDiscount(productM, cardM)) {
            return false;
        }

        MerchantMemberDealModel merchantMemberPromo = MerchantMemberPromoUtils.getMerchantMemberDealModel(productM);
        return isPaidTypeMemberPromo(merchantMemberPromo);
    }

    private static boolean isFreeTypeMemberPromo(MerchantMemberDealModel merchantMemberPromo) {
        return merchantMemberPromo != null && merchantMemberPromo.getChargeType() != null
                && merchantMemberPromo.getChargeType().equals(MerchantMemberPromoUtils.FREE_TYPE);
    }

    private static boolean isPaidTypeMemberPromo(MerchantMemberDealModel merchantMemberPromo) {
        return merchantMemberPromo != null && merchantMemberPromo.getChargeType() != null
                && merchantMemberPromo.getChargeType().equals(PAY_TYPE);
    }

    @Data
    static public class MerchantMemberDealModel{
        /**
         * 会员优惠类型枚举,会员专属、会员价、新会员价,MemberDiscountTypeEnum
         */
        private Integer memberDiscountType;
        /**
         * 会员身份
         */
        private MerchantMemberModel merchantMember;
        /**
         * 会员类型，1-免费入会，2-付费入会
         */
        private Integer chargeType;
    }

    @Data
    static public class MerchantMemberModel{
        /**
         * 会员身份，否是会员
         */
        private Boolean isMember;

        /**
         * 会员身份,是否是新会员
         */
        private Boolean isNewMember;
    }
}
