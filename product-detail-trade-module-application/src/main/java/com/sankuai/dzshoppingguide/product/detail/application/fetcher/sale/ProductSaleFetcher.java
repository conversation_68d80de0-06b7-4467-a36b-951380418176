package com.sankuai.dzshoppingguide.product.detail.application.fetcher.sale;

import com.dianping.deal.sales.common.datatype.SalesDisplayQueryRequest;
import com.dianping.deal.sales.common.datatype.SalesOption;
import com.dianping.deal.sales.common.datatype.SalesSubjectParam;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.FetcherDAGStarter;
import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
import org.apache.commons.collections4.MapUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @date 2025-03-07
 * @desc 商品销量信息查询器
 */
@Fetcher(
        previousLayerDependencies = {FetcherDAGStarter.class}
)
public class ProductSaleFetcher extends NormalFetcherContext<ProductSaleReturnValue> {

    @Resource
    private CompositeAtomService compositeAtomService;

    @Override
    protected CompletableFuture<ProductSaleReturnValue> doFetch() {
        SalesDisplayQueryRequest saleRequest = buildSaleRequest(request);
        return compositeAtomService.querySaleDisplay(saleRequest).thenApply(result -> {
            if (MapUtils.isEmpty(result)) {
                return new ProductSaleReturnValue();
            }
            SalesSubjectParam key = buildSaleSubjectParam(request);
            return new ProductSaleReturnValue(result.get(key));
        });
    }

    private SalesDisplayQueryRequest buildSaleRequest(ProductDetailPageRequest pageRequest) {
        SalesDisplayQueryRequest saleRequest = new SalesDisplayQueryRequest();
        saleRequest.setSubjectParams(Collections.singletonList(buildSaleSubjectParam(pageRequest)));
        saleRequest.setOption(SalesOption.defaultOption());
        return saleRequest;
    }

    private SalesSubjectParam buildSaleSubjectParam(ProductDetailPageRequest pageRequest) {
        if (pageRequest.getClientTypeEnum().isMtClientType()) {
            return SalesSubjectParam.ptDealGroup(pageRequest.getProductId());
        }
        return SalesSubjectParam.bizDealGroup(pageRequest.getProductId());
    }
}
