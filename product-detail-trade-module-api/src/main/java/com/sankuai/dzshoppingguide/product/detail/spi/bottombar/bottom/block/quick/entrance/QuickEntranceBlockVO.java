package com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.block.quick.entrance;

import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.ProductBottomBarBlockVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.component.quick.entrance.QuickEntranceButtonVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.enums.BottomBarBlockTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: guangyujie
 * @Date: 2025/3/16 14:34
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class QuickEntranceBlockVO extends ProductBottomBarBlockVO {

    /**
     * 快捷入口按钮列表
     */
    private List<QuickEntranceButtonVO> quickEntrance;

    public QuickEntranceBlockVO(List<QuickEntranceButtonVO> quickEntrance) {
        this.quickEntrance = quickEntrance.stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    @Override
    public int getBlockType() {
        return BottomBarBlockTypeEnum.QUICK_ENTRANCE_BUTTON_LIST.getCode();
    }

}
