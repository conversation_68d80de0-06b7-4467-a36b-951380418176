package com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl;

import com.dianping.dztrade.enums.BizCodeEnum;
import com.dianping.dztrade.enums.TradeTypeEnum;
import com.dianping.lion.client.Lion;
import com.dianping.tuangu.dztg.usercenter.api.CreateOrderPageUrlService;
import com.dianping.tuangu.dztg.usercenter.api.dto.BatchGetCreateOrderPageUrlDto;
import com.dianping.tuangu.dztg.usercenter.api.dto.CreateOrderPageUrlDTO;
import com.dianping.tuangu.dztg.usercenter.api.dto.GetCreateOrderPageUrlEnvDto;
import com.dianping.tuangu.dztg.usercenter.api.dto.Response;
import com.dianping.tuangu.dztg.usercenter.api.enums.ChannelEnum;
import com.dianping.tuangu.dztg.usercenter.api.enums.CreateOrderPageSourceEnum;
import com.dianping.tuangu.dztg.usercenter.api.request.BatchGetCreateOrderPageUrlReq;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.athena.inf.AthenaInf;
import com.sankuai.athena.inf.rpc.RpcClient;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bookingtime.enums.MultiTradeTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.pricebar.builder.enums.CouponGuideTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.abtest.AbTestFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.abtest.AbTestReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.bookingtime.ShopBookingFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.category.ProductCategoryFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.dealgroup.DealGroupIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.dealgroup.DealGroupIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.coupon.magic.MagicCouponFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.coupon.magic.MagicCouponInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.degrade.OrderPageDegradeUtils;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.exception.OrderPageUrlException;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.ext.param.ExtParamBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.ext.param.request.ExtParamBuilderRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.best.shop.ShopInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.best.shop.ShopInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.ProductNormalPriceFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.ProductPromoPriceFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.purchase.coupon.PurchaseCouponFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.defaultselect.SkuDefaultSelect;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.defaultselect.SkuDefaultSelectFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.utils.JsonUtils;
import com.sankuai.dzshoppingguide.product.detail.application.utils.MultiTradeUtils;
import com.sankuai.dzshoppingguide.product.detail.domain.utils.LionConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Stream;

import static com.sankuai.dzshoppingguide.product.detail.domain.utils.LionConstants.TRADE_MODULE_APPKEY;

/**
 * @Author: guangyujie
 * @Date: 2025/3/20 16:49
 */
@Fetcher(
        previousLayerDependencies = {
                ProductCategoryFetcher.class,
                DealGroupIdMapperFetcher.class,
                SkuDefaultSelectFetcher.class,
                ShopInfoFetcher.class,
                PurchaseCouponFetcher.class,
                ProductNormalPriceFetcher.class,
                ProductPromoPriceFetcher.class,
                MagicCouponFetcher.class,
                AbTestFetcher.class,
                ShopBookingFetcher.class
        }
)
@Slf4j
public abstract class BaseOrderPageUrlFetcher extends NormalFetcherContext<OrderPageUrlResult> {

    @RpcClient(url = "http://service.dianping.com/tuangou/dztgUsercenterService/createOrderPageUrlService_1.0.0")
    private CreateOrderPageUrlService createOrderPageUrlServiceFuture;

    @Resource
    private OrderPageDegradeUtils orderPageDegradeUtils;

    @Resource
    private List<ExtParamBuilder> extParamBuilderList;

    @Override
    protected CompletableFuture<OrderPageUrlResult> doFetch() {
        if (!isContinue()) {
            return CompletableFuture.completedFuture(new OrderPageUrlResult(null));
        }
        try {
            final DealGroupIdMapper dealGroupIdMapper = getDependencyResult(DealGroupIdMapperFetcher.class);
            final SkuDefaultSelect skuDefaultSelect = getDependencyResult(SkuDefaultSelectFetcher.class);
            final ShopInfo shopInfo = getDependencyResult(ShopInfoFetcher.class);
            BatchGetCreateOrderPageUrlReq req = new BatchGetCreateOrderPageUrlReq();
            GetCreateOrderPageUrlEnvDto getCreateOrderPageUrlEnvDto = new GetCreateOrderPageUrlEnvDto();
            getCreateOrderPageUrlEnvDto.setPlatform(request.getClientTypeEnum().isMtClientType() ? 1 : 2);
            getCreateOrderPageUrlEnvDto.setChannel(getChannelEnum().getType());
            getCreateOrderPageUrlEnvDto.setPageSource(getPageSourceType());
            getCreateOrderPageUrlEnvDto.setVersion(request.getShepherdGatewayParam().getAppVersion());
            req.setEnvDto(getCreateOrderPageUrlEnvDto);
            BatchGetCreateOrderPageUrlDto batchGetCreateOrderPageUrlDto = new BatchGetCreateOrderPageUrlDto();
            req.setBatchGetCreateOrderPageUrlDtoList(Lists.newArrayList(batchGetCreateOrderPageUrlDto));
            batchGetCreateOrderPageUrlDto.setProductId(String.valueOf(
                    request.getClientTypeEnum().isMtClientType() ? dealGroupIdMapper.getMtDealGroupId() : dealGroupIdMapper.getDpDealGroupId()
            ));
            if (skuDefaultSelect.getSelectedSkuId() > 0) {
                //将请求中的skuId透传给提单页
                batchGetCreateOrderPageUrlDto.setSkuId(String.valueOf(skuDefaultSelect.getSelectedSkuId()));
            }
            if (request.getClientTypeEnum().isMtClientType()) {
                batchGetCreateOrderPageUrlDto.setShopId(String.valueOf(shopInfo.getMtPoiDTO().getMtPoiId()));
            } else {
                batchGetCreateOrderPageUrlDto.setShopId(String.valueOf(shopInfo.getDpPoiDTO().getShopId()));
                batchGetCreateOrderPageUrlDto.setShopUuid(shopInfo.getDpPoiDTO().getUuid());
            }
            batchGetCreateOrderPageUrlDto.setTradeType(getTradeType());
            if (batchGetCreateOrderPageUrlDto.getTradeType() == TradeTypeEnum.OneProductMultiStyles.getCode()) {
                    batchGetCreateOrderPageUrlDto.setBizType(getBizType());
            }
            final Map<String, String> extUrlParam = new HashMap<>();
            ExtParamBuilderRequest builderRequest = buildExtParamBuilderRequest();
            for (ExtParamBuilder extParamBuilder : extParamBuilderList) {
                Map<String, String> extParams = extParamBuilder.buildExtParam(request, builderRequest);
                if (MapUtils.isEmpty(extParams)) {
                    continue;
                }
                extUrlParam.putAll(extParams);
            }
            Map<String, String> customExtParams = buildCustomExtParams();
            if (MapUtils.isNotEmpty(customExtParams)) {
                extUrlParam.putAll(customExtParams);
            }
            batchGetCreateOrderPageUrlDto.setExtUrlParam(extUrlParam);
            // 除一品多态商品使用v2版接口外，其余均使用v1版接口
            if (batchGetCreateOrderPageUrlDto.getTradeType() != TradeTypeEnum.OneProductMultiStyles.getCode() || Lion.getBoolean(TRADE_MODULE_APPKEY, LionConstants.TRADE_URL_V2_INTERFACE_DEGRADE, false)) {
                return getV1Url(req, extUrlParam);
            } else {
                return getV2Url(req, extUrlParam);
            }
        } catch (Exception e) {
            log.warn("跳链兜底降级, request={}", JsonUtils.toJson(request), e);
            return CompletableFuture.completedFuture(new OrderPageUrlResult(OrderPageDegradeUtils.orderPageUrlDegrade(request)));
        }
    }

    private CompletableFuture<OrderPageUrlResult> getV1Url(BatchGetCreateOrderPageUrlReq req, Map<String, String> extUrlParam) {
        return AthenaInf.getRpcCompletableFuture(createOrderPageUrlServiceFuture.batchGetCreateOrderPageUrl(req))
                .thenApply(res -> {
                    if (res == null) {
                        throw new OrderPageUrlException("获取提单页跳链response=null");
                    }
                    if (!res.isSuccess()) {
                        throw new OrderPageUrlException(String.format("获取提单页跳链失败,code:%s,msg:%s", res.getCode(), res.getMsg()));
                    }
                    String url = Optional.of(res)
                            .map(Response::getContent)
                            .map(Map::values)
                            .map(Collection::stream)
                            .flatMap(Stream::findAny)
                            .orElse(null);
                    if (StringUtils.isNotBlank(url)) {
                        return new OrderPageUrlResult(url, extUrlParam);
                    } else {
                        //提单页跳链为空
                        throw new OrderPageUrlException("返回值中提单页url为空");
                    }
                }).exceptionally(throwable -> {
                    String url = orderPageDegradeUtils.orderPageUrlDegrade(req);
                    return new OrderPageUrlResult(url, extUrlParam);
                });
    }

    private CompletableFuture<OrderPageUrlResult> getV2Url(BatchGetCreateOrderPageUrlReq req, Map<String, String> extUrlParam) {
        return AthenaInf.getRpcCompletableFuture(createOrderPageUrlServiceFuture.batchGetCreateOrderPageUrlV2(req))
                .thenApply(res -> {
                    if (res == null) {
                        throw new OrderPageUrlException("获取提单页跳链response=null");
                    }
                    if (!res.isSuccess()) {
                        throw new OrderPageUrlException(String.format("获取提单页跳链失败,code:%s,msg:%s", res.getCode(), res.getMsg()));
                    }
                    CreateOrderPageUrlDTO urlDTO = Optional.of(res)
                            .map(Response::getContent)
                            .map(List::stream)
                            .flatMap(Stream::findFirst)
                            .orElse(null);
                    if (urlDTO != null && StringUtils.isNotBlank(urlDTO.getUrl())) {
                        return new OrderPageUrlResult(urlDTO.getUrl(), extUrlParam);
                    } else {
                        //提单页跳链为空
                        throw new OrderPageUrlException("返回值中提单页url为空");
                    }
                }).exceptionally(throwable -> {
                    String url = orderPageDegradeUtils.orderPageUrlDegrade(req);
                    return new OrderPageUrlResult(url, extUrlParam);
                });
    }


    protected abstract boolean isContinue();

    protected abstract Map<String, String> buildCustomExtParams();

    private ExtParamBuilderRequest buildExtParamBuilderRequest() {
        ExtParamBuilderRequest builderRequest = new ExtParamBuilderRequest();
        builderRequest.setPurchaseCoupon(getDependencyResult(PurchaseCouponFetcher.class));
        builderRequest.setNormalPrice(getDependencyResult(ProductNormalPriceFetcher.class));
        builderRequest.setPromoPrice(getDependencyResult(ProductPromoPriceFetcher.class));
        fulfillExtParamBuilderRequest(builderRequest);
        return builderRequest;
    }

    protected void fulfillExtParamBuilderRequest(final ExtParamBuilderRequest builderRequest) {

    }

    // 交易未提供新的枚举，故改为使用字符串返回
    protected String getPageSourceType() {
        if (request.getClientTypeEnum() == ClientTypeEnum.MT_LIVE_XCX) {
            return CreateOrderPageSourceEnum.MEIBO.getType();
        }
        ProductCategory productCategory = getDependencyResult(ProductCategoryFetcher.class);
        if ("mallFoodPoiShelf".equals(request.getPageSource()) && 712 == productCategory.getProductSecondCategoryId()) {
            return CreateOrderPageSourceEnum.UNITY_DEAL_GROUP_DETAIL.getType();
        }
        MagicCouponInfo magicCouponInfo = getDependencyResult(MagicCouponFetcher.class);
        if (Objects.nonNull(magicCouponInfo) && magicCouponInfo.getCouponGuideTypeEnum() == CouponGuideTypeEnum.GUIDE_PURCHASE) {
            return "dealGroupDetailMMCBuy";
        }
        if (Objects.nonNull(magicCouponInfo) && magicCouponInfo.getCouponGuideTypeEnum() == CouponGuideTypeEnum.GUIDE_TO_USE_INFLATE) {
            return "dealGroupDetailMMCUse";
        }
        return CreateOrderPageSourceEnum.DEAL_GROUP_DETAIL.getType();
    }

    private CreateOrderPageSourceEnum getPageSource() {
        if (request.getClientTypeEnum() == ClientTypeEnum.MT_LIVE_XCX) {
            return CreateOrderPageSourceEnum.MEIBO;
        }
        ProductCategory productCategory = getDependencyResult(ProductCategoryFetcher.class);
        if ("mallFoodPoiShelf".equals(request.getPageSource()) && 712 == productCategory.getProductSecondCategoryId()) {
            return CreateOrderPageSourceEnum.UNITY_DEAL_GROUP_DETAIL;
        }
        return CreateOrderPageSourceEnum.DEAL_GROUP_DETAIL;
    }

    private ChannelEnum getChannelEnum() {
        return CLIENT_TYPE_MAP.getOrDefault(this.request.getClientTypeEnum(), ChannelEnum.H5);
    }

    private int getTradeType() {
        ProductBaseInfo productBaseInfo = getDependencyResult(ProductBaseInfoFetcher.class);
        if (productBaseInfo == null || productBaseInfo.getBasic() == null) {
            return 0;
        }
        // 参考 https://km.sankuai.com/collabpage/2711196576
        return MultiTradeUtils.showMultiTradeFeature(productBaseInfo.getBasic(), getDependencyResult(ShopBookingFetcher.class), getDependencyResult(AbTestFetcher.class), request) ? TradeTypeEnum.OneProductMultiStyles.getCode() : 0;
    }


    protected int getBizType() {
        // 参考 https://km.sankuai.com/collabpage/2711196576 这里是底bar逻辑，时间选择传206
        AbTestReturnValue abTestReturnValue = getDependencyResult(AbTestFetcher.class);
        return MultiTradeUtils.getMultiTradeType(request, abTestReturnValue) == MultiTradeTypeEnum.DIRECT_BOOKING ? BizCodeEnum.unified_book.getUniBizType() : BizCodeEnum.groupbuy.getUniBizType();
    }

    public static final Map<ClientTypeEnum, ChannelEnum> CLIENT_TYPE_MAP = Maps.newHashMap();

    static {
        //APP
        CLIENT_TYPE_MAP.put(ClientTypeEnum.MT_APP, ChannelEnum.APP);
        CLIENT_TYPE_MAP.put(ClientTypeEnum.DP_APP, ChannelEnum.APP);
        //微信小程序
        CLIENT_TYPE_MAP.put(ClientTypeEnum.MT_XCX, ChannelEnum.WX_MINI_PROGRAM);
        CLIENT_TYPE_MAP.put(ClientTypeEnum.DP_XCX, ChannelEnum.WX_MINI_PROGRAM);
        //快手小程序
        CLIENT_TYPE_MAP.put(ClientTypeEnum.MT_KUAI_SHOU_XCX, ChannelEnum.KS_MINI_PROGRAM);
        //百度地图小程序
        CLIENT_TYPE_MAP.put(ClientTypeEnum.DP_BAIDU_MAP_XCX, ChannelEnum.BAIDU_MAP_MINI_PROGRAM);
        //美播小程序
        CLIENT_TYPE_MAP.put(ClientTypeEnum.MT_LIVE_XCX, ChannelEnum.SHANBO_MINI_PROGRAM);
    }

}
