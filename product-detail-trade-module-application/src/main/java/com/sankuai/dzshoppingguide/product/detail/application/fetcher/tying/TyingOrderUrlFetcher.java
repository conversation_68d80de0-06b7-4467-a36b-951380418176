package com.sankuai.dzshoppingguide.product.detail.application.fetcher.tying;

import com.dianping.tuangu.dztg.usercenter.api.dto.BatchGetCreateOrderPageUrlDto;
import com.dianping.tuangu.dztg.usercenter.api.dto.GetCreateOrderPageUrlEnvDto;
import com.dianping.tuangu.dztg.usercenter.api.enums.CreateOrderPageSourceEnum;
import com.dianping.tuangu.dztg.usercenter.api.request.BatchGetCreateOrderPageUrlReq;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
import com.sankuai.dzshoppingguide.product.detail.application.utils.ClientMappingUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.BindingSourceEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * 搭售填充提单跳链
 */
@Fetcher(previousLayerDependencies = {
        TyingInfoPaddingFetcher.class,
})
@Slf4j
public class TyingOrderUrlFetcher extends NormalFetcherContext<TyingPaddingResult> {

    @Autowired
    private CompositeAtomService compositeAtomService;

    private TyingPaddingResult tyingPaddingResult;

    public static final String JUMP_URL_SUFFIX = "&combinationid=%s&combinationproductid=%s&combinationskuid=%s";

    @Override
    protected CompletableFuture<TyingPaddingResult> doFetch() throws Exception {
        tyingPaddingResult = getDependencyResult(TyingInfoPaddingFetcher.class);
        if (tyingPaddingResult == null || CollectionUtils.isEmpty(tyingPaddingResult.getCombinationDealInfoList())) {
            return CompletableFuture.completedFuture(null);
        }
        return compositeAtomService.batchGetCreateOrderPageUrl(buildBatchGetCreateOrderPageUrlReq()).thenApply(this::toTyingPaddingResult);
    }

    private TyingPaddingResult toTyingPaddingResult(Map<String, String> orderUrlMap) {
        tyingPaddingResult.setDealOrderPageUrlMap(getDealOrderPageUrlMap(orderUrlMap, tyingPaddingResult.getBindingDealCombinationInfoMap()));
        return tyingPaddingResult;
    }

    private Map<Integer, String> getDealOrderPageUrlMap(Map<String, String> orderUrlMap, Map<Integer, CombinationDealInfo> bindingDealCombinationInfoMap) {
        if (MapUtils.isEmpty(orderUrlMap)) {
            return Maps.newHashMap();
        }
        Map<Integer, String> dealIdUrlMap = Maps.newHashMap();
        String jumpUrlPrefix = orderUrlMap.get(String.valueOf(tyingPaddingResult.getMainDealGroupId()));
        bindingDealCombinationInfoMap.forEach((bindingDealId, combinationDealInfo) -> {
            dealIdUrlMap.put(bindingDealId, jumpUrlPrefix + String.format(JUMP_URL_SUFFIX, getCombinationId(combinationDealInfo), combinationDealInfo.getBindingDealId(), combinationDealInfo.getTyingSaleSkuId()));
        });
        return dealIdUrlMap;
    }

    // 算法组品屏蔽组合id
    private String getCombinationId(CombinationDealInfo combinationDealInfo) {
        if (combinationDealInfo.getBindingSource() == BindingSourceEnum.ALGORITHM.getValue()) {
            return "0";
        }
        return combinationDealInfo.getItemId();
    }


    private BatchGetCreateOrderPageUrlReq buildBatchGetCreateOrderPageUrlReq() {
        BatchGetCreateOrderPageUrlReq req = new BatchGetCreateOrderPageUrlReq();
        GetCreateOrderPageUrlEnvDto getCreateOrderPageUrlEnvDto = new GetCreateOrderPageUrlEnvDto();
        getCreateOrderPageUrlEnvDto.setPlatform(request.getClientTypeEnum().isMtClientType() ? 1 : 2);
        getCreateOrderPageUrlEnvDto.setChannel(ClientMappingUtils.getOrderClientType(request).getType());
        getCreateOrderPageUrlEnvDto.setPageSource(CreateOrderPageSourceEnum.DEAL_GROUP_DETAIL.type);
        getCreateOrderPageUrlEnvDto.setVersion(request.getShepherdGatewayParam().getAppVersion());
        req.setEnvDto(getCreateOrderPageUrlEnvDto);

        List<BatchGetCreateOrderPageUrlDto> dtos = Lists.newArrayList();
        tyingPaddingResult.getCombinationDealInfoList().forEach(combinationDealInfo -> {
            BatchGetCreateOrderPageUrlDto batchGetCreateOrderPageUrlDto = new BatchGetCreateOrderPageUrlDto();
            if (Objects.nonNull(combinationDealInfo)) {
                batchGetCreateOrderPageUrlDto.setProductId(String.valueOf(combinationDealInfo.getMainDealId()));
            }
            batchGetCreateOrderPageUrlDto.setShopId(String.valueOf(request.getPoiId()));
            Map<String, String> extUrlParamMap = batchGetCreateOrderPageUrlDto.getExtUrlParam();
            if (MapUtils.isEmpty(extUrlParamMap)) {
                extUrlParamMap = Maps.newHashMap();
                batchGetCreateOrderPageUrlDto.setExtUrlParam(extUrlParamMap);
            }
            if (StringUtils.isNotBlank(tyingPaddingResult.getPriceSecretInfo())) {
                extUrlParamMap.put("pricecipher", tyingPaddingResult.getPriceSecretInfo());
            }
            dtos.add(batchGetCreateOrderPageUrlDto);
        });
        req.setBatchGetCreateOrderPageUrlDtoList(dtos);
        return req;
    }

}
