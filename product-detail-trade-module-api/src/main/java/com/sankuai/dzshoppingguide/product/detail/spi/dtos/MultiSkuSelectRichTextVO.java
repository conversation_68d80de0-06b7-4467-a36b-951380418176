package com.sankuai.dzshoppingguide.product.detail.spi.dtos;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: zheng<PERSON><EMAIL>
 * @Date: 2025/2/25
 */
@Data
@TypeDoc(description = "多sku富文本信息")
@MobileDo(id = 0x41fe)
public class MultiSkuSelectRichTextVO implements Serializable {
    @FieldDoc(description = "标识")
    @MobileDo.MobileField(key = 0xbe81)
    private String symbol;

    @FieldDoc(description = "到手价")
    @MobileDo.MobileField(key = 0xec65)
    private String salePrice;

    @FieldDoc(description = "划线价")
    @MobileDo.MobileField(key = 0x6242)
    private String marketPrice;
}
