package com.sankuai.dzshoppingguide.product.detail.application.fetcher.reserve.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class PeriodPriceM implements Serializable {

    /**
     * 时段开始时间
     */
    private long periodStartTime;

    /**
     * 时段结束时间
     */
    private long periodEndTime;

    /**
     * 当前时段是否可订（是否有库存）
     */
    private boolean canBook;

    /**
     * 时段最早可订时间
     */
    private Long earliestBookTime;

    /**
     * 结束时间是否跨天
     */
    private Boolean acrossDay;

    /**
     * 时段门市价
     */
    private String periodMarketPrice;

    /**
     * 时段基准价
     */
    private String periodOriginSalePrice;

    /**
     * 时段最低到手价
     */
    private String periodSalePrice;

    /**
     * 时段最低到手价描述（"起"等）
     */
    private String periodSalePriceDesc;

    private PeriodPromoPriceM periodPromoPrices;

    private PeriodPromoPriceM periodFuturePromoPrices;

    public PeriodPriceM(long periodStartTime, long periodEndTime) {
        this.periodStartTime = periodStartTime;
        this.periodEndTime = periodEndTime;
    }

    public PeriodPriceM() {

    }
}
