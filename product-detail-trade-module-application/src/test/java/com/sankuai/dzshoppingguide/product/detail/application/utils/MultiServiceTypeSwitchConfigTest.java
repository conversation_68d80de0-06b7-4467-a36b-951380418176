package com.sankuai.dzshoppingguide.product.detail.application.utils;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import java.util.ArrayList;
import java.util.Arrays;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * Unit tests for {@link MultiServiceTypeSwitchConfig}
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("MultiServiceTypeSwitchConfig Tests")
public class MultiServiceTypeSwitchConfigTest {

    /**
     * Test scenario: When allServiceType is true, should return true regardless of serviceType
     */
    @Test
    @DisplayName("Should return true when allServiceType is true")
    public void testIsMultiSku_WhenAllServiceTypeIsTrue() throws Throwable {
        // arrange
        MultiServiceTypeSwitchConfig config = new MultiServiceTypeSwitchConfig();
        config.setAllServiceType(true);
        config.setServiceTypes(null);
        // act
        boolean result = config.isMultiSku("anyServiceType");
        // assert
        assertTrue(result, "Should return true when allServiceType is true regardless of serviceType");
    }

    /**
     * Test scenario: When allServiceType is false and serviceTypes is null
     */
    @Test
    @DisplayName("Should return false when serviceTypes is null")
    public void testIsMultiSku_WhenServiceTypesIsNull() throws Throwable {
        // arrange
        MultiServiceTypeSwitchConfig config = new MultiServiceTypeSwitchConfig();
        config.setAllServiceType(false);
        config.setServiceTypes(null);
        // act
        boolean result = config.isMultiSku("anyServiceType");
        // assert
        assertFalse(result, "Should return false when serviceTypes is null");
    }

    /**
     * Test scenario: When allServiceType is false and serviceTypes is empty
     */
    @Test
    @DisplayName("Should return false when serviceTypes is empty")
    public void testIsMultiSku_WhenServiceTypesIsEmpty() throws Throwable {
        // arrange
        MultiServiceTypeSwitchConfig config = new MultiServiceTypeSwitchConfig();
        config.setAllServiceType(false);
        config.setServiceTypes(new ArrayList<>());
        // act
        boolean result = config.isMultiSku("anyServiceType");
        // assert
        assertFalse(result, "Should return false when serviceTypes is empty");
    }

    /**
     * Test scenario: When allServiceType is false and serviceTypes contains the input serviceType
     */
    @Test
    @DisplayName("Should return true when serviceType exists in serviceTypes")
    public void testIsMultiSku_WhenServiceTypeExists() throws Throwable {
        // arrange
        MultiServiceTypeSwitchConfig config = new MultiServiceTypeSwitchConfig();
        config.setAllServiceType(false);
        config.setServiceTypes(Arrays.asList("type1", "type2", "type3"));
        String targetServiceType = "type2";
        // act
        boolean result = config.isMultiSku(targetServiceType);
        // assert
        assertTrue(result, "Should return true when serviceType exists in serviceTypes list");
    }

    /**
     * Test scenario: When allServiceType is false and serviceTypes doesn't contain the input serviceType
     */
    @Test
    @DisplayName("Should return false when serviceType doesn't exist in serviceTypes")
    public void testIsMultiSku_WhenServiceTypeNotExists() throws Throwable {
        // arrange
        MultiServiceTypeSwitchConfig config = new MultiServiceTypeSwitchConfig();
        config.setAllServiceType(false);
        config.setServiceTypes(Arrays.asList("type1", "type2", "type3"));
        String nonExistentServiceType = "type4";
        // act
        boolean result = config.isMultiSku(nonExistentServiceType);
        // assert
        assertFalse(result, "Should return false when serviceType is not in serviceTypes list");
    }

    /**
     * Test scenario: When serviceType parameter is null
     */
    @Test
    @DisplayName("Should handle null serviceType parameter")
    public void testIsMultiSku_WhenServiceTypeIsNull() throws Throwable {
        // arrange
        MultiServiceTypeSwitchConfig config = new MultiServiceTypeSwitchConfig();
        config.setAllServiceType(false);
        config.setServiceTypes(Arrays.asList("type1", "type2", "type3"));
        // act
        boolean result = config.isMultiSku(null);
        // assert
        assertFalse(result, "Should return false when serviceType parameter is null");
    }
}
