package com.sankuai.dzshoppingguide.product.detail.application.fetcher.tying;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSONObject;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.BindingSourceEnum;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;
import com.dianping.martgeneral.recommend.api.dto.RecommendDTO;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

@ExtendWith(MockitoExtension.class)
class BlackListFilterFetcherTest {

    private final BlackListFilterFetcher fetcher = new BlackListFilterFetcher();

    /**
     * Test null input returns empty list
     */
    @Test
    public void testConvertToCombinationDealInfoNullInput() throws Throwable {
        // act
        List<CombinationDealInfo> result = fetcher.convertToCombinationDealInfo((String) null);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test empty string input returns empty list
     */
    @Test
    public void testConvertToCombinationDealInfoEmptyString() throws Throwable {
        // arrange
        String emptyJson = "";
        // act
        List<CombinationDealInfo> result = fetcher.convertToCombinationDealInfo(emptyJson);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test invalid JSON format returns empty list
     */
    @Test
    public void testConvertToCombinationDealInfoInvalidJsonFormat() throws Throwable {
        // arrange
        String invalidJson = "{invalid:json}";
        // act
        List<CombinationDealInfo> result = fetcher.convertToCombinationDealInfo(invalidJson);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test JSON missing sortedResult field returns empty list
     */
    @Test
    public void testConvertToCombinationDealInfoMissingSortedResult() throws Throwable {
        // arrange
        String json = "{\"otherField\":\"value\"}";
        // act
        List<CombinationDealInfo> result = fetcher.convertToCombinationDealInfo(json);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test valid JSON with invalid entries (is_valid!=1) returns empty list
     */
    @Test
    public void testConvertToCombinationDealInfoInvalidIsValid() throws Throwable {
        // arrange
        String json = "{\"sortedResult\":[{\"bizData\":{\"is_valid\":\"0\",\"isSameShop\":\"1\"}}]}";
        // act
        List<CombinationDealInfo> result = fetcher.convertToCombinationDealInfo(json);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test valid JSON with invalid entries (isSameShop!=1) returns empty list
     */
    @Test
    public void testConvertToCombinationDealInfoInvalidIsSameShop() throws Throwable {
        // arrange
        String json = "{\"sortedResult\":[{\"bizData\":{\"is_valid\":\"1\",\"isSameShop\":\"0\"}}]}";
        // act
        List<CombinationDealInfo> result = fetcher.convertToCombinationDealInfo(json);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test valid JSON with one valid entry returns correct CombinationDealInfo
     */
    @Test
    public void testConvertToCombinationDealInfoValidEntry() throws Throwable {
        // arrange
        String json = "{\"sortedResult\":[{\"bizData\":{\"is_valid\":\"1\",\"isSameShop\":\"1\"," + "\"combinationId\":\"123\",\"productId_a\":\"100\",\"productId_b\":\"200\"}}]}";
        // act
        List<CombinationDealInfo> result = fetcher.convertToCombinationDealInfo(json);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        CombinationDealInfo info = result.get(0);
        assertEquals("123", info.getItemId());
        assertEquals(100, info.getMainDealId());
        assertEquals(200, info.getBindingDealId());
        assertEquals(BindingSourceEnum.MANUAL.getValue(), info.getBindingSource());
    }

    /**
     * Test valid JSON with multiple entries returns only valid ones
     */
    @Test
    public void testConvertToCombinationDealInfoMultipleEntries() throws Throwable {
        // arrange
        String json = "{\"sortedResult\":[" + "{\"bizData\":{\"is_valid\":\"1\",\"isSameShop\":\"1\",\"combinationId\":\"1\",\"productId_a\":\"101\",\"productId_b\":\"201\"}}," + "{\"bizData\":{\"is_valid\":\"0\",\"isSameShop\":\"1\",\"combinationId\":\"2\",\"productId_a\":\"102\",\"productId_b\":\"202\"}}," + "{\"bizData\":{\"is_valid\":\"1\",\"isSameShop\":\"0\",\"combinationId\":\"3\",\"productId_a\":\"103\",\"productId_b\":\"203\"}}," + "{\"bizData\":{\"is_valid\":\"1\",\"isSameShop\":\"1\",\"combinationId\":\"4\",\"productId_a\":\"104\",\"productId_b\":\"204\"}}" + "]}";
        // act
        List<CombinationDealInfo> result = fetcher.convertToCombinationDealInfo(json);
        // assert
        assertNotNull(result);
        assertEquals(2, result.size());
        CombinationDealInfo info1 = result.get(0);
        assertEquals("1", info1.getItemId());
        assertEquals(101, info1.getMainDealId());
        assertEquals(201, info1.getBindingDealId());
        CombinationDealInfo info2 = result.get(1);
        assertEquals("4", info2.getItemId());
        assertEquals(104, info2.getMainDealId());
        assertEquals(204, info2.getBindingDealId());
    }

    @Test
    void testConvertToCombinationDealInfo_NullInput_ReturnsEmptyList() throws Throwable {
        // arrange
        BlackListFilterFetcher fetcher = new BlackListFilterFetcher();
        // act
        List<CombinationDealInfo> result = fetcher.convertToCombinationDealInfo((List<RecommendDTO>) null);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testConvertToCombinationDealInfo_EmptyList_ReturnsEmptyList() throws Throwable {
        // arrange
        BlackListFilterFetcher fetcher = new BlackListFilterFetcher();
        // act
        List<CombinationDealInfo> result = fetcher.convertToCombinationDealInfo(Collections.emptyList());
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    void testConvertToCombinationDealInfo_NullBizData_SkipsItem() throws Throwable {
        // arrange
        BlackListFilterFetcher fetcher = new BlackListFilterFetcher();
        RecommendDTO mockDTO = mock(RecommendDTO.class);
        when(mockDTO.getBizData()).thenReturn(null);
        List<RecommendDTO> input = Collections.singletonList(mockDTO);
        // act
        List<CombinationDealInfo> result = fetcher.convertToCombinationDealInfo(input);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(mockDTO).getBizData();
    }

    @Test
    void testConvertToCombinationDealInfo_MissingRequiredKeys_SkipsItem() throws Throwable {
        // arrange
        BlackListFilterFetcher fetcher = new BlackListFilterFetcher();
        RecommendDTO mockDTO = mock(RecommendDTO.class);
        Map<String, Object> bizData = new HashMap<>();
        bizData.put("other_key", "value");
        when(mockDTO.getBizData()).thenReturn(bizData);
        List<RecommendDTO> input = Collections.singletonList(mockDTO);
        // act
        List<CombinationDealInfo> result = fetcher.convertToCombinationDealInfo(input);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(mockDTO).getBizData();
    }

    @Test
    void testConvertToCombinationDealInfo_InvalidFlag_SkipsItem() throws Throwable {
        // arrange
        BlackListFilterFetcher fetcher = new BlackListFilterFetcher();
        RecommendDTO mockDTO = mock(RecommendDTO.class);
        Map<String, Object> bizData = new HashMap<>();
        bizData.put(BlackListFilterFetcher.MAIN_DEAL_KEY, "123");
        bizData.put(BlackListFilterFetcher.BINDING_DEAL_KEY, "456");
        bizData.put("is_valid", "0");
        when(mockDTO.getBizData()).thenReturn(bizData);
        List<RecommendDTO> input = Collections.singletonList(mockDTO);
        // act
        List<CombinationDealInfo> result = fetcher.convertToCombinationDealInfo(input);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(mockDTO).getBizData();
    }

    @Test
    void testConvertToCombinationDealInfo_ValidData_ReturnsCorrectInfo() throws Throwable {
        // arrange
        BlackListFilterFetcher fetcher = new BlackListFilterFetcher();
        RecommendDTO mockDTO = mock(RecommendDTO.class);
        Map<String, Object> bizData = new HashMap<>();
        bizData.put(BlackListFilterFetcher.MAIN_DEAL_KEY, "123");
        bizData.put(BlackListFilterFetcher.BINDING_DEAL_KEY, "456");
        bizData.put("is_valid", "1");
        when(mockDTO.getItem()).thenReturn("test_item");
        when(mockDTO.getBizData()).thenReturn(bizData);
        List<RecommendDTO> input = Collections.singletonList(mockDTO);
        // act
        List<CombinationDealInfo> result = fetcher.convertToCombinationDealInfo(input);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        CombinationDealInfo info = result.get(0);
        assertEquals("test_item", info.getItemId());
        assertEquals(123, info.getMainDealId());
        assertEquals(456, info.getBindingDealId());
        assertEquals(BindingSourceEnum.ALGORITHM.getValue(), info.getBindingSource());
        verify(mockDTO).getItem();
        verify(mockDTO).getBizData();
    }

    @Test
    void testConvertToCombinationDealInfo_MixedData_ReturnsOnlyValidItems() throws Throwable {
        // arrange
        BlackListFilterFetcher fetcher = new BlackListFilterFetcher();
        // Valid DTO
        RecommendDTO validDTO = mock(RecommendDTO.class);
        Map<String, Object> validBizData = new HashMap<>();
        validBizData.put(BlackListFilterFetcher.MAIN_DEAL_KEY, "111");
        validBizData.put(BlackListFilterFetcher.BINDING_DEAL_KEY, "222");
        validBizData.put("is_valid", "1");
        when(validDTO.getItem()).thenReturn("valid_item");
        when(validDTO.getBizData()).thenReturn(validBizData);
        // Invalid DTO (missing keys)
        RecommendDTO invalidDTO1 = mock(RecommendDTO.class);
        Map<String, Object> invalidBizData1 = new HashMap<>();
        invalidBizData1.put("other_key", "value");
        when(invalidDTO1.getBizData()).thenReturn(invalidBizData1);
        // Invalid DTO (invalid flag)
        RecommendDTO invalidDTO2 = mock(RecommendDTO.class);
        Map<String, Object> invalidBizData2 = new HashMap<>();
        invalidBizData2.put(BlackListFilterFetcher.MAIN_DEAL_KEY, "333");
        invalidBizData2.put(BlackListFilterFetcher.BINDING_DEAL_KEY, "444");
        invalidBizData2.put("is_valid", "0");
        when(invalidDTO2.getBizData()).thenReturn(invalidBizData2);
        List<RecommendDTO> input = Arrays.asList(validDTO, invalidDTO1, invalidDTO2);
        // act
        List<CombinationDealInfo> result = fetcher.convertToCombinationDealInfo(input);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        CombinationDealInfo info = result.get(0);
        assertEquals("valid_item", info.getItemId());
        assertEquals(111, info.getMainDealId());
        assertEquals(222, info.getBindingDealId());
        verify(validDTO).getItem();
        verify(validDTO).getBizData();
        verify(invalidDTO1).getBizData();
        verify(invalidDTO2).getBizData();
    }
}
