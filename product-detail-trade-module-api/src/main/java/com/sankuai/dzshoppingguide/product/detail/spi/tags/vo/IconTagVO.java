package com.sankuai.dzshoppingguide.product.detail.spi.tags.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

@Data
@TypeDoc(description = "携带icon的标签模型")
@MobileDo(id = 0x154)
public class IconTagVO extends BaseTagDTO{
    /**
     * tag后图标icon
     */
    @MobileDo.MobileField(key = 0x1256)
    private String suffixIconUrl;
}
