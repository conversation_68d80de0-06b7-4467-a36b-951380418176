package com.sankuai.dzshoppingguide.product.detail.application.fetcher.producttheme;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommendgoods.dtos.ProductM;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2025/5/22 09:54
 */
@Data
public class ProductThemeResult  extends FetcherReturnValueDTO {

    /**
     * 推荐的商品id
     */
    private List<ProductM> products;

    /**
     * 商品总数
     */
    private int total;

    /**
     * 是否还有商品
     */
    private boolean hasNext;
}
