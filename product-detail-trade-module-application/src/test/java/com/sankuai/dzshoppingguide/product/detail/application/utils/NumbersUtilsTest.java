package com.sankuai.dzshoppingguide.product.detail.application.utils;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
@DisplayName("NumbersUtils GreaterThanZero Integer Tests")
class NumbersUtilsTest {

    /**
     * Test lessThanAndEqualZero method with null input
     */
    @Test
    public void testLessThanAndEqualZeroWithNull() {
        // arrange
        Long input = null;
        // act
        boolean result = NumbersUtils.lessThanAndEqualZero(input);
        // assert
        assertTrue(result, "Null input should return true");
    }

    /**
     * Test lessThanAndEqualZero method with zero input
     */
    @Test
    public void testLessThanAndEqualZeroWithZero() {
        // arrange
        Long input = 0L;
        // act
        boolean result = NumbersUtils.lessThanAndEqualZero(input);
        // assert
        assertTrue(result, "Zero input should return true");
    }

    /**
     * Test lessThanAndEqualZero method with negative input
     */
    @Test
    public void testLessThanAndEqualZeroWithNegativeNumber() {
        // arrange
        Long input = -1L;
        // act
        boolean result = NumbersUtils.lessThanAndEqualZero(input);
        // assert
        assertTrue(result, "Negative input should return true");
    }

    /**
     * Test lessThanAndEqualZero method with positive input
     */
    @Test
    public void testLessThanAndEqualZeroWithPositiveNumber() {
        // arrange
        Long input = 1L;
        // act
        boolean result = NumbersUtils.lessThanAndEqualZero(input);
        // assert
        assertFalse(result, "Positive input should return false");
    }

    /**
     * Test lessThanAndEqualZero method with maximum Long value
     */
    @Test
    public void testLessThanAndEqualZeroWithMaxLongValue() {
        // arrange
        Long input = Long.MAX_VALUE;
        // act
        boolean result = NumbersUtils.lessThanAndEqualZero(input);
        // assert
        assertFalse(result, "Maximum Long value should return false");
    }

    /**
     * Test lessThanAndEqualZero method with minimum Long value
     */
    @Test
    public void testLessThanAndEqualZeroWithMinLongValue() {
        // arrange
        Long input = Long.MIN_VALUE;
        // act
        boolean result = NumbersUtils.lessThanAndEqualZero(input);
        // assert
        assertTrue(result, "Minimum Long value should return true");
    }

    /**
     * Test toLong method with null input
     */
    @Test
    public void testToLongWithNullInput() {
        // arrange
        Long input = null;
        // act
        Long result = NumbersUtils.toLong(input);
        // assert
        assertEquals(0L, result, "Should return 0L for null input");
    }

    /**
     * Test toLong method with positive Long value
     */
    @Test
    public void testToLongWithPositiveValue() {
        // arrange
        Long input = 100L;
        // act
        Long result = NumbersUtils.toLong(input);
        // assert
        assertEquals(input, result, "Should return the same positive value");
    }

    /**
     * Test toLong method with negative Long value
     */
    @Test
    public void testToLongWithNegativeValue() {
        // arrange
        Long input = -100L;
        // act
        Long result = NumbersUtils.toLong(input);
        // assert
        assertEquals(input, result, "Should return the same negative value");
    }

    /**
     * Test toLong method with zero Long value
     */
    @Test
    public void testToLongWithZeroValue() {
        // arrange
        Long input = 0L;
        // act
        Long result = NumbersUtils.toLong(input);
        // assert
        assertEquals(input, result, "Should return zero");
    }

    /**
     * Test toLong method with maximum Long value
     */
    @Test
    public void testToLongWithMaxValue() {
        // arrange
        Long input = Long.MAX_VALUE;
        // act
        Long result = NumbersUtils.toLong(input);
        // assert
        assertEquals(input, result, "Should return the maximum Long value");
    }

    /**
     * Test toLong method with minimum Long value
     */
    @Test
    public void testToLongWithMinValue() {
        // arrange
        Long input = Long.MIN_VALUE;
        // act
        Long result = NumbersUtils.toLong(input);
        // assert
        assertEquals(input, result, "Should return the minimum Long value");
    }

    /**
     * Test lessThanAndEqualZero method with null input
     */
    @Test
    public void testLessThanAndEqualZeroWithNullInput() {
        // arrange
        Integer num = null;
        // act
        boolean result = NumbersUtils.lessThanAndEqualZero(num);
        // assert
        assertTrue(result, "Null input should return true");
    }

    /**
     * Test lessThanAndEqualZero method with zero input
     */
    @Test
    public void testLessThanAndEqualZeroWithZeroInput() {
        // arrange
        Integer num = 0;
        // act
        boolean result = NumbersUtils.lessThanAndEqualZero(num);
        // assert
        assertTrue(result, "Zero input should return true");
    }

    /**
     * Test lessThanAndEqualZero method with positive input
     */
    @Test
    public void testLessThanAndEqualZeroWithPositiveInput() {
        // arrange
        Integer num = 1;
        // act
        boolean result = NumbersUtils.lessThanAndEqualZero(num);
        // assert
        assertFalse(result, "Positive input should return false");
    }

    /**
     * Test lessThanAndEqualZero method with negative input
     */
    @Test
    public void testLessThanAndEqualZeroWithNegativeInput() {
        // arrange
        Integer num = -1;
        // act
        boolean result = NumbersUtils.lessThanAndEqualZero(num);
        // assert
        assertTrue(result, "Negative input should return true");
    }

    /**
     * Test lessThanAndEqualZero method with maximum positive Integer value
     */
    @Test
    public void testLessThanAndEqualZeroWithMaxPositiveInteger() {
        // arrange
        Integer num = Integer.MAX_VALUE;
        // act
        boolean result = NumbersUtils.lessThanAndEqualZero(num);
        // assert
        assertFalse(result, "Maximum positive Integer value should return false");
    }

    /**
     * Test lessThanAndEqualZero method with minimum negative Integer value
     */
    @Test
    public void testLessThanAndEqualZeroWithMinNegativeInteger() {
        // arrange
        Integer num = Integer.MIN_VALUE;
        // act
        boolean result = NumbersUtils.lessThanAndEqualZero(num);
        // assert
        assertTrue(result, "Minimum negative Integer value should return true");
    }

    /**
     * Test scenario: Input is null
     * Expected: Returns false when input is null
     */
    @Test
    @DisplayName("Should return false when input is null")
    public void testGreaterThanZero_WhenInputNull() {
        // arrange
        Integer input = null;
        // act
        boolean result = NumbersUtils.greaterThanZero(input);
        // assert
        assertFalse(result, "Should return false for null input");
    }

    /**
     * Test scenario: Input is a positive number
     * Expected: Returns true for positive number
     */
    @Test
    @DisplayName("Should return true when input is positive")
    public void testGreaterThanZero_WhenInputPositive() {
        // arrange
        Integer input = 1;
        // act
        boolean result = NumbersUtils.greaterThanZero(input);
        // assert
        assertTrue(result, "Should return true for positive number");
    }

    /**
     * Test scenario: Input is zero
     * Expected: Returns false for zero
     */
    @Test
    @DisplayName("Should return false when input is zero")
    public void testGreaterThanZero_WhenInputZero() {
        // arrange
        Integer input = 0;
        // act
        boolean result = NumbersUtils.greaterThanZero(input);
        // assert
        assertFalse(result, "Should return false for zero");
    }

    /**
     * Test scenario: Input is a negative number
     * Expected: Returns false for negative number
     */
    @Test
    @DisplayName("Should return false when input is negative")
    public void testGreaterThanZero_WhenInputNegative() {
        // arrange
        Integer input = -1;
        // act
        boolean result = NumbersUtils.greaterThanZero(input);
        // assert
        assertFalse(result, "Should return false for negative number");
    }

    /**
     * Test scenario: Input is Integer.MAX_VALUE
     * Expected: Returns true for maximum integer value
     */
    @Test
    @DisplayName("Should return true when input is Integer.MAX_VALUE")
    public void testGreaterThanZero_WhenInputMaxValue() {
        // arrange
        Integer input = Integer.MAX_VALUE;
        // act
        boolean result = NumbersUtils.greaterThanZero(input);
        // assert
        assertTrue(result, "Should return true for Integer.MAX_VALUE");
    }

    /**
     * Test scenario: Input is Integer.MIN_VALUE
     * Expected: Returns false for minimum integer value
     */
    @Test
    @DisplayName("Should return false when input is Integer.MIN_VALUE")
    public void testGreaterThanZero_WhenInputMinValue() {
        // arrange
        Integer input = Integer.MIN_VALUE;
        // act
        boolean result = NumbersUtils.greaterThanZero(input);
        // assert
        assertFalse(result, "Should return false for Integer.MIN_VALUE");
    }

    /**
     * Test scenario: Input is null
     * Expected: Should return false
     */
    @Test
    @DisplayName("When input is null, should return false")
    public void testGreaterThanZero_WhenNull() throws Throwable {
        // arrange
        Long input = null;
        // act
        boolean result = NumbersUtils.greaterThanZero(input);
        // assert
        assertFalse(result, "Should return false when input is null");
    }

    /**
     * Test scenario: Input is positive number
     * Expected: Should return true
     */
    @Test
    @DisplayName("When input is positive, should return true")
    public void testGreaterThanZero_WhenPositive() throws Throwable {
        // arrange
        Long input = 1L;
        // act
        boolean result = NumbersUtils.greaterThanZero(input);
        // assert
        assertTrue(result, "Should return true when input is positive");
    }

    /**
     * Test scenario: Input is negative number
     * Expected: Should return false
     */
    @Test
    @DisplayName("When input is negative, should return false")
    public void testGreaterThanZero_WhenNegative() throws Throwable {
        // arrange
        Long input = -1L;
        // act
        boolean result = NumbersUtils.greaterThanZero(input);
        // assert
        assertFalse(result, "Should return false when input is negative");
    }

    /**
     * Test scenario: Input is zero
     * Expected: Should return false
     */
    @Test
    @DisplayName("When input is zero, should return false")
    public void testGreaterThanZero_WhenZero() throws Throwable {
        // arrange
        Long input = 0L;
        // act
        boolean result = NumbersUtils.greaterThanZero(input);
        // assert
        assertFalse(result, "Should return false when input is zero");
    }

    /**
     * Test scenario: Input is maximum possible Long value
     * Expected: Should return true
     */
    @Test
    @DisplayName("When input is Long.MAX_VALUE, should return true")
    public void testGreaterThanZero_WhenMaxValue() throws Throwable {
        // arrange
        Long input = Long.MAX_VALUE;
        // act
        boolean result = NumbersUtils.greaterThanZero(input);
        // assert
        assertTrue(result, "Should return true when input is Long.MAX_VALUE");
    }

    /**
     * Test scenario: Input is minimum possible Long value
     * Expected: Should return false
     */
    @Test
    @DisplayName("When input is Long.MIN_VALUE, should return false")
    public void testGreaterThanZero_WhenMinValue() throws Throwable {
        // arrange
        Long input = Long.MIN_VALUE;
        // act
        boolean result = NumbersUtils.greaterThanZero(input);
        // assert
        assertFalse(result, "Should return false when input is Long.MIN_VALUE");
    }

    /**
     * Test toDouble method with null input
     */
    @Test
    public void testToDoubleWithNullInput() {
        // arrange
        Double input = null;
        // act
        double result = NumbersUtils.toDouble(input);
        // assert
        assertEquals(0.0, result, "Should return 0.0 for null input");
    }

    /**
     * Test toDouble method with positive double input
     */
    @Test
    public void testToDoubleWithPositiveInput() {
        // arrange
        Double input = 10.5;
        // act
        double result = NumbersUtils.toDouble(input);
        // assert
        assertEquals(10.5, result, "Should return the same value for positive input");
    }

    /**
     * Test toDouble method with negative double input
     */
    @Test
    public void testToDoubleWithNegativeInput() {
        // arrange
        Double input = -7.3;
        // act
        double result = NumbersUtils.toDouble(input);
        // assert
        assertEquals(-7.3, result, "Should return the same value for negative input");
    }

    /**
     * Test toDouble method with zero input
     */
    @Test
    public void testToDoubleWithZeroInput() {
        // arrange
        Double input = 0.0;
        // act
        double result = NumbersUtils.toDouble(input);
        // assert
        assertEquals(0.0, result, "Should return 0.0 for zero input");
    }

    /**
     * Test toDouble method with maximum double value input
     */
    @Test
    public void testToDoubleWithMaxDoubleValue() {
        // arrange
        Double input = Double.MAX_VALUE;
        // act
        double result = NumbersUtils.toDouble(input);
        // assert
        assertEquals(Double.MAX_VALUE, result, "Should return MAX_VALUE for maximum double input");
    }

    /**
     * Test toDouble method with minimum double value input
     */
    @Test
    public void testToDoubleWithMinDoubleValue() {
        // arrange
        Double input = Double.MIN_VALUE;
        // act
        double result = NumbersUtils.toDouble(input);
        // assert
        assertEquals(Double.MIN_VALUE, result, "Should return MIN_VALUE for minimum double input");
    }
}
