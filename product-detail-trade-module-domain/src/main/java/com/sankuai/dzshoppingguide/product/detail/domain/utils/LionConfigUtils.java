package com.sankuai.dzshoppingguide.product.detail.domain.utils;

import com.dianping.lion.client.Lion;
import com.sankuai.dzshoppingguide.product.detail.domain.utils.dtos.DealRepurchaseConfig;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.MarkupModuleItem;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.NavBarItem;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;

import java.util.*;

import static com.sankuai.dzshoppingguide.product.detail.domain.utils.LionConstants.*;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/2/6 16:05
 */
@Slf4j
public class LionConfigUtils {


    /**
     * 获取id映射缓存刷新时间
     *
     * @return 刷新时间（秒）
     */
    public static int getIdMapperCacheRefreshTime() {
        return Lion.getInt(DZTGDETAIL_APPKEY, ID_MAPPER_CACHE_REFRESH_TIME_CONFIG, 0);
    }

    /**
     * 获取id映射缓存过期时间
     *
     * @return 过期时间（秒）
     */
    public static int getIdMapperCacheExpiryTime() {
        return Lion.getInt(DZTGDETAIL_APPKEY, ID_MAPPER_CACHE_EXPIRE_TIME_CONFIG, 1296000);
    }

    public static String getOpenPlatformKey() {
        return Lion.getString(COMMON_MODULE_APPKEY, CITYID_TRANSFORM_OPEN_PLATFORM_KEY, "");
    }

    public static List<NavBarItem> getNavBarItemList() {
        return Lion.getList(COMMON_MODULE_APP_KEY, NAV_BAR_ITEM_LIST, NavBarItem.class, Collections.emptyList());
    }

    public static List<MarkupModuleItem> getMarkupModuleItemList() {
        return Lion.getList(LionConstants.TRADE_MODULE_APPKEY, LionConstants.MARKUP_MODULE_ITEM_LIST, MarkupModuleItem.class, Collections.emptyList());
    }

    /**
     * 获取页面来源到价格来源的映射关系
     * @return 返回页面来源到价格来源的映射关系的Map对象
     */
    public static Map<String, String> getPageSource2PriceSourceMap() {
        return Lion.getMap(DZTGDETAIL_APPKEY, LionConstants.PAGE_SOURCE_TO_PRICE_SOURCE, String.class, new HashMap<>());
    }

    public static boolean useNewSourceForCaixi() {
        return Lion.getBoolean(DZTGDETAIL_APPKEY, LionConstants.USE_NEW_SOURCE_FOR_CAIXI_SWITCH, false);
    }

    public static boolean judgeMemberPriceCategory(int secondCategoryId) {
        List<Integer> memberPriceCategoryIds = Lion.getList(DZTGDETAIL_APPKEY, LionConstants.MEMBER_PRICE_CATEGORY_IDS, Integer.class, Collections.emptyList());
        if (CollectionUtils.isEmpty(memberPriceCategoryIds)) {
            return false;
        }
        return memberPriceCategoryIds.contains(secondCategoryId);
    }

    /**
     * 获取比价助手实验配置
     * @return 实验配置信息
     */
    public static Map<String, String> getComparePriceAssistantExpConfig() {
        return Lion.getMap(DZTGDETAIL_APPKEY, COMPARE_PRICE_ASSISTANT_CATEGORY_AB_TEST_CONFIG, String.class, new HashMap<>());
    }

    /**
     * 获取N选1政府消费券MRN版控版本
     * @return MRN版本号
     */
    public static String getNSelectOneGovernmentCouponMrnVersion() {
        return Lion.getString(DZTGDETAIL_APPKEY, LionConstants.N_SELECT_ONE_GOVERNMENT_COUPON_MRN_VERSION, "0.5.8");
    }

    public static long getAtmosphereResourceLocationId(boolean isMt) {
        Map<String, Long> platform2ResourceLocationIdMap = Lion.getMap(TRADE_MODULE_APPKEY, ATMOSPHERE_RESOURCE_LOCATION_ID, Long.class, new HashMap<>());
        return platform2ResourceLocationIdMap.get(isMt ? "mt" : "dp");
    }

    /**
     * 复购货架 配置信息
     * enableSwitch：总开关
     * allPass：不区分行业和城市放量开关，默认false
     * categoryIds：适用行业的类目id, 如果为空则不区分行业
     * mtCityIds ：适用美团的城市id，如果为空则不区分城市
     * dpCityIds：适用的点评城市id，如果为空则不区分城市
     * 优先级：
     * enableSwitch > allPass > categoryIds &&（mtCityIds || dpCityIds）> （mtCityIds || dpCityIds）|| categoryIds
     *
     * @param isMt
     * @return
     */
    public static boolean enableRepurchase(int categoryId, boolean isMt, int cityId){
        DealRepurchaseConfig repurchaseConfig = LionConfigUtils.getRepurchaseConfig();
        if (Objects.nonNull(repurchaseConfig) && repurchaseConfig.isEnableSwitch()){
            if (repurchaseConfig.isAllPass()){
                // 不区分行业和城市推广
                return Boolean.TRUE;
            }
            List<Integer> categoryIds = repurchaseConfig.getCategoryIds();
            List<Integer> cityIds;

            if (isMt){
                cityIds = repurchaseConfig.getMtCityIds();
            }else {
                cityIds = repurchaseConfig.getDpCityIds();
            }
            // 在“指定行业”和“指定城市”下推广
            if (CollectionUtils.isNotEmpty(categoryIds) && CollectionUtils.isNotEmpty(cityIds)){
                return categoryIds.contains(categoryId) && cityIds.contains(cityId);
            }
            // 在“指定行业”下推广
            if (CollectionUtils.isNotEmpty(categoryIds) ){
                return categoryIds.contains(categoryId);
            }
            // 在“指定城市”下推广
            if ( CollectionUtils.isNotEmpty(cityIds)){
                return cityIds.contains(cityId);
            }
        }
        // 兜底逻辑
        return Boolean.FALSE;
    }

    /**
     * 获取同店比价样式的配置
     * @param
     * @return
     */
    public static DealRepurchaseConfig getRepurchaseConfig() {
        return Lion.getBean("com.sankuai.dzu.tpbase.dztgdetailweb",
                LionConstants.REPURCHASE_CONFIG, DealRepurchaseConfig.class);
    }

    public static boolean isComparePriceShopBlackList(boolean isMt, String shopIdStr) {
        Map<String, List> blackList = Lion.getMap("com.sankuai.dzu.tpbase.dztgdetailweb", LionConstants.COMPARE_PRICE_SHOP_BLACK_LIST, List.class, Collections.emptyMap());
        if (MapUtils.isEmpty(blackList)) {
            return false;
        }
        return blackList.containsKey(isMt ? "mt" : "dp") && blackList.get(isMt ? "mt" : "dp").contains(shopIdStr);
    }

    /**
     * 获取底部推荐相关配置参数
     */
    public static Map<String, String> getRelatedRecommendConfigMap() {
        return Lion.getMap(TRADE_MODULE_APPKEY, LionConstants.RELATED_RECOMMEND_CONFIG_MAP, String.class, Collections.emptyMap());
    }

    /**
     * 团购次卡接入先用后付团单二级类目白名单
     *
     * @return
     */
    public static List<Integer> getProductCreditPayCategories() {
        return Lion.getList(DZTGDETAIL_APPKEY, PRODUCT_CREDIT_PAY_CATEGORY, Integer.class, Collections.emptyList());
    }
}
