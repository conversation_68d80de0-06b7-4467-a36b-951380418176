package com.sankuai.dzshoppingguide.product.detail.spi.promo.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;

@Data
@MobileDo(id = 0x8556)
public class DealDetailPromoDetailExtraInfo implements Serializable {
    /**
    * 普通为0，会员专享优惠指定类型为1
    */
    @MobileDo.MobileField(key = 0x3e22)
    private int promoDetailExtraInfoType;

    /**
    * 一以内的折扣率
    */
    @MobileDo.MobileField(key = 0x2349)
    private String discountRateWithinOne;

    /**
    * 十以内的折扣率
    */
    @MobileDo.MobileField(key = 0x1082)
    private String discountRateWithinTen;


}