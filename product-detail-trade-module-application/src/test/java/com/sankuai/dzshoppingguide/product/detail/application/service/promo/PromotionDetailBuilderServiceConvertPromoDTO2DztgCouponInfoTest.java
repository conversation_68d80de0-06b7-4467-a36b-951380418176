package com.sankuai.dzshoppingguide.product.detail.application.service.promo;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.*;
import static org.powermock.api.mockito.PowerMockito.mockStatic;
import com.sankuai.dealuser.price.display.api.enums.CouponAssignStatusEnum;
import com.sankuai.dealuser.price.display.api.enums.PromoTypeEnum;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import com.sankuai.dealuser.price.display.api.model.PromoIdentity;
import com.sankuai.dzshoppingguide.product.detail.application.utils.LionConfigUtils;
import com.sankuai.dzshoppingguide.product.detail.application.utils.PromoHelper;
import com.sankuai.dzshoppingguide.product.detail.application.utils.PromoInfoHelper;
import com.sankuai.dzshoppingguide.product.detail.spi.promo.vo.DztgCouponInfo;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

@RunWith(PowerMockRunner.class)
@PrepareForTest({ PromoHelper.class, PromoInfoHelper.class, LionConfigUtils.class })
@PowerMockIgnore({ "javax.management.*", "javax.script.*" })
public class PromotionDetailBuilderServiceConvertPromoDTO2DztgCouponInfoTest {

    @Mock
    private LeafRepository leafRepository;

    @InjectMocks
    private PromotionDetailBuilderService promotionDetailBuilderService;

    @Before
    public void setUp() {
        PowerMockito.mockStatic(PromoHelper.class);
        PowerMockito.mockStatic(PromoInfoHelper.class);
        PowerMockito.mockStatic(LionConfigUtils.class);
    }

    /**
     * Test when input coupon is null
     */
    @Test
    public void testConvertPromoDTO2DztgCouponInfo_NullCoupon() throws Throwable {
        // arrange
        PromoDTO coupon = null;
        String mrnVersion = "1.0.0";
        // act
        DztgCouponInfo result = promotionDetailBuilderService.convertPromoDTO2DztgCouponInfo(coupon, mrnVersion);
        // assert
        assertNull(result);
    }

    /**
     * Test regular coupon with canAssign true
     */
    @Test
    public void testConvertPromoDTO2DztgCouponInfo_RegularCouponCanAssign() throws Throwable {
        // arrange
        PromoDTO coupon = new PromoDTO();
        PromoIdentity identity = new PromoIdentity(12345L, PromoTypeEnum.COUPON.getType());
        coupon.setIdentity(identity);
        coupon.setExtendDesc("Test Coupon");
        coupon.setCouponValueText("10");
        coupon.setAmount(new BigDecimal("10"));
        coupon.setCanAssign(true);
        coupon.setCouponValueType(1);
        coupon.setPriceLimitDesc("No limit");
        coupon.setUseTimeDesc("Valid for 30 days");
        String mrnVersion = "1.0.0";
        PowerMockito.when(PromoHelper.convertSourceTag(coupon)).thenReturn("优惠券");
        // act
        DztgCouponInfo result = promotionDetailBuilderService.convertPromoDTO2DztgCouponInfo(coupon, mrnVersion);
        // assert
        assertNotNull(result);
        assertEquals(12345L, result.getCouponGroupId());
        assertEquals(0, result.getStatus());
        assertEquals("Test Coupon", result.getTitle());
        assertEquals("10", result.getAmount());
        assertEquals("元", result.getAmountCornerMark());
        assertEquals("优惠券", result.getSourceTag());
    }

    /**
     * Test merchant coupon with canAssign false
     */
    @Test
    public void testConvertPromoDTO2DztgCouponInfo_MerchantCouponCannotAssign() throws Throwable {
        // arrange
        PromoDTO coupon = new PromoDTO();
        PromoIdentity identity = new PromoIdentity(12345L, PromoTypeEnum.COUPON.getType());
        identity.setPromoShowType("MERCHANT_COUPON");
        coupon.setIdentity(identity);
        coupon.setExtendDesc("Merchant Coupon");
        coupon.setCouponValueText("15");
        coupon.setAmount(new BigDecimal("15"));
        coupon.setCanAssign(false);
        coupon.setCouponValueType(1);
        coupon.setPriceLimitDesc("Min $20");
        coupon.setUseTimeDesc("Valid for 7 days");
        String mrnVersion = "1.0.0";
        PowerMockito.when(PromoHelper.convertSourceTag(coupon)).thenReturn("商家券");
        // act
        DztgCouponInfo result = promotionDetailBuilderService.convertPromoDTO2DztgCouponInfo(coupon, mrnVersion);
        // assert
        assertNotNull(result);
        assertEquals(12345L, result.getCouponGroupId());
        assertEquals(0, result.getCouponType());
        assertEquals(1, result.getStatus());
        assertEquals("Merchant Coupon", result.getTitle());
        assertEquals("15", result.getAmount());
        assertEquals("商家券", result.getSourceTag());
    }

    /**
     * Test government coupon with unassigned status
     */
    @Test
    public void testConvertPromoDTO2DztgCouponInfo_GovernmentCouponUnassigned() throws Throwable {
        // arrange
        PromoDTO coupon = new PromoDTO();
        PromoIdentity identity = new PromoIdentity(12345L, PromoTypeEnum.GOVERNMENT_CONSUME_COUPON.getType());
        coupon.setIdentity(identity);
        coupon.setCouponGroupId("54321");
        coupon.setExtendDesc("Government Coupon");
        coupon.setCouponValueText("50");
        coupon.setAmount(new BigDecimal("50"));
        coupon.setCouponAssignStatus(CouponAssignStatusEnum.UN_ASSIGNED.getCode());
        coupon.setCouponValueType(4);
        coupon.setPriceLimitDesc("Min $100");
        coupon.setUseTimeDesc("Valid until Dec 31");
        Map<String, String> promotionOtherInfoMap = new HashMap<>();
        promotionOtherInfoMap.put("FINANCE_EXT", "{\"packageSecretKey\":\"test-key\"}");
        coupon.setPromotionOtherInfoMap(promotionOtherInfoMap);
        String mrnVersion = "1.0.0";
        PowerMockito.when(PromoInfoHelper.getFinanceExtPackageSecretKey(coupon)).thenReturn("test-key");
        PowerMockito.when(LionConfigUtils.getNSelectOneGovernmentCouponMrnVersion()).thenReturn("0.5.8");
        PowerMockito.when(PromoHelper.convertSourceTag(coupon)).thenReturn("政府消费券");
        when(leafRepository.batchGenFinancialConsumeSerialId(anyInt())).thenReturn(Collections.singletonList(98765L));
        // act
        DztgCouponInfo result = promotionDetailBuilderService.convertPromoDTO2DztgCouponInfo(coupon, mrnVersion);
        // assert
        assertNotNull(result);
        assertEquals(54321L, result.getCouponGroupId());
        assertEquals(2, result.getCouponType());
        assertEquals(0, result.getStatus());
        assertEquals("Government Coupon", result.getTitle());
        assertEquals("50", result.getAmount());
        assertEquals("折", result.getAmountCornerMark());
        assertEquals("test-key", result.getPackageSecretKey());
        assertEquals("98765", result.getSerialno());
        assertEquals("政府消费券", result.getSourceTag());
    }

    /**
     * Test government coupon with mrnVersion less than configured version
     */
    @Test
    public void testConvertPromoDTO2DztgCouponInfo_MrnVersionLessThanConfigured() throws Throwable {
        // arrange
        PromoDTO coupon = new PromoDTO();
        PromoIdentity identity = new PromoIdentity(12345L, PromoTypeEnum.GOVERNMENT_CONSUME_COUPON.getType());
        coupon.setIdentity(identity);
        coupon.setCouponGroupId("54321");
        coupon.setExtendDesc("Government Coupon");
        coupon.setCouponValueText("50");
        coupon.setAmount(new BigDecimal("50"));
        Map<String, String> promotionOtherInfoMap = new HashMap<>();
        promotionOtherInfoMap.put("FINANCE_EXT", "{\"packageSecretKey\":\"test-key\"}");
        coupon.setPromotionOtherInfoMap(promotionOtherInfoMap);
        String mrnVersion = "0.5.7";
        PowerMockito.when(PromoInfoHelper.getFinanceExtPackageSecretKey(coupon)).thenReturn("test-key");
        PowerMockito.when(LionConfigUtils.getNSelectOneGovernmentCouponMrnVersion()).thenReturn("0.5.8");
        // act
        DztgCouponInfo result = promotionDetailBuilderService.convertPromoDTO2DztgCouponInfo(coupon, mrnVersion);
        // assert
        assertNull(result);
    }

    /**
     * Test government coupon with non-numeric couponGroupId
     */
    @Test
    public void testConvertPromoDTO2DztgCouponInfo_GovernmentCouponNonNumericGroupId() throws Throwable {
        // arrange
        PromoDTO coupon = new PromoDTO();
        PromoIdentity identity = new PromoIdentity(12345L, PromoTypeEnum.GOVERNMENT_CONSUME_COUPON.getType());
        coupon.setIdentity(identity);
        coupon.setCouponGroupId("non-numeric");
        coupon.setExtendDesc("Government Coupon");
        coupon.setCouponValueText("50");
        coupon.setAmount(new BigDecimal("50"));
        coupon.setCouponAssignStatus(CouponAssignStatusEnum.UN_ASSIGNED.getCode());
        String mrnVersion = "1.0.0";
        PowerMockito.when(PromoHelper.convertSourceTag(coupon)).thenReturn("政府消费券");
        // act
        DztgCouponInfo result = promotionDetailBuilderService.convertPromoDTO2DztgCouponInfo(coupon, mrnVersion);
        // assert
        assertNotNull(result);
        assertEquals(0L, result.getCouponGroupId());
        assertEquals("Government Coupon", result.getTitle());
        assertEquals("50", result.getAmount());
        assertEquals("政府消费券", result.getSourceTag());
    }

    /**
     * Test coupon with null amount or title
     */
    @Test
    public void testConvertPromoDTO2DztgCouponInfo_NullAmountOrTitle() throws Throwable {
        // arrange
        PromoDTO coupon = new PromoDTO();
        PromoIdentity identity = new PromoIdentity(12345L, PromoTypeEnum.COUPON.getType());
        coupon.setIdentity(identity);
        // null title
        coupon.setExtendDesc(null);
        // null amount
        coupon.setCouponValueText(null);
        coupon.setAmount(new BigDecimal("10"));
        coupon.setCanAssign(true);
        String mrnVersion = "1.0.0";
        // act
        DztgCouponInfo result = promotionDetailBuilderService.convertPromoDTO2DztgCouponInfo(coupon, mrnVersion);
        // assert
        assertNull(result);
    }

    /**
     * Test government coupon with empty leaf repository response
     */
    @Test
    public void testConvertPromoDTO2DztgCouponInfo_EmptyLeafResponse() throws Throwable {
        // arrange
        PromoDTO coupon = new PromoDTO();
        PromoIdentity identity = new PromoIdentity(12345L, PromoTypeEnum.GOVERNMENT_CONSUME_COUPON.getType());
        coupon.setIdentity(identity);
        coupon.setCouponGroupId("54321");
        coupon.setExtendDesc("Government Coupon");
        coupon.setCouponValueText("50");
        coupon.setAmount(new BigDecimal("50"));
        coupon.setCouponAssignStatus(CouponAssignStatusEnum.UN_ASSIGNED.getCode());
        String mrnVersion = "1.0.0";
        PowerMockito.when(PromoHelper.convertSourceTag(coupon)).thenReturn("政府消费券");
        when(leafRepository.batchGenFinancialConsumeSerialId(anyInt())).thenReturn(Collections.emptyList());
        // act
        DztgCouponInfo result = promotionDetailBuilderService.convertPromoDTO2DztgCouponInfo(coupon, mrnVersion);
        // assert
        assertNotNull(result);
        assertEquals(54321L, result.getCouponGroupId());
        assertEquals(2, result.getCouponType());
        assertEquals(0, result.getStatus());
        assertNull(result.getSerialno());
        assertEquals("政府消费券", result.getSourceTag());
    }
}
