package com.sankuai.dzshoppingguide.product.detail.application.utils;

import org.apache.commons.lang.StringUtils;

/**
 * @Author: <EMAIL>
 * @Date: 2024/6/25
 */
public class ShareIdUtils {
    private static final String[] NUMBER_CONVERSION = new String[]{"0", "1", "2", "3", "4",
            "5", "6", "7", "8", "9", "a", "b", "c", "d", "e", "f", "g", "h", "i", "j", "k",
            "l", "m", "n", "o", "p", "q", "r", "s", "t", "u", "v", "w", "x", "y", "z"};

    public static String getShareId() {
        return getShareId(4);
    }

    public static String getShareId(int length) {
        String timestamp = longto36(System.currentTimeMillis());
        String randomNumber = getRandomNumber(length);
        String shareId = timestamp + randomNumber;
        return shareId;
    }

    private static String getRandomNumber(int length) {
        StringBuffer sb = new StringBuffer();
        int max = 35;
        int min = 0;
        for (int i = 0; i < length; i++) {
            int num = (int) (Math.random() * (max - min + 1) + min);
            String tmp = NUMBER_CONVERSION[num];
            sb.append(tmp);
        }
        String result = sb.toString();
        return StringUtils.isEmpty(result) ? "" : result;
    }

    private static String longto36(long num) {
        long source = num;
        String result = "";
        while (source != 0) {
            result = NUMBER_CONVERSION[(int) (source % 36)] + result;
            source = Math.round(Math.floor(source / 36));
        }
        switch (result.length() % 8) {
            case 0:
                return result;
            case 1:
                return "0000000" + result;
            case 2:
                return "000000" + result;
            case 3:
                return "00000" + result;
            case 4:
                return "0000" + result;
            case 5:
                return "000" + result;
            case 6:
                return "00" + result;
            case 7:
                return "0" + result;
            default:
                return "";
        }
    }
}
