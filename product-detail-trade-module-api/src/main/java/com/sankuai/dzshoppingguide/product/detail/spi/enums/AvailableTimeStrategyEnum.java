package com.sankuai.dzshoppingguide.product.detail.spi.enums;

import lombok.Getter;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2024/12/12 15:59
 */
@Getter
public enum AvailableTimeStrategyEnum {
    DEFAULT_STRATEGY(Integer.MAX_VALUE, "默认策略"),
    KTV_STRATEGY(301, "ktv策略"),
    WIN_BAR_STRATEGY(312, "酒吧策略");

    private int code;
    private String desc;

    AvailableTimeStrategyEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static AvailableTimeStrategyEnum valueOf(int code) {
        for (AvailableTimeStrategyEnum availableTimeStrategyEnum : values()) {
            if (availableTimeStrategyEnum.code == code) {
                return availableTimeStrategyEnum;
            }
        }
        return null;
    }
}
