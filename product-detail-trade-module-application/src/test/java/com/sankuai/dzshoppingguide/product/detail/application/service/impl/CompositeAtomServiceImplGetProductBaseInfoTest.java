package com.sankuai.dzshoppingguide.product.detail.application.service.impl;

import com.dianping.pay.promo.display.api.PromoDisplayService;
import com.dianping.pay.promo.display.api.dto.PromoDisplayDTO;
import com.dianping.pay.promo.display.api.dto.QueryPromoDisplayRequest;
import com.dianping.pay.promo.rule.api.dto.Response;
import com.dianping.pigeon.remoting.invoker.concurrent.InvocationCallback;
import com.dianping.pigeon.remoting.invoker.util.InvokerHelper;
import com.google.common.util.concurrent.SettableFuture;
import com.meituan.service.mobile.mtthrift.netty.ContextStore;
import com.sankuai.clr.content.process.gateway.thrift.api.LeadsQueryGatewayService;
import com.sankuai.clr.content.process.gateway.thrift.dto.leads.LoadLeadsInfoReqDTO;
import com.sankuai.clr.content.process.gateway.thrift.dto.leads.LoadLeadsInfoRespDTO;
import com.sankuai.dzshoppingguide.product.detail.domain.utils.PigeonCallbackUtils;
import com.sankuai.dztheme.massagebook.theme.ReserveThemeQueryService;
import com.sankuai.dztheme.massagebook.theme.req.ReserveQueryRequest;
import com.sankuai.dztheme.massagebook.theme.res.ReserveQueryResponse;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResponse;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResult;
import com.sankuai.general.product.query.center.client.service.DealGroupQueryService;
import org.apache.thrift.TException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.stubbing.Answer;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.sankuai.dzshoppingguide.product.detail.domain.utils.ThriftAsyncUtils;
import com.sankuai.mppack.product.client.query.model.TyingCombineItem;
import com.sankuai.mppack.product.client.query.request.TyingBlackListQueryRequest;
import com.sankuai.mppack.product.client.query.response.TyingBlackListResponse;
import com.sankuai.mppack.product.client.supply.service.PackBlackListService;
import java.util.Collections;
import java.util.concurrent.ExecutionException;
import org.mockito.MockedStatic;
import org.mockito.junit.*;

@ExtendWith(MockitoExtension.class)
public class CompositeAtomServiceImplGetProductBaseInfoTest {

    @Mock
    private DealGroupQueryService dealGroupQueryServiceAtom;

    @InjectMocks
    private CompositeAtomServiceImpl compositeAtomService;

    @Mock
    private LeadsQueryGatewayService leadsQueryGatewayServiceFuture;

    @Mock
    private ReserveThemeQueryService reserveThemeQueryService;

    @Mock
    private PromoDisplayService promoDisplayServiceFuture;

    @Mock
    private PackBlackListService packBlackListService;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.openMocks(this);
    }

    @BeforeEach
    public void tearDown() {
        ContextStore.removeFuture();
    }

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    /**
     * Test getProductBaseInfo with valid dealGroupIds and successful query.
     */
    @Test
    public void testGetProductBaseInfo_Successful() throws Throwable {
        // arrange
        List<Long> dealGroupIds = new ArrayList<>();
        dealGroupIds.add(1L);
        dealGroupIds.add(2L);
        QueryDealGroupListResponse mockResponse = new QueryDealGroupListResponse();
        QueryDealGroupListResult mockResult = new QueryDealGroupListResult();
        List<DealGroupDTO> expectedList = new ArrayList<>();
        expectedList.add(new DealGroupDTO());
        mockResult.setList(expectedList);
        mockResponse.setData(mockResult);
        when(dealGroupQueryServiceAtom.queryByDealGroupIds(any(QueryByDealGroupIdRequest.class))).thenReturn(mockResponse);
        // act
        List<DealGroupDTO> result = compositeAtomService.getProductBaseInfo(dealGroupIds, true);
        // assert
        assertNotNull(result);
        assertEquals(expectedList.size(), result.size());
        assertEquals(expectedList, result);
    }

    /**
     * Test getProductBaseInfo with valid dealGroupIds but throws TException.
     */
    @Test
    public void testGetProductBaseInfo_ThrowsException() throws Throwable {
        // arrange
        List<Long> dealGroupIds = new ArrayList<>();
        dealGroupIds.add(1L);
        dealGroupIds.add(2L);
        when(dealGroupQueryServiceAtom.queryByDealGroupIds(any(QueryByDealGroupIdRequest.class))).thenThrow(new TException("Service failure"));
        // act
        List<DealGroupDTO> result = compositeAtomService.getProductBaseInfo(dealGroupIds, true);
        // assert
        assertNull(result);
        verify(dealGroupQueryServiceAtom).queryByDealGroupIds(any(QueryByDealGroupIdRequest.class));
    }

    /**
     * Test getProductBaseInfo with empty dealGroupIds list.
     */
    @Test
    public void testGetProductBaseInfo_EmptyList() throws Throwable {
        // arrange
        List<Long> dealGroupIds = new ArrayList<>();
        QueryDealGroupListResponse mockResponse = new QueryDealGroupListResponse();
        QueryDealGroupListResult mockResult = new QueryDealGroupListResult();
        mockResult.setList(new ArrayList<>());
        mockResponse.setData(mockResult);
        when(dealGroupQueryServiceAtom.queryByDealGroupIds(any(QueryByDealGroupIdRequest.class))).thenReturn(mockResponse);
        // act
        List<DealGroupDTO> result = compositeAtomService.getProductBaseInfo(dealGroupIds, true);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试loadLeadsInfo方法在正常情况下的行为
     * - 验证当服务调用成功时能正确返回结果
     */
    @Test
    public void testLoadLeadsInfoNormal() throws Throwable {
        // arrange
        LoadLeadsInfoReqDTO request = new LoadLeadsInfoReqDTO();
        LoadLeadsInfoRespDTO expectedResponse = new LoadLeadsInfoRespDTO();
        SettableFuture<LoadLeadsInfoRespDTO> settableFuture = SettableFuture.create();
        ContextStore.setFuture(settableFuture);
        when(leadsQueryGatewayServiceFuture.loadLeadsInfo(request)).thenReturn(expectedResponse);
        // act
        CompletableFuture<LoadLeadsInfoRespDTO> future = compositeAtomService.loadLeadsInfo(request);
        settableFuture.set(expectedResponse);
        // assert
        assertNotNull(future);
        assertEquals(expectedResponse, future.get());
        verify(leadsQueryGatewayServiceFuture).loadLeadsInfo(request);
    }

    /**
     * 测试loadLeadsInfo方法对null请求参数的处理
     * - 验证当传入null请求时的行为
     */
    @Test
    public void testLoadLeadsInfoWithNullRequest() throws Throwable {
        // arrange
        SettableFuture<LoadLeadsInfoRespDTO> settableFuture = SettableFuture.create();
        ContextStore.setFuture(settableFuture);
        when(leadsQueryGatewayServiceFuture.loadLeadsInfo(null)).thenReturn(null);
        // act
        CompletableFuture<LoadLeadsInfoRespDTO> future = compositeAtomService.loadLeadsInfo(null);
        settableFuture.set(null);
        // assert
        assertNotNull(future);
        assertNull(future.get());
        verify(leadsQueryGatewayServiceFuture).loadLeadsInfo(null);
    }

    /**
     * 测试异常场景：reserveThemeQueryService.query(request) 抛出异常
     * 验证返回值为 null 且异常被正确处理
     */
    @Test
    public void testQueryReserveProductException() throws Throwable {
        // arrange
        ReserveQueryRequest request = new ReserveQueryRequest();
        RuntimeException expectedException = new RuntimeException("Service error");
        CountDownLatch latch = new CountDownLatch(1);
        doAnswer(invocation -> {
            new Thread(() -> {
                try {
                    CompletableFuture<ReserveQueryResponse> future = PigeonCallbackUtils.setPigeonCallback();
                    future.completeExceptionally(expectedException);
                    latch.countDown();
                } catch (Exception e) {
                    fail("Unexpected exception: " + e.getMessage());
                }
            }).start();
            return null;
        }).when(reserveThemeQueryService).query(any(ReserveQueryRequest.class));
        // act
        CompletableFuture<ReserveQueryResponse> future = compositeAtomService.queryReserveProduct(request);
        // assert
        assertTrue(latch.await(2, TimeUnit.SECONDS), "Callback was not executed within timeout");
        assertNotNull(future);
        ReserveQueryResponse response = future.getNow(null);
        assertNull(response);
        verify(reserveThemeQueryService, times(1)).query(request);
    }

    /**
     * Tests the queryPromoDisplayDTO method under normal conditions
     * Expected: Returns a CompletableFuture containing a list of PromoDisplayDTO
     */
    @Test
    public void testQueryPromoDisplayDTO_Normal() throws Throwable {
        // arrange
        QueryPromoDisplayRequest request = new QueryPromoDisplayRequest();
        Response<List<PromoDisplayDTO>> response = new Response<>();
        List<PromoDisplayDTO> promoList = new ArrayList<>();
        promoList.add(new PromoDisplayDTO());
        response.setSuccess(true);
        response.setResult(promoList);
        CompletableFuture<List<PromoDisplayDTO>> expectedFuture = new CompletableFuture<>();
        doAnswer((Answer<Void>) invocation -> {
            InvocationCallback callback = InvokerHelper.getCallback();
            if (callback != null) {
                callback.onSuccess(response);
            }
            return null;
        }).when(promoDisplayServiceFuture).queryPromoDisplayDTO(any(QueryPromoDisplayRequest.class));
        // act
        CompletableFuture<List<PromoDisplayDTO>> result = compositeAtomService.queryPromoDisplayDTO(request);
        // assert
        List<PromoDisplayDTO> resultList = result.get(100, TimeUnit.MILLISECONDS);
        assertNotNull(resultList);
        assertEquals(1, resultList.size());
    }

    /**
     * Tests the queryPromoDisplayDTO method when an exception occurs
     * Expected: Returns a CompletableFuture containing null
     */
    @Test
    public void testQueryPromoDisplayDTO_Exception() throws Throwable {
        // arrange
        QueryPromoDisplayRequest request = new QueryPromoDisplayRequest();
        doAnswer((Answer<Void>) invocation -> {
            InvocationCallback callback = InvokerHelper.getCallback();
            if (callback != null) {
                callback.onFailure(new RuntimeException("Test exception"));
            }
            return null;
        }).when(promoDisplayServiceFuture).queryPromoDisplayDTO(any(QueryPromoDisplayRequest.class));
        // act
        CompletableFuture<List<PromoDisplayDTO>> result = compositeAtomService.queryPromoDisplayDTO(request);
        // assert
        List<PromoDisplayDTO> resultList = result.get(100, TimeUnit.MILLISECONDS);
        assertNull(resultList);
    }

    /**
     * Tests the queryPromoDisplayDTO method when response is not successful
     * Expected: Returns a CompletableFuture containing null
     */
    @Test
    public void testQueryPromoDisplayDTO_UnsuccessfulResponse() throws Throwable {
        // arrange
        QueryPromoDisplayRequest request = new QueryPromoDisplayRequest();
        Response<List<PromoDisplayDTO>> response = new Response<>();
        response.setSuccess(false);
        doAnswer((Answer<Void>) invocation -> {
            InvocationCallback callback = InvokerHelper.getCallback();
            if (callback != null) {
                callback.onSuccess(response);
            }
            return null;
        }).when(promoDisplayServiceFuture).queryPromoDisplayDTO(any(QueryPromoDisplayRequest.class));
        // act
        CompletableFuture<List<PromoDisplayDTO>> result = compositeAtomService.queryPromoDisplayDTO(request);
        // assert
        List<PromoDisplayDTO> resultList = result.get(100, TimeUnit.MILLISECONDS);
        assertNull(resultList);
    }

    @Test
    public void testBatchQueryTyingBlackList_Success() throws Throwable {
        // arrange
        TyingBlackListQueryRequest request = new TyingBlackListQueryRequest();
        request.setChannel(1);
        request.setTyingCombineItemList(Collections.singletonList(new TyingCombineItem()));
        TyingBlackListResponse expectedResponse = new TyingBlackListResponse();
        expectedResponse.setSuccess(true);
        // act
        CompletableFuture<TyingBlackListResponse> result = compositeAtomService.batchQueryTyingBlackList(request);
        // assert
        assertNotNull(result);
        verify(packBlackListService).batchQueryTyingBlackList(request);
    }

    @Test
    public void testBatchQueryTyingBlackList_ServiceThrowsException() throws Throwable {
        // arrange
        TyingBlackListQueryRequest request = new TyingBlackListQueryRequest();
        request.setChannel(1);
        request.setTyingCombineItemList(Collections.singletonList(new TyingCombineItem()));
        doThrow(new RuntimeException("Service error")).when(packBlackListService).batchQueryTyingBlackList(request);
        // act
        CompletableFuture<TyingBlackListResponse> result = compositeAtomService.batchQueryTyingBlackList(request);
        // assert
        assertNotNull(result);
        verify(packBlackListService).batchQueryTyingBlackList(request);
        CompletableFuture<TyingBlackListResponse> completedFuture = CompletableFuture.completedFuture(null);
        assertEquals(completedFuture.get(), result.get());
    }

    @Test
    public void testBatchQueryTyingBlackList_EmptyList() throws Throwable {
        // arrange
        TyingBlackListQueryRequest request = new TyingBlackListQueryRequest();
        request.setChannel(1);
        request.setTyingCombineItemList(Collections.emptyList());
        // act
        CompletableFuture<TyingBlackListResponse> result = compositeAtomService.batchQueryTyingBlackList(request);
        // assert
        assertNotNull(result);
        verify(packBlackListService).batchQueryTyingBlackList(request);
    }
}
