package com.sankuai.dzshoppingguide.product.detail.application.utils;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.sankuai.dzshoppingguide.product.detail.application.utils.model.TaskInfoModel;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class PlayCenterWrapperBuildTaskInfoTest {

    @Mock
    private JSONObject mockTaskInfoObj;

    /**
     * Test scenario: Input JSONObject is null
     * Expected: Returns null
     */
    @Test
    public void testBuildTaskInfo_WhenInputNull_ShouldReturnNull() throws Throwable {
        // arrange
        JSONObject nullTaskInfoObj = null;
        // act
        TaskInfoModel result = PlayCenterWrapper.buildTaskInfo(nullTaskInfoObj);
        // assert
        assertNull(result);
    }

    /**
     * Test scenario: All fields in JSONObject are null
     * Expected: Returns TaskInfoModel with null/empty values
     */
    @Test
    public void testBuildTaskInfo_WhenAllFieldsNull_ShouldReturnEmptyModel() throws Throwable {
        // arrange
        when(mockTaskInfoObj.getInteger("prizeCount")).thenReturn(0);
        when(mockTaskInfoObj.getLong("taskEndTime")).thenReturn(null);
        when(mockTaskInfoObj.getJSONArray("prizeInfoList")).thenReturn(null);
        // act
        TaskInfoModel result = PlayCenterWrapper.buildTaskInfo(mockTaskInfoObj);
        // assert
        assertNotNull(result);
        assertEquals(0, result.getPrizeCount());
        assertNull(result.getTaskEndTime());
        assertNotNull(result.getPrizeInfoList());
        assertTrue(result.getPrizeInfoList().isEmpty());
    }

    /**
     * Test scenario: JSONObject contains only basic fields without prizeInfoList
     * Expected: Returns TaskInfoModel with basic fields populated and empty prizeInfoList
     */
    @Test
    public void testBuildTaskInfo_WhenBasicFieldsOnly_ShouldReturnModelWithBasicFields() throws Throwable {
        // arrange
        JSONObject taskInfoObj = new JSONObject();
        taskInfoObj.put("prizeCount", 5);
        taskInfoObj.put("taskEndTime", 1234567890L);
        // act
        TaskInfoModel result = PlayCenterWrapper.buildTaskInfo(taskInfoObj);
        // assert
        assertNotNull(result);
        assertEquals(5, result.getPrizeCount());
        assertEquals(1234567890L, result.getTaskEndTime());
        assertNotNull(result.getPrizeInfoList());
        assertTrue(result.getPrizeInfoList().isEmpty());
    }

    /**
     * Test scenario: JSONObject contains complete valid data
     * Expected: Returns fully populated TaskInfoModel
     */
    @Test
    public void testBuildTaskInfo_WhenCompleteValidData_ShouldReturnFullModel() throws Throwable {
        // arrange
        JSONObject taskInfoObj = new JSONObject();
        taskInfoObj.put("prizeCount", 3);
        taskInfoObj.put("taskEndTime", 1234567890L);
        JSONArray prizeInfoArray = new JSONArray();
        JSONObject prize = new JSONObject();
        prize.put("prizeName", "Test Prize");
        prize.put("image", "test.jpg");
        prizeInfoArray.add(prize);
        taskInfoObj.put("prizeInfoList", prizeInfoArray);
        // act
        TaskInfoModel result = PlayCenterWrapper.buildTaskInfo(taskInfoObj);
        // assert
        assertNotNull(result);
        assertEquals(3, result.getPrizeCount());
        assertEquals(1234567890L, result.getTaskEndTime());
        assertNotNull(result.getPrizeInfoList());
        assertEquals(1, result.getPrizeInfoList().size());
        assertEquals("Test Prize", result.getPrizeInfoList().get(0).getPrizeName());
        assertEquals("test.jpg", result.getPrizeInfoList().get(0).getPrizeImage());
    }

    /**
     * Test scenario: JSONObject contains empty prizeInfoList
     * Expected: Returns TaskInfoModel with empty prizeInfoList
     */
    @Test
    public void testBuildTaskInfo_WhenEmptyPrizeInfoList_ShouldReturnModelWithEmptyList() throws Throwable {
        // arrange
        JSONObject taskInfoObj = new JSONObject();
        taskInfoObj.put("prizeCount", 1);
        taskInfoObj.put("taskEndTime", 1234567890L);
        taskInfoObj.put("prizeInfoList", new JSONArray());
        // act
        TaskInfoModel result = PlayCenterWrapper.buildTaskInfo(taskInfoObj);
        // assert
        assertNotNull(result);
        assertEquals(1, result.getPrizeCount());
        assertEquals(1234567890L, result.getTaskEndTime());
        assertNotNull(result.getPrizeInfoList());
        assertTrue(result.getPrizeInfoList().isEmpty());
    }
}
