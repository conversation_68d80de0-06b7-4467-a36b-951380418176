package com.sankuai.dzshoppingguide.product.detail.application.builder.bookingtime;

import com.google.common.collect.Lists;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: guang<PERSON><PERSON>e
 * @Date: 2025/6/30 14:35
 */
public class DealBookingDayOffsetConstant {

    public final static List<Integer> DAY_OFFSETS = Lists.newArrayList(0, 1, 2, 3, 4, 5, 6);

    public final static List<Integer> FETCHER_DAY_OFFSETS = new ArrayList<>();

    static {
        //因为目前有凌晨场的逻辑，即当天时间片需要聚合第二天凌晨场，所以查询数据的时候需要加上最后一天的第二天数据
        int additionalDayOffset = DAY_OFFSETS.get(DAY_OFFSETS.size() - 1) + 1;
        FETCHER_DAY_OFFSETS.addAll(DAY_OFFSETS);
        FETCHER_DAY_OFFSETS.add(additionalDayOffset);
    }

    public final static List<Integer> BEFORE_DAWN_BUILDER_DAY_OFFSETS = new ArrayList<>();

    static {
        //因为目前有凌晨场的逻辑，即第一天的凌晨场需要聚合到前一天，所以要在最前面的tab增加一个凌晨场
        int additionalDayOffset = DAY_OFFSETS.get(0) - 1;
        BEFORE_DAWN_BUILDER_DAY_OFFSETS.add(additionalDayOffset);
        BEFORE_DAWN_BUILDER_DAY_OFFSETS.addAll(DAY_OFFSETS);
    }

}
