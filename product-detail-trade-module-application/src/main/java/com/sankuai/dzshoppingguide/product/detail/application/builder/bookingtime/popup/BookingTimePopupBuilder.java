package com.sankuai.dzshoppingguide.product.detail.application.builder.bookingtime.popup;

import com.google.common.collect.Lists;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bookingtime.AbstractBookingTimeModuleBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bookingtime.DealBookingDayOffsetConstant;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bookingtime.enums.MoreActionEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bookingtime.enums.MultiTradeBizEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bookingtime.enums.MultiTradeSourceTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.abtest.AbTestFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.bookingtime.DealBookingTimeFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.bookingtime.ShopBookingFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.category.ProductCategoryFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.handler.booking.BookingTimeModuleOrderPageUrlFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sale.info.ProductSaleInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.FetcherDAGStarter;
import com.sankuai.dzshoppingguide.product.detail.application.utils.DateTimeUtils;
import com.sankuai.dzshoppingguide.product.detail.application.utils.JsonUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.bookingtime.vo.BookingDateTab;
import com.sankuai.dzshoppingguide.product.detail.spi.bookingtime.vo.BookingTimeCardVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bookingtime.vo.BookingTimeItemVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bookingtime.vo.BookingTimeModuleVO;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.spt.statequery.api.dto.BaseStateDTO;
import com.sankuai.spt.statequery.api.dto.TimeSliceDTO;
import com.sankuai.spt.statequery.api.enums.TimeSliceRecallStrategyEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.jetbrains.annotations.Nullable;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Builder(
        moduleKey = ModuleKeyConstants.MODULE_DETAIL_BOOKING_TIME_POPUP,
        startFetcher = FetcherDAGStarter.class,
        dependentFetchers = {
                ProductBaseInfoFetcher.class,
                ProductCategoryFetcher.class,
                ShopBookingFetcher.class,
                DealBookingTimeFetcher.class,
                BookingTimeModuleOrderPageUrlFetcher.class,
                AbTestFetcher.class,
                ProductSaleInfoFetcher.class,
        }
)
@Slf4j
public class BookingTimePopupBuilder extends AbstractBookingTimeModuleBuilder {

    @Override
    public BookingTimeModuleVO doBuild() {
        init();
        if (!isShow()) {
            return null;
        }
        try {
            switch (productCategory.getProductSecondCategoryId()) {
                case 301:
                    return getBookingTimeModuleVO(MultiTradeBizEnum.KTV);
                case 303:
                    return getBookingTimeModuleVO(MultiTradeBizEnum.MASSAGE);
                default:
                    return null;
            }
        } catch (Exception e) {
            log.error("buildPopup failed, request={}", JsonUtils.toJson(request), e);
        }
        return null;
    }

    private @Nullable BookingTimeModuleVO getBookingTimeModuleVO(MultiTradeBizEnum ktv) {
        if (!fromMarketingSource) {
            //非营销场不弹起浮层
            return null;
        }
        BookingTimeModuleVO timeModuleVO = new BookingTimeModuleVO();
        timeModuleVO.setTitle("全部可订时间");
        timeModuleVO.setTabList(buildPopDateTab());
        timeModuleVO.setMoreText("");
        timeModuleVO.setMoreAction(MoreActionEnum.NONE.getValue());
        timeModuleVO.setBiz(ktv.getValue());
        timeModuleVO.setSourceType(fromMarketingSource ? MultiTradeSourceTypeEnum.SALES.getValue() : MultiTradeSourceTypeEnum.MAIN.getValue());
        return timeModuleVO;
    }

    private List<BookingDateTab> buildPopDateTab() {
        List<BookingDateTab> dateTabList = Lists.newArrayList();
        for (int offset : DealBookingDayOffsetConstant.BEFORE_DAWN_BUILDER_DAY_OFFSETS) {
            BookingDateTab bookingDateTab = buildPopSingleDate(offset);
            if (bookingDateTab != null) {
                dateTabList.add(bookingDateTab);
            }
        }
        return dateTabList;
    }

    private BookingDateTab buildPopSingleDate(final int offset) {
        final String offsetDate = DateTimeUtils.getOffsetDate(offset);
        final BookingDateTab bookingDateTab = new BookingDateTab();
        bookingDateTab.setTitle(getWeekText(offset));
        bookingDateTab.setSubTitle(DateTimeUtils.getMonthDate(LocalDateTime.now().plusDays(offset)));
        final List<BookingTimeCardVO> timeCardVOList = new ArrayList<>();
        //处理今日时间片
        final BaseStateDTO todayBaseStateDTO = getBaseStateDTO(offsetDate);
        if (todayBaseStateDTO != null && CollectionUtils.isNotEmpty(todayBaseStateDTO.getTimeSlices())) {
            List<BookingTimeCardVO> todayTimeCardVOList = todayBaseStateDTO.getTimeSlices().stream()
                    .map(timeSliceDTO -> toPopupTimeCard(timeSliceDTO, TimeSliceRecallStrategyEnum.TODAY))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            timeCardVOList.addAll(todayTimeCardVOList);
        }
        //处理明日时间片中标记为昨日召回的时间片
        final BaseStateDTO tomorrowBaseStateDTO = getBaseStateDTO(DateTimeUtils.getOffsetDate(offset + 1));
        if (tomorrowBaseStateDTO != null && CollectionUtils.isNotEmpty(tomorrowBaseStateDTO.getTimeSlices())) {
            List<BookingTimeCardVO> tomorrowTimeCardVOList = tomorrowBaseStateDTO.getTimeSlices().stream()
                    .map(timeSliceDTO -> toPopupTimeCard(timeSliceDTO, TimeSliceRecallStrategyEnum.YESTERDAY))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            timeCardVOList.addAll(tomorrowTimeCardVOList);
        }
        //如果时间片为空则不展示时间选择偏
        if (CollectionUtils.isEmpty(timeCardVOList)) {
            if (offset < 0) {
                return null;//凌晨场如果没有可选时间片直接删除tab
            }
            bookingDateTab.setTips("当日暂无可订时间，换个时间试试哦");
        } else {
            bookingDateTab.setTimeCardList(timeCardVOList);
        }
        return bookingDateTab;
    }

    private BookingTimeCardVO toPopupTimeCard(final TimeSliceDTO timeSliceDTO,
                                              final TimeSliceRecallStrategyEnum selectedRecallStrategy) {
        if (!timeSliceDTO.getRecallStrategies().contains(selectedRecallStrategy)) {
            //如果时间片召回规则不包含指定规则类型
            return null;
        }
        BookingTimeCardVO bookingTimeCardVO = new BookingTimeCardVO();
        BookingStateInfo stateInfo = getBookingState(timeSliceDTO);
        if (stateInfo == null) {
            return null;
        }
        BookingTimeItemVO bookingState = BookingTimeItemVO.builder()
                .content(stateInfo.getText())
                .contentColor(stateInfo.getColor())
                .contentSize("12")
                .contentWeight("400")
                .build();
        String prefix = selectedRecallStrategy == TimeSliceRecallStrategyEnum.YESTERDAY ? "次日" : "";
        BookingTimeItemVO timeInfo = BookingTimeItemVO.builder()
                .content(prefix + timeSliceDTO.getStartTime())
                .contentColor("订满".equals(stateInfo.getText()) ? stateInfo.getColor() : "#555555")
                .contentSize("14")
                .contentWeight("400")
                .build();
        bookingTimeCardVO.setBookingInfo(Lists.newArrayList(timeInfo, bookingState));
        bookingTimeCardVO.setClickable(false);
        bookingTimeCardVO.setClickUrl("");
        return bookingTimeCardVO;
    }

}
