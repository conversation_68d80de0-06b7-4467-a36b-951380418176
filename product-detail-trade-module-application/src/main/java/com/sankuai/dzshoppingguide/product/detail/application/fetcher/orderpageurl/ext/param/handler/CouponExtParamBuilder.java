package com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.ext.param.handler;

import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.enums.OrderPageExtParamEnums;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.ext.param.BaseSingleExtParamBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.ext.param.request.ExtParamBuilderRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.purchase.coupon.PurchaseCouponReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.utils.GsonUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * @Author: guangyujie
 * @Date: 2025/3/18 14:57
 */
@Component
public class CouponExtParamBuilder extends BaseSingleExtParamBuilder {

    @Override
    protected OrderPageExtParamEnums buildKey() {
        return OrderPageExtParamEnums.otherpromptinfo;
    }

    @Override
    protected String doBuildExtParam(final ProductDetailPageRequest request,
                                     final ExtParamBuilderRequest builderRequest) throws Exception {
        List<Map<String, Object>> couponParamForOrderPage = Optional.ofNullable(builderRequest.getPurchaseCoupon())
                .map(PurchaseCouponReturnValue::getCouponParamForOrderPage)
                .orElse(new ArrayList<>());
        if (CollectionUtils.isEmpty(couponParamForOrderPage)) {
            return null;
        }
        return GsonUtils.toJsonString(couponParamForOrderPage);
    }

}
