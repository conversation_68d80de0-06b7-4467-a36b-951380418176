package com.sankuai.dzshoppingguide.product.detail.application.utils;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.sankuai.dzshoppingguide.product.detail.application.utils.model.MaterialInfoModel;
import com.sankuai.dzshoppingguide.product.detail.application.utils.model.PlayActivityModel;
import com.sankuai.dzshoppingguide.product.detail.application.utils.model.PlayInfoModel;
import com.sankuai.dzshoppingguide.product.detail.application.utils.model.TaskInfoModel;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class PlayCenterWrapperBuildPlayActivityModelListTest {

    @Mock
    private JSONObject mockPlayInfo;

    /**
     * Test case: input array is null
     * Expected: return empty list
     */
    @Test
    public void testBuildPlayActivityModelListNullArray() throws Throwable {
        // arrange
        JSONArray array = null;
        // act
        List<PlayActivityModel> result = PlayCenterWrapper.buildPlayActivityModelList(array);
        // assert
        assertNotNull(result);
        assertEquals(0, result.size());
    }

    /**
     * Test case: input array is empty
     * Expected: return empty list
     */
    @Test
    public void testBuildPlayActivityModelListEmptyArray() throws Throwable {
        // arrange
        JSONArray array = new JSONArray();
        // act
        List<PlayActivityModel> result = PlayCenterWrapper.buildPlayActivityModelList(array);
        // assert
        assertNotNull(result);
        assertEquals(0, result.size());
    }

    /**
     * Test case: array contains null object
     * Expected: return empty list
     */
    @Test
    public void testBuildPlayActivityModelListNullObj() throws Throwable {
        // arrange
        JSONArray array = new JSONArray();
        JSONObject obj = new JSONObject();
        array.add(obj);
        // act
        List<PlayActivityModel> result = PlayCenterWrapper.buildPlayActivityModelList(array);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertNull(result.get(0).getPlayInfo());
    }

    /**
     * Test case: array contains object without playInfo
     * Expected: return list with one element with null playInfo
     */
    @Test
    public void testBuildPlayActivityModelListNoPlayInfo() throws Throwable {
        // arrange
        JSONArray array = new JSONArray();
        JSONObject obj = new JSONObject();
        array.add(obj);
        // act
        List<PlayActivityModel> result = PlayCenterWrapper.buildPlayActivityModelList(array);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertNull(result.get(0).getPlayInfo());
    }

    /**
     * Test case: array contains valid object with complete playInfo
     * Expected: return list with one element containing valid playInfo
     */
    @Test
    public void testBuildPlayActivityModelListWithPlayInfo() throws Throwable {
        // arrange
        JSONArray array = new JSONArray();
        JSONObject obj = new JSONObject();
        JSONObject playInfo = new JSONObject();
        playInfo.put("title", "test");
        playInfo.put("subTitle", "subTest");
        playInfo.put("activityId", 1L);
        playInfo.put("startTime", 1000L);
        playInfo.put("endTime", 2000L);
        playInfo.put("materialInfoList", new JSONArray());
        playInfo.put("taskInfoList", new JSONArray());
        obj.put("playInfo", playInfo);
        array.add(obj);
        // act
        List<PlayActivityModel> result = PlayCenterWrapper.buildPlayActivityModelList(array);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertNotNull(result.get(0).getPlayInfo());
        assertEquals("test", result.get(0).getPlayInfo().getTitle());
        assertEquals("subTest", result.get(0).getPlayInfo().getSubTitle());
        assertEquals(1L, result.get(0).getPlayInfo().getActivityId());
        assertEquals(1000L, result.get(0).getPlayInfo().getStartTime());
        assertEquals(2000L, result.get(0).getPlayInfo().getEndTime());
        assertNotNull(result.get(0).getPlayInfo().getMaterialInfoList());
        assertNotNull(result.get(0).getPlayInfo().getTaskInfoList());
    }

    /**
     * Test when input playInfo is null
     */
    @Test
    public void testBuildPlayInfo_NullInput() {
        // arrange
        JSONObject playInfo = null;
        // act
        PlayInfoModel result = PlayCenterWrapper.buildPlayInfo(playInfo);
        // assert
        assertNull(result);
    }

    /**
     * Test with complete valid input data
     */
    @Test
    public void testBuildPlayInfo_CompleteValidData() {
        // arrange
        JSONObject playInfo = new JSONObject();
        JSONArray materialInfoList = new JSONArray();
        JSONObject materialInfo = new JSONObject();
        materialInfo.put("fieldKey", "testKey");
        materialInfo.put("fieldValue", "testValue");
        materialInfoList.add(materialInfo);
        JSONArray taskInfoList = new JSONArray();
        JSONObject taskInfo = new JSONObject();
        taskInfo.put("prizeCount", 1);
        taskInfo.put("status", 1);
        taskInfo.put("prizeInfoList", new JSONArray());
        taskInfoList.add(taskInfo);
        when(mockPlayInfo.getString("title")).thenReturn("Test Title");
        when(mockPlayInfo.getString("subTitle")).thenReturn("Test Subtitle");
        when(mockPlayInfo.getLong("startTime")).thenReturn(1625097600000L);
        when(mockPlayInfo.getLong("endTime")).thenReturn(1625184000000L);
        when(mockPlayInfo.getLong("activityId")).thenReturn(12345L);
        when(mockPlayInfo.getJSONArray("materialInfoList")).thenReturn(materialInfoList);
        when(mockPlayInfo.getJSONArray("taskInfoList")).thenReturn(taskInfoList);
        // act
        PlayInfoModel result = PlayCenterWrapper.buildPlayInfo(mockPlayInfo);
        // assert
        assertNotNull(result);
        assertEquals("Test Title", result.getTitle());
        assertEquals("Test Subtitle", result.getSubTitle());
        assertEquals(1625097600000L, result.getStartTime());
        assertEquals(1625184000000L, result.getEndTime());
        assertEquals(12345L, result.getActivityId());
        List<MaterialInfoModel> resultMaterialList = result.getMaterialInfoList();
        assertNotNull(resultMaterialList);
        assertEquals(1, resultMaterialList.size());
        assertEquals("testKey", resultMaterialList.get(0).getFieldKey());
        assertEquals("testValue", resultMaterialList.get(0).getFieldValue());
        List<TaskInfoModel> resultTaskList = result.getTaskInfoList();
        assertNotNull(resultTaskList);
        assertEquals(1, resultTaskList.size());
        assertEquals(1, resultTaskList.get(0).getPrizeCount());
        assertEquals(1, resultTaskList.get(0).getStatus());
    }

    /**
     * Test with empty arrays for materialInfoList and taskInfoList
     */
    @Test
    public void testBuildPlayInfo_EmptyArrays() {
        // arrange
        when(mockPlayInfo.getString("title")).thenReturn("Test Title");
        when(mockPlayInfo.getString("subTitle")).thenReturn("Test Subtitle");
        when(mockPlayInfo.getLong("startTime")).thenReturn(1625097600000L);
        when(mockPlayInfo.getLong("endTime")).thenReturn(1625184000000L);
        when(mockPlayInfo.getLong("activityId")).thenReturn(12345L);
        when(mockPlayInfo.getJSONArray("materialInfoList")).thenReturn(new JSONArray());
        when(mockPlayInfo.getJSONArray("taskInfoList")).thenReturn(new JSONArray());
        // act
        PlayInfoModel result = PlayCenterWrapper.buildPlayInfo(mockPlayInfo);
        // assert
        assertNotNull(result);
        assertEquals("Test Title", result.getTitle());
        assertEquals("Test Subtitle", result.getSubTitle());
        assertNotNull(result.getMaterialInfoList());
        assertTrue(result.getMaterialInfoList().isEmpty());
        assertNotNull(result.getTaskInfoList());
        assertTrue(result.getTaskInfoList().isEmpty());
    }

    /**
     * Test with null values for optional fields
     */
    @Test
    public void testBuildPlayInfo_NullOptionalFields() {
        // arrange
        when(mockPlayInfo.getString("title")).thenReturn(null);
        when(mockPlayInfo.getString("subTitle")).thenReturn(null);
        when(mockPlayInfo.getLong("startTime")).thenReturn(1625097600000L);
        when(mockPlayInfo.getLong("endTime")).thenReturn(1625184000000L);
        when(mockPlayInfo.getLong("activityId")).thenReturn(12345L);
        when(mockPlayInfo.getJSONArray("materialInfoList")).thenReturn(null);
        when(mockPlayInfo.getJSONArray("taskInfoList")).thenReturn(null);
        // act
        PlayInfoModel result = PlayCenterWrapper.buildPlayInfo(mockPlayInfo);
        // assert
        assertNotNull(result);
        assertNull(result.getTitle());
        assertNull(result.getSubTitle());
        assertNotNull(result.getMaterialInfoList());
        assertTrue(result.getMaterialInfoList().isEmpty());
        assertNotNull(result.getTaskInfoList());
        assertTrue(result.getTaskInfoList().isEmpty());
    }

    /**
     * Test with malformed JSONArray for materialInfoList
     */
    @Test
    public void testBuildPlayInfo_MalformedMaterialInfoList() {
        // arrange
        JSONArray malformedMaterialInfoList = new JSONArray();
        JSONObject invalidMaterialInfo = new JSONObject();
        // Missing required fields
        malformedMaterialInfoList.add(invalidMaterialInfo);
        when(mockPlayInfo.getString("title")).thenReturn("Test Title");
        when(mockPlayInfo.getString("subTitle")).thenReturn("Test Subtitle");
        when(mockPlayInfo.getLong("startTime")).thenReturn(1625097600000L);
        when(mockPlayInfo.getLong("endTime")).thenReturn(1625184000000L);
        when(mockPlayInfo.getLong("activityId")).thenReturn(12345L);
        when(mockPlayInfo.getJSONArray("materialInfoList")).thenReturn(malformedMaterialInfoList);
        when(mockPlayInfo.getJSONArray("taskInfoList")).thenReturn(new JSONArray());
        // act
        PlayInfoModel result = PlayCenterWrapper.buildPlayInfo(mockPlayInfo);
        // assert
        assertNotNull(result);
        assertNotNull(result.getMaterialInfoList());
        assertEquals(1, result.getMaterialInfoList().size());
        assertNull(result.getMaterialInfoList().get(0).getFieldKey());
        assertNull(result.getMaterialInfoList().get(0).getFieldValue());
    }

    /**
     * Test verification of method calls
     */
    @Test
    public void testBuildPlayInfo_VerifyMethodCalls() {
        // arrange
        when(mockPlayInfo.getString("title")).thenReturn("Test Title");
        when(mockPlayInfo.getString("subTitle")).thenReturn("Test Subtitle");
        when(mockPlayInfo.getLong("startTime")).thenReturn(1625097600000L);
        when(mockPlayInfo.getLong("endTime")).thenReturn(1625184000000L);
        when(mockPlayInfo.getLong("activityId")).thenReturn(12345L);
        when(mockPlayInfo.getJSONArray("materialInfoList")).thenReturn(new JSONArray());
        when(mockPlayInfo.getJSONArray("taskInfoList")).thenReturn(new JSONArray());
        // act
        PlayInfoModel result = PlayCenterWrapper.buildPlayInfo(mockPlayInfo);
        // assert
        verify(mockPlayInfo).getString("title");
        verify(mockPlayInfo).getString("subTitle");
        verify(mockPlayInfo).getLong("startTime");
        verify(mockPlayInfo).getLong("endTime");
        verify(mockPlayInfo).getLong("activityId");
        verify(mockPlayInfo).getJSONArray("materialInfoList");
        verify(mockPlayInfo).getJSONArray("taskInfoList");
    }

    /**
     * Test convertPlayActivityModelMap with empty input
     */
    @Test
    public void testConvertPlayActivityModelMapEmptyInput() throws Throwable {
        // arrange
        String emptyInput = "";
        // act
        Map<Long, List<PlayActivityModel>> result = PlayCenterWrapper.convertPlayActivityModelMap(emptyInput);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test convertPlayActivityModelMap with null input
     */
    @Test
    public void testConvertPlayActivityModelMapNullInput() throws Throwable {
        // arrange
        String nullInput = null;
        // act
        Map<Long, List<PlayActivityModel>> result = PlayCenterWrapper.convertPlayActivityModelMap(nullInput);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test convertPlayActivityModelMap with valid input containing a single key
     */
    @Test
    public void testConvertPlayActivityModelMapValidSingleKey() throws Throwable {
        // arrange
        String input = "{\"123\":[{\"playInfo\":{" + "\"title\":\"Test Title\"," + "\"subTitle\":\"Test Subtitle\"," + "\"startTime\":1625097600000," + "\"endTime\":1625184000000," + "\"activityId\":456," + "\"materialInfoList\":[]," + "\"taskInfoList\":[]" + "}}]}";
        // act
        Map<Long, List<PlayActivityModel>> result = PlayCenterWrapper.convertPlayActivityModelMap(input);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertTrue(result.containsKey(123L));
        List<PlayActivityModel> models = result.get(123L);
        assertNotNull(models);
        assertEquals(1, models.size());
        PlayActivityModel model = models.get(0);
        assertNotNull(model.getPlayInfo());
        assertEquals("Test Title", model.getPlayInfo().getTitle());
        assertEquals("Test Subtitle", model.getPlayInfo().getSubTitle());
        assertEquals(456L, model.getPlayInfo().getActivityId());
    }

    /**
     * Test convertPlayActivityModelMap with valid input containing multiple keys
     */
    @Test
    public void testConvertPlayActivityModelMapValidMultipleKeys() throws Throwable {
        // arrange
        String input = "{" + "\"123\":[{\"playInfo\":{" + "\"title\":\"Title 1\"," + "\"subTitle\":\"Subtitle 1\"," + "\"startTime\":1625097600000," + "\"endTime\":1625184000000," + "\"activityId\":456," + "\"materialInfoList\":[]," + "\"taskInfoList\":[]" + "}}]," + "\"789\":[{\"playInfo\":{" + "\"title\":\"Title 2\"," + "\"subTitle\":\"Subtitle 2\"," + "\"startTime\":1625097600000," + "\"endTime\":1625184000000," + "\"activityId\":101112," + "\"materialInfoList\":[]," + "\"taskInfoList\":[]" + "}}]" + "}";
        // act
        Map<Long, List<PlayActivityModel>> result = PlayCenterWrapper.convertPlayActivityModelMap(input);
        // assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.containsKey(123L));
        assertTrue(result.containsKey(789L));
        List<PlayActivityModel> models1 = result.get(123L);
        assertNotNull(models1);
        assertEquals(1, models1.size());
        assertEquals("Title 1", models1.get(0).getPlayInfo().getTitle());
        assertEquals(456L, models1.get(0).getPlayInfo().getActivityId());
        List<PlayActivityModel> models2 = result.get(789L);
        assertNotNull(models2);
        assertEquals(1, models2.size());
        assertEquals("Title 2", models2.get(0).getPlayInfo().getTitle());
        assertEquals(101112L, models2.get(0).getPlayInfo().getActivityId());
    }

    /**
     * Test convertPlayActivityModelMap with invalid JSON input
     */
    @Test
    public void testConvertPlayActivityModelMapInvalidJson() throws Throwable {
        // arrange
        String invalidInput = "{invalid json}";
        // act
        Map<Long, List<PlayActivityModel>> result = PlayCenterWrapper.convertPlayActivityModelMap(invalidInput);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test scenario: Input JSONObject is null
     * Expected: Returns null
     */
    @Test
    public void testBuildTaskInfo_WhenInputNull_ShouldReturnNull() throws Throwable {
        // arrange
        JSONObject nullTaskInfoObj = null;
        // act
        TaskInfoModel result = PlayCenterWrapper.buildTaskInfo(nullTaskInfoObj);
        // assert
        assertNull(result);
    }

    /**
     * Test scenario: All fields in JSONObject are null
     * Expected: Returns TaskInfoModel with null/empty values
     */
    @Test
    public void testBuildTaskInfo_WhenAllFieldsNull_ShouldReturnEmptyModel() throws Throwable {
        // arrange
        when(mockPlayInfo.getInteger("prizeCount")).thenReturn(0);
        when(mockPlayInfo.getLong("taskEndTime")).thenReturn(null);
        when(mockPlayInfo.getJSONArray("prizeInfoList")).thenReturn(null);
        // act
        TaskInfoModel result = PlayCenterWrapper.buildTaskInfo(mockPlayInfo);
        // assert
        assertNotNull(result);
        assertEquals(0, result.getPrizeCount());
        assertNull(result.getTaskEndTime());
        assertNotNull(result.getPrizeInfoList());
        assertTrue(result.getPrizeInfoList().isEmpty());
    }

    /**
     * Test scenario: JSONObject contains only basic fields without prizeInfoList
     * Expected: Returns TaskInfoModel with basic fields populated and empty prizeInfoList
     */
    @Test
    public void testBuildTaskInfo_WhenBasicFieldsOnly_ShouldReturnModelWithBasicFields() throws Throwable {
        // arrange
        JSONObject taskInfoObj = new JSONObject();
        taskInfoObj.put("prizeCount", 5);
        taskInfoObj.put("taskEndTime", 1234567890L);
        // act
        TaskInfoModel result = PlayCenterWrapper.buildTaskInfo(taskInfoObj);
        // assert
        assertNotNull(result);
        assertEquals(5, result.getPrizeCount());
        assertEquals(1234567890L, result.getTaskEndTime());
        assertNotNull(result.getPrizeInfoList());
        assertTrue(result.getPrizeInfoList().isEmpty());
    }

    /**
     * Test scenario: JSONObject contains complete valid data
     * Expected: Returns fully populated TaskInfoModel
     */
    @Test
    public void testBuildTaskInfo_WhenCompleteValidData_ShouldReturnFullModel() throws Throwable {
        // arrange
        JSONObject taskInfoObj = new JSONObject();
        taskInfoObj.put("prizeCount", 3);
        taskInfoObj.put("taskEndTime", 1234567890L);
        JSONArray prizeInfoArray = new JSONArray();
        JSONObject prize = new JSONObject();
        prize.put("prizeName", "Test Prize");
        prize.put("image", "test.jpg");
        prizeInfoArray.add(prize);
        taskInfoObj.put("prizeInfoList", prizeInfoArray);
        // act
        TaskInfoModel result = PlayCenterWrapper.buildTaskInfo(taskInfoObj);
        // assert
        assertNotNull(result);
        assertEquals(3, result.getPrizeCount());
        assertEquals(1234567890L, result.getTaskEndTime());
        assertNotNull(result.getPrizeInfoList());
        assertEquals(1, result.getPrizeInfoList().size());
        assertEquals("Test Prize", result.getPrizeInfoList().get(0).getPrizeName());
        assertEquals("test.jpg", result.getPrizeInfoList().get(0).getPrizeImage());
    }

    /**
     * Test scenario: JSONObject contains empty prizeInfoList
     * Expected: Returns TaskInfoModel with empty prizeInfoList
     */
    @Test
    public void testBuildTaskInfo_WhenEmptyPrizeInfoList_ShouldReturnModelWithEmptyList() throws Throwable {
        // arrange
        JSONObject taskInfoObj = new JSONObject();
        taskInfoObj.put("prizeCount", 1);
        taskInfoObj.put("taskEndTime", 1234567890L);
        taskInfoObj.put("prizeInfoList", new JSONArray());
        // act
        TaskInfoModel result = PlayCenterWrapper.buildTaskInfo(taskInfoObj);
        // assert
        assertNotNull(result);
        assertEquals(1, result.getPrizeCount());
        assertEquals(1234567890L, result.getTaskEndTime());
        assertNotNull(result.getPrizeInfoList());
        assertTrue(result.getPrizeInfoList().isEmpty());
    }

    /**
     * Test formatDate with very large timestamp
     * Expected: Should return date in correct format without throwing exception
     */
    @Test
    @DisplayName("Format date with large timestamp should maintain format")
    public void testFormatDateLargeTimestamp() throws Throwable {
        // arrange
        // Large but valid timestamp
        // Adjusted to a more manageable large timestamp
        long timestamp = 1000000000000L;
        // act
        String result = PlayCenterWrapper.formatDate(timestamp);
        // assert
        assertNotNull(result, "Formatted date should not be null");
        assertTrue(result.matches("\\d{4}\\.\\d{2}\\.\\d{2}"), "Formatted date should match pattern yyyy.MM.dd");
    }

    /**
     * Test formatDate with a specific timestamp
     * Expected: Should return date in format "yyyy.MM.dd"
     */
    @Test
    @DisplayName("Format date with specific timestamp should return correct format")
    public void testFormatDateSpecificTimestamp() throws Throwable {
        // arrange
        // 2023.01.01
        long timestamp = 1672531200000L;
        String expected = "2023.01.01";
        // act
        String result = PlayCenterWrapper.formatDate(timestamp);
        // assert
        assertEquals(expected, result, "Formatted date should match expected format");
    }

    /**
     * Test formatDate with epoch timestamp (0)
     * Expected: Should return "1970.01.01"
     */
    @Test
    @DisplayName("Format date with epoch timestamp should return 1970.01.01")
    public void testFormatDateEpochTimestamp() throws Throwable {
        // arrange
        long timestamp = 0L;
        String expected = "1970.01.01";
        // act
        String result = PlayCenterWrapper.formatDate(timestamp);
        // assert
        assertEquals(expected, result, "Epoch timestamp should format to1970.01.01");
    }

    /**
     * Test formatDate with current timestamp
     * Expected: Should return today's date in correct format
     */
    @Test
    @DisplayName("Format date with current timestamp should match expected format")
    public void testFormatDateCurrentTimestamp() throws Throwable {
        // arrange
        long timestamp = System.currentTimeMillis();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy.MM.dd");
        String expected = sdf.format(new Date(timestamp));
        // act
        String result = PlayCenterWrapper.formatDate(timestamp);
        // assert
        assertEquals(expected, result, "Current timestamp should format to today's date");
    }

    /**
     * Test formatDate with future timestamp
     * Expected: Should return future date in correct format
     */
    @Test
    @DisplayName("Format date with future timestamp should return correct format")
    public void testFormatDateFutureTimestamp() throws Throwable {
        // arrange
        // tomorrow
        long timestamp = System.currentTimeMillis() + 86400000L;
        String result = PlayCenterWrapper.formatDate(timestamp);
        // assert
        assertNotNull(result, "Formatted date should not be null");
        assertTrue(result.matches("\\d{4}\\.\\d{2}\\.\\d{2}"), "Formatted date should match pattern yyyy.MM.dd");
    }

    /**
     * Test formatDate with past timestamp
     * Expected: Should return past date in correct format
     */
    @Test
    @DisplayName("Format date with past timestamp should return correct format")
    public void testFormatDatePastTimestamp() throws Throwable {
        // arrange
        // 2020.01.01
        long timestamp = 1577836800000L;
        String expected = "2020.01.01";
        // act
        String result = PlayCenterWrapper.formatDate(timestamp);
        // assert
        assertEquals(expected, result, "Past timestamp should format correctly");
    }
}
