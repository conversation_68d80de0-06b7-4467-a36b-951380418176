package com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr;

import com.alibaba.fastjson.JSON;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import lombok.EqualsAndHashCode;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * @Author: guangyujie
 * @Date: 2025/2/5 17:13
 */
@EqualsAndHashCode(callSuper = true)
public class ProductAttr extends FetcherReturnValueDTO {

    public ProductAttr(Map<String, AttrDTO> skuAttr) {
        this.skuAttr = skuAttr;
    }

    /**
     * attrName,attrValue
     */
    private final Map<String, AttrDTO> skuAttr;

    public Optional<AttrDTO> getProductAttr(String attrName) {
        return Optional.ofNullable(skuAttr).map(map -> map.get(attrName));
    }

    public List<String> getProductAttrValue(String attrName) {
        Optional<AttrDTO> optionalAttrDTO = getProductAttr(attrName);
        return optionalAttrDTO.map(AttrDTO::getValue).orElse(null);
    }

    public String getProductAttrValueJson(String attrName) {
        return getProductAttr(attrName)
                .map(AttrDTO::getValue)
                .filter(CollectionUtils::isNotEmpty)
                .map(JSON::toJSONString)
                .orElse(null);
    }

    public String getProductAttrFirstValue(String attrName) {
        return getProductAttr(attrName)
                .map(AttrDTO::getValue)
                .filter(CollectionUtils::isNotEmpty)
                .map(list -> list.get(0))
                .orElse(null);
    }

}
