package com.sankuai.dzshoppingguide.product.detail.application.utils.magic;

import static com.sankuai.dz.product.detail.RequestCustomParamEnum.*;
import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;
import com.sankuai.dz.product.detail.gateway.spi.dto.CustomParam;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class MagicFlagUtilsGetMmcBuyTest {

    @Mock
    private ProductDetailPageRequest pageRequest;

    @Mock
    private CustomParam customParam;

    /**
     * Test getMmcBuy method when ProductDetailPageRequest object is null.
     * Since the original method does not handle null inputs, this test case is expected to throw a NullPointerException.
     * This is a known limitation based on the current implementation of getMmcBuy.
     */
    @Test(expected = NullPointerException.class)
    public void testGetMmcBuyWhenPageRequestIsNull() throws Throwable {
        // arrange
        ProductDetailPageRequest pageRequest = null;
        // act
        String result = MagicFlagUtils.getMmcBuy(pageRequest);
        // assert is handled by the expected exception
    }

    /**
     * Test getMmcBuy method when mmcbuy parameter is null.
     */
    @Test
    public void testGetMmcBuyWhenMmcBuyIsNull() throws Throwable {
        // arrange
        when(pageRequest.getCustomParam(mmcbuy)).thenReturn(null);
        // act
        String result = MagicFlagUtils.getMmcBuy(pageRequest);
        // assert
        assertEquals("0", result);
    }

    /**
     * Test getMmcBuy method when mmcbuy parameter cannot be converted to a number.
     */
    @Test
    public void testGetMmcBuyWhenMmcBuyIsNotCreatable() throws Throwable {
        // arrange
        when(pageRequest.getCustomParam(mmcbuy)).thenReturn("notCreatable");
        // act
        String result = MagicFlagUtils.getMmcBuy(pageRequest);
        // assert
        assertEquals("0", result);
    }

    /**
     * Test getMmcBuy method when mmcbuy parameter can be converted to a number.
     */
    @Test
    public void testGetMmcBuyWhenMmcBuyIsCreatable() throws Throwable {
        // arrange
        when(pageRequest.getCustomParam(mmcbuy)).thenReturn("123");
        // act
        String result = MagicFlagUtils.getMmcBuy(pageRequest);
        // assert
        assertEquals("123", result);
    }
}
