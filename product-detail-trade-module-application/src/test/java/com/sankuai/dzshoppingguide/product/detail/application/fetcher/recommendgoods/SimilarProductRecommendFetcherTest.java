package com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommendgoods;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;
import com.sankuai.dz.product.detail.RequestCustomParamEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import java.lang.reflect.Field;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.*;

class SimilarProductRecommendFetcherTest {

    private SimilarProductRecommendFetcher fetcher;

    private ProductDetailPageRequest mockRequest;

    @BeforeEach
    void setUp() throws Exception {
        fetcher = new SimilarProductRecommendFetcher();
        mockRequest = Mockito.mock(ProductDetailPageRequest.class);
        // 使用递归查找字段的方法
        setFieldValue(fetcher, "request", mockRequest);
    }

    /**
     * 递归查找并设置字段值的辅助方法
     */
    private void setFieldValue(Object target, String fieldName, Object value) throws Exception {
        Class<?> clazz = target.getClass();
        Field field = null;
        // 递归查找字段，包括所有父类
        while (clazz != null && field == null) {
            try {
                field = clazz.getDeclaredField(fieldName);
            } catch (NoSuchFieldException e) {
                clazz = clazz.getSuperclass();
            }
        }
        if (field != null) {
            field.setAccessible(true);
            field.set(target, value);
        } else {
            throw new NoSuchFieldException(fieldName);
        }
    }

    /**
     * Test normal case with all parameters available
     */
    @Test
    public void testBuildBizParamsNormalCase() throws Throwable {
        // arrange
        when(mockRequest.getProductId()).thenReturn(12345L);
        when(mockRequest.getPoiId()).thenReturn(67890L);
        when(mockRequest.getCustomParam(RequestCustomParamEnum.joinNum)).thenReturn("3");
        // act
        Map<String, Object> result = fetcher.buildBizParams();
        // assert
        assertNotNull(result);
        assertEquals(7, result.size());
        assertEquals(12345L, result.get("productTagIds"));
        assertEquals("display", result.get("applyType"));
        assertEquals(12345L, result.get("productId"));
        assertEquals(67890L, result.get("shopId"));
        assertEquals(3, result.get("restPersons"));
        assertEquals("001", result.get("flowFlag"));
        assertEquals("1001", result.get("productType"));
    }

    /**
     * Test case when joinNum custom param is missing
     */
    @Test
    public void testBuildBizParamsMissingJoinNum() throws Throwable {
        // arrange
        when(mockRequest.getProductId()).thenReturn(12345L);
        when(mockRequest.getPoiId()).thenReturn(67890L);
        when(mockRequest.getCustomParam(RequestCustomParamEnum.joinNum)).thenReturn(null);
        // act
        Map<String, Object> result = fetcher.buildBizParams();
        // assert
        assertNotNull(result);
        assertEquals(7, result.size());
        // default value
        assertEquals(1, result.get("restPersons"));
    }

    /**
     * Test case when joinNum string is empty
     */
    @Test
    public void testBuildBizParamsEmptyJoinNum() throws Throwable {
        // arrange
        when(mockRequest.getProductId()).thenReturn(12345L);
        when(mockRequest.getPoiId()).thenReturn(67890L);
        when(mockRequest.getCustomParam(RequestCustomParamEnum.joinNum)).thenReturn("");
        // act
        Map<String, Object> result = fetcher.buildBizParams();
        // assert
        assertNotNull(result);
        assertEquals(7, result.size());
        // default value
        assertEquals(1, result.get("restPersons"));
    }

    /**
     * Test case when joinNum string is invalid (non-numeric)
     */
    @Test
    public void testBuildBizParamsInvalidJoinNum() throws Throwable {
        // arrange
        when(mockRequest.getProductId()).thenReturn(12345L);
        when(mockRequest.getPoiId()).thenReturn(67890L);
        when(mockRequest.getCustomParam(RequestCustomParamEnum.joinNum)).thenReturn("abc");
        // act & assert
        assertThrows(NumberFormatException.class, () -> fetcher.buildBizParams());
    }

    /**
     * Test case when request is null
     */
    @Test
    public void testBuildBizParamsNullRequest() throws Throwable {
        // arrange
        setFieldValue(fetcher, "request", null);
        // act & assert
        assertThrows(NullPointerException.class, () -> fetcher.buildBizParams());
    }
}
