package com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommend.dto;


import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Map;

/**
 * @Author: zhangyuan103
 * @Date: 2025/5/26
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
public class Dp2MtShopIdMapperReturnValue extends FetcherReturnValueDTO {
    private Map<Long, List<Long>> dpMtShopIds;
}
