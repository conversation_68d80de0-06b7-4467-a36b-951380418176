package com.sankuai.dzshoppingguide.product.detail.application.fetcher.creditpay;

import com.dianping.lion.client.Lion;
import com.dianping.vc.sdk.lang.NumberUtils;
import com.google.common.collect.Maps;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftClient;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.enums.MobileOSTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.abtest.AbTestFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.abtest.AbTestReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategoryFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.cityId.CityIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.cityId.CityIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.best.shop.ShopInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.best.shop.ShopInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttrFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.utils.TimesDealUtil;
import com.sankuai.dzshoppingguide.product.detail.domain.utils.LionConfigUtils;
import com.sankuai.dzshoppingguide.product.detail.domain.utils.ThriftAsyncUtils;
import com.sankuai.fincreditpay.bnpl.client.access.thrift.IBNPLAccessThriftService;
import com.sankuai.fincreditpay.bnpl.client.access.thrift.dto.BNPLExposureDTO;
import com.sankuai.fincreditpay.bnpl.client.access.thrift.dto.DeviceInfoDTO;
import com.sankuai.fincreditpay.bnpl.client.access.thrift.dto.ExposureScenarioDTO;
import com.sankuai.fincreditpay.bnpl.client.access.thrift.dto.ProductSignDTO;
import com.sankuai.fincreditpay.bnpl.client.access.thrift.enums.BizIdFieldMeaning;
import com.sankuai.fincreditpay.bnpl.client.access.thrift.enums.ExposureEnum;
import com.sankuai.fincreditpay.bnpl.client.access.thrift.request.BNPLExposureRequest;
import com.sankuai.fincreditpay.bnpl.client.access.thrift.response.BNPLExposureResponse;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @date 2025/6/12 19:58
 */
@Fetcher(
        previousLayerDependencies = {AbTestFetcher.class, ProductBaseInfoFetcher.class, ProductCategoryFetcher.class, CityIdMapperFetcher.class, ShopInfoFetcher.class, ProductAttrFetcher.class}
)
@Slf4j
public class UserAndProductCreditPayFetcher extends NormalFetcherContext<ExposeResult> {

    private static final String USER_CREDIT_PAY_CONFIG = "deal.shelf.user.credit.pay.config";
    private static final String USE_CASE = "OPEN";
    private static final String BIZ_ID = "bizId";
    private static final String PLAN_ID = "planId";
    private static final String SIGN_IPH_PAY_MERCHANT_NO = "signIphPayMerchantNo";

    @MdpThriftClient(timeout = 1000, testTimeout = 5000, remoteAppKey = "com.sankuai.fincreditpay.bnpl",async = true)
    private IBNPLAccessThriftService bnplAccessThriftService;

    @Override
    protected CompletableFuture<ExposeResult> doFetch() throws Exception {
        ProductBaseInfo productBaseInfo = getDependencyResult(ProductBaseInfoFetcher.class);
        ProductCategory productCategory = getDependencyResult(ProductCategoryFetcher.class);
        AbTestReturnValue abTestReturnValue = getDependencyResult(AbTestFetcher.class);
        String expResult = abTestReturnValue.getAbTestExpResult("MtCreditPayExp");
        boolean isHit = StringUtils.equals("c",expResult);
        int categoryId = Optional.ofNullable(productCategory).map(ProductCategory::getProductSecondCategoryId).orElse(0);

        List<Integer> productCreditPayCategories = LionConfigUtils.getProductCreditPayCategories();
        // 前置判断条件
        boolean isMt = request.getClientTypeEnum().isMtClientType();
        long mtUserId = request.getMtUserId();

        // 获取商品是否可用属性
        // String payMethod = productAttr.getProductAttrFirstValue("pay_method");
        // boolean productSupport = StringUtils.equals("4", payMethod);

        // 是团购次卡 && 美团侧 && 类目准入 && userId不为空 && 命中实验 && 商品支持先用后付
        boolean allowEntry = TimesDealUtil.isTimesDeal(productBaseInfo) && isMt && productCreditPayCategories.contains(categoryId) && mtUserId > 0 && isHit;

        if (!allowEntry) {
            return CompletableFuture.completedFuture(null);
        }

        bnplAccessThriftService.exposure(buildBNPLExposureRequest());
        return ThriftAsyncUtils.<BNPLExposureResponse>getThriftFuture().thenApply(response -> {
            if (response == null || !response.isSuccess() || response.getData() == null) {
                return null;
            }
            BNPLExposureDTO data = response.getData();
            String exposureResult = Optional.ofNullable(data).map(BNPLExposureDTO::getExposure).orElse(StringUtils.EMPTY);
            return ExposeResult.builder().supportExpose(StringUtils.equals(ExposureEnum.EXPOSED.getCode(), exposureResult)).build();
        });
    }


    private BNPLExposureRequest buildBNPLExposureRequest() {
        boolean isMt = request.getClientTypeEnum().isMtClientType();

        BNPLExposureRequest bnplExposureRequest = new BNPLExposureRequest();
        ExposureScenarioDTO exposureScenarioDTO = new ExposureScenarioDTO();
        // 业务ID字段含义
        exposureScenarioDTO.setBizIdFieldMeaning(BizIdFieldMeaning.PLAN_ID.getValue());

        // 用例 OPEN：预览准入； OPEN_ACCOUNT：开通准入； COMMIT_ORDER：提单页准入
        exposureScenarioDTO.setUseCase(USE_CASE);
        // 丽人次卡：BeauCard 教育次卡：EduCard 按摩足疗次卡:SpaCard
        exposureScenarioDTO.setSubBizScenario(getSubBizScenario());
        bnplExposureRequest.setExposureScenario(exposureScenarioDTO);

        bnplExposureRequest.setUserId(request.getUserId());
        ProductSignDTO productSignDTO = new ProductSignDTO();
        UserCreditPayConfig userCreditPayConfig = getUserCreditPayConfig();
        if (userCreditPayConfig != null) {
            // 业务ID值 金服提供
            exposureScenarioDTO.setBizId(userCreditPayConfig.getBizId());
            // 先用后付模版ID 金服提供
            productSignDTO.setPlanId(userCreditPayConfig.getPlanId());
            // 签约商户号 金服提供
            productSignDTO.setSignIphPayMerchantNo(userCreditPayConfig.getSignPayMerchantNo());
        }
        bnplExposureRequest.setProductSignInfo(productSignDTO);
        DeviceInfoDTO deviceInfoDTO = new DeviceInfoDTO();
        deviceInfoDTO.setUuid(request.getShepherdGatewayParam().getDeviceId());
        // 平台标识
        MobileOSTypeEnum mobileOSType = request.getMobileOSType();
        
        deviceInfoDTO.setPlatform(Objects.equals(MobileOSTypeEnum.IOS,mobileOSType) ? "iphone" : "android");
        // 客户端标识
        deviceInfoDTO.setApp(getApp());
        deviceInfoDTO.setLocation(getLocation());


        deviceInfoDTO.setCityId((long)getCityId4P());

        deviceInfoDTO.setAppVersion(request.getShepherdGatewayParam().getAppVersion());
        bnplExposureRequest.setDeviceInfo(deviceInfoDTO);
        return bnplExposureRequest;
    }

    private int getCityId4P() {
        boolean isMt = request.getClientTypeEnum().isMtClientType();
        CityIdMapper cityIdMapper = getDependencyResult(CityIdMapperFetcher.class);
        return isMt ? Optional.ofNullable(cityIdMapper).map(CityIdMapper::getMtCityId).orElse(0) :
                Optional.ofNullable(cityIdMapper).map(CityIdMapper::getDpCityId).orElse(0);
    }

    private String getSubBizScenario() {
        ShopInfo shopInfo = getDependencyResult(ShopInfoFetcher.class);
        // todo 这个地方要实际和团详主项目里面的bestshop里面的shoptype进行比对
        int shopType = Optional.ofNullable(shopInfo).map(ShopInfo::getDpPoiDTO).map(DpPoiDTO::getShopType).orElse(0);
        if (shopType == 50) {
            return "BeauCard";
        } else if (shopType == 30) {
            return "SpaCard";
        } else if (shopType == 75) {
            return "EduCard";
        }
        return null;
    }

    private String getLocation() {

        double userLat = request.getUserLat();
        double userLng = request.getUserLng();
        return userLat + "_" + userLng;
    }

    private String getApp() {
        ClientTypeEnum clientTypeEnum = request.getClientTypeEnum();
        boolean mainApp = clientTypeEnum.isInApp();
        boolean mt = request.getClientTypeEnum().isMtClientType();
        if (mt) {
            if (mainApp) {
                return "group";
            } else {
                return "weixin";
            }
        } else {
            if (mainApp) {
                return "dianping-nova";
            }
        }
        return null;
    }

    @Data
    static class UserCreditPayConfig {
        private String bizId;
        private Long planId;
        private Long signPayMerchantNo;
    }

    private UserCreditPayConfig getUserCreditPayConfig() {
        try {
            Map<String, String> configMap = Lion.getMap("com.sankuai.dzviewscene.dealshelf", USER_CREDIT_PAY_CONFIG,
                    String.class, Maps.newHashMap());
            String bizIdStr = configMap.getOrDefault(BIZ_ID, StringUtils.EMPTY);
            String planIdStr = configMap.getOrDefault(PLAN_ID, StringUtils.EMPTY);
            String signPayStr = configMap.getOrDefault(SIGN_IPH_PAY_MERCHANT_NO, StringUtils.EMPTY);
            UserCreditPayConfig config = new UserCreditPayConfig();
            config.setBizId(bizIdStr);
            config.setPlanId(NumberUtils.toLong(planIdStr, 0));
            config.setSignPayMerchantNo(NumberUtils.toLong(signPayStr, 0));
            return config;
        } catch (Exception e) {
            log.error("getUserCreditPayConfig error", e);
            return null;
        }
    }
}
