package com.sankuai.dzshoppingguide.product.detail.application.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.dianping.deal.sales.common.datatype.IResponse;
import com.dianping.pigeon.remoting.invoker.concurrent.InvocationCallback;
import com.dianping.pigeon.remoting.invoker.util.InvokerHelper;
import com.dianping.spc.common.Response;
import com.dianping.tgc.open.entity.BatchExProxyCouponRequest;
import com.dianping.tgc.open.entity.BatchExProxyCouponResponseDTO;
import com.dianping.tgc.open.v2.TGCGetCouponComponentQueryService;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonClient;
import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
import com.sankuai.dzshoppingguide.product.detail.domain.utils.PigeonCallbackUtils;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResponse;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResult;
import com.sankuai.general.product.query.center.client.service.DealGroupQueryService;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import org.apache.thrift.TException;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;

@ExtendWith(MockitoExtension.class)
class CompositeAtomServiceImplGetProductBaseInfoByUnifiedIdTest {

    private final DealGroupQueryService dealGroupQueryServiceAtom = mock(DealGroupQueryService.class);

    private final Logger log = mock(Logger.class);

    private final CompositeAtomServiceImpl service;

    @Mock
    private TGCGetCouponComponentQueryService couponComponentQueryServiceFuture;

    @InjectMocks
    private CompositeAtomServiceImpl compositeAtomService;

    @Captor
    private ArgumentCaptor<BatchExProxyCouponRequest> requestCaptor;

    private BatchExProxyCouponRequest validRequest;

    private BatchExProxyCouponResponseDTO validResponse;

    CompositeAtomServiceImplGetProductBaseInfoByUnifiedIdTest() {
        service = new CompositeAtomServiceImpl();
        // Use reflection to set the mocked dealGroupQueryServiceAtom
        try {
            java.lang.reflect.Field field = CompositeAtomServiceImpl.class.getDeclaredField("dealGroupQueryServiceAtom");
            field.setAccessible(true);
            field.set(service, dealGroupQueryServiceAtom);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @AfterEach
    void tearDown() {
        Mockito.reset(dealGroupQueryServiceAtom, log);
    }

    @BeforeEach
    void setUp() {
        validRequest = new BatchExProxyCouponRequest();
        validResponse = new BatchExProxyCouponResponseDTO();
    }

    /**
     * Test getProductBaseInfoByUnifiedId with valid response and non-empty result list.
     */
    @Test
    void testGetProductBaseInfoByUnifiedId_ValidResponse() throws Throwable {
        // Arrange
        List<Long> unifiedProductId = new ArrayList<>();
        unifiedProductId.add(1L);
        unifiedProductId.add(2L);
        QueryDealGroupListResponse mockResponse = new QueryDealGroupListResponse();
        QueryDealGroupListResult mockResult = new QueryDealGroupListResult();
        List<DealGroupDTO> expectedList = new ArrayList<>();
        expectedList.add(new DealGroupDTO());
        mockResult.setList(expectedList);
        mockResponse.setData(mockResult);
        when(dealGroupQueryServiceAtom.queryByDealGroupIds(any(QueryByDealGroupIdRequest.class))).thenReturn(mockResponse);
        // Act
        List<DealGroupDTO> result = service.getProductBaseInfoByUnifiedId(unifiedProductId);
        // Assert
        assertNotNull(result);
        assertFalse(result.isEmpty());
        assertEquals(expectedList, result);
        verify(dealGroupQueryServiceAtom, times(1)).queryByDealGroupIds(any(QueryByDealGroupIdRequest.class));
    }

    /**
     * Test getProductBaseInfoByUnifiedId with valid response but empty result list.
     */
    @Test
    void testGetProductBaseInfoByUnifiedId_EmptyResult() throws Throwable {
        // Arrange
        List<Long> unifiedProductId = new ArrayList<>();
        unifiedProductId.add(1L);
        unifiedProductId.add(2L);
        QueryDealGroupListResponse mockResponse = new QueryDealGroupListResponse();
        QueryDealGroupListResult mockResult = new QueryDealGroupListResult();
        mockResult.setList(new ArrayList<>());
        mockResponse.setData(mockResult);
        when(dealGroupQueryServiceAtom.queryByDealGroupIds(any(QueryByDealGroupIdRequest.class))).thenReturn(mockResponse);
        // Act
        List<DealGroupDTO> result = service.getProductBaseInfoByUnifiedId(unifiedProductId);
        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(dealGroupQueryServiceAtom, times(1)).queryByDealGroupIds(any(QueryByDealGroupIdRequest.class));
    }

    /**
     * 测试远程调用成功且返回有效结果的情况
     */
    @Test
    void testQueryExcludeProxyCouponListSuccessWithValidResponse() throws Throwable {
        // arrange
        doAnswer(invocation -> {
            InvokerHelper.getCallback().onSuccess(new Response<>(true, 0, "success", validResponse));
            return null;
        }).when(couponComponentQueryServiceFuture).queryExcludeProxyCouponList(any());
        // act
        CompletableFuture<BatchExProxyCouponResponseDTO> result = compositeAtomService.queryExcludeProxyCouponList(validRequest);
        // assert
        verify(couponComponentQueryServiceFuture).queryExcludeProxyCouponList(requestCaptor.capture());
        assertEquals(validRequest, requestCaptor.getValue());
        assertNotNull(result.get());
    }

    /**
     * 测试远程调用成功但返回null结果的情况
     */
    @Test
    void testQueryExcludeProxyCouponListSuccessWithNullResponse() throws Throwable {
        // arrange
        doAnswer(invocation -> {
            InvokerHelper.getCallback().onSuccess(new Response<>(true, 0, "success", null));
            return null;
        }).when(couponComponentQueryServiceFuture).queryExcludeProxyCouponList(any());
        // act
        CompletableFuture<BatchExProxyCouponResponseDTO> result = compositeAtomService.queryExcludeProxyCouponList(validRequest);
        // assert
        verify(couponComponentQueryServiceFuture).queryExcludeProxyCouponList(any());
        assertNull(result.get());
    }

    /**
     * 测试远程调用成功但响应不成功的情况
     */
    @Test
    void testQueryExcludeProxyCouponListSuccessButResponseNotSuccess() throws Throwable {
        // arrange
        doAnswer(invocation -> {
            InvokerHelper.getCallback().onSuccess(new Response<>(false, 500, "Error", null));
            return null;
        }).when(couponComponentQueryServiceFuture).queryExcludeProxyCouponList(any());
        // act
        CompletableFuture<BatchExProxyCouponResponseDTO> result = compositeAtomService.queryExcludeProxyCouponList(validRequest);
        // assert
        verify(couponComponentQueryServiceFuture).queryExcludeProxyCouponList(any());
        assertNull(result.get());
    }

    /**
     * 测试远程调用抛出异常的情况
     */
    @Test
    void testQueryExcludeProxyCouponListWithException() throws Throwable {
        // arrange
        RuntimeException exception = new RuntimeException("Test exception");
        doAnswer(invocation -> {
            InvokerHelper.getCallback().onFailure(exception);
            return null;
        }).when(couponComponentQueryServiceFuture).queryExcludeProxyCouponList(any());
        // act
        CompletableFuture<BatchExProxyCouponResponseDTO> result = compositeAtomService.queryExcludeProxyCouponList(validRequest);
        // assert
        verify(couponComponentQueryServiceFuture).queryExcludeProxyCouponList(any());
        assertNull(result.get());
    }

    /**
     * 测试请求参数为null的边界情况
     */
    @Test
    void testQueryExcludeProxyCouponListWithNullRequest() throws Throwable {
        // arrange
        doAnswer(invocation -> {
            InvokerHelper.getCallback().onSuccess(new Response<>(true, 0, "success", validResponse));
            return null;
        }).when(couponComponentQueryServiceFuture).queryExcludeProxyCouponList(any());
        // act
        CompletableFuture<BatchExProxyCouponResponseDTO> result = compositeAtomService.queryExcludeProxyCouponList(null);
        // assert
        verify(couponComponentQueryServiceFuture).queryExcludeProxyCouponList(requestCaptor.capture());
        assertNull(requestCaptor.getValue());
        assertNotNull(result.get());
    }
}
