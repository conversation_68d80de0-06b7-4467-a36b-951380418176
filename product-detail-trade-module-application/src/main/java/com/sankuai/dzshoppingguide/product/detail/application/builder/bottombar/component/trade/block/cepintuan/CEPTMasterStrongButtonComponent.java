package com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.component.trade.block.cepintuan;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.component.trade.block.cepintuan.utils.CEPTShareInfoUtils;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.exception.TradeButtonBuildFatalException;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.cepinpool.dto.CostEffectivePinTuan;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.component.trade.button.StandardTradeButtonVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.backgroud.BottomBarBackgroundVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.rich.content.CountDownRichContentVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.rich.content.PicRichContentVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.rich.content.TextRichContentVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.rich.content.enums.TextStyleEnum;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;

import javax.annotation.Nullable;

import static com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.constant.ButtonStyleConstant.CE_PIN_TUAN_STRONG_BUTTON;

/**
 * @Author: guangyujie
 * @Date: 2025/3/20 22:00
 */
@Slf4j
public class CEPTMasterStrongButtonComponent {

    public static StandardTradeButtonVO build(final ProductDetailPageRequest request,
                                              @NotNull final CostEffectivePinTuan costEffectivePinTuan,
                                              @Nullable final ProductBaseInfo productBaseInfo) {
        try {
            StandardTradeButtonVO button = new StandardTradeButtonVO();
            button.setActionData(CEPTShareInfoUtils.buildShareInfo(costEffectivePinTuan, productBaseInfo));
            button.setMainTitle(Lists.newArrayList(
                    new PicRichContentVO(42, 32, "https://img.meituan.net/beautyimg/e81a8a1d09229bbd9aa1489be06772381671.png"),
                    new TextRichContentVO(
                            costEffectivePinTuan.isLimitNewCustomJoin() ? "邀请新用户拼团" : "邀请好友拼团",
                            TextStyleEnum.Bold, 32, CE_PIN_TUAN_STRONG_BUTTON.getTitleColor()
                    )
            ));
            button.setSubTitle(Lists.newArrayList(
                    new TextRichContentVO("距结束 ", TextStyleEnum.Default, 20, CE_PIN_TUAN_STRONG_BUTTON.getTitleColor()),
                    new CountDownRichContentVO(costEffectivePinTuan.getExpireTime(), TextStyleEnum.Default, 20, CE_PIN_TUAN_STRONG_BUTTON.getTitleColor())
            ));
            button.setBackground(BottomBarBackgroundVO.buildVerticalGradientColor(
                    CE_PIN_TUAN_STRONG_BUTTON.getBackGroundColors()
            ));
            return button;
        } catch (
                Throwable throwable) {
            log.error("CEPTMasterStrongButtonComponent.build,request:{}",
                    JSON.toJSONString(request), new TradeButtonBuildFatalException(throwable));
            return null;
        }
    }

}
