package com.sankuai.dzshoppingguide.product.detail.spi.dtos.pintuan;

import com.dianping.mobile.framework.annotation.MobileDo;

import java.io.Serializable;

/**
 * @Author: <EMAIL>
 * @Date: 2025/3/13
 */
@MobileDo(id = 0xf539)
public class ProductDetailGuaranteeItem implements Serializable {
    /**
     * icon
     */
    @MobileDo.MobileField(key = 0x3c48)
    private String icon;

    /**
     * 文案
     */
    @MobileDo.MobileField(key = 0x451b)
    private String text;

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }
}