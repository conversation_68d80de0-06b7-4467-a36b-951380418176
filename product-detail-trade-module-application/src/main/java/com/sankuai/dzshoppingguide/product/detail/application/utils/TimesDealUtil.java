package com.sankuai.dzshoppingguide.product.detail.application.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.sankuai.dz.product.detail.RequestCustomParamEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.constants.DealAttrKeys;
import com.sankuai.dzshoppingguide.product.detail.application.constants.PromoTagTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.creditpay.ExposeResult;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommendgoods.dtos.ProductM;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.timesdeal.dto.DealDetailBuildContext;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.deal.DealGroupDealDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.sankuai.dzshoppingguide.product.detail.application.utils.LionConfigUtils.dealsFilterSwitch;


/**
 * @author: created by hang.yu on 2024/1/11 15:02
 */
@Slf4j
public class TimesDealUtil {

    private static final Integer TIMES_CARD = 19;
    private static final int VALID_DEAL_STATUS = 1;
    private static final String MONTHLY_SUBSCRIPTION = "是";
    public static final String SYS_MULTI_SALE_NUMBER = "sys_multi_sale_number";


    public static List<String> getAntiRefreshModuleList(final ProductDetailPageRequest request) {
        try {
            String antiRefreshModuleStr = request.getCustomParam(RequestCustomParamEnum.antiRefreshModuleList);
            if (com.dianping.cat.util.StringUtils.isEmpty(antiRefreshModuleStr)) {
                return Lists.newArrayList();
            }
            return JSON.parseArray(antiRefreshModuleStr, String.class);
        } catch (Exception e) {
            log.error("getAntiRefreshModuleList error,request:{}", JSONObject.toJSONString(request),e);
        }
        return Lists.newArrayList();
    }

    /**
     * 判断是否为 团购次卡的团单
     */
    public static boolean isTimesDeal(ProductBaseInfo productBaseInfo) {
        if (Objects.isNull(productBaseInfo) || Objects.isNull(productBaseInfo.getBasic())) {
            return false;
        }
        return Objects.equals(TIMES_CARD, productBaseInfo.getBasic().getTradeType());
    }

    /**
     * 次卡->连续包月
     * @param productBaseInfo
     * @return
     */
    public static Boolean isMonthlySubscription(ProductBaseInfo productBaseInfo, ProductAttr productAttr) {
        boolean isMonthlySubscription = false;
        if(isTimesDeal(productBaseInfo) && Objects.nonNull(productAttr)){
            String sysMultiSaleTypeAttr =  productAttr.getProductAttrFirstValue(DealAttrKeys.CONTINUOUS_MONTHLY_SUBSCRIPTION);
            if(MONTHLY_SUBSCRIPTION.equals(sysMultiSaleTypeAttr)){
                isMonthlySubscription = true;
            }

        }
        return isMonthlySubscription;
    }

    /**
     * 判断是否 单次到店仅可核销一次
     */
    public static boolean onlyVerificationOne(DealGroupDTO dealGroupDTO) {
        if (dealGroupDTO == null || CollectionUtils.isEmpty(dealGroupDTO.getAttrs())) {
            return false;
        }
        return DealAttrHelper.onlyVerificationOne(dealGroupDTO.getAttrs());
    }

    /**
     * 解析次数
     */
    public static String parseTimes(List<DealGroupDealDTO> deals) {
        if (CollectionUtils.isEmpty(deals)) {
            return null;
        }
        // 过滤无效团单
        deals = filterInvalidDeals(deals);
        DealGroupDealDTO dealGroupDealDTO = deals.get(0);
        if (dealGroupDealDTO == null || CollectionUtils.isEmpty(dealGroupDealDTO.getAttrs())) {
            return null;
        }
        return DealAttrHelper.getTimes(dealGroupDealDTO.getAttrs());
    }

    /**
     * 过滤无效团单
     */
    public static List<DealGroupDealDTO> filterInvalidDeals(List<DealGroupDealDTO> deals){
        if (dealsFilterSwitch() && CollectionUtils.isNotEmpty(deals)){
            deals = deals.stream().filter(deal->Objects.nonNull(deal.getBasic()) && Objects.nonNull(deal.getBasic().getStatus()) && deal.getBasic().getStatus() == VALID_DEAL_STATUS).collect(Collectors.toList());
        }
        return deals;
    }

    /**
     * 相似团购的团单发生倒挂
     * 当前团单的次数 > 关联团单的次数   当前的单次卡价格>=关联团单的单次卡价格就倒挂
     * 当前团单的次数 < 关联团单的次数   当前的单次卡价格<=关联团单的单次卡价格就倒挂
     * 当前团单的次数 = 关联团单的次数   当前的单次卡价格>=关联团单的单次卡价格就倒挂
     */
    public static boolean timesDealHangUpDown(ProductM currentProductM, ProductM relatedProductM) {
        int currentDealTimes = getDealTimes(currentProductM);
        int relatedDealTimes = getDealTimes(relatedProductM);
        BigDecimal currentSinglePrice = getProductSinglePrice(currentProductM, currentDealTimes);
        BigDecimal relatedSinglePrice = getProductSinglePrice(relatedProductM, relatedDealTimes);
        if (currentDealTimes > relatedDealTimes) {
            return currentSinglePrice.compareTo(relatedSinglePrice) >= 0;
        } else if (currentDealTimes < relatedDealTimes) {
            return currentSinglePrice.compareTo(relatedSinglePrice) <= 0;
        } else {
            return currentSinglePrice.compareTo(relatedSinglePrice) >= 0;
        }
    }

    /**
     * 根据售价和次数算出单次价格
     */
    public static BigDecimal getProductSinglePrice(ProductM productM, int times) {
        if (!productM.isTimesDeal()) {
            return productM.getSalePrice();
        }
        try {
            return productM.getSalePrice().divide(new BigDecimal(times), 2, RoundingMode.UP);
        } catch (Exception e) {
            log.error("getSinglePrice error", e);
            return productM.getSalePrice();
        }
    }

    public static int getDealTimes(ProductM productM) {
        // 非多次卡返回1
        if (!productM.isTimesDeal()) {
            return 1;
        }
        // 获取多次卡的次数
        String times = productM.getAttr(TimesDealUtil.SYS_MULTI_SALE_NUMBER);
        return NumberUtils.toInt(times, 1);
    }

    /**
     * 多次卡团单倒挂： 多次卡的单价 >= 关联单次卡的到手价
     */
    public static boolean timesDealHangUpDown(ProductM timesDealProductM, Map<Integer, ProductM> allProducts) {
        if (!timesDealProductM.isTimesDeal()) {
            return false;
        }
        // 获取对应的单次卡
        ProductM singleTimeDeal = getRelatedSingleProductM(timesDealProductM, allProducts);
        ProductM multiTimesDeal = allProducts.get(timesDealProductM.getProductId());
        if (singleTimeDeal == null || multiTimesDeal == null) {
            return false;
        }
        BigDecimal productSinglePrice = getProductSinglePrice(multiTimesDeal);
        if (productSinglePrice == null || singleTimeDeal.getSalePrice() == null) {
            return false;
        }
        // 多次卡的单价 大于或等于 单次卡到手价。
        return productSinglePrice.compareTo(singleTimeDeal.getSalePrice()) >= 0;
    }

    /**
     * 获取多次卡关联的单次卡团单
     */
    public static ProductM getRelatedSingleProductM(ProductM timesDealProductM, Map<Integer, ProductM> allProducts) {
        if (!timesDealProductM.isTimesDeal()) {
            return null;
        }
        // 获取对应的单次卡
        return timesDealProductM.getRelatedTimesDeal().stream().map(allProducts::get).filter(Objects::nonNull).filter(productM -> !productM.isTimesDeal()).findFirst().orElse(null);
    }

    /**
     * 根据售价和次数算出单次价格
     */
    public static BigDecimal getProductSinglePrice(ProductM productM) {
        if (!productM.isTimesDeal()) {
            return null;
        }
        // 获取多次卡的次数
        String times = productM.getAttr(TimesDealUtil.SYS_MULTI_SALE_NUMBER);
        if (StringUtils.isBlank(times) || !NumberUtils.isDigits(times)) {
            return null;
        }
        try {
            return productM.getSalePrice().divide(new BigDecimal(times), 2, RoundingMode.UP);
        } catch (Exception e) {
            log.error("getSinglePrice error", e);
            return null;
        }
    }

    public static void setPricePromoTagFlag(List<ProductM> productMS, DealDetailBuildContext buildContext) {
        try {
            if (CollectionUtils.isEmpty(productMS)) {
                return;
            }
            // 性价比高标签
            productMS.stream().filter(Objects::nonNull) // 必须是次卡召回的
                    .filter(ProductM::isTimesDeal) // 必须是团购次卡
                    .filter(productM -> productM.getAttr(TimesDealUtil.SYS_MULTI_SALE_NUMBER) != null)
                    .min(Comparator.comparingInt(o -> NumberUtils.toInt(o.getAttr(TimesDealUtil.SYS_MULTI_SALE_NUMBER),9999)))
                    .ifPresent(productM -> productM.setPromoTagType(PromoTagTypeEnum.COST_EFFECTIVENESS.getCode()));

            String expResult = Optional.ofNullable(buildContext).map(DealDetailBuildContext::getAbTestReturnValue).map(item -> item.getAbTestExpResult("MtCreditPayExp")).orElse(StringUtils.EMPTY);
            boolean isUserSupportExposure = Optional.ofNullable(buildContext).map(DealDetailBuildContext::getExposeResult).map(ExposeResult::isSupportExpose).orElse(false);
            boolean hitCreditPay = StringUtils.equals("c",expResult);
            if (!hitCreditPay || !isUserSupportExposure) {
                // 未命中实验,或者用户不支持曝光
                return;
            }
            // 先用后付标签(优先级 > 性价比高)
            productMS.forEach(product -> {
                boolean supportCreditPay = dealTimesCardSupportCreditPay(product);
                if (supportCreditPay) {
                    product.setPromoTagType(PromoTagTypeEnum.CREDIT_USE.getCode());
                }
            });

        } catch (Exception e) {
            log.error(String.format("setPricePromoTagFlag error,parameter: %s", com.dianping.vc.sdk.codec.JsonCodec.encodeWithUTF8(buildContext)), e);
        }
    }

    /**
     * 判断次卡是否支持先用后付
     * @param productM
     * @return
     */
    public static boolean dealTimesCardSupportCreditPay(ProductM productM) {
        if (!productM.isTimesDeal()) {
            return false;
        }
        String afterPayProduct = productM.getAttr("pay_method");
        int value = NumberUtils.toInt(afterPayProduct, 0);
        // 4 支持先用后付
        return value == 4;
    }

    /**
     * 根据售价和次数算出单次价格
     */
    public static String getSinglePrice(BigDecimal salePrice, String times) {
        if (salePrice == null) {
            return null;
        }
        if (StringUtils.isBlank(times) || !NumberUtils.isDigits(times)) {
            return null;
        }
        // 取到手价/可用次数  向上取整
        try {
            return salePrice.divide(new BigDecimal(times), 2, RoundingMode.UP).stripTrailingZeros().toPlainString();
        } catch (Exception e) {
            log.error("getSinglePrice error", e);
            return null;
        }
    }
}
