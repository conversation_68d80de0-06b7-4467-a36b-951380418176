package com.sankuai.dzshoppingguide.product.detail.application.utils;

import static org.junit.Assert.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import com.dianping.lion.client.Lion;
import org.junit.Test;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.modules.junit4.PowerMockRunnerDelegate;
import org.springframework.test.context.junit4.SpringRunner;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import java.util.Arrays;
import java.util.Collections;
import org.junit.Before;

@RunWith(PowerMockRunner.class)
@PrepareForTest(Lion.class)
@PowerMockIgnore({ "javax.management.*", "javax.script.*" })
public class LionConfigUtilsNeedReplaceImToCsForCarefree1Test {

    private static final String APP_KEY = "dztrade-mapi-web";

    private static final String CONFIG_KEY = "dztrade-mapi-web.carefree.self.own.csUrl.switch";

    private static final boolean DEFAULT_VALUE = false;

    private static final String APPKEY = "com.sankuai.dzu.tpbase.dztgdetailweb";

    private static final String COUNTRY_SUBSIDY_SERVICE_TYPE_IDS = "country.subsidy.service.type.ids";

    /**
     * 测试当Lion配置返回true时，方法返回true
     */
    @Test
    public void testNeedReplaceImToCsForCarefree_WhenLionReturnsTrue() throws Throwable {
        // arrange
        PowerMockito.mockStatic(Lion.class);
        PowerMockito.when(Lion.getBoolean(eq(APP_KEY), eq(CONFIG_KEY), eq(DEFAULT_VALUE))).thenReturn(true);
        // act
        boolean result = LionConfigUtils.needReplaceImToCsForCarefree();
        // assert
        org.junit.Assert.assertTrue(result);
        PowerMockito.verifyStatic(Lion.class);
        Lion.getBoolean(APP_KEY, CONFIG_KEY, DEFAULT_VALUE);
    }

    /**
     * 测试当Lion配置返回false时，方法返回false
     */
    @Test
    public void testNeedReplaceImToCsForCarefree_WhenLionReturnsFalse() throws Throwable {
        // arrange
        PowerMockito.mockStatic(Lion.class);
        PowerMockito.when(Lion.getBoolean(eq(APP_KEY), eq(CONFIG_KEY), eq(DEFAULT_VALUE))).thenReturn(false);
        // act
        boolean result = LionConfigUtils.needReplaceImToCsForCarefree();
        // assert
        org.junit.Assert.assertFalse(result);
        PowerMockito.verifyStatic(Lion.class);
        Lion.getBoolean(APP_KEY, CONFIG_KEY, DEFAULT_VALUE);
    }

    /**
     * 测试当Lion抛出异常时，方法返回默认值false
     */
    @Test
    public void testNeedReplaceImToCsForCarefree_WhenLionThrowsException() throws Throwable {
        // arrange
        PowerMockito.mockStatic(Lion.class);
        PowerMockito.when(Lion.getBoolean(eq(APP_KEY), eq(CONFIG_KEY), eq(DEFAULT_VALUE))).thenThrow(new RuntimeException("Lion error"));
        // act
        boolean result = LionConfigUtils.needReplaceImToCsForCarefree();
        // assert
        org.junit.Assert.assertFalse(result);
        PowerMockito.verifyStatic(Lion.class);
        Lion.getBoolean(APP_KEY, CONFIG_KEY, DEFAULT_VALUE);
    }

    @Before
    public void setup() {
        PowerMockito.mockStatic(Lion.class);
    }

    @Test
    public void testIsCountrySubsidyDealServiceTypeIdWhenProductBaseInfoIsNull() throws Throwable {
        // arrange
        ProductBaseInfo productBaseInfo = null;
        // act
        boolean result = LionConfigUtils.isCountrySubsidyDealServiceTypeId(productBaseInfo);
        // assert
        assertFalse(result);
    }

    @Test
    public void testIsCountrySubsidyDealServiceTypeIdWhenCategoryIsNull() throws Throwable {
        // arrange
        ProductBaseInfo productBaseInfo = mock(ProductBaseInfo.class);
        when(productBaseInfo.getCategory()).thenReturn(null);
        // act
        boolean result = LionConfigUtils.isCountrySubsidyDealServiceTypeId(productBaseInfo);
        // assert
        assertFalse(result);
    }

    @Test
    public void testIsCountrySubsidyDealServiceTypeIdWhenServiceTypeNotSubsidized() throws Throwable {
        // arrange
        ProductBaseInfo productBaseInfo = mock(ProductBaseInfo.class);
        DealGroupCategoryDTO category = mock(DealGroupCategoryDTO.class);
        when(productBaseInfo.getCategory()).thenReturn(category);
        when(category.getServiceTypeId()).thenReturn(123L);
        PowerMockito.when(Lion.getList(eq(APPKEY), eq(COUNTRY_SUBSIDY_SERVICE_TYPE_IDS), eq(Long.class), any())).thenReturn(Arrays.asList(456L, 789L));
        // act
        boolean result = LionConfigUtils.isCountrySubsidyDealServiceTypeId(productBaseInfo);
        // assert
        assertFalse(result);
    }

    @Test
    public void testIsCountrySubsidyDealServiceTypeIdWhenServiceTypeIsSubsidized() throws Throwable {
        // arrange
        ProductBaseInfo productBaseInfo = mock(ProductBaseInfo.class);
        DealGroupCategoryDTO category = mock(DealGroupCategoryDTO.class);
        when(productBaseInfo.getCategory()).thenReturn(category);
        when(category.getServiceTypeId()).thenReturn(123L);
        PowerMockito.when(Lion.getList(eq(APPKEY), eq(COUNTRY_SUBSIDY_SERVICE_TYPE_IDS), eq(Long.class), any())).thenReturn(Arrays.asList(123L, 456L));
        // act
        boolean result = LionConfigUtils.isCountrySubsidyDealServiceTypeId(productBaseInfo);
        // assert
        assertTrue(result);
    }

    @Test
    public void testIsCountrySubsidyDealServiceTypeIdWhenSubsidizedListIsEmpty() throws Throwable {
        // arrange
        ProductBaseInfo productBaseInfo = mock(ProductBaseInfo.class);
        DealGroupCategoryDTO category = mock(DealGroupCategoryDTO.class);
        when(productBaseInfo.getCategory()).thenReturn(category);
        when(category.getServiceTypeId()).thenReturn(123L);
        PowerMockito.when(Lion.getList(eq(APPKEY), eq(COUNTRY_SUBSIDY_SERVICE_TYPE_IDS), eq(Long.class), any())).thenReturn(Collections.emptyList());
        // act
        boolean result = LionConfigUtils.isCountrySubsidyDealServiceTypeId(productBaseInfo);
        // assert
        assertFalse(result);
    }

    @Test
    public void testIsCountrySubsidyDealServiceTypeIdWithMultipleSubsidizedTypes() throws Throwable {
        // arrange
        ProductBaseInfo productBaseInfo = mock(ProductBaseInfo.class);
        DealGroupCategoryDTO category = mock(DealGroupCategoryDTO.class);
        when(productBaseInfo.getCategory()).thenReturn(category);
        when(category.getServiceTypeId()).thenReturn(123L);
        PowerMockito.when(Lion.getList(eq(APPKEY), eq(COUNTRY_SUBSIDY_SERVICE_TYPE_IDS), eq(Long.class), any())).thenReturn(Arrays.asList(100L, 123L, 200L));
        // act
        boolean result = LionConfigUtils.isCountrySubsidyDealServiceTypeId(productBaseInfo);
        // assert
        assertTrue(result);
    }
}
