package com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.period.price;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.ComponentFetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.response.FetcherResponse;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.ComponentFetcherContext;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.QueryCenterAggregateFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.QueryCenterAggregateReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.period.price.dto.PeriodPriceMergeDTO;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.utils.SkuIdUtils;
import com.sankuai.general.product.query.center.client.builder.model.DealDailyTimePriceBuilder;
import com.sankuai.general.product.query.center.client.builder.model.DealPeriodPriceBuilder;
import com.sankuai.general.product.query.center.client.builder.model.DealWeeklyPriceBuilder;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;

import java.util.ArrayList;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Author: guangyujie
 * @Date: 2025/2/5 17:12
 */
@ComponentFetcher(
        aggregateFetcher = QueryCenterAggregateFetcher.class
)
public class SkuPeriodPriceFetcher extends ComponentFetcherContext<
        QueryByDealGroupIdRequestBuilder,
        QueryCenterAggregateReturnValue,
        SkuPeriodPrice> {

    @Override
    public void fulfillRequest(QueryByDealGroupIdRequestBuilder requestBuilder) {
        requestBuilder
                .dealWeeklyPrice(DealWeeklyPriceBuilder.builder().weeklyPrice())
                .dealDailyTimePrice(DealDailyTimePriceBuilder.builder().dailyTimePrice())
                .dealPeriodPrice(DealPeriodPriceBuilder.builder().periodPrice())
        ;
    }

    @Override
    protected FetcherResponse<SkuPeriodPrice> mapResult(FetcherResponse<QueryCenterAggregateReturnValue> aggregateResult) {
        Map<Long, PeriodPriceMergeDTO> periodPriceDTOMap = Optional.ofNullable(aggregateResult)
                .map(FetcherResponse::getReturnValue)
                .map(QueryCenterAggregateReturnValue::getDealGroupDTO)
                .map(DealGroupDTO::getDeals)
                .orElse(new ArrayList<>())
                .stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(
                        sku -> SkuIdUtils.getSkuId(this.request.getProductTypeEnum(), sku),
                        sku -> new PeriodPriceMergeDTO(
                                sku.getWeeklyPricePlan(),
                                sku.getDateTimePrice(),
                                sku.getPeriodPrice()
                        ),
                        (v1, v2) -> v1
                ));
        return FetcherResponse.succeed(new SkuPeriodPrice(periodPriceDTOMap));
    }

}
