package com.sankuai.dzshoppingguide.product.detail.spi.similargoods.vo.dtos;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
@MobileDo(id = 0xebad)
public class DzProductVO implements Serializable {
    /**
    * long类型商品id
    */
    @MobileDo.MobileField(key = 0x4a91)
    private long longProductId;

    /**
    * 市场价，单位为分
    */
    @MobileDo.MobileField(key = 0xdcf3)
    private String marketPriceCent;

    /**
    * skuId
    */
    @MobileDo.MobileField(key = 0x3847)
    private int productItemId;

    /**
    * 折扣(例：9.6折)
    */
    @MobileDo.MobileField(key = 0xde9f)
    private String discountTag;

    /**
    * 优惠信息列表
    */
    @MobileDo.MobileField(key = 0x54a6)
    private List<DzPromoVO> promoVOList;

    /**
    *
    */
    @MobileDo.MobileField(key = 0xb5c9)
    private String shopName;

    /**
    * 扩展属性
    */
    @MobileDo.MobileField(key = 0x867e)
    private DzProductExtraAttrVo extAttrs;

    private Map<String, Object> extAttrMap;

    /**
    * 图片宽高比
    */
    @MobileDo.MobileField(key = 0x1f41)
    private double picScale;

    /**
    * 商品描述
    */
    @MobileDo.MobileField(key = 0x9730)
    private String productDesc;

    /**
    * 按钮状态
    */
    @MobileDo.MobileField(key = 0xd57)
    private int buttonStatus;

    /**
    * 商户信息
    */
    @MobileDo.MobileField(key = 0xdac3)
    private ShopVO shop;

    /**
    * 商品类型
    */
    @MobileDo.MobileField(key = 0x67de)
    private int productType;

    /**
    * 门市价
    */
    @MobileDo.MobileField(key = 0x6242)
    private String marketPrice;

    /**
    * 销量
    */
    @MobileDo.MobileField(key = 0xc072)
    private String sale;

    /**
    * 图片区
    */
    @MobileDo.MobileField(key = 0xb18b)
    private String pic;

    /**
    * 详情页跳链
    */
    @MobileDo.MobileField(key = 0x83e2)
    private String detailJumpUrl;

    /**
    * 按钮名称
    */
    @MobileDo.MobileField(key = 0x3981)
    private String buttonName;

    /**
    * 商品标签列表
    */
    @MobileDo.MobileField(key = 0xc2bf)
    private List<String> productTags;

    /**
    * 是否可用
    */
    @MobileDo.MobileField(key = 0x5e88)
    private boolean available;

    /**
    * 按钮跳转链接
    */
    @MobileDo.MobileField(key = 0x16d8)
    private String buttonJumpUrl;

    /**
    * 售价
    */
    @MobileDo.MobileField(key = 0xec65)
    private String salePrice;

    /**
    * 商品名称
    */
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

   }