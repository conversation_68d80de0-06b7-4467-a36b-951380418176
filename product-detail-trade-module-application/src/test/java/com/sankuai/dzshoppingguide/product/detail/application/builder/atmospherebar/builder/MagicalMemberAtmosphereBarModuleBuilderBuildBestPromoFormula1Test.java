package com.sankuai.dzshoppingguide.product.detail.application.builder.atmospherebar.builder;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.powermock.api.mockito.PowerMockito.when;
import com.dianping.gmkt.scene.api.delivery.dto.res.DealGroupPromotionDTO;
import com.dianping.gmkt.scene.api.delivery.dto.res.ResourceExposureResponseDTO;
import com.google.common.collect.Lists;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import com.sankuai.dealuser.price.display.api.model.PromoIdentity;
import com.sankuai.dzshoppingguide.product.detail.application.builder.atmospherebar.builder.enums.PromoSceneEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.pricebar.builder.enums.CouponGuideTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.pricebar.component.InflateCouponTipsComponent;
import com.sankuai.dzshoppingguide.product.detail.application.builder.pricebar.config.IconConfig;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.abtest.AbTestReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.utils.LionConfigUtils;
import com.sankuai.dzshoppingguide.product.detail.application.utils.PriceHelper;
import com.sankuai.dzshoppingguide.product.detail.application.utils.PriceUtils;
import com.sankuai.dzshoppingguide.product.detail.application.utils.PromoHelper;
import com.sankuai.dzshoppingguide.product.detail.domain.douhu.AbTestResult;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.ProductBestPromoFormula;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.PromoTagEnum;
import com.sankuai.nibmkt.promotion.api.common.enums.PromotionPropertyEnum;
import com.sankuai.nibmkt.promotion.api.query.model.magicalmember.response.MMCTooltipTextDTO;
import java.math.BigDecimal;
import java.util.*;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.*;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

@RunWith(PowerMockRunner.class)
@PrepareForTest({ LionConfigUtils.class, PriceUtils.class, PriceHelper.class, PromoHelper.class, InflateCouponTipsComponent.class })
@PowerMockIgnore({ "javax.management.*", "javax.crypto.*" })
public class MagicalMemberAtmosphereBarModuleBuilderBuildBestPromoFormula1Test {

    private MagicalMemberAtmosphereBarModuleBuilder builder;

    Map<String, AbTestResult> abTestResultMap = new HashMap<>();
    AbTestReturnValue abTestReturnValue = new AbTestReturnValue(abTestResultMap);

    private IconConfig mockIconConfig;

    @Before
    public void setUp() {
        builder = new MagicalMemberAtmosphereBarModuleBuilder();
        mockIconConfig = new IconConfig();
        mockIconConfig.setMarketPriceIconText("门市价");
        mockIconConfig.setAfterInflate("afterInflateIcon");
        mockIconConfig.setAfterInflateWidth(10);
        mockIconConfig.setAfterInflateHeight(10);
        mockIconConfig.setDoubleRedPacket("doubleRedPacketIcon");
        mockIconConfig.setDoubleRedPacketWidth(10);
        mockIconConfig.setDoubleRedPacketHeight(10);
        PowerMockito.mockStatic(LionConfigUtils.class);
        PowerMockito.mockStatic(PriceUtils.class);
        PowerMockito.mockStatic(PriceHelper.class);
        PowerMockito.mockStatic(PromoHelper.class);
        PowerMockito.mockStatic(InflateCouponTipsComponent.class);
        when(LionConfigUtils.getIconConfig()).thenReturn(mockIconConfig);
        when(PriceUtils.buildMarketPrice(any())).thenReturn("200");
        when(PriceHelper.dropLastZero(any())).thenReturn("50");
    }

    @Test
    public void testBuildBestPromoFormula_NullInput() throws Throwable {
        // arrange
        PriceDisplayDTO promoPriceDisplayDTO = null;
        String finalPrice = "100";
        // act
        List<ProductBestPromoFormula> result = builder.buildBestPromoFormula(promoPriceDisplayDTO, finalPrice, abTestReturnValue);
        // assert
        Assert.assertNull(result);
    }

    @Test
    public void testBuildBestPromoFormula_EmptyUsedPromos() throws Throwable {
        // arrange
        PriceDisplayDTO promoPriceDisplayDTO = new PriceDisplayDTO();
        promoPriceDisplayDTO.setMarketPrice(new BigDecimal("200"));
        promoPriceDisplayDTO.setUsedPromos(Collections.emptyList());
        String finalPrice = "100";
        when(InflateCouponTipsComponent.getPromoScene(promoPriceDisplayDTO)).thenReturn(PromoSceneEnum.ONLY_DEAL_PROMO);

        // act
        List<ProductBestPromoFormula> result = builder.buildBestPromoFormula(promoPriceDisplayDTO, finalPrice, abTestReturnValue);
        // assert
        Assert.assertEquals(1, result.size());
        Assert.assertEquals("门市价", result.get(0).getPriceDesc());
        Assert.assertEquals("200", result.get(0).getPrice());
        Assert.assertEquals("#111111", result.get(0).getPriceColor());
    }

    @Test
    public void testBuildBestPromoFormula_RegularPromo() throws Throwable {
        // arrange
        PriceDisplayDTO promoPriceDisplayDTO = new PriceDisplayDTO();
        promoPriceDisplayDTO.setMarketPrice(new BigDecimal("200"));
        PromoDTO promoDTO = new PromoDTO();
        PromoIdentity identity = new PromoIdentity(1234L, 0);
        identity.setPromoShowType("PLATFORM_COUPON");
        identity.setPromoTypeDesc("平台券");
        promoDTO.setIdentity(identity);
        promoDTO.setAmount(new BigDecimal("50"));
        promoDTO.setPromotionOtherInfoMap(new HashMap<>());
        promoPriceDisplayDTO.setUsedPromos(Collections.singletonList(promoDTO));
        String finalPrice = "100";
        when(PromoHelper.convertPromoTag("PLATFORM_COUPON", "平台券")).thenReturn("平台券");
        when(InflateCouponTipsComponent.getPromoScene(promoPriceDisplayDTO)).thenReturn(PromoSceneEnum.ONLY_DEAL_PROMO);
        // act
        List<ProductBestPromoFormula> result = builder.buildBestPromoFormula(promoPriceDisplayDTO, finalPrice, abTestReturnValue);
        // assert
        Assert.assertEquals(2, result.size());
        Assert.assertEquals("平台券", result.get(1).getPromoDesc());
        Assert.assertEquals("50", result.get(1).getPrice());
        Assert.assertEquals("#111111", result.get(1).getPriceColor());
    }

    @Test
    public void testBuildBestPromoFormula_MagicalMemberAfterInflate() throws Throwable {
        // arrange
        PriceDisplayDTO promoPriceDisplayDTO = new PriceDisplayDTO();
        promoPriceDisplayDTO.setMarketPrice(new BigDecimal("200"));
        PromoDTO promoDTO = new PromoDTO();
        PromoIdentity identity = new PromoIdentity(1234L, 0);
        identity.setPromoShowType(PromoTagEnum.MAGICAL_MEMBER_PLATFORM_COUPON.getType());
        identity.setPromoTypeDesc("神券");
        promoDTO.setIdentity(identity);
        promoDTO.setAmount(new BigDecimal("50"));
        Map<String, String> promotionOtherInfoMap = new HashMap<>();
        promotionOtherInfoMap.put(PromotionPropertyEnum.AFTER_INFLATE.getValue(), "true");
        promoDTO.setPromotionOtherInfoMap(promotionOtherInfoMap);
        promoPriceDisplayDTO.setUsedPromos(Collections.singletonList(promoDTO));
        when(InflateCouponTipsComponent.getPromoScene(promoPriceDisplayDTO)).thenReturn(PromoSceneEnum.ONLY_DEAL_PROMO);

        String finalPrice = "100";
        // act
        List<ProductBestPromoFormula> result = builder.buildBestPromoFormula(promoPriceDisplayDTO, finalPrice, abTestReturnValue);
        // assert
        Assert.assertEquals(2, result.size());
        // cleared for magical member
        Assert.assertEquals("", result.get(1).getPromoDesc());
        Assert.assertEquals("50", result.get(1).getPrice());
        Assert.assertEquals("#FF4B10", result.get(1).getPriceColor());
        Assert.assertTrue(result.get(1).isAfterInflate());
        Assert.assertNotNull(result.get(1).getWhiteBoxIcon());
    }

    @Test
    public void testBuildBestPromoFormula_MagicalMemberGuideInflate() throws Throwable {
        // arrange
        PriceDisplayDTO promoPriceDisplayDTO = new PriceDisplayDTO();
        promoPriceDisplayDTO.setMarketPrice(new BigDecimal("200"));
        PromoDTO promoDTO = new PromoDTO();
        PromoIdentity identity = new PromoIdentity(1234L, 0);
        identity.setPromoShowType(PromoTagEnum.MAGICAL_MEMBER_PLATFORM_COUPON.getType());
        identity.setPromoTypeDesc("神券");
        promoDTO.setIdentity(identity);
        promoDTO.setAmount(new BigDecimal("50"));
        Map<String, String> promotionOtherInfoMap = new HashMap<>();
        promotionOtherInfoMap.put(PromotionPropertyEnum.CAN_INFLATE.getValue(), "true");
        promoDTO.setPromotionOtherInfoMap(promotionOtherInfoMap);
        promoPriceDisplayDTO.setUsedPromos(Collections.singletonList(promoDTO));
        Map<String, String> extendDisplayInfo = new HashMap<>();
        extendDisplayInfo.put("mmcTooltipTextDTO", "{\"preInflateDiscountAmount\":\"30\",\"guideType\":1}");
        promoPriceDisplayDTO.setExtendDisplayInfo(extendDisplayInfo);
        String finalPrice = "100";
        MMCTooltipTextDTO mmcTooltipTextDTO = new MMCTooltipTextDTO();
        mmcTooltipTextDTO.setPreInflateDiscountAmount("30");
        mmcTooltipTextDTO.setGuideType(1);
        when(InflateCouponTipsComponent.getCouponGuideTypeEnum(any(), any(), any())).thenReturn(CouponGuideTypeEnum.GUIDE_INFLATE);
        when(InflateCouponTipsComponent.getMMCTooltipTextDTO(any())).thenReturn(mmcTooltipTextDTO);
        when(InflateCouponTipsComponent.getPromoScene(promoPriceDisplayDTO)).thenReturn(PromoSceneEnum.ONLY_DEAL_PROMO);

        // act
        List<ProductBestPromoFormula> result = builder.buildBestPromoFormula(promoPriceDisplayDTO, finalPrice, abTestReturnValue);
        // assert
        Assert.assertEquals(2, result.size());
        Assert.assertEquals("30", result.get(1).getBlackBoxPrice());
        Assert.assertEquals("#FF2626", result.get(1).getBlackBoxPriceColor());
        Assert.assertNotNull(result.get(1).getBlackBoxIcon());
    }

    @Test
    public void testBuildBestPromoFormula_PromoWithNullIdentity() throws Throwable {
        // arrange
        PriceDisplayDTO promoPriceDisplayDTO = new PriceDisplayDTO();
        promoPriceDisplayDTO.setMarketPrice(new BigDecimal("200"));
        PromoDTO promoDTO = new PromoDTO();
        promoDTO.setIdentity(null);
        promoDTO.setAmount(new BigDecimal("50"));
        promoDTO.setPromotionOtherInfoMap(new HashMap<>());
        promoPriceDisplayDTO.setUsedPromos(Collections.singletonList(promoDTO));
        String finalPrice = "100";
        when(InflateCouponTipsComponent.getPromoScene(promoPriceDisplayDTO)).thenReturn(PromoSceneEnum.ONLY_DEAL_PROMO);

        // act
        List<ProductBestPromoFormula> result = builder.buildBestPromoFormula(promoPriceDisplayDTO, finalPrice, abTestReturnValue);
        // assert
        // only market price formula
        Assert.assertEquals(1, result.size());
    }

    @Test
    public void testBuildBestPromoFormula_PromoWithNullAmount() throws Throwable {
        // arrange
        PriceDisplayDTO promoPriceDisplayDTO = new PriceDisplayDTO();
        promoPriceDisplayDTO.setMarketPrice(new BigDecimal("200"));
        PromoDTO promoDTO = new PromoDTO();
        PromoIdentity identity = new PromoIdentity(1234L, 0);
        identity.setPromoShowType("PLATFORM_COUPON");
        identity.setPromoTypeDesc("平台券");
        promoDTO.setIdentity(identity);
        promoDTO.setAmount(null);
        promoDTO.setPromotionOtherInfoMap(new HashMap<>());
        promoPriceDisplayDTO.setUsedPromos(Collections.singletonList(promoDTO));
        String finalPrice = "100";
        when(InflateCouponTipsComponent.getPromoScene(promoPriceDisplayDTO)).thenReturn(PromoSceneEnum.ONLY_DEAL_PROMO);

        // act
        List<ProductBestPromoFormula> result = builder.buildBestPromoFormula(promoPriceDisplayDTO, finalPrice, abTestReturnValue);
        // assert
        // only market price formula
        Assert.assertEquals(1, result.size());
    }

    @Test
    public void testBuildBestPromoFormula_MultiplePromosSorting() throws Throwable {
        // arrange
        PriceDisplayDTO promoPriceDisplayDTO = new PriceDisplayDTO();
        promoPriceDisplayDTO.setMarketPrice(new BigDecimal("200"));
        List<PromoDTO> promos = new ArrayList<>();
        // Market price will be first, then this should be second (DEAL_PROMO)
        PromoDTO dealPromo = new PromoDTO();
        PromoIdentity dealIdentity = new PromoIdentity(1234L, 0);
        dealIdentity.setPromoShowType("DEAL_PROMO");
        dealIdentity.setPromoTypeDesc("团购优惠");
        dealPromo.setIdentity(dealIdentity);
        dealPromo.setAmount(new BigDecimal("20"));
        dealPromo.setPromotionOtherInfoMap(new HashMap<>());
        promos.add(dealPromo);
        // This should be third (MAGICAL_MEMBER_PLATFORM_COUPON)
        PromoDTO memberPromo = new PromoDTO();
        PromoIdentity memberIdentity = new PromoIdentity(1235L, 0);
        memberIdentity.setPromoShowType(PromoTagEnum.MAGICAL_MEMBER_PLATFORM_COUPON.getType());
        memberIdentity.setPromoTypeDesc("神券");
        memberPromo.setIdentity(memberIdentity);
        memberPromo.setAmount(new BigDecimal("30"));
        Map<String, String> memberPromotionOtherInfoMap = new HashMap<>();
        memberPromotionOtherInfoMap.put(PromotionPropertyEnum.AFTER_INFLATE.getValue(), "false");
        memberPromo.setPromotionOtherInfoMap(memberPromotionOtherInfoMap);
        promos.add(memberPromo);
        // This should be last (PLATFORM_COUPON)
        PromoDTO platformPromo = new PromoDTO();
        PromoIdentity platformIdentity = new PromoIdentity(1236L, 0);
        platformIdentity.setPromoShowType("PLATFORM_COUPON");
        platformIdentity.setPromoTypeDesc("平台券");
        platformPromo.setIdentity(platformIdentity);
        platformPromo.setAmount(new BigDecimal("10"));
        platformPromo.setPromotionOtherInfoMap(new HashMap<>());
        promos.add(platformPromo);
        promoPriceDisplayDTO.setUsedPromos(promos);
        String finalPrice = "100";
        // Mock specific behaviors for each promo type
        when(PromoHelper.convertPromoTag("DEAL_PROMO", "团购优惠")).thenReturn("团购优惠");
        when(PromoHelper.convertPromoTag(PromoTagEnum.MAGICAL_MEMBER_PLATFORM_COUPON.getType(), "神券")).thenReturn("神券");
        when(PromoHelper.convertPromoTag("PLATFORM_COUPON", "平台券")).thenReturn("平台券");
        when(InflateCouponTipsComponent.getPromoScene(promoPriceDisplayDTO)).thenReturn(PromoSceneEnum.ONLY_DEAL_PROMO);

        // act
        List<ProductBestPromoFormula> result = builder.buildBestPromoFormula(promoPriceDisplayDTO, finalPrice, abTestReturnValue);
        // assert
        Assert.assertEquals(4, result.size());
        Assert.assertEquals("门市价", result.get(0).getPromoDesc());
        Assert.assertEquals("团购优惠", result.get(1).getPromoDesc());
        Assert.assertEquals("", result.get(2).getPromoDesc());
        Assert.assertEquals("平台券", result.get(3).getPromoDesc());
    }

    @Test
    public void testBuildBestPromoFormula_InvalidPromoShowType() throws Throwable {
        // arrange
        PriceDisplayDTO promoPriceDisplayDTO = new PriceDisplayDTO();
        promoPriceDisplayDTO.setMarketPrice(new BigDecimal("200"));
        PromoDTO promoDTO = new PromoDTO();
        PromoIdentity identity = new PromoIdentity(1234L, 0);
        identity.setPromoShowType("INVALID_TYPE");
        identity.setPromoTypeDesc("Invalid");
        promoDTO.setIdentity(identity);
        promoDTO.setAmount(new BigDecimal("50"));
        promoDTO.setPromotionOtherInfoMap(new HashMap<>());
        promoPriceDisplayDTO.setUsedPromos(Collections.singletonList(promoDTO));
        String finalPrice = "100";
        when(PromoHelper.convertPromoTag("INVALID_TYPE", "Invalid")).thenReturn("团购优惠");
        when(InflateCouponTipsComponent.getPromoScene(promoPriceDisplayDTO)).thenReturn(PromoSceneEnum.ONLY_DEAL_PROMO);

        // act
        List<ProductBestPromoFormula> result = builder.buildBestPromoFormula(promoPriceDisplayDTO, finalPrice, abTestReturnValue);
        // assert
        Assert.assertEquals(2, result.size());
        Assert.assertEquals("团购优惠", result.get(1).getPromoDesc());
    }
}
