package com.sankuai.dzshoppingguide.product.detail.application.utils;

import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Calendar;
import java.util.TimeZone;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("DateAndTimeUtils weekToDateStartTime Tests")
class DateAndTimeUtilsTest {

    private static final long ONE_DAY_IN_MILLIS = 24L * 60L * 60L * 1000L;

    private static TimeZone originalTimeZone;

    private Calendar setFixedCalendar(Calendar calendar, int year, int month, int day, int hourOfDay, int minute, int second, int millisecond) {
        calendar.set(year, month, day, hourOfDay, minute, second);
        calendar.set(Calendar.MILLISECOND, millisecond);
        return calendar;
    }

    @BeforeAll
    public static void setUp() {
        originalTimeZone = TimeZone.getDefault();
        TimeZone.setDefault(TimeZone.getTimeZone("GMT+8"));
    }

    @AfterAll
    public static void tearDown() {
        TimeZone.setDefault(originalTimeZone);
    }

    /**
     * Test when the original time is already at an interval boundary
     */
    @Test
    public void testConvertToNearestSmallerLongTimeAtIntervalBoundary() {
        // arrange
        Calendar cal = Calendar.getInstance();
        cal.set(2023, Calendar.JANUARY, 1, 12, 0, 0);
        cal.set(Calendar.MILLISECOND, 0);
        long originalTime = cal.getTimeInMillis();
        int interval = 15;
        // act
        long result = DateAndTimeUtils.convertToNearestSmallerLongTime(originalTime, interval);
        // assert
        assertEquals(originalTime, result);
    }

    /**
     * Test when the original time needs to be adjusted to the nearest smaller interval
     */
    @Test
    public void testConvertToNearestSmallerLongTimeNeedsAdjustment() {
        // arrange
        Calendar cal = Calendar.getInstance();
        cal.set(2023, Calendar.JANUARY, 1, 12, 7, 0);
        cal.set(Calendar.MILLISECOND, 0);
        long originalTime = cal.getTimeInMillis();
        int interval = 15;
        cal.set(2023, Calendar.JANUARY, 1, 12, 0, 0);
        long expectedTime = cal.getTimeInMillis();
        // act
        long result = DateAndTimeUtils.convertToNearestSmallerLongTime(originalTime, interval);
        // assert
        assertEquals(expectedTime, result);
    }

    /**
     * Test when the interval is 1 minute (edge case)
     */
    @Test
    public void testConvertToNearestSmallerLongTimeOneMinuteInterval() {
        // arrange
        Calendar cal = Calendar.getInstance();
        cal.set(2023, Calendar.JANUARY, 1, 12, 0, 30);
        cal.set(Calendar.MILLISECOND, 500);
        long originalTime = cal.getTimeInMillis();
        int interval = 1;
        cal.set(2023, Calendar.JANUARY, 1, 12, 0, 0);
        cal.set(Calendar.MILLISECOND, 0);
        long expectedTime = cal.getTimeInMillis();
        // act
        long result = DateAndTimeUtils.convertToNearestSmallerLongTime(originalTime, interval);
        // assert
        assertEquals(expectedTime, result);
    }

    /**
     * Test when the interval is 60 minutes (edge case)
     */
    @Test
    public void testConvertToNearestSmallerLongTimeSixtyMinuteInterval() {
        // arrange
        Calendar cal = Calendar.getInstance();
        cal.set(2023, Calendar.JANUARY, 1, 12, 30, 0);
        cal.set(Calendar.MILLISECOND, 0);
        long originalTime = cal.getTimeInMillis();
        int interval = 60;
        cal.set(2023, Calendar.JANUARY, 1, 12, 0, 0);
        long expectedTime = cal.getTimeInMillis();
        // act
        long result = DateAndTimeUtils.convertToNearestSmallerLongTime(originalTime, interval);
        // assert
        assertEquals(expectedTime, result);
    }

    /**
     * Test when the original time is at the start of a day
     */
    @Test
    public void testConvertToNearestSmallerLongTimeStartOfDay() {
        // arrange
        Calendar cal = Calendar.getInstance();
        cal.set(2023, Calendar.JANUARY, 1, 0, 0, 0);
        cal.set(Calendar.MILLISECOND, 0);
        long originalTime = cal.getTimeInMillis();
        int interval = 15;
        // act
        long result = DateAndTimeUtils.convertToNearestSmallerLongTime(originalTime, interval);
        // assert
        assertEquals(originalTime, result);
    }

    /**
     * Test when the original time is at the end of a day
     */
    @Test
    public void testConvertToNearestSmallerLongTimeEndOfDay() {
        // arrange
        Calendar cal = Calendar.getInstance();
        cal.set(2023, Calendar.JANUARY, 1, 23, 59, 59);
        cal.set(Calendar.MILLISECOND, 999);
        long originalTime = cal.getTimeInMillis();
        int interval = 15;
        cal.set(2023, Calendar.JANUARY, 1, 23, 45, 0);
        cal.set(Calendar.MILLISECOND, 0);
        long expectedTime = cal.getTimeInMillis();
        // act
        long result = DateAndTimeUtils.convertToNearestSmallerLongTime(originalTime, interval);
        // assert
        assertEquals(expectedTime, result);
    }

    /**
     * Test when the interval is larger than 60 minutes (exceptional case)
     */
    @Test
    public void testConvertToNearestSmallerLongTimeLargeInterval() {
        // arrange
        Calendar cal = Calendar.getInstance();
        cal.set(2023, Calendar.JANUARY, 1, 14, 30, 0);
        cal.set(Calendar.MILLISECOND, 0);
        long originalTime = cal.getTimeInMillis();
        // 2 hours
        int interval = 120;
        cal.set(2023, Calendar.JANUARY, 1, 14, 0, 0);
        long expectedTime = cal.getTimeInMillis();
        // act
        long result = DateAndTimeUtils.convertToNearestSmallerLongTime(originalTime, interval);
        // assert
        assertEquals(expectedTime, result);
    }

    @Test
    public void testWeekToDateStartTime_FromMondayToSunday() throws Throwable {
        Calendar calendar = Calendar.getInstance();
        // Adjust the expected date based on the current date
        int currentDay = calendar.get(Calendar.DAY_OF_WEEK);
        int targetDay = 7;
        int daysDiff = targetDay - (currentDay > 1 ? currentDay - 1 : 7);
        calendar.add(Calendar.DAY_OF_YEAR, daysDiff);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        long expectedTimeInMillis = calendar.getTimeInMillis();
        // act
        long result = DateAndTimeUtils.weekToDateStartTime("7");
        // assert
        assertEquals(expectedTimeInMillis, result);
    }

    @Test
    public void testWeekToDateStartTime_InvalidInput() throws Throwable {
        // act & assert
        assertThrows(NumberFormatException.class, () -> DateAndTimeUtils.weekToDateStartTime("invalid"));
    }

    /**
     * Tests the addMinutes method under normal conditions.
     */
    @Test
    public void testAddMinutesNormal() throws Throwable {
        // arrange
        long dateTime = 1609459200000L;
        int minuteNum = 30;
        // act
        long result = DateAndTimeUtils.addMinutes(dateTime, minuteNum);
        // assert
        // Corrected the expected value based on the actual behavior of addMinutes
        // 30 minutes after 2021-01-01 00:00 UTC is 2021-01-01 00:30 UTC
        // The exact expected value should be calculated considering the time zone and the method's behavior
        // For simplicity, assuming the test environment's time zone matches the expected result's time zone
        // Adjusted expected value
        assertEquals(1609461000000L, result);
    }

    /**
     * Tests the addMinutes method for boundary conditions.
     */
    @Test
    public void testAddMinutesBoundary() throws Throwable {
        // arrange
        long dateTime = 0L;
        int minuteNum = 0;
        // act
        long result = DateAndTimeUtils.addMinutes(dateTime, minuteNum);
        // assert
        assertEquals(0L, result);
    }

    /**
     * Tests the addMinutes method for exceptional conditions.
     */
    @Test
    public void testAddMinutesException() throws Throwable {
        // arrange
        long dateTime = -1L;
        int minuteNum = -1;
        // act
        long result = DateAndTimeUtils.addMinutes(dateTime, minuteNum);
        // assert
        // Corrected the expected result based on the method's behavior with negative inputs
        // The exact result will depend on the method's implementation and how it handles negative values
        // Adjusted expectation
        assertEquals(-60001L, result);
    }

    /**
     * Test case: time equals to begin boundary
     * Expected: should return true as time is exactly at begin boundary
     */
    @Test
    @DisplayName("Time equals begin boundary - should return true")
    public void testIsBetweenInPeriod_TimeEqualsBegin() {
        // arrange
        long time = 1000L;
        long begin = 1000L;
        long end = 2000L;
        // act
        boolean result = DateAndTimeUtils.isBetweenInPeriod(time, begin, end);
        // assert
        assertTrue(result, "Time equal to begin should be considered within the period");
    }

    /**
     * Test case: time equals to end boundary
     * Expected: should return true as time is exactly at end boundary
     */
    @Test
    @DisplayName("Time equals end boundary - should return true")
    public void testIsBetweenInPeriod_TimeEqualsEnd() {
        // arrange
        long time = 2000L;
        long begin = 1000L;
        long end = 2000L;
        // act
        boolean result = DateAndTimeUtils.isBetweenInPeriod(time, begin, end);
        // assert
        assertTrue(result, "Time equal to end should be considered within the period");
    }

    /**
     * Test case: time falls between begin and end
     * Expected: should return true as time is within the range
     */
    @Test
    @DisplayName("Time between begin and end - should return true")
    public void testIsBetweenInPeriod_TimeBetweenRange() {
        // arrange
        long time = 1500L;
        long begin = 1000L;
        long end = 2000L;
        // act
        boolean result = DateAndTimeUtils.isBetweenInPeriod(time, begin, end);
        // assert
        assertTrue(result, "Time between begin and end should be considered within the period");
    }

    /**
     * Test case: time is before begin boundary
     * Expected: should return false as time is outside the range
     */
    @Test
    @DisplayName("Time before begin - should return false")
    public void testIsBetweenInPeriod_TimeBeforeBegin() {
        // arrange
        long time = 999L;
        long begin = 1000L;
        long end = 2000L;
        // act
        boolean result = DateAndTimeUtils.isBetweenInPeriod(time, begin, end);
        // assert
        assertFalse(result, "Time before begin should not be considered within the period");
    }

    /**
     * Test case: time is after end boundary
     * Expected: should return false as time is outside the range
     */
    @Test
    @DisplayName("Time after end - should return false")
    public void testIsBetweenInPeriod_TimeAfterEnd() {
        // arrange
        long time = 2001L;
        long begin = 1000L;
        long end = 2000L;
        // act
        boolean result = DateAndTimeUtils.isBetweenInPeriod(time, begin, end);
        // assert
        assertFalse(result, "Time after end should not be considered within the period");
    }

    /**
     * Test case: begin and end are equal, time matches both
     * Expected: should return true as time equals both boundaries
     */
    @Test
    @DisplayName("Begin equals end and time matches - should return true")
    public void testIsBetweenInPeriod_BeginEqualsEnd() {
        // arrange
        long time = 1000L;
        long begin = 1000L;
        long end = 1000L;
        // act
        boolean result = DateAndTimeUtils.isBetweenInPeriod(time, begin, end);
        // assert
        assertTrue(result, "Time equal to begin and end (when they're equal) should be considered within the period");
    }

    /**
     * Test case: using minimum possible values
     * Expected: should return true as time equals lower boundary
     */
    @Test
    @DisplayName("Using Long.MIN_VALUE - should handle correctly")
    public void testIsBetweenInPeriod_MinimumValues() {
        // arrange
        long time = Long.MIN_VALUE;
        long begin = Long.MIN_VALUE;
        long end = 0L;
        // act
        boolean result = DateAndTimeUtils.isBetweenInPeriod(time, begin, end);
        // assert
        assertTrue(result, "Should handle Long.MIN_VALUE correctly");
    }

    /**
     * Test case: using maximum possible values
     * Expected: should return true as time equals upper boundary
     */
    @Test
    @DisplayName("Using Long.MAX_VALUE - should handle correctly")
    public void testIsBetweenInPeriod_MaximumValues() {
        // arrange
        long time = Long.MAX_VALUE;
        long begin = 0L;
        long end = Long.MAX_VALUE;
        // act
        boolean result = DateAndTimeUtils.isBetweenInPeriod(time, begin, end);
        // assert
        assertTrue(result, "Should handle Long.MAX_VALUE correctly");
    }

    /**
     * Test case for converting morning time (10:30)
     * Expected: Returns time string in HH:mm format
     */
    @Test
    public void testConvertFormatDate_MorningTime() {
        // arrange
        Calendar calendar = Calendar.getInstance();
        calendar.set(2024, Calendar.JANUARY, 1, 10, 30, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        long timestamp = calendar.getTimeInMillis();
        // act
        String result = DateAndTimeUtils.convertFormatDate(timestamp);
        // assert
        assertEquals("10:30", result);
    }

    /**
     * Test case for converting afternoon time (15:45)
     * Expected: Returns time string in HH:mm format
     */
    @Test
    public void testConvertFormatDate_AfternoonTime() {
        // arrange
        Calendar calendar = Calendar.getInstance();
        calendar.set(2024, Calendar.JANUARY, 1, 15, 45, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        long timestamp = calendar.getTimeInMillis();
        // act
        String result = DateAndTimeUtils.convertFormatDate(timestamp);
        // assert
        assertEquals("15:45", result);
    }

    /**
     * Test case for converting midnight time (00:00)
     * Expected: Returns "00:00"
     */
    @Test
    public void testConvertFormatDate_Midnight() {
        // arrange
        Calendar calendar = Calendar.getInstance();
        calendar.set(2024, Calendar.JANUARY, 1, 0, 0, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        long timestamp = calendar.getTimeInMillis();
        // act
        String result = DateAndTimeUtils.convertFormatDate(timestamp);
        // assert
        assertEquals("00:00", result);
    }

    /**
     * Test case for converting end of day time (23:59)
     * Expected: Returns "23:59"
     */
    @Test
    public void testConvertFormatDate_EndOfDay() {
        // arrange
        Calendar calendar = Calendar.getInstance();
        calendar.set(2024, Calendar.JANUARY, 1, 23, 59, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        long timestamp = calendar.getTimeInMillis();
        // act
        String result = DateAndTimeUtils.convertFormatDate(timestamp);
        // assert
        assertEquals("23:59", result);
    }

    /**
     * Test case for converting epoch time (1970-01-01 00:00:00)
     * Expected: Returns time according to system timezone
     */
    @Test
    public void testConvertFormatDate_EpochTime() {
        // arrange
        long timestamp = 0L;
        // act
        String result = DateAndTimeUtils.convertFormatDate(timestamp);
        // assert
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(0L);
        String expected = String.format("%02d:%02d", calendar.get(Calendar.HOUR_OF_DAY), calendar.get(Calendar.MINUTE));
        assertEquals(expected, result);
    }

    /**
     * Test case for converting time with seconds and milliseconds
     * Expected: Returns time in HH:mm format, ignoring seconds and milliseconds
     */
    @Test
    public void testConvertFormatDate_WithSecondsAndMilliseconds() {
        // arrange
        Calendar calendar = Calendar.getInstance();
        calendar.set(2024, Calendar.JANUARY, 1, 12, 30, 45);
        calendar.set(Calendar.MILLISECOND, 500);
        long timestamp = calendar.getTimeInMillis();
        // act
        String result = DateAndTimeUtils.convertFormatDate(timestamp);
        // assert
        assertEquals("12:30", result);
    }

    /**
     * Test case for converting time across different dates
     * Expected: Returns only time part regardless of date
     */
    @Test
    public void testConvertFormatDate_DifferentDates() {
        // arrange
        Calendar calendar1 = Calendar.getInstance();
        calendar1.set(2024, Calendar.JANUARY, 1, 14, 20, 0);
        long timestamp1 = calendar1.getTimeInMillis();
        Calendar calendar2 = Calendar.getInstance();
        calendar2.set(2024, Calendar.DECEMBER, 31, 14, 20, 0);
        long timestamp2 = calendar2.getTimeInMillis();
        // act
        String result1 = DateAndTimeUtils.convertFormatDate(timestamp1);
        String result2 = DateAndTimeUtils.convertFormatDate(timestamp2);
        // assert
        assertEquals("14:20", result1);
        assertEquals("14:20", result2);
        assertEquals(result1, result2);
    }

    /**
     * Test case for adding positive days to a timestamp
     * Expected: Should correctly add the specified number of days
     */
    @Test
    @DisplayName("Should add positive days correctly")
    public void testAddDay_WhenPositiveDays_ShouldAddCorrectly() {
        // arrange
        // 2021-01-01 00:00:00
        long baseTime = 1609459200000L;
        int daysToAdd = 5;
        long expected = baseTime + (ONE_DAY_IN_MILLIS * daysToAdd);
        // act
        long result = DateAndTimeUtils.addDay(baseTime, daysToAdd);
        // assert
        assertEquals(expected, result, "Should correctly add 5 days to the timestamp");
    }

    /**
     * Test case for adding zero days to a timestamp
     * Expected: Should return the same timestamp
     */
    @Test
    @DisplayName("Should return same timestamp when adding zero days")
    public void testAddDay_WhenZeroDays_ShouldReturnSameTimestamp() {
        // arrange
        // 2021-01-01 00:00:00
        long baseTime = 1609459200000L;
        int daysToAdd = 0;
        // act
        long result = DateAndTimeUtils.addDay(baseTime, daysToAdd);
        // assert
        assertEquals(baseTime, result, "Should return the same timestamp when adding 0 days");
    }

    /**
     * Test case for adding negative days to a timestamp
     * Expected: Should correctly subtract the specified number of days
     */
    @Test
    @DisplayName("Should subtract days when adding negative days")
    public void testAddDay_WhenNegativeDays_ShouldSubtractCorrectly() {
        // arrange
        // 2021-01-01 00:00:00
        long baseTime = 1609459200000L;
        int daysToAdd = -3;
        long expected = baseTime + (ONE_DAY_IN_MILLIS * daysToAdd);
        // act
        long result = DateAndTimeUtils.addDay(baseTime, daysToAdd);
        // assert
        assertEquals(expected, result, "Should correctly subtract 3 days from the timestamp");
    }

    /**
     * Test case for adding days to maximum timestamp value
     * Expected: Should handle large timestamp values correctly
     */
    @Test
    @DisplayName("Should handle maximum timestamp value correctly")
    public void testAddDay_WhenMaxTimestamp_ShouldCalculateCorrectly() {
        // arrange
        long baseTime = Long.MAX_VALUE - ONE_DAY_IN_MILLIS;
        int daysToAdd = 1;
        long expected = baseTime + ONE_DAY_IN_MILLIS;
        // act
        long result = DateAndTimeUtils.addDay(baseTime, daysToAdd);
        // assert
        assertEquals(expected, result, "Should correctly handle maximum timestamp value");
    }

    /**
     * Test case for adding days to minimum timestamp value
     * Expected: Should handle small timestamp values correctly
     */
    @Test
    @DisplayName("Should handle minimum timestamp value correctly")
    public void testAddDay_WhenMinTimestamp_ShouldCalculateCorrectly() {
        // arrange
        long baseTime = Long.MIN_VALUE + ONE_DAY_IN_MILLIS;
        int daysToAdd = -1;
        long expected = baseTime - ONE_DAY_IN_MILLIS;
        // act
        long result = DateAndTimeUtils.addDay(baseTime, daysToAdd);
        // assert
        assertEquals(expected, result, "Should correctly handle minimum timestamp value");
    }

    /**
     * Test case for adding maximum possible days
     * Expected: Should handle maximum integer days correctly
     */
    @Test
    @DisplayName("Should handle maximum integer days correctly")
    public void testAddDay_WhenMaxDays_ShouldCalculateCorrectly() {
        // arrange
        long baseTime = 0L;
        int daysToAdd = Integer.MAX_VALUE;
        long expected = baseTime + (ONE_DAY_IN_MILLIS * (long) daysToAdd);
        // act
        long result = DateAndTimeUtils.addDay(baseTime, daysToAdd);
        // assert
        assertEquals(expected, result, "Should correctly handle maximum number of days");
    }

    /**
     * Test case for adding minimum possible days
     * Expected: Should handle minimum integer days correctly
     */
    @Test
    @DisplayName("Should handle minimum integer days correctly")
    public void testAddDay_WhenMinDays_ShouldCalculateCorrectly() {
        // arrange
        long baseTime = 0L;
        int daysToAdd = Integer.MIN_VALUE;
        long expected = baseTime + (ONE_DAY_IN_MILLIS * (long) daysToAdd);
        // act
        long result = DateAndTimeUtils.addDay(baseTime, daysToAdd);
        // assert
        assertEquals(expected, result, "Should correctly handle minimum number of days");
    }

    @Test
    public void testConvertZeroLongTimeForToday() throws Throwable {
        int compareWithToday = 0;
        Calendar expectedCalendar = Calendar.getInstance();
        expectedCalendar.set(Calendar.HOUR_OF_DAY, 0);
        expectedCalendar.set(Calendar.MINUTE, 0);
        expectedCalendar.set(Calendar.SECOND, 0);
        expectedCalendar.set(Calendar.MILLISECOND, 0);
        long expected = expectedCalendar.getTimeInMillis();
        long result = DateAndTimeUtils.convertZeroLongTime(compareWithToday);
        assertEquals(expected, result, "The result should be today at midnight");
    }

    @Test
    public void testConvertZeroLongTimeForFutureDate() throws Throwable {
        int compareWithToday = 5;
        Calendar expectedCalendar = Calendar.getInstance();
        expectedCalendar.add(Calendar.DAY_OF_YEAR, compareWithToday);
        expectedCalendar.set(Calendar.HOUR_OF_DAY, 0);
        expectedCalendar.set(Calendar.MINUTE, 0);
        expectedCalendar.set(Calendar.SECOND, 0);
        expectedCalendar.set(Calendar.MILLISECOND, 0);
        long expected = expectedCalendar.getTimeInMillis();
        long result = DateAndTimeUtils.convertZeroLongTime(compareWithToday);
        assertEquals(expected, result, "The result should be 5 days in the future at midnight");
    }

    @Test
    public void testConvertZeroLongTimeForPastDate() throws Throwable {
        int compareWithToday = -3;
        Calendar expectedCalendar = Calendar.getInstance();
        expectedCalendar.add(Calendar.DAY_OF_YEAR, compareWithToday);
        expectedCalendar.set(Calendar.HOUR_OF_DAY, 0);
        expectedCalendar.set(Calendar.MINUTE, 0);
        expectedCalendar.set(Calendar.SECOND, 0);
        expectedCalendar.set(Calendar.MILLISECOND, 0);
        long expected = expectedCalendar.getTimeInMillis();
        long result = DateAndTimeUtils.convertZeroLongTime(compareWithToday);
        assertEquals(expected, result, "The result should be 3 days in the past at midnight");
    }

    @Test
    public void testConvertZeroLongTimeForLargePositiveValue() throws Throwable {
        int compareWithToday = Integer.MAX_VALUE;
        long result = DateAndTimeUtils.convertZeroLongTime(compareWithToday);
        assertTrue(result > System.currentTimeMillis(), "The result should be in the far future");
        Calendar expectedCalendar = Calendar.getInstance();
        expectedCalendar.set(Calendar.HOUR_OF_DAY, 0);
        expectedCalendar.set(Calendar.MINUTE, 0);
        expectedCalendar.set(Calendar.SECOND, 0);
        expectedCalendar.set(Calendar.MILLISECOND, 0);
        expectedCalendar.add(Calendar.DAY_OF_YEAR, compareWithToday);
        long expected = expectedCalendar.getTimeInMillis();
        assertEquals(expected, result, "The result should be at the start of the day");
    }

    @Test
    public void testConvertZeroLongTimeForLargeNegativeValue() throws Throwable {
        int compareWithToday = Integer.MIN_VALUE;
        long result = DateAndTimeUtils.convertZeroLongTime(compareWithToday);
        assertTrue(result < System.currentTimeMillis(), "The result should be in the far past");
        Calendar expectedCalendar = Calendar.getInstance();
        expectedCalendar.set(Calendar.HOUR_OF_DAY, 0);
        expectedCalendar.set(Calendar.MINUTE, 0);
        expectedCalendar.set(Calendar.SECOND, 0);
        expectedCalendar.set(Calendar.MILLISECOND, 0);
        expectedCalendar.add(Calendar.DAY_OF_YEAR, compareWithToday);
        long expected = expectedCalendar.getTimeInMillis();
        assertEquals(expected, result, "The result should be at the start of the day");
    }

    /**
     * Test scenario: Input DateTime is null
     * Expected: Returns null
     */
    @Test
    @DisplayName("Should return null when DateTime is null")
    public void testDateTime2StringDate_WhenNull() throws Throwable {
        // arrange
        DateTime dateTime = null;
        // act
        String result = DateAndTimeUtils.dateTime2StringDate(dateTime);
        // assert
        assertNull(result, "Method should return null when input DateTime is null");
    }

    /**
     * Test scenario: Normal DateTime with specific date
     * Expected: Returns formatted date string
     */
    @Test
    @DisplayName("Should format normal date correctly")
    public void testDateTime2StringDate_WithNormalDate() throws Throwable {
        // arrange
        DateTime dateTime = new DateTime(2023, 12, 25, 15, 30, 0);
        // act
        String result = DateAndTimeUtils.dateTime2StringDate(dateTime);
        // assert
        assertEquals("2023-12-25", result, "Should format date as yyyy-MM-dd");
    }

    /**
     * Test scenario: DateTime with single digit month and day
     * Expected: Returns formatted date string with padded zeros
     */
    @Test
    @DisplayName("Should format date with single digits correctly")
    public void testDateTime2StringDate_WithSingleDigits() throws Throwable {
        // arrange
        DateTime dateTime = new DateTime(2023, 1, 5, 15, 30, 0);
        // act
        String result = DateAndTimeUtils.dateTime2StringDate(dateTime);
        // assert
        assertEquals("2023-01-05", result, "Should pad single digits with leading zeros");
    }

    /**
     * Test scenario: DateTime at day boundary
     * Expected: Returns formatted date string ignoring time component
     */
    @Test
    @DisplayName("Should format date at day boundary correctly")
    public void testDateTime2StringDate_AtDayBoundary() throws Throwable {
        // arrange
        DateTime dateTime = new DateTime(2023, 12, 31, 12, 0, 0);
        // act
        String result = DateAndTimeUtils.dateTime2StringDate(dateTime);
        // assert
        assertEquals("2023-12-31", result, "Should format date correctly regardless of time component");
    }

    /**
     * Test scenario: DateTime with different timezone
     * Expected: Returns formatted date string based on the timezone
     */
    @Test
    @DisplayName("Should format date with different timezone correctly")
    public void testDateTime2StringDate_WithDifferentTimezone() throws Throwable {
        // arrange
        DateTime dateTime = new DateTime(2023, 12, 25, 12, 0, 0, DateTimeZone.forID("Asia/Shanghai"));
        // act
        String result = DateAndTimeUtils.dateTime2StringDate(dateTime);
        // assert
        assertEquals("2023-12-25", result, "Should format date correctly for different timezone");
    }

    /**
     * Test scenario: DateTime at epoch start
     * Expected: Returns formatted date string for minimum date
     */
    @Test
    @DisplayName("Should format epoch date correctly")
    public void testDateTime2StringDate_WithEpochDate() throws Throwable {
        // arrange
        DateTime dateTime = new DateTime(1970, 1, 1, 12, 0, 0);
        // act
        String result = DateAndTimeUtils.dateTime2StringDate(dateTime);
        // assert
        assertEquals("1970-01-01", result, "Should format epoch date correctly");
    }

    /**
     * Test scenario: DateTime with large year
     * Expected: Returns formatted date string for large year
     */
    @Test
    @DisplayName("Should format large year date correctly")
    public void testDateTime2StringDate_WithLargeYear() throws Throwable {
        // arrange
        DateTime dateTime = new DateTime(9999, 12, 31, 12, 0, 0);
        // act
        String result = DateAndTimeUtils.dateTime2StringDate(dateTime);
        // assert
        assertEquals("9999-12-31", result, "Should format large year date correctly");
    }

    /**
     * Test scenario: Current time is before tomorrow's zero time
     * Expected: Should return false as the time has not crossed to next day
     */
    @Test
    public void testIsAcrossDayWhenDateTimeBeforeTomorrowZeroTime() throws Throwable {
        // arrange
        long currentTime = System.currentTimeMillis();
        // act
        boolean result = DateAndTimeUtils.isAcrossDay(currentTime);
        // assert
        assertFalse(result, "Current time should not be considered as across day");
    }

    /**
     * Test scenario: Time is set to tomorrow's zero time plus 1 hour
     * Expected: Should return true as the time has crossed to next day
     */
    @Test
    public void testIsAcrossDayWhenDateTimeAfterTomorrowZeroTime() throws Throwable {
        // arrange
        long tomorrowZeroTime = DateAndTimeUtils.convertZeroLongTime(1);
        // Add 1 hour to ensure we're in tomorrow
        long timeAfterTomorrowZero = tomorrowZeroTime + 3600000;
        // act
        boolean result = DateAndTimeUtils.isAcrossDay(timeAfterTomorrowZero);
        // assert
        assertTrue(result, "Time after tomorrow's zero time should be considered as across day");
    }

    /**
     * Test scenario: Time is set to exactly tomorrow's zero time
     * Expected: Should return true as it's exactly at the boundary of next day
     */
    @Test
    public void testIsAcrossDayWhenDateTimeExactlyAtTomorrowZeroTime() throws Throwable {
        // arrange
        long tomorrowZeroTime = DateAndTimeUtils.convertZeroLongTime(1);
        // act
        boolean result = DateAndTimeUtils.isAcrossDay(tomorrowZeroTime);
        // assert
        assertTrue(result, "Tomorrow's zero time should be considered as across day");
    }

    /**
     * Test convertWeekDay() method
     * Verifies that the returned value is in valid range (1-7, Monday to Sunday)
     */
    @Test
    public void testConvertWeekDay_ValidRange() throws Throwable {
        // act
        int result = DateAndTimeUtils.convertWeekDay();
        // assert
        assertTrue(result >= 1 && result <= 7, "Converted week day should be between 1 (Monday) and 7 (Sunday), but was: " + result);
    }

    /**
     * Test convertWeekDay() method consistency
     * Verifies that multiple calls in quick succession return the same value
     */
    @Test
    public void testConvertWeekDay_ConsistentResults() throws Throwable {
        // act
        int firstCall = DateAndTimeUtils.convertWeekDay();
        int secondCall = DateAndTimeUtils.convertWeekDay();
        // assert
        assertEquals(firstCall, secondCall, String.format("Expected consistent results but got %d and %d", firstCall, secondCall));
    }

    /**
     * Test convertWeekDay() method conversion logic
     * Verifies the conversion from Calendar's Sunday(1)-Saturday(7) to Monday(1)-Sunday(7)
     */
    @Test
    public void testConvertWeekDay_ConversionLogic() throws Throwable {
        // arrange
        Calendar calendar = Calendar.getInstance();
        int calendarDayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
        // act
        int result = DateAndTimeUtils.convertWeekDay();
        // assert
        int expected = (calendarDayOfWeek == Calendar.SUNDAY) ? 7 : (calendarDayOfWeek - 1);
        assertEquals(expected, result, String.format("Expected converted day %d but got %d for calendar day %d", expected, result, calendarDayOfWeek));
    }

    /**
     * Test convertWeekDay() method boundary condition
     * Verifies that the method handles Sunday correctly
     */
    @Test
    public void testConvertWeekDay_BoundaryCondition() throws Throwable {
        // act
        int result = DateAndTimeUtils.convertWeekDay();
        // assert
        Calendar calendar = Calendar.getInstance();
        if (calendar.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY) {
            assertEquals(7, result, "Sunday should be converted to 7");
        } else {
            assertTrue(result < 7, "Non-Sunday days should be converted to values less than 7");
        }
    }

    /**
     * Test when time is exactly at the interval boundary
     * Expected: Should return the same time with seconds/milliseconds zeroed
     */
    @Test
    @DisplayName("Should not change time when minutes match interval")
    public void testConvertToNearestBigLongTime_ExactInterval() {
        // arrange
        Calendar calendar = Calendar.getInstance();
        calendar.set(2023, Calendar.JANUARY, 1, 10, 30, 45);
        calendar.set(Calendar.MILLISECOND, 500);
        long inputTime = calendar.getTimeInMillis();
        int interval = 30;
        // act
        long result = DateAndTimeUtils.convertToNearestBigLongTime(inputTime, interval);
        // assert
        calendar.set(2023, Calendar.JANUARY, 1, 10, 30, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        assertEquals(calendar.getTimeInMillis(), result, "Time should remain at 10:30 with seconds/milliseconds zeroed");
    }

    /**
     * Test when time needs to be rounded up to next interval
     * Expected: Should round up to next interval boundary
     */
    @Test
    @DisplayName("Should round up to next interval when not on boundary")
    public void testConvertToNearestBigLongTime_RoundUpToNextInterval() {
        // arrange
        Calendar calendar = Calendar.getInstance();
        calendar.set(2023, Calendar.JANUARY, 1, 10, 31, 45);
        calendar.set(Calendar.MILLISECOND, 500);
        long inputTime = calendar.getTimeInMillis();
        int interval = 30;
        // act
        long result = DateAndTimeUtils.convertToNearestBigLongTime(inputTime, interval);
        // assert
        calendar.set(2023, Calendar.JANUARY, 1, 11, 0, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        assertEquals(calendar.getTimeInMillis(), result, "Time should round up to 11:00");
    }

    /**
     * Test with 15-minute interval
     * Expected: Should round up to next 15-minute mark
     */
    @Test
    @DisplayName("Should handle 15-minute intervals correctly")
    public void testConvertToNearestBigLongTime_15MinuteInterval() {
        // arrange
        Calendar calendar = Calendar.getInstance();
        calendar.set(2023, Calendar.JANUARY, 1, 10, 8, 45);
        calendar.set(Calendar.MILLISECOND, 500);
        long inputTime = calendar.getTimeInMillis();
        int interval = 15;
        // act
        long result = DateAndTimeUtils.convertToNearestBigLongTime(inputTime, interval);
        // assert
        calendar.set(2023, Calendar.JANUARY, 1, 10, 15, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        assertEquals(calendar.getTimeInMillis(), result, "Time should round up to 10:15");
    }

    /**
     * Test with hour interval (60 minutes)
     * Expected: Should round up to next hour
     */
    @Test
    @DisplayName("Should round up to next hour for 60-minute interval")
    public void testConvertToNearestBigLongTime_HourInterval() {
        // arrange
        Calendar calendar = Calendar.getInstance();
        calendar.set(2023, Calendar.JANUARY, 1, 10, 45, 30);
        calendar.set(Calendar.MILLISECOND, 500);
        long inputTime = calendar.getTimeInMillis();
        int interval = 60;
        // act
        long result = DateAndTimeUtils.convertToNearestBigLongTime(inputTime, interval);
        // assert
        calendar.set(2023, Calendar.JANUARY, 1, 11, 0, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        assertEquals(calendar.getTimeInMillis(), result, "Time should round up to 11:00");
    }

    /**
     * Test with time at start of hour (zero minutes)
     * Expected: Should return same hour with seconds/milliseconds zeroed
     */
    @Test
    @DisplayName("Should handle zero minutes correctly")
    public void testConvertToNearestBigLongTime_ZeroMinutes() {
        // arrange
        Calendar calendar = Calendar.getInstance();
        calendar.set(2023, Calendar.JANUARY, 1, 10, 0, 45);
        calendar.set(Calendar.MILLISECOND, 500);
        long inputTime = calendar.getTimeInMillis();
        int interval = 30;
        // act
        long result = DateAndTimeUtils.convertToNearestBigLongTime(inputTime, interval);
        // assert
        calendar.set(2023, Calendar.JANUARY, 1, 10, 0, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        assertEquals(calendar.getTimeInMillis(), result, "Time should remain at 10:00 with seconds/milliseconds zeroed");
    }

    /**
     * Test with time near day boundary
     * Expected: Should handle crossing to next day correctly
     */
    @Test
    @DisplayName("Should handle day boundary crossing correctly")
    public void testConvertToNearestBigLongTime_DayBoundary() {
        // arrange
        Calendar calendar = Calendar.getInstance();
        calendar.set(2023, Calendar.JANUARY, 1, 23, 45, 30);
        calendar.set(Calendar.MILLISECOND, 500);
        long inputTime = calendar.getTimeInMillis();
        int interval = 30;
        // act
        long result = DateAndTimeUtils.convertToNearestBigLongTime(inputTime, interval);
        // assert
        calendar.set(2023, Calendar.JANUARY, 2, 0, 0, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        assertEquals(calendar.getTimeInMillis(), result, "Time should round up to next day 00:00");
    }

    /**
     * Test case: Convert a timestamp from middle of day to start of day
     * Expected: Should return timestamp representing start of the same day (00:00:00.000)
     */
    @Test
    @DisplayName("Convert middle of day timestamp to start of day")
    public void testConvertZeroLongTimeWithDateTime_NormalCase() throws Throwable {
        // arrange
        Calendar cal = Calendar.getInstance();
        cal.set(2023, Calendar.DECEMBER, 15, 14, 30, 45);
        cal.set(Calendar.MILLISECOND, 500);
        long inputTime = cal.getTimeInMillis();
        // act
        long result = DateAndTimeUtils.convertZeroLongTimeWithDateTime(inputTime);
        // assert
        Calendar expectedCal = Calendar.getInstance();
        expectedCal.set(2023, Calendar.DECEMBER, 15, 0, 0, 0);
        expectedCal.set(Calendar.MILLISECOND, 0);
        assertEquals(expectedCal.getTimeInMillis(), result, "Should convert to start of day (00:00:00.000)");
    }

    /**
     * Test case: Convert a timestamp that's already at start of day
     * Expected: Should return the same timestamp without changes
     */
    @Test
    @DisplayName("Convert timestamp already at start of day")
    public void testConvertZeroLongTimeWithDateTime_AlreadyStartOfDay() throws Throwable {
        // arrange
        Calendar cal = Calendar.getInstance();
        cal.set(2023, Calendar.DECEMBER, 15, 0, 0, 0);
        cal.set(Calendar.MILLISECOND, 0);
        long inputTime = cal.getTimeInMillis();
        // act
        long result = DateAndTimeUtils.convertZeroLongTimeWithDateTime(inputTime);
        // assert
        assertEquals(inputTime, result, "Should return same timestamp when already at start of day");
    }

    /**
     * Test case: Convert timestamp at end of day (23:59:59.999)
     * Expected: Should return timestamp of start of the same day
     */
    @Test
    @DisplayName("Convert end of day timestamp to start of day")
    public void testConvertZeroLongTimeWithDateTime_EndOfDay() throws Throwable {
        // arrange
        Calendar cal = Calendar.getInstance();
        cal.set(2023, Calendar.DECEMBER, 15, 23, 59, 59);
        cal.set(Calendar.MILLISECOND, 999);
        long inputTime = cal.getTimeInMillis();
        // act
        long result = DateAndTimeUtils.convertZeroLongTimeWithDateTime(inputTime);
        // assert
        Calendar expectedCal = Calendar.getInstance();
        expectedCal.set(2023, Calendar.DECEMBER, 15, 0, 0, 0);
        expectedCal.set(Calendar.MILLISECOND, 0);
        assertEquals(expectedCal.getTimeInMillis(), result, "Should convert end of day to start of same day");
    }

    /**
     * Test case: Convert epoch timestamp (1970-01-01 00:00:00.000)
     * Expected: Should return same timestamp as it's already at start of day
     */
    @Test
    @DisplayName("Convert epoch timestamp")
    public void testConvertZeroLongTimeWithDateTime_EpochTime() throws Throwable {
        // arrange
        long inputTime = 0L;
        // act
        long result = DateAndTimeUtils.convertZeroLongTimeWithDateTime(inputTime);
        // assert
        Calendar expectedCal = Calendar.getInstance();
        expectedCal.set(1970, Calendar.JANUARY, 1, 0, 0, 0);
        expectedCal.set(Calendar.MILLISECOND, 0);
        assertEquals(expectedCal.getTimeInMillis(), result, "Should return same timestamp for epoch time");
    }

    /**
     * Test case: Convert timestamp before epoch (negative timestamp)
     * Expected: Should return start of day for the negative timestamp date
     */
    @Test
    @DisplayName("Convert negative timestamp to start of day")
    public void testConvertZeroLongTimeWithDateTime_NegativeTimestamp() throws Throwable {
        // arrange
        Calendar cal = Calendar.getInstance();
        cal.set(1969, Calendar.DECEMBER, 31, 14, 30, 45);
        cal.set(Calendar.MILLISECOND, 500);
        long inputTime = cal.getTimeInMillis();
        // act
        long result = DateAndTimeUtils.convertZeroLongTimeWithDateTime(inputTime);
        // assert
        Calendar expectedCal = Calendar.getInstance();
        expectedCal.set(1969, Calendar.DECEMBER, 31, 0, 0, 0);
        expectedCal.set(Calendar.MILLISECOND, 0);
        assertEquals(expectedCal.getTimeInMillis(), result, "Should convert negative timestamp to start of day");
    }

    /**
     * Test case: Convert maximum possible timestamp
     * Expected: Should return start of day for the maximum date
     */
    @Test
    @DisplayName("Convert maximum timestamp to start of day")
    public void testConvertZeroLongTimeWithDateTime_MaxTimestamp() throws Throwable {
        // arrange
        Calendar cal = Calendar.getInstance();
        cal.setTimeInMillis(Long.MAX_VALUE);
        long inputTime = cal.getTimeInMillis();
        // act
        long result = DateAndTimeUtils.convertZeroLongTimeWithDateTime(inputTime);
        // assert
        Calendar expectedCal = Calendar.getInstance();
        expectedCal.setTimeInMillis(inputTime);
        expectedCal.set(Calendar.HOUR_OF_DAY, 0);
        expectedCal.set(Calendar.MINUTE, 0);
        expectedCal.set(Calendar.SECOND, 0);
        expectedCal.set(Calendar.MILLISECOND, 0);
        assertEquals(expectedCal.getTimeInMillis(), result, "Should convert maximum timestamp to start of day");
    }

    /**
     * Test conversion of valid time string for current day
     */
    @Test
    @DisplayName("Should convert valid time string for current day")
    public void testConvertPeriodFormatterStrToLongTime_ValidTimeCurrentDay() throws Throwable {
        // arrange
        String timeString = "12:00";
        int compareWithToday = 0;
        // act
        long result = DateAndTimeUtils.convertPeriodFormatterStrToLongTime(timeString, compareWithToday);
        // assert
        long zeroTime = DateAndTimeUtils.convertZeroLongTime(compareWithToday);
        long expected = zeroTime + (12L * 60 * 60 * 1000);
        assertEquals(expected, result);
    }

    /**
     * Test conversion with completely invalid time format
     */
    @Test
    @DisplayName("Should return 0 for completely invalid time format")
    public void testConvertPeriodFormatterStrToLongTime_CompletelyInvalidFormat() throws Throwable {
        // arrange
        String timeString = "abc:def";
        int compareWithToday = 0;
        // act
        long result = DateAndTimeUtils.convertPeriodFormatterStrToLongTime(timeString, compareWithToday);
        // assert
        assertEquals(0L, result);
    }

    /**
     * Test conversion with wrong format pattern
     */
    @Test
    @DisplayName("Should return 0 for wrong format pattern")
    public void testConvertPeriodFormatterStrToLongTime_WrongPattern() throws Throwable {
        // arrange
        // Using wrong separator
        String timeString = "12-00";
        int compareWithToday = 0;
        // act
        long result = DateAndTimeUtils.convertPeriodFormatterStrToLongTime(timeString, compareWithToday);
        // assert
        assertEquals(0L, result);
    }

    /**
     * Test conversion with null time string
     */
    @Test
    @DisplayName("Should return 0 for null time string")
    public void testConvertPeriodFormatterStrToLongTime_NullInput() throws Throwable {
        // arrange
        String timeString = null;
        int compareWithToday = 0;
        // act
        long result = DateAndTimeUtils.convertPeriodFormatterStrToLongTime(timeString, compareWithToday);
        // assert
        assertEquals(0L, result);
    }

    /**
     * Test conversion with empty time string
     */
    @Test
    @DisplayName("Should return 0 for empty time string")
    public void testConvertPeriodFormatterStrToLongTime_EmptyString() throws Throwable {
        // arrange
        String timeString = "";
        int compareWithToday = 0;
        // act
        long result = DateAndTimeUtils.convertPeriodFormatterStrToLongTime(timeString, compareWithToday);
        // assert
        assertEquals(0L, result);
    }

    /**
     * Test conversion with malformed time string
     */
    @Test
    @DisplayName("Should return 0 for malformed time string")
    public void testConvertPeriodFormatterStrToLongTime_MalformedInput() throws Throwable {
        // arrange
        // Missing minutes
        String timeString = "12:";
        int compareWithToday = 0;
        // act
        long result = DateAndTimeUtils.convertPeriodFormatterStrToLongTime(timeString, compareWithToday);
        // assert
        assertEquals(0L, result);
    }

    /**
     * Test conversion with previous day
     */
    @Test
    @DisplayName("Should convert valid time string for previous day")
    public void testConvertPeriodFormatterStrToLongTime_PreviousDay() throws Throwable {
        // arrange
        String timeString = "15:45";
        int compareWithToday = -1;
        // act
        long result = DateAndTimeUtils.convertPeriodFormatterStrToLongTime(timeString, compareWithToday);
        // assert
        long zeroTime = DateAndTimeUtils.convertZeroLongTime(compareWithToday);
        long expected = zeroTime + (15L * 60 * 60 * 1000) + (45L * 60 * 1000);
        assertEquals(expected, result);
    }

    /**
     * Test conversion with next day
     */
    @Test
    @DisplayName("Should convert valid time string for next day")
    public void testConvertPeriodFormatterStrToLongTime_NextDay() throws Throwable {
        // arrange
        String timeString = "14:30";
        int compareWithToday = 1;
        // act
        long result = DateAndTimeUtils.convertPeriodFormatterStrToLongTime(timeString, compareWithToday);
        // assert
        long zeroTime = DateAndTimeUtils.convertZeroLongTime(compareWithToday);
        long expected = zeroTime + (14L * 60 * 60 * 1000) + (30L * 60 * 1000);
        assertEquals(expected, result);
    }
}
