package com.sankuai.dzshoppingguide.product.detail.spi.promo.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import lombok.Data;

import java.io.Serializable;

@TypeDoc(description = "优惠栏模块")
@Data
@MobileDo(id = 0x122e)
public class PromotionBarModule implements Serializable {

    @FieldDoc(description = "标签图标")
    @MobileField(key = 0x4e4c)
    private String labelIcon;

    @FieldDoc(description = "标签文案描述")
    @MobileField(key = 0x8c1a)
    private String labelDesc;

    @FieldDoc(description = "标签类型主题")
    @MobileField(key = 0xa23b)
    private String labelTheme;

    @FieldDoc(description = "icon高度")
    @MobileField(key = 0x75c3)
    private int iconHeight;

    @FieldDoc(description = "icon宽度")
    @MobileField(key = 0x4864)
    private int iconWidth;
}