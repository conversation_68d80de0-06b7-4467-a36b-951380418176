package com.sankuai.dzshoppingguide.product.detail.application.service.promo;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.sankuai.dzshoppingguide.product.detail.application.builder.promodetail.dto.PromotionDetailParams;
import com.sankuai.dzshoppingguide.product.detail.application.builder.promodetail.enums.CouponStatusEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.promodetail.enums.CouponStyleEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.promodetail.enums.CouponTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.promo.vo.CouponModule;
import com.sankuai.dzshoppingguide.product.detail.spi.promo.vo.DealGift;
import groovyjarjarantlr.collections.List;
import org.apache.commons.collections.CollectionUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class PromoDetailServiceBuildBuyGiftActivityTest {

    @Spy
    @InjectMocks
    private PromoDetailService promoDetailService;

    private PromotionDetailParams params;

    private DealGift dealGift;

    @BeforeEach
    void setUp() {
        params = new PromotionDetailParams();
        dealGift = new DealGift();
    }

    @Test
    void testBuildBuyGiftActivityWhenParamsIsNull() throws Throwable {
        // arrange
        doReturn(null).when(promoDetailService).buildBuyGiftActivity(null);
        // act
        java.util.List<CouponModule> result = promoDetailService.buildBuyGiftActivity(null);
        // assert
        assertNull(result);
        verify(promoDetailService, times(1)).buildBuyGiftActivity(null);
    }

    @Test
    void testBuildBuyGiftActivityWhenDealGiftsIsNull() throws Throwable {
        // arrange
        params.setDealGifts(null);
        // act
        java.util.List<CouponModule> result = promoDetailService.buildBuyGiftActivity(params);
        // assert
        assertNull(result);
        verify(promoDetailService, times(1)).buildBuyGiftActivity(params);
    }

    @Test
    void testBuildBuyGiftActivityWhenDealGiftsIsEmpty() throws Throwable {
        // arrange
        params.setDealGifts(new java.util.ArrayList<>());
        // act
        java.util.List<CouponModule> result = promoDetailService.buildBuyGiftActivity(params);
        // assert
        assertNull(result);
        verify(promoDetailService, times(1)).buildBuyGiftActivity(params);
    }

    @Test
    void testBuildBuyGiftActivityWithSingleDealGiftHavingOnlyUseRule() throws Throwable {
        // arrange
        dealGift.setTitle("Test Title");
        dealGift.setProductTag("Test Tag");
        dealGift.setUseRule("Valid for all users");
        dealGift.setThumbnail("thumbnail_url");
        dealGift.setCouponNum(5);
        dealGift.setActivityId(12345L);
        java.util.List<DealGift> dealGifts = new java.util.ArrayList<>();
        dealGifts.add(dealGift);
        params.setDealGifts(dealGifts);
        // act
        java.util.List<CouponModule> result = promoDetailService.buildBuyGiftActivity(params);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        CouponModule module = result.get(0);
        assertEquals(1, module.getCouponSubTitle().size());
        assertEquals("规则：Valid for all users", module.getCouponSubTitle().get(0).getContent());
        assertEquals("Test Title", module.getCouponTitle().get(0).getContent());
        assertEquals("Test Tag", module.getCouponTag().getContent());
        assertEquals("thumbnail_url", module.getCouponAvatar());
        assertEquals(5, module.getCouponNum());
        assertEquals("12345", module.getApplyId());
        verify(promoDetailService, times(1)).buildBuyGiftActivity(params);
    }

    @Test
    void testBuildBuyGiftActivityWithSingleDealGiftHavingNoTimeDescOrUseRule() throws Throwable {
        // arrange
        dealGift.setTitle("Test Title");
        dealGift.setProductTag("Test Tag");
        dealGift.setThumbnail("thumbnail_url");
        dealGift.setCouponNum(5);
        dealGift.setActivityId(12345L);
        java.util.List<DealGift> dealGifts = new java.util.ArrayList<>();
        dealGifts.add(dealGift);
        params.setDealGifts(dealGifts);
        // act
        java.util.List<CouponModule> result = promoDetailService.buildBuyGiftActivity(params);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        CouponModule module = result.get(0);
        assertNull(module.getCouponSubTitle());
        assertEquals("Test Title", module.getCouponTitle().get(0).getContent());
        assertEquals("Test Tag", module.getCouponTag().getContent());
        assertEquals("thumbnail_url", module.getCouponAvatar());
        assertEquals(5, module.getCouponNum());
        assertEquals("12345", module.getApplyId());
        verify(promoDetailService, times(1)).buildBuyGiftActivity(params);
    }

    @Test
    void testBuildBuyGiftActivityCouponIconProperties() throws Throwable {
        // arrange
        dealGift.setTitle("Test");
        dealGift.setProductTag("Tag");
        dealGift.setThumbnail("url");
        dealGift.setCouponNum(1);
        dealGift.setActivityId(1L);
        java.util.List<DealGift> dealGifts = new java.util.ArrayList<>();
        dealGifts.add(dealGift);
        params.setDealGifts(dealGifts);
        // act
        java.util.List<CouponModule> result = promoDetailService.buildBuyGiftActivity(params);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        CouponModule module = result.get(0);
        assertNotNull(module.getCouponIcon());
        assertEquals("赠品", module.getCouponIcon().getText());
        assertEquals("#FFFFFF", module.getCouponIcon().getTextColor());
        assertEquals("#FF7000", module.getCouponIcon().getBackgroundStartColor());
        assertEquals("#FF4B10", module.getCouponIcon().getBackgroundEndColor());
        verify(promoDetailService, times(1)).buildBuyGiftActivity(params);
    }

    @Test
    void testBuildBuyGiftActivityWithNullFields() throws Throwable {
        // arrange
        dealGift.setTitle(null);
        dealGift.setProductTag(null);
        dealGift.setTimeDesc(null);
        dealGift.setUseRule(null);
        dealGift.setThumbnail(null);
        dealGift.setCouponNum(0);
        dealGift.setActivityId(0L);
        java.util.List<DealGift> dealGifts = new java.util.ArrayList<>();
        dealGifts.add(dealGift);
        params.setDealGifts(dealGifts);
        // act
        java.util.List<CouponModule> result = promoDetailService.buildBuyGiftActivity(params);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        CouponModule module = result.get(0);
        assertNull(module.getCouponSubTitle());
        assertNotNull(module.getCouponTitle());
        assertNull(module.getCouponTitle().get(0).getContent());
        assertNull(module.getCouponTag().getContent());
        assertNull(module.getCouponAvatar());
        assertEquals(0, module.getCouponNum());
        assertEquals("0", module.getApplyId());
        verify(promoDetailService, times(1)).buildBuyGiftActivity(params);
    }
}
