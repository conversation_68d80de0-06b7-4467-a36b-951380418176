package com.sankuai.dzshoppingguide.product.detail.spi.similargoods.vo.dtos;

import com.dianping.mobile.framework.annotation.MobileDo;

import java.io.Serializable;
import java.util.List;

@MobileDo(id = 0x1bad86c4)
public class DzIconTagVO implements Serializable {
    /**
    * 图标URL
    */
    @MobileDo.MobileField(key = 0x3c48)
    private String icon;

    /**
    * 文字信息
    */
    @MobileDo.MobileField(key = 0xa780)
    private String msg;

    /**
    * 标签信息（这个字段后端没有赋值，不能用，类型不对，不要使用它，此处只做占位）
    */
    @MobileDo.MobileField(key = 0xe22f)
    private List<String> labels;

    /**
    * 跳转链接
    */
    @MobileDo.MobileField(key = 0x774e)
    private String jumpUrl;

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public List<String> getLabels() {
        return labels;
    }

    public void setLabels(List<String> labels) {
        this.labels = labels;
    }

    public String getJumpUrl() {
        return jumpUrl;
    }

    public void setJumpUrl(String jumpUrl) {
        this.jumpUrl = jumpUrl;
    }
}