package com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.factory;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.BuilderTypeEnum;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.extend.BaseBuilderFactory;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.extend.BaseVariableBuilder;
import com.sankuai.dz.product.detail.RequestSourceEnum;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.builder.BaseBottomBarBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.builder.enums.BottomBarSceneEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.builder.handler.cepintuan.CEPinTuanBottomBarBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.builder.handler.gym.MonthlySubscriptionBottomBarBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.builder.handler.standard.impl.DefaultBottomBarBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.builder.handler.standard.impl.LiveDealBottomBarBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.builder.handler.standard.impl.PreviewDealBottomBarBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.builder.handler.standard.impl.ShopMemberExclusiveBottomBarBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.builder.handler.zero.reserve.CommonFreeDealReserveBottomBarBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.builder.handler.zero.reserve.VaccineFreeReserveBottomBarBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.exception.BottomBarFatalException;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.category.ProductCategoryFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttrFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sale.info.ProductSaleInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sale.info.ProductSaleInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sale.info.enums.ProductSaleTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.FetcherDAGStarter;
import com.sankuai.dzshoppingguide.product.detail.application.utils.TimesDealUtil;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.ProductBuyBarModule;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Nullable;
import java.util.Map;
import java.util.Optional;
import java.util.Set;


/**
 * @Author: guangyujie
 * @Date: 2025/3/10 13:51
 */
@Builder(
        builderType = BuilderTypeEnum.BUILDER_FACTORY,
        moduleKey = ModuleKeyConstants.BOTTOM_BAR,
        startFetcher = FetcherDAGStarter.class,
        dependentFetchers = {
                ProductSaleInfoFetcher.class,
                ProductCategoryFetcher.class,
                ProductBaseInfoFetcher.class,
                ProductAttrFetcher.class
        }
)
@Slf4j
@SuppressWarnings("rawtypes")
public class BottomBarBuilderFactory extends BaseBuilderFactory<ProductBuyBarModule> {

    private @Nullable ProductCategory productCategory;

    /**
     * 根据
     */
    @Override
    protected Class<? extends BaseVariableBuilder> selectVariableBuilder() {
        Transaction transaction = Cat.newTransaction("BottomBarBuilderFactory", "selectVariableBuilder");
        try {
            final ProductSaleInfo productSaleInfo = getDependencyResult(ProductSaleInfoFetcher.class);
            final Set<ProductSaleTypeEnum> saleType = Optional.ofNullable(productSaleInfo)
                    .map(ProductSaleInfo::getSaleType)
                    .orElse(Sets.newHashSet(ProductSaleTypeEnum.COMMON_DEAL));
            productCategory = getDependencyResult(ProductCategoryFetcher.class);
            Class<? extends BaseBottomBarBuilder> builder = DefaultBottomBarBuilder.class;
            final BottomBarSceneEnum buyBarScene;
            ProductBaseInfo productBaseInfo = getDependencyResult(ProductBaseInfoFetcher.class);
            ProductAttr productAttr = getDependencyResult(ProductAttrFetcher.class);
            if (request.getClientTypeEnum() == ClientTypeEnum.MT_LIVE_XCX
                    || request.getClientTypeEnum() == ClientTypeEnum.MT_LIVE_ORDER_XCX) {
                //美团美播微信小程序&&美团美播提单小程序
                buyBarScene = BottomBarSceneEnum.MT_LIVE_XCX;

            } else if (RequestSourceEnum.ODP.getSource().equals(request.getPageSource())) {
                //分销渠道，流量几乎为0
                buyBarScene = BottomBarSceneEnum.ODP;

            } else if (TimesDealUtil.isMonthlySubscription(productBaseInfo, productAttr)) {
                //次卡->连续包月
                buyBarScene = BottomBarSceneEnum.MONTHLY_SUBSCRIPTION;
                builder = MonthlySubscriptionBottomBarBuilder.class;
            } else if (saleType.contains(ProductSaleTypeEnum.PREVIEW_DEAL)) {
                buyBarScene = BottomBarSceneEnum.PREVIEW;
                builder = PreviewDealBottomBarBuilder.class;
            } else if (saleType.contains(ProductSaleTypeEnum.MEMBER_EXCLUSIVE_DEAL)) {
                //商家会员专属，未开通商家会员不能购买
                buyBarScene = BottomBarSceneEnum.MEMBER_EXCLUSIVE;
                builder = ShopMemberExclusiveBottomBarBuilder.class;
            } else if (saleType.contains(ProductSaleTypeEnum.PREPAY_DEAL)) {
                //预付团单，没有流量
                buyBarScene = BottomBarSceneEnum.PREPAY_DEAL;
            } else if (saleType.contains(ProductSaleTypeEnum.LEADS_DEAL)) {
                //留资团单，LE才有
                buyBarScene = BottomBarSceneEnum.LEADS_DEAL;
            } else if (saleType.contains(ProductSaleTypeEnum.FITNESS_CROSS_DEAL)) {
                //健身通
                buyBarScene = BottomBarSceneEnum.FITNESS_CROSS;
            } else if (saleType.contains(ProductSaleTypeEnum.FREE_RESERVATION_DEAL)) {
                //0元预约，流量很低，个位数
                buyBarScene = BottomBarSceneEnum.FREE_RESERVATION_DEAL;
                int productSecondCategoryId = Optional.ofNullable(productCategory)
                        .map(ProductCategory::getProductSecondCategoryId)
                        .orElse(0);
                if (1611 == productSecondCategoryId) {
                    //疫苗定制
                    builder = VaccineFreeReserveBottomBarBuilder.class;
                } else {
                    builder = CommonFreeDealReserveBottomBarBuilder.class;
                }
            } else if (saleType.contains(ProductSaleTypeEnum.COST_EFFECTIVE_PIN_TUAN)) {
                //特团拼团
                buyBarScene = BottomBarSceneEnum.COST_EFFECTIVE_PIN_TUAN;
                builder = CEPinTuanBottomBarBuilder.class;
            } else if (saleType.contains(ProductSaleTypeEnum.LIVE_SOURCE_DEAL)) {
                buyBarScene = BottomBarSceneEnum.LIVE;
                builder = LiveDealBottomBarBuilder.class;
            } else {
                //普通团购
                buyBarScene = BottomBarSceneEnum.DEFAULT;
            }
            transaction.setStatus(Transaction.SUCCESS);
            doLog(buyBarScene, builder);
            return builder;
        } catch (Throwable throwable) {
            transaction.setStatus(throwable);
            log.error("FATAL ERROR!!!底Bar场景判断失败,request:{}", JSON.toJSONString(this.request), new BottomBarFatalException(throwable));
            return DefaultBottomBarBuilder.class;
        }
    }

    private void doLog(final BottomBarSceneEnum buyBarScene,
                       final Class<? extends BaseVariableBuilder> builder) {
        Map<String, String> tags = Maps.newHashMap();
        if (productCategory != null) {
            tags.put("category23", String.format("%s-%s",
                    productCategory.getProductSecondCategoryId(),
                    productCategory.getProductThirdCategoryId()
            ));
        } else {
            tags.put("category23", "unknown");
        }
        tags.put("scene", String.format("%s-%s", buyBarScene.name(), builder.getSimpleName()));
        Cat.logMetricForCount("BuyBarScene", tags);
    }

}
