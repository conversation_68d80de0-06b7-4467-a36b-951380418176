package com.sankuai.dzshoppingguide.product.detail.spi.review.vo;

import com.sankuai.dz.product.detail.gateway.spi.vo.AbstractModuleVO;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.dianping.mobile.framework.annotation.MobileDo;
import java.io.Serializable;


@MobileDo(id = 0x2140)
public class DpReserveShopReview extends AbstractModuleVO implements Serializable {


    @Override
    public String getModuleKey() {
        return ModuleKeyConstants.DP_RESERVE_SHOP_REVIEW_MODULE;
    }

    /**
     * 更多按钮文案
     */
    @MobileDo.MobileField(key = 0xb0d1)
    private String moreText;

    /**
     * 模块信息
     */
    @MobileDo.MobileField(key = 0xcce)
    private String content;

    /**
     * 更多跳转链接
     */
    @MobileDo.MobileField(key = 0x9c3c)
    private String moreUrl;

    /**
     * 模块名
     */
    @MobileDo.MobileField(key = 0xb308)
    private String moduleName;

    public String getMoreText() {
        return moreText;
    }

    public void setMoreText(String moreText) {
        this.moreText = moreText;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getMoreUrl() {
        return moreUrl;
    }

    public void setMoreUrl(String moreUrl) {
        this.moreUrl = moreUrl;
    }

    public String getModuleName() {
        return moduleName;
    }

    public void setModuleName(String moduleName) {
        this.moduleName = moduleName;
    }
}