// package com.sankuai.dzshoppingguide.product.detail.application.builder.navbar.handler;
//
// import static org.junit.jupiter.api.Assertions.*;
// import static org.mockito.ArgumentMatchers.*;
// import static org.mockito.Mockito.*;
// import com.sankuai.dealuser.price.display.api.enums.ProductTypeEnum;
// import com.sankuai.dealuser.price.display.api.enums.PromoTypeEnum;
// import com.sankuai.dealuser.price.display.api.enums.PromotionExplanatoryTagEnum;
// import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
// import com.sankuai.dealuser.price.display.api.model.PromoDTO;
// import com.sankuai.dealuser.price.display.api.model.PromoIdentity;
// import com.sankuai.dz.product.detail.RequestSourceEnum;
// import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
// import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
// import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapper;
// import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.dto.ProductPriceReturnValue;
// import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
// import com.sankuai.dzshoppingguide.product.detail.application.utils.AppImageSize;
// import com.sankuai.dzshoppingguide.product.detail.application.utils.ImageHelper;
// import com.sankuai.dzshoppingguide.product.detail.spi.dtos.ShareModuleMiniProgramConfig;
// import com.sankuai.general.product.query.center.client.dto.PriceDTO;
// import java.math.BigDecimal;
// import java.util.Collections;
// import java.util.List;
// import org.junit.jupiter.api.BeforeEach;
// import org.junit.jupiter.api.Test;
// import org.junit.jupiter.api.extension.ExtendWith;
// import org.mockito.InjectMocks;
// import org.mockito.Mock;
// import org.mockito.MockitoAnnotations;
// import org.mockito.junit.*;
// import org.mockito.junit.jupiter.MockitoExtension;
//
// @ExtendWith(MockitoExtension.class)
// class NavBarDealProductHandlerBuildImageTest {
//
//     @InjectMocks
//     private NavBarDealProductHandler navBarDealProductHandler;
//
//     @Mock
//     private ProductDetailPageRequest request;
//
//     @Mock
//     private ShopIdMapper idMapper;
//
//     private final NavBarDealProductHandler handler = new NavBarDealProductHandler();
//
//     @BeforeEach
//     void setUp() {
//         MockitoAnnotations.openMocks(this);
//     }
//
//     /**
//      * Test case for when the input image is null
//      */
//     @Test
//     void testBuildImageWhenImageIsNull() throws Throwable {
//         // arrange
//         String image = null;
//         // act
//         String result = navBarDealProductHandler.buildImage(image);
//         // assert
//         assertNull(result);
//     }
//
//     /**
//      * Test case for when the input image is empty string
//      */
//     @Test
//     void testBuildImageWhenImageIsEmpty() throws Throwable {
//         // arrange
//         String image = "";
//         // act
//         String result = navBarDealProductHandler.buildImage(image);
//         // assert
//         assertEquals("", result);
//     }
//
//     /**
//      * Test case for when the input image is valid and mini program share size is valid
//      */
//     @Test
//     void testBuildImageWhenImageIsValidAndMiniProgramShareSizeIsValid() throws Throwable {
//         // arrange
//         String image = "test_image.jpg";
//         // act
//         String result = navBarDealProductHandler.buildImage(image);
//         // assert
//         assertNotNull(result);
//         // Since we can't mock static method, we can verify the result is not empty
//         assertFalse(result.isEmpty());
//     }
//
//     /**
//      * Test case for when the input image is valid but uses medium size
//      */
//     @Test
//     void testBuildImageWhenImageIsValidAndUsesMediumSize() throws Throwable {
//         // arrange
//         String image = "test_image.jpg";
//         NavBarDealProductHandler handler = new NavBarDealProductHandler() {
//
//             int[] convertWidthHeight(int width, int height) {
//                 // Force using medium size
//                 return null;
//             }
//         };
//         // act
//         String result = handler.buildImage(image);
//         // assert
//         assertNotNull(result);
//         assertFalse(result.isEmpty());
//     }
//
//     /**
//      * Test case for when the input image is valid and uses mini program share size
//      */
//     @Test
//     void testBuildImageWhenImageIsValidAndUsesMiniProgramShareSize() throws Throwable {
//         // arrange
//         String image = "test_image.jpg";
//         NavBarDealProductHandler handler = new NavBarDealProductHandler() {
//
//             int[] convertWidthHeight(int width, int height) {
//                 // Return valid dimensions
//                 return new int[] { width, height };
//             }
//         };
//         // act
//         String result = handler.buildImage(image);
//         // assert
//         assertNotNull(result);
//         assertFalse(result.isEmpty());
//     }
//
//     /**
//      * Test case for when the input image is valid but dimensions are invalid
//      */
//     @Test
//     void testBuildImageWhenImageIsValidButDimensionsInvalid() throws Throwable {
//         // arrange
//         String image = "test_image.jpg";
//         NavBarDealProductHandler handler = new NavBarDealProductHandler() {
//
//             int[] convertWidthHeight(int width, int height) {
//                 // Return invalid dimensions
//                 return new int[] { 0, 0 };
//             }
//         };
//         // act
//         String result = handler.buildImage(image);
//         // assert
//         assertNotNull(result);
//         assertFalse(result.isEmpty());
//     }
//
//     /**
//      * Test case for when the input image is null.
//      */
//     @Test
//     void testBuildImageImageIsNull() throws Throwable {
//         // arrange
//         String image = null;
//         // act
//         String result = navBarDealProductHandler.buildImage(image);
//         // assert
//         assertNull(result);
//     }
//
//     /**
//      * Test case for unknown client type
//      */
//     @Test
//     public void testBuildMiniProgramConfigUnknownClientType() throws Throwable {
//         // arrange
//         when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.UNKNOWN);
//         // act
//         ShareModuleMiniProgramConfig config = navBarDealProductHandler.buildMiniProgramConfig(request, idMapper);
//         // assert
//         assertNotNull(config);
//         assertNull(config.getMiniProgramId());
//         assertNull(config.getPath());
//     }
//
//     /**
//      * Test case for MT_WX client type
//      */
//     @Test
//     public void testBuildMiniProgramConfigMtWx() throws Throwable {
//         // arrange
//         when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.MT_WX);
//         when(request.getProductId()).thenReturn(12345L);
//         when(idMapper.getMtBestShopId()).thenReturn(67890L);
//         // act
//         ShareModuleMiniProgramConfig config = navBarDealProductHandler.buildMiniProgramConfig(request, idMapper);
//         // assert
//         assertEquals("gh_870576f3c6f9", config.getMiniProgramId());
//         String expectedPath = String.format("/gc/pages/deal/dealdetail/dealdetail?dealid=%s&shopid=%s&nsrc=2", request.getProductId(), idMapper.getMtBestShopId());
//         assertEquals(expectedPath, config.getPath());
//     }
//
//     @Test
//     public void testBuildMiniProgramConfigOther() throws Throwable {
//         when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.UNKNOWN);
//         ShareModuleMiniProgramConfig config = navBarDealProductHandler.buildMiniProgramConfig(request, idMapper);
//         assertNull(config.getMiniProgramId());
//         assertNull(config.getPath());
//     }
//
//     /**
//      * Test case where client type is MT_APP and page source is COST_EFFECTIVE without a matching promo.
//      */
//     @Test
//     public void testBuildTitle_MT_APP_CostEffective_WithoutMatchingPromo() throws Throwable {
//         // arrange
//         ProductDetailPageRequest request = mock(ProductDetailPageRequest.class);
//         when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.MT_APP);
//         when(request.getPageSource()).thenReturn(RequestSourceEnum.COST_EFFECTIVE.name());
//         ProductBaseInfo baseInfo = mock(ProductBaseInfo.class);
//         ProductPriceReturnValue costEffectivePrice = new ProductPriceReturnValue(new PriceDisplayDTO());
//         PromoDTO promo = new PromoDTO();
//         promo.setIdentity(new PromoIdentity(PromoTypeEnum.NORMAL_PROMO.getType()));
//         promo.setPromotionExplanatoryTags(Collections.emptyList());
//         costEffectivePrice.getPriceDisplayDTO().setUsedPromos(Collections.singletonList(promo));
//         // act
//         String result = handler.buildTitle(request, "Custom Title", baseInfo, costEffectivePrice);
//         // assert
//         assertEquals("Custom Title", result);
//     }
//
//     /**
//      * Test case where client type is MT_APP and page source is COST_EFFECTIVE with null costEffectivePrice.
//      */
//     @Test
//     public void testBuildTitle_MT_APP_CostEffective_NullCostEffectivePrice() throws Throwable {
//         // arrange
//         ProductDetailPageRequest request = mock(ProductDetailPageRequest.class);
//         when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.MT_APP);
//         when(request.getPageSource()).thenReturn(RequestSourceEnum.COST_EFFECTIVE.name());
//         ProductBaseInfo baseInfo = mock(ProductBaseInfo.class);
//         // act
//         String result = handler.buildTitle(request, null, baseInfo, null);
//         // assert
//         assertEquals("分享个团购给你", result);
//     }
//
//     /**
//      * Test case where client type is DP_APP with valid prices.
//      */
//     @Test
//     public void testBuildTitle_DP_APP_WithValidPrices() throws Throwable {
//         // arrange
//         ProductDetailPageRequest request = mock(ProductDetailPageRequest.class);
//         when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.DP_APP);
//         ProductBaseInfo baseInfo = mock(ProductBaseInfo.class);
//         PriceDTO priceDTO = new PriceDTO();
//         priceDTO.setSalePrice("100.0");
//         priceDTO.setMarketPrice("200.0");
//         when(baseInfo.getPrice()).thenReturn(priceDTO);
//         // act
//         String result = handler.buildTitle(request, "Deal Title", baseInfo, null);
//         // assert
//         assertEquals("Deal Title：仅售100.0元，价值200.0元Deal Title", result);
//     }
//
//     /**
//      * Test case where client type is DP_WX with valid prices.
//      */
//     @Test
//     public void testBuildTitle_DP_WX_WithValidPrices() throws Throwable {
//         // arrange
//         ProductDetailPageRequest request = mock(ProductDetailPageRequest.class);
//         when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.DP_WX);
//         ProductBaseInfo baseInfo = mock(ProductBaseInfo.class);
//         PriceDTO priceDTO = new PriceDTO();
//         priceDTO.setSalePrice("150.0");
//         priceDTO.setMarketPrice("300.0");
//         when(baseInfo.getPrice()).thenReturn(priceDTO);
//         // act
//         String result = handler.buildTitle(request, "Deal Title", baseInfo, null);
//         // assert
//         assertEquals("Deal Title：仅售150.0元，价值300.0元Deal Title", result);
//     }
//
//     /**
//      * Test case where client type is neither MT_APP, MT_WX, DP_APP, nor DP_WX.
//      */
//     @Test
//     public void testBuildTitle_OtherClientType() throws Throwable {
//         // arrange
//         ProductDetailPageRequest request = mock(ProductDetailPageRequest.class);
//         when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.UNKNOWN);
//         ProductBaseInfo baseInfo = mock(ProductBaseInfo.class);
//         // act
//         String result = handler.buildTitle(request, null, baseInfo, null);
//         // assert
//         assertNull(result);
//     }
// }
