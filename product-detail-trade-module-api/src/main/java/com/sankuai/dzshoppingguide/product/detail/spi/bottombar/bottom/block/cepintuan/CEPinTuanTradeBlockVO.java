package com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.block.cepintuan;

import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.ProductBottomBarBlockVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.component.trade.button.StandardTradeButtonVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.enums.BottomBarBlockTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Author: guangyujie
 * @Date: 2025/3/20 15:18
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CEPinTuanTradeBlockVO extends ProductBottomBarBlockVO {

    /**
     * 交易按钮列表
     */
    private List<StandardTradeButtonVO> buttonList;

    @Override
    public int getBlockType() {
        return BottomBarBlockTypeEnum.COST_EFFECTIVE_BUTTON_LIST.getCode();
    }

}
