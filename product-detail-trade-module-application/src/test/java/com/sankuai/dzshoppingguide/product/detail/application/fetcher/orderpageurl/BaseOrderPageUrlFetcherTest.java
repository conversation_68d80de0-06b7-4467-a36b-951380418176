package com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.BaseFetcherContext;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.builder.pricebar.builder.enums.CouponGuideTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategoryFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.coupon.magic.MagicCouponFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.coupon.magic.MagicCouponInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.ext.param.request.ExtParamBuilderRequest;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class BaseOrderPageUrlFetcherTest {

    @Mock
    private ProductDetailPageRequest request;

    @Mock
    private ProductCategory productCategory;

    @Mock
    private MagicCouponInfo magicCouponInfo;

    private TestBaseOrderPageUrlFetcher fetcher;

    @BeforeEach
    void setUp() {
        fetcher = new TestBaseOrderPageUrlFetcher();
        fetcher.setRequest(request);
    }

    /**
     * Test case for MT_LIVE_XCX client type
     */
    @Test
    public void testGetPageSourceType_WhenMtLiveXcx_ReturnMeibo() throws Throwable {
        // arrange
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.MT_LIVE_XCX);
        // act
        String result = fetcher.getPageSourceType();
        // assert
        assertEquals("meibo", result);
    }

    /**
     * Test case for mallFoodPoiShelf page source with category id 712
     */
    @Test
    public void testGetPageSourceType_WhenMallFoodPoiShelfAndCategory712_ReturnUnityDealGroupDetail() throws Throwable {
        // arrange
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.MT_APP);
        when(request.getPageSource()).thenReturn("mallFoodPoiShelf");
        when(productCategory.getProductSecondCategoryId()).thenReturn(Integer.valueOf(712));
        fetcher.setProductCategory(productCategory);
        // act
        String result = fetcher.getPageSourceType();
        // assert
        assertEquals("unityDealGroupDetail", result);
    }

    /**
     * Test case for magic coupon with GUIDE_PURCHASE type
     */
    @Test
    public void testGetPageSourceType_WhenMagicCouponGuidePurchase_ReturnDealGroupDetailMMCBuy() throws Throwable {
        // arrange
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.MT_APP);
        when(magicCouponInfo.getCouponGuideTypeEnum()).thenReturn(CouponGuideTypeEnum.GUIDE_PURCHASE);
        fetcher.setMagicCouponInfo(magicCouponInfo);
        // act
        String result = fetcher.getPageSourceType();
        // assert
        assertEquals("dealGroupDetailMMCBuy", result);
    }

    /**
     * Test case for magic coupon with GUIDE_TO_USE_INFLATE type
     */
    @Test
    public void testGetPageSourceType_WhenMagicCouponGuideToUseInflate_ReturnDealGroupDetailMMCUse() throws Throwable {
        // arrange
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.MT_APP);
        when(magicCouponInfo.getCouponGuideTypeEnum()).thenReturn(CouponGuideTypeEnum.GUIDE_TO_USE_INFLATE);
        fetcher.setMagicCouponInfo(magicCouponInfo);
        // act
        String result = fetcher.getPageSourceType();
        // assert
        assertEquals("dealGroupDetailMMCUse", result);
    }

    /**
     * Test case for mallFoodPoiShelf page source but not category 712
     */
    @Test
    public void testGetPageSourceType_WhenMallFoodPoiShelfButNotCategory712_ReturnDealGroupDetail() throws Throwable {
        // arrange
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.MT_APP);
        when(request.getPageSource()).thenReturn("mallFoodPoiShelf");
        when(productCategory.getProductSecondCategoryId()).thenReturn(Integer.valueOf(713));
        fetcher.setProductCategory(productCategory);
        // act
        String result = fetcher.getPageSourceType();
        // assert
        assertEquals("dealGroupDetail", result);
    }

    /**
     * Test case for null magic coupon info
     */
    @Test
    public void testGetPageSourceType_WhenMagicCouponInfoNull_ReturnDealGroupDetail() throws Throwable {
        // arrange
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.MT_APP);
        fetcher.setMagicCouponInfo(null);
        // act
        String result = fetcher.getPageSourceType();
        // assert
        assertEquals("dealGroupDetail", result);
    }

    /**
     * Test case for default scenario
     */
    @Test
    public void testGetPageSourceType_WhenDefaultScenario_ReturnDealGroupDetail() throws Throwable {
        // arrange
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.MT_APP);
        when(request.getPageSource()).thenReturn("otherSource");
        // act
        String result = fetcher.getPageSourceType();
        // assert
        assertEquals("dealGroupDetail", result);
    }

    private class TestBaseOrderPageUrlFetcher extends BaseOrderPageUrlFetcher {

        private ProductCategory productCategory;

        private MagicCouponInfo magicCouponInfo;

        @Override
        protected CompletableFuture<OrderPageUrlResult> doFetch() {
            return null;
        }

        @Override
        protected boolean isContinue() {
            return false;
        }

        @Override
        protected java.util.Map<String, String> buildCustomExtParams() {
            return new HashMap<>();
        }

        @Override
        protected void fulfillExtParamBuilderRequest(ExtParamBuilderRequest builderRequest) {
        }

        @Override
        protected <T extends FetcherReturnValueDTO> T getDependencyResult(Class<? extends BaseFetcherContext> dependencyFetcherClass) {
            if (dependencyFetcherClass == ProductCategoryFetcher.class) {
                return (T) productCategory;
            }
            if (dependencyFetcherClass == MagicCouponFetcher.class) {
                return (T) magicCouponInfo;
            }
            return null;
        }

        public void setProductCategory(ProductCategory productCategory) {
            this.productCategory = productCategory;
        }

        public void setMagicCouponInfo(MagicCouponInfo magicCouponInfo) {
            this.magicCouponInfo = magicCouponInfo;
        }

        public void setRequest(ProductDetailPageRequest request) {
            this.request = request;
        }
    }
}
