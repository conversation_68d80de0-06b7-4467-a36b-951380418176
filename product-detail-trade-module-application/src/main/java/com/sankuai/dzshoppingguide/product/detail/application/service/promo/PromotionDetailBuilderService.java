package com.sankuai.dzshoppingguide.product.detail.application.service.promo;

import com.sankuai.dealuser.price.display.api.enums.CouponAssignStatusEnum;
import com.sankuai.dealuser.price.display.api.enums.PromoTypeEnum;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import com.sankuai.dzshoppingguide.product.detail.application.utils.LionConfigUtils;
import com.sankuai.dzshoppingguide.product.detail.application.utils.PromoHelper;
import com.sankuai.dzshoppingguide.product.detail.application.utils.PromoInfoHelper;
import com.sankuai.dzshoppingguide.product.detail.application.utils.VersionUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.promo.vo.DztgCouponInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024-06-25
 * @desc 优惠明细构造服务
 */
@Slf4j
@Component
public class PromotionDetailBuilderService {
    @Autowired
    private LeafRepository leafRepository;

    public DztgCouponInfo convertPromoDTO2DztgCouponInfo(PromoDTO coupon, String mrnVersion) {
        if (coupon == null) {
            return null;
        }
        DztgCouponInfo dztgCouponInfo = new DztgCouponInfo();

        if (coupon.getIdentity() != null && Objects.equals(coupon.getIdentity().getPromoType(), PromoTypeEnum.COUPON.getType())) {
            dztgCouponInfo.setCouponGroupId(coupon.getIdentity().getPromoId());
            if ("MERCHANT_COUPON".equals(coupon.getIdentity().getPromoShowType())) {
                dztgCouponInfo.setCouponType(0);
            }
            if (coupon.isCanAssign()) {
                dztgCouponInfo.setStatus(0);
            } else {
                dztgCouponInfo.setStatus(1);
            }
        } else if (coupon.getIdentity() != null && Objects.equals(coupon.getIdentity().getPromoType(), PromoTypeEnum.GOVERNMENT_CONSUME_COUPON.getType())) {
            if (org.apache.commons.lang3.StringUtils.isNumeric(coupon.getCouponGroupId())) {
                dztgCouponInfo.setCouponGroupId(Long.valueOf(coupon.getCouponGroupId()));
            }
            // 获取政府金融券-N选1的券包密钥
            String financeExtSecretKey = PromoInfoHelper.getFinanceExtPackageSecretKey(coupon);
            if (StringUtils.isNotBlank(financeExtSecretKey)) {
                String nSelectOneMrnVersion = LionConfigUtils.getNSelectOneGovernmentCouponMrnVersion();
                if (VersionUtils.isLessThanOrEqual(mrnVersion, nSelectOneMrnVersion)) {
                    return null;
                }
                dztgCouponInfo.setPackageSecretKey(financeExtSecretKey);
            }
            dztgCouponInfo.setCouponType(2);
            if (Objects.equals(CouponAssignStatusEnum.UN_ASSIGNED.getCode(), coupon.getCouponAssignStatus())) {
                dztgCouponInfo.setStatus(0);
                List<Long> leafIds = leafRepository.batchGenFinancialConsumeSerialId(1);
                if (CollectionUtils.isNotEmpty(leafIds)) {
                    dztgCouponInfo.setSerialno(String.valueOf(leafIds.get(0)));
                }
            } else {
                dztgCouponInfo.setStatus(1);
            }
        }

        dztgCouponInfo.setTitle(coupon.getExtendDesc());

        if (coupon.getAmount() != null) {
            dztgCouponInfo.setAmount(coupon.getCouponValueText());
        }
        dztgCouponInfo.setAmountCornerMark(coupon.getCouponValueType() == 4 ? "折" : "元");
        dztgCouponInfo.setTimeDesc(coupon.getUseTimeDesc());
        dztgCouponInfo.setAmountDesc(coupon.getPriceLimitDesc());

        dztgCouponInfo.setSourceTag(coupon.getIdentity() == null ? "优惠券" : PromoHelper.convertSourceTag(coupon));

        dztgCouponInfo.setSpecificTag(null);

        if (dztgCouponInfo.getAmount() == null || dztgCouponInfo.getTitle() == null) {
            // 如果最终生成的coupon有问题，过滤掉，不展示
            return null;
        }

        if(StringUtils.isNotBlank(coupon.getCouponId())) {
            dztgCouponInfo.setCouponId(coupon.getCouponId());
        }

        return dztgCouponInfo;
    }
    
    //TODO 待优化
    public String convertSourceTag(PromoDTO promoDTO) {
        if (promoDTO == null) {
            return null;
        }
        if (promoDTO.getIdentity() == null || promoDTO.getIdentity().getPromoShowType() == null) {
            return "团购优惠";
        }
        if (promoDTO.getPromoIdentity() != null) {
            String promoIdentity = promoDTO.getPromoIdentity();
            if ("godCouponAlliance".equals(promoIdentity)) {
                return "神券联盟";
            } else if ("specialDiscountCode".equals(promoIdentity)) {
                return "特惠码专享";
            } else if ("beautyMember".equals(promoIdentity)) {
                return "丽人会员";
            }
        }
        switch (promoDTO.getIdentity().getPromoShowType()) {
            case "DISCOUNT_SELL":
                return "特惠促销";
            case "MT_SUBSIDY":
                return "美团补贴";
            case "NEW_CUSTOMER_DISCOUNT":
                return "新客特惠";
            case "MEMBER_BENEFITS":
                return "会员优惠";
            case "PLATFORM_COUPON":
                return "美团券";
            case "MERCHANT_COUPON":
                return "商家券";
            case "GOVERNMENT_CONSUME_COUPON":
                return "政府消费券";
            case "DEAL_PROMO":
                return "团购优惠";
            case "PRESALE_PROMO":
                return "预售优惠";
            case "NEW_MEMBER_BENEFITS":
                return "新会员优惠";
            default:
                return "优惠";
        }
    }
}
