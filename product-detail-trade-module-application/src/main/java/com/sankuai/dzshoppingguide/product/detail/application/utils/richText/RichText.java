package com.sankuai.dzshoppingguide.product.detail.application.utils.richText;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.Data;

import java.util.List;

@Data
public class RichText {
    List<TextItem> textItemList = Lists.newArrayList();

    @Override
    public String toString() {
        return JSON.toJSONString(textItemList);
    }

    /**
     * 单个文本值
     */
    @Data
    public static class TextItem {
        private String text;  //文本
        private int textsize = 0;  //字体大小 单位px
        private String textcolor = "#FF000000";  //字体颜色
        private String backgroundcolor = "#00FFFFFF";  //字体背景色
        private String textstyle = "Default";  //字体样式
        private boolean strikethrough = false;  //是否有删除线
        private boolean underline = false;  //是否有下滑线

        public TextItem(){

        }

        public TextItem(int textsize, String textcolor, String backgroundcolor, String textstyle, boolean strikethrough, boolean underline) {
            this.text = "";
            this.textsize = textsize;
            this.textcolor = textcolor;
            this.backgroundcolor = backgroundcolor;
            this.textstyle = textstyle;
            this.strikethrough = strikethrough;
            this.underline = underline;
        }

        //field简单复制
        public TextItem clone() {
            return new TextItem(this.textsize, this.textcolor, this.backgroundcolor, this.textstyle, this.strikethrough, this.underline);
        }

    }
}