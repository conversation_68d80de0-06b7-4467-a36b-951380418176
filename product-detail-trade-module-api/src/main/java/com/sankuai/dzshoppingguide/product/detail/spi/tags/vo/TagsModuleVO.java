package com.sankuai.dzshoppingguide.product.detail.spi.tags.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.dz.product.detail.gateway.spi.vo.AbstractModuleVO;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/2/26 10:57
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TypeDoc(description = "榜单,排行榜等标签")
@MobileDo(id = 0x2b8)
public abstract class TagsModuleVO extends AbstractModuleVO {

    @FieldDoc(description = "标签类型")
    @MobileDo.MobileField(key = 0x342f)
    private List<BaseTagDTO> tags;
}
