package com.sankuai.dzshoppingguide.product.detail.spi.dtos.promo;

import com.sankuai.dzshoppingguide.product.detail.spi.promo.vo.CouponItemInfoDTO;

import java.io.Serializable;
import java.util.List;

/**
 * 功能描述:  <p>
 *
 * <AUTHOR> lintaolei <p>
 * @version 2024/4/4
 * @since mapi-dztgdetail-web
 */
public class CouponItemPackageDTO implements Serializable {

    private List<CouponItemInfoDTO> couponItems;

    private List<String> couponIds;

    public List<CouponItemInfoDTO> getCouponItems() {
        return couponItems;
    }

    public void setCouponItems(List<CouponItemInfoDTO> couponItems) {
        this.couponItems = couponItems;
    }

    public List<String> getCouponIds() {
        return couponIds;
    }

    public void setCouponIds(List<String> couponIds) {
        this.couponIds = couponIds;
    }
}
