package com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.enums;

import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.ProductBottomBarComponentVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.component.cepintuan.CEPinTuanInfoVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.component.trade.button.OnlyIconTradeButtonVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.component.quick.entrance.QuickEntranceButtonVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.component.trade.button.StandardTradeButtonVO;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: guangyujie
 * @Date: 2025/3/16 14:40
 */
@Getter
public enum BottomBarComponentTypeEnum {

    TRADE_BUTTON(1, "交易按钮", StandardTradeButtonVO.class),
    QUICK_ENTRANCE_BUTTON(2, "快捷入口按钮", QuickEntranceButtonVO.class),
    COST_EFFECTIVE_PIN_TUAN_INFO(3, "特团拼团信息", CEPinTuanInfoVO.class),
    ONLY_ICON_TRADE_BUTTON(4, "仅图标的交易按钮", OnlyIconTradeButtonVO.class);

    private final int code;

    private final String desc;

    private final Class<? extends ProductBottomBarComponentVO> componentClass;

    BottomBarComponentTypeEnum(int code, String desc, Class<? extends ProductBottomBarComponentVO> componentClass) {
        this.code = code;
        this.desc = desc;
        this.componentClass = componentClass;
    }

    public static BottomBarComponentTypeEnum fromCode(int code) {
        for (BottomBarComponentTypeEnum value : BottomBarComponentTypeEnum.values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        throw new IllegalArgumentException("无效code:" + code);
    }

    public static boolean containsCode(int code) {
        for (BottomBarComponentTypeEnum value : BottomBarComponentTypeEnum.values()) {
            if (value.getCode() == code) {
                return true;
            }
        }
        return false;
    }

    public static Map<Integer, String> buildCodeDescMap() {
        return Arrays.stream(BottomBarComponentTypeEnum.values()).collect(Collectors.toMap(
                BottomBarComponentTypeEnum::getCode,
                BottomBarComponentTypeEnum::getDesc
        ));
    }

    public static Map<String, String> buildNameDescMap() {
        return Arrays.stream(BottomBarComponentTypeEnum.values()).collect(Collectors.toMap(
                BottomBarComponentTypeEnum::name,
                BottomBarComponentTypeEnum::getDesc
        ));
    }

}