package com.sankuai.dzshoppingguide.product.detail.application.builder.similargoods;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.google.common.collect.Lists;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommendgoods.dtos.ProductM;
import com.sankuai.dzshoppingguide.product.detail.spi.similargoods.vo.dtos.DzProductVO;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import com.alibaba.fastjson.JSONObject;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommendgoods.dtos.AttrM;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommendgoods.dtos.ProductSaleM;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommendgoods.dtos.ShopM;
import com.sankuai.dzshoppingguide.product.detail.spi.similargoods.vo.dtos.DzPromoVO;
import com.sankuai.dzshoppingguide.product.detail.spi.similargoods.vo.dtos.ShopVO;
import java.math.BigDecimal;
import java.util.Collections;
import org.mockito.junit.*;

@ExtendWith(MockitoExtension.class)
public class ListingProductsBuilderTest {

    @InjectMocks
    private ListingProductsBuilder listingProductsBuilder;

    @Mock
    private ProductDetailPageRequest mockRequest;

    private ListingProductsBuilder builder;

    /**
     * Test when input product list is null
     */
    @Test
    public void testBuildProductList_NullInput() {
        // arrange
        List<ProductM> nullList = null;
        // act
        List<DzProductVO> result = listingProductsBuilder.buildProductList(nullList, mockRequest);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test when input product list is empty
     */
    @Test
    public void testBuildProductList_EmptyInput() {
        // arrange
        List<ProductM> emptyList = new ArrayList<>();
        // act
        List<DzProductVO> result = listingProductsBuilder.buildProductList(emptyList, mockRequest);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test when input product list contains single item
     */
    @Test
    public void testBuildProductList_SingleItem() {
        // arrange
        ProductM productM = new ProductM();
        productM.setProductId(123L);
        List<ProductM> singleItemList = Lists.newArrayList(productM);
        // act
        mockRequest = new ProductDetailPageRequest();
        mockRequest.setClientType(100);
        List<DzProductVO> result = listingProductsBuilder.buildProductList(singleItemList, mockRequest);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertNotNull(result.get(0));
    }

    /**
     * Test when input product list contains multiple items
     */
    @Test
    public void testBuildProductList_MultipleItems() {
        // arrange
        ProductM product1 = new ProductM();
        product1.setProductId(123L);
        ProductM product2 = new ProductM();
        product2.setProductId(456L);
        List<ProductM> multipleItemsList = Lists.newArrayList(product1, product2);
        // act
        mockRequest = new ProductDetailPageRequest();
        mockRequest.setClientType(100);
        List<DzProductVO> result = listingProductsBuilder.buildProductList(multipleItemsList, mockRequest);
        // assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertNotNull(result.get(0));
        assertNotNull(result.get(1));
    }

    /**
     * Test when buildDzProductVO returns null for some items
     */
    @Test
    public void testBuildProductList_WithNullResults() {
        // arrange
        ProductM nullProduct = null;
        ProductM validProduct = new ProductM();
        validProduct.setProductId(123L);
        List<ProductM> mixedList = Lists.newArrayList(nullProduct, validProduct);
        // act
        mockRequest = new ProductDetailPageRequest();
        mockRequest.setClientType(100);
        List<DzProductVO> result = listingProductsBuilder.buildProductList(mixedList, mockRequest);
        // assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertNull(result.get(0));
        assertNotNull(result.get(1));
    }

    @BeforeEach
    void setUp() {
        builder = new ListingProductsBuilder();
    }

    @Test
    public void testBuildDzProductVONullProduct() throws Throwable {
        // arrange
        ProductDetailPageRequest request = new ProductDetailPageRequest();
        // act
        DzProductVO result = builder.buildDzProductVO(null, request);
        // assert
        assertNull(result);
    }

    @Test
    public void testBuildDzProductVOMinimalProduct() throws Throwable {
        // arrange
        ProductM productM = new ProductM();
        productM.setProductId(123L);
        productM.setTitle("Test Product");
        productM.setPicUrl("http://test.com/pic.jpg");
        productM.setJumpUrl("http://test.com/product");
        productM.setAvailable(true);
        ProductDetailPageRequest request = new ProductDetailPageRequest();
        // act
        DzProductVO result = builder.buildDzProductVO(productM, request);
        // assert
        assertNotNull(result);
        assertEquals(123L, result.getLongProductId());
        assertEquals("Test Product", result.getTitle());
        assertEquals("https://test.com/pic.jpg", result.getPic());
        assertTrue(result.isAvailable());
        assertEquals("抢购", result.getButtonName());
        assertEquals(0, result.getButtonStatus());
    }

    @Test
    public void testBuildDzProductVOWithShopInfo() throws Throwable {
        // arrange
        ProductM productM = new ProductM();
        productM.setProductId(123L);
        ShopM shopM = new ShopM();
        shopM.setLongShopId(456L);
        shopM.setShopName("Test Shop");
        shopM.setDistance("1.2km");
        shopM.setDetailUrl("http://test.com/shop");
        shopM.setMainRegionName("Test Region");
        shopM.setScoreTag("4.5");
        productM.setShopMs(Collections.singletonList(shopM));
        ProductDetailPageRequest request = new ProductDetailPageRequest();
        // act
        DzProductVO result = builder.buildDzProductVO(productM, request);
        // assert
        assertNotNull(result.getShop());
        assertEquals(456L, result.getShop().getShopId());
        assertEquals("Test Shop", result.getShop().getShopName());
        assertEquals("1.2km", result.getShop().getDistance());
        assertEquals("Test Shop", result.getShopName());
    }

    @Test
    public void testBuildDzProductVOWithSaleInfo() throws Throwable {
        // arrange
        ProductM productM = new ProductM();
        productM.setProductId(123L);
        ProductSaleM saleM = new ProductSaleM();
        saleM.setSaleTag("100+ sold");
        productM.setSale(saleM);
        ProductDetailPageRequest request = new ProductDetailPageRequest();
        // act
        DzProductVO result = builder.buildDzProductVO(productM, request);
        // assert
        assertEquals("100+ sold", result.getSale());
    }

    @Test
    public void testBuildDzProductVOWithDealPageSource() throws Throwable {
        // arrange
        ProductM productM = new ProductM();
        productM.setProductId(123L);
        productM.setShopCarPrice(new BigDecimal("99.99"));
        productM.setBasePrice(new BigDecimal("109.99"));
        productM.setMarketPrice("199.99");
        ProductDetailPageRequest request = new ProductDetailPageRequest();
        request.setPageSource("deal");
        // act
        DzProductVO result = builder.buildDzProductVO(productM, request);
        // assert
        assertEquals("99.99", result.getSalePrice());
    }

    @Test
    public void testBuildDzProductVOWithProductDescFromAttr() throws Throwable {
        // arrange
        ProductM productM = new ProductM();
        productM.setProductId(123L);
        AttrM descAttr = new AttrM();
        descAttr.setName("dealSubTitle");
        descAttr.setValue("[\"Feature 1\",\"Feature 2\"]");
        List<AttrM> attrs = new ArrayList<>();
        attrs.add(descAttr);
        productM.setExtAttrs(attrs);
        ProductDetailPageRequest request = new ProductDetailPageRequest();
        // act
        DzProductVO result = builder.buildDzProductVO(productM, request);
        // assert
        assertEquals("Feature 1・Feature 2", result.getProductDesc());
    }

    @Test
    public void testBuildDzProductVOWithPromoInfo() throws Throwable {
        // arrange
        ProductM productM = new ProductM();
        productM.setProductId(123L);
        ProductDetailPageRequest request = new ProductDetailPageRequest();
        // act
        DzProductVO result = builder.buildDzProductVO(productM, request);
        // assert
        assertNotNull(result.getPromoVOList());
        assertEquals(1, result.getPromoVOList().size());
        DzPromoVO promo = result.getPromoVOList().get(0);
        assertEquals("#FF4B10", promo.getTextColor());
        assertNotNull(promo.getPrePic());
    }

    @Test
    public void testBuildDzProductVOWithExtAttrs() throws Throwable {
        // arrange
        ProductM productM = new ProductM();
        productM.setProductId(123L);
        AttrM shopNumAttr = new AttrM();
        shopNumAttr.setName("dealApplyShopNumberAttr");
        shopNumAttr.setValue("5");
        List<AttrM> attrs = new ArrayList<>();
        attrs.add(shopNumAttr);
        productM.setExtAttrs(attrs);
        ProductDetailPageRequest request = new ProductDetailPageRequest();
        // act
        DzProductVO result = builder.buildDzProductVO(productM, request);
        // assert
        assertNotNull(result.getExtAttrMap());
        assertEquals("5", result.getExtAttrMap().get("dealApplyShopNumberAttr"));
    }

    @Test
    public void testBuildDzProductVOWithInvalidMarketPrice() throws Throwable {
        // arrange
        ProductM productM = new ProductM();
        productM.setProductId(123L);
        productM.setMarketPrice("invalid");
        ProductDetailPageRequest request = new ProductDetailPageRequest();
        // act
        DzProductVO result = builder.buildDzProductVO(productM, request);
        // assert
        assertEquals("invalid", result.getMarketPrice());
        assertEquals("", result.getMarketPriceCent());
    }

    @Test
    public void testBuildDzProductVOWithShopcarPageSource() throws Throwable {
        // arrange
        ProductM productM = new ProductM();
        productM.setProductId(123L);
        productM.setShopCarPrice(new BigDecimal("89.99"));
        productM.setBasePrice(new BigDecimal("109.99"));
        productM.setMarketPrice("199.99");
        ProductDetailPageRequest request = new ProductDetailPageRequest();
        request.setPageSource("shopcarselect");
        // act
        DzProductVO result = builder.buildDzProductVO(productM, request);
        // assert
        assertEquals("89.99", result.getSalePrice());
    }

    @Test
    public void testBuildDzProductVOWithDefaultPageSource() throws Throwable {
        // arrange
        ProductM productM = new ProductM();
        productM.setProductId(123L);
        productM.setBasePrice(new BigDecimal("109.99"));
        productM.setBasePriceTag("109.99");
        productM.setMarketPrice("199.99");
        productM.setPayPrice(new BigDecimal("109.99"));
        ProductDetailPageRequest request = new ProductDetailPageRequest();
        request.setPageSource("other");
        // act
        DzProductVO result = builder.buildDzProductVO(productM, request);
        // assert
        assertEquals("109.99", result.getSalePrice());
    }

    @Test
    public void testBuildDzProductVOWithProductTags() throws Throwable {
        // arrange
        ProductM productM = new ProductM();
        productM.setProductId(123L);
        productM.setBasePrice(new BigDecimal("80.00"));
        productM.setMarketPrice("100.00");
        AttrM tagAttr = new AttrM();
        tagAttr.setName("highestPriorityPricePowerTagAttr");
        tagAttr.setValue("Best Value");
        List<AttrM> attrs = new ArrayList<>();
        attrs.add(tagAttr);
        productM.setExtAttrs(attrs);
        ProductDetailPageRequest request = new ProductDetailPageRequest();
        // act
        DzProductVO result = builder.buildDzProductVO(productM, request);
        // assert
        assertNotNull(result.getProductTags());
        assertTrue(result.getProductTags().contains("Best Value"));
    }

    @Test
    public void testBuildDzProductVOWithEmptyShopList() throws Throwable {
        // arrange
        ProductM productM = new ProductM();
        productM.setProductId(123L);
        productM.setShopMs(Collections.emptyList());
        ProductDetailPageRequest request = new ProductDetailPageRequest();
        // act
        DzProductVO result = builder.buildDzProductVO(productM, request);
        // assert
        assertNull(result.getShop());
        assertEquals("", result.getShopName());
    }

    @Test
    public void testBuildDzProductVOWithProductItemId() throws Throwable {
        // arrange
        ProductM productM = new ProductM();
        productM.setProductId(123L);
        AttrM itemIdAttr = new AttrM();
        itemIdAttr.setName("dealDefaultSkuId");
        itemIdAttr.setValue("456");
        List<AttrM> attrs = new ArrayList<>();
        attrs.add(itemIdAttr);
        productM.setExtAttrs(attrs);
        ProductDetailPageRequest request = new ProductDetailPageRequest();
        // act
        DzProductVO result = builder.buildDzProductVO(productM, request);
        // assert
        assertEquals(456, result.getProductItemId());
    }

    @Test
    public void testBuildDzProductVOWithNullPageSource() throws Throwable {
        // arrange
        ProductM productM = new ProductM();
        productM.setProductId(123L);
        productM.setBasePrice(new BigDecimal("109.99"));
        productM.setBasePriceTag("109.99");
        productM.setPayPrice(new BigDecimal("109.99"));
        ProductDetailPageRequest request = new ProductDetailPageRequest();
        request.setPageSource(null);
        // act
        DzProductVO result = builder.buildDzProductVO(productM, request);
        // assert
        assertEquals("109.99", result.getSalePrice());
    }

@Test
public void testBuildMarketPriceCent_EmptyString_ReturnsEmpty() throws Throwable {
// arrange
String input = "";
// act
String result = ListingProductsBuilder.buildMarketPriceCent(input);
// assert
assertEquals("", result);
}

@Test
public void testBuildMarketPriceCent_NullInput_ReturnsEmpty() throws Throwable {
// arrange
String input = null;
// act
String result = ListingProductsBuilder.buildMarketPriceCent(input);
// assert
assertEquals("", result);
}

@Test
public void testBuildMarketPriceCent_InvalidFormat_ReturnsEmpty() throws Throwable {
// arrange
String input = "abc";
// act
String result = ListingProductsBuilder.buildMarketPriceCent(input);
// assert
assertEquals("", result);
}

@Test
public void testBuildMarketPriceCent_MaxValue_ReturnsNonEmpty() throws Throwable {
// arrange
String input = String.valueOf(Double.MAX_VALUE);
// act
String result = ListingProductsBuilder.buildMarketPriceCent(input);
// assert
assertNotNull(result);
assertFalse(result.isEmpty());
}

@Test
public void testBuildMarketPriceCent_MinValue_ReturnsNonEmpty() throws Throwable {
// arrange
String input = String.valueOf(Double.MIN_VALUE);
// act
String result = ListingProductsBuilder.buildMarketPriceCent(input);
// assert
assertNotNull(result);
assertFalse(result.isEmpty());
}

@Test
public void testBuildMarketPriceCent_NumberWithCommas_ReturnsEmpty() throws Throwable {
// arrange
String input = "1,000.50";
// act
String result = ListingProductsBuilder.buildMarketPriceCent(input);
// assert
assertEquals("", result);
}
}
