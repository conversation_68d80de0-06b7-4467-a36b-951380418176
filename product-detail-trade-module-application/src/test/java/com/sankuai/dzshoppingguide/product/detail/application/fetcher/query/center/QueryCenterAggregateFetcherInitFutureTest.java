package com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.response.FetcherResponse;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.exception.QueryCenterException;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.exception.QueryCenterResultCheckException;
import com.sankuai.dzshoppingguide.product.detail.domain.query.center.QueryCenterAclService;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.enums.QueryDealFieldEnum;
import com.sankuai.general.product.query.center.client.enums.QueryDealGroupFieldEnum;
import com.sankuai.general.product.query.center.client.enums.QuerySpuFieldEnum;
import com.sankuai.general.product.query.center.client.enums.ResponseCodeEnum;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResponse;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResult;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import org.apache.thrift.TException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class QueryCenterAggregateFetcherInitFutureTest {

    @Mock
    private QueryCenterAclService queryCenterAclService;

    @Mock
    private QueryByDealGroupIdRequest queryByDealGroupIdRequest;

    @Mock
    private QueryByDealGroupIdRequestBuilder requestBuilder;

    @InjectMocks
    private QueryCenterAggregateFetcher fetcher;

    @BeforeEach
    void setUp() {
        when(requestBuilder.build()).thenReturn(queryByDealGroupIdRequest);
    }


    /**
     * Tests TException case
     */
    @Test
    void testInitFuture_TExceptionCase() throws Throwable {
        // arrange
        when(queryCenterAclService.query(any(QueryByDealGroupIdRequest.class))).thenThrow(new TException("Test TException"));
        // act
        CompletableFuture<FetcherResponse<QueryCenterAggregateReturnValue>> future = fetcher.initFuture(requestBuilder);
        // assert
        FetcherResponse<QueryCenterAggregateReturnValue> fetcherResponse = future.get();
        assertFalse(fetcherResponse.isSuccess());
        assertTrue(fetcherResponse.getError() instanceof QueryCenterException);
        assertTrue(fetcherResponse.getError().getCause() instanceof TException);
    }

    /**
     * Tests other exception case
     */
    @Test
    void testInitFuture_OtherExceptionCase() throws Throwable {
        // arrange
        CompletableFuture<QueryDealGroupListResponse> exceptionalFuture = new CompletableFuture<>();
        exceptionalFuture.completeExceptionally(new RuntimeException("Test exception"));
        when(queryCenterAclService.query(any(QueryByDealGroupIdRequest.class))).thenReturn(exceptionalFuture);
        // act
        CompletableFuture<FetcherResponse<QueryCenterAggregateReturnValue>> future = fetcher.initFuture(requestBuilder);
        // assert
        FetcherResponse<QueryCenterAggregateReturnValue> fetcherResponse = future.get();
        assertFalse(fetcherResponse.isSuccess());
        assertTrue(fetcherResponse.getError() instanceof QueryCenterException);
        assertTrue(fetcherResponse.getError().getCause() instanceof RuntimeException);
    }

}
