package com.sankuai.dzshoppingguide.product.detail.application.utils;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.sankuai.dzshoppingguide.product.detail.application.utils.model.MaterialInfoModel;
import com.sankuai.dzshoppingguide.product.detail.application.utils.model.PlayInfoModel;
import com.sankuai.dzshoppingguide.product.detail.application.utils.model.TaskInfoModel;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * Unit tests for PlayCenterWrapper.buildPlayInfo method
 */
@ExtendWith(MockitoExtension.class)
public class PlayCenterWrapperBuildPlayInfoTest {

    @Mock
    private JSONObject mockPlayInfo;

    /**
     * Test when input playInfo is null
     */
    @Test
    public void testBuildPlayInfo_NullInput() {
        // arrange
        JSONObject playInfo = null;
        // act
        PlayInfoModel result = PlayCenterWrapper.buildPlayInfo(playInfo);
        // assert
        assertNull(result);
    }

    /**
     * Test with complete valid input data
     */
    @Test
    public void testBuildPlayInfo_CompleteValidData() {
        // arrange
        JSONObject playInfo = new JSONObject();
        JSONArray materialInfoList = new JSONArray();
        JSONObject materialInfo = new JSONObject();
        materialInfo.put("fieldKey", "testKey");
        materialInfo.put("fieldValue", "testValue");
        materialInfoList.add(materialInfo);
        JSONArray taskInfoList = new JSONArray();
        JSONObject taskInfo = new JSONObject();
        taskInfo.put("prizeCount", 1);
        taskInfo.put("status", 1);
        taskInfo.put("prizeInfoList", new JSONArray());
        taskInfoList.add(taskInfo);
        when(mockPlayInfo.getString("title")).thenReturn("Test Title");
        when(mockPlayInfo.getString("subTitle")).thenReturn("Test Subtitle");
        when(mockPlayInfo.getLong("startTime")).thenReturn(1625097600000L);
        when(mockPlayInfo.getLong("endTime")).thenReturn(1625184000000L);
        when(mockPlayInfo.getLong("activityId")).thenReturn(12345L);
        when(mockPlayInfo.getJSONArray("materialInfoList")).thenReturn(materialInfoList);
        when(mockPlayInfo.getJSONArray("taskInfoList")).thenReturn(taskInfoList);
        // act
        PlayInfoModel result = PlayCenterWrapper.buildPlayInfo(mockPlayInfo);
        // assert
        assertNotNull(result);
        assertEquals("Test Title", result.getTitle());
        assertEquals("Test Subtitle", result.getSubTitle());
        assertEquals(1625097600000L, result.getStartTime());
        assertEquals(1625184000000L, result.getEndTime());
        assertEquals(12345L, result.getActivityId());
        List<MaterialInfoModel> resultMaterialList = result.getMaterialInfoList();
        assertNotNull(resultMaterialList);
        assertEquals(1, resultMaterialList.size());
        assertEquals("testKey", resultMaterialList.get(0).getFieldKey());
        assertEquals("testValue", resultMaterialList.get(0).getFieldValue());
        List<TaskInfoModel> resultTaskList = result.getTaskInfoList();
        assertNotNull(resultTaskList);
        assertEquals(1, resultTaskList.size());
        assertEquals(1, resultTaskList.get(0).getPrizeCount());
        assertEquals(1, resultTaskList.get(0).getStatus());
    }

    /**
     * Test with empty arrays for materialInfoList and taskInfoList
     */
    @Test
    public void testBuildPlayInfo_EmptyArrays() {
        // arrange
        when(mockPlayInfo.getString("title")).thenReturn("Test Title");
        when(mockPlayInfo.getString("subTitle")).thenReturn("Test Subtitle");
        when(mockPlayInfo.getLong("startTime")).thenReturn(1625097600000L);
        when(mockPlayInfo.getLong("endTime")).thenReturn(1625184000000L);
        when(mockPlayInfo.getLong("activityId")).thenReturn(12345L);
        when(mockPlayInfo.getJSONArray("materialInfoList")).thenReturn(new JSONArray());
        when(mockPlayInfo.getJSONArray("taskInfoList")).thenReturn(new JSONArray());
        // act
        PlayInfoModel result = PlayCenterWrapper.buildPlayInfo(mockPlayInfo);
        // assert
        assertNotNull(result);
        assertEquals("Test Title", result.getTitle());
        assertEquals("Test Subtitle", result.getSubTitle());
        assertNotNull(result.getMaterialInfoList());
        assertTrue(result.getMaterialInfoList().isEmpty());
        assertNotNull(result.getTaskInfoList());
        assertTrue(result.getTaskInfoList().isEmpty());
    }

    /**
     * Test with null values for optional fields
     */
    @Test
    public void testBuildPlayInfo_NullOptionalFields() {
        // arrange
        when(mockPlayInfo.getString("title")).thenReturn(null);
        when(mockPlayInfo.getString("subTitle")).thenReturn(null);
        when(mockPlayInfo.getLong("startTime")).thenReturn(1625097600000L);
        when(mockPlayInfo.getLong("endTime")).thenReturn(1625184000000L);
        when(mockPlayInfo.getLong("activityId")).thenReturn(12345L);
        when(mockPlayInfo.getJSONArray("materialInfoList")).thenReturn(null);
        when(mockPlayInfo.getJSONArray("taskInfoList")).thenReturn(null);
        // act
        PlayInfoModel result = PlayCenterWrapper.buildPlayInfo(mockPlayInfo);
        // assert
        assertNotNull(result);
        assertNull(result.getTitle());
        assertNull(result.getSubTitle());
        assertNotNull(result.getMaterialInfoList());
        assertTrue(result.getMaterialInfoList().isEmpty());
        assertNotNull(result.getTaskInfoList());
        assertTrue(result.getTaskInfoList().isEmpty());
    }

    /**
     * Test with malformed JSONArray for materialInfoList
     */
    @Test
    public void testBuildPlayInfo_MalformedMaterialInfoList() {
        // arrange
        JSONArray malformedMaterialInfoList = new JSONArray();
        JSONObject invalidMaterialInfo = new JSONObject();
        // Missing required fields
        malformedMaterialInfoList.add(invalidMaterialInfo);
        when(mockPlayInfo.getString("title")).thenReturn("Test Title");
        when(mockPlayInfo.getString("subTitle")).thenReturn("Test Subtitle");
        when(mockPlayInfo.getLong("startTime")).thenReturn(1625097600000L);
        when(mockPlayInfo.getLong("endTime")).thenReturn(1625184000000L);
        when(mockPlayInfo.getLong("activityId")).thenReturn(12345L);
        when(mockPlayInfo.getJSONArray("materialInfoList")).thenReturn(malformedMaterialInfoList);
        when(mockPlayInfo.getJSONArray("taskInfoList")).thenReturn(new JSONArray());
        // act
        PlayInfoModel result = PlayCenterWrapper.buildPlayInfo(mockPlayInfo);
        // assert
        assertNotNull(result);
        assertNotNull(result.getMaterialInfoList());
        assertEquals(1, result.getMaterialInfoList().size());
        assertNull(result.getMaterialInfoList().get(0).getFieldKey());
        assertNull(result.getMaterialInfoList().get(0).getFieldValue());
    }

    /**
     * Test verification of method calls
     */
    @Test
    public void testBuildPlayInfo_VerifyMethodCalls() {
        // arrange
        when(mockPlayInfo.getString("title")).thenReturn("Test Title");
        when(mockPlayInfo.getString("subTitle")).thenReturn("Test Subtitle");
        when(mockPlayInfo.getLong("startTime")).thenReturn(1625097600000L);
        when(mockPlayInfo.getLong("endTime")).thenReturn(1625184000000L);
        when(mockPlayInfo.getLong("activityId")).thenReturn(12345L);
        when(mockPlayInfo.getJSONArray("materialInfoList")).thenReturn(new JSONArray());
        when(mockPlayInfo.getJSONArray("taskInfoList")).thenReturn(new JSONArray());
        // act
        PlayInfoModel result = PlayCenterWrapper.buildPlayInfo(mockPlayInfo);
        // assert
        verify(mockPlayInfo).getString("title");
        verify(mockPlayInfo).getString("subTitle");
        verify(mockPlayInfo).getLong("startTime");
        verify(mockPlayInfo).getLong("endTime");
        verify(mockPlayInfo).getLong("activityId");
        verify(mockPlayInfo).getJSONArray("materialInfoList");
        verify(mockPlayInfo).getJSONArray("taskInfoList");
    }
}
