package com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.period.price;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.sankuai.general.product.query.center.client.builder.model.DealDailyTimePriceBuilder;
import com.sankuai.general.product.query.center.client.builder.model.DealPeriodPriceBuilder;
import com.sankuai.general.product.query.center.client.builder.model.DealWeeklyPriceBuilder;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * Test class for SkuPeriodPriceFetcher
 */
@ExtendWith(MockitoExtension.class)
public class SkuPeriodPriceFetcherFulfillRequestTest {

    private final SkuPeriodPriceFetcher skuPeriodPriceFetcher = new SkuPeriodPriceFetcher();

    @Mock
    private QueryByDealGroupIdRequestBuilder requestBuilder;

    /**
     * Test case for fulfilling request with valid request builder.
     */
    @Test
    public void testFulfillRequestWithValidRequestBuilder() throws Throwable {
        // arrange
        when(requestBuilder.dealWeeklyPrice(any(DealWeeklyPriceBuilder.class))).thenReturn(requestBuilder);
        when(requestBuilder.dealDailyTimePrice(any(DealDailyTimePriceBuilder.class))).thenReturn(requestBuilder);
        when(requestBuilder.dealPeriodPrice(any(DealPeriodPriceBuilder.class))).thenReturn(requestBuilder);
        // act
        skuPeriodPriceFetcher.fulfillRequest(requestBuilder);
        // assert
        verify(requestBuilder).dealWeeklyPrice(any(DealWeeklyPriceBuilder.class));
        verify(requestBuilder).dealDailyTimePrice(any(DealDailyTimePriceBuilder.class));
        verify(requestBuilder).dealPeriodPrice(any(DealPeriodPriceBuilder.class));
    }

    /**
     * Test case for fulfilling request with null request builder.
     */
    @Test
    public void testFulfillRequestWithNullRequestBuilder() throws Throwable {
        // arrange
        QueryByDealGroupIdRequestBuilder nullRequestBuilder = null;
        // act & assert
        Exception exception = null;
        try {
            skuPeriodPriceFetcher.fulfillRequest(nullRequestBuilder);
        } catch (NullPointerException e) {
            exception = e;
        }
        // Validate that the exception is thrown
        assert exception != null : "Expected NullPointerException but no exception was thrown";
        assert exception instanceof NullPointerException : "Expected NullPointerException but got different exception";
    }

    /**
     * Test case for fulfilling request with empty request builder.
     */
    @Test
    public void testFulfillRequestWithEmptyRequestBuilder() throws Throwable {
        // arrange
        when(requestBuilder.dealWeeklyPrice(any(DealWeeklyPriceBuilder.class))).thenReturn(requestBuilder);
        when(requestBuilder.dealDailyTimePrice(any(DealDailyTimePriceBuilder.class))).thenReturn(requestBuilder);
        when(requestBuilder.dealPeriodPrice(any(DealPeriodPriceBuilder.class))).thenReturn(requestBuilder);
        // act
        skuPeriodPriceFetcher.fulfillRequest(requestBuilder);
        // assert
        verify(requestBuilder).dealWeeklyPrice(any(DealWeeklyPriceBuilder.class));
        verify(requestBuilder).dealDailyTimePrice(any(DealDailyTimePriceBuilder.class));
        verify(requestBuilder).dealPeriodPrice(any(DealPeriodPriceBuilder.class));
        verifyNoMoreInteractions(requestBuilder);
    }
}
