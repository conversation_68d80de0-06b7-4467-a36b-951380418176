package com.sankuai.dzshoppingguide.product.detail.spi.review.vo;

import com.sankuai.dz.product.detail.gateway.spi.vo.AbstractModuleVO;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.dianping.mobile.framework.annotation.MobileDo;

import java.io.Serializable;

@MobileDo(id = 0x9fa2)
public class MtReserveReview extends AbstractModuleVO implements Serializable {
    /**
     * 评价信息
     * 结构见 https://mobile.sankuai.com/studio/api/20465/index?tab=management
     */
    @MobileDo.MobileField(key = 0xcce)
    private String content;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    @Override
    public String getModuleKey() {
        return ModuleKeyConstants.MT_RESERVE_REVIEW_MODULE_V1;
    }
}