package com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommend;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.meituan.inf.xmdlog.XMDLogFormat;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftClient;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommend.dto.DealGroupDTOReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommend.dto.RecommendInfoReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
import com.sankuai.dzshoppingguide.product.detail.domain.utils.ThriftAsyncUtils;
import com.sankuai.general.product.query.center.client.builder.model.*;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResponse;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResult;
import com.sankuai.general.product.query.center.client.service.DealGroupQueryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.thrift.TException;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * @Author: zhangyuan103
 * @Date: 2025/5/23
 * @Description 根据推荐的团单id获取团单基础信息
 */
@Fetcher(previousLayerDependencies = {RecommendInfoInShopFetcher.class})
@Slf4j
public class DealBaseInfoFetcher extends NormalFetcherContext<DealGroupDTOReturnValue> {
    @Resource
    private CompositeAtomService compositeAtomService;

    @Resource(name = "dealGroupQueryServiceAsync")
    public DealGroupQueryService dealGroupQueryServiceAsync;

    @Override
    protected CompletableFuture<DealGroupDTOReturnValue> doFetch() throws Exception {
        RecommendInfoReturnValue recommendInfoReturnValue = getDependencyResult(RecommendInfoInShopFetcher.class);
        List<Long> sortedDealGroupIds = Optional.ofNullable(recommendInfoReturnValue)
                .map(RecommendInfoReturnValue::getSortedDealGroupIds).orElse(null);
        if (CollectionUtils.isEmpty(sortedDealGroupIds)) {
            return CompletableFuture.completedFuture(null);
        }
        return queryByDealGroupIds(
                        buildDealGroupIdRequest(sortedDealGroupIds, request.getClientTypeEnum().isMtClientType()))
                .thenApply(response -> {
                    if (response == null || response.getCode() != 0 || response.getData() == null
                            || CollectionUtils.isEmpty(response.getData().getList())) {
                        return null;
                    }
                    return new DealGroupDTOReturnValue(response.getData().getList());
                });
    }

    private QueryByDealGroupIdRequest buildDealGroupIdRequest(List<Long> dealGroupIds, boolean isMt) {
        QueryByDealGroupIdRequest queryCenterRequest = QueryByDealGroupIdRequestBuilder.builder()
                .dealGroupIds(new HashSet<>(dealGroupIds), isMt ? IdTypeEnum.MT : IdTypeEnum.DP)
                .basicInfo(DealGroupBasicInfoBuilder.builder().all()).image(DealGroupImageBuilder.builder().all())
                .dealGroupId(DealGroupIdBuilder.builder().dpDealGroupId())
                .extendImage(DealGroupExtendImageBuilder.builder().extendImage())
                .dealGroupPrice(DealGroupPriceBuilder.builder().all()).build();
        return queryCenterRequest;
    }

    private CompletableFuture<QueryDealGroupListResponse> queryByDealGroupIds(QueryByDealGroupIdRequest req) {
        try {
            dealGroupQueryServiceAsync.queryByDealGroupIds(req);
        } catch (TException e) {
            log.error(XMDLogFormat.build().putTag("scene", "DealBaseInfoFetcher")
                    .putTag("method", "queryByDealGroupIds")
                    .message(String.format("queryByDealGroupIds error, request : %s", JsonCodec.encodeWithUTF8(req))));
        }
        return ThriftAsyncUtils.getThriftFuture();
    }
}
