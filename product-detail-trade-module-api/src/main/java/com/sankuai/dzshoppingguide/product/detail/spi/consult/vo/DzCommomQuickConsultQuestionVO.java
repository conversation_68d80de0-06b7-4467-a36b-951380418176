package com.sankuai.dzshoppingguide.product.detail.spi.consult.vo;

import com.dianping.mobile.framework.annotation.MobileDo;

import java.io.Serializable;

@MobileDo(id = 0x93f3)
public class DzCommomQuickConsultQuestionVO implements Serializable {
    /**
    * 跳转链接
    */
    @MobileDo.MobileField(key = 0x94a8)
    private String entryUrl;

    /**
    * 问题入口文案
    */
    @MobileDo.MobileField(key = 0xdbd2)
    private String entryText;

    /**
    * 是否高亮
    */
    @MobileDo.MobileField(key = 0x4936)
    private boolean highLight;

    public String getEntryUrl() {
        return entryUrl;
    }

    public void setEntryUrl(String entryUrl) {
        this.entryUrl = entryUrl;
    }

    public String getEntryText() {
        return entryText;
    }

    public void setEntryText(String entryText) {
        this.entryText = entryText;
    }

    public boolean getHighLight() {
        return highLight;
    }

    public void setHighLight(boolean highLight) {
        this.highLight = highLight;
    }
}