package com.sankuai.dzshoppingguide.product.detail.application.fetcher.timesdeal.dto;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/14 09:56
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
public class TimesDealRelatedProduct extends FetcherReturnValueDTO {
    List<DealGroupDTO> dealGroupDTOList;
}
