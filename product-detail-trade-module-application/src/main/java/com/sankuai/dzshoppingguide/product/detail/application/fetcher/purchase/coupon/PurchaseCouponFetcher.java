package com.sankuai.dzshoppingguide.product.detail.application.fetcher.purchase.coupon;

import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.sankuai.dealuser.price.display.api.enums.CouponAssignStatusEnum;
import com.sankuai.dealuser.price.display.api.enums.PromoTypeEnum;
import com.sankuai.dealuser.price.display.api.enums.PromoUseTypeEnum;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.ProductNormalPriceFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.dto.ProductPriceReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.utils.PromoInfoHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * @Author: guangyujie
 * @Date: 2025/3/18 15:16
 */
@Fetcher(previousLayerDependencies = {
        ProductNormalPriceFetcher.class
})
@Slf4j
public class PurchaseCouponFetcher extends NormalFetcherContext<PurchaseCouponReturnValue> {

    private PriceDisplayDTO normalPrice;

    @Override
    protected CompletableFuture<PurchaseCouponReturnValue> doFetch() {
        normalPrice = getDependencyResult(ProductNormalPriceFetcher.class, ProductPriceReturnValue.class)
                .map(ProductPriceReturnValue::getPriceDisplayDTO).orElse(null);
        if (normalPrice == null) {
            return CompletableFuture.completedFuture(null);
        }
        List<Map<String, Object>> couponParamForOrderPage = new ArrayList<>();
        //提单页领券购，政府消费券
        Map<String, Object> governmentConsumeCouponPromo = handleGovernmentConsumeCouponPromo();
        if (MapUtils.isNotEmpty(governmentConsumeCouponPromo)) {
            couponParamForOrderPage.add(governmentConsumeCouponPromo);
        }
        //提单页领券购，到综消费券
        Map<String, Object> couponPurchaseCouponPromo = handleCouponPurchaseCouponPromo();
        if (MapUtils.isNotEmpty(couponPurchaseCouponPromo)) {
            couponParamForOrderPage.add(couponPurchaseCouponPromo);
        }
        //团详页领券购
        Set<Long> couponGroupIdsForPurchase = buildCouponGroupIdsForPurchase();
        return CompletableFuture.completedFuture(new PurchaseCouponReturnValue(
                governmentConsumeCouponPromo != null,
                couponPurchaseCouponPromo != null,
                couponParamForOrderPage,
                couponGroupIdsForPurchase
        ));
    }

    /**
     * 政府消费券
     */
    private Map<String, Object> handleGovernmentConsumeCouponPromo() {
        if (!request.getClientTypeEnum().isInApp()) {
            return null;
        }
        if (CollectionUtils.isEmpty(normalPrice.getUsedPromos())) {
            return null;
        }
        PromoDTO governmentConsumeCouponPromoDTO = normalPrice.getUsedPromos()
                .stream()
                .filter(Objects::nonNull)
                .filter(promoDTO -> promoDTO.getIdentity() != null
                        && promoDTO.getIdentity().getPromoType() == PromoTypeEnum.GOVERNMENT_CONSUME_COUPON.getType())
                .findFirst()
                .orElse(null);
        if (governmentConsumeCouponPromoDTO == null) {
            return null;
        }
        return buildParamForOrderPageUrl(governmentConsumeCouponPromoDTO, 1);
    }

    /**
     * 到综消费券二期支持APP和美团微信小程序
     */
    private Map<String, Object> handleCouponPurchaseCouponPromo() {
        if (!request.getClientTypeEnum().isInApp()
                && request.getClientTypeEnum() != ClientTypeEnum.MT_XCX) {
            return null;
        }
        if (CollectionUtils.isEmpty(normalPrice.getUsedPromos())) {
            return null;
        }
        PromoDTO couponPurchasePromoDTO = normalPrice.getUsedPromos()
                .stream()
                .filter(Objects::nonNull)
                .filter(this::isOrderPageCouponPurchase)
                .findFirst()
                .orElse(null);
        if (couponPurchasePromoDTO == null) {
            return null;
        }
        return buildParamForOrderPageUrl(couponPurchasePromoDTO, 2);
    }

    protected Set<Long> buildCouponGroupIdsForPurchase() {
        if (!Lion.getBoolean(
                Environment.getAppName(),
                "com.sankuai.dzshoppingguide.detail.trademodule.purchase.coupon.switch",
                false)) {
            return null;
        }
        List<PromoDTO> couponPromos = normalPrice.getUsedPromos();
        if (CollectionUtils.isEmpty(couponPromos)) {
            return null;
        }
        return couponPromos.stream()
                .filter(Objects::nonNull)
                .filter(couponPromo -> couponPromo.getIdentity().getPromoType() == PromoTypeEnum.COUPON.getType())
                .filter(PromoDTO::isCanAssign)
                .filter(couponPromo -> !isOrderPageCouponPurchase(couponPromo))
                .map(couponPromo -> couponPromo.getIdentity().getPromoId())
                .collect(Collectors.toSet());
    }

    /**
     * 是否是领券购，注意这里的领券购不是团详的领券购
     * 所以如果是领券购则不在团详领券
     */
    private boolean isOrderPageCouponPurchase(PromoDTO promoDTO) {
        return Optional.ofNullable(promoDTO)
                .filter(x -> x.getIdentity() != null && x.getIdentity().getPromoType() == PromoTypeEnum.COUPON.getType())
                .filter(x -> x.getUseType() != null && x.getUseType() == PromoUseTypeEnum.COUPON_PURCHASE.getType())
                .isPresent();
    }

    private @NotNull Map<String, Object> buildParamForOrderPageUrl(PromoDTO item, int sceneType) {
        Map<String, Object> map = new HashMap<>();
        map.put("receiptBatchId", item.getCouponGroupId());
        if (item.getCouponAssignStatus().equals(CouponAssignStatusEnum.ASSIGNED.getCode())) {
            map.put("receiptCode", item.getIdentity().getPromoId());
        }
        String financeExtJson = PromoInfoHelper.getFinanceExtJson(item);
        if (StringUtils.isNotBlank(financeExtJson)) {
            // 政府消费券N选1传的券包密钥
            map.put("ext", financeExtJson);
        }
        map.put("sceneType", sceneType);
        return map;
    }

}
