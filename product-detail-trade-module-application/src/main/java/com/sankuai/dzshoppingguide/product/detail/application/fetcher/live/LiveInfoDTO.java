package com.sankuai.dzshoppingguide.product.detail.application.fetcher.live;

import com.google.common.collect.Maps;
import com.sankuai.dealuser.price.display.api.enums.ConstantsSourceEnum;
import com.sankuai.dealuser.price.display.api.enums.ExtensionKeyEnum;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import com.sankuai.mlive.goods.trade.api.model.GoodsSellingInfoDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.Objects;

/**
 * @Author: guangyujie
 * @Date: 2025/4/7 22:39
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class LiveInfoDTO extends FetcherReturnValueDTO {

    /**
     * 是否是直播渠道品，直播间来的 or 商品本身只能在直播渠道售卖
     */
    public boolean isLiveSourceProduct() {
        return liveId > 0 || onlyLiveChannel;
    }

    /**
     * 直播场次id
     */
    private long liveId;

    /**
     * 仅直播渠道品
     */
    private boolean onlyLiveChannel;

    /**
     * 主播id
     */
    private String anchorId;

    /**
     * 直播秒杀id
     */
    private String liveSecKillActiveId;

    /**
     * 直播侧商品类型
     */
    private int goodsType;

    /**
     * 直播商品售卖状态
     */
    private GoodsSellingInfoDTO goodsSellingInfoDTO;

    /**
     * 判断是否可售
     */
    public boolean isAllowSelling() {
        if (Objects.isNull(goodsSellingInfoDTO)) {
            return true;
        } else {
            return goodsSellingInfoDTO.isAllowSelling();
        }
    }

    public Map<String, String> getLiveSecKillParams() {
        Map<String, String> params = Maps.newHashMap();
        if (liveId > 0) {
            params.put(ExtensionKeyEnum.Source.getDesc(), ConstantsSourceEnum.M_LIVE.getCode());
            // 直播场次ID
            params.put(ExtensionKeyEnum.LiveStreamingSceneId.getDesc(), String.valueOf(liveId));
        }
        if (StringUtils.isNotBlank(anchorId)) {
            // 主播ID
            params.put(ExtensionKeyEnum.AnchorId.getDesc(), anchorId);
        }
        if (StringUtils.isNotBlank(liveSecKillActiveId)) {
            // 直播秒杀ID
            params.put(ExtensionKeyEnum.LiveSecKillActiveId.getDesc(), liveSecKillActiveId);
        }
        return params;
    }

}
