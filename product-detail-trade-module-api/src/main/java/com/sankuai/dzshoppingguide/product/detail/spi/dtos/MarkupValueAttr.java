package com.sankuai.dzshoppingguide.product.detail.spi.dtos;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2025/3/3
 */
@Data
@TypeDoc(description = "加价属性")
@MobileDo(id = 0xb1bf)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MarkupValueAttr implements Serializable {

    @FieldDoc(description = "描述信息")
    @MobileDo.MobileField(key = 0x4e50)
    private List<String> descList;

    @FieldDoc(description = "价格信息")
    @MobileDo.MobileField(key = 0xb716)
    private String price;

    @FieldDoc(description = "唯一标识")
    @MobileDo.MobileField(key = 0x4698)
    private String identityKey;

    @FieldDoc(description = "标题")
    @MobileDo.MobileField(key = 0x24cc)
    private String title;
}
