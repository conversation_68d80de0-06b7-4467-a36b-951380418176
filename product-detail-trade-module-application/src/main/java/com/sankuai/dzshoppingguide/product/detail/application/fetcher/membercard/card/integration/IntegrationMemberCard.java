package com.sankuai.dzshoppingguide.product.detail.application.fetcher.membercard.card.integration;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import com.sankuai.dzshoppingguide.product.detail.application.service.dto.ShopMemberDetailV;
import com.sankuai.mpmctmember.query.common.enums.MemberChargeTypeEnum;
import com.sankuai.mpmctmember.query.thrift.dto.QueryPlanAndUserIdentityDetailResp;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class IntegrationMemberCard extends FetcherReturnValueDTO {

    /**
     * @see MemberChargeTypeEnum
     */
    private int memberCardType;

    /**
     * 商家会员卡权益信息
     */
    private ShopMemberDetailV memberCardInterest;

    /**
     * 商家会员卡详情信息
     */
    private QueryPlanAndUserIdentityDetailResp memberCardDetail;

    /**
     * 是否免费商家会员卡
     * @return
     */
    public boolean isFreeMemberCard() {
        return memberCardType == MemberChargeTypeEnum.FREE.getCode();
    }

    /**
     * 是否付费商家会员卡
     * @return
     */
    public boolean isPremiumMemberCard() {
        return memberCardType == MemberChargeTypeEnum.CHARGE.getCode();
    }

}
