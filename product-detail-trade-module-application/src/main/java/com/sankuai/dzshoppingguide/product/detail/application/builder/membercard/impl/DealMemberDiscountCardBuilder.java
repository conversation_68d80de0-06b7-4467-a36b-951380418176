package com.sankuai.dzshoppingguide.product.detail.application.builder.membercard.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.BuilderTypeEnum;
import com.sankuai.dzcard.navigation.api.dto.QueryCardInfoDTO;
import com.sankuai.dzshoppingguide.product.detail.application.builder.membercard.basebuilder.BaseAbstractMemberCardBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.discountcard.DealDiscountCardFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.discountcard.DealDiscountCardResult;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.FetcherDAGStarter;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DiscountCardContents;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.CardTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.membercard.vo.BasicCardModuleVO;
import com.sankuai.dzshoppingguide.product.detail.spi.membercard.vo.DiscountCardModuleVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/3/16 14:42
 */
@Builder(
        builderType = BuilderTypeEnum.VARIABLE_BUILDER,
        moduleKey = ModuleKeyConstants.MEMBER_CARD,
        startFetcher = FetcherDAGStarter.class,
        dependentFetchers = {
                DealDiscountCardFetcher.class
        }
)
@Slf4j
public class DealMemberDiscountCardBuilder extends BaseAbstractMemberCardBuilder {

    @Override
    public BasicCardModuleVO doBuild() {
        DealDiscountCardResult discountCardResult = getDependencyResult(DealDiscountCardFetcher.class);
        if (discountCardResult == null || discountCardResult.getQueryCardInfoDTO() == null) {
            return null;
        }
        QueryCardInfoDTO queryCardInfoDTO = discountCardResult.getQueryCardInfoDTO();
        boolean showCard = isShowCard(queryCardInfoDTO);
        if (!showCard) {
            return null;
        }

        DiscountCardModuleVO cardModuleVO = new DiscountCardModuleVO();
        cardModuleVO.setCardType(CardTypeEnum.DISCOUNT_CARD.getCode());
        cardModuleVO.setContents(buildContents(queryCardInfoDTO.getLongDisplayText()));
        cardModuleVO.setJumpUrl(queryCardInfoDTO.getLinkUrl());
        cardModuleVO.setBackgroundColor("#FFEDDE");
        cardModuleVO.setPrefixIcon("https://p0.meituan.net/dealproduct/18acc242c09a2e2c1ad1abee202ed3521601.png");
        cardModuleVO.setSuffixIcon("https://p0.meituan.net/dealproduct/9385c3035492d55b7354ce5bbdea7009380.png");
        cardModuleVO.setDealPriceWithCard(queryCardInfoDTO.getDealPriceWithCard());
        cardModuleVO.setDealPriceWithPromo(queryCardInfoDTO.getDealPriceWithPromo());
        cardModuleVO.setShow(showCard);
        cardModuleVO.setLimitTimePromoDesc(queryCardInfoDTO.getLimitTimePromoDesc());
        cardModuleVO.setHasCard(queryCardInfoDTO.isHasUserCard());
        cardModuleVO.setCardNo(queryCardInfoDTO.getCardNo());
        return cardModuleVO;
    }

    private static boolean isShowCard(QueryCardInfoDTO buyCardVO) {
        if (Objects.isNull(buyCardVO)) {
            return false;
        }
        return StringUtils.isNotBlank(buyCardVO.getShortDisplayText())
                && StringUtils.isNotBlank(buyCardVO.getLongDisplayText()) && !buyCardVO.isHasUserCard()
                && StringUtils.isNotBlank(buyCardVO.getDealPriceWithCard())
                && StringUtils.isNotBlank(buyCardVO.getDealPriceWithPromo())
                && new BigDecimal(buyCardVO.getDealPriceWithCard())
                        .compareTo(new BigDecimal(buyCardVO.getDealPriceWithPromo())) <= 0;

    }

    private List<DiscountCardContents> buildContents(String desc) {
        try {
            if (StringUtils.isBlank(desc)) {
                return Lists.newArrayList();
            }
            // backgroundcolor. fontweight. strikethrough. text. textcolor. textsize
            List<JSONObject> jsonObjects = JSON.parseArray(desc, JSONObject.class);
            if (CollectionUtils.isEmpty(jsonObjects)) {
                return Lists.newArrayList();
            }

            return jsonObjects.stream().filter(Objects::nonNull).map(obj -> {
                DiscountCardContents content = new DiscountCardContents();
                content.setContent(obj.getString("text")); // 设置文案
                content.setFontColor(obj.getString("textcolor")); // 设置字体颜色
                content.setFontSize(12); // 设置字体大小
                return content;
            }).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("buildContents error, desc:{}", desc, e);
        }
        return Lists.newArrayList();
    }
}
