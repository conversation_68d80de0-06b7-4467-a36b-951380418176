package com.sankuai.dzshoppingguide.product.detail.application.fetcher.statesubsidies;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import com.sankuai.statesubsidies.c.thrift.constant.QualificationStatusEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2025-04-22
 * @desc 国家补贴资格
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CountrySubsidiesQualificationDTO extends FetcherReturnValueDTO {
    /**
     * 国家补贴资格状态
     */
    private QualificationStatusEnum qualificationStatusEnum;
}
