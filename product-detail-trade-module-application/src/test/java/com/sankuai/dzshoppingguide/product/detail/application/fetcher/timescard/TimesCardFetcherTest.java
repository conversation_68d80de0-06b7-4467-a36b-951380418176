package com.sankuai.dzshoppingguide.product.detail.application.fetcher.timescard;

import static org.junit.jupiter.api.Assertions.assertEquals;
import com.sankuai.merchantcard.timescard.exposure.res.QueryDealGroupCardBarSummaryRequest;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class TimesCardFetcherTest {

    /**
     * 测试 buildRequest 方法，正常创建 QueryDealGroupCardBarSummaryRequest 对象
     */
    @Test
    public void testBuildRequestNormal() throws Throwable {
        // arrange
        TimesCardFetcher timesCardFetcher = new TimesCardFetcher();
        int dealGroupId = 1;
        long longShopId = 1L;
        long userId = 1L;
        int code = 1;
        // act
        QueryDealGroupCardBarSummaryRequest result = timesCardFetcher.buildRequest(dealGroupId, longShopId, userId, code);
        // assert
        assertEquals(longShopId, result.getLongShopId());
        assertEquals(code, result.getPlatform());
        assertEquals(dealGroupId, result.getDealGroupId());
        assertEquals(userId, result.getUserId());
    }
}
