package com.sankuai.dzshoppingguide.product.detail.application.utils;

import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dzcard.navigation.api.dto.CardQualifyEventIdDTO;
import lombok.Data;

import java.io.Serializable;

@Data
public class PriceContext implements Serializable {

    private boolean isZuLiaoButtonNewStyle =false;

    private PriceDisplayDTO dcCardMemberPrice;//折扣卡

    private PriceDisplayDTO idlePromoPrice;
    private PriceDisplayDTO normalPrice;
    private PriceDisplayDTO costEffectivePrice;
    private PriceDisplayDTO dealPromoPrice;
    private PriceDisplayDTO atmosphereBarAndGeneralPromoDetailPrice;

}
