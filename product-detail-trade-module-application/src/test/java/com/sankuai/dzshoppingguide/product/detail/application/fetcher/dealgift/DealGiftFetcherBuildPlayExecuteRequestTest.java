package com.sankuai.dzshoppingguide.product.detail.application.fetcher.dealgift;

import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.cityId.CityIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.dealgroup.DealGroupIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapper;
import com.sankuai.mktplay.center.mkt.play.center.client.PlayExecuteRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class DealGiftFetcherBuildPlayExecuteRequestTest {

    @InjectMocks
    private DealGiftFetcher dealGiftFetcher;

    @Mock
    private ProductDetailPageRequest request;

    private DealGroupIdMapper dealGroupIdMapper;

    private ShopIdMapper shopIdMapper;

    private CityIdMapper cityIdMapper;

    @BeforeEach
    void setUp() {
        dealGroupIdMapper = new DealGroupIdMapper();
        shopIdMapper = new ShopIdMapper();
        cityIdMapper = new CityIdMapper();
    }

    /**
     * Test building PlayExecuteRequest with MT client type
     */
    @Test
    public void testBuildPlayExecuteRequest_WithMTClientType() {
        // arrange
        long playId = 123L;
        long userId = 456L;
        Map<String, String> requestMap = new HashMap<>();
        requestMap.put("key", "value");
        when(request.getUserId()).thenReturn(userId);
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.MT_APP);
        // act
        // PlayExecuteRequest result = dealGiftFetcher.buildPlayExecuteRequest(dealGroupIdMapper, shopIdMapper, cityIdMapper, playId, requestMap);
        PlayExecuteRequest result = dealGiftFetcher.buildPlayExecuteRequest(playId, requestMap);
        // assert
        assertNotNull(result);
        assertEquals(playId, result.getPlayId());
        assertEquals(userId, result.getUserInfo().getUserId());
        // MT user source
        assertEquals(1, result.getUserInfo().getUserSource());
        assertEquals(requestMap, result.getRequest());
    }

    /**
     * Test building PlayExecuteRequest with DP client type
     */
    @Test
    public void testBuildPlayExecuteRequest_WithDPClientType() {
        // arrange
        long playId = 123L;
        long userId = 456L;
        Map<String, String> requestMap = new HashMap<>();
        requestMap.put("key", "value");
        when(request.getUserId()).thenReturn(userId);
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.DP_APP);
        // act
        PlayExecuteRequest result = dealGiftFetcher.buildPlayExecuteRequest(playId, requestMap);
        // assert
        assertNotNull(result);
        assertEquals(playId, result.getPlayId());
        assertEquals(userId, result.getUserInfo().getUserId());
        // DP user source
        assertEquals(2, result.getUserInfo().getUserSource());
        assertEquals(requestMap, result.getRequest());
    }

    /**
     * Test building PlayExecuteRequest with null request map
     */
    @Test
    public void testBuildPlayExecuteRequest_WithNullRequestMap() {
        // arrange
        long playId = 123L;
        long userId = 456L;
        when(request.getUserId()).thenReturn(userId);
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.MT_APP);
        // act
        PlayExecuteRequest result = dealGiftFetcher.buildPlayExecuteRequest(playId, null);
        // assert
        assertNotNull(result);
        assertEquals(playId, result.getPlayId());
        assertEquals(userId, result.getUserInfo().getUserId());
        assertNull(result.getRequest());
    }

    /**
     * Test building PlayExecuteRequest with empty request map
     */
    @Test
    public void testBuildPlayExecuteRequest_WithEmptyRequestMap() {
        // arrange
        long playId = 123L;
        long userId = 456L;
        Map<String, String> requestMap = new HashMap<>();
        when(request.getUserId()).thenReturn(userId);
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.MT_APP);
        // act
        PlayExecuteRequest result = dealGiftFetcher.buildPlayExecuteRequest(playId, requestMap);
        // assert
        assertNotNull(result);
        assertEquals(playId, result.getPlayId());
        assertEquals(userId, result.getUserInfo().getUserId());
        assertTrue(result.getRequest().isEmpty());
    }

    /**
     * Test building PlayExecuteRequest with zero playId
     */
    @Test
    public void testBuildPlayExecuteRequest_WithZeroPlayId() {
        // arrange
        long playId = 0L;
        long userId = 456L;
        Map<String, String> requestMap = new HashMap<>();
        requestMap.put("key", "value");
        when(request.getUserId()).thenReturn(userId);
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.MT_APP);
        // act
        PlayExecuteRequest result = dealGiftFetcher.buildPlayExecuteRequest(playId, requestMap);
        // assert
        assertNotNull(result);
        assertEquals(0L, result.getPlayId());
        assertEquals(userId, result.getUserInfo().getUserId());
        assertEquals(requestMap, result.getRequest());
    }
}
