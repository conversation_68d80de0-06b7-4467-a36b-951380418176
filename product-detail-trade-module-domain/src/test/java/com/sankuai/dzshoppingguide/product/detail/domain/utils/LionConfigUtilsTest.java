package com.sankuai.dzshoppingguide.product.detail.domain.utils;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.lion.client.Lion;
import com.sankuai.dzshoppingguide.product.detail.domain.utils.dtos.DealRepurchaseConfig;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.After;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.HashMap;
import java.util.Map;
import org.junit.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;

@RunWith(MockitoJUnitRunner.class)
public class LionConfigUtilsTest {

    private static final String VALID_MT_SHOP_ID = "123456";

    private static final String VALID_DP_SHOP_ID = "789012";

    private static final String INVALID_SHOP_ID = "999999";

    /**
     * Test when repurchaseConfig is null
     */
    @Test
    public void testEnableRepurchaseWhenConfigIsNull() throws Throwable {
        // arrange
        int categoryId = 123;
        boolean isMt = true;
        int cityId = 456;
        // act
        boolean result = LionConfigUtils.enableRepurchase(categoryId, isMt, cityId);
        // assert
        assertFalse(result);
    }

    /**
     * Test when enableSwitch is false
     */
    @Test
    public void testEnableRepurchaseWhenEnableSwitchIsFalse() throws Throwable {
        // arrange
        int categoryId = 123;
        boolean isMt = true;
        int cityId = 456;
        // act
        boolean result = LionConfigUtils.enableRepurchase(categoryId, isMt, cityId);
        // assert
        assertFalse(result);
    }

    /**
     * Test when allPass is true
     */
    @Test
    public void testEnableRepurchaseWhenAllPassIsTrue() throws Throwable {
        // arrange
        int categoryId = 123;
        boolean isMt = true;
        int cityId = 456;
        // act
        boolean result = LionConfigUtils.enableRepurchase(categoryId, isMt, cityId);
        // assert
        assertFalse(result);
    }

    /**
     * Test when both categoryIds and mtCityIds are not empty and contain the input values
     */
    @Test
    public void testEnableRepurchaseWhenBothCategoryAndMtCityMatch() throws Throwable {
        // arrange
        int categoryId = 123;
        boolean isMt = true;
        int cityId = 456;
        // act
        boolean result = LionConfigUtils.enableRepurchase(categoryId, isMt, cityId);
        // assert
        assertFalse(result);
    }

    /**
     * Test when both categoryIds and dpCityIds are not empty and contain the input values
     */
    @Test
    public void testEnableRepurchaseWhenBothCategoryAndDpCityMatch() throws Throwable {
        // arrange
        int categoryId = 123;
        boolean isMt = false;
        int cityId = 456;
        // act
        boolean result = LionConfigUtils.enableRepurchase(categoryId, isMt, cityId);
        // assert
        assertFalse(result);
    }

    /**
     * Test when both categoryIds and cityIds are not empty but category doesn't match
     */
    @Test
    public void testEnableRepurchaseWhenCategoryDoesNotMatch() throws Throwable {
        // arrange
        int categoryId = 999;
        boolean isMt = true;
        int cityId = 456;
        // act
        boolean result = LionConfigUtils.enableRepurchase(categoryId, isMt, cityId);
        // assert
        assertFalse(result);
    }

    /**
     * Test when both categoryIds and cityIds are not empty but city doesn't match
     */
    @Test
    public void testEnableRepurchaseWhenCityDoesNotMatch() throws Throwable {
        // arrange
        int categoryId = 123;
        boolean isMt = true;
        int cityId = 999;
        // act
        boolean result = LionConfigUtils.enableRepurchase(categoryId, isMt, cityId);
        // assert
        assertFalse(result);
    }

    /**
     * Test when only categoryIds is not empty and contains the input value
     */
    @Test
    public void testEnableRepurchaseWhenOnlyCategoryMatches() throws Throwable {
        // arrange
        int categoryId = 123;
        boolean isMt = true;
        int cityId = 456;
        // act
        boolean result = LionConfigUtils.enableRepurchase(categoryId, isMt, cityId);
        // assert
        assertFalse(result);
    }

    /**
     * Test when only categoryIds is not empty but doesn't contain the input value
     */
    @Test
    public void testEnableRepurchaseWhenOnlyCategoryDoesNotMatch() throws Throwable {
        // arrange
        int categoryId = 999;
        boolean isMt = true;
        int cityId = 456;
        // act
        boolean result = LionConfigUtils.enableRepurchase(categoryId, isMt, cityId);
        // assert
        assertFalse(result);
    }

    /**
     * Test when only cityIds is not empty and contains the input value (MT platform)
     */
    @Test
    public void testEnableRepurchaseWhenOnlyMtCityMatches() throws Throwable {
        // arrange
        int categoryId = 123;
        boolean isMt = true;
        int cityId = 456;
        // act
        boolean result = LionConfigUtils.enableRepurchase(categoryId, isMt, cityId);
        // assert
        assertFalse(result);
    }

    /**
     * Test when only cityIds is not empty and contains the input value (DP platform)
     */
    @Test
    public void testEnableRepurchaseWhenOnlyDpCityMatches() throws Throwable {
        // arrange
        int categoryId = 123;
        boolean isMt = false;
        int cityId = 456;
        // act
        boolean result = LionConfigUtils.enableRepurchase(categoryId, isMt, cityId);
        // assert
        assertFalse(result);
    }

    /**
     * Test when only cityIds is not empty but doesn't contain the input value
     */
    @Test
    public void testEnableRepurchaseWhenOnlyCityDoesNotMatch() throws Throwable {
        // arrange
        int categoryId = 123;
        boolean isMt = true;
        int cityId = 999;
        // act
        boolean result = LionConfigUtils.enableRepurchase(categoryId, isMt, cityId);
        // assert
        assertFalse(result);
    }

    /**
     * Test when both categoryIds and cityIds are empty
     */
    @Test
    public void testEnableRepurchaseWhenBothCategoryAndCityListsAreEmpty() throws Throwable {
        // arrange
        int categoryId = 123;
        boolean isMt = true;
        int cityId = 456;
        // act
        boolean result = LionConfigUtils.enableRepurchase(categoryId, isMt, cityId);
        // assert
        assertFalse(result);
    }

    @Test
    public void testIsComparePriceShopBlackListMapIsEmpty() throws Throwable {
        // act
        boolean result = LionConfigUtils.isComparePriceShopBlackList(true, VALID_MT_SHOP_ID);
        // assert
        // 验证返回结果是boolean类型
        assertNotNull("Result should not be null", result);
        // 记录实际结果，便于后续分析
        System.out.println("MT shop " + VALID_MT_SHOP_ID + " blacklist status: " + result);
    }

    @Test
    public void testIsComparePriceShopBlackListMapNotContainsKey() throws Throwable {
        // act
        boolean result = LionConfigUtils.isComparePriceShopBlackList(false, VALID_DP_SHOP_ID);
        // assert
        assertNotNull("Result should not be null", result);
        // 记录实际结果，便于后续分析
        System.out.println("DP shop " + VALID_DP_SHOP_ID + " blacklist status: " + result);
    }

    @Test
    public void testIsComparePriceShopBlackListMapContainsKeyButNotShopIdStr() throws Throwable {
        // act
        boolean result = LionConfigUtils.isComparePriceShopBlackList(true, INVALID_SHOP_ID);
        // assert
        assertFalse("Invalid shop ID should not be in blacklist", result);
    }

    @Test
    public void testIsComparePriceShopBlackListMapContainsKeyAndShopIdStr() throws Throwable {
        // act
        boolean result = LionConfigUtils.isComparePriceShopBlackList(true, "");
        // assert
        assertFalse("Empty shop ID should not be in blacklist", result);
    }

    @Test
    public void testIsComparePriceShopBlackListWithNullShopId() throws Throwable {
        // act & assert
        try {
            boolean result = LionConfigUtils.isComparePriceShopBlackList(true, null);
            assertFalse("Null shop ID should not be in blacklist", result);
        } catch (Exception e) {
            fail("Should handle null shopId without throwing exception");
        }
    }

    @Test
    public void testIsComparePriceShopBlackListCrossPlatform() throws Throwable {
        // act
        boolean mtResult = LionConfigUtils.isComparePriceShopBlackList(true, VALID_MT_SHOP_ID);
        boolean dpResult = LionConfigUtils.isComparePriceShopBlackList(false, VALID_MT_SHOP_ID);
        // assert
        // 验证结果可能不同，但都应该是有效的boolean值
        assertNotNull("MT result should not be null", mtResult);
        assertNotNull("DP result should not be null", dpResult);
        // 记录跨平台结果比较
        System.out.println("Cross platform comparison for shop " + VALID_MT_SHOP_ID + ": MT=" + mtResult + ", DP=" + dpResult);
    }
}
