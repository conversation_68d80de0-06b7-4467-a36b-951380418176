package com.sankuai.dzshoppingguide.product.detail.application.builder.membercard;

import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.BaseBuilder;
import com.sankuai.dz.product.detail.gateway.spi.enums.ProductTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.membercard.card.integration.IntegrationMemberCard;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.membercard.card.integration.IntegrationMemberCardFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.FetcherDAGStarter;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.CardTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.membercard.vo.FreeMemberCardModuleVO;
import com.sankuai.mpmctmember.query.common.enums.MemberChargeTypeEnum;
import com.sankuai.mpmctmember.query.common.enums.UserMemberTypeEnum;
import com.sankuai.mpmctmember.query.thrift.dto.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/3/3 16:36
 */
@Builder(
        moduleKey = ModuleKeyConstants.FREE_MERCHANT_CARD, startFetcher = FetcherDAGStarter.class,
        dependentFetchers = {IntegrationMemberCardFetcher.class}
)
@Slf4j
public class FreeMemberCardBuilder extends BaseBuilder<FreeMemberCardModuleVO> {

    @Override
    public FreeMemberCardModuleVO doBuild() {
        if (request.getProductTypeEnum() != ProductTypeEnum.DEAL) {
            return null;
        }
        IntegrationMemberCard integrationMemberCard = getDependencyResult(IntegrationMemberCardFetcher.class);
        if (Objects.isNull(integrationMemberCard)) {
            return null;
        }

        if (!integrationMemberCard.isFreeMemberCard()) {
            return null;
        }

        QueryPlanAndUserIdentityDetailResp freeMemberCardInfo = integrationMemberCard.getMemberCardDetail();

        if (Objects.isNull(freeMemberCardInfo)) {
            return null;
        }
        MemberPlanDetailDTO memberPlanDetailDTO = freeMemberCardInfo.getMemberPlanDetailDTO();
        UserIdentityDTO userIdentityDTO = freeMemberCardInfo.getUserIdentityDTO();
        if (Objects.isNull(memberPlanDetailDTO) || Objects.isNull(userIdentityDTO)) {
            return null;
        }
        MemberPlanBaseDTO baseDTO = memberPlanDetailDTO.getBaseDTO();
        int chargeType = Optional.ofNullable(baseDTO).map(MemberPlanBaseDTO::getChargeType).orElse(0);
        if (!Objects.equals(MemberChargeTypeEnum.FREE.getCode(), chargeType)) {
            return null;
        }
        FreeMemberCardModuleVO freeMemberCardModuleVO = new FreeMemberCardModuleVO();
        freeMemberCardModuleVO.setCardType(CardTypeEnum.FREE_MEMBER_CARD.getCode());
        freeMemberCardModuleVO.setJumpUrl(memberPlanDetailDTO.getMemberPageLink());
        freeMemberCardModuleVO.setShow(true);
        freeMemberCardModuleVO.setMemberCardName(
                Optional.ofNullable(baseDTO).map(MemberPlanBaseDTO::getPlanName).orElse(StringUtils.EMPTY));
        freeMemberCardModuleVO.setMemberCardLogo(Optional.ofNullable(baseDTO)
                .map(MemberPlanBaseDTO::getCreateCardTemplate).map(CardTemplateDTO::getLogo).orElse(StringUtils.EMPTY));
        freeMemberCardModuleVO.setRegisterMemberText(Optional.ofNullable(baseDTO)
                .map(MemberPlanBaseDTO::getCreateCardTemplate).map(CardTemplateDTO::getText).orElse(StringUtils.EMPTY));

        freeMemberCardModuleVO.setMemberCountDesc(memberPlanDetailDTO.getMemberCountText());
        freeMemberCardModuleVO.setCardNumber(userIdentityDTO.getCardNum());
        freeMemberCardModuleVO.setMemberName(userIdentityDTO.getUserName());
        freeMemberCardModuleVO.setMember(userIdentityDTO.isMember());
        freeMemberCardModuleVO.setNewMember(userIdentityDTO.isMember()
                && StringUtils.equals(UserMemberTypeEnum.NEW_MEMBER.getCode(), userIdentityDTO.getMemberType()));
        freeMemberCardModuleVO.setLevelName(buildLevelName(userIdentityDTO, memberPlanDetailDTO));
        return freeMemberCardModuleVO;
    }

    /**
     * 如果当前用户是会员，则取实际的等级 如果不是会员则展示兜底等级信息（最小等级,map中key最小的）
     * 
     * @param userIdentityDTO
     * @param memberPlanDetailDTO
     * @return
     */
    private String buildLevelName(UserIdentityDTO userIdentityDTO, MemberPlanDetailDTO memberPlanDetailDTO) {
        Map<Integer, MemberLevelDTO> level2MemberLevelMap = memberPlanDetailDTO.getLevel2MemberLevelMap();

        if (MapUtils.isEmpty(level2MemberLevelMap)) {
            return StringUtils.EMPTY;
        }

        if (!userIdentityDTO.isMember()) {
            return getLowestLevelName(level2MemberLevelMap);
        }
        Integer memberGrade = Optional.ofNullable(userIdentityDTO.getMemberGrade()).orElse(-1);
        MemberLevelDTO currentLevel = level2MemberLevelMap.getOrDefault(memberGrade, null);
        return Optional.ofNullable(currentLevel).map(MemberLevelDTO::getLevelName).orElse(StringUtils.EMPTY);
    }

    private static String getLowestLevelName(Map<Integer, MemberLevelDTO> level2MemberLevelMap) {
        if (MapUtils.isEmpty(level2MemberLevelMap)) {
            return StringUtils.EMPTY;
        }
        return level2MemberLevelMap.entrySet().stream().min(Map.Entry.comparingByKey()).map(Map.Entry::getValue)
                .map(MemberLevelDTO::getLevelName).orElse(StringUtils.EMPTY);
    }
}
