package com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.component.trade.block.shopping.cart;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.component.trade.block.buy.button.shopping.cart.AddShoppingCardButtonComponent;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * Test cases for {@link AddShoppingCardButtonComponent#isNewWearableNailDeal}
 */
@ExtendWith(MockitoExtension.class)
public class AddShoppingCardButtonComponentTest {

    @Mock
    private ProductBaseInfo productBaseInfo;

    @Mock
    private ProductAttr productAttr;

    @Mock
    private DealGroupCategoryDTO category;

    /**
     * Test when both inputs are null
     */
    @Test
    public void testIsNewWearableNailDeal_BothInputsNull() {
        // arrange
        // act
        boolean result = AddShoppingCardButtonComponent.isNewWearableNailDeal(null, null);
        // assert
        assertFalse(result);
    }

    /**
     * Test when productBaseInfo is null
     */
    @Test
    public void testIsNewWearableNailDeal_ProductBaseInfoNull() {
        // arrange
        // act
        boolean result = AddShoppingCardButtonComponent.isNewWearableNailDeal(null, productAttr);
        // assert
        assertFalse(result);
    }

    /**
     * Test when productAttr is null
     */
    @Test
    public void testIsNewWearableNailDeal_ProductAttrNull() {
        // arrange
        // act
        boolean result = AddShoppingCardButtonComponent.isNewWearableNailDeal(productBaseInfo, null);
        // assert
        assertFalse(result);
    }

    /**
     * Test when category ID doesn't match
     */
    @Test
    public void testIsNewWearableNailDeal_CategoryIdNotMatch() {
        // arrange
        when(productBaseInfo.getCategory()).thenReturn(category);
        when(category.getCategoryId()).thenReturn(501L);
        // act
        boolean result = AddShoppingCardButtonComponent.isNewWearableNailDeal(productBaseInfo, productAttr);
        // assert
        assertFalse(result);
    }

    /**
     * Test when service type doesn't match
     */
    @Test
    public void testIsNewWearableNailDeal_ServiceTypeNotMatch() {
        // arrange
        when(productBaseInfo.getCategory()).thenReturn(category);
        when(category.getCategoryId()).thenReturn(502L);
        when(category.getServiceType()).thenReturn("其他服务");
        // act
        boolean result = AddShoppingCardButtonComponent.isNewWearableNailDeal(productBaseInfo, productAttr);
        // assert
        assertFalse(result);
    }

    /**
     * Test when tag attribute doesn't exist
     */
    @Test
    public void testIsNewWearableNailDeal_TagNotExist() {
        // arrange
        when(productBaseInfo.getCategory()).thenReturn(category);
        when(category.getCategoryId()).thenReturn(502L);
        when(category.getServiceType()).thenReturn("穿戴甲");
        when(productAttr.getProductAttr("tag_unifyProduct")).thenReturn(Optional.empty());
        // act
        boolean result = AddShoppingCardButtonComponent.isNewWearableNailDeal(productBaseInfo, productAttr);
        // assert
        assertFalse(result);
    }

    /**
     * Test positive case when all conditions match
     */
    @Test
    public void testIsNewWearableNailDeal_AllConditionsMatch() {
        // arrange
        when(productBaseInfo.getCategory()).thenReturn(category);
        when(category.getCategoryId()).thenReturn(502L);
        when(category.getServiceType()).thenReturn("穿戴甲");
        when(productAttr.getProductAttr("tag_unifyProduct")).thenReturn(Optional.of(new AttrDTO()));
        // act
        boolean result = AddShoppingCardButtonComponent.isNewWearableNailDeal(productBaseInfo, productAttr);
        // assert
        assertTrue(result);
    }
}
