package com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.component.quick.entrance.shop;

import com.alibaba.fastjson.JSON;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.utils.shop.ShopUrlUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.enums.OpenTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.extend.redirect.SimpleRedirectActionVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.component.quick.entrance.QuickEntranceButtonVO;
import lombok.extern.slf4j.Slf4j;

/**
 * @Author: guangyujie
 * @Date: 2025/3/18 20:17
 */
@Slf4j
public class ShopQuickEntranceComponent {

    public static QuickEntranceButtonVO build(final ShopIdMapper shopIdMapper,
                                              final ProductDetailPageRequest request) {
        try {
            if (shopIdMapper == null) {
                throw new IllegalArgumentException("无最佳适用门店，理论上不可能!!!");
            }
            QuickEntranceButtonVO button = new QuickEntranceButtonVO();
            button.setButtonText("门店");
            button.setButtonPic("https://p0.meituan.net/ingee/52846ce7ec79a01ad041615fecbcda9a1734.png");
            button.setActionData(new SimpleRedirectActionVO(
                    OpenTypeEnum.redirect, ShopUrlUtils.getShopDetailUrl(shopIdMapper, request)
            ));
            return button;
        } catch (Throwable throwable) {
            log.error("ShopQuickEntranceComponent.build,request:{}", JSON.toJSONString(request), throwable);
            return null;
        }
    }

}
