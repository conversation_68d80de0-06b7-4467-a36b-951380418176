package com.sankuai.dzshoppingguide.product.detail.application.fetcher.bookingtime;

import com.google.common.collect.Lists;
import com.meituan.inf.xmdlog.XMDLogFormat;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftClient;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.FetcherDAGStarter;
import com.sankuai.dzshoppingguide.product.detail.application.utils.LionConfigUtils;
import com.sankuai.dzshoppingguide.product.detail.application.utils.MultiTradeUtils;
import com.sankuai.dzshoppingguide.product.detail.domain.utils.ThriftAsyncUtils;
import com.sankuai.spt.statequery.api.request.QueryShopBookInfoRequest;
import com.sankuai.spt.statequery.api.response.QueryShopBookInfoResponse;
import com.sankuai.spt.statequery.api.service.ShopBookInfoQueryService;
import lombok.extern.slf4j.Slf4j;

import java.util.Optional;
import java.util.concurrent.CompletableFuture;

@Fetcher(
        previousLayerDependencies = {FetcherDAGStarter.class, ShopIdMapperFetcher.class, ProductBaseInfoFetcher.class}
)
@Slf4j
public class ShopBookingFetcher extends NormalFetcherContext<ShopBookingReturnValue> {

    @MdpThriftClient(timeout = 1000, testTimeout = 5000, remoteAppKey = "com.sankuai.spt.statequery", async = true)
    private ShopBookInfoQueryService shopBookInfoQueryService;

    private ShopIdMapper shopIdMapper;

    @Override
    protected CompletableFuture<ShopBookingReturnValue> doFetch() throws Exception {
        shopIdMapper = getDependencyResult(ShopIdMapperFetcher.class);
        if (!LionConfigUtils.getOneProductMultiSwitch() || shopIdMapper == null || shopIdMapper.getMtBestShopId() <= 0L) {
            return CompletableFuture.completedFuture(null);
        }
        ProductBaseInfo productBaseInfo = getDependencyResult(ProductBaseInfoFetcher.class);
        if (!MultiTradeUtils.isMultiTradeGoods(Optional.ofNullable(productBaseInfo).map(ProductBaseInfo::getBasic).orElse(null))) {
            return CompletableFuture.completedFuture(null);
        }
        shopBookInfoQueryService.queryShopBookInfo(buildRequest());
        CompletableFuture<QueryShopBookInfoResponse> cf = ThriftAsyncUtils.getThriftFuture();
        return cf.thenApply(v -> {
            if (!v.isSuccess() || v.getData() == null) {
                return null;
            }
            ShopBookingReturnValue bookingTimeReturnValue = new ShopBookingReturnValue();
            bookingTimeReturnValue.setShopBookInfoQueryResultDTO(v.getData());
            return bookingTimeReturnValue;
        }).exceptionally(e -> {
            log.error(XMDLogFormat.build()
                    .putTag("scene", "shopBookInfoQueryService")
                    .putTag("method", "queryShopBookInfo")
                    .message(String.format("queryShopBookInfo error, mtShopId :%d", shopIdMapper.getMtBestShopId())), e);
            return null;
        });
    }

    private QueryShopBookInfoRequest buildRequest() {
        QueryShopBookInfoRequest bookInfoRequest = new QueryShopBookInfoRequest();
        bookInfoRequest.setMtShopIds(Lists.newArrayList(shopIdMapper.getMtBestShopId()));
        return bookInfoRequest;
    }

}
