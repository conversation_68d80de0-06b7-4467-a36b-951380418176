package com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommendgoods.dtos;

import lombok.Data;

import java.util.Map;

/**
 * 评分和星级数据
 */
@Data
public class ReviewM {

    /**
     * 总收藏人数
     */
    private int collectNum;

    /**
     * 总评价人数
     */
    private int reviewNum;

    /**
     * 星级占比，key=10,20,30,40,50,value=百分比
     */
    private Map<String, String> star2Count;

    /**
     * 星级。0~5,美团侧连续，点评侧不连续。
     */
    private String star;

    /**
     * 评分。十分制，保留一位小数
     */
    private String score;

    /**
     * 跳转链接
     */
    private String jumpUrl;
}
