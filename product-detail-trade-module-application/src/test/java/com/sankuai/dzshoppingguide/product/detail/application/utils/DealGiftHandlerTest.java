package com.sankuai.dzshoppingguide.product.detail.application.utils;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.dealgift.DealGiftResult;
import com.sankuai.dzshoppingguide.product.detail.spi.promo.vo.DealGift;
import com.sankuai.mktplay.center.mkt.play.center.client.PlayExecuteResponse;
import java.util.Collections;
import java.util.List;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

@RunWith(PowerMockRunner.class)
@PrepareForTest({ PlayCenterWrapper.class })
public class DealGiftHandlerTest {

    @Mock
    private DealGiftResult dealGiftResult;

    @Mock
    private PlayExecuteResponse playActivityResponse;

    @Mock
    private PlayExecuteResponse playResponse;

    @Before
    public void setUp() {
        PowerMockito.mockStatic(PlayCenterWrapper.class);
        PowerMockito.when(PlayCenterWrapper.buildGiftsForNewCustomActivity(any())).thenReturn(null);
        PowerMockito.when(PlayCenterWrapper.convert2FilterPlayActivityModel(any())).thenReturn(null);
        PowerMockito.when(PlayCenterWrapper.convertPlayActivityModelMap(any())).thenReturn(null);
    }

    /**
     * 测试 buildActivityTimeDesc 方法，当 endTime 为 null 时
     */
    @Test
    public void testBuildActivityTimeDescWhenEndTimeIsNull() throws Throwable {
        // arrange
        Long endTime = null;
        // act
        String result = DealGiftHandler.buildActivityTimeDesc(endTime);
        // assert
        assertEquals("", result);
    }

    /**
     * 测试 buildActivityTimeDesc 方法，当 endTime 不为 null 时
     */
    @Test
    public void testBuildActivityTimeDescWhenEndTimeIsNotNull() throws Throwable {
        // arrange
        Long endTime = System.currentTimeMillis();
        // act
        String result = DealGiftHandler.buildActivityTimeDesc(endTime);
        // assert
        assertNotNull(result);
        assertTrue(result.contains("活动有效期至"));
    }

    /**
     * Test when dealGiftResult is null
     */
    @Test
    public void testBuildDealGiftsFromPlayDealGiftResultIsNull() throws Throwable {
        // act
        List<DealGift> result = DealGiftHandler.buildDealGiftsFromPlay(1L, 1L, null);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test when playActivityResult and playResult contain valid data
     */
    @Test
    public void testBuildDealGiftsFromPlayPlayActivityResultAndPlayResultAreNotEmptyAndNoExceptionOccurs() throws Throwable {
        // arrange
        when(dealGiftResult.getPlayActivityResponse()).thenReturn(playActivityResponse);
        when(dealGiftResult.getPlayResponse()).thenReturn(playResponse);
        String playActivityResult = "{\"activityId\":\"123\",\"hasParticipate\":true,\"taskInfoList\":[{\"status\":1,\"eventType\":17,\"prizeInfoList\":[{\"prizeName\":\"Test Prize\",\"prizeImage\":\"test.jpg\"}],\"prizeCount\":1}],\"endTime\":1640995200000}";
        String playResult = "{\"1\":[{\"playInfo\":{\"activityId\":123,\"title\":\"Test\",\"taskInfoList\":[{\"prizeInfoList\":[{\"prizeName\":\"Test Prize\",\"prizeImage\":\"test.jpg\"}],\"prizeCount\":1}]}}]}";
        when(playActivityResponse.getResult()).thenReturn(playActivityResult);
        when(playResponse.getResult()).thenReturn(playResult);
        // Prepare expected result
        DealGift expectedGift = new DealGift();
        expectedGift.setTitle("Test Prize");
        expectedGift.setThumbnail("test.jpg");
        expectedGift.setCouponNum(1);
        PowerMockito.when(PlayCenterWrapper.buildGiftsForNewCustomActivity(playActivityResult)).thenReturn(Collections.singletonList(expectedGift));
        // act
        List<DealGift> result = DealGiftHandler.buildDealGiftsFromPlay(1L, 1L, dealGiftResult);
        // assert
        assertNotNull(result);
        assertFalse(result.isEmpty());
        assertEquals(1, result.size());
        DealGift dealGift = result.get(0);
        assertEquals("Test Prize", dealGift.getTitle());
        assertEquals("test.jpg", dealGift.getThumbnail());
        assertEquals(1, dealGift.getCouponNum());
    }
}
