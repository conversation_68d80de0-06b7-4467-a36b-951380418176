package com.sankuai.dzshoppingguide.product.detail.spi.dtos;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Author: <EMAIL>
 * @Date: 2025/3/3
 */
@Data
@TypeDoc(description = "加价详情弹框")
@MobileDo(id = 0x35b1)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MarkupDetailPopup implements Serializable {

    @FieldDoc(description = "加价详情弹框文案")
    @MobileDo.MobileField(key = 0x451b)
    private String text;

    @FieldDoc(description = "跳链")
    @MobileDo.MobileField(key = 0x774e)
    private String jumpUrl;
}
