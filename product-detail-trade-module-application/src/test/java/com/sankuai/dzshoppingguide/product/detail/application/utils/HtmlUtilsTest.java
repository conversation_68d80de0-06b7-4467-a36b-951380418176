package com.sankuai.dzshoppingguide.product.detail.application.utils;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.dianping.cat.Cat;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * Test class for HtmlUtils.rmEndsByLabel method
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("HtmlUtils removeLastEnter Tests")
public class HtmlUtilsTest {

    /**
     * Test case for null input string
     */
    @Test
    public void testRmEndsByLabel_NullInput() throws Throwable {
        // arrange
        String rowLine = null;
        String label = "p";
        // act
        String result = HtmlUtils.rmEndsByLabel(rowLine, label);
        // assert
        assertEquals("", result);
    }

    /**
     * Test case for empty input string
     */
    @Test
    public void testRmEndsByLabel_EmptyInput() throws Throwable {
        // arrange
        String rowLine = "";
        String label = "p";
        // act
        String result = HtmlUtils.rmEndsByLabel(rowLine, label);
        // assert
        assertEquals("", result);
    }

    /**
     * Test case for single HTML tag
     */
    @Test
    public void testRmEndsByLabel_SingleTag() throws Throwable {
        // arrange
        String rowLine = "<p>Hello World</p>";
        String label = "p";
        // act
        String result = HtmlUtils.rmEndsByLabel(rowLine, label);
        // assert
        assertEquals("Hello World", result);
    }

    /**
     * Test case for HTML with attributes in tag
     */
    @Test
    public void testRmEndsByLabel_TagWithAttributes() throws Throwable {
        // arrange
        String rowLine = "<p class=\"test\" id=\"1\">Hello World</p>";
        String label = "p";
        // act
        String result = HtmlUtils.rmEndsByLabel(rowLine, label);
        // assert
        assertEquals("Hello World", result);
    }

    /**
     * Test case for multiple tags of same type
     */
    @Test
    public void testRmEndsByLabel_MultipleTags() throws Throwable {
        // arrange
        String rowLine = "<p>Hello</p> <p>World</p>";
        String label = "p";
        // act
        String result = HtmlUtils.rmEndsByLabel(rowLine, label);
        // assert
        assertEquals("Hello World", result);
    }

    /**
     * Test case for nested tags
     */
    @Test
    public void testRmEndsByLabel_NestedTags() throws Throwable {
        // arrange
        String rowLine = "<div><p>Hello World</p></div>";
        String label = "p";
        // act
        String result = HtmlUtils.rmEndsByLabel(rowLine, label);
        // assert
        assertEquals("<div>Hello World</div>", result);
    }

    /**
     * Test case for special characters in content
     */
    @Test
    public void testRmEndsByLabel_SpecialCharacters() throws Throwable {
        // arrange
        String rowLine = "<p>Hello & World < > \" '</p>";
        String label = "p";
        // act
        String result = HtmlUtils.rmEndsByLabel(rowLine, label);
        // assert
        assertEquals("Hello & World < > \" '", result);
    }

    /**
     * Test scenario: Input is null
     * Expected: Should return null
     */
    @Test
    @DisplayName("Should return null when input is null")
    public void testRemoveLastEnter_WhenInputNull() throws Throwable {
        // arrange
        String input = null;
        // act
        String result = HtmlUtils.removeLastEnter(input);
        // assert
        assertNull(result, "Should return null for null input");
    }

    /**
     * Test scenario: Input is empty string
     * Expected: Should return empty string
     */
    @Test
    @DisplayName("Should return empty string when input is empty")
    public void testRemoveLastEnter_WhenInputEmpty() throws Throwable {
        // arrange
        String input = "";
        // act
        String result = HtmlUtils.removeLastEnter(input);
        // assert
        assertEquals("", result, "Should return empty string for empty input");
    }

    /**
     * Test scenario: Input contains only whitespace
     * Expected: Should return the same whitespace string
     */
    @Test
    @DisplayName("Should return same string when input is only whitespace")
    public void testRemoveLastEnter_WhenInputWhitespace() throws Throwable {
        // arrange
        String input = "   ";
        // act
        String result = HtmlUtils.removeLastEnter(input);
        // assert
        assertEquals("   ", result, "Should return same string for whitespace input");
    }

    /**
     * Test scenario: Input string ends with newline
     * Expected: Should remove the last newline character
     */
    @Test
    @DisplayName("Should remove last newline when input ends with newline")
    public void testRemoveLastEnter_WhenEndsWithNewline() throws Throwable {
        // arrange
        String input = "Hello World\n";
        // act
        String result = HtmlUtils.removeLastEnter(input);
        // assert
        assertEquals("Hello World", result, "Should remove the last newline character");
    }

    /**
     * Test scenario: Input string does not end with newline
     * Expected: Should return the original string unchanged
     */
    @Test
    @DisplayName("Should return same string when input doesn't end with newline")
    public void testRemoveLastEnter_WhenNotEndsWithNewline() throws Throwable {
        // arrange
        String input = "Hello World";
        // act
        String result = HtmlUtils.removeLastEnter(input);
        // assert
        assertEquals("Hello World", result, "Should return unchanged string when no trailing newline");
    }

    /**
     * Test scenario: Input string ends with multiple newlines
     * Expected: Should remove only the last newline character
     */
    @Test
    @DisplayName("Should remove only last newline when input ends with multiple newlines")
    public void testRemoveLastEnter_WhenMultipleNewlines() throws Throwable {
        // arrange
        String input = "Hello World\n\n";
        // act
        String result = HtmlUtils.removeLastEnter(input);
        // assert
        assertEquals("Hello World\n", result, "Should remove only the last newline character");
    }

    /**
     * Test scenario: Input string with newline in middle
     * Expected: Should not remove newline in middle of string
     */
    @Test
    @DisplayName("Should not remove newline when it's in middle of string")
    public void testRemoveLastEnter_WhenNewlineInMiddle() throws Throwable {
        // arrange
        String input = "Hello\nWorld";
        // act
        String result = HtmlUtils.removeLastEnter(input);
        // assert
        assertEquals("Hello\nWorld", result, "Should not remove newline from middle of string");
    }

    /**
     * Test removeHtml with null input
     */
    @Test
    @DisplayName("Should return null when input is null")
    public void testRemoveHtmlWithNull() {
        // arrange
        String input = null;
        // act
        String result = HtmlUtils.removeHtml(input);
        // assert
        assertNull(result, "Result should be null for null input");
    }

    /**
     * Test removeHtml with empty string
     */
    @Test
    @DisplayName("Should return empty string when input is empty")
    public void testRemoveHtmlWithEmptyString() {
        // arrange
        String input = "";
        // act
        String result = HtmlUtils.removeHtml(input);
        // assert
        assertEquals("", result, "Result should be empty string for empty input");
    }

    /**
     * Test removeHtml with blank string
     */
    @Test
    @DisplayName("Should return same blank string when input contains only whitespace")
    public void testRemoveHtmlWithBlankString() {
        // arrange
        String input = "   ";
        // act
        String result = HtmlUtils.removeHtml(input);
        // assert
        assertEquals("   ", result, "Result should be unchanged for blank input");
    }

    /**
     * Test removeHtml with string containing no HTML tags
     */
    @Test
    @DisplayName("Should return unchanged text when no HTML tags present")
    public void testRemoveHtmlWithNoTags() {
        // arrange
        String input = "Plain text without any HTML tags";
        // act
        String result = HtmlUtils.removeHtml(input);
        // assert
        assertEquals("Plain text without any HTML tags", result, "Result should be unchanged when no HTML tags are present");
    }

    /**
     * Test removeHtml with string containing single HTML tag
     */
    @Test
    @DisplayName("Should remove single HTML tag")
    public void testRemoveHtmlWithSingleTag() {
        // arrange
        String input = "<p>Single paragraph</p>";
        // act
        String result = HtmlUtils.removeHtml(input);
        // assert
        assertEquals("Single paragraph", result, "Result should contain only text content without HTML tags");
    }

    /**
     * Test removeHtml with string containing multiple HTML tags
     */
    @Test
    @DisplayName("Should remove multiple HTML tags")
    public void testRemoveHtmlWithMultipleTags() {
        // arrange
        String input = "<div><p>First paragraph</p><p>Second paragraph</p></div>";
        // act
        String result = HtmlUtils.removeHtml(input);
        // assert
        assertEquals("First paragraphSecond paragraph", result, "Result should contain concatenated text content without HTML tags");
    }

    /**
     * Test removeHtml with string containing HTML tags with attributes
     */
    @Test
    @DisplayName("Should remove HTML tags with attributes")
    public void testRemoveHtmlWithTagsAndAttributes() {
        // arrange
        String input = "<div class=\"container\"><p style=\"color: red;\">Styled text</p></div>";
        // act
        String result = HtmlUtils.removeHtml(input);
        // assert
        assertEquals("Styled text", result, "Result should remove HTML tags including their attributes");
    }

    /**
     * Test removeHtml with string containing self-closing HTML tags
     */
    @Test
    @DisplayName("Should remove self-closing HTML tags")
    public void testRemoveHtmlWithSelfClosingTags() {
        // arrange
        String input = "Text with <br/> line break and <img src=\"image.jpg\"/> image";
        // act
        String result = HtmlUtils.removeHtml(input);
        // assert
        assertEquals("Text with  line break and  image", result, "Result should remove self-closing HTML tags");
    }

    /**
     * Test removeHtml with string containing malformed HTML tags
     */
    @Test
    @DisplayName("Should handle malformed HTML tags")
    public void testRemoveHtmlWithMalformedTags() {
        // arrange
        String input = "<div>Unclosed div <p>Nested content</div>";
        // act
        String result = HtmlUtils.removeHtml(input);
        // assert
        assertEquals("Unclosed div Nested content", result, "Result should handle and remove malformed HTML tags");
    }

    /**
     * Test removeHtml with string containing special characters in HTML tags
     */
    @Test
    @DisplayName("Should handle HTML tags with special characters")
    public void testRemoveHtmlWithSpecialCharacters() {
        // arrange
        String input = "<div data-test=\"test&quot;\">Special &amp; characters</div>";
        // act
        String result = HtmlUtils.removeHtml(input);
        // assert
        assertEquals("Special &amp; characters", result, "Result should preserve HTML entities while removing tags");
    }

    /**
     * Test case for format method with null input
     */
    @Test
    public void testFormatWithNullInput() throws Throwable {
        // arrange
        String input = null;
        // act
        String result = HtmlUtils.format(input);
        // assert
        assertEquals("", result);
    }

    /**
     * Test case for format method with blank input
     */
    @Test
    public void testFormatWithBlankInput() throws Throwable {
        // arrange
        String input = "   ";
        // act
        String result = HtmlUtils.format(input);
        // assert
        assertEquals("", result);
    }

    /**
     * Test case for format method with input containing no HTML tags
     */
    @Test
    public void testFormatWithNoHtmlTags() throws Throwable {
        // arrange
        String input = "This is a plain text without any HTML tags.";
        // act
        String result = HtmlUtils.format(input);
        // assert
        assertEquals(input, result);
    }

    /**
     * Test case for format method with input containing p and i tags
     */
    @Test
    public void testFormatWithPreservedTags() throws Throwable {
        // arrange
        String input = "<p>Text with <i>italic</i> formatting</p>";
        // act
        String result = HtmlUtils.format(input);
        // assert
        assertEquals("<p>Text with <i>italic</i> formatting</p>", result);
    }

    /**
     * Test case for format method with input containing tags that should be removed
     */
    @Test
    public void testFormatWithRemovedTags() throws Throwable {
        // arrange
        String input = "<b>Bold text</b> and <strong>strong text</strong>";
        // act
        String result = HtmlUtils.format(input);
        // assert
        assertEquals("Bold text and strong text", result);
    }

    /**
     * Test case for format method with mixed preserved and removed tags
     */
    @Test
    public void testFormatWithMixedTags() throws Throwable {
        // arrange
        String input = "<p>Text with <i>italic</i> and <b>bold</b> formatting</p>";
        // act
        String result = HtmlUtils.format(input);
        // assert
        assertEquals("<p>Text with <i>italic</i> and bold formatting</p>", result);
    }
}
