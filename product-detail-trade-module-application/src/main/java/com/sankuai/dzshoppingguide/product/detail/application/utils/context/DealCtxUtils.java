package com.sankuai.dzshoppingguide.product.detail.application.utils.context;

import com.dianping.product.shelf.common.dto.activity.ActivityDetailDTO;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.LeadsInfoFetcher.LeadsInfoResult;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.dto.SkuBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.dto.SkuBaseInfoMap;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.defaultselect.SkuDefaultSelect;
import com.sankuai.dzshoppingguide.product.detail.application.utils.FreeDealUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.promo.FreeDealConfig;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.FreeDealEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.RequestSourceEnum;
import com.sankuai.general.product.query.center.client.enums.TradeTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Objects;

import static com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum.*;
import static com.sankuai.dzshoppingguide.product.detail.application.utils.ClientTypeUtils.isPureHarmony;

/**
 * <AUTHOR>
 * @create 2025/3/5 17:35
 */
@Slf4j
public class DealCtxUtils {


    public static FreeDealConfig getFreeDealConfig(Long categoryId){
        return FreeDealUtils.getFreeDealConfig(getFreeDealType(categoryId));
    }

    public static FreeDealEnum getFreeDealType(Long categoryId){
        return FreeDealEnum.fromDealCategory(String.valueOf(categoryId));
    }

    public static boolean isFreeDeal(ProductBaseInfo dealGroupBase){
        if (Objects.isNull(dealGroupBase) || Objects.isNull(dealGroupBase.getCategory())) {
            return false;
        }
        if (!FreeDealUtils.inCategoryList(dealGroupBase.getCategory().getCategoryId())) {
            return false;
        }
        if (Objects.isNull(dealGroupBase.getBasic())) {
            return false;
        }
        if (Objects.isNull(dealGroupBase.getBasic().getTradeType()) || dealGroupBase.getBasic().getTradeType() != TradeTypeEnum.RESERVATION.getCode()) {
            return false;
        }
        return true;
    }


    public static boolean isHitSpecialValueDeal(LeadsInfoResult leadsInfoResult){
        return hasSpecialValueDeal(leadsInfoResult.getActivityDetailDTO());
    }

    public static boolean hasSpecialValueDeal(ActivityDetailDTO activityDetailDTO) {
        if (activityDetailDTO == null || activityDetailDTO.getActivityId() <= 0) {
            return false;
        }
        return true;
    }

    public static String getDefaultSkuId(final SkuDefaultSelect skuDefaultSelect,
                                         final ProductBaseInfo productBaseInfo) {
        if (Objects.nonNull(skuDefaultSelect)) {
            return String.valueOf(skuDefaultSelect.getSelectedSkuId());
        }
        return String.valueOf(getDealId(productBaseInfo));
    }

    public static Integer getDealId(final ProductBaseInfo productBaseInfo) {
        if (productBaseInfo == null || productBaseInfo.getSkuMap() == null) {
            return 0;
        }
        SkuBaseInfoMap skuBaseInfoMap = productBaseInfo.getSkuMap();
        if (CollectionUtils.isNotEmpty(skuBaseInfoMap.getAllSkuBaseInfo())) {
            for (SkuBaseInfo skuBaseInfo : skuBaseInfoMap.getAllSkuBaseInfo()) {
                if (skuBaseInfo.getBasic().getStatus() == 1) {
                    return Math.toIntExact(skuBaseInfo.getSkuId());
                }
            }
        }
        return 0;
    }

    public static boolean isHarmony(String userAgent){
        return isPureHarmony(userAgent);
    }

    public static boolean isThirdPArt(ClientTypeEnum clientTypeEnum){
        return KAI_DIAN_BAO.equals(clientTypeEnum) || APOLLO.equals(clientTypeEnum);
    }

    public static boolean notMiniProgramCostEffective(ClientTypeEnum clientTypeEnum, String requestSource) {
        return MT_XCX.equals(clientTypeEnum)
                && !(RequestSourceEnum.COST_EFFECTIVE.getSource().equals(requestSource));
    }

    /**
     * 如果是美团app、点评app以外的请求，则为external
     */
    public static boolean isExternal(ClientTypeEnum clientTypeEnum) {
        if(clientTypeEnum == null) {
            return true;
        }
        if(ClientTypeEnum.MT_APP == clientTypeEnum) {
            return false;
        }
        if(ClientTypeEnum.DP_APP == clientTypeEnum) {
            return false;
        }
        if(ClientTypeEnum.DP_M == clientTypeEnum) {
            return false;
        }
        if(ClientTypeEnum.MT_I == clientTypeEnum) {
            return false;
        }
        return true;
    }

    //美团、点评APP客户端
    public static boolean judgeMainApp(ClientTypeEnum clientTypeEnum) {
        return ClientTypeEnum.MT_APP == clientTypeEnum || ClientTypeEnum.DP_APP == clientTypeEnum;
    }
}
