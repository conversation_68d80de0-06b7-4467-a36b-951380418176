package com.sankuai.dzshoppingguide.product.detail.application.service.impl;

import com.alibaba.fastjson.JSON;
import com.dianping.csc.center.engine.access.dto.AccessRequestDTO;
import com.dianping.csc.center.engine.access.dto.AccessResponseDTO;
import com.dianping.csc.center.engine.access.service.AccessInService;
import com.dianping.deal.idmapper.api.DealIdMapperService;
import com.dianping.deal.sales.common.datatype.*;
import com.dianping.deal.sales.display.api.service.ProductSceneSalesDisplayService;
import com.dianping.deal.sales.display.api.service.SalesDisplayQueryService;
import com.dianping.deal.shop.DealShopQueryService;
import com.dianping.gm.marketing.times.card.api.dto.CardResponse;
import com.dianping.gmkt.scene.api.delivery.dto.req.QueryExposureResourcesReqDTO;
import com.dianping.gmkt.scene.api.delivery.dto.res.DeliveryCommonResponse;
import com.dianping.gmkt.scene.api.delivery.dto.res.ResourceExposureResponseDTO;
import com.dianping.gmkt.scene.api.delivery.enums.DeliveryErrorCode;
import com.dianping.gmkt.scene.api.delivery.service.ResourcesExposureService;
import com.dianping.martgeneral.recommend.api.dto.RecommendDTO;
import com.dianping.martgeneral.recommend.api.entity.RecommendParameters;
import com.dianping.martgeneral.recommend.api.entity.RecommendResult;
import com.dianping.martgeneral.recommend.api.service.RecommendService;
import com.dianping.pay.promo.display.api.PromoDisplayService;
import com.dianping.pay.promo.display.api.dto.PromoDisplayDTO;
import com.dianping.pay.promo.display.api.dto.QueryPromoDisplayRequest;
import com.dianping.pigeon.remoting.common.domain.CallMethod;
import com.dianping.pigeon.remoting.common.exception.RpcException;
import com.dianping.poi.relation.service.api.PoiRelationService;
import com.dianping.product.shelf.common.dto.activity.ActivityDetailDTO;
import com.dianping.product.shelf.common.request.activities.ActivityProductQueryRequest;
import com.dianping.product.shelf.query.api.ActivityShelfQueryService;
import com.dianping.spc.common.Response;
import com.dianping.tgc.open.entity.BatchExProxyCouponRequest;
import com.dianping.tgc.open.entity.BatchExProxyCouponResponseDTO;
import com.dianping.tgc.open.v2.TGCGetCouponComponentQueryService;
import com.dianping.tpfun.product.api.govsubsidy.model.GovSubsidyInfo;
import com.dianping.tpfun.product.api.govsubsidy.request.QueryGovSubsidyInfoRequest;
import com.dianping.tpfun.product.api.govsubsidy.service.GovSubsidyInfoQueryService;
import com.dianping.tpfun.product.api.sku.pintuan.PinFacadeService;
import com.dianping.tpfun.product.api.sku.pintuan.dto.PinProductBrief;
import com.dianping.tpfun.product.api.sku.pintuan.request.GetPinProductBriefReq;
import com.dianping.tpfun.product.api.sku.pintuan.request.GetPinProductIdOptional;
import com.dianping.tuangu.dztg.usercenter.api.CreateOrderPageUrlService;
import com.dianping.tuangu.dztg.usercenter.api.request.BatchGetCreateOrderPageUrlReq;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.meituan.inf.xmdlog.XMDLogFormat;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonClient;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftClient;
import com.meituan.service.mobile.mtthrift.callback.OctoThriftCallback;
import com.sankuai.athena.inf.AthenaInf;
import com.sankuai.clr.content.process.gateway.thrift.api.LeadsQueryGatewayService;
import com.sankuai.clr.content.process.gateway.thrift.dto.leads.LoadLeadsInfoReqDTO;
import com.sankuai.clr.content.process.gateway.thrift.dto.leads.LoadLeadsInfoRespDTO;
import com.sankuai.dealuser.price.display.api.PriceDisplayService;
import com.sankuai.dealuser.price.display.api.model.BatchPriceRequest;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PriceResponse;
import com.sankuai.dzcard.navigation.api.DzCardExposureService;
import com.sankuai.dzcard.navigation.api.DzCardQueryService;
import com.sankuai.dzcard.navigation.api.dto.CardHoldStatusDTO;
import com.sankuai.dzcard.navigation.api.dto.DzCardQueryRequest;
import com.sankuai.dzcard.navigation.api.dto.FindDCCardHoldStatusLiteReqDTO;
import com.sankuai.dzcard.navigation.api.dto.QueryCardInfoDTO;
import com.sankuai.dzcard.navigation.api.dto.QueryDiscountCardReq;
import com.sankuai.dzim.cliententry.ClientEntryService;
import com.sankuai.dzim.cliententry.dto.ClientEntryDTO;
import com.sankuai.dzim.cliententry.dto.ClientEntryReqDTO;
import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
import com.sankuai.dzshoppingguide.product.detail.application.service.dto.ShopMemberDetailV;
import com.sankuai.dzshoppingguide.product.detail.application.service.dto.ShopMemberQueryResponse;
import com.sankuai.dzshoppingguide.product.detail.domain.utils.PigeonCallbackUtils;
import com.sankuai.dzshoppingguide.product.detail.domain.utils.ThriftAsyncUtils;
import com.sankuai.dztheme.deal.DealProductService;
import com.sankuai.dztheme.deal.DealSkuService;
import com.sankuai.dztheme.deal.req.DealProductRequest;
import com.sankuai.dztheme.deal.operatorpage.DealProductBizQueryService;
import com.sankuai.dztheme.deal.req.SkuOptionBatchRequest;
import com.sankuai.dztheme.deal.res.DealProductResult;
import com.sankuai.dztheme.deal.res.DealSkuSummaryDTO;
import com.sankuai.dztheme.massagebook.theme.ReserveThemeQueryService;
import com.sankuai.dztheme.massagebook.theme.req.ReserveQueryRequest;
import com.sankuai.dztheme.massagebook.theme.res.ReserveQueryResponse;
import com.sankuai.general.product.query.center.client.builder.model.*;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResponse;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResult;
import com.sankuai.general.product.query.center.client.service.DealGroupQueryService;
import com.sankuai.meituan.charity.merchant.main.sdk.WelfareDocFacade;
import com.sankuai.meituan.charity.merchant.main.sdk.dto.request.DzVpoiReq;
import com.sankuai.meituan.charity.merchant.main.sdk.dto.response.DzProductDocResp;
import com.sankuai.merchantcard.timescard.exposure.TimesCardNavigationService;
import com.sankuai.merchantcard.timescard.exposure.dto.CardSummaryBarDTO;
import com.sankuai.merchantcard.timescard.exposure.res.QueryDealGroupCardBarSummaryRequest;
import com.sankuai.mktplay.center.mkt.play.center.client.PlayCenterService;
import com.sankuai.mktplay.center.mkt.play.center.client.PlayExecuteRequest;
import com.sankuai.mktplay.center.mkt.play.center.client.PlayExecuteResponse;
import com.sankuai.mktplay.center.mkt.play.center.client.ScenePlayExecuteRequest;
import com.sankuai.mpmctmember.query.common.enums.MemberInterestCodeEnum;
import com.sankuai.mpmctmember.query.common.enums.MemberInterestSubjectTypeEnum;
import com.sankuai.mpmctmember.query.common.enums.MemberQueryFieldEnum;
import com.sankuai.mpmctmember.query.common.enums.UserMemberTypeEnum;
import com.sankuai.mpmctmember.query.thrift.api.MemberInterestQueryService;
import com.sankuai.mpmctmember.query.thrift.api.MemberPlanQueryService;
import com.sankuai.mpmctmember.query.thrift.dto.*;
import com.sankuai.mppack.product.client.query.request.TyingBlackListQueryRequest;
import com.sankuai.mppack.product.client.query.response.TyingBlackListResponse;
import com.sankuai.mppack.product.client.supply.service.PackBlackListService;
import com.sankuai.mpproduct.idservice.api.request.BizProductIdConvertRequest;
import com.sankuai.mpproduct.idservice.api.request.DpGroupIdConvertRequest;
import com.sankuai.mpproduct.idservice.api.request.MtGroupIdConvertRequest;
import com.sankuai.mpproduct.idservice.api.response.BizProductIdConvertResponse;
import com.sankuai.mpproduct.idservice.api.response.GroupIdConvertResponse;
import com.sankuai.mpproduct.idservice.api.service.IdService;
import com.sankuai.newdzcard.supply.dto.DzCardDTO;

import com.sankuai.nib.price.operation.api.common.dto.SessionContextDTO;
import com.sankuai.nib.price.operation.api.guarantee.guide.standard.dto.ObjectGuaranteeTagDTO;
import com.sankuai.nib.price.operation.api.guarantee.guide.standard.request.BatchQueryGuaranteeTagRequest;
import com.sankuai.nib.price.operation.api.guarantee.guide.standard.service.GuaranteeQueryService;
import com.sankuai.statesubsidies.c.thrift.request.GetUserQualificationOpenRequest;
import com.sankuai.statesubsidies.c.thrift.response.GetUserQualificationOpenResponse;
import com.sankuai.statesubsidies.c.thrift.service.StateSubsidiesOpenService;
import com.sankuai.tpfun.skuoperationapi.price.dto.request.BatchPriceRangeInfoRequest;
import com.sankuai.tpfun.skuoperationapi.price.dto.response.BatchPriceRangeInfoResponse;
import com.sankuai.tpfun.skuoperationapi.price.service.PriceRangeQueryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.thrift.TException;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-02-13
 * @desc
 */
@Slf4j
@Component
public class CompositeAtomServiceImpl implements CompositeAtomService {

    @MdpPigeonClient(url = "com.sankuai.dzcard.navigation.api.DzCardExposureService"
            , callType = CallMethod.CALLBACK, timeout = 1000, testTimeout = 50000
    )
    private DzCardExposureService dzCardExposureService;

    @MdpThriftClient(timeout = 1000, testTimeout = 5000, remoteAppKey = "com.sankuai.mpmctmember.query",async = true)
    public MemberInterestQueryService newMemberInterestQueryService;

    @MdpPigeonClient(url = "com.sankuai.dealuser.price.display.api.PriceDisplayService"
            , callType = CallMethod.CALLBACK, timeout = 1800, testTimeout = 50000
    )
    private PriceDisplayService priceDisplayService;

    @MdpPigeonClient(url = "com.sankuai.dztheme.massagebook.ReserveThemeQueryService"
            , callType = CallMethod.CALLBACK, timeout = 10000, testTimeout = 50000
    )
    private ReserveThemeQueryService reserveThemeQueryService;

    @MdpThriftClient(
            timeout = 1500, testTimeout = 5000,
            remoteAppKey = "com.sankuai.productuser.query.center"
    )
    public DealGroupQueryService dealGroupQueryServiceAtom;


    // 抵用券
    @MdpPigeonClient(url = "http://service.dianping.com/tgcOpenService/v2/tgcGetCouponComponentQueryService_1.0.0",callType = CallMethod.CALLBACK, timeout = 500,testTimeout = 50000)
    private TGCGetCouponComponentQueryService couponComponentQueryServiceFuture;

    // 查询团购/泛商品关联的有效活动
    @MdpPigeonClient(url = "com.dianping.product.shelf.query.api.ActivityShelfQueryService",callType = CallMethod.CALLBACK, timeout = 1000,testTimeout = 50000)
    private ActivityShelfQueryService activityShelfQueryServiceFuture;

    // 查询单个留资信息
    @MdpThriftClient(
            timeout = 1500, testTimeout = 5000,
            remoteAppKey = "com.sankuai.leads.content.process",
            async = true
    )
    private LeadsQueryGatewayService leadsQueryGatewayServiceFuture;

    // 查询单个留资信息
    @MdpThriftClient(
            timeout = 1000, testTimeout = 5000,
            remoteAppKey = "com.sankuai.mktplay.center", remoteUniProto = true, filterByServiceName = true, async = true
    )
    private PlayCenterService.AsyncIface playCenterServiceFuture;

    @MdpThriftClient(
            timeout = 500, testTimeout = 5000,
            remoteAppKey = "com.sankuai.statesubsidies.c"
    )
    private StateSubsidiesOpenService.Iface stateSubsidiesOpenServiceFuture;

    // 查询团单id和拼团id的映射
    @MdpPigeonClient(url = "http://service.dianping.com/tpfunService/pingfacadeservice_1.0.0", callType = CallMethod.CALLBACK, timeout = 500,testTimeout = 50000)
    private PinFacadeService pinFacadeServiceFuture;

    @MdpPigeonClient(url = "http://service.dianping.com/payPromoDisplayService/PromoDisplayService_1.0.0", callType = CallMethod.CALLBACK, timeout = 500,testTimeout = 50000)
    private PromoDisplayService promoDisplayServiceFuture;

    @MdpThriftClient(timeout = 1000, testTimeout = 5000, remoteAppKey = "com.sankuai.mpmctmember.query",async = true)
    private MemberPlanQueryService memberPlanQueryService;

    @MdpPigeonClient(timeout = 1000, testTimeout = 5000, callType = CallMethod.CALLBACK, url = "com.dianping.deal.sales.display.api.service.SalesDisplayQueryService")
    private SalesDisplayQueryService salesDisplayQueryService;

    @MdpThriftClient(
            timeout = 1000, testTimeout = 5000,
            remoteAppKey = "com.sankuai.fsp.charity.merchantmain",
            async = true,
            remoteServerPort = 9005
    )
    public WelfareDocFacade welfareDocFacade;

    @MdpPigeonClient(url = "com.dianping.gmkt.scene.api.delivery.service.ResourcesExposureService", callType = CallMethod.CALLBACK, timeout = 1500, testTimeout = 5000)
    private ResourcesExposureService resourcesExposureService;

    @MdpPigeonClient(url = "com.sankuai.dztheme.deal.DealSkuService", callType = CallMethod.CALLBACK, timeout = 1000, testTimeout = 5000)
    private DealSkuService dealSkuServiceFuture;

    @MdpPigeonClient(url = "com.sankuai.dzim.cliententry.ClientEntryService", callType = CallMethod.CALLBACK, timeout = 500, testTimeout = 5000)
    private ClientEntryService clientEntryService;

    @MdpPigeonClient(url = "com.dianping.csc.center.engine.access.service.AccessInService", callType = CallMethod.CALLBACK, timeout = 1000, testTimeout = 5000)
    private AccessInService cscAccessInServiceFuture;

    @MdpPigeonClient(url = "http://service.dianping.com/TimesCardNavigationService/TimesCardNavigationService_1.0.0", callType = CallMethod.CALLBACK, timeout = 1000, testTimeout = 5000)
    private TimesCardNavigationService timesCardNavigation;

    @MdpPigeonClient(url = "com.sankuai.dzcard.navigation.api.DzCardQueryService", callType = CallMethod.CALLBACK, timeout = 1000, testTimeout = 5000)
    private DzCardQueryService dzCardQueryService;

    @MdpThriftClient(remoteAppKey = "com.sankuai.priceoperation.service", timeout = 1000, testTimeout = 5000,async = true)
    private GuaranteeQueryService guaranteeQueryService;

    @MdpThriftClient(remoteAppKey = "com.sankuai.mpproduct.idservice", timeout = 500, testTimeout = 5000,async = true)
    private IdService idService;

    @MdpPigeonClient(url = "com.dianping.tpfun.product.api.govsubsidy.service.GovSubsidyInfoQueryService",
            remoteAppKey = "sku-underlayer-service", callType = CallMethod.CALLBACK, timeout = 500 ,testTimeout = 5000)
    private GovSubsidyInfoQueryService govSubsidyInfoQueryService;

    @MdpPigeonClient(url = "com.sankuai.dztheme.dealproduct.DealProductService", callType = CallMethod.CALLBACK, timeout = 1000, testTimeout = 500000)
    private DealProductService dealProductService;

    @MdpPigeonClient(url = "com.sankuai.dztheme.dealproduct.new.DealProductService", callType = CallMethod.CALLBACK, timeout = 1000, testTimeout = 5000)
    private DealProductService dealProductNewService;

    @MdpPigeonClient(url = "http://service.dianping.com/martgeneral/recommend_1.0.0", callType = CallMethod.CALLBACK, timeout = 1000, testTimeout = 5000)
    private RecommendService recommendService;

    @MdpThriftClient(remoteAppKey = "com.sankuai.pack.product", timeout = 1500, testTimeout = 5000,async = true)
    private PackBlackListService packBlackListService;

    // 销量
    @MdpPigeonClient(url = "com.dianping.deal.sales.display.api.service.ProductSceneSalesDisplayService", callType = CallMethod.CALLBACK, timeout = 500,testTimeout = 50000)
    private ProductSceneSalesDisplayService productSceneSalesDisplayService;

    // 提单跳链
    @MdpPigeonClient(url = "http://service.dianping.com/tuangou/dztgUsercenterService/createOrderPageUrlService_1.0.0", callType = CallMethod.CALLBACK, timeout = 500,testTimeout = 50000)
    private CreateOrderPageUrlService createOrderPageUrlService;

    // 副标题
    @MdpPigeonClient(url = "com.sankuai.dztheme.deal.dealProductBizQueryService", callType = CallMethod.CALLBACK, timeout = 500,testTimeout = 50000)
    private DealProductBizQueryService dealProductBizQueryService;

    // 查询中心
    @MdpThriftClient(remoteAppKey = "com.sankuai.productuser.query.center", timeout = 1000, testTimeout = 5000,async = true)
    public DealGroupQueryService dealGroupQueryServiceAsync;

    @MdpThriftClient(remoteAppKey = "com.sankuai.productuser.operation.systemservice",
            timeout = 1000,
            testTimeout = 5000,
            async = true,
            remoteServerPort = 8410,nettyIO = true)
    private PriceRangeQueryService priceRangeQueryService;

    // 同店推荐-团单id映射
    @MdpPigeonClient(url = "http://service.dianping.com/dealIdMapperService/dealIdMapperService_1.0.0",
            callType = CallMethod.CALLBACK, timeout = 1000, testTimeout = 50000)
    private DealIdMapperService dealIdMapperService;

    // 门店id映射
    @MdpPigeonClient(url = "com.dianping.poi.relation.service.api.PoiRelationService", callType = CallMethod.CALLBACK,
            timeout = 1500, testTimeout = 50000)
    PoiRelationService poiRelationService;

    @MdpPigeonClient(
            url = "http://service.dianping.com/tuangou/dealShopService/dealShopQueryService_1.0.0",
            callType = CallMethod.CALLBACK, timeout = 1000, testTimeout = 50000
    )
    private DealShopQueryService dealShopQueryService;

    @Override
    public CompletableFuture<GetShopMemberEntranceRespDTO> getShopMemberEntrance(GetShopMemberEntranceReqDTO request) {
        memberPlanQueryService.getShopMemberEntrance(request);
        return ThriftAsyncUtils.getThriftFuture();
    }

    @Override
    public CompletableFuture<QueryPlanAndUserIdentityDetailResp> queryFreeMemberCardDetail(QueryPlanAndUserIdentityDetailBySubjectIdReq request) {
        memberPlanQueryService.queryPlanAndUserIdentityDetailBySubjectId(request);
        return ThriftAsyncUtils.getThriftFuture();
    }

    @Override
    public CompletableFuture<CardHoldStatusDTO> queryShopAndUserCardHoldStatus(FindDCCardHoldStatusLiteReqDTO request) {
        CompletableFuture<CardHoldStatusDTO> future = PigeonCallbackUtils
                .setPigeonCallback();
        dzCardExposureService.findShopAndUserCardHoldStatusWithLongShopIdLite(request);
        return future;
    }

    @Override
    public CompletableFuture<QueryCardInfoDTO> queryDiscountCardInfo(QueryDiscountCardReq request) {
        CompletableFuture<QueryCardInfoDTO> future = PigeonCallbackUtils.setPigeonCallback();
        dzCardExposureService.queryCardInfo(request);
        return future;
    }

    @Override
    public CompletableFuture<ReserveQueryResponse> queryReserveProduct(ReserveQueryRequest request) {
        CompletableFuture<ReserveQueryResponse> future = PigeonCallbackUtils.setPigeonCallback();
        reserveThemeQueryService.query(request);
        future.exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "queryReserveProduct")
                            .message(String.format("queryReserveProduct error, request : %s", JsonCodec.encodeWithUTF8(request))));
                    return null;
                });
        return future;
    }

    @Override
    public List<DealGroupDTO> getProductBaseInfo(List<Long> dealGroupIds, boolean isMt) {
        final QueryByDealGroupIdRequest queryCenterRequest = QueryByDealGroupIdRequestBuilder
                .builder()
                .dealGroupIds(new HashSet<>(dealGroupIds), IdTypeEnum.BIZ_PRODUCT)
                .displayShop(DealGroupDisplayShopBuilder.builder().all())
                .dealGroupId(DealGroupIdBuilder.builder().bizProductId())
                .build();
        try {
            QueryDealGroupListResponse queryDealGroupListResponse = dealGroupQueryServiceAtom.queryByDealGroupIds(queryCenterRequest);
            return new ArrayList<>(Optional.of(queryDealGroupListResponse)
                    .map(QueryDealGroupListResponse::getData)
                    .map(QueryDealGroupListResult::getList)
                    .orElse(new ArrayList<>()));
        } catch (TException e) {
            log.error("查询中心查询失败,request:{}", dealGroupIds,e);
        }
        return null;
    }

    @Override
    public List<DealGroupDTO> getProductBaseInfoByUnifiedId(List<Long> unifiedProductId) {
        final QueryByDealGroupIdRequest queryCenterRequest = QueryByDealGroupIdRequestBuilder
                .builder()
                .dealGroupIds(new HashSet<>(unifiedProductId), IdTypeEnum.MT)
                .dealGroupId(DealGroupIdBuilder.builder().all())
                .build();
        try {
            QueryDealGroupListResponse queryDealGroupListResponse = dealGroupQueryServiceAtom.queryByDealGroupIds(queryCenterRequest);
            return new ArrayList<>(Optional.of(queryDealGroupListResponse)
                    .map(QueryDealGroupListResponse::getData)
                    .map(QueryDealGroupListResult::getList)
                    .orElse(new ArrayList<>()));
        } catch (TException e) {
            log.error("统一ID查询中心查询失败,request:{}", unifiedProductId, e);
        }
        return null;
    }

    /**
     *
     * @param userId 美团侧传美团userId,点评侧传美团虚userId
     * @param platform com.sankuai.mpmctmember.query.common.enums.PlatformEnum
     * @param dpDealGroupIds 点评侧团单id
     * @param needPageUrl 是否需要会员落地页链接
     * @return
     */
    @Override
    public CompletableFuture<ShopMemberQueryResponse> getMemberDiscountInfoList(final Long userId,
                                                                                final com.sankuai.mpmctmember.query.common.enums.PlatformEnum platform,
                                                                                final List<Long> dpDealGroupIds,
                                                                                final boolean needPageUrl) {
        if (userId == null || userId == 0
                || CollectionUtils.isEmpty(dpDealGroupIds)) {
            return CompletableFuture.completedFuture(new ShopMemberQueryResponse(true));
        }
        final String paramString = String.format("userId:%s,platform:%s,dpDealGroupIds:%s",
                userId, platform, JSON.toJSONString(dpDealGroupIds));
        List<CompletableFuture<BatchQueryMemberInterestResp>> memberInfoCfs = Lists.partition(dpDealGroupIds, 20).stream()
                .map(partitionDpDealGroupIds -> {
                    BatchQueryMemberInterestReq req = new BatchQueryMemberInterestReq();
                    req.setSubjectIds(partitionDpDealGroupIds);
                    req.setSubjectType(MemberInterestSubjectTypeEnum.DP_DEAL_GROUP.getType());
                    req.setMtUserId(userId);
                    req.setPlatform(platform.getPlatform());
                    UserMemberInterestReqContext reqContext = new UserMemberInterestReqContext();
                    reqContext.setQueryFields(Lists.newArrayList(
                            MemberQueryFieldEnum.USER_MEMBER_IDENTITY.getCode(), MemberQueryFieldEnum.USER_MEMBER_INTEREST.getCode()
                    ));
                    reqContext.setInterestCodes(Lists.newArrayList(
                            MemberInterestCodeEnum.PLATFORM_PRODUCT_EXCLUSIVE.getCode(), MemberInterestCodeEnum.PLATFORM_PRODUCT_PROMO.getCode()
                    ));
                    reqContext.setNeedMemberPageUrl(needPageUrl);
                    req.setReqContext(reqContext);
                    req.setQueryChannel("unknown");
                    newMemberInterestQueryService.batchQueryMemberInterest(req);
                    return ThriftAsyncUtils.<BatchQueryMemberInterestResp>getThriftFuture();
                }).collect(Collectors.toList());
        return CompletableFuture.allOf(memberInfoCfs.toArray(new CompletableFuture[0]))
                .thenApply(aVoid -> memberInfoCfs.stream().map(CompletableFuture::join).collect(Collectors.toList()))
                .exceptionally(throwable -> {
                    log.error("查询会员权益失败,param:{}", paramString, throwable);
                    return null;
                })
                .thenApply(res -> buildMemberDpDealGroupInterestDTOResp(res, paramString));
    }

    private ShopMemberQueryResponse buildMemberDpDealGroupInterestDTOResp(final List<BatchQueryMemberInterestResp> memberDiscountInfoList,
                                                                          final String paramString) {
        if (CollectionUtils.isEmpty(memberDiscountInfoList)) {
            return new ShopMemberQueryResponse(true);
        }
        Map<Long, ShopMemberDetailV> result = memberDiscountInfoList.stream()
                .filter(Objects::nonNull).peek(resp -> {
                    String errorMsg = null;
                    if (resp.getCommonResp() == null) {
                        errorMsg = String.format("查询会员权益失败，response为空,param:%s", paramString);
                    } else {
                        if (resp.getCommonResp().getCode() == 202) {
                            errorMsg = String.format("查询会员权益失败，非法参数,msg:%s,param:%s", resp.getCommonResp().getMsg(), paramString);
                        } else if (resp.getCommonResp().getCode() != 200) {
                            errorMsg = String.format("查询会员权益失败,msg:%s,param:%s", resp.getCommonResp().getMsg(), paramString);
                        }
                    }
                    if (errorMsg != null) {
                        log.error(errorMsg, new RpcException(errorMsg));
                    }
                })
                .filter(memberDiscountInfoRespDTO -> MapUtils.isNotEmpty(memberDiscountInfoRespDTO.getSubjectInterestMap()))
                .flatMap(memberDiscountInfoRespDTO -> memberDiscountInfoRespDTO.getSubjectInterestMap().entrySet().stream())
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> mapNewMemberInterestDetailToShopMemberDetail(entry.getValue())
                ));
        return new ShopMemberQueryResponse(true, result);
    }

    private ShopMemberDetailV mapNewMemberInterestDetailToShopMemberDetail(final MemberInterestDetailDTO memberInterestDetailDTO) {
        ShopMemberDetailV shopMemberDetailV = new ShopMemberDetailV();
        shopMemberDetailV.setOriginalResult(memberInterestDetailDTO);
        if (memberInterestDetailDTO == null) {
            return shopMemberDetailV;
        }
        shopMemberDetailV.setPlanId(Optional.ofNullable(memberInterestDetailDTO.getPlanId()).orElse(0L));
        UserIdentityDTO userIdentity = memberInterestDetailDTO.getUserIdentity();
        if (userIdentity != null) {
            shopMemberDetailV.setMember(userIdentity.isMember());
            shopMemberDetailV.setNewMember(UserMemberTypeEnum.NEW_MEMBER.getCode().equals(userIdentity.getMemberType()));
            shopMemberDetailV.setDealOnlyForNewMember(isDealOnlyForNewMember(memberInterestDetailDTO));
        }
        shopMemberDetailV.setDealMemberExclusive(isDealForExclusive(memberInterestDetailDTO));
        return shopMemberDetailV;
    }

    /**
     * 团单是否是会员专属
     * @param memberInterestDetailDTO
     * @return
     */
    private boolean isDealForExclusive(MemberInterestDetailDTO memberInterestDetailDTO) {
        List<MemberInterestDTO> memberInterestDTOList = Optional.ofNullable(memberInterestDetailDTO.getInterests()).orElse(new ArrayList<>());
        Set<String> allInterestCodes = memberInterestDTOList.stream()
                .filter(Objects::nonNull)
                .map(MemberInterestDTO::getInterestCode)
                .collect(Collectors.toSet());
        return allInterestCodes.contains(MemberInterestCodeEnum.PLATFORM_PRODUCT_EXCLUSIVE.getCode());
    }

    /**
     * 团单是否只适用于新会员
     */
    private boolean isDealOnlyForNewMember(MemberInterestDetailDTO memberInterestDetailDTO) {
        List<MemberInterestDTO> memberInterestDTOList = Optional.ofNullable(memberInterestDetailDTO.getInterests()).orElse(new ArrayList<>());
        Set<String> allInterestCodes = memberInterestDTOList.stream()
                .filter(Objects::nonNull)
                .map(MemberInterestDTO::getInterestCode)
                .collect(Collectors.toSet());
        if (allInterestCodes.size() == 1 && allInterestCodes.contains(MemberInterestCodeEnum.PLATFORM_PRODUCT_PROMO.getCode())) {
            List<String> allTargetMemberTypes = memberInterestDTOList.stream()
                    .filter(Objects::nonNull)
                    .map(MemberInterestDTO::getInterestUseRule)
                    .filter(Objects::nonNull)
                    .map(MemberInterestUseRuleDTO::getTargetMemberTypes)
                    .reduce((list1, list2) -> {
                        list1.addAll(list2);
                        return list1;
                    }).orElse(new ArrayList<>());
            return allTargetMemberTypes.size() == 1 && allTargetMemberTypes.contains(UserMemberTypeEnum.NEW_MEMBER.getCode());
        }
        return false;
    }

    @Override
    public CompletableFuture<PriceResponse<Map<Long, List<PriceDisplayDTO>>>> batchQueryPriceWithResponse(BatchPriceRequest request) {
        CompletableFuture<PriceResponse<Map<Long, List<PriceDisplayDTO>>>> future = PigeonCallbackUtils.setPigeonCallback();
        priceDisplayService.batchQueryPriceByLongShopId(request);
        return future.exceptionally(ex -> {
            log.error("batchQueryPriceWithResponse error, request : {}", JsonCodec.encodeWithUTF8(request), ex);
            return null;
        }).thenApply(result -> result);
    }

    @Override
    public CompletableFuture<Map<SalesSubjectParam, SalesDisplayInfoDTO>> querySaleDisplay(SalesDisplayQueryRequest request) {
        CompletableFuture<IResponse<Map<SalesSubjectParam, SalesDisplayInfoDTO>>> future = PigeonCallbackUtils.setPigeonCallback();
        salesDisplayQueryService.batchQuerySales(request);
        return future.thenApply(result -> {
            if (result == null || !result.isSuccess() || result.getData() == null) {
                return null;
            }
            return result.getData();
        });
    }

    @Override
    public CompletableFuture<DzProductDocResp> queryDzProductDoc(DzVpoiReq dzVpoiReq) {
        welfareDocFacade.queryDzProductDoc(dzVpoiReq);
        return ThriftAsyncUtils.getThriftFuture().exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "queryDzProductDoc")
                            .message(String.format("queryDzProductDoc error, request : %s", JsonCodec.encodeWithUTF8(dzVpoiReq))));
                    return null;
                })
                .thenApply(result -> {
                    if (result == null) {
                        return null;
                    }
                    return (DzProductDocResp)result;
                });
    }

    @Override
    public CompletableFuture<List<ResourceExposureResponseDTO>> queryExposureResources(QueryExposureResourcesReqDTO request) {
        CompletableFuture<DeliveryCommonResponse<List<ResourceExposureResponseDTO>>> future = PigeonCallbackUtils.setPigeonCallback();
        resourcesExposureService.queryExposureResources(request);
        return future.thenApply(result -> {
            if (Objects.isNull(result) || !Objects.equals(result.getCode(), DeliveryErrorCode.SUCESS.getErrorCode()) || Objects.isNull(result.getData())) {
                return Lists.newArrayList();
            }
            return result.getData();
        });
    }

    @Override
    public CompletableFuture<BatchExProxyCouponResponseDTO> queryExcludeProxyCouponList(BatchExProxyCouponRequest request) {
        CompletableFuture<Response<BatchExProxyCouponResponseDTO>> future = PigeonCallbackUtils.setPigeonCallback();
        couponComponentQueryServiceFuture.queryExcludeProxyCouponList(request);
        return future.exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "queryExcludeProxyCouponList")
                            .message(String.format("queryExcludeProxyCouponList error, request : %s", JsonCodec.encodeWithUTF8(request))));
                    return null;
                })
                .thenApply(result -> {
                    if (result == null || !result.isSuccess() || result.getResult() == null) {
                        return null;
                    }
                    return result.getResult();
                });
    }

    @Override
    public CompletableFuture<LoadLeadsInfoRespDTO> loadLeadsInfo(LoadLeadsInfoReqDTO request) throws TException {
        leadsQueryGatewayServiceFuture.loadLeadsInfo(request);
        return ThriftAsyncUtils.getThriftFuture()
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "loadLeadsInfo")
                            .message(String.format("loadLeadsInfo error, request : %s", JsonCodec.encodeWithUTF8(request))));
                    return null;
                })
                .thenApply(result -> (LoadLeadsInfoRespDTO) result);
    }

    @Override
    public CompletableFuture<ActivityDetailDTO> querySpecialValueDeal(ActivityProductQueryRequest request) {
        CompletableFuture<com.dianping.product.shelf.common.dto.Response> future = PigeonCallbackUtils.setPigeonCallback();
        activityShelfQueryServiceFuture.queryOnlineActivity(request);
        return future
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "querySpecialValueDeal")
                            .message(String.format("querySpecialValueDeal error, request : %s", JsonCodec.encodeWithUTF8(request))));
                    return null;
                })
                .thenApply(result ->{
                    if (result == null || !result.isSuccess()){
                        return null;
                    }
                    return (ActivityDetailDTO) result.getContent();
                });
    }

    @Override
    public CompletableFuture<PlayExecuteResponse> queryExecutePlay(PlayExecuteRequest playExecuteRequest) throws TException {
        OctoThriftCallback playCallBack = new OctoThriftCallback();
        playCenterServiceFuture.executePlay(playExecuteRequest, playCallBack);
        return ThriftAsyncUtils.getThriftFuture()
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "queryExecutePlay")
                            .message(String.format("queryExecutePlay error, request : %s", JsonCodec.encodeWithUTF8(playExecuteRequest))));
                    return null;
                })
                .thenApply(result -> (PlayExecuteResponse) result);
    }

    @Override
    public CompletableFuture<PlayExecuteResponse> queryNewCustomExecutePlay(PlayExecuteRequest playExecuteRequest) throws TException {
        OctoThriftCallback playCallBack = new OctoThriftCallback();
        playCenterServiceFuture.executePlay(playExecuteRequest, playCallBack);
        return ThriftAsyncUtils.getThriftFuture()
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "queryNewCustomExecutePlay")
                            .message(String.format("queryNewCustomExecutePlay error, request : %s", JsonCodec.encodeWithUTF8(playExecuteRequest))));
                    return null;
                })
                .thenApply(result -> (PlayExecuteResponse) result);
    }

    @Override
    public CompletableFuture<PlayExecuteResponse> querySceneExecutePlay(ScenePlayExecuteRequest scenePlayExecuteRequest) throws TException {
        OctoThriftCallback playCallBack = new OctoThriftCallback();
        playCenterServiceFuture.sceneExecutePlay(scenePlayExecuteRequest, playCallBack);
        return ThriftAsyncUtils.getThriftFuture(playCallBack)
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "querySceneExecutePlay")
                            .message(String.format("querySceneExecutePlay error, request : %s", JsonCodec.encodeWithUTF8(scenePlayExecuteRequest))));
                    return null;
                })
                .thenApply(result -> (PlayExecuteResponse) result);
    }

    @Override
    public CompletableFuture<com.dianping.martgeneral.recommend.api.entity.Response<RecommendResult>> recommend(RecommendParameters recommendParameters) {
        CompletableFuture<com.dianping.martgeneral.recommend.api.entity.Response<RecommendResult>> future = PigeonCallbackUtils.setPigeonCallback();
        try {
            recommendService.recommend(recommendParameters, RecommendDTO.class);
            return future;
        } catch (Exception e) {
            log.error("CompositeAtmoServiceImpl recommend error, request={}", JsonCodec.encodeWithUTF8(recommendParameters), e);
            return CompletableFuture.completedFuture(null);
        }
    }

    @Override
    public CompletableFuture<com.dianping.martgeneral.recommend.api.entity.Response<RecommendResult<RecommendDTO>>> recommendRecommendDTO(RecommendParameters recommendParameters) {
        CompletableFuture<com.dianping.martgeneral.recommend.api.entity.Response<RecommendResult<RecommendDTO>>> future = PigeonCallbackUtils.setPigeonCallback();
        recommendService.recommend(recommendParameters, RecommendDTO.class);
        future.exceptionally(e -> {
            log.error(XMDLogFormat.build().putTag("scene", "CompositeAtmoService").putTag("method", "recommendRecommendDTO")
                    .message(String.format("recommend error, request : %s",
                            JsonCodec.encodeWithUTF8(recommendParameters))));
            return null;
        });
        return future;
    }

    @Override
    public CompletableFuture<TyingBlackListResponse> batchQueryTyingBlackList(TyingBlackListQueryRequest tyingBlackListQueryRequest) {
        try {
            packBlackListService.batchQueryTyingBlackList(tyingBlackListQueryRequest);
            return ThriftAsyncUtils.getThriftFuture();
        } catch (Exception e) {
            log.error("batchQueryTyingBlackList error, request={}", JsonCodec.encodeWithUTF8(tyingBlackListQueryRequest), e);
            return CompletableFuture.completedFuture(null);
        }
    }

    @Override
    public CompletableFuture<Map<Long, Long>> getPinProductIdByDealGroupIds(List<Long> dealGroupIds) {
        if (CollectionUtils.isEmpty(dealGroupIds)) {
            throw new IllegalArgumentException("dealGroupIds为空!!!");
        }
        CompletableFuture<Map<Long, Long>> future = PigeonCallbackUtils.setPigeonCallback();
            pinFacadeServiceFuture.getPinProductIdByLongDealGroupIds(dealGroupIds, new GetPinProductIdOptional());
            return future.exceptionally(e ->{
                        log.error(XMDLogFormat.build()
                                .putTag("scene", "compositeAtomService")
                                .putTag("method", "getPinProductIdByDealGroupIds")
                                .message(String.format("getPinProductIdByDealGroupIds error, request : %s", JsonCodec.encodeWithUTF8(dealGroupIds))));
                        return null;
                    });
    }

    @Override
    public CompletableFuture<Map<Integer, PinProductBrief>> batchGetPinProductBrief(GetPinProductBriefReq req) {
        if (req == null || CollectionUtils.isEmpty(req.getLongPinProductIds())) {
            throw new IllegalArgumentException("普通拼团Id为空");
        }
        CompletableFuture<Map<Integer, PinProductBrief>> future = PigeonCallbackUtils.setPigeonCallback();
        pinFacadeServiceFuture.mGetPinProductBrief(req);
        return future;
    }

    @Override
    public CompletableFuture<List<PromoDisplayDTO>> queryPromoDisplayDTO(QueryPromoDisplayRequest request) {
        CompletableFuture<com.dianping.pay.promo.rule.api.dto.Response<List<PromoDisplayDTO>>> future = PigeonCallbackUtils.setPigeonCallback();
            promoDisplayServiceFuture.queryPromoDisplayDTO(request);
            return future.exceptionally(e -> {
                        log.error(XMDLogFormat.build()
                                .putTag("scene", "compositeAtomService")
                                .putTag("method", "queryPromoDisplayDTO")
                                .message(String.format("queryPromoDisplayDTO error, request : %s", JsonCodec.encodeWithUTF8(request))));
                        return null;
            }).thenApply(result -> {
                        if (result == null || !result.isSuccess()){
                            return null;
                        }
                return result.getResult();
                    });
    }

    @Override
    public CompletableFuture<Map<Long, DealSkuSummaryDTO>> batchQuerySummary(SkuOptionBatchRequest skuOptionRequest) {
        CompletableFuture<Map<Long, DealSkuSummaryDTO>> future = PigeonCallbackUtils.setPigeonCallback();
        dealSkuServiceFuture.batchQuerySummary(skuOptionRequest);
        return future.thenApply(result -> {
            if (result == null) {
                return null;
            }
            return result;
        });
    }

    @Override
    public CompletableFuture<AccessResponseDTO> prepareAccessIn(AccessRequestDTO request) {
        CompletableFuture<AccessResponseDTO> future = PigeonCallbackUtils.setPigeonCallback();
        cscAccessInServiceFuture.accessIn(request);
        return future.exceptionally(e -> {
            log.error(XMDLogFormat.build()
                    .putTag("scene", "compositeAtomService")
                    .putTag("method", "prepareAccessIn")
                    .message(String.format("prepareAccessIn error, request : %s", JsonCodec.encodeWithUTF8(request))));
            return null;
        });
    }

    @Override
    public CompletableFuture<ClientEntryDTO> preOnlineConsultUrl(ClientEntryReqDTO request) {
        CompletableFuture<ClientEntryDTO> future = PigeonCallbackUtils.setPigeonCallback();
        clientEntryService.getClientEntry(request);
        return future.exceptionally(e -> {
            log.error(XMDLogFormat.build()
                    .putTag("scene", "compositeAtomService")
                    .putTag("method", "preOnlineConsultUrl")
                    .message(String.format("preOnlineConsultUrl error, request : %s", JsonCodec.encodeWithUTF8(request))));
            return null;
        });
    }

    @Override
    public CompletableFuture<CardResponse<CardSummaryBarDTO>> preTimesCardsV2(QueryDealGroupCardBarSummaryRequest request) {
        CompletableFuture<CardResponse<CardSummaryBarDTO>> future = PigeonCallbackUtils.setPigeonCallback();
        timesCardNavigation.loadDealGroupCardBarSummary(request);
        return future.exceptionally(e -> {
            log.error(XMDLogFormat.build()
                    .putTag("scene", "compositeAtomService")
                    .putTag("method", "preTimesCardsV2")
                    .message(String.format("preTimesCardsV2 error, request : %s", JsonCodec.encodeWithUTF8(request))));
            return null;
        });
    }

    @Override
    public CompletableFuture<List<DzCardDTO>> queryDzCard(DzCardQueryRequest request) {
        CompletableFuture<List<DzCardDTO>> future = PigeonCallbackUtils.setPigeonCallback();
        try {
            dzCardQueryService.queryCard(request);
            return future;
        } catch (Exception e) {
            log.error("CompositeAtmoServiceImpl queryDzCard error, request={}", JsonCodec.encodeWithUTF8(request), e);
            return null;
        }
    }

    @Override
    public CompletableFuture<List<ObjectGuaranteeTagDTO>> batchQueryGuaranteeTag(SessionContextDTO sessionContext, BatchQueryGuaranteeTagRequest request) {
        try {
            guaranteeQueryService.batchQueryGuaranteeTag(sessionContext, request);
            CompletableFuture<com.sankuai.nib.price.operation.api.common.response.Response<List<ObjectGuaranteeTagDTO>>> future = ThriftAsyncUtils.getThriftFuture();
            return future.thenApply(result -> {
                if (Objects.isNull(result) || Objects.isNull(result.getData())) {
                    return null;
                }
                return result.getData();
            });
        } catch (Exception e) {
            log.error("batchQueryGuaranteeTag error, request={}", JsonCodec.encodeWithUTF8(request), e);
            return CompletableFuture.completedFuture(null);
        }
    }

    @Override
    public CompletableFuture<Map<Long, Long>> batchPlatformProductId(BizProductIdConvertRequest request) {
        try {
            idService.convertBizProductIdsToProductIds(request);
            CompletableFuture<BizProductIdConvertResponse> future = ThriftAsyncUtils.getThriftFuture();
            return future.thenApply(result -> {
                if (Objects.isNull(result) || !result.isSuccess()) {
                    return Maps.newHashMap();
                }
                return result.getBizProductIdConvertResult();
            });
        } catch (Exception e) {
            log.error("batchPlatformProductId error, request={}", JsonCodec.encodeWithUTF8(request), e);
            return CompletableFuture.completedFuture(Maps.newHashMap());
        }
    }

    @Override
    public CompletableFuture<Map<Long, Long>> convertMtGroupIdsToDpGroupIds(List<Long> mtGroupIds) {
        try {
            MtGroupIdConvertRequest req = new MtGroupIdConvertRequest();
            req.setMtGroupIds(mtGroupIds);
            idService.convertMtGroupIdsToDpGroupIds(req);
            CompletableFuture<GroupIdConvertResponse> future = ThriftAsyncUtils.getThriftFuture();
            return future.thenApply(result -> {
                if (Objects.isNull(result) || !result.isSuccess()) {
                    return Maps.newHashMap();
                }
                return result.getGroupIdConvertResult().entrySet().stream().collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> entry.getValue().getGroupId()));
            });
        } catch (Exception e) {
            log.error("convertMtGroupIdsToDpGroupIds error, request={}", JsonCodec.encodeWithUTF8(mtGroupIds), e);
            return CompletableFuture.completedFuture(Maps.newHashMap());
        }
    }

    @Override
    public CompletableFuture<Map<Long, Long>> convertDpGroupIdsToMtGroupIds(List<Long> dpGroupIds) {
        try {
            DpGroupIdConvertRequest req = new DpGroupIdConvertRequest();
            req.setDpGroupIds(dpGroupIds);
            idService.convertDpGroupIdsToMtGroupIds(req);
            CompletableFuture<GroupIdConvertResponse> future = ThriftAsyncUtils.getThriftFuture();
            return future.thenApply(result -> {
                if (Objects.isNull(result) || !result.isSuccess()) {
                    return Maps.newHashMap();
                }
                return result.getGroupIdConvertResult().entrySet().stream().collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> entry.getValue().getGroupId()));
            });
        } catch (Exception e) {
            log.error("convertDpGroupIdsToMtGroupIds error, request={}", JsonCodec.encodeWithUTF8(dpGroupIds), e);
            return CompletableFuture.completedFuture(Maps.newHashMap());
        }
    }


    @Override
    public CompletableFuture<Map<ProductParam, SalesDisplayDTO>> multiGetSales(SalesDisplayRequest request) {
        CompletableFuture<Map<ProductParam, SalesDisplayDTO>> future = PigeonCallbackUtils.setPigeonCallback();
        productSceneSalesDisplayService.multiGetSales(request);
        return future.exceptionally(e -> {
            log.error(XMDLogFormat.build()
                    .putTag("scene", "compositeAtomService")
                    .putTag("method", "multiGetSales")
                    .message(String.format("multiGetSales error, request : %s", JsonCodec.encodeWithUTF8(request))));
            return null;
        });
    }

    @Override
    public CompletableFuture<Map<String, String>> batchGetCreateOrderPageUrl(BatchGetCreateOrderPageUrlReq request) {
        CompletableFuture<com.dianping.tuangu.dztg.usercenter.api.dto.Response<Map<String, String>>> future = PigeonCallbackUtils.setPigeonCallback();
        createOrderPageUrlService.batchGetCreateOrderPageUrl(request);
        return future.thenApply(r -> {
                    if (!r.isSuccess() || MapUtils.isEmpty(r.getContent())) {
                        return null;
                    }
                    return r.getContent();
                })
        .exceptionally(e -> {
            log.error(XMDLogFormat.build()
                    .putTag("scene", "compositeAtomService")
                    .putTag("method", "batchGetCreateOrderPageUrl")
                    .message(String.format("batchGetCreateOrderPageUrl error, request : %s", JsonCodec.encodeWithUTF8(request))));
            return null;
        });
    }

    @Override
    public CompletableFuture<QueryDealGroupListResponse> queryByDealGroupIds(QueryByDealGroupIdRequest request) {
        try {
            dealGroupQueryServiceAsync.queryByDealGroupIds(request);
            return ThriftAsyncUtils.getThriftFuture();
        } catch (TException e) {
            log.error(XMDLogFormat.build()
                    .putTag("scene", "compositeAtomService")
                    .putTag("method", "queryByDealGroupIds")
                    .message(String.format("queryByDealGroupIds error, request : %s", JsonCodec.encodeWithUTF8(request))));
        }
        return CompletableFuture.completedFuture(null);
    }

    @Override
    public CompletableFuture<DealProductResult> queryDealSubtitle(DealProductRequest request) {
        CompletableFuture<DealProductResult> future = PigeonCallbackUtils.setPigeonCallback();
        dealProductBizQueryService.queryDealSubtitle(request);
        return future.exceptionally(e -> {
            log.error(XMDLogFormat.build()
                    .putTag("scene", "compositeAtomService")
                    .putTag("method", "queryDealSubtitle")
                    .message(String.format("queryDealSubtitle error, request : %s", JsonCodec.encodeWithUTF8(request))));
            return null;
        });
    }

    @Override
    public CompletableFuture<GetUserQualificationOpenResponse> getUserQualificationResponse(GetUserQualificationOpenRequest request) {
        try {
            log.info("getUserQualificationOpen request={}", JsonCodec.encodeWithUTF8(request));
            GetUserQualificationOpenResponse response = stateSubsidiesOpenServiceFuture.getUserQualificationOpen(request);
            log.info("getUserQualificationOpen response={}", JsonCodec.encodeWithUTF8(response));
            return CompletableFuture.completedFuture(response);
        } catch (Exception e) {
            log.error("GetUserQualificationResponse error, request={}", JsonCodec.encodeWithUTF8(request), e);
            return CompletableFuture.completedFuture(null);
        }
    }

    @Override
    public CompletableFuture<GovSubsidyInfo> queryGovSubsidyInfo(QueryGovSubsidyInfoRequest request) {
        try {
            CompletableFuture<com.dianping.tpfun.product.api.common.IResponse<GovSubsidyInfo>> future = PigeonCallbackUtils.setPigeonCallback();
            govSubsidyInfoQueryService.queryGovSubsidyInfoByUpcCode(request);
            return future.thenApply(result -> {
                if (Objects.isNull(result) || !result.isSuccess()) {
                    return new GovSubsidyInfo();
                }
                return result.getResult();
            });
        } catch (Exception e) {
            log.error("queryGovSubsidyInfo error, request={}", JsonCodec.encodeWithUTF8(request), e);
            return CompletableFuture.completedFuture(new GovSubsidyInfo());
        }
    }

    @Override
    public CompletableFuture<com.sankuai.tpfun.skuoperationapi.price.dto.response.Response<BatchPriceRangeInfoResponse>> batchGetPriceRangeInfo(BatchPriceRangeInfoRequest request) {
        try {
            priceRangeQueryService.batchGetPriceRangeInfo(request);
        } catch (Exception e) {
            log.error(XMDLogFormat.build()
                    .putTag("scene", "compositeAtomService")
                    .putTag("method", "batchGetPriceRangeInfo")
                    .message(String.format("batchGetPriceRangeInfo error, request : %s", JsonCodec.encodeWithUTF8(request))));
        }
        return ThriftAsyncUtils.getThriftFuture();
    }

    @Override
    public CompletableFuture<DealProductResult> queryDealProductTheme(DealProductRequest dealProductRequest) {
        return AthenaInf.getRpcCompletableFuture(dealProductService.query(dealProductRequest)).exceptionally(e -> {
            log.error(XMDLogFormat.build()
                    .putTag("scene", "compositeAtomService")
                    .putTag("method", "queryDealProductTheme")
                    .message(String.format("dealProductService.query error，request: %s", JsonCodec.encode(dealProductRequest))));
            log.error(String.format("%s_%s", "CompositeAtomServiceImpl", "queryDealProductTheme"), e);
            return null;
        });
    }

    @Override
    public CompletableFuture<DealProductResult> queryDealProductByTheme(DealProductRequest dealProductRequest) {
        CompletableFuture<DealProductResult> future = PigeonCallbackUtils.setPigeonCallback();
        dealProductService.query(dealProductRequest);
        return future.exceptionally(e -> {
            log.error(XMDLogFormat.build()
                    .putTag("scene", "compositeAtomService")
                    .putTag("method", "queryDealProductByTheme")
                    .message(String.format("queryDealProductByTheme error, request : %s", JsonCodec.encodeWithUTF8(dealProductRequest))));
            return null;
        });
    }

    @Override
    public CompletableFuture<DealProductResult> queryNewDealProductTheme(DealProductRequest dealProductRequest) {
        return AthenaInf.getRpcCompletableFuture(dealProductNewService.query(dealProductRequest)).exceptionally(e -> {
            log.error(XMDLogFormat.build()
                    .putTag("scene", "compositeAtomService")
                    .putTag("method", "queryNewDealProductTheme")
                    .message(String.format("dealProductNewService.query error，request: %s", JsonCodec.encode(dealProductRequest))));
            log.error(String.format("%s_%s", "CompositeAtomServiceImpl", "queryNewDealProductTheme"), e);
            return null;
        });
    }

    @Override
    public CompletableFuture<RecommendResult<RecommendDTO>> getRecommendResult(RecommendParameters recommendParameters) {
        return AthenaInf.getRpcCompletableFuture(recommendService.recommend(recommendParameters, RecommendDTO.class))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build()
                            .putTag("scene", "compositeAtomService")
                            .putTag("method", "getRecommendResult")
                            .message(String.format("getRecommendResult error, req: %s", JsonCodec.encode(recommendParameters))));
                    log.error(String.format("%s_%s", "CompositeAtomServiceImpl", "getRecommendResult"), e);
                    return null;
                })
                .thenApply(response -> {
                    if (response == null || !response.isSuccess() || response.getResult() == null) {
                        return null;
                    }
                    return JsonCodec.decode(JsonCodec.encode(response.getResult()), new TypeReference<RecommendResult<RecommendDTO>>() {
                    });
                });
    }

    @Override
    public CompletableFuture<DealProductResult> queryDealProduct(DealProductRequest req) {
        CompletableFuture<DealProductResult> future = PigeonCallbackUtils.setPigeonCallback();
        dealProductService.query(req);
        future.exceptionally(e -> {
            log.error(XMDLogFormat.build().putTag("scene", "compositeAtomService").putTag("method", "queryDealProduct")
                    .message(String.format("dealProductService.query error, request : %s", JsonCodec.encodeWithUTF8(req))));
            return null;
        });
        return future;
    }

    @Override
    public CompletableFuture<Map<Long, List<Long>>> queryMtByDpIdsL(List<Long> dpShopIds) {
        CompletableFuture<Map<Long, List<Long>>> future = PigeonCallbackUtils.setPigeonCallback();
        try{
            poiRelationService.queryMtByDpIdsL(dpShopIds);
            return future;
        } catch (Exception e) {
            log.error(XMDLogFormat.build().putTag("scene", "compositeAtomService")
                    .putTag("method", "queryMtByDpIdsL").message(String.format(
                            "queryMtByDpIdsL error, request : %s", JsonCodec.encodeWithUTF8(dpShopIds))));
            return null;
        }
    }

    @Override
    public CompletableFuture<Map<Long, List<Long>>> batchQuerySaleDealGroupId(long dpShopId, boolean isMt) {
        CompletableFuture<Map<Long, List<Long>>> future = PigeonCallbackUtils.setPigeonCallback();
        dealShopQueryService.batchQuerySaleDealGroupId(Sets.newHashSet(dpShopId), isMt ? 200 : 100, 1, 100);
        future.exceptionally(e -> {
            log.error(XMDLogFormat.build().putTag("scene", "compositeAtomService")
                    .putTag("method", "querySaleDealGroupId")
                    .message(String.format("batchQuerySaleDealGroupId error, dpShopId : %s, isMt : %s,error message:%s",
                            dpShopId, isMt, e)));
            return null;
        });
        return future;
    }

}
