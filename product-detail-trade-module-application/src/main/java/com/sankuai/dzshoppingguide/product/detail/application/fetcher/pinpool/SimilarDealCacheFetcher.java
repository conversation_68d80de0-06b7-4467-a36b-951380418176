package com.sankuai.dzshoppingguide.product.detail.application.fetcher.pinpool;

import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.athena.inf.cache2.CacheClient;
import com.sankuai.athena.inf.cache2.CacheKey;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.dto.ProductIdDTO;
import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
import com.sankuai.dzshoppingguide.product.detail.domain.utils.RedisClientUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Optional;
import java.util.Random;
import java.util.concurrent.CompletableFuture;

/**
 * 从redis中获取相识团缓存单Fetcher
 */
@Fetcher(
        previousLayerDependencies = { ProductBaseInfoFetcher.class}
)
@Slf4j
public class SimilarDealCacheFetcher extends NormalFetcherContext<SimilarDealCacheResult> {


    /**
     * 团详页相似团单-缓存key
     */
    public static final String CACHE_SIMILAR_DEAL = "similar_deal";
    private static final int CACHE_EXPIRE_TIME = 172800; // 2天
    private static final int CACHE_REFRESH_TIME = 0;

    @Autowired
    private CompositeAtomService compositeAtomService;

    private static CacheClient cacheClient = RedisClientUtils.getRedisCacheClient();

    @Override
    protected CompletableFuture<SimilarDealCacheResult> doFetch() {
        Optional<ProductBaseInfo> productBaseInfoOptional = getDependencyResult(ProductBaseInfoFetcher.class, ProductBaseInfo.class);
        long mtDealGroupId = productBaseInfoOptional.map(ProductBaseInfo::getProductIdDTO).map(ProductIdDTO::getMtProductId).orElse(0L);
        return fetchSimilarDealCache(String.valueOf(mtDealGroupId))
                .thenApply(SimilarDealCacheResult::new);
    }

    private CompletableFuture<String> fetchSimilarDealCache(String dealId) {
        int platform = request.getPlatformEnum().getCode();
        CacheKey cacheKey = new CacheKey(CACHE_SIMILAR_DEAL, platform, dealId);


        return cacheClient.asyncGetReadThrough(
                cacheKey,
                new TypeReference<String>() {
                },
                null,
                CACHE_EXPIRE_TIME + new Random().nextInt(300),
                CACHE_REFRESH_TIME
        );
    }
}