package com.sankuai.dzshoppingguide.product.detail.spi.ab.vo;

import com.sankuai.dz.product.detail.gateway.spi.dto.ABResultDTO;
import com.sankuai.dz.product.detail.gateway.spi.vo.AbstractModuleVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

import static com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants.PAGE_AB_RESULT;

@EqualsAndHashCode(callSuper = true)
@Data
public class AbResultVO extends AbstractModuleVO {

    private List<ABResultDTO> abResultList;

    @Override
    public String getModuleKey() {
        return PAGE_AB_RESULT;
    }
}
