package com.sankuai.dzshoppingguide.product.detail.application.service.impl;

import com.sankuai.dzshoppingguide.product.detail.application.service.dto.ShopMemberQueryResponse;
import com.sankuai.dzshoppingguide.product.detail.domain.utils.ThriftAsyncUtils;
import com.sankuai.mpmctmember.query.common.enums.MemberInterestCodeEnum;
import com.sankuai.mpmctmember.query.common.enums.PlatformEnum;
import com.sankuai.mpmctmember.query.thrift.api.MemberInterestQueryService;
import com.sankuai.mpmctmember.query.thrift.dto.*;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.CompletableFuture;

import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.powermock.api.mockito.PowerMockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({ ThriftAsyncUtils.class })
public class CompositeAtomServiceImplGetMemberDiscountInfoListTest {

    @InjectMocks
    private CompositeAtomServiceImpl compositeAtomService;

    @Mock
    private MemberInterestQueryService newMemberInterestQueryService;

    private BatchQueryMemberInterestResp createSuccessResponse() {
        BatchQueryMemberInterestResp mockResp = new BatchQueryMemberInterestResp();
        CommonRespDTO commonResp = new CommonRespDTO();
        commonResp.setCode(200);
        mockResp.setCommonResp(commonResp);
        // Set subjectInterestMap
        HashMap<Long, MemberInterestDetailDTO> interestMap = new HashMap<>();
        MemberInterestDetailDTO detailDTO = new MemberInterestDetailDTO();
        detailDTO.setPlanId(1L);
        // Set user identity
        UserIdentityDTO userIdentity = new UserIdentityDTO();
        userIdentity.setMember(true);
        userIdentity.setMemberType("NORMAL");
        detailDTO.setUserIdentity(userIdentity);
        // Set interests
        List<MemberInterestDTO> interests = new ArrayList<>();
        MemberInterestDTO interest = new MemberInterestDTO();
        interest.setInterestCode(MemberInterestCodeEnum.PLATFORM_PRODUCT_EXCLUSIVE.getCode());
        interests.add(interest);
        detailDTO.setInterests(interests);
        interestMap.put(1L, detailDTO);
        mockResp.setSubjectInterestMap(interestMap);
        return mockResp;
    }

    @Before
    public void setUp() {
        PowerMockito.mockStatic(ThriftAsyncUtils.class);
        BatchQueryMemberInterestResp successResp = createSuccessResponse();
        PowerMockito.when(ThriftAsyncUtils.<BatchQueryMemberInterestResp>getThriftFuture()).thenReturn(CompletableFuture.completedFuture(successResp));
        when(newMemberInterestQueryService.batchQueryMemberInterest(any())).thenReturn(successResp);
    }

    /**
     * Test case for null or empty input parameters
     */
    @Test
    public void testGetMemberDiscountInfoList_EmptyParams() throws Throwable {
        // arrange
        Long mtVirtualUserId = null;
        List<Long> dpDealGroupIds = new ArrayList<>();
        PlatformEnum platform = PlatformEnum.MT_APP;
        boolean needPageUrl = false;
        // act
        CompletableFuture<ShopMemberQueryResponse> future = compositeAtomService.getMemberDiscountInfoList(mtVirtualUserId, platform, dpDealGroupIds, needPageUrl);
        // assert
        ShopMemberQueryResponse response = future.get();
        assertNotNull("Response should not be null", response);
        assertTrue("New interface flag should be true", response.isNewInterface());
        assertTrue("Member detail map should be empty", response.getDpDealId_shopMemberDetail_map().isEmpty());
    }

    /**
     * Test case for normal scenario with valid input
     */
    @Test
    public void testGetMemberDiscountInfoList_Success() throws Throwable {
        // arrange
        Long mtVirtualUserId = 1L;
        List<Long> dpDealGroupIds = Arrays.asList(1L, 2L);
        PlatformEnum platform = PlatformEnum.MT_APP;
        boolean needPageUrl = false;
        // act
        CompletableFuture<ShopMemberQueryResponse> future = compositeAtomService.getMemberDiscountInfoList(mtVirtualUserId, platform, dpDealGroupIds, needPageUrl);
        // assert
        ShopMemberQueryResponse response = future.get();
        assertNotNull("Response should not be null", response);
        assertTrue("New interface flag should be true", response.isNewInterface());
        verify(newMemberInterestQueryService, times(1)).batchQueryMemberInterest(any());
    }

    /**
     * Test case for service error response
     */
    @Test
    public void testGetMemberDiscountInfoList_ServiceError() throws Throwable {
        // arrange
        Long mtVirtualUserId = 1L;
        List<Long> dpDealGroupIds = Arrays.asList(1L);
        PlatformEnum platform = PlatformEnum.MT_APP;
        boolean needPageUrl = false;
        BatchQueryMemberInterestResp errorResp = new BatchQueryMemberInterestResp();
        CommonRespDTO commonResp = new CommonRespDTO();
        commonResp.setCode(500);
        commonResp.setMsg("Internal Error");
        errorResp.setCommonResp(commonResp);
        when(newMemberInterestQueryService.batchQueryMemberInterest(any())).thenReturn(errorResp);
        PowerMockito.when(ThriftAsyncUtils.<BatchQueryMemberInterestResp>getThriftFuture()).thenReturn(CompletableFuture.completedFuture(errorResp));
        // act
        CompletableFuture<ShopMemberQueryResponse> future = compositeAtomService.getMemberDiscountInfoList(mtVirtualUserId, platform, dpDealGroupIds, needPageUrl);
        // assert
        ShopMemberQueryResponse response = future.get();
        assertNotNull("Response should not be null", response);
        assertTrue("New interface flag should be true", response.isNewInterface());
        assertTrue("Member detail map should be empty for error response", response.getDpDealId_shopMemberDetail_map().isEmpty());
    }

    /**
     * Test case for service throwing exception
     */
    @Test
    public void testGetMemberDiscountInfoList_ServiceException() throws Throwable {
        // arrange
        Long mtVirtualUserId = 1L;
        List<Long> dpDealGroupIds = Arrays.asList(1L);
        PlatformEnum platform = PlatformEnum.MT_APP;
        boolean needPageUrl = false;
        // Create null response for error case
        when(newMemberInterestQueryService.batchQueryMemberInterest(any())).thenReturn(null);
        PowerMockito.when(ThriftAsyncUtils.<BatchQueryMemberInterestResp>getThriftFuture()).thenReturn(CompletableFuture.completedFuture(null));
        // act
        CompletableFuture<ShopMemberQueryResponse> future = compositeAtomService.getMemberDiscountInfoList(mtVirtualUserId, platform, dpDealGroupIds, needPageUrl);
        // assert
        ShopMemberQueryResponse response = future.get();
        assertNotNull("Response should not be null", response);
        assertTrue("New interface flag should be true", response.isNewInterface());
        assertTrue("Member detail map should be empty for exception", response.getDpDealId_shopMemberDetail_map().isEmpty());
        verify(newMemberInterestQueryService, times(1)).batchQueryMemberInterest(any());
    }

}
