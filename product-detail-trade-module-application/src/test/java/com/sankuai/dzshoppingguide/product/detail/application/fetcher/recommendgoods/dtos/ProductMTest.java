package com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommendgoods.dtos;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * Test cases for ProductM.getAttr method
 */
@ExtendWith(MockitoExtension.class)
public class ProductMTest {

    /**
     * Test scenario: extAttrs is null
     * Expected: Should return null
     */
    @Test
    public void testGetAttr_WhenExtAttrsIsNull() throws Throwable {
        // arrange
        ProductM product = spy(new ProductM());
        when(product.getExtAttrs()).thenReturn(null);
        // act
        String result = product.getAttr("testAttr");
        // assert
        assertNull(result);
        verify(product, times(1)).getExtAttrs();
    }

    /**
     * Test scenario: extAttrs is empty list
     * Expected: Should return null
     */
    @Test
    public void testGetAttr_WhenExtAttrsIsEmpty() throws Throwable {
        // arrange
        ProductM product = spy(new ProductM());
        when(product.getExtAttrs()).thenReturn(new ArrayList<>());
        // act
        String result = product.getAttr("testAttr");
        // assert
        assertNull(result);
        verify(product, times(1)).getExtAttrs();
    }

    /**
     * Test scenario: Requested attribute exists in extAttrs
     * Expected: Should return the attribute value
     */
    @Test
    public void testGetAttr_WhenAttributeExists() throws Throwable {
        // arrange
        ProductM product = spy(new ProductM());
        AttrM attr = new AttrM("testAttr", "testValue");
        List<AttrM> attrs = Arrays.asList(attr);
        when(product.getExtAttrs()).thenReturn(attrs);
        // act
        String result = product.getAttr("testAttr");
        // assert
        assertEquals("testValue", result);
        verify(product, times(2)).getExtAttrs();
    }

    /**
     * Test scenario: Requested attribute doesn't exist in extAttrs
     * Expected: Should return null
     */
    @Test
    public void testGetAttr_WhenAttributeDoesNotExist() throws Throwable {
        // arrange
        ProductM product = spy(new ProductM());
        AttrM attr = new AttrM("existingAttr", "testValue");
        List<AttrM> attrs = Arrays.asList(attr);
        when(product.getExtAttrs()).thenReturn(attrs);
        // act
        String result = product.getAttr("nonExistingAttr");
        // assert
        assertNull(result);
        verify(product, times(2)).getExtAttrs();
    }

    /**
     * Test scenario: extAttrs contains null element
     * Expected: Should handle null element and return correct value if found
     */
    @Test
    public void testGetAttr_WhenExtAttrsContainsNullElement() throws Throwable {
        // arrange
        ProductM product = spy(new ProductM());
        AttrM attr = new AttrM("testAttr", "testValue");
        List<AttrM> attrs = Arrays.asList(null, attr);
        when(product.getExtAttrs()).thenReturn(attrs);
        // act
        String result = product.getAttr("testAttr");
        // assert
        assertEquals("testValue", result);
        verify(product, times(2)).getExtAttrs();
    }

    /**
     * Test scenario: Multiple attributes exist in extAttrs
     * Expected: Should return the value of the specific requested attribute
     */
    @Test
    public void testGetAttr_WhenMultipleAttributesExist() throws Throwable {
        // arrange
        ProductM product = spy(new ProductM());
        List<AttrM> attrs = Arrays.asList(new AttrM("attr1", "value1"), new AttrM("attr2", "value2"), new AttrM("attr3", "value3"));
        when(product.getExtAttrs()).thenReturn(attrs);
        // act
        String result = product.getAttr("attr2");
        // assert
        assertEquals("value2", result);
        verify(product, times(2)).getExtAttrs();
    }
}
