package com.sankuai.dzshoppingguide.product.detail.spi.multisku.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.dz.product.detail.gateway.spi.vo.AbstractModuleVO;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.MultiSkuSelectItemVO;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2025/2/25
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TypeDoc(description = "多sku选择模块")
@MobileDo(id = 0xb3ea)
public class MultiSkuSelectVO extends AbstractModuleVO {
    @Override
    public String getModuleKey() {
        return ModuleKeyConstants.MODULE_DETAIL_DEAL_MULTI_SKU_SELECT;
    }

    @FieldDoc(description = "sku选择列表")
    @MobileDo.MobileField(key = 0x1c66)
    private List<MultiSkuSelectItemVO> skuSelectItems;
}
