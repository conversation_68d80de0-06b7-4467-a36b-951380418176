package com.sankuai.dzshoppingguide.product.detail.application.fetcher.statesubsidies;

import com.dianping.tpfun.product.api.govsubsidy.model.GovSubsidyInfo;
import com.dianping.tpfun.product.api.govsubsidy.request.QueryGovSubsidyInfoRequest;
import com.google.common.collect.Lists;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dz.product.detail.gateway.spi.dto.ShepherdGatewayParam;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.cityId.CityIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.cityId.CityIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.attr.SkuAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.attr.SkuAttrFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.defaultselect.SkuDefaultSelect;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.defaultselect.SkuDefaultSelectFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.FetcherDAGStarter;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.tag.guarantee.ProductGuaranteeTagInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.tag.guarantee.SkuGuaranteeTagFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
import com.sankuai.nib.price.operation.api.guarantee.common.dto.TagDTO;
import com.sankuai.statesubsidies.c.thrift.constant.BizLineEnum;
import com.sankuai.statesubsidies.c.thrift.constant.QualificationStatusEnum;
import com.sankuai.statesubsidies.c.thrift.dto.QualificationInfo;
import com.sankuai.statesubsidies.c.thrift.request.CommonRequest;
import com.sankuai.statesubsidies.c.thrift.request.GetUserQualificationOpenRequest;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-04-22
 * @desc 国家补贴补贴资格查询Fetcher
 */
@Fetcher(
        previousLayerDependencies = {
                FetcherDAGStarter.class,
                CityIdMapperFetcher.class,
                SkuGuaranteeTagFetcher.class,
                SkuAttrFetcher.class,
                SkuDefaultSelectFetcher.class
        }
)
public class CountrySubsidiesQualificationFetcher extends NormalFetcherContext<CountrySubsidiesQualificationDTO> {

    private static final String UPC_CODE = "Barcode";

    @Resource
    private CompositeAtomService compositeAtomService;

    @Override
    protected CompletableFuture<CountrySubsidiesQualificationDTO> doFetch() throws Exception {
        TagDTO countrySubsidiesTag = getDependencyResult(SkuGuaranteeTagFetcher.class, ProductGuaranteeTagInfo.class)
                .map(ProductGuaranteeTagInfo::getStateSubsidies)
                .orElse(null);
        if (Objects.isNull(countrySubsidiesTag)) {
            return CompletableFuture.completedFuture(null);
        }
        Long selectedSkuId = getDependencyResult(SkuDefaultSelectFetcher.class, SkuDefaultSelect.class).map(SkuDefaultSelect::getSelectedSkuId).orElse(0L);
        String upcCodeStr = getDependencyResult(SkuAttrFetcher.class, SkuAttr.class)
                .map(skuAttr -> skuAttr.getSkuAttrFirstValue(selectedSkuId, UPC_CODE))
                .orElse(null);
        if (StringUtils.isBlank(upcCodeStr) || !StringUtils.isNumeric(upcCodeStr)) {
            return CompletableFuture.completedFuture(null);
        }
        Long upcCode = Long.parseLong(upcCodeStr);
        QueryGovSubsidyInfoRequest subsidyInfoRequest = buildGovSubsidyInfoRequest(upcCode);
        CompletableFuture<GovSubsidyInfo> govSubsidyInfoCf = compositeAtomService.queryGovSubsidyInfo(subsidyInfoRequest);
        CityIdMapper cityIdMapper = getDependencyResult(CityIdMapperFetcher.class);
        GetUserQualificationOpenRequest qualificationOpenRequest = buildUserQualificationRequest(cityIdMapper);

        return compositeAtomService.getUserQualificationResponse(qualificationOpenRequest).thenApply(result -> {
            if (Objects.isNull(result) || CollectionUtils.isEmpty(result.getQualificationInfo())) {
                return null;
            }
            GovSubsidyInfo govSubsidyInfo = govSubsidyInfoCf.join();
            List<String> govCodes = Optional.ofNullable(govSubsidyInfo).map(GovSubsidyInfo::getGovCategoryCode).orElse(Lists.newArrayList());

            List<QualificationStatusEnum> qualificationStatusEnums = result.getQualificationInfo().stream()
                    .filter(qualificationInfo -> CollectionUtils.isNotEmpty(govCodes) && govCodes.contains(qualificationInfo.getGovProductCategoryCode()))
                    .map(QualificationInfo::getStatus)
                    .collect(Collectors.toList());
            QualificationStatusEnum qualificationStatusEnum = getQualificationStatus(qualificationStatusEnums);
            CountrySubsidiesQualificationDTO countrySubsidiesQualificationDTO = new CountrySubsidiesQualificationDTO();
            countrySubsidiesQualificationDTO.setQualificationStatusEnum(qualificationStatusEnum);
            return countrySubsidiesQualificationDTO;
        });
    }

    private QueryGovSubsidyInfoRequest buildGovSubsidyInfoRequest(Long upcCode) {
        QueryGovSubsidyInfoRequest queryGovSubsidyInfoRequest = new QueryGovSubsidyInfoRequest();
        queryGovSubsidyInfoRequest.setUpcCode(upcCode);
        return queryGovSubsidyInfoRequest;
    }

    private QualificationStatusEnum getQualificationStatus(List<QualificationStatusEnum> qualificationStatusEnums) {
        if (CollectionUtils.isEmpty(qualificationStatusEnums)) {
            return QualificationStatusEnum.UNBIND;
        }
        // 看国补资格是不是都被使用了
        boolean used = qualificationStatusEnums.stream().allMatch(qualificationStatusEnum -> Objects.equals(qualificationStatusEnum, QualificationStatusEnum.USED));
        if (used) {
            return QualificationStatusEnum.USED;
        }
        // 看是否已领取
        boolean hasBind = qualificationStatusEnums.stream().anyMatch(qualificationStatusEnum -> Objects.equals(qualificationStatusEnum, QualificationStatusEnum.BIND));
        if (hasBind) {
            return QualificationStatusEnum.BIND;
        }
        return QualificationStatusEnum.UNBIND;
    }

    private GetUserQualificationOpenRequest buildUserQualificationRequest(CityIdMapper cityIdMapper) {
        GetUserQualificationOpenRequest qualificationOpenRequest = new GetUserQualificationOpenRequest();
        qualificationOpenRequest.setCommonRequest(buildStateSubsidiesCommonRequest(cityIdMapper));
        qualificationOpenRequest.setBizLine(BizLineEnum.FU_WU_LING_SHOU);
        return qualificationOpenRequest;
    }

    private CommonRequest buildStateSubsidiesCommonRequest(CityIdMapper cityIdMapper) {
        ShepherdGatewayParam shepherdGatewayParam = request.getShepherdGatewayParam();
        CommonRequest commonRequest = new CommonRequest();
        commonRequest.setUserId(request.getMtUserId());
        commonRequest.setUuid(shepherdGatewayParam.getDeviceId());
        commonRequest.setMtCityId(cityIdMapper.getMtCityId());
        return commonRequest;
    }
}
