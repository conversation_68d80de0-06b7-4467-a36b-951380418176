package com.sankuai.dzshoppingguide.product.detail.application.builder.similargoods.dtos;

import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.acl.douhu.AbTestResult;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.dealgroup.DealGroupIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.shopid.ShopIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.best.shop.ShopInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dztheme.deal.res.DealProductResult;
import com.sankuai.dztheme.deal.res.DealSkuSummaryDTO;
import com.sankuai.tpfun.skuoperationapi.price.dto.response.BatchPriceRangeInfoResponse;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @create 2025/6/9 15:42
 */
@Data
@Builder
public class ShowLowPriceItemRequest {
    private ProductDetailPageRequest request;
    private ShopInfo shopInfo;
    private ProductBaseInfo productBaseInfo;
    private ShopIdMapper shopIdMapper;
    private DealGroupIdMapper dealGroupIdMapper;
    private AbTestResult abTestResult;
    private BatchPriceRangeInfoResponse batchPriceRangeInfoResponse;
    private DealSkuSummaryDTO skuSummary;
    private DealProductResult dealProductResult;
    private boolean hitShopInBlackList;
}
