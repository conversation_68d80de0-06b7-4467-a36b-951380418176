package com.sankuai.dzshoppingguide.product.detail.application.builder.pricebar.builder.impl;

import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.BuilderTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.pricebar.builder.AbstractProductBarModuleBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategoryFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.ProductNormalPriceFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.ProductPromoPriceFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.dto.ProductPriceReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.attr.SkuAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.attr.SkuAttrFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.sale.ProductSaleFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.sale.ProductSaleReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.FetcherDAGStarter;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DetailPriceVO;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DetailSaleVO;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.pricebar.vo.ProductPriceBarModuleVO;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025-03-03
 * @desc 商品详情页价格条构造器
 */
@Builder(
        builderType = BuilderTypeEnum.VARIABLE_BUILDER,
        moduleKey = ModuleKeyConstants.PRICE_SALE_BAR_MODULE,
        startFetcher = FetcherDAGStarter.class,
        dependentFetchers = {
                ProductNormalPriceFetcher.class,
                ProductPromoPriceFetcher.class,
                ProductSaleFetcher.class,
                ProductCategoryFetcher.class,
                ProductBaseInfoFetcher.class,
                SkuAttrFetcher.class
        }
)
public class ProductPriceBarModuleBuilder extends AbstractProductBarModuleBuilder<ProductPriceBarModuleVO> {

    @Override
    public ProductPriceBarModuleVO doBuild() {
        ProductPriceReturnValue normalPriceReturnValue = getDependencyResult(ProductNormalPriceFetcher.class);
        ProductPriceReturnValue promoPriceReturnValue = getDependencyResult(ProductPromoPriceFetcher.class);
        ProductSaleReturnValue productSaleReturnValue = getDependencyResult(ProductSaleFetcher.class);
        ProductCategory productCategory = getDependencyResult(ProductCategoryFetcher.class);
        ProductBaseInfo productBaseInfo = getDependencyResult(ProductBaseInfoFetcher.class);
        SkuAttr skuAttr = getDependencyResult(SkuAttrFetcher.class);

        ProductPriceBarModuleVO result = new ProductPriceBarModuleVO();
        // 价格
        ProductPriceParam priceParam = ProductPriceParam.builder()
                .normalPriceReturnValue(normalPriceReturnValue)
                .dealPromoPriceReturnValue(promoPriceReturnValue)
                .productCategory(productCategory)
                .productBaseInfo(productBaseInfo)
                .skuAttr(skuAttr)
                .build();
        DetailPriceVO priceVO = buildPriceVO(priceParam);
        if (Objects.isNull(priceVO)) {
            return null;
        }
        result.setPrice(priceVO);
        // 销量
        DetailSaleVO saleVO = buildSaleInfo(productSaleReturnValue);
        result.setSale(saleVO);
        return result;
    }

}