package com.sankuai.dzshoppingguide.product.detail.application.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.google.common.util.concurrent.SettableFuture;
import com.meituan.service.mobile.mtthrift.netty.ContextStore;
import com.sankuai.dzshoppingguide.product.detail.domain.utils.ThriftAsyncUtils;
import com.sankuai.mpproduct.idservice.api.request.BizProductIdConvertRequest;
import com.sankuai.mpproduct.idservice.api.response.BizProductIdConvertResponse;
import com.sankuai.mpproduct.idservice.api.service.IdService;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.powermock.reflect.Whitebox;

@ExtendWith(MockitoExtension.class)
class CompositeAtomServiceImplBatchPlatformProductIdTest {

    @Mock
    private IdService idService;

    @InjectMocks
    private CompositeAtomServiceImpl compositeAtomService;

    private BizProductIdConvertRequest validRequest;

    private BizProductIdConvertRequest emptyRequest;

    @BeforeEach
    void setUp() {
        validRequest = new BizProductIdConvertRequest();
        validRequest.setBizProductIds(Collections.singletonList(123L));
        emptyRequest = new BizProductIdConvertRequest();
    }

    /**
     * Helper method to set up the SettableFuture in ContextStore
     */
    private void setupContextStore(BizProductIdConvertResponse response) {
        SettableFuture<BizProductIdConvertResponse> future = SettableFuture.create();
        future.set(response);
        Whitebox.setInternalState(ContextStore.class, "threadLocalFuture", new ThreadLocal<SettableFuture<?>>() {

            @Override
            protected SettableFuture<?> initialValue() {
                return future;
            }
        });
    }

    @AfterEach
    void tearDown() {
        // Clean up ThreadLocal
        ContextStore.removeFuture();
    }

    /**
     * Tests null response from service
     */
    @Test
    public void testBatchPlatformProductId_NullResponse() throws Throwable {
        // arrange
        setupContextStore(null);
        when(idService.convertBizProductIdsToProductIds(any())).thenReturn(null);
        // act
        CompletableFuture<Map<Long, Long>> resultFuture = compositeAtomService.batchPlatformProductId(validRequest);
        Map<Long, Long> result = resultFuture.get();
        // assert
        assertTrue(result.isEmpty());
        verify(idService).convertBizProductIdsToProductIds(validRequest);
    }

    /**
     * Tests unsuccessful response (success=false)
     */
    @Test
    public void testBatchPlatformProductId_UnsuccessfulResponse() throws Throwable {
        // arrange
        BizProductIdConvertResponse mockResponse = new BizProductIdConvertResponse();
        mockResponse.setSuccess(false);
        setupContextStore(mockResponse);
        when(idService.convertBizProductIdsToProductIds(any())).thenReturn(null);
        // act
        CompletableFuture<Map<Long, Long>> resultFuture = compositeAtomService.batchPlatformProductId(validRequest);
        Map<Long, Long> result = resultFuture.get();
        // assert
        assertTrue(result.isEmpty());
        verify(idService).convertBizProductIdsToProductIds(validRequest);
    }

    /**
     * Tests exception during service call
     */
    @Test
    public void testBatchPlatformProductId_ServiceException() throws Throwable {
        // arrange
        when(idService.convertBizProductIdsToProductIds(any())).thenThrow(new RuntimeException("Service error"));
        // act
        CompletableFuture<Map<Long, Long>> resultFuture = compositeAtomService.batchPlatformProductId(validRequest);
        Map<Long, Long> result = resultFuture.get();
        // assert
        assertTrue(result.isEmpty());
        verify(idService).convertBizProductIdsToProductIds(validRequest);
    }

    /**
     * Tests empty request case
     */
    @Test
    public void testBatchPlatformProductId_EmptyRequest() throws Throwable {
        // arrange
        BizProductIdConvertResponse mockResponse = new BizProductIdConvertResponse();
        mockResponse.setSuccess(true);
        mockResponse.setBizProductIdConvertResult(Collections.emptyMap());
        setupContextStore(mockResponse);
        when(idService.convertBizProductIdsToProductIds(any())).thenReturn(null);
        // act
        CompletableFuture<Map<Long, Long>> resultFuture = compositeAtomService.batchPlatformProductId(emptyRequest);
        Map<Long, Long> result = resultFuture.get();
        // assert
        assertTrue(result.isEmpty());
        verify(idService).convertBizProductIdsToProductIds(emptyRequest);
    }

    /**
     * Tests request with null fields
     */
    @Test
    public void testBatchPlatformProductId_NullFieldsRequest() throws Throwable {
        // arrange
        BizProductIdConvertRequest nullRequest = new BizProductIdConvertRequest();
        BizProductIdConvertResponse mockResponse = new BizProductIdConvertResponse();
        mockResponse.setSuccess(true);
        mockResponse.setBizProductIdConvertResult(Collections.emptyMap());
        setupContextStore(mockResponse);
        when(idService.convertBizProductIdsToProductIds(any())).thenReturn(null);
        // act
        CompletableFuture<Map<Long, Long>> resultFuture = compositeAtomService.batchPlatformProductId(nullRequest);
        Map<Long, Long> result = resultFuture.get();
        // assert
        assertTrue(result.isEmpty());
        verify(idService).convertBizProductIdsToProductIds(nullRequest);
    }
}
