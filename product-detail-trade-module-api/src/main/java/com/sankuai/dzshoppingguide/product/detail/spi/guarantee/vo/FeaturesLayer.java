package com.sankuai.dzshoppingguide.product.detail.spi.guarantee.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dz.product.detail.gateway.spi.vo.AbstractModuleVO;

import java.io.Serializable;
import java.util.List;

@MobileDo(id = 0x4721)
public class FeaturesLayer extends AbstractModuleVO implements Serializable {

    @Override
    public String getModuleKey() {
        return ModuleKeyConstants.GUARANTEE_INFO_TAG_POPUP;
    }
    /**
    * 浮层标题
    */
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    /**
    * 价保项
    */
    @MobileDo.MobileField(key = 0x479f)
    private List<LayerConfig> layerConfigs;

    /**
    * 价保标签
    */
    @MobileDo.MobileField(key = 0xcf30)
    private String priceProtectionTag;

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public List<LayerConfig> getLayerConfigs() {
        return layerConfigs;
    }

    public void setLayerConfigs(List<LayerConfig> layerConfigs) {
        this.layerConfigs = layerConfigs;
    }

    public String getPriceProtectionTag() {
        return priceProtectionTag;
    }

    public void setPriceProtectionTag(String priceProtectionTag) {
        this.priceProtectionTag = priceProtectionTag;
    }
}