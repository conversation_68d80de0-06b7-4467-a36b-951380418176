package com.sankuai.dzshoppingguide.product.detail.application.builder.timecard.list;

import com.google.common.collect.Lists;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommendgoods.dtos.ProductM;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

@Component
@Slf4j
public class TimesDealListOpt extends AbstractTimesDealListOpt {

    @Override
    public List<ProductM> getSimilarDealList(ProductM currentProduct, List<ProductM> list) {
        return Lists.newArrayList();
    }

    @Override
    public boolean isHit(ProductCategory productCategory) {
        int categoryId = Optional.ofNullable(productCategory).map(ProductCategory::getProductSecondCategoryId).orElse(0);
        return categoryId == 303 || categoryId == 301;
    }
}