package com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.ext.param.handler;

import com.dianping.lion.client.Lion;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.enums.OrderPageExtParamEnums;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.ext.param.BaseSingleExtParamBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.ext.param.request.ExtParamBuilderRequest;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import static com.sankuai.dzshoppingguide.product.detail.domain.utils.LionConstants.DZTGDETAIL_APPKEY;

/**
 * @Author: guangyujie
 * @Date: 2025/3/12 17:26
 */
@Component
public class TrafficFlagExtParamBuilder extends BaseSingleExtParamBuilder {

    @Override
    public OrderPageExtParamEnums buildKey() {
        return OrderPageExtParamEnums.trafficflag;
    }

    @Override
    protected String doBuildExtParam(final ProductDetailPageRequest request,
                                     final ExtParamBuilderRequest builderRequest) throws Exception {
        if (StringUtils.isBlank(request.getPageSource())) {
            return null;
        }
        Map<String, String> pageSource2OrderTrafficFlag = Lion.getMap(
                DZTGDETAIL_APPKEY,
                "com.sankuai.dzu.tpbase.dztgdetailweb.pagesource.to.order.trafficFlag",
                String.class, new HashMap<>()
        );
        String trafficFlag = pageSource2OrderTrafficFlag.get(request.getPageSource());
        if (StringUtils.isBlank(trafficFlag)) {
            return null;
        }
        return trafficFlag;
    }

}
