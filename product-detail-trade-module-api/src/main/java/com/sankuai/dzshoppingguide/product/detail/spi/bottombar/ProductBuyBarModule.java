package com.sankuai.dzshoppingguide.product.detail.spi.bottombar;

import com.google.common.collect.Lists;
import com.sankuai.dz.product.detail.gateway.spi.vo.AbstractModuleVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.banner.BottomBarTopBannerVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.ProductBottomBarVO;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Arrays;
import java.util.List;

/**
 * @Author: guangyujie
 * @Date: 2025/3/5 18:24
 * https://km.sankuai.com/collabpage/2706045304
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class ProductBuyBarModule extends AbstractModuleVO {

    /**
     * 购买按钮区域
     */
    private ProductBottomBarVO bottomBar;

    /**
     * 上方小黄条，可能有多个，目前暂时只有一个
     */
    private List<BottomBarTopBannerVO> topBannerList;

    public static ProductBuyBarModule buildWithoutBanner(ProductBottomBarVO bottomBar) {
        ProductBuyBarModule productBuyBarModule = new ProductBuyBarModule();
        productBuyBarModule.setBottomBar(bottomBar);
        return productBuyBarModule;
    }

    public static ProductBuyBarModule buildWithBanner(ProductBottomBarVO bottomBar, BottomBarTopBannerVO topBanner) {
        ProductBuyBarModule productBuyBarModule = new ProductBuyBarModule();
        productBuyBarModule.setBottomBar(bottomBar);
        if (topBanner != null) {
            productBuyBarModule.setTopBannerList(Lists.newArrayList(topBanner));
        }
        return productBuyBarModule;
    }

    public static ProductBuyBarModule buildWithMultiBanner(ProductBottomBarVO bottomBar, BottomBarTopBannerVO... topBanner) {
        ProductBuyBarModule productBuyBarModule = new ProductBuyBarModule();
        productBuyBarModule.setBottomBar(bottomBar);
        if (topBanner != null && topBanner.length > 0) {
            productBuyBarModule.setTopBannerList(Arrays.asList(topBanner));
        }
        return productBuyBarModule;
    }

    @Override
    public String getModuleKey() {
        return ModuleKeyConstants.BOTTOM_BAR;
    }

}
