package com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.ext.param.handler;

import com.sankuai.dz.product.detail.RequestCustomParamEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.enums.OrderPageExtParamEnums;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.ext.param.BaseSingleExtParamBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.ext.param.request.ExtParamBuilderRequest;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * @Author: guangyujie
 * @Date: 2025/4/9 13:26
 */
@Component
public class OfflineCodeExtParamBuilder extends BaseSingleExtParamBuilder {
    @Override
    protected OrderPageExtParamEnums buildKey() {
        return OrderPageExtParamEnums.offlinecode;
    }

    @Override
    protected String doBuildExtParam(ProductDetailPageRequest request, ExtParamBuilderRequest builderRequest) throws Exception {
        String offlineCode = request.getCustomParam(RequestCustomParamEnum.offlinecode);
        if (StringUtils.isBlank(offlineCode)) {
            return null;
        }
        return offlineCode;
    }

}
