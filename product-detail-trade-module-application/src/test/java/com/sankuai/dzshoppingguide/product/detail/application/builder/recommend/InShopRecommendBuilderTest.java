package com.sankuai.dzshoppingguide.product.detail.application.builder.recommend;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dzshoppingguide.product.detail.spi.recommend.vo.RelatedDealPBO;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class InShopRecommendBuilderTest {

    @InjectMocks
    private InShopRecommendBuilder inShopRecommendBuilder;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    /**
     * Test empty input list returns empty result
     */
    @Test
    public void testGetInShopDealsEmptyInput() throws Throwable {
        // arrange
        List<Long> input = Collections.emptyList();
        // act
        List<RelatedDealPBO> result = inShopRecommendBuilder.getInShopDeals(input);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test null input list - should expect NullPointerException since the method doesn't handle null input
     */
    @Test
    public void testGetInShopDealsNullInput() throws Throwable {
        // arrange
        List<Long> input = null;
        // act & assert
        assertThrows(NullPointerException.class, () -> inShopRecommendBuilder.getInShopDeals(input));
    }

    /**
     * Test list with all valid deal group IDs returns all expected results
     */
    @Test
    public void testGetInShopDealsAllValidDeals() throws Throwable {
        // arrange
        List<Long> input = Arrays.asList(1L, 2L, 3L);
        RelatedDealPBO mockPbo1 = new RelatedDealPBO();
        RelatedDealPBO mockPbo2 = new RelatedDealPBO();
        RelatedDealPBO mockPbo3 = new RelatedDealPBO();
        InShopRecommendBuilder spyBuilder = spy(inShopRecommendBuilder);
        doReturn(mockPbo1).when(spyBuilder).buildDealGroupPBO(1L);
        doReturn(mockPbo2).when(spyBuilder).buildDealGroupPBO(2L);
        doReturn(mockPbo3).when(spyBuilder).buildDealGroupPBO(3L);
        // act
        List<RelatedDealPBO> result = spyBuilder.getInShopDeals(input);
        // assert
        assertNotNull(result);
        assertEquals(3, result.size());
        assertSame(mockPbo1, result.get(0));
        assertSame(mockPbo2, result.get(1));
        assertSame(mockPbo3, result.get(2));
    }

    /**
     * Test list with all invalid deal group IDs returns empty result
     */
    @Test
    public void testGetInShopDealsAllInvalidDeals() throws Throwable {
        // arrange
        List<Long> input = Arrays.asList(1L, 2L, 3L);
        InShopRecommendBuilder spyBuilder = spy(inShopRecommendBuilder);
        doReturn(null).when(spyBuilder).buildDealGroupPBO(anyLong());
        // act
        List<RelatedDealPBO> result = spyBuilder.getInShopDeals(input);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test mixed list with both valid and invalid deal group IDs returns only valid results
     */
    @Test
    public void testGetInShopDealsMixedValidAndInvalidDeals() throws Throwable {
        // arrange
        List<Long> input = Arrays.asList(1L, 2L, 3L);
        RelatedDealPBO mockPbo1 = new RelatedDealPBO();
        RelatedDealPBO mockPbo3 = new RelatedDealPBO();
        InShopRecommendBuilder spyBuilder = spy(inShopRecommendBuilder);
        doReturn(mockPbo1).when(spyBuilder).buildDealGroupPBO(1L);
        doReturn(null).when(spyBuilder).buildDealGroupPBO(2L);
        doReturn(mockPbo3).when(spyBuilder).buildDealGroupPBO(3L);
        // act
        List<RelatedDealPBO> result = spyBuilder.getInShopDeals(input);
        // assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertSame(mockPbo1, result.get(0));
        assertSame(mockPbo3, result.get(1));
    }

    /**
     * Test single element list with valid deal group ID returns single result
     */
    @Test
    public void testGetInShopDealsSingleValidDeal() throws Throwable {
        // arrange
        List<Long> input = Collections.singletonList(1L);
        RelatedDealPBO mockPbo = new RelatedDealPBO();
        InShopRecommendBuilder spyBuilder = spy(inShopRecommendBuilder);
        doReturn(mockPbo).when(spyBuilder).buildDealGroupPBO(1L);
        // act
        List<RelatedDealPBO> result = spyBuilder.getInShopDeals(input);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertSame(mockPbo, result.get(0));
    }

    /**
     * Test single element list with invalid deal group ID returns empty result
     */
    @Test
    public void testGetInShopDealsSingleInvalidDeal() throws Throwable {
        // arrange
        List<Long> input = Collections.singletonList(1L);
        InShopRecommendBuilder spyBuilder = spy(inShopRecommendBuilder);
        doReturn(null).when(spyBuilder).buildDealGroupPBO(1L);
        // act
        List<RelatedDealPBO> result = spyBuilder.getInShopDeals(input);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }
}
