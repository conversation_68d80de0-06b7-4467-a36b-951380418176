package com.sankuai.dzshoppingguide.product.detail.domain.utils;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/2/6 15:45
 */
public interface LionConstants {

    String DZTGDETAIL_APPKEY = "com.sankuai.dzu.tpbase.dztgdetailweb";
    String COMMON_MODULE_APPKEY = "com.sankuai.dzshoppingguide.detail.commonmodule";
    String TRADE_MODULE_APPKEY = "com.sankuai.dzshoppingguide.detail.trademodule";

    /**
     * id映射缓存刷新时间配置
     */
    String ID_MAPPER_CACHE_REFRESH_TIME_CONFIG = "id.mapper.cache.refresh.time.config";

    /**
     * id映射缓存过期时间配置
     */
    String ID_MAPPER_CACHE_EXPIRE_TIME_CONFIG = "id.mapper.cache.expire.time.config";

    String CITYID_TRANSFORM_OPEN_PLATFORM_KEY = "cityid.transform.open.platform.key";

    String NAV_BAR_ITEM_LIST = "com.sankuai.dzshoppingguide.detail.commonmodule.nav.bar.item.list";

    String COMMON_MODULE_APP_KEY = "com.sankuai.dzshoppingguide.detail.commonmodule";

    /**
     * 加价详情模块配置
     */
    String MARKUP_MODULE_ITEM_LIST = "markup.module.item.list";

    /**
     * 团详pageSource和报价source的映射
     */
    String PAGE_SOURCE_TO_PRICE_SOURCE = "com.sankuai.dzu.tpbase.dztgdetailweb.pagesource.to.price.source";

    /**
     * 猜喜渠道是否使用新渠道来源
     */
    String USE_NEW_SOURCE_FOR_CAIXI_SWITCH = "use.new.source.for.caixi.switch";

    /**
     * 是否开启特团拼团
     */
    String COST_EFFECTIVE_PIN_TUAN_ENABLE = "com.sankuai.dzu.tpbase.dztgdetailweb.cost.effective.pin.tuan.enbale";

    /**
     * 点评APP神会员生效最低mrn版本
     */
    String MAGICAL_DP_MRN_MIN_VERSION = "dp.magical.member.valid.min.version";

    /**
     * 价格查询策略规则
     */
    String QUERY_PRICE_STRATEGY_RULES = "query.price.strategy.rule";

    /**
     * 商家会员优惠，白名单类目
     */
    String MEMBER_PRICE_CATEGORY_IDS = "com.sankuai.dzu.tpbase.dztgdetailweb.memberprice.category.ids";

    /**
     * 比价助手分类目实验配置
     */
    String COMPARE_PRICE_ASSISTANT_CATEGORY_AB_TEST_CONFIG = "compare.price.assistant.category.ab.test.config";

    /**
     * 特团“拼团规则”配置，图标+文案
     */
    String PIN_TUAN_RULE = "com.sankuai.dzu.tpbase.dztgdetailweb.pin.tuan.rule";

    /**
     * 指定渠道分享配置
     */
    String CUSTOM_PAGESOURCE_SHARE_CONFIG = "com.sankuai.dzu.tpbase.dztgdetailweb.custom.pagesource.share.config";

     /**
     * N选1政府消费券MRN版本控制
     */
    String N_SELECT_ONE_GOVERNMENT_COUPON_MRN_VERSION = "n.select.one.government.coupon.mrn.version";

    String ATMOSPHERE_RESOURCE_LOCATION_ID = "atmosphere.resource.location.id";

    /**
     * 底部推荐参数配置
     */
    String RELATED_RECOMMEND_CONFIG_MAP = "related.recommend.config.map";

    //多买多省搭售展示条目个数
    String BUY_MORE_SAVE_MORE_SHOW_ITEM_SIZE = "com.sankuai.dzu.tpbase.dztgdetailweb.buy.more.save.more.show.item.size";

    // 多买多省文案map
    String BUY_MORE_SAVE_MORE_TEXT_MAP = "com.sankuai.dzu.tpbase.dztgdetailweb.buy.more.save.more.text.map";

    /**
     * 提单新跳链服务手动降级开关
     * true-降级 false-不降级
     */
    String TRADE_URL_V2_INTERFACE_DEGRADE = "trade.url.v2.interface.degrade";

    /**
     * 可复购货架配置
     */
    String REPURCHASE_CONFIG = "com.sankuai.dzu.tpbase.dztgdetailweb.repurchase.config";

    /**
     * 综团比价AB实验开关
     */
    String COMPARE_PRICE_AB_TEST_SWITCH = "compare.price.ab.test.switch";

    /**
     * 综团比价门店黑名单，此黑名单门店不展示查价比价入口
     */
    String COMPARE_PRICE_SHOP_BLACK_LIST = "compare.price.shop.blacklist";

    /**
     * 团购次卡先用后付团单二级分类白名单
     */
    String PRODUCT_CREDIT_PAY_CATEGORY = "com.sankuai.dzu.tpbase.dztgdetailweb.product.credit.pay.category";

    String ONE_PRODUCT_MULTI_SWITCH = "one.product.multi.switch";

    /**
     * 神券强化感知 购券包icon配置
     */
    String MAGIC_COUPON_PROMOTION_BAR_PACKAGE_ICON_CONFIG = "magic.coupon.promotion.bar.package.icon.config";

    /**
     * 多买多省白名单渠道
     */
    String BUY_MORE_TRAFFIC_FLAG_WHITE_LIST = "buy.more.source.white.list";

    /**
     * 团详pageSource和交易侧trafficFlag映射
     */
    String PAGE_SOURCE_TO_ORDER_TRAFFIC_FLAG = "com.sankuai.dzu.tpbase.dztgdetailweb.pagesource.to.order.trafficFlag";

}