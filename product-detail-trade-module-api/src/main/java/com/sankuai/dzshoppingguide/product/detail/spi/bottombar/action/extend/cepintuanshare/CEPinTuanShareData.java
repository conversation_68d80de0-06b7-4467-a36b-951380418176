package com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.extend.cepintuanshare;

import lombok.Data;

import java.io.Serializable;

/**
 * @Author: guangyujie
 * @Date: 2025/4/1 15:51
 */
@Data
public class CEPinTuanShareData implements Serializable {


    /**
     * 跳链url
     */
    private String jumpUrl;

    /**
     * 标题
     */
    private String title;

    /**
     * 背景图片
     */
    private String background;

    /**
     * 商品图片
     */
    private String dealImage;

    /**
     * 分享模板id
     */
    private Integer templateId;

    /**
     * 特团来源场景
     */
    private Integer sceneType;

    /**
     * 拼团ID
     */
    private String pintuanActivityId;

    /**
     * 分享token， 同shareToken
     */
    private String orderGroupId;

    /**
     * 到手价
     */
    private String finalPrice;

    /**
     * 优惠模块的最终优惠减后价
     */
    private String promoPrice;

    /**
     * 门市价折扣 = 到手价 / 门市价
     */
    private String marketPromoDiscount;

    /**
     * 门市价
     */
    private String marketPrice;

}
