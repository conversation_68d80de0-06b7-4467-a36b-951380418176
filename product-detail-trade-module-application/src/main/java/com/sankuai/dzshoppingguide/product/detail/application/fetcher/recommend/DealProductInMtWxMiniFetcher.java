package com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommend;

import com.dianping.deal.shop.dto.DealGroupShop;
import com.dianping.frog.sdk.util.CollectionUtils;
import com.google.common.collect.Maps;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.cityId.CityIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.cityId.CityIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.shopid.ShopIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.shopid.ShopIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommend.dto.DealMapperReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommend.dto.DealProductInMtWxMiniReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
import com.sankuai.dztheme.deal.req.DealProductRequest;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * @Author: zhangyuan103
 * @Date: 2025/5/26
 * @Description 获取美团小程序跳链
 */
@Fetcher(previousLayerDependencies = {CityIdMapperFetcher.class, RecommendDealMapperFetcher.class, ShopIdMapperFetcher.class})
@Slf4j
public class DealProductInMtWxMiniFetcher extends NormalFetcherContext<DealProductInMtWxMiniReturnValue> {
    @Resource
    CompositeAtomService compositeAtomService;

    /**
     * 推荐小程序跳链-计划ID
     */
    private static final String DEFAULT_PLAN_ID = "10002669";

    @Override
    protected CompletableFuture<DealProductInMtWxMiniReturnValue> doFetch() throws Exception {
        DealMapperReturnValue dealMapperReturnValue = getDependencyResult(RecommendDealMapperFetcher.class);
        Map<Long, DealGroupShop> dpDealGroupShopMap = Optional.ofNullable(dealMapperReturnValue).map(DealMapperReturnValue::getDpDealGroupShopMap).orElse(null);
        if (request.getClientTypeEnum().equals(ClientTypeEnum.MT_XCX) || CollectionUtils.isEmpty(dpDealGroupShopMap)) {
            return CompletableFuture.completedFuture(null);
        }
        return compositeAtomService.queryDealProduct(buildDealProductRequest(dpDealGroupShopMap)).thenApply(DealProductInMtWxMiniReturnValue::new);
    }

    /**
     * 构建DealProductRequest请求对象
     */
    private DealProductRequest buildDealProductRequest(Map<Long, DealGroupShop> dealGroupShopMap) {
        boolean isMt = request.getClientTypeEnum().isMtClientType();
        ShopIdMapper shopIdMapper = getDependencyResult(ShopIdMapperFetcher.class);
        long shopId = request.getPoiId();
        long mtShopId = shopIdMapper.getMtBestShopId();
        long dpShopId = shopIdMapper.getDpBestShopId();

        List<Long> dealIdLs = new ArrayList<>();
        // 构建dealId到shopId的映射
        Map<Long, Long> dealId2ShopIdForLong = Maps.newHashMap();
        for (Map.Entry<Long, DealGroupShop> entry : dealGroupShopMap.entrySet()) {
            Long dealId = entry.getKey();
            dealIdLs.add(dealId);
            DealGroupShop dealGroupShop = entry.getValue();
            if (dealGroupShop == null) {
                continue;
            }
            Long longShopId = dealGroupShop.getLongShopId();
            dealId2ShopIdForLong.put(dealId, longShopId);
        }
        List<Integer> dealIds = dealIdLs.stream().map(Long::intValue).collect(Collectors.toList());
        DealProductRequest dealProductRequest = new DealProductRequest();
        dealProductRequest.setProductIds(dealIds);
        dealProductRequest.setPlanId(DEFAULT_PLAN_ID);

        Map<String, Object> extParams = Maps.newHashMap();
        extParams.put("deviceId", request.getShepherdGatewayParam().getDeviceId());
        extParams.put("cityId", request.getCityId());
        // 202表示美团微信小程序 参考： com.sankuai.dztheme.dealgroup 项目 VCClientTypeEnum
        extParams.put("clientType", 202);
        CityIdMapper cityIdMapper = getDependencyResult(CityIdMapperFetcher.class);
        extParams.put("mtCityId", cityIdMapper.getMtCityId());
        extParams.put("dpCityId", cityIdMapper.getDpCityId());
        extParams.put("mtUserId", request.getMtUserId());
        extParams.put("dpUserId", request.getDpUserId());
        extParams.put("shopIdForLong", shopId);
        extParams.put("mtShopId", mtShopId);
        extParams.put("mtShopIdForLong", mtShopId);
        extParams.put("dpShopIdForLong", dpShopId);
        extParams.put("platform", isMt ? 2 : 1);
        extParams.put("dealIds", dealIds);
        extParams.put("dealId2ShopIdForLong", dealId2ShopIdForLong);
        // 商户店铺跳链 小程序标识
        extParams.put("miniProgramShop", true);
        dealProductRequest.setExtParams(extParams);
        return dealProductRequest;
    }

}
