package com.sankuai.dzshoppingguide.product.detail.application.builder.navbar.handler;

import com.sankuai.dealuser.price.display.api.enums.PromoTypeEnum;
import com.sankuai.dealuser.price.display.api.enums.PromotionExplanatoryTagEnum;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import com.sankuai.dz.product.detail.RequestCustomParamEnum;
import com.sankuai.dz.product.detail.RequestSourceEnum;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.constants.Constants;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.shopid.ShopIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.best.shop.ShopInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.dto.ProductPriceReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.utils.AppImageSize;
import com.sankuai.dzshoppingguide.product.detail.application.utils.ImageHelper;
import com.sankuai.dzshoppingguide.product.detail.application.utils.ShareIdUtils;
import com.sankuai.dzshoppingguide.product.detail.application.utils.ShareUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.ShareModuleMiniProgramConfig;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * @Author: <EMAIL>
 * @Date: 2025/3/5
 */
@Component(Constants.NAV_BAR_DEAL_PRODUCT_HANDLER)
public class NavBarDealProductHandler implements NavBarProductTypeHandler {
    @Override
    public String buildImage(String image) {
        int[] imageSize = convertWidthHeight(AppImageSize.MINI_PROGRAM_SHARE.width, AppImageSize.MINI_PROGRAM_SHARE.height);
        if (imageSize != null) {
            return ImageHelper.formatWithoutWatermark(image, imageSize[0], imageSize[1], true);
        } else {
            return ImageHelper.formatWithoutWatermark(image, AppImageSize.MEDIUM.width, AppImageSize.MEDIUM.height, true);
        }
    }

    @Override
    public ShareModuleMiniProgramConfig buildMiniProgramConfig(ProductDetailPageRequest request, ShopIdMapper idMapper) {
        ShareModuleMiniProgramConfig config = new ShareModuleMiniProgramConfig();
        switch (request.getClientTypeEnum()) {
            case MT_APP:
            case MT_WX:
                config.setMiniProgramId("gh_870576f3c6f9");
                config.setPath(String.format("/gc/pages/deal/dealdetail/dealdetail?dealid=%s&shopid=%s&nsrc=2", request.getProductId(), Objects.nonNull(idMapper) ? idMapper.getMtBestShopId() : 0L));
                break;
            case DP_APP:
            case DP_WX:
                config.setMiniProgramId("gh_bc5b635c05c4");
                config.setPath(String.format("/packages/dpmapp-gc-dealdetail/pages/deal/dealdetail/dealdetail?dealid=%s&shopid=%s&nsrc=2&share_id=${shareId}&source=%s",
                        request.getProductId(),
                        Objects.nonNull(idMapper) ? idMapper.getMtBestShopId() : 0L,
                        ShareIdUtils.getShareId(),
                        request.getPageSource()));
                break;
        }
        return config;
    }

    @Override
    public String buildShareUrl(ProductDetailPageRequest request, ShopIdMapper idMapper, ShopInfo shopInfo) {
        switch (request.getClientTypeEnum()) {
            case MT_APP:
            case MT_WX:
                String platform = StringUtils.EMPTY, utmMedium = StringUtils.EMPTY;
                String utm = ShareUtils.shareIdGenerator(request.getCustomParam(RequestCustomParamEnum.uuid));
                switch (request.getMobileOSType()) {
                    case IOS:
                        platform = "ios";
                        utmMedium = "iosweb";
                        break;
                    case ANDROID:
                        platform = "android";
                        utmMedium = "androidweb";
                        break;
                }
                String url = "https://www.meituan.com/deal/" + request.getProductId()
                        + ".html?utm_term=A$"
                        + platform + "BgroupC" + request.getShepherdGatewayParam().getAppVersion() + "DEorderG"
                        + utm + "&utm_source=appshare&utm_medium="
                        + utmMedium + "&share_id=" + ShareIdUtils.getShareId() + "&source="
                        + request.getPageSource();
                return url;
            case DP_APP:
            case DP_WX:
                DpPoiDTO dpPoiDTO = shopInfo.getDpPoiDTO();
                String shopUuid = Objects.nonNull(dpPoiDTO) ? dpPoiDTO.getUuid() : StringUtils.EMPTY;
                switch (request.getMobileOSType()) {
                    case IOS:
                        return "http://m.dianping.com/tuan/deal/" + request.getProductId()
                                + "?utm_source=appshare&shopId="
                                + request.getPoiId() + "&shopUuid=" + shopUuid
                                + "&share_id=" + ShareIdUtils.getShareId() + "&source="
                                + request.getPageSource();
                    case ANDROID:
                        return "http://m.dianping.com/tuan/deal/" + request.getProductId()
                                + "?utm_source=appshare&share_id=" + ShareIdUtils.getShareId() + "&source="
                                + request.getPageSource();
                }
                break;
        }
        return null;
    }

    @Override
    public String buildTitle(ProductDetailPageRequest request, String title, ProductPriceReturnValue costEffectivePrice) {
        switch (request.getClientTypeEnum()) {
            case MT_APP:
            case MT_WX:
                // 特团拼团场景
                if (RequestSourceEnum.COST_EFFECTIVE.equals(request.getPageSource())
                        && Objects.nonNull(costEffectivePrice) && Objects.nonNull(costEffectivePrice.getPriceDisplayDTO()) && (ClientTypeEnum.MT_APP.getCode() == request.getClientType() || ClientTypeEnum.MT_WX.getCode() == request.getClientType())) {
                    List<PromoDTO> promoDTOS = costEffectivePrice.getPriceDisplayDTO().getUsedPromos();
                    PromoDTO promoDTO = CollectionUtils.emptyIfNull(promoDTOS).stream().filter(usedPromo -> matchPinTuan(usedPromo)).findFirst().orElse(null);
                    if (Objects.nonNull(promoDTO)) {
                        return "我在拼团中，快来加入我的拼团吧";
                    }
                }
                return StringUtils.isBlank(title) ? "分享个团购给你" : title;
            case DP_APP:
            case DP_WX:
                return title;
        }
        return null;
    }

    @Override
    public String buildDesc(ProductDetailPageRequest request, ProductBaseInfo baseInfo) {
        switch (request.getClientTypeEnum()) {
            case MT_APP:
            case MT_WX:
                String price = Objects.isNull(baseInfo.getPrice()) || Objects.isNull(baseInfo.getPrice().getSalePrice()) ? "0.0" : baseInfo.getPrice().getSalePrice();
                return "仅售" + price + "元";
            default:
                return StringUtils.EMPTY;
        }
    }

    private int[] convertWidthHeight(int width, int height) {
        if (width <= 0 || height <= 0) {
            return null;
        }
        int newWidth = (int) (height * 5.0 / 4.0);
        int newHeight = height;
        if (newWidth > width) {
            newWidth = width;
            newHeight = (int) (width * 4.0 / 5.0);
        }
        return new int[]{newWidth, newHeight};
    }

    private boolean matchPinTuan(PromoDTO usedPromo) {
        if (CollectionUtils.isEmpty(usedPromo.getPromotionExplanatoryTags())) {
            return false;
        }
        return PromoTypeEnum.NORMAL_PROMO.getType() == usedPromo.getIdentity().getPromoType() && usedPromo.getPromotionExplanatoryTags().contains(PromotionExplanatoryTagEnum.PIN_TUAN_DEDUCTION.getCode());
    }
}
