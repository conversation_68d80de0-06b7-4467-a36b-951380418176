package com.sankuai.dzshoppingguide.product.detail.application.fetcher.creditpay;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2025/6/12 19:59
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
public class ExposeResult extends FetcherReturnValueDTO {
    /**
     * 是否支持曝光
     */
    private boolean supportExpose;
}
