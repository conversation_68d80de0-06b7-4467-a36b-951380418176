package com.sankuai.dzshoppingguide.product.detail.application.builder.consult;

import static org.junit.jupiter.api.Assertions.*;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * Unit tests for DealDetailIMBuilder.buildOriginalOnlineConsult method
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("DealDetailIMBuilder Tests")
public class DealDetailIMBuilderTest {

    @InjectMocks
    private DealDetailIMBuilder dealDetailIMBuilder;

    /**
     * Test when input URL is null
     */
    @Test
    @DisplayName("Should return null when input URL is null")
    public void testBuildOriginalOnlineConsult_WhenUrlIsNull() {
        // arrange
        String imUrl = null;
        // act
        String result = dealDetailIMBuilder.buildOriginalOnlineConsult(imUrl);
        // assert
        assertNull(result, "Result should be null when input URL is null");
    }

    /**
     * Test when input URL is empty
     */
    @Test
    @DisplayName("Should decode empty string when input URL is empty")
    public void testBuildOriginalOnlineConsult_WhenUrlIsEmpty() {
        // arrange
        String imUrl = "";
        // act
        String result = dealDetailIMBuilder.buildOriginalOnlineConsult(imUrl);
        // assert
        assertEquals("", result, "Result should be empty string when input URL is empty");
    }

    /**
     * Test when input URL doesn't contain ?url= parameter
     */
    @Test
    @DisplayName("Should decode full URL when no ?url= parameter exists")
    public void testBuildOriginalOnlineConsult_WhenNoUrlParameter() {
        // arrange
        String imUrl = "https://example.com/path?param=value";
        // act
        String result = dealDetailIMBuilder.buildOriginalOnlineConsult(imUrl);
        // assert
        assertEquals("https://example.com/path?param=value", result, "Should return decoded full URL when no ?url= parameter exists");
    }

    /**
     * Test when input URL contains ?url= parameter
     */
    @Test
    @DisplayName("Should extract and decode URL after ?url= parameter")
    public void testBuildOriginalOnlineConsult_WhenHasUrlParameter() {
        // arrange
        String imUrl = "https://example.com/path?url=https%3A%2F%2Ftarget.com%2Fpage";
        // act
        String result = dealDetailIMBuilder.buildOriginalOnlineConsult(imUrl);
        // assert
        assertEquals("https://target.com/page", result, "Should return decoded URL part after ?url= parameter");
    }

    /**
     * Test when input URL contains multiple ?url= parameters
     */
    @Test
    @DisplayName("Should handle URL with multiple ?url= parameters")
    public void testBuildOriginalOnlineConsult_WhenMultipleUrlParameters() {
        // arrange
        String imUrl = "https://example.com/path?url=https%3A%2F%2Ftarget.com%2Fpage?url=second";
        // act
        String result = dealDetailIMBuilder.buildOriginalOnlineConsult(imUrl);
        // assert
        assertEquals("https://target.com/page?url=second", result, "Should handle multiple ?url= parameters correctly");
    }

    /**
     * Test when URL contains invalid encoding
     */
    @Test
    @DisplayName("Should return null when URL contains invalid encoding")
    public void testBuildOriginalOnlineConsult_WhenInvalidEncoding() {
        // arrange
        String imUrl = "https://example.com/path?url=%invalid";
        // act
        String result = dealDetailIMBuilder.buildOriginalOnlineConsult(imUrl);
        // assert
        assertNull(result, "Should return null when URL contains invalid encoding");
    }

    /**
     * Test when URL contains special characters
     */
    @Test
    @DisplayName("Should handle URL with special characters")
    public void testBuildOriginalOnlineConsult_WhenSpecialCharacters() {
        // arrange
        String imUrl = "https://example.com/path?url=https%3A%2F%2Ftarget.com%2Fpage%3Fid%3D123%26name%3Dtest%2B1";
        // act
        String result = dealDetailIMBuilder.buildOriginalOnlineConsult(imUrl);
        // assert
        assertEquals("https://target.com/page?id=123&name=test+1", result, "Should correctly decode URL with special characters");
    }
}
