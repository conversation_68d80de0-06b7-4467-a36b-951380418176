package com.sankuai.dzshoppingguide.product.detail.application.utils;

import static org.junit.Assert.*;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.cepinpool.dto.CostEffectivePinTuan;
import org.junit.Test;

public class CostEffectivePinTuanUtilsTest {

    /**
     * 测试 activePinTuan 方法，当 pinTuanOpened 和 activePinTuan 都为 true 时，应返回 true
     */
    @Test
    public void testActivePinTuan_BothTrue() {
        // arrange
        CostEffectivePinTuan costEffectivePinTuan = new CostEffectivePinTuan();
        costEffectivePinTuan.setCePinTuanScene(true);
        costEffectivePinTuan.setPinTuanOpened(true);
        costEffectivePinTuan.setActivePinTuan(true);
        // act
        boolean result = CostEffectivePinTuanUtils.activePinTuan(costEffectivePinTuan);
        // assert
        assertTrue(result);
    }

    /**
     * 测试 activePinTuan 方法，当 pinTuanOpened 为 true，activePinTuan 为 false 时，应返回 false
     */
    @Test
    public void testActivePinTuan_ActivePinTuanFalse() {
        // arrange
        CostEffectivePinTuan costEffectivePinTuan = new CostEffectivePinTuan();
        costEffectivePinTuan.setCePinTuanScene(true);
        costEffectivePinTuan.setPinTuanOpened(true);
        costEffectivePinTuan.setActivePinTuan(false);
        // act
        boolean result = CostEffectivePinTuanUtils.activePinTuan(costEffectivePinTuan);
        // assert
        assertFalse(result);
    }

    /**
     * 测试 activePinTuan 方法，当 pinTuanOpened 为 false 时，应返回 false
     */
    @Test
    public void testActivePinTuan_PinTuanOpenedFalse() {
        // arrange
        CostEffectivePinTuan costEffectivePinTuan = new CostEffectivePinTuan();
        costEffectivePinTuan.setCePinTuanScene(true);
        costEffectivePinTuan.setPinTuanOpened(false);
        // act
        boolean result = CostEffectivePinTuanUtils.activePinTuan(costEffectivePinTuan);
        // assert
        assertFalse(result);
    }
}
