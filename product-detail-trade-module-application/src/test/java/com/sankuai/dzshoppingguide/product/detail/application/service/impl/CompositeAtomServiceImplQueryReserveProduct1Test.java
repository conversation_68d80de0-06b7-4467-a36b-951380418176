package com.sankuai.dzshoppingguide.product.detail.application.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.Mockito.*;
import com.dianping.pigeon.remoting.common.domain.CallMethod;
import com.dianping.pigeon.remoting.common.exception.RpcException;
import com.dianping.pigeon.remoting.invoker.concurrent.InvocationCallback;
import com.dianping.pigeon.remoting.invoker.util.InvokerHelper;
import com.meituan.inf.xmdlog.XMDLogFormat;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonClient;
import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
import com.sankuai.dzshoppingguide.product.detail.domain.utils.PigeonCallbackUtils;
import com.sankuai.dztheme.massagebook.theme.ReserveThemeQueryService;
import com.sankuai.dztheme.massagebook.theme.req.ReserveQueryRequest;
import com.sankuai.dztheme.massagebook.theme.res.ReserveQueryResponse;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;

@ExtendWith(MockitoExtension.class)
class CompositeAtomServiceImplQueryReserveProduct1Test {

    @Mock
    private ReserveThemeQueryService reserveThemeQueryService;

    @InjectMocks
    private CompositeAtomServiceImpl compositeAtomService;

    @BeforeEach
    void setUp() {
        InvokerHelper.clearCallback();
    }

    /**
     * 测试正常返回结果的情况
     */
    @Test
    void testQueryReserveProductSuccess() throws Throwable {
        // arrange
        ReserveQueryRequest request = new ReserveQueryRequest();
        ReserveQueryResponse expectedResponse = new ReserveQueryResponse();
        CountDownLatch latch = new CountDownLatch(1);
        doAnswer(invocation -> {
            InvocationCallback callback = InvokerHelper.getCallback();
            if (callback != null) {
                callback.onSuccess(expectedResponse);
            }
            latch.countDown();
            return null;
        }).when(reserveThemeQueryService).query(any(ReserveQueryRequest.class));
        // act
        CompletableFuture<ReserveQueryResponse> future = compositeAtomService.queryReserveProduct(request);
        // assert
        assertTrue(latch.await(5, TimeUnit.SECONDS), "Callback was not executed within timeout");
        assertNotNull(future);
        ReserveQueryResponse actualResponse = future.get(5, TimeUnit.SECONDS);
        assertEquals(expectedResponse, actualResponse);
        verify(reserveThemeQueryService, times(1)).query(request);
    }

    /**
     * 测试服务抛出异常的情况
     */
    @Test
    void testQueryReserveProductException() throws Throwable {
        // arrange
        ReserveQueryRequest request = new ReserveQueryRequest();
        RuntimeException expectedException = new RuntimeException("Service error");
        CountDownLatch latch = new CountDownLatch(1);
        doAnswer(invocation -> {
            InvocationCallback callback = InvokerHelper.getCallback();
            if (callback != null) {
                callback.onFailure(expectedException);
            }
            latch.countDown();
            return null;
        }).when(reserveThemeQueryService).query(any(ReserveQueryRequest.class));
        // act
        CompletableFuture<ReserveQueryResponse> future = compositeAtomService.queryReserveProduct(request);
        // assert
        assertTrue(latch.await(5, TimeUnit.SECONDS), "Callback was not executed within timeout");
        assertNotNull(future);
        try {
            future.get(5, TimeUnit.SECONDS);
            fail("Expected exception was not thrown");
        } catch (ExecutionException e) {
            assertEquals(expectedException, e.getCause());
        }
        verify(reserveThemeQueryService, times(1)).query(request);
    }

    /**
     * 测试请求参数为null的情况
     */
    @Test
    void testQueryReserveProductNullRequest() throws Throwable {
        // arrange
        CountDownLatch latch = new CountDownLatch(1);
        doAnswer(invocation -> {
            InvocationCallback callback = InvokerHelper.getCallback();
            if (callback != null) {
                callback.onSuccess(null);
            }
            latch.countDown();
            return null;
        }).when(reserveThemeQueryService).query(isNull());
        // act
        CompletableFuture<ReserveQueryResponse> future = compositeAtomService.queryReserveProduct(null);
        // assert
        assertTrue(latch.await(5, TimeUnit.SECONDS), "Callback was not executed within timeout");
        assertNotNull(future);
        ReserveQueryResponse response = future.get(5, TimeUnit.SECONDS);
        assertNull(response);
        verify(reserveThemeQueryService, times(1)).query(null);
    }

    /**
     * 测试回调处理异常的情况
     */
    @Test
    void testQueryReserveProductCallbackException() throws Throwable {
        // arrange
        ReserveQueryRequest request = new ReserveQueryRequest();
        RuntimeException callbackException = new RuntimeException("Callback error");
        CountDownLatch latch = new CountDownLatch(1);
        doAnswer(invocation -> {
            InvocationCallback callback = InvokerHelper.getCallback();
            if (callback != null) {
                callback.onFailure(callbackException);
            }
            latch.countDown();
            return null;
        }).when(reserveThemeQueryService).query(any(ReserveQueryRequest.class));
        // act
        CompletableFuture<ReserveQueryResponse> future = compositeAtomService.queryReserveProduct(request);
        // assert
        assertTrue(latch.await(5, TimeUnit.SECONDS), "Callback was not executed within timeout");
        assertNotNull(future);
        try {
            future.get(5, TimeUnit.SECONDS);
            fail("Expected exception was not thrown");
        } catch (ExecutionException e) {
            assertEquals(callbackException, e.getCause());
        }
        verify(reserveThemeQueryService, times(1)).query(request);
    }
}
