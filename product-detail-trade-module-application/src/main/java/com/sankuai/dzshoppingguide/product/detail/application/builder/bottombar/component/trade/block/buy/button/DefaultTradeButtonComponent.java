package com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.component.trade.block.buy.button;

import com.alibaba.fastjson.JSON;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.component.trade.block.utils.TradeButtonBuildUtils;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.exception.TradeButtonBuildFatalException;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sale.info.enums.ProductSaleStatusEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.enums.OpenTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.extend.buy.BuyActionVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.component.trade.button.StandardTradeButtonVO;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Nullable;

import static com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.constant.ButtonStyleConstant.STANDARD_TRADE_BUTTON_STYLE;

/**
 * @Author: guangyujie
 * @Date: 2025/3/17 15:18
 */
@Slf4j
public class DefaultTradeButtonComponent {

    public static StandardTradeButtonVO build(final ProductDetailPageRequest request,
                                              @Nullable final String orderPageUrl) {
        try {
            return TradeButtonBuildUtils.buildTradeButtonVO(
                    ProductSaleStatusEnum.ONLINE,
                    "团购价",
                    "立即抢购",
                    STANDARD_TRADE_BUTTON_STYLE,
                    new BuyActionVO(OpenTypeEnum.redirect, orderPageUrl, null)
            );
        } catch (Throwable throwable) {
            log.error("DefaultTradeButtonComponent.build,request:{}",
                    JSON.toJSONString(request), new TradeButtonBuildFatalException(throwable));
            return null;
        }
    }

}
