package com.sankuai.dzshoppingguide.product.detail.application.service.promo;

import static org.junit.jupiter.api.Assertions.*;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import com.sankuai.dealuser.price.display.api.model.PromoIdentity;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.promo.vo.DealBestPromoDetail;
import com.sankuai.dzshoppingguide.product.detail.spi.promo.vo.DealGift;
import com.sankuai.dzshoppingguide.product.detail.spi.promo.vo.PromoActivityInfoVO;
import com.sankuai.dzshoppingguide.product.detail.spi.promo.vo.PromoDetailModule;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class PromoDetailModuleBuilderServiceBuildPromoAbstractListTest {

    private final PromoDetailModuleBuilderService service = new PromoDetailModuleBuilderService();

    /**
     * Test when promoDetailModule is null
     */
    @Test
    public void testBuildPromoAbstractList_WithNullModule() throws Throwable {
        List<String> result = service.buildPromoAbstractList(null, ClientTypeEnum.MT_APP, null, null);
        assertNull(result);
    }

    /**
     * Test with empty bestPromoDetails and no normalPrice
     */
    @Test
    public void testBuildPromoAbstractList_EmptyBestPromoNoNormalPrice() throws Throwable {
        PromoDetailModule module = new PromoDetailModule();
        module.setBestPromoDetails(Collections.emptyList());
        List<String> result = service.buildPromoAbstractList(module, ClientTypeEnum.MT_APP, null, null);
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test with bestPromoDetails containing valid promo names
     */
    @Test
    public void testBuildPromoAbstractList_WithBestPromoDetails() throws Throwable {
        PromoDetailModule module = new PromoDetailModule();
        List<DealBestPromoDetail> bestPromos = new ArrayList<>();
        DealBestPromoDetail promo1 = new DealBestPromoDetail();
        promo1.setPromoName("会员优惠");
        bestPromos.add(promo1);
        DealBestPromoDetail promo2 = new DealBestPromoDetail();
        promo2.setPromoName("秒杀活动");
        bestPromos.add(promo2);
        module.setBestPromoDetails(bestPromos);
        List<String> result = service.buildPromoAbstractList(module, ClientTypeEnum.MT_APP, null, null);
        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.get(0).contains("会员"));
        assertTrue(result.get(1).contains("秒杀"));
    }

    /**
     * Test with bestPromoDetails containing national subsidy (should be filtered out)
     */
    @Test
    public void testBuildPromoAbstractList_WithNationalSubsidy() throws Throwable {
        PromoDetailModule module = new PromoDetailModule();
        List<DealBestPromoDetail> bestPromos = new ArrayList<>();
        DealBestPromoDetail promo1 = new DealBestPromoDetail();
        promo1.setPromoName("会员优惠");
        bestPromos.add(promo1);
        DealBestPromoDetail promo2 = new DealBestPromoDetail();
        promo2.setPromoName("国家补贴");
        promo2.setPromoTag("国家补贴");
        bestPromos.add(promo2);
        module.setBestPromoDetails(bestPromos);
        List<String> result = service.buildPromoAbstractList(module, ClientTypeEnum.MT_APP, null, null);
        assertNotNull(result);
        assertEquals(1, result.size());
        assertTrue(result.get(0).contains("会员优惠"));
    }

    /**
     * Test with empty bestPromoDetails but with normalPrice's usedPromos
     */
    @Test
    public void testBuildPromoAbstractList_WithNormalPriceUsedPromos() throws Throwable {
        PromoDetailModule module = new PromoDetailModule();
        module.setBestPromoDetails(Collections.emptyList());
        PriceDisplayDTO normalPrice = new PriceDisplayDTO();
        List<PromoDTO> usedPromos = new ArrayList<>();
        PromoDTO promo1 = new PromoDTO();
        PromoIdentity identity1 = new PromoIdentity(1, 1, "平台立减");
        promo1.setIdentity(identity1);
        promo1.setAmount(new java.math.BigDecimal("10"));
        usedPromos.add(promo1);
        PromoDTO promo2 = new PromoDTO();
        PromoIdentity identity2 = new PromoIdentity(2, 2, "商家优惠");
        promo2.setIdentity(identity2);
        promo2.setAmount(new java.math.BigDecimal("5"));
        usedPromos.add(promo2);
        normalPrice.setUsedPromos(usedPromos);
        List<String> result = service.buildPromoAbstractList(module, ClientTypeEnum.MT_APP, normalPrice, null);
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("平台立减", result.get(0));
        assertEquals("商家优惠", result.get(1));
    }

    /**
     * Test with activities
     */
    @Test
    public void testBuildPromoAbstractList_WithActivities() throws Throwable {
        PromoDetailModule module = new PromoDetailModule();
        List<PromoActivityInfoVO> activities = new ArrayList<>();
        PromoActivityInfoVO activity1 = new PromoActivityInfoVO();
        activity1.setShortText("限时活动");
        activities.add(activity1);
        PromoActivityInfoVO activity2 = new PromoActivityInfoVO();
        activity2.setShortText("新客专享");
        activities.add(activity2);
        module.setPromoActivityList(activities);
        List<String> result = service.buildPromoAbstractList(module, ClientTypeEnum.MT_APP, null, null);
        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.contains("限时活动"));
        assertTrue(result.contains("新客专享"));
    }

    /**
     * Test with deal gifts for APP client
     */
    @Test
    public void testBuildPromoAbstractList_WithDealGiftsForApp() throws Throwable {
        PromoDetailModule module = new PromoDetailModule();
        List<DealGift> gifts = new ArrayList<>();
        DealGift gift1 = new DealGift();
        gift1.setTitle("小礼品");
        gifts.add(gift1);
        DealGift gift2 = new DealGift();
        gift2.setTitle("大礼包");
        gift2.setCustomerActivityPrefix("送");
        gifts.add(gift2);
        List<String> result = service.buildPromoAbstractList(module, ClientTypeEnum.MT_APP, null, gifts);
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("赠小礼品", result.get(0));
        assertEquals("送大礼包", result.get(1));
    }

    /**
     * Test with rich text promo name containing Unicode escapes
     */
    @Test
    public void testBuildPromoAbstractList_WithRichTextPromoName() throws Throwable {
        PromoDetailModule module = new PromoDetailModule();
        List<DealBestPromoDetail> bestPromos = new ArrayList<>();
        DealBestPromoDetail promo1 = new DealBestPromoDetail();
        // Unicode for "会员优惠"
        promo1.setPromoName("{\"text\":\"\\u4f1a\\u5458\\u4f18\\u60e0\"}");
        bestPromos.add(promo1);
        module.setBestPromoDetails(bestPromos);
        List<String> result = service.buildPromoAbstractList(module, ClientTypeEnum.MT_APP, null, null);
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("{\"text\":\"\\u4f1a\\u5458\\u4f18\\u60e0\"}", result.get(0));
    }

    /**
     * Test with invalid JSON in promo name
     */
    @Test
    public void testBuildPromoAbstractList_WithInvalidJsonPromoName() throws Throwable {
        PromoDetailModule module = new PromoDetailModule();
        List<DealBestPromoDetail> bestPromos = new ArrayList<>();
        DealBestPromoDetail promo1 = new DealBestPromoDetail();
        promo1.setPromoName("{invalid json}");
        bestPromos.add(promo1);
        module.setBestPromoDetails(bestPromos);
        List<String> result = service.buildPromoAbstractList(module, ClientTypeEnum.MT_APP, null, null);
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("{invalid json}", result.get(0));
    }

    /**
     * Test with member promo in APP (should be highlighted)
     */
    @Test
    public void testBuildPromoAbstractList_WithMemberPromoInApp() throws Throwable {
        PromoDetailModule module = new PromoDetailModule();
        List<DealBestPromoDetail> bestPromos = new ArrayList<>();
        DealBestPromoDetail promo1 = new DealBestPromoDetail();
        promo1.setPromoName("会员专享");
        bestPromos.add(promo1);
        module.setBestPromoDetails(bestPromos);
        List<String> result = service.buildPromoAbstractList(module, ClientTypeEnum.MT_APP, null, null);
        assertNotNull(result);
        assertEquals(1, result.size());
        assertTrue(result.get(0).contains("\"text\":\"会员专享\""));
        assertTrue(result.get(0).contains("\"textcolor\":\"#8e3c13\""));
    }

    /**
     * Test with member promo in non-APP (should not be highlighted)
     */
    @Test
    public void testBuildPromoAbstractList_WithMemberPromoInNonApp() throws Throwable {
        PromoDetailModule module = new PromoDetailModule();
        List<DealBestPromoDetail> bestPromos = new ArrayList<>();
        DealBestPromoDetail promo1 = new DealBestPromoDetail();
        promo1.setPromoName("会员专享");
        bestPromos.add(promo1);
        module.setBestPromoDetails(bestPromos);
        List<String> result = service.buildPromoAbstractList(module, ClientTypeEnum.MT_XCX, null, null);
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("会员专享", result.get(0));
    }

    /**
     * Test with all components (bestPromo, activities, gifts)
     */
    @Test
    public void testBuildPromoAbstractList_WithAllComponents() throws Throwable {
        PromoDetailModule module = new PromoDetailModule();
        // Best promos
        List<DealBestPromoDetail> bestPromos = new ArrayList<>();
        DealBestPromoDetail promo1 = new DealBestPromoDetail();
        promo1.setPromoName("会员优惠");
        bestPromos.add(promo1);
        module.setBestPromoDetails(bestPromos);
        // Activities
        List<PromoActivityInfoVO> activities = new ArrayList<>();
        PromoActivityInfoVO activity1 = new PromoActivityInfoVO();
        activity1.setShortText("限时活动");
        activities.add(activity1);
        module.setPromoActivityList(activities);
        // Gifts
        List<DealGift> gifts = new ArrayList<>();
        DealGift gift1 = new DealGift();
        gift1.setTitle("赠品");
        gifts.add(gift1);
        List<String> result = service.buildPromoAbstractList(module, ClientTypeEnum.MT_APP, null, gifts);
        assertNotNull(result);
        assertEquals(3, result.size());
        assertEquals("赠赠品", result.get(0));
        assertTrue(result.get(1).contains("会员优惠"));
        assertEquals("限时活动", result.get(2));
    }

    /**
     * Test with empty activities
     */
    @Test
    public void testBuildPromoAbstractList_WithEmptyActivities() throws Throwable {
        PromoDetailModule module = new PromoDetailModule();
        module.setPromoActivityList(Collections.emptyList());
        List<String> result = service.buildPromoAbstractList(module, ClientTypeEnum.MT_APP, null, null);
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test with null activities
     */
    @Test
    public void testBuildPromoAbstractList_WithNullActivities() throws Throwable {
        PromoDetailModule module = new PromoDetailModule();
        module.setPromoActivityList(null);
        List<String> result = service.buildPromoAbstractList(module, ClientTypeEnum.MT_APP, null, null);
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test with empty deal gifts
     */
    @Test
    public void testBuildPromoAbstractList_WithEmptyDealGifts() throws Throwable {
        PromoDetailModule module = new PromoDetailModule();
        List<String> result = service.buildPromoAbstractList(module, ClientTypeEnum.MT_APP, null, Collections.emptyList());
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test with null deal gifts
     */
    @Test
    public void testBuildPromoAbstractList_WithNullDealGifts() throws Throwable {
        PromoDetailModule module = new PromoDetailModule();
        List<String> result = service.buildPromoAbstractList(module, ClientTypeEnum.MT_APP, null, null);
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test with promo name containing sorting keywords
     */
    @Test
    public void testBuildPromoAbstractList_WithPromoNameSorting() throws Throwable {
        PromoDetailModule module = new PromoDetailModule();
        List<DealBestPromoDetail> bestPromos = new ArrayList<>();
        DealBestPromoDetail promo1 = new DealBestPromoDetail();
        promo1.setPromoName("特惠活动");
        bestPromos.add(promo1);
        DealBestPromoDetail promo2 = new DealBestPromoDetail();
        promo2.setPromoName("会员专享");
        bestPromos.add(promo2);
        DealBestPromoDetail promo3 = new DealBestPromoDetail();
        promo3.setPromoName("秒杀优惠");
        bestPromos.add(promo3);
        module.setBestPromoDetails(bestPromos);
        List<String> result = service.buildPromoAbstractList(module, ClientTypeEnum.MT_APP, null, null);
        assertNotNull(result);
        assertEquals(3, result.size());
        assertTrue(result.get(0).contains("会员"));
        assertTrue(result.get(1).contains("秒杀"));
        assertTrue(result.get(2).contains("特惠"));
    }
}
