package com.sankuai.dzshoppingguide.product.detail.spi.promo.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;

@Data
@MobileDo(id = 0xcbb2)
public class DealGift implements Serializable {
    /**
     * 使用规则
     */
    @MobileDo.MobileField(key = 0x420f)
    private String useRule;

    /**
    *
    */
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    /**
    *
    */
    @MobileDo.MobileField(key = 0xc857)
    private String productTag;

    /**
    *
    */
    @MobileDo.MobileField(key = 0x1fa2)
    private String thumbnail;

    /**
    * 活动说明
    */
    @MobileDo.MobileField(key = 0x12d8)
    private String timeDesc;

    private String validTimeDesc;

    /**
    *
    */
    @MobileDo.MobileField(key = 0xad6c)
    private String specificTag;

    /**
    * 份数
    */
    @MobileDo.MobileField(key = 0xf240)
    private int couponNum;

    /**
    * 活动Id
    */
    @MobileDo.MobileField(key = 0xe91)
    private long activityId;

    /**
    * 左上角icon浮标
    */
    @MobileDo.MobileField(key = 0xa5fd)
    private String leftIconTag;

    /**
    * 新客活动指定前缀
    */
    @MobileDo.MobileField(key = 0x359b)
    private String customerActivityPrefix;

}