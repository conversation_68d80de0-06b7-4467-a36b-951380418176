package com.sankuai.dzshoppingguide.product.detail.application.fetcher.tying;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TyingIdResult extends FetcherReturnValueDTO {
    private List<CombinationDealInfo> combinationDealInfoList;
    private Integer mainDealGroupId;
    private List<Integer> allDealGroupIds;
    private List<Integer> bindingDealGroupIds;
}
