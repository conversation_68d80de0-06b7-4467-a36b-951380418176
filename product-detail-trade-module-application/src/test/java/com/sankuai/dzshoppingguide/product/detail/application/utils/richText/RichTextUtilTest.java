package com.sankuai.dzshoppingguide.product.detail.application.utils.richText;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.google.common.collect.Lists;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * Test class for RichTextUtil.buildTextItem method
 */
@ExtendWith(MockitoExtension.class)
public class RichTextUtilTest {

    @Mock
    private RichText.TextItem mockTemplate;

    /**
     * Test buildTextItem with normal text and template
     */
    @Test
    public void testBuildTextItem_NormalCase() {
        // arrange
        String text = "Sample Text";
        RichText.TextItem template = new RichText.TextItem(14, "#FF000000", "#00FFFFFF", "Default", false, false);
        RichText.TextItem clonedTemplate = new RichText.TextItem(14, "#FF000000", "#00FFFFFF", "Default", false, false);
        when(mockTemplate.clone()).thenReturn(clonedTemplate);
        // act
        RichText.TextItem result = RichTextUtil.buildTextItem(text, mockTemplate);
        // assert
        assertNotNull(result);
        assertEquals(text, result.getText());
        assertEquals(14, result.getTextsize());
        assertEquals("#FF000000", result.getTextcolor());
        assertEquals("#00FFFFFF", result.getBackgroundcolor());
        assertEquals("Default", result.getTextstyle());
        assertFalse(result.isStrikethrough());
        assertFalse(result.isUnderline());
        verify(mockTemplate, times(1)).clone();
    }

    /**
     * Test buildTextItem with empty text
     */
    @Test
    public void testBuildTextItem_EmptyText() {
        // arrange
        String text = "";
        RichText.TextItem clonedTemplate = new RichText.TextItem(14, "#FF000000", "#00FFFFFF", "Default", false, false);
        when(mockTemplate.clone()).thenReturn(clonedTemplate);
        // act
        RichText.TextItem result = RichTextUtil.buildTextItem(text, mockTemplate);
        // assert
        assertNotNull(result);
        assertEquals("", result.getText());
        verify(mockTemplate, times(1)).clone();
    }

    /**
     * Test buildTextItem with null text
     */
    @Test
    public void testBuildTextItem_NullText() {
        // arrange
        RichText.TextItem clonedTemplate = new RichText.TextItem(14, "#FF000000", "#00FFFFFF", "Default", false, false);
        when(mockTemplate.clone()).thenReturn(clonedTemplate);
        // act
        RichText.TextItem result = RichTextUtil.buildTextItem(null, mockTemplate);
        // assert
        assertNotNull(result);
        assertNull(result.getText());
        verify(mockTemplate, times(1)).clone();
    }

    /**
     * Test buildTextItem with custom template settings
     */
    @Test
    public void testBuildTextItem_CustomTemplate() {
        // arrange
        String text = "Custom Text";
        RichText.TextItem clonedTemplate = new RichText.TextItem(16, "#FF0000FF", "#FFFF0000", "Bold", true, true);
        when(mockTemplate.clone()).thenReturn(clonedTemplate);
        // act
        RichText.TextItem result = RichTextUtil.buildTextItem(text, mockTemplate);
        // assert
        assertNotNull(result);
        assertEquals(text, result.getText());
        assertEquals(16, result.getTextsize());
        assertEquals("#FF0000FF", result.getTextcolor());
        assertEquals("#FFFF0000", result.getBackgroundcolor());
        assertEquals("Bold", result.getTextstyle());
        assertTrue(result.isStrikethrough());
        assertTrue(result.isUnderline());
        verify(mockTemplate, times(1)).clone();
    }

    /**
     * Test buildTextItem when clone throws exception
     */
    @Test
    public void testBuildTextItem_CloneException() {
        // arrange
        String text = "Test Text";
        when(mockTemplate.clone()).thenThrow(new RuntimeException("Clone failed"));
        // act & assert
        assertThrows(RuntimeException.class, () -> {
            RichTextUtil.buildTextItem(text, mockTemplate);
        });
        verify(mockTemplate, times(1)).clone();
    }

    /**
     * Test buildTextItem with null template
     */
    @Test
    public void testBuildTextItem_NullTemplate() {
        // arrange
        String text = "Test Text";
        // act & assert
        assertThrows(NullPointerException.class, () -> {
            RichTextUtil.buildTextItem(text, null);
        });
    }

    /**
     * Test case for multiple regex matches
     */
    @Test
    public void testBuildByRegexWithMultipleMatches() throws Throwable {
        // arrange
        RichText.TextItem defaultTemplate = new RichText.TextItem();
        RichText.TextItem hltTemplate = new RichText.TextItem();
        String regex = "test";
        String sourceText = "test middle test end test";
        // act
        RichText result = RichTextUtil.buildByRegex(defaultTemplate, hltTemplate, regex, sourceText);
        // assert
        assertNotNull(result);
        // Corrected the expected value to match the actual behavior
        assertEquals(5, result.getTextItemList().size());
        assertEquals("test", result.getTextItemList().get(0).getText());
        assertEquals(" middle ", result.getTextItemList().get(1).getText());
        assertEquals("test", result.getTextItemList().get(2).getText());
        assertEquals(" end ", result.getTextItemList().get(3).getText());
        assertEquals("test", result.getTextItemList().get(4).getText());
    }

    /**
     * Test case for empty source text
     */
    @Test
    public void testBuildByRegexWithEmptySourceText() throws Throwable {
        // arrange
        RichText.TextItem defaultTemplate = new RichText.TextItem();
        RichText.TextItem hltTemplate = new RichText.TextItem();
        String regex = "test";
        String sourceText = "";
        // act
        RichText result = RichTextUtil.buildByRegex(defaultTemplate, hltTemplate, regex, sourceText);
        // assert
        assertNotNull(result);
        assertTrue(result.getTextItemList().isEmpty());
    }

    /**
     * Test case for null source text
     */
    @Test
    public void testBuildByRegexWithNullSourceText() throws Throwable {
        // arrange
        RichText.TextItem defaultTemplate = new RichText.TextItem();
        RichText.TextItem hltTemplate = new RichText.TextItem();
        String regex = "test";
        String sourceText = null;
        // act
        RichText result = RichTextUtil.buildByRegex(defaultTemplate, hltTemplate, regex, sourceText);
        // assert
        assertNotNull(result);
        assertTrue(result.getTextItemList().isEmpty());
    }

    /**
     * Test case for no regex matches in source text
     */
    @Test
    public void testBuildByRegexWithNoMatches() throws Throwable {
        // arrange
        RichText.TextItem defaultTemplate = new RichText.TextItem();
        RichText.TextItem hltTemplate = new RichText.TextItem();
        String regex = "test";
        String sourceText = "sample text";
        // act
        RichText result = RichTextUtil.buildByRegex(defaultTemplate, hltTemplate, regex, sourceText);
        // assert
        assertNotNull(result);
        assertEquals(1, result.getTextItemList().size());
        assertEquals("sample text", result.getTextItemList().get(0).getText());
    }

    /**
     * Test case for single regex match at start of text
     */
    @Test
    public void testBuildByRegexWithMatchAtStart() throws Throwable {
        // arrange
        RichText.TextItem defaultTemplate = new RichText.TextItem();
        RichText.TextItem hltTemplate = new RichText.TextItem();
        String regex = "test";
        String sourceText = "test content";
        // act
        RichText result = RichTextUtil.buildByRegex(defaultTemplate, hltTemplate, regex, sourceText);
        // assert
        assertNotNull(result);
        assertEquals(2, result.getTextItemList().size());
        assertEquals("test", result.getTextItemList().get(0).getText());
        assertEquals(" content", result.getTextItemList().get(1).getText());
    }

    /**
     * Test case for single regex match in middle of text
     */
    @Test
    public void testBuildByRegexWithMatchInMiddle() throws Throwable {
        // arrange
        RichText.TextItem defaultTemplate = new RichText.TextItem();
        RichText.TextItem hltTemplate = new RichText.TextItem();
        String regex = "test";
        String sourceText = "before test after";
        // act
        RichText result = RichTextUtil.buildByRegex(defaultTemplate, hltTemplate, regex, sourceText);
        // assert
        assertNotNull(result);
        assertEquals(3, result.getTextItemList().size());
        assertEquals("before ", result.getTextItemList().get(0).getText());
        assertEquals("test", result.getTextItemList().get(1).getText());
        assertEquals(" after", result.getTextItemList().get(2).getText());
    }

    /**
     * Test case for single regex match at end of text
     */
    @Test
    public void testBuildByRegexWithMatchAtEnd() throws Throwable {
        // arrange
        RichText.TextItem defaultTemplate = new RichText.TextItem();
        RichText.TextItem hltTemplate = new RichText.TextItem();
        String regex = "test";
        String sourceText = "content test";
        // act
        RichText result = RichTextUtil.buildByRegex(defaultTemplate, hltTemplate, regex, sourceText);
        // assert
        assertNotNull(result);
        assertEquals(2, result.getTextItemList().size());
        assertEquals("content ", result.getTextItemList().get(0).getText());
        assertEquals("test", result.getTextItemList().get(1).getText());
    }

    /**
     * Test case for complex regex pattern
     */
    @Test
    public void testBuildByRegexWithComplexPattern() throws Throwable {
        // arrange
        RichText.TextItem defaultTemplate = new RichText.TextItem();
        RichText.TextItem hltTemplate = new RichText.TextItem();
        String regex = "\\d+";
        String sourceText = "abc123def456ghi";
        // act
        RichText result = RichTextUtil.buildByRegex(defaultTemplate, hltTemplate, regex, sourceText);
        // assert
        assertNotNull(result);
        assertEquals(5, result.getTextItemList().size());
        assertEquals("abc", result.getTextItemList().get(0).getText());
        assertEquals("123", result.getTextItemList().get(1).getText());
        assertEquals("def", result.getTextItemList().get(2).getText());
        assertEquals("456", result.getTextItemList().get(3).getText());
        assertEquals("ghi", result.getTextItemList().get(4).getText());
    }

    /**
     * Test case for template attributes preservation
     */
    @Test
    public void testBuildByRegexTemplateAttributesPreservation() throws Throwable {
        // arrange
        RichText.TextItem defaultTemplate = new RichText.TextItem(12, "#000000", "#FFFFFF", "normal", false, false);
        RichText.TextItem hltTemplate = new RichText.TextItem(14, "#FF0000", "#000000", "bold", true, true);
        String regex = "test";
        String sourceText = "before test after";
        // act
        RichText result = RichTextUtil.buildByRegex(defaultTemplate, hltTemplate, regex, sourceText);
        // assert
        assertNotNull(result);
        assertEquals(3, result.getTextItemList().size());
        RichText.TextItem defaultItem = result.getTextItemList().get(0);
        assertEquals(12, defaultItem.getTextsize());
        assertEquals("#000000", defaultItem.getTextcolor());
        assertEquals("#FFFFFF", defaultItem.getBackgroundcolor());
        assertEquals("normal", defaultItem.getTextstyle());
        assertFalse(defaultItem.isStrikethrough());
        assertFalse(defaultItem.isUnderline());
        RichText.TextItem hltItem = result.getTextItemList().get(1);
        assertEquals(14, hltItem.getTextsize());
        assertEquals("#FF0000", hltItem.getTextcolor());
        assertEquals("#000000", hltItem.getBackgroundcolor());
        assertEquals("bold", hltItem.getTextstyle());
        assertTrue(hltItem.isStrikethrough());
        assertTrue(hltItem.isUnderline());
    }
}
