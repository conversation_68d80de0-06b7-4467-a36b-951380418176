package com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.ext.param.request;

import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.dto.PriceReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.dto.ProductPriceReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.purchase.coupon.PurchaseCouponReturnValue;
import lombok.Data;

/**
 * @Author: guangyujie
 * @Date: 2025/3/18 15:09
 */
@Data
public class ExtParamBuilderRequest {

    private PurchaseCouponReturnValue purchaseCoupon;

    private ProductPriceReturnValue normalPrice;

    private ProductPriceReturnValue promoPrice;

    private PriceReturnValue finalPrice;

}
