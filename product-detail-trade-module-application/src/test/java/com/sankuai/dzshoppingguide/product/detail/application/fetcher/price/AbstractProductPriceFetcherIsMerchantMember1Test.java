package com.sankuai.dzshoppingguide.product.detail.application.fetcher.price;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.*;
import com.sankuai.dealuser.price.display.api.model.ClientEnv;
import com.sankuai.dealuser.price.display.api.model.ProductIdentity;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.dto.ProductPriceReturnValue;
import com.sankuai.dzshoppingguide.product.detail.domain.utils.LionConfigUtils;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class AbstractProductPriceFetcherIsMerchantMember1Test {

    private AbstractProductPriceFetcher<ProductPriceReturnValue> fetcher;

    @BeforeEach
    void setUp() {
        fetcher = new AbstractProductPriceFetcher<ProductPriceReturnValue>() {

            @Override
            protected CompletableFuture<ProductPriceReturnValue> doFetch() {
                return null;
            }

            @Override
            protected void postProcessPriceRequest(ClientEnv clientEnv, List<ProductIdentity> productIdentities, Map<String, String> extension) {
                // Implementation not needed for these tests
            }
        };
    }

    /**
     * Test when client type is supported
     */
    @Test
    void testIsMerchantMember_SupportedClientTypes() throws Throwable {
        // arrange
        ClientTypeEnum[] supportedTypes = { ClientTypeEnum.MT_APP, ClientTypeEnum.DP_APP, ClientTypeEnum.MT_XCX, ClientTypeEnum.DP_XCX, ClientTypeEnum.MT_LIVE_XCX, ClientTypeEnum.MT_LIVE_ORDER_XCX };
        int categoryId = 123;
        // act & assert
        for (ClientTypeEnum clientType : supportedTypes) {
            boolean result = fetcher.isMerchantMember(clientType, categoryId);
            // The result will be false because we can't control LionConfigUtils.judgeMemberPriceCategory
            // but we can verify that all supported client types are handled correctly
            assertFalse(result, "Should handle supported client type: " + clientType);
        }
    }

    /**
     * Test when client type is not supported
     */
    @Test
    void testIsMerchantMember_UnsupportedClientType() throws Throwable {
        // arrange
        ClientTypeEnum[] unsupportedTypes = { ClientTypeEnum.DP_PC, ClientTypeEnum.MT_PC, ClientTypeEnum.APOLLO, ClientTypeEnum.KAI_DIAN_BAO, ClientTypeEnum.UNKNOWN };
        int categoryId = 123;
        // act & assert
        for (ClientTypeEnum clientType : unsupportedTypes) {
            boolean result = fetcher.isMerchantMember(clientType, categoryId);
            assertFalse(result, "Should return false for unsupported client type: " + clientType);
        }
    }

    /**
     * Test with null client type
     */
    @Test
    void testIsMerchantMember_NullClientType() throws Throwable {
        // arrange
        ClientTypeEnum clientType = null;
        int categoryId = 123;
        // act
        boolean result = fetcher.isMerchantMember(clientType, categoryId);
        // assert
        assertFalse(result, "Should return false for null client type");
    }

    /**
     * Test with negative category ID
     */
    @Test
    void testIsMerchantMember_NegativeCategoryId() throws Throwable {
        // arrange
        ClientTypeEnum clientType = ClientTypeEnum.MT_APP;
        int categoryId = -1;
        // act
        boolean result = fetcher.isMerchantMember(clientType, categoryId);
        // assert
        assertFalse(result, "Should return false for negative category ID");
    }

    /**
     * Test with zero category ID
     */
    @Test
    void testIsMerchantMember_ZeroCategoryId() throws Throwable {
        // arrange
        ClientTypeEnum clientType = ClientTypeEnum.MT_APP;
        int categoryId = 0;
        // act
        boolean result = fetcher.isMerchantMember(clientType, categoryId);
        // assert
        assertFalse(result, "Should return false for zero category ID");
    }
}
