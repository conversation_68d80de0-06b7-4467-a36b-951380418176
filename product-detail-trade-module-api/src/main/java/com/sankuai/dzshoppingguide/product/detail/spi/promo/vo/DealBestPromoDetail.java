package com.sankuai.dzshoppingguide.product.detail.spi.promo.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@MobileDo(id = 0xe867)
public class DealBestPromoDetail implements Serializable {
    /**
     * 优惠金额
     */
    @MobileDo.MobileField(key = 0x6806)
    private String promoAmount;

    /**
     * 优惠名称
     */
    @MobileDo.MobileField(key = 0x4871)
    private String promoName;

    /**
    * 优惠icon
    */
    @MobileDo.MobileField(key = 0xf39b)
    private String iconUrl;

    /**
    * 优惠描述
    */
    @MobileDo.MobileField(key = 0xcc07)
    private String promoDesc;

    /**
    * 优惠明细附加信息模型
    */
    @MobileDo.MobileField(key = 0x566b)
    private DealDetailPromoDetailExtraInfo promoDetailExtraInfo;

    /**
    * 优惠类型标签，如「美团补贴」「新客特惠」
    */
    @MobileDo.MobileField(key = 0x74ac)
    private String promoTag;

    /**
    * 优惠规则描述
    */
    @MobileDo.MobileField(key = 0xa01)
    private String promoRuleDesc;

    /**
    * 优惠描述列表提供给前端展示
    */
    @MobileDo.MobileField(key = 0xc3fd)
    private List<String> promoDescList;

  }