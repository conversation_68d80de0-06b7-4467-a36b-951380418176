package com.sankuai.dzshoppingguide.product.detail.spi.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/03/28
 * 优惠标签枚举
 */
@Getter
@AllArgsConstructor
public enum PromoTagEnum {

    MARKET_PRICE("MARKET_PRICE","门市价"),
    DEAL_PROMO("DEAL_PROMO","团购优惠"),
    MAGICAL_MEMBER_PLATFORM_COUPON("MAGICAL_MEMBER_PLATFORM_COUPON","神券"),
    MEMBER_BENEFITS("MEMBER_BENEFITS","会员优惠"),
    NEW_MEMBER_BENEFITS("NEW_MEMBER_BENEFITS","新会员优惠"),
    PLATFORM_COUPON("PLATFORM_COUPON","美团券"),
    MERCHANT_COUPON("MERCHANT_COUPON","商家券"),
    GOVERNMENT_CONSUME_COUPON("GOVERNMENT_CONSUME_COUPON","政府消费券"),
    SECOND_KILL("SECOND_KILL","限时秒杀"),
    MT_SUBSIDY("MT_SUBSIDY","美团补贴"),
    NEW_CUSTOMER_DISCOUNT("NEW_CUSTOMER_DISCOUNT","新客特惠"),
    DISCOUNT_SELL("DISCOUNT_SELL","特惠促销"),
    MERCHANT_DISCOUNT("MERCHANT_DISCOUNT","商家立减"),
    PRESALE_PROMO("PRESALE_PROMO","预售优惠");


    private String type;
    private String desc;

    /**
     * 按指定顺序排列的标签列表：
     * 门市价>团购优惠>神券>会员优惠>美团券>商家券>政府消费券>限时秒杀>美团补贴>新客特惠>特惠促销>商家立减>预售优惠
     */
    public static final List<String> orderedTags = Collections.unmodifiableList(Arrays.asList(
            MARKET_PRICE.desc,
            DEAL_PROMO.desc,
            MAGICAL_MEMBER_PLATFORM_COUPON.desc,
            MEMBER_BENEFITS.desc,
            NEW_MEMBER_BENEFITS.desc,
            SECOND_KILL.desc,
            MT_SUBSIDY.desc,
            NEW_CUSTOMER_DISCOUNT.desc,
            DISCOUNT_SELL.desc,
            MERCHANT_DISCOUNT.desc,
            PRESALE_PROMO.desc,
            PLATFORM_COUPON.desc,
            MERCHANT_COUPON.desc,
            GOVERNMENT_CONSUME_COUPON.desc
    ));

    public static String getDescByType(String type) {
        for (PromoTagEnum promoTagEnum : PromoTagEnum.values()) {
            if (promoTagEnum.type.equals(type)) {
                return promoTagEnum.getDesc();
            }
        }
        return DEAL_PROMO.getDesc();
    }

    public static String getPromoDetailDescByType(String type) {
        for (PromoTagEnum promoTagEnum : PromoTagEnum.values()) {
            if (promoTagEnum.type.equals(type)) {
                return promoTagEnum.getDesc();
            }
        }
        return "优惠";
    }

}
