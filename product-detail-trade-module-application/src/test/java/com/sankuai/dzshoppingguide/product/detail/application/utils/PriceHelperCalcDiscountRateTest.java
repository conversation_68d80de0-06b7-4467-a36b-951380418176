package com.sankuai.dzshoppingguide.product.detail.application.utils;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.utils.exception.PriceException;
import com.sankuai.general.product.query.center.client.dto.PriceDTO;
import java.math.BigDecimal;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * PriceHelper.calcDiscountRate方法的单元测试类
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("PriceHelper.calcDiscountRate Method Tests")
public class PriceHelperCalcDiscountRateTest {


    @Mock
    private PriceContext mockPriceContext;

    @Mock
    private ProductBaseInfo mockProductBaseInfo;

    @Mock
    private PriceDTO mockPriceDTO;

    /**
     * Test dropLastZero method with integer inputs
     */
    @ParameterizedTest
    @CsvSource({ "0, 0", "1, 1", "-1, -1", "1000, 1000", "-1000, -1000" })
    @DisplayName("Test dropLastZero with integer inputs")
    public void testDropLastZeroIntegerInput(int input, String expected) {
        // arrange
        BigDecimal price = new BigDecimal(input);
        // act
        String result = PriceHelper.dropLastZero(price);
        // assert
        assertEquals(expected, result, "Integer input should return the same value as a string");
    }

    /**
     * Test dropLastZero method with decimal inputs having trailing zeros
     */
    @ParameterizedTest
    @CsvSource({ "1.00, 1", "1.10, 1.1", "1.01, 1.01", "-1.00, -1", "-1.10, -1.1" })
    @DisplayName("Test dropLastZero with decimal inputs having trailing zeros")
    public void testDropLastZeroDecimalInputWithTrailingZeros(String input, String expected) {
        // arrange
        BigDecimal price = new BigDecimal(input);
        // act
        String result = PriceHelper.dropLastZero(price);
        // assert
        assertEquals(expected, result, "Decimal input with trailing zeros should be formatted correctly");
    }

    /**
     * Test dropLastZero method with decimal inputs without trailing zeros
     */
    @ParameterizedTest
    @CsvSource({ "1.1, 1.1", "1.01, 1.01", "-1.1, -1.1", "-1.01, -1.01" })
    @DisplayName("Test dropLastZero with decimal inputs without trailing zeros")
    public void testDropLastZeroDecimalInputWithoutTrailingZeros(String input, String expected) {
        // arrange
        BigDecimal price = new BigDecimal(input);
        // act
        String result = PriceHelper.dropLastZero(price);
        // assert
        assertEquals(expected, result, "Decimal input without trailing zeros should remain unchanged");
    }

    /**
     * 测试calcDiscountRate方法，当dealGroupPrice或promoPrice为null时，应返回null
     */
    @Test
    @DisplayName("When input parameters are null, should return null")
    public void testCalcDiscountRateNullInput() throws Throwable {
        // arrange & act & assert
        assertAll(() -> assertNull(PriceHelper.calcDiscountRate(null, new BigDecimal("100"))), () -> assertNull(PriceHelper.calcDiscountRate(new BigDecimal("100"), null)));
    }

    /**
     * 测试calcDiscountRate方法，当dealGroupPrice或promoPrice的值为0时，应返回null
     */
    @Test
    @DisplayName("When input prices are zero, should return null")
    public void testCalcDiscountRateZeroInput() throws Throwable {
        // arrange & act & assert
        assertAll(() -> assertNull(PriceHelper.calcDiscountRate(BigDecimal.ZERO, new BigDecimal("100"))), () -> assertNull(PriceHelper.calcDiscountRate(new BigDecimal("100"), BigDecimal.ZERO)));
    }

    /**
     * 测试calcDiscountRate方法，当promoPrice大于dealGroupPrice时，应返回null
     */
    @Test
    @DisplayName("When promoPrice is greater than dealGroupPrice, should return null")
    public void testCalcDiscountRatePromoPriceGreaterThanDealGroupPrice() throws Throwable {
        // arrange
        BigDecimal dealGroupPrice = new BigDecimal("100");
        BigDecimal promoPrice = new BigDecimal("200");
        // act
        String result = PriceHelper.calcDiscountRate(dealGroupPrice, promoPrice);
        // assert
        assertNull(result);
    }

    /**
     * 测试calcDiscountRate方法，当promoPrice等于dealGroupPrice时，应返回null
     */
    @Test
    @DisplayName("When promoPrice equals dealGroupPrice, should return null")
    public void testCalcDiscountRatePromoPriceEqualsDealGroupPrice() throws Throwable {
        // arrange
        BigDecimal price = new BigDecimal("100");
        // act
        String result = PriceHelper.calcDiscountRate(price, price);
        // assert
        assertNull(result);
    }

    /**
     * 测试calcDiscountRate方法，当promoPrice小于dealGroupPrice时，应返回正确的折扣率
     * 折扣率计算公式：(promoPrice/dealGroupPrice)*10
     */
    @Test
    @DisplayName("When promoPrice is less than dealGroupPrice, should return correct discount rate")
    public void testCalcDiscountRatePromoPriceLessThanDealGroupPrice() throws Throwable {
        // arrange
        BigDecimal dealGroupPrice = new BigDecimal("100");
        BigDecimal promoPrice = new BigDecimal("50");
        // act
        String result = PriceHelper.calcDiscountRate(dealGroupPrice, promoPrice);
        // assert
        assertEquals("5", result);
    }

    /**
     * 测试calcDiscountRate方法，验证不同精度的折扣计算结果
     * 注意：结果会去除末尾的0
     */
    @Test
    @DisplayName("When calculating discount rates with different precisions")
    public void testCalcDiscountRateWithDifferentPrecision() throws Throwable {
        // arrange & act & assert
        assertAll(() -> assertEquals("3.4", PriceHelper.calcDiscountRate(new BigDecimal("100"), new BigDecimal("33.4"))), () -> assertEquals("6.7", PriceHelper.calcDiscountRate(new BigDecimal("150"), new BigDecimal("100"))));
    }

    /**
     * 测试calcDiscountRate方法，验证小数位处理
     */
    @Test
    @DisplayName("When calculating discount rates with decimal places")
    public void testCalcDiscountRateDecimalHandling() throws Throwable {
        // arrange
        BigDecimal dealGroupPrice = new BigDecimal("99.99");
        BigDecimal promoPrice = new BigDecimal("49.99");
        // act
        String result = PriceHelper.calcDiscountRate(dealGroupPrice, promoPrice);
        // assert
        assertEquals("5", result);
    }

    /**
     * Test formatting regular number without trailing zeros
     */
    @Test
    @DisplayName("Format regular number without trailing zeros")
    public void testFormatPrice_RegularNumber() {
        // arrange
        BigDecimal price = new BigDecimal("123.45");
        // act
        String result = PriceHelper.formatPrice(price);
        // assert
        assertEquals("123.45", result, "Regular number should be formatted correctly");
    }

    /**
     * Test formatting number with trailing zeros
     */
    @Test
    @DisplayName("Format number with trailing zeros")
    public void testFormatPrice_WithTrailingZeros() {
        // arrange
        BigDecimal price = new BigDecimal("100.100");
        // act
        String result = PriceHelper.formatPrice(price);
        // assert
        assertEquals("100.1", result, "Trailing zeros should be removed");
    }

    /**
     * Test formatting whole number
     */
    @Test
    @DisplayName("Format whole number")
    public void testFormatPrice_WholeNumber() {
        // arrange
        BigDecimal price = new BigDecimal("100.00");
        // act
        String result = PriceHelper.formatPrice(price);
        // assert
        assertEquals("100", result, "Whole number should not show decimal places");
    }

    /**
     * Test formatting zero value
     */
    @Test
    @DisplayName("Format zero value")
    public void testFormatPrice_Zero() {
        // arrange
        BigDecimal price = BigDecimal.ZERO;
        // act
        String result = PriceHelper.formatPrice(price);
        // assert
        assertEquals("0", result, "Zero should be formatted as '0'");
    }

    /**
     * Test formatting negative number
     */
    @Test
    @DisplayName("Format negative number")
    public void testFormatPrice_NegativeNumber() {
        // arrange
        BigDecimal price = new BigDecimal("-123.450");
        // act
        String result = PriceHelper.formatPrice(price);
        // assert
        assertEquals("-123.45", result, "Negative number should be formatted correctly");
    }

    /**
     * Test formatting very large number
     */
    @Test
    @DisplayName("Format large number")
    public void testFormatPrice_LargeNumber() {
        // arrange
        BigDecimal price = new BigDecimal("999999999.99");
        // act
        String result = PriceHelper.formatPrice(price);
        // assert
        assertEquals("999999999.99", result, "Large number should be formatted correctly");
    }

    /**
     * Test formatting very small decimal
     */
    @Test
    @DisplayName("Format small decimal")
    public void testFormatPrice_SmallDecimal() {
        // arrange
        BigDecimal price = new BigDecimal("0.001");
        // act
        String result = PriceHelper.formatPrice(price);
        // assert
        assertEquals("0.001", result, "Small decimal should be formatted correctly");
    }

    /**
     * Test null input throws NullPointerException
     */
    @Test
    @DisplayName("Null input should throw NullPointerException")
    public void testFormatPrice_NullInput() {
        // arrange
        BigDecimal price = null;
        // act & assert
        assertThrows(NullPointerException.class, () -> PriceHelper.formatPrice(price), "Null input should throw NullPointerException");
    }

    /**
     * Test dropLastZero method with null input
     */
    @Test
    @DisplayName("Test dropLastZero with null input")
    public void testDropLastZeroNullInput() throws Throwable {
        // arrange
        BigDecimal nullPrice = null;
        // act & assert
        assertThrows(PriceException.class, () -> PriceHelper.dropLastZero(nullPrice), "Expected PriceException to be thrown for null input");
    }

    /**
     * Test dropLastZero method with very large numbers
     */
    @Test
    @DisplayName("Test dropLastZero with very large numbers")
    public void testDropLastZeroLargeNumbers() throws Throwable {
        // arrange
        BigDecimal largeNumber = new BigDecimal("1000000000000000000000.00");
        // act
        String result = PriceHelper.dropLastZero(largeNumber);
        // assert
        assertEquals("1000000000000000000000", result, "Large number should be formatted correctly");
    }

    /**
     * Test dropLastZero method with very small numbers
     */
    @Test
    @DisplayName("Test dropLastZero with very small numbers")
    public void testDropLastZeroSmallNumbers() throws Throwable {
        // arrange
        BigDecimal smallNumber = new BigDecimal("0.01");
        // act
        String result = PriceHelper.dropLastZero(smallNumber);
        // assert
        assertEquals("0.01", result, "Small number should be formatted correctly");
    }

    /**
     * Test formatting integer price
     */
    @Test
    @DisplayName("Test integer price formatting")
    public void testPriceFormat_IntegerPrice() throws Throwable {
        // arrange
        double price = 100.0;
        // act
        String result = PriceHelper.priceFormat(price);
        // assert
        assertEquals("100", result, "Integer price should be formatted without decimal places");
    }

    /**
     * Test formatting price with one decimal place
     */
    @Test
    @DisplayName("Test price with one decimal place")
    public void testPriceFormat_OneDecimalPlace() throws Throwable {
        // arrange
        double price = 99.5;
        // act
        String result = PriceHelper.priceFormat(price);
        // assert
        assertEquals("99.5", result, "Price with one decimal place should be preserved");
    }

    /**
     * Test formatting price with two decimal places
     */
    @Test
    @DisplayName("Test price with two decimal places")
    public void testPriceFormat_TwoDecimalPlaces() throws Throwable {
        // arrange
        double price = 99.99;
        // act
        String result = PriceHelper.priceFormat(price);
        // assert
        assertEquals("99.99", result, "Price with two decimal places should be preserved");
    }

    /**
     * Test formatting price with more than two decimal places
     */
    @Test
    @DisplayName("Test price with more than two decimal places")
    public void testPriceFormat_MoreThanTwoDecimalPlaces() throws Throwable {
        // arrange
        double price = 99.999;
        // act
        String result = PriceHelper.priceFormat(price);
        // assert
        assertEquals("100", result, "Price with more than two decimal places should be rounded");
    }

    /**
     * Test formatting zero price
     */
    @Test
    @DisplayName("Test zero price")
    public void testPriceFormat_ZeroPrice() throws Throwable {
        // arrange
        double price = 0.0;
        // act
        String result = PriceHelper.priceFormat(price);
        // assert
        assertEquals("0", result, "Zero price should be formatted as '0'");
    }

    /**
     * Test formatting negative price
     */
    @Test
    @DisplayName("Test negative price")
    public void testPriceFormat_NegativePrice() throws Throwable {
        // arrange
        double price = -99.99;
        // act
        String result = PriceHelper.priceFormat(price);
        // assert
        assertEquals("-99.99", result, "Negative price should maintain sign and decimal places");
    }

    /**
     * Test formatting very small decimal price
     */
    @Test
    @DisplayName("Test very small decimal price")
    public void testPriceFormat_VerySmallDecimal() throws Throwable {
        // arrange
        double price = 0.001;
        // act
        String result = PriceHelper.priceFormat(price);
        // assert
        assertEquals("0", result, "Very small decimal should be rounded to 0");
    }

    /**
     * Test formatting very large price
     */
    @Test
    @DisplayName("Test very large price")
    public void testPriceFormat_VeryLargeNumber() throws Throwable {
        // arrange
        double price = 999999999.99;
        // act
        String result = PriceHelper.priceFormat(price);
        // assert
        assertEquals("999999999.99", result, "Large numbers should be formatted correctly");
    }
}
