package com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sale.info;

import com.dianping.cat.Cat;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sale.info.enums.ProductSaleStatusEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sale.info.enums.ProductSaleTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.domain.exception.ProductDetailFatalError;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import org.apache.commons.collections4.MapUtils;

import java.util.*;

/**
 * @Author: guangyujie
 * @Date: 2025/4/2 14:20
 */
@Getter
@EqualsAndHashCode(callSuper = true)
public class ProductSaleInfo extends FetcherReturnValueDTO {

    private Set<ProductSaleTypeEnum> saleType = new HashSet<>();

    private Map<ProductSaleTypeEnum, ProductSaleStatusEnum> saleStatus = new HashMap<>();

    public ProductSaleStatusEnum getSaleStatus(ProductSaleTypeEnum saleType) {
        if (saleType == null) {
            throw new ProductDetailFatalError("saleType不能为null");
        }
        ProductSaleStatusEnum productSaleStatusEnum = saleStatus.get(saleType);
        if (productSaleStatusEnum == null) {
            Cat.logError(new ProductDetailFatalError(String.format("获取不到saleType=%s的saleStatus", saleType.name())));
            return ProductSaleStatusEnum.ONLINE;//兜底默认可以交易，至少能跳转到提单页
        }
        return productSaleStatusEnum;
    }

    public boolean isOnlineForSale() {
        if (MapUtils.isEmpty(saleStatus)) {
            return false;
        }
        return saleStatus.containsValue(ProductSaleStatusEnum.ONLINE);
    }

    /**
     * 是否支持直接购买
     */
    public boolean forSale(ProductSaleTypeEnum saleType) {
        return Optional.ofNullable(saleStatus.get(saleType)).map(ProductSaleStatusEnum::isForSale).orElse(false);
    }

    public static ProductSaleInfo getDefaultProductSaleInfo() {
        return new ProductSaleInfo(ProductSaleTypeEnum.COMMON_DEAL, ProductSaleStatusEnum.ONLINE);
    }

    public ProductSaleInfo(ProductSaleTypeEnum saleType, ProductSaleStatusEnum saleStatus) {
        this.saleType.add(saleType);
        this.saleType = Collections.unmodifiableSet(this.saleType);
        this.saleStatus.put(saleType, saleStatus);
        this.saleStatus = Collections.unmodifiableMap(this.saleStatus);
    }

    public ProductSaleInfo(Map<ProductSaleTypeEnum, ProductSaleStatusEnum> saleStatusMap) {
        this.saleType.addAll(saleStatusMap.keySet());
        this.saleType = Collections.unmodifiableSet(this.saleType);
        this.saleStatus.putAll(saleStatusMap);
        this.saleStatus = Collections.unmodifiableMap(this.saleStatus);
    }

}
