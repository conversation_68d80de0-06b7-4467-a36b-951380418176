package com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.period.price.dto;

import com.sankuai.general.product.query.center.client.dto.price.DateTimePriceDTO;
import com.sankuai.general.product.query.center.client.dto.price.PeriodPriceDTO;
import com.sankuai.general.product.query.center.client.dto.price.plan.WeeklyPricePlanDTO;
import lombok.Getter;

/**
 * @Author: guang<PERSON>jie
 * @Date: 2025/2/6 16:56
 */
@Getter
public class PeriodPriceMergeDTO {

    /**
     * 周循环价格计划
     */
    private final WeeklyPricePlanDTO weeklyPricePlan;

    /**
     * 日期时间段价格
     */
    private final DateTimePriceDTO dateTimePrice;

    /**
     * 日期段价格
     */
    private final PeriodPriceDTO periodPrice;

    public PeriodPriceMergeDTO(final WeeklyPricePlanDTO weeklyPricePlan,
                               final DateTimePriceDTO dateTimePrice,
                               final PeriodPriceDTO periodPrice) {
        this.weeklyPricePlan = weeklyPricePlan;
        this.dateTimePrice = dateTimePrice;
        this.periodPrice = periodPrice;
    }

}
