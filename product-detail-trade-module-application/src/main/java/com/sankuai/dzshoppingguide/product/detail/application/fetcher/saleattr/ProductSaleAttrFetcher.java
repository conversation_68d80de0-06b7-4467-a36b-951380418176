package com.sankuai.dzshoppingguide.product.detail.application.fetcher.saleattr;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategoryFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.exception.QueryCenterException;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.attr.SkuAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.utils.SkuIdUtils;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.FetcherDAGStarter;
import com.sankuai.dzshoppingguide.product.detail.application.utils.LionConfigUtils;
import com.sankuai.dzshoppingguide.product.detail.domain.query.center.QueryCenterAclService;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.deal.DealGroupDealDTO;
import com.sankuai.general.product.query.center.client.enums.AttrSubjectEnum;
import com.sankuai.general.product.query.center.client.enums.AttrTypeEnum;
import com.sankuai.general.product.query.center.client.enums.ResponseCodeEnum;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.thrift.TException;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-06-04
 * @desc 商品销售属性查询器
 */
@Slf4j
@Fetcher(
      previousLayerDependencies = {
              FetcherDAGStarter.class,
              ProductCategoryFetcher.class
      }
)
public class ProductSaleAttrFetcher extends NormalFetcherContext<SkuAttr> {

    @Resource
    private QueryCenterAclService queryCenterAclService;

    @Override
    protected CompletableFuture<SkuAttr> doFetch() throws Exception {
        Integer thirdCategoryId = getDependencyResult(ProductCategoryFetcher.class, ProductCategory.class)
                .map(ProductCategory::getProductThirdCategoryId)
                .orElse(0);
        // 目前仅国补商品需要查询
        List<Integer> countrySubsidiesServiceTypeIds = LionConfigUtils.getCountrySubsidiesServiceTypeIds();
        if (!countrySubsidiesServiceTypeIds.contains(thirdCategoryId)) {
            return CompletableFuture.completedFuture(null);
        }
        QueryByDealGroupIdRequestBuilder saleAttrKeyRequestBuilder = QueryCenterAclService.getBaseRequestBuilder(request);
        saleAttrKeyRequestBuilder.attrsByType(AttrSubjectEnum.DEAL, AttrTypeEnum.SALE_PROP);
        QueryByDealGroupIdRequest saleAttrKeyRequest = saleAttrKeyRequestBuilder.build();
        try {
            return queryCenterAclService.query(saleAttrKeyRequest).thenApply(saleAttrKeyResponse -> {
                if (saleAttrKeyResponse == null || saleAttrKeyResponse.getCode() != ResponseCodeEnum.SUCCESS.getCode()
                        || saleAttrKeyResponse.getData() == null || CollectionUtils.isEmpty(saleAttrKeyResponse.getData().getList())) {
                    return null;
                }
                DealGroupDTO dealGroupDTO = saleAttrKeyResponse.getData().getList().get(0);
                if (Objects.isNull(dealGroupDTO) || CollectionUtils.isEmpty(dealGroupDTO.getDeals())) {
                    return null;
                }
                List<AttrDTO> saleAttrDTOS = dealGroupDTO.getDeals()
                        .stream()
                        .map(DealGroupDealDTO::getAttrs)
                        .filter(Objects::nonNull)
                        .flatMap(List::stream)
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(saleAttrDTOS)) {
                    return null;
                }
                Set<String> attrKeys = saleAttrDTOS.stream()
                        .map(AttrDTO::getName)
                        .collect(Collectors.toSet());
                // 查询销售属性值
                QueryByDealGroupIdRequestBuilder saleAttrValueRequestBuilder = QueryCenterAclService.getBaseRequestBuilder(request);
                saleAttrValueRequestBuilder.attrsByKey(AttrSubjectEnum.DEAL, attrKeys);
                QueryByDealGroupIdRequest saleAttrValueRequest = saleAttrValueRequestBuilder.build();
                try {
                    return queryCenterAclService.query(saleAttrValueRequest).thenApply(saleAttrValueResponse -> {
                        if (saleAttrValueResponse == null || saleAttrValueResponse.getCode() != ResponseCodeEnum.SUCCESS.getCode()
                                || saleAttrValueResponse.getData() == null || CollectionUtils.isEmpty(saleAttrValueResponse.getData().getList())) {
                            return null;
                        }
                        DealGroupDTO dealGroupDTO1 = saleAttrKeyResponse.getData().getList().get(0);
                        Map<Long, Map<String, AttrDTO>> skuAttrMap = Optional.ofNullable(dealGroupDTO1)
                                .map(DealGroupDTO::getDeals)
                                .orElse(new ArrayList<>())
                                .stream()
                                .filter(Objects::nonNull)
                                .collect(Collectors.toMap(
                                        sku -> SkuIdUtils.getSkuId(this.request.getProductTypeEnum(), sku),
                                        sku -> {
                                            List<AttrDTO> attrDTOS = Optional.of(sku).map(DealGroupDealDTO::getAttrs).orElse(new ArrayList<>());
                                            return attrDTOS.stream().collect(Collectors.toMap(
                                                    AttrDTO::getName,
                                                    v -> v,
                                                    (v1, v2) -> v1
                                            ));
                                        },
                                        (v1, v2) -> v1
                                ));
                        return new SkuAttr(skuAttrMap);
                    }).join();
                } catch (TException e) {
                    log.error("query sale attr value error, request:{}", request, e);
                    throw new QueryCenterException(e);
                }
            });
        } catch (Exception e) {
            log.error("query sale attr key error, request:{}", request, e);
            return null;
        }

    }
}
