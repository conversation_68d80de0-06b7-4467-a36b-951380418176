package com.sankuai.dzshoppingguide.product.detail.application.builder.tag.edu;

import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.extend.BaseBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.FetcherDAGStarter;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.tag.edu.EduStudyBeforePayBannerTag;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.tag.edu.EduStudyBeforePayTagFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.tag.guarantee.ProductGuaranteeTagInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.tag.guarantee.SkuGuaranteeTagFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.utils.TimesDealUtil;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.CommonRichTextDTO;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.tags.vo.IconTagVO;
import com.sankuai.dzshoppingguide.product.detail.spi.tags.vo.edu.EduStudyBeforePayTagsModuleVO;
import lombok.extern.slf4j.Slf4j;
import java.util.*;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/2/5 17:04
 */
@Builder(
        moduleKey = ModuleKeyConstants.EDU_STUDY_BEFORE_PAY,
        startFetcher = FetcherDAGStarter.class,
        dependentFetchers = {EduStudyBeforePayTagFetcher.class, ProductBaseInfoFetcher.class, SkuGuaranteeTagFetcher.class}
)
@Slf4j
public class EduStudyBeforePayTagsModuleBuilder extends BaseBuilder<EduStudyBeforePayTagsModuleVO> {

    @Override
    public EduStudyBeforePayTagsModuleVO doBuild() {
        ProductBaseInfo productBaseInfo = getDependencyResult(ProductBaseInfoFetcher.class);
        ProductGuaranteeTagInfo productGuaranteeTagInfo = getDependencyResult(SkuGuaranteeTagFetcher.class);
        EduStudyBeforePayBannerTag eduStudyBeforePayBannerTag = getDependencyResult(EduStudyBeforePayTagFetcher.class);
        if (Objects.isNull(eduStudyBeforePayBannerTag)) {
            return null;
        }
        if (!TimesDealUtil.isTimesDeal(productBaseInfo)) {
            return null;
        }
        if (!hasAnXinLearningGuaranteeTag(productGuaranteeTagInfo)) {
            return null;
        }
        return buildTagsModuleVO(eduStudyBeforePayBannerTag.getText(), eduStudyBeforePayBannerTag.getUrl());
    }

    private boolean hasAnXinLearningGuaranteeTag(ProductGuaranteeTagInfo productGuaranteeTagInfo) {
        if (Objects.isNull(productGuaranteeTagInfo) || Objects.isNull(productGuaranteeTagInfo.getAnXinLearning())) {
            return false;
        }
        return true;
    }

    private EduStudyBeforePayTagsModuleVO buildTagsModuleVO(String text, String url) {
        EduStudyBeforePayTagsModuleVO eduStudyBeforePayTagsModuleVO = new EduStudyBeforePayTagsModuleVO();

        IconTagVO tagVo = new IconTagVO();
        tagVo.setSuffixIconUrl("https://p0.meituan.net/ingee/cfc0b3155864b3e933cd5c48646adc0f662.png");
        tagVo.setLink(url);
        tagVo.setSuffixIcon(true);
        tagVo.setBackgroundColor("#00B3001A");

        List<CommonRichTextDTO> contents = new ArrayList<>();
        CommonRichTextDTO commonRichTextDTO = CommonRichTextDTO.builder()
                .fontColor("#34C759")
                .text(text).build();
        contents.add(commonRichTextDTO);
        tagVo.setContents(contents);
        eduStudyBeforePayTagsModuleVO.setTags(Collections.singletonList(tagVo));
        return eduStudyBeforePayTagsModuleVO;

    }
}
