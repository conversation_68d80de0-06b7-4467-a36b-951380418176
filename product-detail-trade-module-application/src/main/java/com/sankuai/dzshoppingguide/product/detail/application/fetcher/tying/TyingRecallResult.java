package com.sankuai.dzshoppingguide.product.detail.application.fetcher.tying;

import com.dianping.martgeneral.recommend.api.dto.RecommendDTO;
import com.dianping.martgeneral.recommend.api.entity.RecommendResult;
import com.dianping.martgeneral.recommend.api.entity.Response;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import com.sankuai.mktplay.center.mkt.play.center.client.PlayExecuteResponse;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TyingRecallResult extends FetcherReturnValueDTO {
    private PlayExecuteResponse playResponse;
    private Response<RecommendResult> recommendResponse;
}
