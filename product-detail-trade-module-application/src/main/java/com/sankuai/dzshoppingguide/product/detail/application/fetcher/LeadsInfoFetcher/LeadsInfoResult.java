package com.sankuai.dzshoppingguide.product.detail.application.fetcher.LeadsInfoFetcher;

import com.dianping.product.shelf.common.dto.activity.ActivityDetailDTO;
import com.sankuai.clr.content.process.gateway.thrift.dto.leads.LoadLeadsInfoRespDTO;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @create 2025/3/7 15:33
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class LeadsInfoResult extends FetcherReturnValueDTO {
    ActivityDetailDTO activityDetailDTO;
    LoadLeadsInfoRespDTO loadLeadsInfoRespDTO;

}
