package com.sankuai.dzshoppingguide.product.detail.application.fetcher.welfare;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.shopid.ShopIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.shopid.ShopIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.customer.ProductCustomer;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.customer.ProductCustomerFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
import com.sankuai.meituan.charity.merchant.main.sdk.dto.request.DzVpoiReq;
import com.sankuai.meituan.charity.merchant.main.sdk.dto.response.DzProductDocResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

/**
 * @Author: <EMAIL>
 * @Date: 2025/3/7
 */
@Fetcher(
        previousLayerDependencies = {ProductCustomerFetcher.class, ShopIdMapperFetcher.class},
        timeout = 500
)
@Slf4j
public class WelfareDocFetcher extends NormalFetcherContext<WelfareDocInfo> {
    @Resource
    private CompositeAtomService compositeAtomService;

    @Override
    protected CompletableFuture<WelfareDocInfo> doFetch() {
        ProductCustomer productCustomer = getDependencyResult(ProductCustomerFetcher.class);
        ShopIdMapper shopIdMapper = getDependencyResult(ShopIdMapperFetcher.class);
        DzVpoiReq dzVpoiReq = new DzVpoiReq();
        String poiIdStr = Objects.nonNull(shopIdMapper) ? String.valueOf(shopIdMapper.getMtBestShopId()) : null;
        dzVpoiReq.setPoiId(poiIdStr);
        String originCustomerId = Objects.nonNull(productCustomer) ? String.valueOf(productCustomer.getOriginCustomerId()) : null;
        dzVpoiReq.setPartnerId(originCustomerId);
        CompletableFuture<DzProductDocResp> future = compositeAtomService.queryDzProductDoc(dzVpoiReq);
        return future.thenApply(dzProductDocResp -> {
            Boolean signCharity = dzProductDocResp.getSignCharity();
            if (BooleanUtils.isNotTrue(signCharity)) {
                return null;
            }
            WelfareDocInfo welfareDocInfo = new WelfareDocInfo();
            welfareDocInfo.setDzProductDocResp(dzProductDocResp);
            return welfareDocInfo;
        });
    }
}
