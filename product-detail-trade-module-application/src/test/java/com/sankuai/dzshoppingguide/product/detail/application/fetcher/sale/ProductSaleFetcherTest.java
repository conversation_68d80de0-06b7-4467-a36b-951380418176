package com.sankuai.dzshoppingguide.product.detail.application.fetcher.sale;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.dianping.deal.sales.common.datatype.SalesDisplayInfoDTO;
import com.dianping.deal.sales.common.datatype.SalesDisplayQueryRequest;
import com.dianping.deal.sales.common.datatype.SalesOption;
import com.dianping.deal.sales.common.datatype.SalesSubjectParam;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import org.apache.commons.collections4.MapUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;
import org.springframework.test.util.ReflectionTestUtils;

class ProductSaleFetcherTest {

    @Mock
    private CompositeAtomService compositeAtomService;

    @InjectMocks
    private ProductSaleFetcher productSaleFetcher;

    private ProductDetailPageRequest pageRequest;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        // 初始化 ProductDetailPageRequest
        pageRequest = new ProductDetailPageRequest();
        pageRequest.setProductId(123L);
        // 使用反射设置 request 字段
        ReflectionTestUtils.setField(productSaleFetcher, "request", pageRequest);
    }

    /**
     * 测试正常场景：compositeAtomService.querySaleDisplay 返回的 Map 不为空，且包含 SalesSubjectParam 对应的 SalesDisplayInfoDTO
     */
    @Test
    public void testDoFetchNormalCase() throws Throwable {
        // arrange
        SalesSubjectParam subjectParam = SalesSubjectParam.bizDealGroup(pageRequest.getProductId());
        SalesDisplayInfoDTO displayInfoDTO = new SalesDisplayInfoDTO();
        displayInfoDTO.setSalesNum(100L);
        Map<SalesSubjectParam, SalesDisplayInfoDTO> resultMap = new HashMap<>();
        resultMap.put(subjectParam, displayInfoDTO);
        when(compositeAtomService.querySaleDisplay(any(SalesDisplayQueryRequest.class))).thenReturn(CompletableFuture.completedFuture(resultMap));
        // act
        CompletableFuture<ProductSaleReturnValue> future = productSaleFetcher.doFetch();
        ProductSaleReturnValue result = future.get();
        // assert
        assertNotNull(result);
        assertNotNull(result.getSaleDisplayDTO());
        assertEquals(100L, result.getSaleDisplayDTO().getSalesNum());
    }

    /**
     * 测试边界场景：compositeAtomService.querySaleDisplay 返回的 Map 为空
     */
    @Test
    public void testDoFetchEmptyMapCase() throws Throwable {
        // arrange
        when(compositeAtomService.querySaleDisplay(any(SalesDisplayQueryRequest.class))).thenReturn(CompletableFuture.completedFuture(new HashMap<>()));
        // act
        CompletableFuture<ProductSaleReturnValue> future = productSaleFetcher.doFetch();
        ProductSaleReturnValue result = future.get();
        // assert
        assertNotNull(result);
        assertNull(result.getSaleDisplayDTO());
    }

    /**
     * 测试异常场景：compositeAtomService.querySaleDisplay 抛出异常
     */
    @Test
    public void testDoFetchExceptionCase() throws Throwable {
        // arrange
        CompletableFuture<Map<SalesSubjectParam, SalesDisplayInfoDTO>> failedFuture = new CompletableFuture<>();
        failedFuture.completeExceptionally(new RuntimeException("Service failed"));
        when(compositeAtomService.querySaleDisplay(any(SalesDisplayQueryRequest.class))).thenReturn(failedFuture);
        // act & assert
        CompletableFuture<ProductSaleReturnValue> future = productSaleFetcher.doFetch();
        Exception exception = assertThrows(ExecutionException.class, future::get);
        assertTrue(exception.getCause() instanceof RuntimeException);
        assertEquals("Service failed", exception.getCause().getMessage());
    }
}
