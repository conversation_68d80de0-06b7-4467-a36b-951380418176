package com.sankuai.dzshoppingguide.product.detail.application.utils;

import com.dianping.piccentercloud.display.api.PictureUrlGenerator;
import com.dianping.piccentercloud.display.api.PictureVisitParams;
import com.dianping.piccentercloud.display.api.enums.PictureVisitPattern;
import com.dianping.piccentercloud.display.api.enums.WaterMark;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

@Slf4j
public class ImageUtil {

    private static final String MT_BIZ = "mc";
    private static final String DP_BIZ = "pc";

    public static String format(String key, int width, int height, boolean isMt) {
        if (StringUtils.isEmpty(key)) {
            return key;
        }
        WaterMark waterMark = isMt ? WaterMark.MEITUAN : WaterMark.DIANPING;
        String biz = isMt ? MT_BIZ : DP_BIZ;
        PictureVisitParams params = new PictureVisitParams(biz, key, 1, 1, width, height, waterMark);
        PictureUrlGenerator pictureUrlGenerator = new PictureUrlGenerator(params, PictureVisitPattern.COMPATIBLE);
        try {
            return pictureUrlGenerator.getPictureURLWithHTTPSProtocol();
        } catch (Exception e) {
            log.error("ImageUtil.format error, key = {}, error = ", key, e);
        }
        return key;
    }

}
