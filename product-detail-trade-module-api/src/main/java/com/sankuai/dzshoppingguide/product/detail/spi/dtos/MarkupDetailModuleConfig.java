package com.sankuai.dzshoppingguide.product.detail.spi.dtos;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Author: <EMAIL>
 * @Date: 2025/3/3
 */
@Data
@TypeDoc(description = "加价详情模块配置信息")
@MobileDo(id = 0xf9a0)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MarkupDetailModuleConfig implements Serializable {

    @FieldDoc(description = "标题")
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    @FieldDoc(description = "弹框模型信息")
    @MobileDo.MobileField(key = 0x76f2)
    private MarkupDetailPopup popup;

    @FieldDoc(description = "提单跳链")
    @MobileDo.MobileField(key = 0x774e)
    private String jumpUrl;
}
