package com.sankuai.dzshoppingguide.product.detail.application.fetcher.pinpool;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;


/**
 * <AUTHOR>
 * @date 2025/04/03
 * 多产品团单结果类
 */
@Data
public class SimilarDealCacheResult extends FetcherReturnValueDTO {
    /**
     * 是否有多产品团单id缓存
     */
    private Boolean hasSimilarDealId;

    public SimilarDealCacheResult(String dealId) {
        this.hasSimilarDealId = StringUtils.isNotEmpty(dealId);
    }

    /**
     * 是否有多产品团单id缓存
     */
    public Boolean hasSimilarDeal() {
        return hasSimilarDealId;
    }
}