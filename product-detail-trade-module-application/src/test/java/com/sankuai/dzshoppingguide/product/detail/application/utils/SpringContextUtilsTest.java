package com.sankuai.dzshoppingguide.product.detail.application.utils;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.springframework.beans.factory.BeanNotOfRequiredTypeException;
import org.springframework.beans.factory.NoSuchBeanDefinitionException;
import org.springframework.context.ApplicationContext;

class SpringContextUtilsTest {

    /**
     * Test successful retrieval of a bean
     */
    @Test
    public void testGetBeanSuccessful() throws Throwable {
        // arrange
        ApplicationContext mockContext = mock(ApplicationContext.class);
        String beanName = "testBean";
        TestBean expectedBean = new TestBean();
        when(mockContext.getBean(beanName, TestBean.class)).thenReturn(expectedBean);
        SpringContextUtils springContextUtils = new SpringContextUtils();
        springContextUtils.setApplicationContext(mockContext);
        // act
        TestBean result = SpringContextUtils.getBean(beanName, TestBean.class);
        // assert
        assertNotNull(result);
        assertEquals(expectedBean, result);
    }

    /**
     * Test when APPLICATION_CONTEXT is null
     */
    @Test
    public void testGetBeanWithNullApplicationContext() throws Throwable {
        // arrange
        SpringContextUtils springContextUtils = new SpringContextUtils();
        springContextUtils.setApplicationContext(null);
        // act
        TestBean result = SpringContextUtils.getBean("testBean", TestBean.class);
        // assert
        assertNull(result);
    }

    /**
     * Test when bean with given name doesn't exist
     */
    @Test
    public void testGetBeanNonExistent() throws Throwable {
        // arrange
        ApplicationContext mockContext = mock(ApplicationContext.class);
        String beanName = "nonExistentBean";
        when(mockContext.getBean(beanName, TestBean.class)).thenThrow(new NoSuchBeanDefinitionException(beanName));
        SpringContextUtils springContextUtils = new SpringContextUtils();
        springContextUtils.setApplicationContext(mockContext);
        // act
        TestBean result = SpringContextUtils.getBean(beanName, TestBean.class);
        // assert
        assertNull(result);
    }

    /**
     * Test when bean exists but is not of the required type
     */
    @Test
    public void testGetBeanWrongType() throws Throwable {
        // arrange
        ApplicationContext mockContext = mock(ApplicationContext.class);
        String beanName = "wrongTypeBean";
        when(mockContext.getBean(beanName, TestBean.class)).thenThrow(new BeanNotOfRequiredTypeException(beanName, TestBean.class, String.class));
        SpringContextUtils springContextUtils = new SpringContextUtils();
        springContextUtils.setApplicationContext(mockContext);
        // act
        TestBean result = SpringContextUtils.getBean(beanName, TestBean.class);
        // assert
        assertNull(result);
    }

    // Helper class for testing
    private static class TestBean {
    }
}
