package com.sankuai.dzshoppingguide.product.detail.application.utils.richText;

import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class RichTextUtil {
    public static RichText buildByRegex(RichText.TextItem defaultTextItemTemplate, RichText.TextItem hltTextItemTemplate, String regexText, String sourceText) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.util.richText.RichTextUtil.buildByRegex(RichText$TextItem,RichText$TextItem,String,String)");
        List<RichText.TextItem> textItemList = Lists.newArrayList();

        if (StringUtils.isEmpty(sourceText)) {
            return new RichText();
        }
        Pattern pattern = Pattern.compile(regexText);
        Matcher matcher = pattern.matcher(sourceText);
        boolean isFind = false;
        int lastMatchEnd = 0;
        while (matcher.find()) {
            isFind = true;
            int start = matcher.start();
            int end = matcher.end();
            if (start > lastMatchEnd || start == 0) {
                if (start != 0) {
                    RichText.TextItem defaultTextItem = defaultTextItemTemplate.clone();
                    defaultTextItem.setText(sourceText.substring(lastMatchEnd, start));
                    textItemList.add(defaultTextItem);
                }
                RichText.TextItem hltTextItem = hltTextItemTemplate.clone();
                hltTextItem.setText(sourceText.substring(start, end));
                textItemList.add(hltTextItem);
                lastMatchEnd = end;
            }
        }
        if (isFind && lastMatchEnd < sourceText.length()) {
            RichText.TextItem defaultTextItem = defaultTextItemTemplate.clone();
            defaultTextItem.setText(sourceText.substring(lastMatchEnd));
            textItemList.add(defaultTextItem);
        }
        if (!isFind) {
            RichText.TextItem defaultTextItem = defaultTextItemTemplate.clone();
            defaultTextItem.setText(sourceText);
            textItemList.add(defaultTextItem);
        }

        RichText richText = new RichText();
        richText.setTextItemList(textItemList);
        return richText;
    }

    public static RichText.TextItem buildTextItem(String text,RichText.TextItem template){
        RichText.TextItem textItem=template.clone();
        textItem.setText(text);
        return textItem;
    }

}
