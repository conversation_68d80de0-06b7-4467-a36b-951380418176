package com.sankuai.dzshoppingguide.product.detail.spi.promo.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.dz.product.detail.gateway.spi.vo.AbstractModuleVO;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2025/3/3 15:00
 */
@Data
@MobileDo(id = 0x63b9)
public class PromoDetailVO extends AbstractModuleVO {

    @FieldDoc(description = "优惠栏模块")
    @MobileField(key = 0xd48c)
    private List<PromotionBarModule> promotionBar;

    @FieldDoc(description = "优惠明细")
    @MobileDo.MobileField(key = 0xc10c)
    private PromoDetailModule promoDetails;


    @FieldDoc(description = "优惠浮层模块")
    @MobileField(key = 0x4dcd)
    private PromotionPopUpModule promotionPopUpModule;

    @FieldDoc(description = "模块名")
    @MobileDo.MobileField(key = 0xb308)
    private String moduleName;

    private String moduleKeyExtend;

    @Override
    public String getModuleKey() {
        if (StringUtils.isNotBlank(moduleKeyExtend)){
            return moduleKeyExtend;
        }
        return ModuleKeyConstants.PRICE_DISCOUNT_DETAIL_MODULE;
    }
}