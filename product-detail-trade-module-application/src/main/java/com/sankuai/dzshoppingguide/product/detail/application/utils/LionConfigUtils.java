
package com.sankuai.dzshoppingguide.product.detail.application.utils;

import com.dianping.lion.client.Lion;
import com.google.common.collect.Lists;
import com.sankuai.dzshoppingguide.product.detail.application.builder.membercard.impl.ReserveMemberCardBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.pricebar.config.IconConfig;
import com.sankuai.dzshoppingguide.product.detail.application.builder.pricebar.config.ProductPriceConfig;
import com.sankuai.dzshoppingguide.product.detail.application.builder.pricebar.config.ProductPricePowerTagConfig;
import com.sankuai.dzshoppingguide.product.detail.application.builder.promodetail.config.PromotionBarConfig;
import com.sankuai.dzshoppingguide.product.detail.application.builder.promodetail.config.PromotionPopUpConfig;
import com.sankuai.dzshoppingguide.product.detail.application.constants.LionConstants;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.model.CountrySubsidiesConfig;
import com.sankuai.dzshoppingguide.product.detail.application.model.MemberFreeConfig;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

import static com.sankuai.dzshoppingguide.product.detail.application.constants.LionConstants.*;
import static com.sankuai.dzshoppingguide.product.detail.domain.utils.LionConstants.*;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/2/6 16:05
 */
@Slf4j
public class LionConfigUtils {
    /**
     * 是否命中 神会员全聚合逻辑
     * @return
     */
    public static boolean hitCouponAggregateSwitch(long userId) {
        List<Long> whiteList = Lion.getList("com.sankuai.dzu.tpbase.dztgdetailweb", MAGIC_MEMBER_COUPON_AGGREGATE_WHITE_LIST, Long.class, Collections.emptyList());
        if(CollectionUtils.isNotEmpty(whiteList) && whiteList.contains(userId)) {
            // 白名单
            return true;
        }

        // userid 比例放量
        int ratio = Lion.getInt("com.sankuai.dzu.tpbase.dztgdetailweb", MAGIC_MEMBER_COUPON_AGGREGATE_USER_ID_RATIO, 0);
        return ratio > userId%100 ;
    }

    public static String getNSelectOneGovernmentCouponMrnVersion() {
        return Lion.getString(TRADE_MODULE_APPKEY, LionConstants.N_SELECT_ONE_GOVERNMENT_COUPON_MRN_VERSION, "0.5.8");
    }

    public static boolean isMtLiveMiniAppInternal() {
        return Lion.getBoolean("com.sankuai.dzu.tpbase.dztgdetailweb", LionConstants.ENABLE_MT_LIVE_WEIXIN_MINIAPP_INTERNAL, true);
    }

    public static boolean hitLeadsDeal(DealGroupCategoryDTO categoryDTO, String lionConfigKey ) {

        String categoryId = Objects.nonNull(categoryDTO.getCategoryId()) ? categoryDTO.getCategoryId().toString() : StringUtils.EMPTY;
        String key = String.format("%s.%s", categoryDTO.getCategoryId(), categoryDTO.getServiceTypeId());
        List<String> leadsDealCats = Lion.getList("com.sankuai.dzu.tpbase.dztgdetailweb", lionConfigKey, String.class, Collections.emptyList());
        if (CollectionUtils.isEmpty(leadsDealCats)) {
            return false;
        }
        return leadsDealCats.contains(key) || leadsDealCats.contains(categoryId);
    }

    /**
     * 获取多sku过滤开关
     * @return
     */
    public static boolean dealsFilterSwitch() {
        return Lion.getBoolean(TRADE_MODULE_APPKEY, LionConstants.DEALS_FILTER_SWITCH, false);
    }

    public static boolean isShowCreateOrderLayer(int categoryId, String serviceType) {
        Map<String, MultiServiceTypeSwitchConfig> config = Lion.getMap("com.sankuai.dzu.tpbase.dztgdetailweb",
                LionConstants.SHOW_CREATE_ORDER_LAYER_DEAL_CATEGORY_SERVICE_TYPE_WHITE, MultiServiceTypeSwitchConfig.class, new HashMap<>());
        MultiServiceTypeSwitchConfig currentConfig = config.get(String.valueOf(categoryId));
        if (currentConfig == null) {
            return false;
        }
        return currentConfig.isMultiSku(serviceType);
    }

    public static ReserveMemberCardBuilder.MidReserveConfig getMiddleReserveConfig() {
        return Lion.getBean(RESERVE_APPKEY, STOCK_GRANULARITY, ReserveMemberCardBuilder.MidReserveConfig.class);
    }

    /**
     * 判断保洁自营门店全链路 是否需要替换 IM url 为 太平洋智能门户
     * @return true 需要替换
     */
    public static boolean needReplaceImToCsForCleaning() {
        try {
            return Lion.getBoolean("dztrade-mapi-web", "dztrade-mapi-web.cleaning.self.own.csUrl.switch", false);
        } catch (Exception e) {
            log.error("needReplaceImToCsForCleaning error", e);
            return false;
        }
    }

    /**
     * 判断无忧通门店全链路 是否需要替换 IM url 为 太平洋智能门户
     * @return true 需要替换
     */
    public static boolean needReplaceImToCsForCarefree() {
        try {
            return Lion.getBoolean("dztrade-mapi-web", "dztrade-mapi-web.carefree.self.own.csUrl.switch", false);
        } catch (Exception e) {
            log.error("needReplaceImToCsForCarefree error", e);
            return false;
        }
    }

    /**
     *  判断是否为教育在线类团购
     * @param categoryId
     * @param serviceTypeId
     * @return
     */
    public static boolean isEduOnlineDeal(int categoryId, Long serviceTypeId) {
        return serviceTypeId != null && getEduOnlineDealServiceLeafIds().contains(serviceTypeId);
    }

    public static List<Long> getEduOnlineDealServiceLeafIds() {
        return Lion.getList("com.sankuai.dzu.tpbase.dztgdetailweb",
                LionConstants.EDU_ONLINE_DEAL_SERVICE_LEAF_IDS, Long.class, Lists.newArrayList(134013L, 123020L));
    }

    /**
     * 神会员感知增强-引导使用，商品价格区间&神券面额配置
     * @return
     */
    public static List<ProductPriceConfig> getProductPriceConfig() {
        return Lion.getList(TRADE_MODULE_APPKEY, MAGICAL_MEMBER_COUPON_GUIDE_FAKE_RECEIVE_CONFIG, ProductPriceConfig.class, Collections.emptyList());
    }

    /**
     * 神会员感知增强，icon配置
     * @return
     */
    public static IconConfig getIconConfig() {
        return Lion.getBean(TRADE_MODULE_APPKEY, MAGICAL_MEMBER_COUPON_ICON_CONFIG, IconConfig.class);
    }

    /**
     * 神会员感知增强V2，引导购买，icon配置
     * @return
     */
    public static IconConfig getGuidePurcahseIconConfig() {
        return Lion.getBean(TRADE_MODULE_APPKEY, MAGICAL_MEMBER_COUPON_GUIDE_PURCHASE_ICON_CONFIG, IconConfig.class);
    }
    /**
     * 神会员感知增强V2，引导膨胀，icon配置
     * @return
     */
    public static IconConfig getGuideInflateIconConfig() {
        return Lion.getBean(TRADE_MODULE_APPKEY, MAGICAL_MEMBER_COUPON_GUIDE_INFLATE_ICON_CONFIG, IconConfig.class);
    }

    /**
     * 神会员感知增强，文本展示信息配置
     * @return
     */
    public static Map<String,List> getTextDisplayInfoConfig() {
         return Lion.getMap(TRADE_MODULE_APPKEY, MAGICAL_MEMBER_COUPON_TEXT_DISPLAY_INFO_CONFIG, List.class, Collections.emptyMap());
    }

    /**
     * 优惠浮层过滤与排序配置
     */
    public static PromotionPopUpConfig getPromotionPopUpConfig() {
        return Lion.getBean(TRADE_MODULE_APPKEY, PROMOTION_POP_UP_COUPON_FILTER_SORT, PromotionPopUpConfig.class);
    }

    /**
     * 领券栏过滤与排序配置
     */
    public static PromotionBarConfig getPromotionBarConfig() {
        return Lion.getBean(TRADE_MODULE_APPKEY, PROMOTION_BAR_FILTER_SORT, PromotionBarConfig.class);
    }

    /**
     * 判断是否为 leads 类团购
     * @param categoryId
     * @return
     */
    public static boolean isLeadsDealCateGoryId(int categoryId) {
        List<Integer> leadDealCategoryIds = Lion.getList(DZTGDETAIL_APPKEY, LEADS_DEAL_CATEGORY_IDS, Integer.class, Collections.emptyList());
        return leadDealCategoryIds.contains(categoryId);
    }

    /**
     * 获取玩法id
     * @return
     */
    public static Long getPlayId(){
        return Lion.getLong(DZTGDETAIL_APPKEY, PLAY_ID, 1000000000438002L);
    }

    /**
     * 判断是否为 国家补贴类目
     * @param productBaseInfo
     * @return
     */
    public static boolean isCountrySubsidyDealServiceTypeId(ProductBaseInfo productBaseInfo) {
        if (Objects.isNull(productBaseInfo) || Objects.isNull(productBaseInfo.getCategory())) {
            return false;
        }
        Long serviceTypeId = productBaseInfo.getCategory().getServiceTypeId();
        List<Long> serviceTypeIds = Lion.getList(DZTGDETAIL_APPKEY, COUNTRY_SUBSIDY_SERVICE_TYPE_IDS, Long.class, Collections.emptyList());
        return serviceTypeIds.contains(serviceTypeId);
    }

    /**
     * 非屏蔽渠道
     * @param requestSource
     * @return
     */
    public static boolean isHidePageSource(String requestSource) {
        List<String> hidePageSources = Lion.getList(DZTGDETAIL_APPKEY, MAGIC_COUPON_ENHANCEMENT_HIDE_PAGE_SOURCE, String.class, Collections.emptyList());
        return hidePageSources.contains(requestSource);
    }

    /**
     *  判断是否为结婚 leads 类团购
     * @param categoryDTO
     * @return
     */
    public static boolean hitWeddingLeadsDeal(DealGroupCategoryDTO categoryDTO) {
        return hitLeadsDealByLion(categoryDTO, WEDDING_LEADS_DEAL_CATE_CONFIG);
    }


    public static boolean hitLeadsDealByLion(DealGroupCategoryDTO categoryDTO, String lionConfigKey) {
        if (Objects.isNull(categoryDTO)) {
            return false;
        }
        String categoryId = Objects.nonNull(categoryDTO.getCategoryId()) ? categoryDTO.getCategoryId().toString() : StringUtils.EMPTY;
        String key = String.format("%s.%s", categoryDTO.getCategoryId(), categoryDTO.getServiceTypeId());
        List<String> leadsDealCats = Lion.getList(DZTGDETAIL_APPKEY, lionConfigKey, String.class, Collections.emptyList());
        if (CollectionUtils.isEmpty(leadsDealCats)) {
            return false;
        }
        return leadsDealCats.contains(key) || leadsDealCats.contains(categoryId);
    }

    public static List<Integer> getMagicCouponEnhancementCityBlackList(boolean isMt) {
        Map<String, List> cityBlackListMap = Lion.getMap(DZTGDETAIL_APPKEY, MAGIC_COUPON_ENHANCEMENT_CITY_BLACK_LIST, List.class, Collections.emptyMap());
        return (List<Integer>)cityBlackListMap.getOrDefault(isMt ? "mt" : "dp", Collections.emptyList());
    }

    public static List<ProductPricePowerTagConfig> getProductPricePowerTagConfig() {
        return Lion.getList(TRADE_MODULE_APPKEY, PRODUCT_PRICE_POWER_TAG_CONFIG, ProductPricePowerTagConfig.class, Collections.emptyList());
    }

    public static Map<String, List> getCountrySubsidiesProductId2GovCodeMap() {
        return Lion.getMap(TRADE_MODULE_APPKEY, COUNTRY_SUBSIDIES_PRODUCT_ID_2_GOV_CODE_MAP, List.class, Collections.emptyMap());
    }

    public static List<String> getCountrySubsidiesProduct2SkuAttrMap(Integer serviceTypeId) {
        if (serviceTypeId == null || serviceTypeId <= 0) {
            return Collections.emptyList();
        }
        Map<String, List> countrySubsidiesProduct2SkuAttrMap = Lion.getMap(TRADE_MODULE_APPKEY, COUNTRY_SUBSIDIES_PRODUCT_2_SKU_ATTR_MAP, List.class, Collections.emptyMap());
        return countrySubsidiesProduct2SkuAttrMap.get(String.valueOf(serviceTypeId));
    }

    public static Map<String, String> getCountrySubsidiesUrl() {
        return Lion.getMap(TRADE_MODULE_APPKEY, COUNTRY_SUBSIDIES_URL, String.class, Collections.emptyMap());
    }

    public static MemberFreeConfig getMemberFreeConfig() {
        return Lion.getBean(DZTGDETAIL_APPKEY, MEMBER_FREE_CONFIG, MemberFreeConfig.class);
    }

    public static CountrySubsidiesConfig getCountrySubsidiesConfig() {
        return Lion.getBean(TRADE_MODULE_APPKEY, COUNTRY_SUBSIDIES_CONFIG, CountrySubsidiesConfig.class);
    }

    public static List<Integer> getCountrySubsidiesServiceTypeIds() {
        return Lion.getList(DZTGDETAIL_APPKEY, COUNTRY_SUBSIDIES_SERVICE_TYPE_IDS, Integer.class, Collections.emptyList());
    }

    // 一品多态开关
    public static boolean getOneProductMultiSwitch() {
        return Lion.getBoolean(COMMON_MODULE_APPKEY, ONE_PRODUCT_MULTI_SWITCH, false);
    }

    /**
     * 获取神券强化感知 购神券包Icon配置
     * @return
     */
    public static Map<String, Object> getMagicPackageCouponIconConfig() {
        return Lion.getMap(TRADE_MODULE_APPKEY, MAGIC_COUPON_PROMOTION_BAR_PACKAGE_ICON_CONFIG, Object.class, Collections.emptyMap());
    }

    /**
     * 多买多省trafficFlag白名单
     * @return
     */
    public static List<String> getBuyMoreTrafficFlagWhiteList() {
        return Lion.getList(TRADE_MODULE_APPKEY, BUY_MORE_TRAFFIC_FLAG_WHITE_LIST, String.class, new ArrayList<>());
    }

    /**
     * 渠道映射成交易侧trafficFlag
     * @return
     */
    public static Map<String, String> getSourceToTrafficFlagMap() {
        return Lion.getMap(DZTGDETAIL_APPKEY, PAGE_SOURCE_TO_ORDER_TRAFFIC_FLAG, String.class, new HashMap<>());
    }

}