package com.sankuai.dzshoppingguide.product.detail.spi.tyingsale;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@MobileDo(id = 0xedf0)
@Data
public class BuyMoreSaveMoreCardVO implements Serializable {
    /**
     * 组合Id
     */
    @MobileField(key = 0xa66)
    private String combinationId;

    /**
     * 卡片组合id
     */
    @MobileField(key = 0xae37)
    private int cardId;

    /**
     * 搭售品类型：0-餐团、1-综团、2-标品
     */
    @MobileField(key = 0x7a22)
    private int bindingProductType;

    /**
     * 主品类型：0-餐团、1-综团、2-标品
     */
    @MobileField(key = 0xc159)
    private int mainProductType;

    /**
     * 搭售商品来源：1-算法组品，2-运营组品
     */
    @MobileField(key = 0x6764)
    private int bindingSource;

    /**
     * 组合价格文案
     */
    @MobileField(key = 0x6ea6)
    private String cardPriceText;

    /**
     * 主品团单id
     */
    @MobileField(key = 0x9c82)
    private int mainDealId;

    /**
     * 待领券信息
     */
    @MobileField(key = 0x1ddb)
    private List<CouponDescItem> couponDescItems;

    /**
     * 搭售品团单id
     */
    @MobileField(key = 0x6fb9)
    private int bindingDealId;

    /**
     * 组合卡片标题
     */
    @MobileField(key = 0xd1ed)
    private String cardTitle;

    /**
     * 跳转提单页链接
     */
    @MobileField(key = 0x4d40)
    private String buyButtonJumpUrl;

    /**
     * 下单按钮文案
     */
    @MobileField(key = 0xdc07)
    private String buyButtonText;

    /**
     * 优惠力度
     */
    @MobileField(key = 0xbfe)
    private String discountPriceDesc;

    /**
     * 搭售品跳链
     */
    @MobileField(key = 0x6b23)
    private String bindingDealJumpUrl;

    /**
     * 销量
     */
    @MobileField(key = 0x4f8e)
    private String sales;

    /**
     * 组合价格
     */
    @MobileField(key = 0x4089)
    private String cardPrice;

    /**
     * 搭售品到手价
     */
    @MobileField(key = 0x601f)
    private String bindingDealPrice;

    /**
     * 当前商品tag
     */
    @MobileField(key = 0x6484)
    private String mainDealTag;

    /**
     * 主品到手价
     */
    @MobileField(key = 0x851a)
    private String mainDealPrice;

    /**
     * 搭售品副标题
     */
    @MobileField(key = 0x9ec4)
    private String bindingDealSubTitle;

    /**
     * 搭售品主标题
     */
    @MobileField(key = 0xef1b)
    private String bindingDealTitle;

    /**
     * 搭售商品头图
     */
    @MobileField(key = 0x31d3)
    private String bindingDealHeaderImage;

    /**
     * 主商品头图
     */
    @MobileField(key = 0xee99)
    private String mainDealHeaderImage;

}