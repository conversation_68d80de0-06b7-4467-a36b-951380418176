package com.sankuai.dzshoppingguide.product.detail.application.fetcher.price;

import com.sankuai.dealuser.price.display.api.enums.PromoTypeEnum;
import com.sankuai.dealuser.price.display.api.enums.RequestSceneEnum;
import com.sankuai.dealuser.price.display.api.model.BatchPriceRequest;
import com.sankuai.dealuser.price.display.api.model.ClientEnv;
import com.sankuai.dealuser.price.display.api.model.ProductIdentity;
import com.sankuai.dealuser.price.display.api.model.PromoIdentity;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.dto.ProductPriceReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.defaultselect.SkuDefaultSelect;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.defaultselect.SkuDefaultSelectFetcher;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @date 2025-03-06
 * @desc 闲时优惠价格查询
 */
@Fetcher(
        previousLayerDependencies = {SkuDefaultSelectFetcher.class}
)
public class ProductIdlePromoPriceFetcher extends AbstractProductPriceFetcher<ProductPriceReturnValue> {

    @Override
    protected CompletableFuture<ProductPriceReturnValue> doFetch() {
        SkuDefaultSelect skuDefaultSelect = getDependencyResult(SkuDefaultSelectFetcher.class);
        BatchPriceRequest priceRequest = buildBatchPriceRequest(request, skuDefaultSelect);
        priceRequest.setScene(RequestSceneEnum.DETAIL_CARD_FUSION.getScene());
        // 闲时优惠
        PromoIdentity idlePromo = new PromoIdentity(0, PromoTypeEnum.IDLE_PROMO.getType());
        priceRequest.setSpecPromo(idlePromo);
        return getProductPriceDisplayDTO(priceRequest).thenApply(ProductPriceReturnValue::new);
    }

    @Override
    protected void postProcessPriceRequest(ClientEnv clientEnv, List<ProductIdentity> productIdentityList, Map<String, String> extension) {
    }
}
