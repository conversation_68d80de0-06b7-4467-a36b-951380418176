package com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.component.trade.block.buy.button.cepintuan;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.component.trade.block.utils.PriceFormatUtils;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.exception.TradeButtonBuildFatalException;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.cepinpool.dto.CostEffectivePinTuan;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sale.info.enums.ProductSaleStatusEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.enums.OpenTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.extend.buy.BuyActionVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.component.trade.button.StandardTradeButtonVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.backgroud.BottomBarBackgroundVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.constant.ButtonStyle;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.rich.content.RichContentVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.rich.content.TextRichContentVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.rich.content.enums.TextStyleEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.tag.ButtonTopTagVO;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;

import javax.annotation.Nullable;
import java.util.List;

import static com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.constant.ButtonStyleConstant.*;

/**
 * @Author: guangyujie
 * @Date: 2025/3/20 17:52
 */
@Slf4j
public class CEPTPinTuanBuyButtonComponent {

    public static StandardTradeButtonVO build(final ProductDetailPageRequest request,
                                              @Nullable final PriceDisplayDTO costEffectivePrice,
                                              @Nullable final String CEPinTuanOrderPageUrl,
                                              @NotNull final CostEffectivePinTuan costEffectivePinTuan,
                                              final ProductSaleStatusEnum saleStatus) {
        try {
            StandardTradeButtonVO button = new StandardTradeButtonVO();
            button.setDisable(!saleStatus.isForSale());
            button.setActionData(new BuyActionVO(OpenTypeEnum.modal, CEPinTuanOrderPageUrl));
            if (costEffectivePinTuan.enhancedStyle()) {
                button.setMainTitle(buildMainTitle(CE_PIN_TUAN_STRONG_BUTTON, costEffectivePrice, costEffectivePinTuan));
                button.setBackground(BottomBarBackgroundVO.buildVerticalGradientColor(
                        CE_PIN_TUAN_STRONG_BUTTON.getBackGroundColors()
                ));
                if (!costEffectivePinTuan.isLimitNewCustomJoin()) {
                    button.setButtonTag(new ButtonTopTagVO(
                            Lists.newArrayList(new TextRichContentVO("所有好友均可参团", TextStyleEnum.Default, 24, CE_PIN_TUAN_STRONG_BUTTON_TAG.getTitleColor())),
                            BottomBarBackgroundVO.buildSingleColor(CE_PIN_TUAN_STRONG_BUTTON_TAG.getBackgroundSingleColor()),
                            1
                    ));
                }
            } else {
                button.setMainTitle(buildMainTitle(CE_PIN_TUAN_WEAK_BUTTON, costEffectivePrice, costEffectivePinTuan));
                button.setBackground(BottomBarBackgroundVO.buildSingleColorWithBorder(
                        CE_PIN_TUAN_WEAK_BUTTON.getBackgroundSingleColor(), CE_PIN_TUAN_WEAK_BUTTON.getTitleColor(), 2
                ));
                if (!costEffectivePinTuan.isLimitNewCustomJoin()) {
                    button.setButtonTag(new ButtonTopTagVO(
                            Lists.newArrayList(new TextRichContentVO("所有好友均可参团", TextStyleEnum.Default, 24, CE_PIN_TUAN_WEAK_BUTTON_TAG.getTitleColor())),
                            BottomBarBackgroundVO.buildSingleColor(CE_PIN_TUAN_WEAK_BUTTON_TAG.getBackgroundSingleColor()),
                            1
                    ));
                }
            }
            return button;
        } catch (Throwable throwable) {
            log.error("CEPTPinTuanBuyButtonComponent.build,request:{}",
                    JSON.toJSONString(request), new TradeButtonBuildFatalException(throwable));
            return null;
        }
    }

    private static List<RichContentVO> buildMainTitle(final ButtonStyle cePinTuanStrongButton,
                                                      final PriceDisplayDTO costEffectivePrice,
                                                      final CostEffectivePinTuan costEffectivePinTuan) {
        return Lists.newArrayList(
                new TextRichContentVO("¥", TextStyleEnum.Bold, 24, cePinTuanStrongButton.getTitleColor()),
                new TextRichContentVO(
                        PriceFormatUtils.getPlainString(costEffectivePrice),
                        TextStyleEnum.Bold, 48, cePinTuanStrongButton.getTitleColor()),
                new TextRichContentVO(
                        String.format(" %s人拼团", costEffectivePinTuan.getGroupSuccCountMin()),
                        TextStyleEnum.Bold, 32, cePinTuanStrongButton.getTitleColor())

        );
    }

}
