package com.sankuai.dzshoppingguide.product.detail.application.builder.similargoods;

import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.extend.BaseBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.similargoods.dtos.ShowLowPriceItemRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.abtest.AbTestReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.abtest.ComparePriceAbTestFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.dealproduct.DealProductFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.dealproduct.DealProductReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.haima.HaiMaFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.haima.HaiMaReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.dealgroup.DealGroupIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.dealgroup.DealGroupIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.shopid.ShopIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.shopid.ShopIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.best.shop.ShopInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.best.shop.ShopInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.pricerange.PriceRangeInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.pricerange.PriceRangeInfoReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.producttheme.OriginProductThemeFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.producttheme.ProductThemeResult;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.producttheme.SimilarRecommendProductThemeFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommendgoods.dtos.ProductM;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.sku.DealSkuSummaryFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.sku.DealSkuSummaryReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.FetcherDAGStarter;
import com.sankuai.dzshoppingguide.product.detail.application.service.dealtinyinfo.DealTinyInfoFacade;
import com.sankuai.dzshoppingguide.product.detail.application.service.dealtinyinfo.dto.DealLowPriceItemEntranceVO;
import com.sankuai.dzshoppingguide.product.detail.application.service.similarproduct.SimilarProductService;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.similargoods.vo.SimilarStyleGoods;
import com.sankuai.dzshoppingguide.product.detail.spi.similargoods.vo.dtos.DzProductVO;
import org.apache.commons.collections4.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @create 2025/5/20 17:03
 */
@Builder(
        moduleKey = ModuleKeyConstants.MODULE_DETAIL_DEAL_SIMILAR_STYLE_GOODS,
        startFetcher = FetcherDAGStarter.class,
        dependentFetchers = {
                SimilarRecommendProductThemeFetcher.class,
                OriginProductThemeFetcher.class,
                ShopInfoFetcher.class,
                ProductBaseInfoFetcher.class,
                ShopIdMapperFetcher.class,
                DealGroupIdMapperFetcher.class,
                ComparePriceAbTestFetcher.class,
                DealSkuSummaryFetcher.class,
                ComparePriceAbTestFetcher.class,
                PriceRangeInfoFetcher.class,
                HaiMaFetcher.class,
                DealProductFetcher.class
        }
)
public class SimilarPriceGoodsRecommendBuilder  extends BaseBuilder<SimilarStyleGoods> {

    private ProductThemeResult similarRecommendProducts = null;
    private ProductThemeResult originalProduct = null;
    private ShopInfo shopInfo = null;
    private ProductBaseInfo productBaseInfo = null;
    private ShopIdMapper shopIdMapper = null;
    private DealGroupIdMapper dealGroupIdMapper = null;
    private DealSkuSummaryReturnValue dealSkuSummaryReturnValue = null;
    private AbTestReturnValue comparePriceAbTest = null;
    private PriceRangeInfoReturnValue priceRangeInfoReturnValue = null;
    private HaiMaReturnValue haiMaReturnValue = null;
    private DealProductReturnValue dealProductReturnValue = null;

    @Resource
    private DealTinyInfoFacade dealTinyInfoFacade;

    @Resource
    private SimilarProductService similarProductService;

    @Resource
    private ListingProductsBuilder listingProductsBuilder;
    @Resource
    private ListingResponseBuilder responseBuilder;

    @Override
    public SimilarStyleGoods doBuild() {
        similarRecommendProducts = getDependencyResult(SimilarRecommendProductThemeFetcher.class);
        originalProduct = getDependencyResult(OriginProductThemeFetcher.class);
        shopInfo = getDependencyResult(ShopInfoFetcher.class);
        productBaseInfo = getDependencyResult(ProductBaseInfoFetcher.class);
        shopIdMapper = getDependencyResult(ShopIdMapperFetcher.class);
        dealGroupIdMapper = getDependencyResult(DealGroupIdMapperFetcher.class);
        dealSkuSummaryReturnValue = getDependencyResult(DealSkuSummaryFetcher.class);
        comparePriceAbTest = getDependencyResult(ComparePriceAbTestFetcher.class);
        priceRangeInfoReturnValue = getDependencyResult(PriceRangeInfoFetcher.class);
        haiMaReturnValue = getDependencyResult(HaiMaFetcher.class);
        dealProductReturnValue = getDependencyResult(DealProductFetcher.class);

        ShowLowPriceItemRequest lowPriceItemRequest = ShowLowPriceItemRequest.builder()
                .request(request)
                .shopInfo(shopInfo)
                .productBaseInfo(productBaseInfo)
                .shopIdMapper(shopIdMapper)
                .dealGroupIdMapper(dealGroupIdMapper)
                .abTestResult(Objects.nonNull(comparePriceAbTest) ? comparePriceAbTest.getAbTestResult() : null)
                .batchPriceRangeInfoResponse(Objects.nonNull(priceRangeInfoReturnValue) ? priceRangeInfoReturnValue.getBatchPriceRangeInfoResponse() : null)
                .skuSummary(Objects.nonNull(dealSkuSummaryReturnValue) ? dealSkuSummaryReturnValue.getDealSkuSummaryDTO() : null)
                .dealProductResult(Objects.nonNull(dealProductReturnValue) ? dealProductReturnValue.getDealProductResult() : null)
                .hitShopInBlackList(Objects.nonNull(haiMaReturnValue) ? haiMaReturnValue.isBlackListShop() : false)
                .build();
        DealLowPriceItemEntranceVO showResult= dealTinyInfoFacade.isShowLowPriceItemEntrance(lowPriceItemRequest);
        // 判断是否展示同款好价
        if(Objects.nonNull(showResult) && !showResult.isShowLowPriceDealList() ) {
            return null;
        }
        ProductM originalProductM = Objects.nonNull(originalProduct) && CollectionUtils.isNotEmpty(originalProduct.getProducts()) ? originalProduct.getProducts().get(0) : null;
        List<ProductM> recommendProductMs = Objects.nonNull(similarRecommendProducts) && CollectionUtils.isNotEmpty(similarRecommendProducts.getProducts()) ? similarRecommendProducts.getProducts() : null;
        List<ProductM> recommendProductMList = similarProductService.filterProducts(request, originalProductM, recommendProductMs);
        List<DzProductVO> recommendProductVO = listingProductsBuilder.buildProductList(recommendProductMList, request);
        DzProductVO originalProductVO = listingProductsBuilder.buildDzProductVO(originalProductM, request);
        return responseBuilder.buildResponse(request, originalProductVO, recommendProductVO, similarRecommendProducts);
    }
}
