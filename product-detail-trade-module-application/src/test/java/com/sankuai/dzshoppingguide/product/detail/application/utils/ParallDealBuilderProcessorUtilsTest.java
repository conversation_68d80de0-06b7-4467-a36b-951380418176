package com.sankuai.dzshoppingguide.product.detail.application.utils;

import static org.junit.jupiter.api.Assertions.*;
import java.util.List;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
@DisplayName("ParallDealBuilderProcessorUtils Test")
public class ParallDealBuilderProcessorUtilsTest {

    @Test
    @DisplayName("Should return empty list when input is null")
    public void testFormatToRichText_WhenInputNull() throws Throwable {
        String input = null;
        int maxLines = 5;
        int maxCharsPerLine = 10;
        List<String> result = ParallDealBuilderProcessorUtils.formatToRichText(input, maxLines, maxCharsPerLine);
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    @DisplayName("Should return empty list when input is empty")
    public void testFormatToRichText_WhenInputEmpty() throws Throwable {
        String input = "";
        int maxLines = 5;
        int maxCharsPerLine = 10;
        List<String> result = ParallDealBuilderProcessorUtils.formatToRichText(input, maxLines, maxCharsPerLine);
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    @DisplayName("Should format simple text within line limits")
    public void testFormatToRichText_SimpleTextWithinLimits() throws Throwable {
        String input = "Hello World";
        int maxLines = 2;
        int maxCharsPerLine = 20;
        List<String> result = ParallDealBuilderProcessorUtils.formatToRichText(input, maxLines, maxCharsPerLine);
        assertEquals(1, result.size());
        assertEquals("Hello World", result.get(0));
    }

    @Test
    @DisplayName("Should split text when exceeding maxCharsPerLine")
    public void testFormatToRichText_ExceedingMaxCharsPerLine() throws Throwable {
        String input = "This is a long text that should be split";
        int maxLines = 3;
        int maxCharsPerLine = 10;
        List<String> result = ParallDealBuilderProcessorUtils.formatToRichText(input, maxLines, maxCharsPerLine);
        assertEquals(3, result.size());
        assertEquals("This is a ", result.get(0));
        assertEquals("long text ", result.get(1));
        assertEquals("that shoul", result.get(2));
    }

    @Test
    @DisplayName("Should handle price format correctly")
    public void testFormatToRichText_PriceFormat() throws Throwable {
        String input = "Price: 123.45元";
        int maxLines = 2;
        int maxCharsPerLine = 20;
        List<String> result = ParallDealBuilderProcessorUtils.formatToRichText(input, maxLines, maxCharsPerLine);
        assertEquals(1, result.size());
        assertEquals("Price: 123.45元", result.get(0));
    }

    @Test
    @DisplayName("Should handle multiple prices correctly")
    public void testFormatToRichText_MultiplePrices() throws Throwable {
        String input = "Price1: 99.9元 Price2: 199.9元";
        int maxLines = 3;
        int maxCharsPerLine = 15;
        List<String> result = ParallDealBuilderProcessorUtils.formatToRichText(input, maxLines, maxCharsPerLine);
        assertEquals(2, result.size());
        assertEquals("Price1: 99.9元 P", result.get(0));
        assertEquals("rice2: 199.9元", result.get(1));
    }

    @Test
    @DisplayName("Should handle Chinese punctuation correctly")
    public void testFormatToRichText_ChinesePunctuation() throws Throwable {
        String input = "你好，世界。";
        int maxLines = 2;
        int maxCharsPerLine = 10;
        List<String> result = ParallDealBuilderProcessorUtils.formatToRichText(input, maxLines, maxCharsPerLine);
        assertEquals(1, result.size());
        assertEquals("你好，世界。", result.get(0));
    }

    @Test
    @DisplayName("Should handle English punctuation correctly")
    public void testFormatToRichText_EnglishPunctuation() throws Throwable {
        String input = "Hello, World!";
        int maxLines = 2;
        int maxCharsPerLine = 6;
        List<String> result = ParallDealBuilderProcessorUtils.formatToRichText(input, maxLines, maxCharsPerLine);
        assertEquals(2, result.size());
        assertEquals("Hello,", result.get(0));
        assertEquals(" World!", result.get(1));
    }

    @Test
    @DisplayName("Should handle complex mixed content correctly")
    public void testFormatToRichText_ComplexMixedContent() throws Throwable {
        String input = "价格：99.9元，折扣价：88.8元！";
        int maxLines = 3;
        int maxCharsPerLine = 10;
        List<String> result = ParallDealBuilderProcessorUtils.formatToRichText(input, maxLines, maxCharsPerLine);
        assertEquals(2, result.size());
        assertEquals("价格：99.9元，折", result.get(0));
        assertEquals("扣价：88.8元！", result.get(1));
    }

    @Test
    @DisplayName("Should handle exact maxCharsPerLine correctly")
    public void testFormatToRichText_ExactMaxCharsPerLine() throws Throwable {
        String input = "1234567890";
        int maxLines = 2;
        int maxCharsPerLine = 10;
        List<String> result = ParallDealBuilderProcessorUtils.formatToRichText(input, maxLines, maxCharsPerLine);
        assertEquals(1, result.size());
        assertEquals("1234567890", result.get(0));
    }

    @Test
    @DisplayName("Should handle mixed punctuation and prices at line boundaries")
    public void testFormatToRichText_MixedContentAtLineBoundaries() throws Throwable {
        String input = "Price：99.9元，Sale：88.8元";
        int maxLines = 3;
        int maxCharsPerLine = 8;
        List<String> result = ParallDealBuilderProcessorUtils.formatToRichText(input, maxLines, maxCharsPerLine);
        assertEquals(3, result.size());
        assertEquals("Price：", result.get(0));
        assertEquals("99.9元，Sa", result.get(1));
        assertEquals("le：88.8元", result.get(2));
    }
}
