package com.sankuai.dzshoppingguide.product.detail.spi.instructions.dealstruct;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: <EMAIL>
 * @Date: 2024/1/12
 */
@Data
@TypeDoc(description = "展示内容项")
@MobileDo(id = 0x2eb9)
public class PnStandardDisplayValueDTO implements Serializable {

    /**
     *     TEXT(1, "普通文案"),
     *     RICH_TEXT(2, "富文本"),
     *     IMG(3, "图片"),
     *     TABLE(4, "表格(序列化的)"),
     *     CUSTOM_EXT_KEY(5, "自定义扩展属性key")
     */
    @FieldDoc(description = "值的类型：1普通文案 2富文本 3图片 4表格 5自定义扩展属性key")
    @MobileDo.MobileField(key = 0x270c)
    private Integer pnType;

    @FieldDoc(description = "具体值")
    @MobileDo.MobileField(key = 0x474b)
    private String pnValue;

    public PnStandardDisplayValueDTO() {
    }

    public PnStandardDisplayValueDTO(Integer pnType, String pnValue) {
        this.pnType = pnType;
        this.pnValue = pnValue;
    }
}
