package com.sankuai.dzshoppingguide.product.detail.application.service.promo;

import static org.junit.Assert.*;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class PromoDetailServiceBuildCouponGroupIdListTest {

    private PromoDetailService promoDetailService = new PromoDetailService();

    @Test
    public void testBuildCouponGroupIdListWhenCouponWalletInfoStrIsNull() {
        String couponWalletInfoStr = null;
        List<String> result = promoDetailService.buildCouponGroupIdList(couponWalletInfoStr);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testBuildCouponGroupIdListWhenCouponPackageListIsNull() {
        String couponWalletInfoStr = "{}";
        List<String> result = promoDetailService.buildCouponGroupIdList(couponWalletInfoStr);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testBuildCouponGroupIdListWhenJsonArrayIsEmpty() {
        String couponWalletInfoStr = "{\"couponPackageList\":[]}";
        List<String> result = promoDetailService.buildCouponGroupIdList(couponWalletInfoStr);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testBuildCouponGroupIdListWhenCouponPackageIdIsNull() {
        String couponWalletInfoStr = "{\"couponPackageList\":[{}]}";
        List<String> result = promoDetailService.buildCouponGroupIdList(couponWalletInfoStr);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testBuildCouponGroupIdListWhenCouponPackageIdIsNotNull() {
        String couponWalletInfoStr = "{\"couponPackageList\":[{\"couponPackageId\":\"1\"}]}";
        List<String> result = promoDetailService.buildCouponGroupIdList(couponWalletInfoStr);
        assertEquals(1, result.size());
        assertEquals("1", result.get(0));
    }

    @Test
    public void testBuildCouponGroupIdListWhenExceptionOccurs() {
        String couponWalletInfoStr = "invalid json";
        List<String> result = promoDetailService.buildCouponGroupIdList(couponWalletInfoStr);
        assertTrue(result.isEmpty());
    }
}
