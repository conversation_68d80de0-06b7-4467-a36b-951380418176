package com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.component.trade.block.buy.button.shop.member;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.sankuai.dealuser.price.display.api.enums.ExtendDisplayInfoKeyEnum;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.component.trade.block.buy.button.StandardTradeButtonComponent;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.component.trade.block.utils.PriceFormatUtils;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.component.trade.block.utils.TradeButtonBuildUtils;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.exception.TradeButtonBuildFatalException;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.dto.ProductPriceReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.purchase.coupon.PurchaseCouponReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sale.info.enums.ProductSaleStatusEnum;
import com.sankuai.dzshoppingguide.product.detail.application.service.dto.ShopMemberDetailV;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.extend.buy.BuyActionVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.component.trade.button.StandardTradeButtonVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import javax.annotation.Nullable;
import java.util.Map;
import java.util.Optional;

import static com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.constant.ButtonStyleConstant.SHOP_MEMBER_BUTTON_STYLE;


/**
 * @Author: guangyujie
 * @Date: 2025/3/17 15:15
 */
@Slf4j
public class ShopMemberTradeButtonComponent {

    public static StandardTradeButtonVO build(final ProductDetailPageRequest request,
                                              final ProductCategory productCategory,
                                              @Nullable final ProductPriceReturnValue normalPriceReturnValue,
                                              @Nullable final ProductPriceReturnValue promoPriceReturnValue,
                                              @Nullable final ShopMemberDetailV shopMemberDetailV,
                                              @Nullable final String orderPageUrl,
                                              @Nullable final PurchaseCouponReturnValue purchaseCouponReturnValue,
                                              final ProductSaleStatusEnum saleStatus) {
        try {
            if (displayInflateCoupon(normalPriceReturnValue)
                    || displayInflateCoupon(promoPriceReturnValue)) {
                //如果有神会员标签文案则不展示商家会员
                return null;
            }
            if (isPriceNotValidForMember(normalPriceReturnValue)
                    || isPriceNotValidForMember(promoPriceReturnValue)
                    || shopMemberDetailV == null) {
                //商家会员是最佳优惠组成 && 商家会员信息不为空
                return null;
            }
            PriceDisplayDTO normalPrice = normalPriceReturnValue.getPriceDisplayDTO();
            return TradeButtonBuildUtils.buildTradeButtonVO(
                    saleStatus,
                    PriceFormatUtils.formatPrice("会员价", normalPrice),
                    StandardTradeButtonComponent.buildSubTitle(purchaseCouponReturnValue),
                    SHOP_MEMBER_BUTTON_STYLE,
                    new BuyActionVO(
                            StandardTradeButtonComponent.getOrderPageOverlayType(request, productCategory.getProductSecondCategoryId()),
                            orderPageUrl,
                            Optional.ofNullable(purchaseCouponReturnValue)
                                    .map(PurchaseCouponReturnValue::getCouponGroupIdsForPurchase)
                                    .orElse(null)
                    )
            );
        } catch (Throwable throwable) {
            log.error("ShopMemberTradeButtonComponent.build,request:{}",
                    JSON.toJSONString(request), new TradeButtonBuildFatalException(throwable));
            return null;
        }
    }

    /**
     * 是否有神会员标签文案
     */
    private static boolean displayInflateCoupon(final ProductPriceReturnValue priceReturnValue) {
        if (priceReturnValue == null || priceReturnValue.getPriceDisplayDTO() == null) {
            return false;
        }

        Map<String, String> extendDisplayInfoMap = priceReturnValue.getPriceDisplayDTO().getExtendDisplayInfo();
        if (MapUtils.isEmpty(extendDisplayInfoMap)) {
            return false;
        }

        //神会员标签文案
        String magicalMemberCouponLabel = extendDisplayInfoMap.get(ExtendDisplayInfoKeyEnum.MAGICAL_MEMBER_COUPON_LABEL.getKey());
        return StringUtils.isNotBlank(magicalMemberCouponLabel);
    }

    private static boolean isPriceNotValidForMember(ProductPriceReturnValue priceReturnValue) {
        return priceReturnValue == null
                || !priceReturnValue.isMemberPromoPrice()//最佳优惠里需要有商家会员优惠
                || priceReturnValue.getPriceDisplayDTO() == null;
    }

    private static final Map<String, String> BTN_TITLE_MAP = Maps.newHashMap();

    static {
        BTN_TITLE_MAP.put("NEW_MEMBER_BENEFITS", "新会员价");
        BTN_TITLE_MAP.put("MEMBER_BENEFITS", "会员价");
    }

    private static PromoDTO getMemberPricePromoDTO(final PriceDisplayDTO promoPrice) {
        for (PromoDTO promoDTO : promoPrice.getUsedPromos()) {
            if (promoDTO.getIdentity() != null && BTN_TITLE_MAP.containsKey(promoDTO.getIdentity().getPromoShowType())) {
                return promoDTO;
            }
        }
        return null;
    }

}
