package com.sankuai.dzshoppingguide.product.detail.application.utils;

import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.dianping.poi.business.relation.util.GsonUtils;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.best.shop.ShopInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.dto.ProductIdDTO;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.defaultselect.SkuDefaultSelect;
import com.sankuai.dzshoppingguide.product.detail.application.utils.context.DealCtxUtils;
import com.sankuai.dzshoppingguide.product.detail.application.utils.enums.DealParamEnum;
import com.sankuai.dzshoppingguide.product.detail.application.utils.magic.MagicFlagUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.MpAppIdEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.RequestSourceEnum;
import com.sankuai.general.product.query.center.client.dto.deal.DealGroupDealDTO;
import lombok.extern.log4j.Log4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import static com.sankuai.dz.product.detail.RequestCustomParamEnum.dealparam;

@Log4j
public class UrlHelper {
    // 获取商户地址
    public static String getShopUrl(ProductDetailPageRequest request, long dpShopId, long mtShopId) {
        int clientType = request.getClientType();
        if (ClientTypeEnum.MT_APP.getCode() == clientType) {
            return String.format("imeituan://www.meituan.com/gc/poi/detail?id=%s", mtShopId);
        }
        if (ClientTypeEnum.DP_APP.getCode() == clientType) {
            return String.format("dianping://shopinfo?shopid=%s", dpShopId);
        }
        return null;
    }

    // 跳转首页
    public static String getHomeUrl(ProductDetailPageRequest request) {
        int clientType = request.getClientType();
        if (ClientTypeEnum.MT_APP.getCode() == clientType) {
            return String.format("imeituan://www.meituan.com/home");
        }
        if (ClientTypeEnum.DP_APP.getCode() == clientType) {
            return String.format("dianping://home");
        }
        return null;
    }

    public static String getShareUrl(ProductDetailPageRequest request, ShopIdMapper idMapper, ShopInfo shopInfo)  {
        int clientType = request.getClientType();
        if (ClientTypeEnum.MT_APP.getCode() == clientType) {
            long shopId = Objects.nonNull(idMapper) ? idMapper.getMtBestShopId() : 0L;
            return "https://w.dianping.com/cube/evoke/meituan.html?url=imeituan%3A%2F%2Fwww.meituan.com%2Fmrn%3Fmrn_biz%3Dgc%26mrn_entry%3Dmrn-gc-bookdetail%26mrn_component%3Dbookdetail%26poiId%3D" + shopId +  "%26productType%3D2%26productId%3D" + request.getProductId();
        }
        if (ClientTypeEnum.DP_APP.getCode() == clientType) {
            long shopId = Objects.nonNull(idMapper) ? idMapper.getDpBestShopId() : 0L;
            return "https://w.dianping.com/cube/evoke/dianping.html?url=dianping%3A%2F%2Fmrn%3Fmrn_biz%3Dgc%26mrn_entry%3Dmrn-gc-bookdetail%26mrn_component%3Dbookdetail%26poiId%3D" + shopId + "%26productType%3D2%26productId%3D" + request.getProductId();
        }
        return null;
    }

    public static String getIdleHoursBuyUrl(ProductDetailPageRequest request,SkuDefaultSelect skuDefaultSelect,ShopInfo shopInfo, ShopIdMapper shopIdMapper,ProductBaseInfo dealGroupBase, Integer cityId) {
        String url = getCommonBuyUrl(request,skuDefaultSelect, shopInfo, shopIdMapper,dealGroupBase, cityId, "");
        url += "&promosource=1";
        return url;
    }

    /**
     * 获取各站点普通购买地址，传参需要对应各个平台
     */
    public static String getCommonBuyUrl(ProductDetailPageRequest request,
                                         SkuDefaultSelect skuDefaultSelect,
                                         ShopInfo shopInfo,
                                         ShopIdMapper shopIdMapper,
                                         ProductBaseInfo dealGroupBase,
                                         int cityId,
                                         String finalPrice) {
        int clientType = request.getClientType();
        int dealId4P = Optional.ofNullable(dealGroupBase)
                .map(ProductBaseInfo::getProductIdDTO)
                .map(ProductIdDTO::getMtProductId)
                .map(Long::intValue)
                .orElse(0);
        long poiId4P = request.getPoiId();

        if (DealCtxUtils.isExternal(request.getClientTypeEnum())) {
            if (Objects.equals(request.getShepherdGatewayParam().getMpAppId(), MpAppIdEnum.MT_WEIXIN_MINIPROGRAM.getMpAppId())) {
                return getWxCommonBuyUrl(request.getPageSource(), dealId4P, cityId, request.getClientTypeEnum().isMtClientType(), poiId4P);
            }
            if(Objects.equals(request.getClientTypeEnum(), ClientTypeEnum.DP_BAIDU_MAP_XCX)) {
                return getBaiduDianpingMiniAppBuyUrl(shopInfo, shopIdMapper,request, dealGroupBase, finalPrice);
            }
            if(Objects.equals(request.getClientTypeEnum(), ClientTypeEnum.DP_XCX)) {
                return getDianpingMiniAppBuyUrl(shopInfo, shopIdMapper, request, dealGroupBase, finalPrice);
            }
            if(Objects.equals(request.getClientTypeEnum(), ClientTypeEnum.MT_MAP)) {
                return getMtAppCommonBuyUrl(dealId4P, poiId4P);
            }
            return getExternalCommonBuyUrl(dealId4P, poiId4P);
        }

        if (Objects.equals(request.getClientTypeEnum(), ClientTypeEnum.MT_APP)) {
            String mtAppCommonBuyUrl = getMtAppCommonBuyUrl(dealId4P, poiId4P);
            return assembleSourceParam(request, mtAppCommonBuyUrl);
        }

        if (Objects.equals(request.getClientTypeEnum(), ClientTypeEnum.DP_APP)) {
            String dpAppCommonBuyUrl = getDpAppCommonBuyUrl(dealId4P, poiId4P, Objects.nonNull(shopInfo.getDpPoiDTO()) ? shopInfo.getDpPoiDTO().getUuid() : null);
            return assembleSourceParam(request, dpAppCommonBuyUrl);
        }

        if (Objects.equals(request.getClientTypeEnum(), ClientTypeEnum.MT_XCX)
                || Objects.equals(request.getClientTypeEnum(), ClientTypeEnum.DP_XCX)) {
            return getWxCommonBuyUrl(request.getPageSource(), dealId4P, cityId, request.getClientTypeEnum().isMtClientType(), poiId4P);
        }

        if (Objects.equals(request.getClientTypeEnum(), ClientTypeEnum.DP_M)
                || Objects.equals(request.getClientTypeEnum(), ClientTypeEnum.MT_I)) {
            int dpDealId = NumberUtils.toInt(DealCtxUtils.getDefaultSkuId(skuDefaultSelect, dealGroupBase));
            return getMWebCommonBuyUrl(dealId4P,dpDealId , request.getClientTypeEnum().isMtClientType());
        }

        return null;
    }

    //获取小程序普通购买地址
    public static String getWxCommonBuyUrl(String requestSource, int dealId, int cityId, boolean mt, long shopId) {
        if (dealId <= 0) {
            return null;
        }

        String url = StringUtils.EMPTY;
        if (mt) {
            url = String.format("/gnc/pages/ordering/index?id=%s&cityId=%s&shopId=%s", dealId, cityId, shopId);
        } else {
            url = "/packages/tuan/pages/ordersubmit/ordersubmit?type=1";
        }

        if (RequestSourceEnum.COST_EFFECTIVE.getSource().equals(requestSource)) {
            url += "&source=cost_effective";
        }
        return url;
    }


    private static String getBaiduDianpingMiniAppBuyUrl(final ShopInfo shopInfo,final ShopIdMapper shopIdMapper, final ProductDetailPageRequest request, final ProductBaseInfo dealGroupBase, final String finalPrice) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.helper.UrlHelper.getBaiduDianpingMiniAppBuyUrl(com.dianping.mobile.mapi.dztgdetail.datatype.context.DealCtx,java.lang.String)");
        Map<String, Object> encodeURIComponent = new LinkedHashMap<>();
        if(dealGroupBase != null) {
            if(CollectionUtils.isNotEmpty(dealGroupBase.getDeals())) {
                Optional<DealGroupDealDTO> dealBaseDTO = dealGroupBase.getDeals().stream().filter(dealBase -> dealBase.getBasic().getStatus() == 1).findFirst();
                if (dealBaseDTO.isPresent()) {
                    encodeURIComponent.put("dealId", dealBaseDTO.get().getDealId());
                    encodeURIComponent.put("title", dealBaseDTO.get().getBasic().getTitle());
                }
            }
            encodeURIComponent.put("dealGroupId", request.getProductId());
            encodeURIComponent.put("maxPerUser", dealGroupBase.getRule().getBuyRule().getMaxPerUser());
        }
        encodeURIComponent.put("price", finalPrice);
        encodeURIComponent.put("shopUuid", shopInfo.getDpPoiDTO().getUuid().toString());
        encodeURIComponent.put("shopId", shopIdMapper.getDpBestShopId());

        String encodedJson = "";
        try {
            String json = GsonUtils.toJsonString(encodeURIComponent);
            encodedJson = URLEncoder.encode(json, "UTF8");
        } catch (UnsupportedEncodingException e) {
            log.error("URLEncoder.encode error", e);
        }

        return "/packages/dpmapp-gc-deal/tuan/pages/ordersubmit/ordersubmit?type=1&orderInfo=" + encodedJson;
    }

    private static String getDianpingMiniAppBuyUrl(final ShopInfo shopInfo,final ShopIdMapper shopIdMapper, final ProductDetailPageRequest request, final ProductBaseInfo dealGroupBase, final String finalPrice) {
        Map<String, Object> encodeURIComponent = new LinkedHashMap<>();
        if(dealGroupBase != null) {
            if(CollectionUtils.isNotEmpty(dealGroupBase.getDeals())) {
                Optional<DealGroupDealDTO> dealBaseDTO = dealGroupBase.getDeals().stream().filter(dealBase -> dealBase.getBasic().getStatus() == 1).findFirst();
                if (dealBaseDTO.isPresent()) {
                    encodeURIComponent.put("dealId", dealBaseDTO.get().getDealId());
                    encodeURIComponent.put("title", dealBaseDTO.get().getBasic().getTitle());
                }
            }
            encodeURIComponent.put("dealGroupId", request.getProductId());
            encodeURIComponent.put("maxPerUser", dealGroupBase.getRule().getBuyRule().getMaxPerUser());
        }
        encodeURIComponent.put("price", finalPrice);
        encodeURIComponent.put("shopUuid", shopInfo.getDpPoiDTO().getUuid());
        encodeURIComponent.put("shopId", shopIdMapper.getDpBestShopId());

        String encodedJson = "";
        try {
            String json = GsonUtils.toJsonString(encodeURIComponent);
            encodedJson = URLEncoder.encode(json, "UTF8");
        } catch (UnsupportedEncodingException e) {
            log.error("URLEncoder.encode error", e);
        }

        String url = "/packages/tuan/pages/ordersubmit/ordersubmit?type=1&orderInfo=" + encodedJson;
        if (RequestSourceEnum.COST_EFFECTIVE.getSource().equals(request.getPageSource())) {
            url += "&source=cost_effective";
        }

        return url;
    }


    public static String getAppUrl( ShopInfo shopInfo, ProductDetailPageRequest request, String shareUrl, boolean mt) {
        if (StringUtils.isEmpty(shareUrl)) {
            return null;
        }
        if (mt) {
            if(Objects.equals(request.getShepherdGatewayParam().getMpAppId(), MpAppIdEnum.MT_WEIXIN_MINIPROGRAM.getMpAppId())) {
                String miniappUrl = shareUrl + "&product=mtwxapp";
                return "/index/pages/h5/h5?f_token=1&f_openId=1&weburl=" + NetUtils.encode(miniappUrl);
            }
            return "imeituan://www.meituan.com/web?url=" + NetUtils.encode(shareUrl);
        } else {
            shareUrl = ShopUuidUtils.filterUrl(shareUrl, shopInfo.getDpPoiDTO().getUuid());
            if( Objects.equals(request.getClientTypeEnum(), ClientTypeEnum.DP_XCX)) {
                return "/pages/webview/webview?url=" + NetUtils.encode(shareUrl);
            }
            return "dianping://web?url=" + NetUtils.encode(shareUrl);
        }
    }

    //获取美团APP普通购买地址
    private static String getMtAppCommonBuyUrl(int mtDealId, long mtPoiId) {
        return String.format("imeituan://www.meituan.com/gc/createorder?dealid=%s&shopid=%s", mtDealId, mtPoiId);
    }

    private static String getExternalCommonBuyUrl(int mtDealId, long mtPoiId) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.helper.UrlHelper.getExternalCommonBuyUrl(int,long)");
        return String.format("/gc/pages/deal/createorder/createorder?dealid=%s&shopid=%s", mtDealId, mtPoiId);
    }

    private static String assembleSourceParam(ProductDetailPageRequest request, String url) {
        if (StringUtils.isBlank(url)) {
            return url;
        }

        if (StringUtils.isBlank(request.getPageSource()) && StringUtils.isBlank(request.getCustomParam(dealparam))) {
            return url;
        }

        if (RequestSourceEnum.needDyeAndReport(request.getPageSource())) {
            return url + "&source=" + request.getPageSource();
        }

        if(DealParamEnum.isMLiveSource(request.getCustomParam(dealparam))) {
            return url + "&source=mlive";
        }

        return url;
    }

    //获取点评APP普通购买地址getDpAppCommonBuyUrl
    private static String getDpAppCommonBuyUrl(int dpDealGroupId, long dpPoiId, String shopUuid) {
        String url = String.format("dianping://createorder?dealid=%s", dpDealGroupId);
        url += "&shopuuid=" + shopUuid;
        url += "&shopid=" + dpPoiId;
        return url;
    }

    //获取M站订单跳转
    private static String getMWebCommonBuyUrl(int dpDealGroupId, int dpDealId, boolean mt) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.helper.UrlHelper.getMWebCommonBuyUrl(int,int,boolean)");
        if (dpDealGroupId <= 0) {
            return null;
        }
        String url;
        String baseUrl;
        if (mt) {
            baseUrl = Lion.getStringValue("com.sankuai.dzu.tpbase.dztgdetailweb.mt.web.url",
                    "https://test-g.meituan.com/app/gfe-app-page-tuan/create-order.html?dealId=%s");
            url = String.format(baseUrl, dpDealGroupId);
        } else {
            baseUrl = Lion.getStringValue("com.sankuai.dzu.tpbase.dztgdetailweb.dp.web.url",
                    "https://m.51ping.com/tuan/buy/%s");
            url = String.format(baseUrl, dpDealId);
        }
        return url;
    }

    /**
     * 美团平台返回美团适用商户ID，点评平台返回点评适用商户ID
     * 注意：如果外部参数没有传shopid，那么就会从适用商户中去获取
     */
    public static String getMtWxMainShopUrl(long shopId, long bestShopId) {
        long poiId = shopId != 0L ? shopId : bestShopId;
        return String.format("/gcpoi/pages/index?id=%s", poiId);
    }


}
