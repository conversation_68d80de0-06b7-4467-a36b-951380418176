package com.sankuai.dzshoppingguide.product.detail.application.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.dianping.pigeon.remoting.common.exception.RpcException;
import com.dianping.pigeon.remoting.invoker.concurrent.InvocationCallback;
import com.dianping.pigeon.remoting.invoker.util.InvokerHelper;
import com.google.common.util.concurrent.SettableFuture;
import com.meituan.service.mobile.mtthrift.netty.ContextStore;
import com.sankuai.dzcard.navigation.api.DzCardExposureService;
import com.sankuai.dzcard.navigation.api.dto.CardHoldStatusDTO;
import com.sankuai.dzcard.navigation.api.dto.FindDCCardHoldStatusLiteReqDTO;
import com.sankuai.mpmctmember.query.thrift.api.MemberPlanQueryService;
import com.sankuai.mpmctmember.query.thrift.dto.GetShopMemberEntranceReqDTO;
import com.sankuai.mpmctmember.query.thrift.dto.GetShopMemberEntranceRespDTO;
import com.sankuai.mpmctmember.query.thrift.dto.UserClientEnvDTO;
import java.util.Arrays;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.stubbing.Answer;

@ExtendWith(MockitoExtension.class)
class CompositeAtomServiceImplQueryShopAndUserCardHoldStatusTest {

    @Mock
    private DzCardExposureService dzCardExposureService;

    @InjectMocks
    private CompositeAtomServiceImpl compositeAtomService;

    @Mock
    private MemberPlanQueryService memberPlanQueryService;

    private SettableFuture<GetShopMemberEntranceRespDTO> settableFuture;

    @BeforeEach
    void setUp() {
        // Clear any previous callback
        InvokerHelper.clearCallback();
    }

    @BeforeEach
    public void setup() {
        settableFuture = SettableFuture.create();
        ContextStore.setFuture(settableFuture);
    }

    @AfterEach
    public void cleanup() {
        ContextStore.removeFuture();
    }

    /**
     * 测试正常调用成功场景
     */
    @Test
    public void testQueryShopAndUserCardHoldStatus_Success() throws Throwable {
        // arrange
        FindDCCardHoldStatusLiteReqDTO request = new FindDCCardHoldStatusLiteReqDTO();
        request.setShopId(123L);
        request.setUserId(456L);
        request.setPlatform(1);
        CardHoldStatusDTO expectedResult = new CardHoldStatusDTO();
        expectedResult.setShopHasCardTypeList(Arrays.asList(1, 2));
        expectedResult.setUserHoldCardTypeList(Arrays.asList(1));
        doAnswer((Answer<Void>) invocation -> {
            InvocationCallback callback = InvokerHelper.getCallback();
            callback.onSuccess(expectedResult);
            return null;
        }).when(dzCardExposureService).findShopAndUserCardHoldStatusWithLongShopIdLite(any());
        // act
        CompletableFuture<CardHoldStatusDTO> future = compositeAtomService.queryShopAndUserCardHoldStatus(request);
        CardHoldStatusDTO actualResult = future.get(1, TimeUnit.SECONDS);
        // assert
        assertNotNull(future);
        assertEquals(expectedResult, actualResult);
        verify(dzCardExposureService).findShopAndUserCardHoldStatusWithLongShopIdLite(request);
    }

    /**
     * 测试服务调用抛出异常场景
     */
    @Test
    public void testQueryShopAndUserCardHoldStatus_ServiceThrowsException() throws Throwable {
        // arrange
        FindDCCardHoldStatusLiteReqDTO request = new FindDCCardHoldStatusLiteReqDTO();
        request.setShopId(123L);
        request.setUserId(456L);
        request.setPlatform(1);
        RpcException expectedException = new RpcException("Service error");
        doAnswer((Answer<Void>) invocation -> {
            InvocationCallback callback = InvokerHelper.getCallback();
            callback.onFailure(expectedException);
            return null;
        }).when(dzCardExposureService).findShopAndUserCardHoldStatusWithLongShopIdLite(any());
        // act
        CompletableFuture<CardHoldStatusDTO> future = compositeAtomService.queryShopAndUserCardHoldStatus(request);
        // assert
        ExecutionException actualException = assertThrows(ExecutionException.class, () -> future.get(1, TimeUnit.SECONDS));
        assertEquals(expectedException, actualException.getCause());
        verify(dzCardExposureService).findShopAndUserCardHoldStatusWithLongShopIdLite(request);
    }

    /**
     * 测试请求参数为null的边界场景
     */
    @Test
    public void testQueryShopAndUserCardHoldStatus_NullRequest() throws Throwable {
        // arrange
        doAnswer((Answer<Void>) invocation -> {
            InvocationCallback callback = InvokerHelper.getCallback();
            callback.onFailure(new IllegalArgumentException("Request cannot be null"));
            return null;
        }).when(dzCardExposureService).findShopAndUserCardHoldStatusWithLongShopIdLite(null);
        // act
        CompletableFuture<CardHoldStatusDTO> future = compositeAtomService.queryShopAndUserCardHoldStatus(null);
        // assert
        assertNotNull(future);
        ExecutionException actualException = assertThrows(ExecutionException.class, () -> future.get(1, TimeUnit.SECONDS));
        assertTrue(actualException.getCause() instanceof IllegalArgumentException);
        assertEquals("Request cannot be null", actualException.getCause().getMessage());
        verify(dzCardExposureService).findShopAndUserCardHoldStatusWithLongShopIdLite(null);
    }

    /**
     * 测试正常调用场景
     */
    @Test
    public void testGetShopMemberEntranceNormalCase() throws Throwable {
        // arrange
        GetShopMemberEntranceReqDTO request = new GetShopMemberEntranceReqDTO();
        request.setDpShopId(12345L);
        request.setMtShopId(67890L);
        UserClientEnvDTO userClientEnv = new UserClientEnvDTO();
        userClientEnv.setPlatformClient(1);
        userClientEnv.setPlatformClientApplication("app");
        userClientEnv.setPlatformClientOS("android");
        request.setUserClientEnv(userClientEnv);
        GetShopMemberEntranceRespDTO expectedResponse = new GetShopMemberEntranceRespDTO();
        expectedResponse.setHasEntrance(true);
        expectedResponse.setMemberPageLink("https://example.com/member");
        expectedResponse.setPlanId(1001L);
        when(memberPlanQueryService.getShopMemberEntrance(request)).thenReturn(expectedResponse);
        // act
        CompletableFuture<GetShopMemberEntranceRespDTO> future = compositeAtomService.getShopMemberEntrance(request);
        settableFuture.set(expectedResponse);
        // assert
        assertNotNull(future);
        assertEquals(expectedResponse, future.get());
        verify(memberPlanQueryService, times(1)).getShopMemberEntrance(request);
    }

    /**
     * 测试服务抛出异常场景
     */
    @Test
    public void testGetShopMemberEntranceServiceThrowsException() throws Throwable {
        // arrange
        GetShopMemberEntranceReqDTO request = new GetShopMemberEntranceReqDTO();
        request.setDpShopId(12345L);
        UserClientEnvDTO userClientEnv = new UserClientEnvDTO();
        userClientEnv.setPlatformClient(1);
        userClientEnv.setPlatformClientApplication("app");
        userClientEnv.setPlatformClientOS("android");
        request.setUserClientEnv(userClientEnv);
        RuntimeException expectedException = new RuntimeException("Service error");
        when(memberPlanQueryService.getShopMemberEntrance(any())).thenThrow(expectedException);
        // act & assert
        assertThrows(RuntimeException.class, () -> compositeAtomService.getShopMemberEntrance(request));
        verify(memberPlanQueryService, times(1)).getShopMemberEntrance(request);
    }

    /**
     * 测试请求参数为null的场景
     */
    @Test
    public void testGetShopMemberEntranceNullRequest() throws Throwable {
        // arrange
        GetShopMemberEntranceRespDTO nullResponse = null;
        when(memberPlanQueryService.getShopMemberEntrance(null)).thenReturn(nullResponse);
        // act
        CompletableFuture<GetShopMemberEntranceRespDTO> future = compositeAtomService.getShopMemberEntrance(null);
        settableFuture.set(nullResponse);
        // assert
        assertNotNull(future);
        assertNull(future.get());
        verify(memberPlanQueryService, times(1)).getShopMemberEntrance(null);
    }
}
