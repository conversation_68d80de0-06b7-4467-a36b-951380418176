package com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommend;

import com.dianping.frog.sdk.util.CollectionUtils;
import com.dianping.martgeneral.recommend.api.dto.RecommendDTO;
import com.dianping.martgeneral.recommend.api.entity.RecommendParameters;
import com.dianping.martgeneral.recommend.api.entity.RecommendResult;
import com.dianping.martgeneral.recommend.api.entity.Response;
import com.dianping.martgeneral.recommend.api.enums.PlatformEnum;
import com.google.common.base.Joiner;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.best.shop.ShopInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.best.shop.ShopInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommend.dto.RecommendInfoReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommend.dto.DealsInShopReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
import com.sankuai.dzshoppingguide.product.detail.domain.utils.LionConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * @Author: zhangyuan103
 * @Date: 2025/5/23
 * @Description: 根据传入的团单id列表和商户后台类目id，获取推荐信息
 */
@Fetcher(previousLayerDependencies = {DealsInShopFetcher.class, ShopInfoFetcher.class})
@Slf4j
public class RecommendInfoInShopFetcher extends NormalFetcherContext<RecommendInfoReturnValue> {
    @Resource
    private CompositeAtomService compositeAtomService;

    private static final String DEAL_DISPLAY_MAX_VALUE = "3";
    private static final String DEAL_DISPLAY_MAX_VALUE_KEY = "dealDisplayMaxValue";

    @Override
    protected CompletableFuture<RecommendInfoReturnValue> doFetch() throws Exception {
        DealsInShopReturnValue dealsInShopReturnValue = getDependencyResult(DealsInShopFetcher.class);
        ShopInfo shopInfo = getDependencyResult(ShopInfoFetcher.class);
        List<Long> saleDealIdList = Optional.ofNullable(dealsInShopReturnValue)
                .map(DealsInShopReturnValue::getAllDealIds).orElse(null);

        List<Integer> backCategoryIds = Optional.ofNullable(shopInfo).map(ShopInfo::getBackCategoryIds).orElse(null);

        if (CollectionUtils.isEmpty(saleDealIdList) || CollectionUtils.isEmpty(backCategoryIds)) {
            return CompletableFuture.completedFuture(null);
        }
        return compositeAtomService.recommendRecommendDTO(buildRecommendParameters(saleDealIdList, shopInfo.getBackCategoryIds()))
                .thenApply(response -> {
                    if (validRecommendResult(response)) {
                        return null;
                    }
                    Map<String, String> relatedRecommendConfigMap = LionConfigUtils.getRelatedRecommendConfigMap();
                    int dealDisplayMaxValue = NumberUtils.toInt(
                            relatedRecommendConfigMap.getOrDefault(DEAL_DISPLAY_MAX_VALUE_KEY, DEAL_DISPLAY_MAX_VALUE));
                    String sortedProductList = (String)response.getResult().getSortedResult().get(0).getBizData()
                            .get("productList");
                    List<Long> sortedDealGroupIds = Arrays.stream(sortedProductList.split(","))
                            .map(dealId -> NumberUtils.toLong(dealId.split(":")[0])).collect(Collectors.toList());
                    sortedDealGroupIds = sortedDealGroupIds.subList(0, Math.min(sortedDealGroupIds.size(), dealDisplayMaxValue));
                    return new RecommendInfoReturnValue(sortedDealGroupIds);
                });
    }

    private RecommendParameters buildRecommendParameters(List<Long> allDealIds, List<Integer> backCategoryIds) {
        RecommendParameters recommendParameters = new RecommendParameters();
        boolean isMT = request.getClientTypeEnum().isMtClientType();
        recommendParameters.setBizId(681);
        recommendParameters.setCityId(request.getCityId());
        if (isMT) {
            // 美团设备号
            recommendParameters.setUuid(request.getShepherdGatewayParam().getDeviceId());
            recommendParameters.setPlatformEnum(PlatformEnum.MT);
            recommendParameters.setOriginUserId(String.valueOf(request.getMtUserId()));// 已确认判断平台后再使用
        } else {
            // 点评设备号
            recommendParameters.setDpid(request.getShepherdGatewayParam().getDeviceId());
            recommendParameters.setPlatformEnum(PlatformEnum.DP);
            recommendParameters.setOriginUserId(String.valueOf(request.getDpUserId()));
        }
        recommendParameters.setLat(request.getUserLat());
        recommendParameters.setLng(request.getUserLng());
        recommendParameters.setPageNumber(1);
        recommendParameters.setPageSize(10);
        Map<String, Object> bizParams = new HashMap<>();
        // 货架列表(待排序列表)
        bizParams.put("tab_9999", buildProductList(allDealIds, request.getProductId())); // productType：1001:团购
                                                                                         // 备注：该场景只有一个tab，所以设置tab值为9999
        bizParams.put("productType", "1001");  // 1001:团购，1005:预定 ...
        bizParams.put("shopId", String.valueOf(request.getPoiId())); // 当前商户id； 美团侧为美团商户id，点评侧为点评商户id
        // 团单所对应的商户的后台类目id,不区分层级，多个用逗号分隔
        if (CollectionUtils.isNotEmpty(backCategoryIds)) {
            // 一级类目
            bizParams.put("cat1Id", backCategoryIds.get(0));
            // 二级类目
            bizParams.put("cat0Id", backCategoryIds.size() > 1 ? backCategoryIds.get(1) : 0);
        }
        // 团详同店推荐
        if (request.getClientTypeEnum().equals(ClientTypeEnum.MT_XCX)) {
            bizParams.put("flowFlag", "101");
        } else {
            bizParams.put("flowFlag", "007");
        }
        recommendParameters.setBizParams(bizParams);
        return recommendParameters;
    }

    private String buildProductList(List<Long> allDealIds, long dealGroupId) {
        if (CollectionUtils.isEmpty(allDealIds)) {
            return StringUtils.EMPTY;
        }
        if (allDealIds.size() > 200) {  // 最多取前200，极端场景防御
            allDealIds = allDealIds.subList(0, 200);
        }
        List<String> productList = allDealIds.stream().filter(id -> !id.equals(dealGroupId)).map(id -> id + ":1001")
                .collect(Collectors.toList());
        return Joiner.on(",").join(productList);
    }

    private boolean validRecommendResult(Response<RecommendResult<RecommendDTO>> response) {
        if (response == null || !response.isSuccess() || response.getResult() == null) {
            return true;
        }
        List<RecommendDTO> sortedResult = response.getResult().getSortedResult();
        if (CollectionUtils.isEmpty(sortedResult) || MapUtils.isEmpty(sortedResult.get(0).getBizData())
                || StringUtils.isBlank((String)sortedResult.get(0).getBizData().get("productList"))) {
            return true;
        }
        return false;
    }
}
