package com.sankuai.dzshoppingguide.product.detail.spi.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/04/17
 * promoDTO.getPromoIdentity() 属性
 */
@Getter
@AllArgsConstructor
public enum PromoIdentityEnum {

    GOD_COUPON_ALLIANCE("godCouponAlliance","神券联盟"),
    SPECIAL_DISCOUNT_CODE("specialDiscountCode","特惠码专享"),
    BEAUTY_MEMBER("beautyMember","丽人会员");
    private String type;
    private String desc;

    public static String getDescByType(String type) {
        for (PromoIdentityEnum promoIdentityEnum : PromoIdentityEnum.values()) {
            if (promoIdentityEnum.type.equals(type)) {
                return promoIdentityEnum.getDesc();
            }
        }
        return "";
    }
}
