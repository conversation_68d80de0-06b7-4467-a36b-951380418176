package com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.ext.param.handler;

/**
 * @Author: g<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025/3/26 21:14
 */

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.dianping.lion.client.Lion;
import com.sankuai.dealuser.price.display.api.enums.PromoIdentityEnum;
import com.sankuai.dz.product.detail.RequestCustomParamEnum;
import com.sankuai.dz.product.detail.RequestSourceEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.enums.OrderPageExtParamEnums;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.ext.param.BaseSingleExtParamBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.ext.param.request.ExtParamBuilderRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.dto.ProductPriceReturnValue;
import com.sankuai.dzshoppingguide.product.detail.domain.utils.LionConfigUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static com.sankuai.dzshoppingguide.product.detail.domain.utils.LionConstants.DZTGDETAIL_APPKEY;

@Slf4j
@Component
public class PromotionChannelExtParamBuilder extends BaseSingleExtParamBuilder {
    @Override
    protected OrderPageExtParamEnums buildKey() {
        return OrderPageExtParamEnums.promotionchannel;
    }

    @Override
    protected String doBuildExtParam(final ProductDetailPageRequest request,
                                     final ExtParamBuilderRequest builderRequest) throws Exception {
        // 渠道专属立减 提单页链接增加参数
        Map<String, String> pageSource2OrderPromotionChannel = Lion.getMap(
                DZTGDETAIL_APPKEY,
                "com.sankuai.dzu.tpbase.dztgdetailweb.pagesource.to.order.promotionchannel",
                String.class, new HashMap<>());
        String promotionChannel = buildPromotionChannel(request, pageSource2OrderPromotionChannel);
        if (hasExclusiveDeduction(builderRequest.getNormalPrice())) {
            //是否有渠道专属优惠
            if (StringUtils.isBlank(promotionChannel)) {//默认是1
                promotionChannel = "1";
            }
            return promotionChannel;
        } else if (StringUtils.isNotBlank(promotionChannel)) {
            return promotionChannel;
        }
        return null;
    }

    /**
     * 猜喜渠道
     */
    public static final String SUBSIDY_SCENE = "subsidyScene";
    public static final String HIT_GUESS_LIKE_SUBSIDY = "hitGuessLikeSubsidy";

    private static String buildPromotionChannel(final ProductDetailPageRequest request,
                                                final Map<String, String> pageSource2OrderPromotionChannel) {
        String pageSource = request.getPageSource();
        String promotionChannel = pageSource2OrderPromotionChannel.get(pageSource);
        // 猜喜渠道流量来源为source=caixi，
        // 但是猜喜侧只有hitGuessLikeSubsidy=true时调用报价才传source=caixi，为和猜喜保持一致，使用hitGuessLikeSubsidy字段判定
        if (!LionConfigUtils.useNewSourceForCaixi()) {
            return promotionChannel;
        }
        Map<String, String> requestExtParams = new HashMap<>();
        if (RequestSourceEnum.CAI_XI.getSource().equals(pageSource)) {
            String extParam = request.getCustomParam(RequestCustomParamEnum.extparam);
            if (StringUtils.isNotBlank(extParam)) {
                try {
                    requestExtParams = JSONObject.parseObject(extParam, new TypeReference<Map<String, String>>() {
                    });
                } catch (Exception e) {
                    log.error("GsonUtils.fromJsonString failed", e);
                    return promotionChannel;
                }
            }
            return MapUtils.isNotEmpty(requestExtParams)
                    && requestExtParams.containsKey(SUBSIDY_SCENE)
                    && HIT_GUESS_LIKE_SUBSIDY.equals(requestExtParams.get(SUBSIDY_SCENE)) ? promotionChannel : null;
        }
        return promotionChannel;
    }

    private boolean hasExclusiveDeduction(final ProductPriceReturnValue normalPrice) {
        if (Objects.isNull(normalPrice)
                || Objects.isNull(normalPrice.getPriceDisplayDTO())
                || CollectionUtils.isNotEmpty(normalPrice.getPriceDisplayDTO().getUsedPromos())) {
            return false;
        }

        return normalPrice.getPriceDisplayDTO().getUsedPromos().stream()
                .filter(Objects::nonNull)
                .anyMatch(promoDTO -> PromoIdentityEnum.EXCLUSIVE_DEDUCTION.isType(promoDTO.getPromoIdentity()));
    }

}
