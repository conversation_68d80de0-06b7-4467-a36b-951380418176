package com.sankuai.dzshoppingguide.product.detail.application.fetcher.dealgift;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.enums.MobileOSTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.cityId.CityIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.dealgroup.DealGroupIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.shopid.ShopIdMapper;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.PlayCenterPlatformEnum;
import java.lang.reflect.Field;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class DealGiftFetcherTest {

    private DealGiftFetcher dealGiftFetcher;

    @Mock
    private ProductDetailPageRequest request;

    public DealGiftFetcherTest() {
        MockitoAnnotations.openMocks(this);
    }

    @BeforeEach
    void setUp() throws Exception {
        dealGiftFetcher = spy(new DealGiftFetcher());
        // Find and set the protected field using reflection
        Class<?> currentClass = dealGiftFetcher.getClass();
        Field requestField = null;
        while (currentClass != null && requestField == null) {
            try {
                requestField = currentClass.getDeclaredField("request");
            } catch (NoSuchFieldException e) {
                currentClass = currentClass.getSuperclass();
            }
        }
        if (requestField != null) {
            requestField.setAccessible(true);
            requestField.set(dealGiftFetcher, request);
        } else {
            throw new RuntimeException("Could not find 'request' field in class hierarchy");
        }
    }

    /**
     * Test normal case with IOS device and MT_WEAPP platform
     */
    @Test
    public void testBuildNewCustomExecutePlay_NormalCase_IOS_MTWeapp() throws Throwable {
        // arrange
        DealGroupIdMapper dealGroupIdMapper = new DealGroupIdMapper(123L, 456L);
        ShopIdMapper shopIdMapper = new ShopIdMapper(789L, 101L);
        CityIdMapper cityIdMapper = new CityIdMapper(1, 2);
        when(request.getMobileOSType()).thenReturn(MobileOSTypeEnum.IOS);
        when(dealGiftFetcher.getPlatformTypeByUserSource(any())).thenReturn(PlayCenterPlatformEnum.MT_WEAPP);
        // act
        Map<String, String> result = dealGiftFetcher.buildNewCustomExecutePlay(dealGroupIdMapper, shopIdMapper, cityIdMapper);
        // assert
        assertNotNull(result);
        assertEquals("2", result.get("cityId"));
        // MT_WEAPP id
        assertEquals("5", result.get("platformType"));
        // IOS
        assertEquals("0", result.get("osType"));
        assertTrue(result.containsKey("activeRequestUnit"));
    }

    /**
     * Test normal case with Android device and DP_WEAPP platform
     */
    @Test
    public void testBuildNewCustomExecutePlay_NormalCase_Android_DPWeapp() throws Throwable {
        // arrange
        DealGroupIdMapper dealGroupIdMapper = new DealGroupIdMapper(123L, 456L);
        ShopIdMapper shopIdMapper = new ShopIdMapper(789L, 101L);
        CityIdMapper cityIdMapper = new CityIdMapper(1, 2);
        when(request.getMobileOSType()).thenReturn(MobileOSTypeEnum.ANDROID);
        when(dealGiftFetcher.getPlatformTypeByUserSource(any())).thenReturn(PlayCenterPlatformEnum.DP_WEAPP);
        // act
        Map<String, String> result = dealGiftFetcher.buildNewCustomExecutePlay(dealGroupIdMapper, shopIdMapper, cityIdMapper);
        // assert
        assertNotNull(result);
        assertEquals("2", result.get("cityId"));
        // DP_WEAPP id
        assertEquals("6", result.get("platformType"));
        // Android
        assertEquals("1", result.get("osType"));
        assertTrue(result.containsKey("activeRequestUnit"));
    }

    @Test
    public void testGetPlatformTypeByUserSource_MTMiniApp() throws Throwable {
        DealGiftFetcher dealGiftFetcher = new DealGiftFetcher();
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.MT_XCX);
        PlayCenterPlatformEnum result = dealGiftFetcher.getPlatformTypeByUserSource(request);
        assertEquals(PlayCenterPlatformEnum.MT_WEAPP, result);
    }

    @Test
    public void testGetPlatformTypeByUserSource_DPMiniApp() throws Throwable {
        DealGiftFetcher dealGiftFetcher = new DealGiftFetcher();
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.DP_XCX);
        PlayCenterPlatformEnum result = dealGiftFetcher.getPlatformTypeByUserSource(request);
        assertEquals(PlayCenterPlatformEnum.DP_WEAPP, result);
    }

    @Test
    public void testGetPlatformTypeByUserSource_MTClientType() throws Throwable {
        DealGiftFetcher dealGiftFetcher = new DealGiftFetcher();
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.MT_APP);
        PlayCenterPlatformEnum result = dealGiftFetcher.getPlatformTypeByUserSource(request);
        assertEquals(PlayCenterPlatformEnum.MEITUAN_APP, result);
    }

    @Test
    public void testGetPlatformTypeByUserSource_DPClientType() throws Throwable {
        DealGiftFetcher dealGiftFetcher = new DealGiftFetcher();
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.DP_APP);
        PlayCenterPlatformEnum result = dealGiftFetcher.getPlatformTypeByUserSource(request);
        assertEquals(PlayCenterPlatformEnum.DIANPING_APP, result);
    }

    @Test
    public void testGetPlatformTypeByUserSource_UnknownClientType() throws Throwable {
        DealGiftFetcher dealGiftFetcher = new DealGiftFetcher();
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.UNKNOWN);
        PlayCenterPlatformEnum result = dealGiftFetcher.getPlatformTypeByUserSource(request);
        assertEquals(PlayCenterPlatformEnum.UNKNOWN, result);
    }

    /**
     * Test case for when input parameter is exactly "undefined".
     * Expected to return empty string.
     */
    @Test
    void testGetExtParam_WhenInputIsUndefined_ShouldReturnEmptyString() {
        // arrange
        DealGiftFetcher fetcher = new DealGiftFetcher();
        String input = "undefined";
        // act
        String result = fetcher.getExtParam(input);
        // assert
        assertEquals("", result, "Should return empty string when input is 'undefined'");
    }

    /**
     * Test case for when input parameter is a normal string.
     * Expected to return the input string as-is.
     */
    @Test
    void testGetExtParam_WhenInputIsNormalString_ShouldReturnSameString() {
        // arrange
        DealGiftFetcher fetcher = new DealGiftFetcher();
        String input = "normal_param_value";
        // act
        String result = fetcher.getExtParam(input);
        // assert
        assertEquals(input, result, "Should return same string when input is not 'undefined'");
    }

    /**
     * Test case for when input parameter is null.
     * Expected to return null.
     */
    @Test
    void testGetExtParam_WhenInputIsNull_ShouldReturnNull() {
        // arrange
        DealGiftFetcher fetcher = new DealGiftFetcher();
        // act
        String result = fetcher.getExtParam(null);
        // assert
        assertNull(result, "Should return null when input is null");
    }

    /**
     * Test case for when input parameter is empty string.
     * Expected to return empty string.
     */
    @Test
    void testGetExtParam_WhenInputIsEmptyString_ShouldReturnEmptyString() {
        // arrange
        DealGiftFetcher fetcher = new DealGiftFetcher();
        String input = "";
        // act
        String result = fetcher.getExtParam(input);
        // assert
        assertEquals("", result, "Should return empty string when input is empty string");
    }

    /**
     * Test case for when input parameter is "undefined" with different case.
     * Expected to return the original string (case sensitive comparison).
     */
    @Test
    void testGetExtParam_WhenInputIsUndefinedWithDifferentCase_ShouldReturnOriginalString() {
        // arrange
        DealGiftFetcher fetcher = new DealGiftFetcher();
        String input = "UNDEFINED";
        // act
        String result = fetcher.getExtParam(input);
        // assert
        assertEquals(input, result, "Should return original string when case doesn't match");
    }

    /**
     * Test case for when input parameter contains "undefined" as substring.
     * Expected to return the original string.
     */
    @Test
    void testGetExtParam_WhenInputContainsUndefined_ShouldReturnOriginalString() {
        // arrange
        DealGiftFetcher fetcher = new DealGiftFetcher();
        String input = "prefix_undefined_suffix";
        // act
        String result = fetcher.getExtParam(input);
        // assert
        assertEquals(input, result, "Should return original string when input contains 'undefined' as substring");
    }
}
