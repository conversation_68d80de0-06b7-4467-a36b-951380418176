package com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.period.price;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.response.FetcherResponse;
import com.sankuai.dz.product.detail.gateway.spi.enums.ProductTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.QueryCenterAggregateReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.period.price.dto.PeriodPriceMergeDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.deal.DealGroupDealDTO;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.*;

class SkuPeriodPriceFetcherTest {

    private void setRequest(SkuPeriodPriceFetcher fetcher, ProductDetailPageRequest request) throws Exception {
        Field requestField = fetcher.getClass().getSuperclass().getSuperclass().getDeclaredField("request");
        requestField.setAccessible(true);
        requestField.set(fetcher, request);
    }

    /**
     * 测试正常场景：aggregateResult 不为空，且所有嵌套对象都不为空
     */
    @Test
    public void testMapResultNormalCase() throws Throwable {
        // arrange
        SkuPeriodPriceFetcher fetcher = new SkuPeriodPriceFetcher();
        ProductDetailPageRequest request = mock(ProductDetailPageRequest.class);
        when(request.getProductTypeEnum()).thenReturn(ProductTypeEnum.DEAL);
        setRequest(fetcher, request);
        FetcherResponse<QueryCenterAggregateReturnValue> aggregateResult = mock(FetcherResponse.class);
        QueryCenterAggregateReturnValue returnValue = mock(QueryCenterAggregateReturnValue.class);
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        DealGroupDealDTO deal = mock(DealGroupDealDTO.class);
        List<DealGroupDealDTO> deals = new ArrayList<>();
        deals.add(deal);
        when(aggregateResult.getReturnValue()).thenReturn(returnValue);
        when(returnValue.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getDeals()).thenReturn(deals);
        when(deal.getDealId()).thenReturn(123L);
        when(deal.getWeeklyPricePlan()).thenReturn(null);
        when(deal.getDateTimePrice()).thenReturn(null);
        when(deal.getPeriodPrice()).thenReturn(null);
        // act
        FetcherResponse<SkuPeriodPrice> result = fetcher.mapResult(aggregateResult);
        // assert
        assertNotNull(result);
        assertNotNull(result.getReturnValue());
        Optional<PeriodPriceMergeDTO> periodPriceDTO = result.getReturnValue().getPeriodPriceDTO(123L);
        assertTrue(periodPriceDTO.isPresent());
    }

    /**
     * 测试异常场景：aggregateResult 为空
     */
    @Test
    public void testMapResultAggregateResultIsNull() throws Throwable {
        // arrange
        SkuPeriodPriceFetcher fetcher = new SkuPeriodPriceFetcher();
        ProductDetailPageRequest request = mock(ProductDetailPageRequest.class);
        when(request.getProductTypeEnum()).thenReturn(ProductTypeEnum.DEAL);
        setRequest(fetcher, request);
        // act
        FetcherResponse<SkuPeriodPrice> result = fetcher.mapResult(null);
        // assert
        assertNotNull(result);
        assertNotNull(result.getReturnValue());
        Optional<PeriodPriceMergeDTO> periodPriceDTO = result.getReturnValue().getPeriodPriceDTO(123L);
        assertFalse(periodPriceDTO.isPresent());
    }

    /**
     * 测试异常场景：aggregateResult.getReturnValue() 返回的 QueryCenterAggregateReturnValue 为空
     */
    @Test
    public void testMapResultReturnValueIsNull() throws Throwable {
        // arrange
        SkuPeriodPriceFetcher fetcher = new SkuPeriodPriceFetcher();
        ProductDetailPageRequest request = mock(ProductDetailPageRequest.class);
        when(request.getProductTypeEnum()).thenReturn(ProductTypeEnum.DEAL);
        setRequest(fetcher, request);
        FetcherResponse<QueryCenterAggregateReturnValue> aggregateResult = mock(FetcherResponse.class);
        when(aggregateResult.getReturnValue()).thenReturn(null);
        // act
        FetcherResponse<SkuPeriodPrice> result = fetcher.mapResult(aggregateResult);
        // assert
        assertNotNull(result);
        assertNotNull(result.getReturnValue());
        Optional<PeriodPriceMergeDTO> periodPriceDTO = result.getReturnValue().getPeriodPriceDTO(123L);
        assertFalse(periodPriceDTO.isPresent());
    }

    /**
     * 测试异常场景：QueryCenterAggregateReturnValue.getDealGroupDTO() 返回的 DealGroupDTO 为空
     */
    @Test
    public void testMapResultDealGroupDTOIsNull() throws Throwable {
        // arrange
        SkuPeriodPriceFetcher fetcher = new SkuPeriodPriceFetcher();
        ProductDetailPageRequest request = mock(ProductDetailPageRequest.class);
        when(request.getProductTypeEnum()).thenReturn(ProductTypeEnum.DEAL);
        setRequest(fetcher, request);
        FetcherResponse<QueryCenterAggregateReturnValue> aggregateResult = mock(FetcherResponse.class);
        QueryCenterAggregateReturnValue returnValue = mock(QueryCenterAggregateReturnValue.class);
        when(aggregateResult.getReturnValue()).thenReturn(returnValue);
        when(returnValue.getDealGroupDTO()).thenReturn(null);
        // act
        FetcherResponse<SkuPeriodPrice> result = fetcher.mapResult(aggregateResult);
        // assert
        assertNotNull(result);
        assertNotNull(result.getReturnValue());
        Optional<PeriodPriceMergeDTO> periodPriceDTO = result.getReturnValue().getPeriodPriceDTO(123L);
        assertFalse(periodPriceDTO.isPresent());
    }

    /**
     * 测试异常场景：DealGroupDTO.getDeals() 返回的 deals 为空
     */
    @Test
    public void testMapResultDealsIsEmpty() throws Throwable {
        // arrange
        SkuPeriodPriceFetcher fetcher = new SkuPeriodPriceFetcher();
        ProductDetailPageRequest request = mock(ProductDetailPageRequest.class);
        when(request.getProductTypeEnum()).thenReturn(ProductTypeEnum.DEAL);
        setRequest(fetcher, request);
        FetcherResponse<QueryCenterAggregateReturnValue> aggregateResult = mock(FetcherResponse.class);
        QueryCenterAggregateReturnValue returnValue = mock(QueryCenterAggregateReturnValue.class);
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        when(aggregateResult.getReturnValue()).thenReturn(returnValue);
        when(returnValue.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getDeals()).thenReturn(new ArrayList<DealGroupDealDTO>());
        // act
        FetcherResponse<SkuPeriodPrice> result = fetcher.mapResult(aggregateResult);
        // assert
        assertNotNull(result);
        assertNotNull(result.getReturnValue());
        Optional<PeriodPriceMergeDTO> periodPriceDTO = result.getReturnValue().getPeriodPriceDTO(123L);
        assertFalse(periodPriceDTO.isPresent());
    }

    /**
     * 测试异常场景：DealGroupDTO.getDeals() 返回的 deals 包含空元素
     */
    @Test
    public void testMapResultDealsContainsNull() throws Throwable {
        // arrange
        SkuPeriodPriceFetcher fetcher = new SkuPeriodPriceFetcher();
        ProductDetailPageRequest request = mock(ProductDetailPageRequest.class);
        when(request.getProductTypeEnum()).thenReturn(ProductTypeEnum.DEAL);
        setRequest(fetcher, request);
        FetcherResponse<QueryCenterAggregateReturnValue> aggregateResult = mock(FetcherResponse.class);
        QueryCenterAggregateReturnValue returnValue = mock(QueryCenterAggregateReturnValue.class);
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        List<DealGroupDealDTO> deals = new ArrayList<>();
        deals.add(null);
        when(aggregateResult.getReturnValue()).thenReturn(returnValue);
        when(returnValue.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getDeals()).thenReturn(deals);
        // act
        FetcherResponse<SkuPeriodPrice> result = fetcher.mapResult(aggregateResult);
        // assert
        assertNotNull(result);
        assertNotNull(result.getReturnValue());
        Optional<PeriodPriceMergeDTO> periodPriceDTO = result.getReturnValue().getPeriodPriceDTO(123L);
        assertFalse(periodPriceDTO.isPresent());
    }
}
