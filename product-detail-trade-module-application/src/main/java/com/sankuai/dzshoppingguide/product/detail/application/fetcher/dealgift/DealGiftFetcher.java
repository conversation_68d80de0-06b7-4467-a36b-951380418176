package com.sankuai.dzshoppingguide.product.detail.application.fetcher.dealgift;

import com.dianping.lion.client.Lion;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dz.product.detail.RequestCustomParamEnum;
import com.sankuai.dz.product.detail.gateway.spi.enums.MobileOSTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.constants.LionConstants;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.cityId.CityIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.cityId.CityIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.dealgroup.DealGroupIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.dealgroup.DealGroupIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
import com.sankuai.dzshoppingguide.product.detail.application.utils.GsonUtils;
import com.sankuai.dzshoppingguide.product.detail.application.utils.RequestUtils;
import com.sankuai.dzshoppingguide.product.detail.application.utils.TimesDealUtil;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.PlayCenterPlatformEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.RequestSourceEnum;
import com.sankuai.mktplay.center.mkt.play.center.client.*;
import com.sankuai.mktplay.center.mkt.play.center.client.play.entity.ActiveRequestUnitV2;
import com.sankuai.nib.mkt.common.base.enums.DepartmentTypeEnum;
import com.sankuai.nib.mkt.common.base.enums.ProductTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2025/3/10 10:27
 */
@Fetcher(previousLayerDependencies = {DealGroupIdMapperFetcher.class,
        ShopIdMapperFetcher.class,
        CityIdMapperFetcher.class,
        ProductBaseInfoFetcher.class})
@Slf4j
public class DealGiftFetcher extends NormalFetcherContext<DealGiftResult> {

    private static final String DZ_DEAL_BIZ_CODE = "nib.general.groupbuy";
    private static final String COST_EFFECTIVE_SOURCE = "costEffective";
    private static final String SPECIAL_DEAL_PRODUCT_TYPE = "6";

    private static final String COST_EFFECTIVE_ICON = "https://p0.meituan.net/travelcube/f8b02bfbf00c465ad4a61ba5b8a685227006.png";
    private static final String SUMMER_PLAY_ID_PARAM = "summerPlayId";
    private static final String SURPRISE_GIFT_PLAY_ID_PARAM = "surpriseGiftPlayId";
    private static final String NEW_CUSTOMER_ACTIVITY = "newCustomerActivity";

    @Autowired
    private CompositeAtomService compositeAtomService;
    
    @Override
    protected CompletableFuture<DealGiftResult> doFetch() throws TException {
        DealGroupIdMapper dealGroupIdResult = getDependencyResult(DealGroupIdMapperFetcher.class);
        ShopIdMapper shopIdMapper = getDependencyResult(ShopIdMapperFetcher.class);
        CityIdMapper cityIdMapper = getDependencyResult(CityIdMapperFetcher.class);
        ProductBaseInfo productBaseInfo = getDependencyResult(ProductBaseInfoFetcher.class);

        // 接口调用过滤
        if (!isEnable(productBaseInfo)){
            return CompletableFuture.completedFuture(new DealGiftResult());
        }

        Map<String, Long> playIdMap = Lion.getMap("com.sankuai.dzu.tpbase.dztgdetailweb", LionConstants.DEAL_GIFT_PLAY_ID_CONFIG, Long.class);
        if (MapUtils.isEmpty(playIdMap)) {
            return CompletableFuture.completedFuture(new DealGiftResult());
        }
        CompletableFuture<PlayExecuteResponse> playActivityCf = CompletableFuture.completedFuture(null);
        // 特团渠道，需要查询暑促活动接口
        if (isFromCostEffective()) {
            playActivityCf = compositeAtomService.queryExecutePlay(buildPlayExecuteRequest(playIdMap.getOrDefault(SUMMER_PLAY_ID_PARAM, 0L),
                    buildRequestForExecutePlay(dealGroupIdResult, shopIdMapper, cityIdMapper)));
        }
        // 新客渠道，需要查询新客活动接口
        if (isNewCustomActivity()) {
            playActivityCf = compositeAtomService.queryNewCustomExecutePlay(buildPlayExecuteRequest(playIdMap.getOrDefault(NEW_CUSTOMER_ACTIVITY, 0L),
                    buildNewCustomExecutePlay(dealGroupIdResult, shopIdMapper, cityIdMapper)));
        }
        // 所有渠道均需查询惊喜买赠活动接口
        CompletableFuture<PlayExecuteResponse> playExecuteResponseCf = compositeAtomService.querySceneExecutePlay(buildScenePlayExecuteRequest(productBaseInfo, dealGroupIdResult,
                shopIdMapper, cityIdMapper,
                playIdMap.getOrDefault(SURPRISE_GIFT_PLAY_ID_PARAM, 0L),
                null
                ));
        CompletableFuture<PlayExecuteResponse> finalPlayActivityCf = playActivityCf;
        return CompletableFuture.allOf(finalPlayActivityCf, playExecuteResponseCf).thenApply(a -> new DealGiftResult(finalPlayActivityCf.join(), playExecuteResponseCf.join()));
    }

    private boolean isFromCostEffective() {
        if (Objects.equals(request.getPageSource(), RequestSourceEnum.COST_EFFECTIVE.getSource())) {
            return true;
        }
        return false;
    }

    public ScenePlayExecuteRequest buildScenePlayExecuteRequest(ProductBaseInfo productBaseInfo, DealGroupIdMapper dealGroupIdResult, ShopIdMapper shopIdMapper, CityIdMapper cityIdMapper, long playId,  Map<String, String> requestForExecutePlay){
        ScenePlayExecuteRequest scenePlayExecuteRequest = new ScenePlayExecuteRequest();
        scenePlayExecuteRequest.setPlayId(playId);
        scenePlayExecuteRequest.setUserInfo(buildUserInfo());
        scenePlayExecuteRequest.setRequest(buildRequestForSceneExecutePlay(productBaseInfo, cityIdMapper));
        scenePlayExecuteRequest.setActiveRequestUnit(buildActiveRequestUnit(dealGroupIdResult, shopIdMapper));
        return scenePlayExecuteRequest;
    }

    private List<ActiveRequestUnit> buildActiveRequestUnit(DealGroupIdMapper dealGroupIdResult, ShopIdMapper shopIdMapper) {
        Map<String, String> properties = new HashMap<>();
        //美团体系
        properties.put("dealId", String.valueOf(dealGroupIdResult.getMtDealGroupId()));
        properties.put("poiId", String.valueOf(shopIdMapper.getMtBestShopId()));
        properties.put("productType", String.valueOf(ProductTypeEnum.DAOZONG_GROUP.getCode()));

        ActiveRequestUnit activeRequestUnit = new ActiveRequestUnit();
        activeRequestUnit.setRowId(dealGroupIdResult.getMtDealGroupId());
        activeRequestUnit.setProperties(properties);
        return Lists.newArrayList(activeRequestUnit);
    }

    private Map<String, String> buildRequestForSceneExecutePlay(ProductBaseInfo productBaseInfo, CityIdMapper cityIdMapper) {
        String dealTimes = TimesDealUtil.parseTimes(productBaseInfo.getDeals());
        Map<String, String> request = new HashMap<>();
        // 不管美团还是点评，都传美团城市id
        request.put("cityId", String.valueOf(cityIdMapper.getMtCityId()));
        //1-美团APP；2-点评APP；5-美团微信小程序
        request.put("platformType", this.request.getClientTypeEnum().isMtClientType() ? "1" : "2");
        //0 ios 1安卓
        request.put("osType", MobileOSTypeEnum.IOS.equals(this.request.getMobileOSType()) ? "0" : "1");
        request.put("riskParams", buildRiskParams());
        request.put("autoSign", "true");
        request.put("sceneProductType", String.valueOf(ProductTypeEnum.DAOZONG_GROUP.getCode()));
        //团单次卡的次数，兜底传1
        request.put("couponQuantity", StringUtils.isEmpty(dealTimes) ? "1" : dealTimes);
        return request;
    }
    private String buildRiskParams() {
        Map<String, String> riskParams = new HashMap<>();
        if (request.getClientTypeEnum().isMtClientType()) {
            riskParams.put("userid", String.valueOf(request.getMtUserId()));
        } else {
            riskParams.put("dpUserid", String.valueOf(request.getDpUserId()));
        }
        riskParams.put("uuid", request.getShepherdGatewayParam().getUnionid());
        riskParams.put("ip", request.getShepherdGatewayParam().getUserIp());
        riskParams.put("partner", "0");
        //平台标识，如4代表安卓，5代表IOS平台，微信小程序传13
        riskParams.put("platform", MobileOSTypeEnum.IOS.equals(this.request.getMobileOSType()) ? "5" : "4");
        riskParams.put("version", request.getShepherdGatewayParam().getAppVersion());
        riskParams.put("app", "0");
        return JsonCodec.encode(riskParams);
    }
    
    public PlayExecuteRequest buildPlayExecuteRequest(long playId,  Map<String, String> requestForExecutePlay){
        PlayExecuteRequest playExecuteRequest = new PlayExecuteRequest();
        playExecuteRequest.setPlayId(playId);
        playExecuteRequest.setUserInfo(buildUserInfo());
        playExecuteRequest.setRequest(requestForExecutePlay);
        return playExecuteRequest;
    }

    private UserInfo buildUserInfo() {
        UserInfo userInfo = new UserInfo();
        userInfo.setUserId(request.getUserId());
        userInfo.setUserSource(request.getClientTypeEnum().isMtClientType() ? UserSourceEnum.MT.getValue() : UserSourceEnum.DP.getValue());
        return userInfo;
    }

    private Map<String, String> buildRequestForExecutePlay(DealGroupIdMapper dealGroupIdResult, ShopIdMapper shopIdMapper, CityIdMapper cityIdMapper) {
        Map<String, String> request = Maps.newHashMap();
        request.put("cityId", String.valueOf(cityIdMapper.getMtCityId()));
        request.put("sceneProductType", SPECIAL_DEAL_PRODUCT_TYPE);
        request.put("productType", SPECIAL_DEAL_PRODUCT_TYPE);
        String source = Objects.equals(this.request.getPageSource(), RequestSourceEnum.COST_EFFECTIVE.getSource()) ? COST_EFFECTIVE_SOURCE : "";
        request.put("trafficSource", source);
        request.put("platformType", this.request.getClientTypeEnum().isMtClientType() ? String.valueOf(UserSourceEnum.MT.getValue()) :
                String.valueOf(UserSourceEnum.DP.getValue()));
        request.put("activeRequestUnit", GsonUtils.toJsonString(buildActiveRequestUnitV2(dealGroupIdResult, shopIdMapper, cityIdMapper)));
        return request;
    }

    private List<ActiveRequestUnitV2> buildActiveRequestUnitV2(DealGroupIdMapper dealGroupIdResult, ShopIdMapper shopIdMapper, CityIdMapper cityIdMapper) {
        List<String> idFlags = Lists.newArrayList("dealId", "poiId");
        Map<String, Long> idMap = Maps.newHashMap();
        idMap.put("dealId",  dealGroupIdResult.getMtDealGroupId());
        idMap.put("poiId", shopIdMapper.getMtBestShopId());
        return idFlags.stream()
                .filter(idFlag -> idMap.containsKey(idFlag) && idMap.get(idFlag) != null)
                .map(flag -> createActiveRequestUnitV2(flag, idMap.get(flag))).filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private ActiveRequestUnitV2 createActiveRequestUnitV2(String idKey, Long idValue) {
        if (idValue == null) {
            return null;
        }
        Map<String, String> properties = initBaseMap();
        properties.put(idKey, String.valueOf(idValue));
        ActiveRequestUnitV2 activeRequestUnitV2 = new ActiveRequestUnitV2();
        activeRequestUnitV2.setRowId(idValue);
        activeRequestUnitV2.setProperties(properties);
        return activeRequestUnitV2;
    }

    private Map<String, String> initBaseMap() {
        Map<String, String> properties = Maps.newHashMap();
        properties.put("departmentType", String.valueOf(DepartmentTypeEnum.DAOZONG.getCode()));
        properties.put("bizcode", DZ_DEAL_BIZ_CODE);
        return properties;
    }

    /**
     * 判断是否为新客活动
     * https://km.sankuai.com/collabpage/2387363811
     * @return
     */
    public boolean isNewCustomActivity() {
        return Objects.equals(request.getPageSource(), RequestSourceEnum.NEW_CUSTOMER_ACTIVITY.getSource());
    }

    public Map<String, String> buildNewCustomExecutePlay(DealGroupIdMapper dealGroupIdResult, ShopIdMapper shopIdMapper, CityIdMapper cityIdMapper) {
        Map<String, String> request = Maps.newHashMap();
        request.put("cityId", String.valueOf(cityIdMapper.getDpCityId()));
        request.put("platformType", String.valueOf(getPlatformTypeByUserSource(this.request).getId()));
        request.put("osType", MobileOSTypeEnum.IOS.equals(this.request.getMobileOSType()) ? "0" : "1");
        request.put("activeRequestUnit", GsonUtils.toJsonString(buildActiveRequestUnitV2(dealGroupIdResult, shopIdMapper, cityIdMapper)));
        // 获取请求的额外参数, 待前端确认
        String extParam = getExtParam(this.request.getCustomParam(RequestCustomParamEnum.extparam));
        String activityToken = GsonUtils.getParamFromMapJson(extParam, "activityToken", String.class);
        if (StringUtils.isNotEmpty(activityToken) && RequestSourceEnum.NEW_CUSTOMER_ACTIVITY.getSource().equals(this.request.getPageSource())) {
            request.put("activityToken", activityToken);
        }
        return request;
    }

    public String getExtParam(String extParam) {
        if (Objects.equals(extParam, "undefined")) {
            return org.apache.commons.lang3.StringUtils.EMPTY;
        }
        return extParam;
    }
    /**
     * 根据用户来源获取平台类型
     *
     * @param
     * @return
     */
    public PlayCenterPlatformEnum getPlatformTypeByUserSource(ProductDetailPageRequest request) {
        try {
            if (RequestUtils.isMtMiniApp(request)) {
                return PlayCenterPlatformEnum.MT_WEAPP;
            }
            if (RequestUtils.isDpMiniApp(request)) {
                return PlayCenterPlatformEnum.DP_WEAPP;
            }
            if (request.getClientTypeEnum().isMtClientType()) {
                return PlayCenterPlatformEnum.MEITUAN_APP;
            }
            if (request.getClientTypeEnum().isDpClientType()) {
                return PlayCenterPlatformEnum.DIANPING_APP;
            }
        } catch (Exception e) {
            log.error("PlayCenterWrapper getPlatformTypeByUserSource error!, request = " + GsonUtils.toJsonString(request), e);
        }
        return PlayCenterPlatformEnum.UNKNOWN;
    }

    public boolean isEnable(ProductBaseInfo productBaseInfo) {
        if (RequestSourceEnum.fromTradeSnapshot(request.getPageSource())
                || Objects.isNull(productBaseInfo) || Objects.isNull(productBaseInfo.getCategory())) {
            return false;
        }
        long categoryId = productBaseInfo.getCategory().getCategoryId();
        //根据团单二级类目查询品类
        List<Long> categoryIds = Lion.getList("com.sankuai.dzu.tpbase.dztgdetailweb", LionConstants.DEAL_GIFT_CATEGORY_CONFIG, Long.class);
        if (CollectionUtils.isEmpty(categoryIds)) {
            return false;
        }
        return categoryIds.contains(categoryId);
    }
}
