package com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.builder.enums;

import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.extend.BaseVariableBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.builder.handler.cepintuan.CEPinTuanBottomBarBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.builder.handler.gym.MonthlySubscriptionBottomBarBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.builder.handler.standard.impl.DefaultBottomBarBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.builder.handler.standard.impl.LiveDealBottomBarBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.builder.handler.standard.impl.PreviewDealBottomBarBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.builder.handler.zero.reserve.CommonFreeDealReserveBottomBarBuilder;
import lombok.Getter;

/**
 * @Author: guangyujie
 * @Date: 2025/3/10 15:58
 */
@Getter
public enum BottomBarSceneEnum {

    DEFAULT("兜底底Bar", DefaultBottomBarBuilder.class),
    PREVIEW("预览团单", PreviewDealBottomBarBuilder.class),
    LIVE("美团直播", LiveDealBottomBarBuilder.class),
    MT_LIVE_XCX("私域直播，美团美播微信小程序&&美团美播提单小程序", null),
    FREE_RESERVATION_DEAL("0元预约团单", null),
    ODP("分销渠道", null),
    COST_EFFECTIVE_PIN_TUAN(" 特团拼团主态", CEPinTuanBottomBarBuilder.class),
    FITNESS_CROSS("健身通", null),
    PREPAY_DEAL("预付团单", null),
    LEADS_DEAL("留资团单", null),
    ZERO_YUAN_RESERVE("0元预约不可交易", CommonFreeDealReserveBottomBarBuilder.class),
    MEMBER_EXCLUSIVE("商家会员专属商品", null),
    MONTHLY_SUBSCRIPTION("连续包月", MonthlySubscriptionBottomBarBuilder.class);

    private final String desc;

    private final Class<? extends BaseVariableBuilder> builderClass;

    BottomBarSceneEnum(String desc, Class<? extends BaseVariableBuilder> builderClass) {
        this.desc = desc;
        this.builderClass = builderClass;
    }

}