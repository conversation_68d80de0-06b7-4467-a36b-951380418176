package com.sankuai.dzshoppingguide.product.detail.application.utils;

import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.dianping.vc.sdk.dp.config.LionObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommendgoods.dtos.ProductActivityM;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommendgoods.dtos.ProductM;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommendgoods.dtos.ProductPromoPriceM;
import com.sankuai.dztheme.deal.res.enums.PromoTypeEnum;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/6/1 7:30 下午
 */
public class PerfectActivityBuildUtils {

    private static LionObject<PerfectActivityConstant> perfectActivityConstantLionObject = LionObject.create("com.sankuai.dzviewscene.productshelf.perfect.activity.constant.config", new TypeReference<PerfectActivityConstant>() {});

    public static boolean hasPerfectActivity(ProductM productM) {
        ProductActivityM perfectActivity = extractPerfectActivity(productM);
        ProductActivityM perfectPreHeatActivity = extractPerfectPreHeatActivity(productM);
        if(perfectActivity == null && perfectPreHeatActivity == null) {
            return false;
        }
        return true;
    }



    private static long getActivityStartTime(ProductM productM) {
        Cat.logEvent("INVALID_METHOD_1", "com.sankuai.dzviewscene.shelf.business.utils.PerfectActivityBuildUtils.getActivityStartTime(com.sankuai.dzviewscene.shelf.platform.common.model.ProductM)");
        ProductActivityM perfectActivity = extractPerfectActivity(productM);
        ProductActivityM perfectPreHeatActivity = extractPerfectPreHeatActivity(productM);
        if(perfectActivity == null && perfectPreHeatActivity == null) {
            return Long.MAX_VALUE;
        }
        if(perfectActivity == null && perfectPreHeatActivity != null) {
            return perfectPreHeatActivity.getRemainingTime() * 1000;
        }
        return perfectActivity.getActivityBeginTime();
    }

    private static long getPreheatActivityStartTime(ProductM productM) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.utils.PerfectActivityBuildUtils.getPreheatActivityStartTime(com.sankuai.dzviewscene.shelf.platform.common.model.ProductM)");
        ProductActivityM perfectPreHeatActivity = extractPerfectPreHeatActivity(productM);
        ProductActivityM prefectActivity = extractPerfectActivity(productM);
        if(perfectPreHeatActivity == null && prefectActivity == null) {
            return Long.MAX_VALUE;
        }
        if(perfectPreHeatActivity == null && prefectActivity != null) {
            return Long.MIN_VALUE;
        }
        return perfectPreHeatActivity.getActivityBeginTime();
    }

    private static long getActivityEndTime(ProductM productM) {
        ProductActivityM perfectActivity = extractPerfectActivity(productM);
        ProductActivityM perfectPreHeatActivity = extractPerfectPreHeatActivity(productM);
        if(perfectActivity == null && perfectPreHeatActivity == null) {
            return Long.MIN_VALUE;
        }
        if(perfectActivity == null && perfectPreHeatActivity != null) {
            return Long.MAX_VALUE;
        }
        return perfectActivity.getRemainingTime() * 1000;
    }

    public static BigDecimal getPerfectActivityPromoPrice(ProductM productM) {
        if(productM == null) {
            return null;
        }
        ProductPromoPriceM productPromoPriceM = productM.getPromo(8);
        if(productPromoPriceM == null) {
            return null;
        }
        return productPromoPriceM.getPromoPrice();
    }

    public static boolean isDuringPerfectActivity(ProductM productM) {
        if(!isShowPerfectActivity(productM)) {
            return false;
        }
        long startTime = getActivityStartTime(productM);
        long endTime = getActivityEndTime(productM);
        return startTime < System.currentTimeMillis() && System.currentTimeMillis() < endTime;
    }

    public static boolean isShowPerfectActivity(ProductM productM) {
        if(!hasPerfectActivity(productM)) {
            return false;
        }
        long now = System.currentTimeMillis();
        if(getActivityEndTime(productM) < now || getPreheatActivityStartTime(productM) > now) {
            return false;
        }
        return isPerfectActivityPriceLessThanIdlePromoPrice(productM) && isPerfectActivityPricesLessThanDailyPrice(productM);
    }


    private static boolean isPerfectActivityPriceLessThanIdlePromoPrice(ProductM productM) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.utils.PerfectActivityBuildUtils.isPerfectActivityPriceLessThanIdlePromoPrice(com.sankuai.dzviewscene.shelf.platform.common.model.ProductM)");
        if(productM == null) {
            return false;
        }
        ProductPromoPriceM idlePromo = productM.getPromo(PromoTypeEnum.IDLE_PROMO.getType());
        ProductPromoPriceM perfectPromo = productM.getPromo(8);
        if(perfectPromo == null || perfectPromo.getPromoPrice() == null) {
            return false;
        }
        if(idlePromo == null || idlePromo.getPromoPrice() == null) {
            return true;
        }
        return idlePromo.getPromoPrice().compareTo(perfectPromo.getPromoPrice()) > 0;
    }

    private static boolean isPerfectActivityPricesLessThanDailyPrice(ProductM productM) {
        Cat.logEvent("INVALID_METHOD_4", "com.sankuai.dzviewscene.shelf.business.utils.PerfectActivityBuildUtils.isPerfectActivityPricesLessThanDailyPrice(com.sankuai.dzviewscene.shelf.platform.common.model.ProductM)");
        long now = System.currentTimeMillis();
        long startTime = getActivityStartTime(productM);
        if(now > startTime) {
            return true;
        }
        if(productM == null) {
            return false;
        }
        ProductPromoPriceM perfectPromo = productM.getPromo(8);
        BigDecimal dailyPrice = getDailyPrice(productM);
        if(perfectPromo == null || perfectPromo.getPromoPrice() == null) {
            return false;
        }
        if(dailyPrice == null) {
            return true;
        }
        return perfectPromo.getPromoPrice().compareTo(dailyPrice) < 0;

    }

    private static BigDecimal getDailyPrice(ProductM productM) {
        Cat.logEvent("INVALID_METHOD_2", "com.sankuai.dzviewscene.shelf.business.utils.PerfectActivityBuildUtils.getDailyPrice(com.sankuai.dzviewscene.shelf.platform.common.model.ProductM)");
        ProductPromoPriceM productPromoPriceM = productM.getPromo(PromoTypeEnum.DIRECT_PROMO.getType());
        if (productPromoPriceM == null) {
            return productM.getBasePrice();
        }
        return productPromoPriceM.getPromoPrice();
    }

    public static ProductActivityM extractPerfectActivity(ProductM productM) {
        if(productM == null || CollectionUtils.isEmpty(productM.getActivities()) || CollectUtils.firstValue(productM.getActivities()) == null) {
            return null;
        }
        //玩美季活动判断
        return productM.getActivities().stream().filter(activity -> isPerfectActivity(activity)).findFirst().orElse(null);
    }

    private static boolean isPerfectActivity(ProductActivityM activityM) {
        if(activityM == null) {
            return false;
        }
        boolean typeSwitch = Lion.getBooleanValue("com.sankuai.dzviewscene.productshelf.perfect.activity.type.switch", false);
        if(typeSwitch) {
            return activityM.getShelfActivityType() != null && activityM.getShelfActivityType() == 1 && !activityM.isPreheat();
        }
        return activityM.getActivityBeginTime() != 0 && !activityM.isPreheat();
    }

    private static boolean isPreheatPerfectActivity(ProductActivityM activityM) {
        if(activityM == null) {
            return false;
        }
        boolean typeSwitch = Lion.getBooleanValue("com.sankuai.dzviewscene.productshelf.perfect.activity.type.switch", false);
        if(typeSwitch) {
            return activityM.getShelfActivityType() != null && activityM.getShelfActivityType() == 1 && activityM.isPreheat();
        }
        return activityM.getActivityBeginTime() != 0 && activityM.isPreheat();
    }


    public static ProductActivityM extractPerfectPreHeatActivity(ProductM productM) {
        if(productM == null || CollectionUtils.isEmpty(productM.getActivities()) || CollectUtils.firstValue(productM.getActivities()) == null) {
            return null;
        }
        //玩美季活动判断
        return productM.getActivities().stream().filter(PerfectActivityBuildUtils::isPreheatPerfectActivity).findFirst().orElse(null);
    }

    @Data
    private static class PerfectActivityConstant{
        private String priceDefaultDoc;
        private String priceDefaultColor;
        private String activityDatePrefix;
        private String activityDateSuffx;
        private String activityDateColor;
        private int activityDateSize;
        private String activityTimeDoc;
        private String activityTimeColor;
        private int activityTimeSize;
        private String secondaryActivityTimeDoc;
        private String secondaryActivityTimeColor;
        private int secondaryActivityTimeSize;
        private int headPicLabelHeight;
        private double headPicLabelAspectRadio;
        private int preheatHeadPicLabelHeight;
        private double preheatHeadPicLabelAspectRadio;
        private int doubleHeadPicLabelHeight;
        private double doubleHeadPicLabelAspectRadio;
        private int doublePreheatHeadPicLabelHeight;
        private double doublePreheatHeadPicLabelAspectRadio;
        private String filterTabColor;
        private String selectedFilterColor;
        private int filterTabSize;
        private int selectedFilterSize;
        private String activityName;
        private List<String> sceneCodeWithStartTimeList;
        private String priceDefaultDocWithoutLock;
    }
}

