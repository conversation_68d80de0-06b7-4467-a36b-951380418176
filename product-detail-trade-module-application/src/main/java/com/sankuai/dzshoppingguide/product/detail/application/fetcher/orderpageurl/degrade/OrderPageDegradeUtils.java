package com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.degrade;

import com.dianping.cat.Cat;
import com.dianping.tuangu.dztg.usercenter.api.dto.BatchGetCreateOrderPageUrlDto;
import com.dianping.tuangu.dztg.usercenter.api.enums.ChannelEnum;
import com.dianping.tuangu.dztg.usercenter.api.request.BatchGetCreateOrderPageUrlReq;
import com.google.common.collect.Maps;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.enums.OrderPageExtParamEnums;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Optional;

/**
 * @Author: guangyujie
 * @Date: 2025/3/12 16:46
 */
@Slf4j
@Component
public class OrderPageDegradeUtils {

    private static final String MT_PREFIX = "imeituan://www.meituan.com/mrn?mrn_biz=gc&mrn_entry=group-order-submit&mrn_component=GroupOrderSubmit&isTransparent=true&mrn_transparent=ture&hideLoading=true&mrn_hideloading=true&mrn_hideNextNavBar=true&pagesource=dealGroupDetail";
    private static final String DP_PREFIX = "dianping://mrn?mrn_biz=gc&mrn_entry=group-order-submit&mrn_component=GroupOrderSubmit&isTransparent=true&mrn_transparent=ture&hideLoading=true&mrn_hideloading=true&mrn_hideNextNavBar=true&pagesource=dealGroupDetail";

    private static final String PARAM = "&dealid=%s&skuid=%s&shopid=%s&trafficflag=%s";

    public String orderPageUrlDegrade(BatchGetCreateOrderPageUrlReq req) {
        Map<String, String> tags = Maps.newConcurrentMap();
        tags.put("platform", req.getEnvDto() == null ? "0" : String.valueOf(req.getEnvDto().getPlatform()));
        try {
            // 仅APP场景支持降级自行拼跳链
            if (req.getEnvDto() == null || req.getEnvDto().getChannel() != ChannelEnum.APP.type) {
                tags.put("degrade", "fail");
                return "";
            }
            if (CollectionUtils.isEmpty(req.getBatchGetCreateOrderPageUrlDtoList()) || req.getBatchGetCreateOrderPageUrlDtoList().get(0) == null) {
                tags.put("degrade", "fail");
                return "";
            }
            BatchGetCreateOrderPageUrlDto pageUrlDto = req.getBatchGetCreateOrderPageUrlDtoList().get(0);
            String params = String.format(PARAM, pageUrlDto.getProductId(), pageUrlDto.getSkuId(), pageUrlDto.getShopId(), Optional.ofNullable(pageUrlDto.getExtUrlParam()).map(a -> a.get(OrderPageExtParamEnums.trafficflag.name())).orElse(""));
            tags.put("degrade", "success");
            return (req.getEnvDto().getPlatform() == 1 ? MT_PREFIX : DP_PREFIX) + params;
        } catch (Exception e) {
            log.warn("降级处理失败");
        } finally {
            Cat.logMetricForCount("DEAL_URL_DEGRADE_INTERNAL", 1, tags);
        }
        return "";
    }

    public static String orderPageUrlDegrade(ProductDetailPageRequest request) {
        Map<String, String> tags = Maps.newConcurrentMap();
        tags.put("platform", request.getClientTypeEnum().isMtClientType() ? "1" : "2");
        try {
            // 仅APP场景支持降级自行拼跳链
            if (!request.getClientTypeEnum().isInApp()) {
                tags.put("degrade", "fail");
                return "";
            }
            String params = String.format(PARAM, request.getProductId(), request.getSkuId(), request.getPoiId(), "");
            tags.put("degrade", "success");
            return (request.getClientTypeEnum().isMtClientType() ? MT_PREFIX : DP_PREFIX) + params;
        } catch (Exception e) {
            log.warn("降级处理失败", e);
        } finally {
            Cat.logMetricForCount("DEAL_URL_DEGRADE", 1, tags);
        }
        return "";
    }

}
