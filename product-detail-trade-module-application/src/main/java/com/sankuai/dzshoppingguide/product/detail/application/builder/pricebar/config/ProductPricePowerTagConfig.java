package com.sankuai.dzshoppingguide.product.detail.application.builder.pricebar.config;

import com.sankuai.dealuser.price.display.api.enums.PricePowerTagEnum;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-04-24
 * @desc
 */
@Data
public class ProductPricePowerTagConfig {
    /**
     * 排序的价格力标签
     */
    private List<PricePowerTagEnum> sortPowerTags;
    /**
     * 生效的二级类目
     */
    private List<Integer> categoryIds;
}
