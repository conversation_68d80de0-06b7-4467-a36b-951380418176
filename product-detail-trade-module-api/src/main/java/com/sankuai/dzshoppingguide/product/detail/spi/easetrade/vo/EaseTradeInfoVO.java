package com.sankuai.dzshoppingguide.product.detail.spi.easetrade.vo;

import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.dz.product.detail.gateway.spi.vo.AbstractModuleVO;
import lombok.Data;

import java.util.List;

import static com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants.MODULE_DETAIL_DEAL_EASE_TRADE_INFO;

/**
 * https://mobile.sankuai.com/studio/model/info/43117
 */
@Data
@TypeDoc(description = "安心交易提示信息")
public class EaseTradeInfoVO extends AbstractModuleVO {
    /**
     * 背景色-终止值
     */
    @MobileField
    private String backgroundColorEnd;

    /**
     * 背景色-起始值
     */
    @MobileField
    private String backgroundColorStart;
    /**
     * 数据内容
     */
    @MobileField
    private List<DetailTextVO> data;

    @Override
    public String getModuleKey() {
        return MODULE_DETAIL_DEAL_EASE_TRADE_INFO;
    }
}
