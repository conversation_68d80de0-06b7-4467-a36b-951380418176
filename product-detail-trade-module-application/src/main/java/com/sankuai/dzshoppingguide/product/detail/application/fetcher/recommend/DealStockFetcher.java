package com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommend;


import com.dianping.deal.sales.common.datatype.ProductParam;
import com.dianping.deal.sales.common.datatype.SalesDisplayRequest;
import com.dianping.deal.sales.common.enums.SalesPlatform;
import com.google.common.collect.Maps;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommend.dto.RecommendInfoReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommend.dto.SaleConstants;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommend.dto.SalesDisplayReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections.MapUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * @Author: zhangyuan103
 * @Date: 2025/5/26
 * @Description 销量展示信息获取
 */
@Fetcher(previousLayerDependencies = {RecommendInfoInShopFetcher.class})
@Slf4j
public class DealStockFetcher extends NormalFetcherContext<SalesDisplayReturnValue> {
    @Resource
    private CompositeAtomService compositeAtomService;


    @Override
    protected CompletableFuture<SalesDisplayReturnValue> doFetch() throws Exception {
        RecommendInfoReturnValue recommendInfoReturnValue = getDependencyResult(RecommendInfoInShopFetcher.class);
        List<Long> sortedDealGroupIds = Optional.ofNullable(recommendInfoReturnValue).map(RecommendInfoReturnValue::getSortedDealGroupIds).orElse(null);
        if (CollectionUtils.isEmpty(sortedDealGroupIds)) {
            return CompletableFuture.completedFuture(null);
        }
        return compositeAtomService.multiGetSales(buildSalesDisplayRequest(sortedDealGroupIds)).thenApply(salesDisplayDTOMap -> {
            if (MapUtils.isEmpty(salesDisplayDTOMap)) {
                return null;
            }
            return new SalesDisplayReturnValue(salesDisplayDTOMap);
        });
    }

    private SalesDisplayRequest buildSalesDisplayRequest(List<Long> sortedDealGroupIds) {
        List<ProductParam> productParamList = sortedDealGroupIds.stream()
                .map(dealGroupId -> ProductParam.productScene(dealGroupId, request.getCityId()))
                .collect(Collectors.toList());
        SalesDisplayRequest multiRequest = SalesDisplayRequest.multiQuery(productParamList);
        multiRequest.setPlatform(request.getClientTypeEnum().isMtClientType() ? SalesPlatform.MT.getValue() : SalesPlatform.DP.getValue());
        Map<String, String> extra = Maps.newHashMap();
        //透传销量区间差异化，反爬标识
        extra.put(SaleConstants.MTSI_FLAG, request.getShepherdGatewayParam().getMtsiflag());
        extra.put(SaleConstants.CONFUSION_FLAG, SaleConstants.GENERAL_SECTION);
        multiRequest.setExtra(extra);
        return multiRequest;
    }
}
