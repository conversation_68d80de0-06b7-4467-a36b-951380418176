package com.sankuai.dzshoppingguide.product.detail.application.utils;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Calendar;
import java.util.Date;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * Test class for TimeUtils.buildVideoTime method
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("TimeUtils formatOvernightTime Test")
public class TimeUtilsTest {

    private LocalDateTime now;

    @BeforeEach
    public void setUp() {
        // 获取当前时间
        now = LocalDateTime.now();
    }

    /**
     * Tests the buildVideoTime method with a normal input (positive seconds).
     * Input: 120 seconds
     * Expected: "02:00"
     */
    @Test
    public void testBuildVideoTimeNormal() throws Throwable {
        // arrange
        long time = 120;
        // act
        String result = TimeUtils.buildVideoTime(time);
        // assert
        assertEquals("02:00", result);
    }

    /**
     * Tests the buildVideoTime method with a boundary input (0 seconds).
     * Input: 0 seconds
     * Expected: "00:00"
     */
    @Test
    public void testBuildVideoTimeBoundary() throws Throwable {
        // arrange
        long time = 0;
        // act
        String result = TimeUtils.buildVideoTime(time);
        // assert
        assertEquals("00:00", result);
    }

    /**
     * Tests the buildVideoTime method with an exceptional input (negative seconds).
     * Note: The method's current implementation formats negative numbers as "0-2:00"
     * Input: -120 seconds
     * Expected: "0-2:00"
     */
    @Test
    public void testBuildVideoTimeException() throws Throwable {
        // arrange
        long time = -120;
        // act
        String result = TimeUtils.buildVideoTime(time);
        // assert
        // Matches the actual implementation behavior
        assertEquals("0-2:00", result);
    }

    /**
     * Tests the buildVideoTime method with single digit minutes and seconds.
     * Input: 65 seconds
     * Expected: "01:05"
     */
    @Test
    public void testBuildVideoTimeSingleDigits() throws Throwable {
        // arrange
        long time = 65;
        // act
        String result = TimeUtils.buildVideoTime(time);
        // assert
        assertEquals("01:05", result);
    }

    /**
     * Tests the buildVideoTime method with large input.
     * Input: 3661 seconds (1 hour + 1 minute + 1 second)
     * Expected: "61:01"
     */
    @Test
    public void testBuildVideoTimeLargeInput() throws Throwable {
        // arrange
        long time = 3661;
        // act
        String result = TimeUtils.buildVideoTime(time);
        // assert
        assertEquals("61:01", result);
    }

    /**
     * 测试 getDaysAgoMidnightTimestamp 方法，获取当前时间减去两天后的午夜时间戳
     */
    @Test
    public void testGetDaysAgoMidnightTimestamp() {
        // arrange
        // 计算当前时间减去两天后的午夜时间戳
        LocalDateTime twoDaysAgoMidnight = now.minusDays(2).withHour(0).withMinute(0).withSecond(0).withNano(0);
        // 转换为时间戳（毫秒）
        long expectedTimestamp = twoDaysAgoMidnight.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
        // act
        long actualTimestamp = TimeUtils.getDaysAgoMidnightTimestamp();
        // assert
        assertEquals(expectedTimestamp, actualTimestamp, "The timestamp should match the expected value.");
    }

    /**
     * Test adding a positive number of days to a date
     */
    @Test
    public void testGetFewDaysLaterBeginTimePositiveDays() throws Throwable {
        // arrange
        Calendar calendar = Calendar.getInstance();
        // 2023-01-01
        calendar.set(2023, Calendar.JANUARY, 1);
        Date startDate = calendar.getTime();
        int daysToAdd = 5;
        // act
        Date result = TimeUtils.getFewDaysLaterBeginTime(startDate, daysToAdd);
        // assert
        Calendar expectedCal = Calendar.getInstance();
        expectedCal.setTime(startDate);
        expectedCal.add(Calendar.DATE, daysToAdd);
        Date expectedDate = expectedCal.getTime();
        assertEquals(expectedDate, result);
    }

    /**
     * Test adding zero days to a date
     */
    @Test
    public void testGetFewDaysLaterBeginTimeZeroDays() throws Throwable {
        // arrange
        Calendar calendar = Calendar.getInstance();
        // 2023-01-01
        calendar.set(2023, Calendar.JANUARY, 1);
        Date startDate = calendar.getTime();
        int daysToAdd = 0;
        // act
        Date result = TimeUtils.getFewDaysLaterBeginTime(startDate, daysToAdd);
        // assert
        Calendar expectedCal = Calendar.getInstance();
        expectedCal.setTime(startDate);
        expectedCal.add(Calendar.DATE, daysToAdd);
        Date expectedDate = expectedCal.getTime();
        assertEquals(expectedDate, result);
    }

    /**
     * Test adding a negative number of days to a date
     */
    @Test
    public void testGetFewDaysLaterBeginTimeNegativeDays() throws Throwable {
        // arrange
        Calendar calendar = Calendar.getInstance();
        // 2023-01-01
        calendar.set(2023, Calendar.JANUARY, 1);
        Date startDate = calendar.getTime();
        int daysToAdd = -3;
        // act
        Date result = TimeUtils.getFewDaysLaterBeginTime(startDate, daysToAdd);
        // assert
        Calendar expectedCal = Calendar.getInstance();
        expectedCal.setTime(startDate);
        expectedCal.add(Calendar.DATE, daysToAdd);
        Date expectedDate = expectedCal.getTime();
        assertEquals(expectedDate, result);
    }

    /**
     * Test with null input date
     */
    @Test
    public void testGetFewDaysLaterBeginTimeNullDate() throws Throwable {
        // arrange
        Date startDate = null;
        int daysToAdd = 5;
        // act & assert
        assertThrows(NullPointerException.class, () -> {
            TimeUtils.getFewDaysLaterBeginTime(startDate, daysToAdd);
        });
    }

    /**
     * Test when input is null
     */
    @Test
    public void testFormatOvernightTimeWhenInputNull() throws Throwable {
        // arrange
        String timeStr = null;
        // act
        String result = TimeUtils.formatOvernightTime(timeStr);
        // assert
        assertEquals("", result, "Should return empty string for null input");
    }

    /**
     * Test when input is empty string
     */
    @Test
    public void testFormatOvernightTimeWhenInputEmpty() throws Throwable {
        // arrange
        String timeStr = "";
        // act
        String result = TimeUtils.formatOvernightTime(timeStr);
        // assert
        assertEquals("", result, "Should return empty string for empty input");
    }

    /**
     * Test when input is blank string
     */
    @Test
    public void testFormatOvernightTimeWhenInputBlank() throws Throwable {
        // arrange
        String timeStr = "   ";
        // act
        String result = TimeUtils.formatOvernightTime(timeStr);
        // assert
        assertEquals("", result, "Should return empty string for blank input");
    }

    /**
     * Test normal time before midnight
     */
    @Test
    public void testFormatOvernightTimeWhenNormalTime() throws Throwable {
        // arrange
        String timeStr = "14:30";
        // act
        String result = TimeUtils.formatOvernightTime(timeStr);
        // assert
        assertEquals("14:30", result, "Should return same time format for normal time");
    }

    /**
     * Test overnight time (next day)
     */
    @Test
    public void testFormatOvernightTimeWhenOvernight() throws Throwable {
        // arrange
        String timeStr = "25:45";
        // act
        String result = TimeUtils.formatOvernightTime(timeStr);
        // assert
        assertEquals("次日01:45", result, "Should return next day format for overnight time");
    }

    /**
     * Test exactly midnight time
     */
    @Test
    public void testFormatOvernightTimeWhenMidnight() throws Throwable {
        // arrange
        String timeStr = "24:00";
        // act
        String result = TimeUtils.formatOvernightTime(timeStr);
        // assert
        assertEquals("次日00:00", result, "Should return next day format for midnight");
    }

    /**
     * Test time just before midnight
     */
    @Test
    public void testFormatOvernightTimeWhenJustBeforeMidnight() throws Throwable {
        // arrange
        String timeStr = "23:59";
        // act
        String result = TimeUtils.formatOvernightTime(timeStr);
        // assert
        assertEquals("23:59", result, "Should return same format for time before midnight");
    }

    /**
     * Test time with single digit values
     */
    @Test
    public void testFormatOvernightTimeWhenSingleDigits() throws Throwable {
        // arrange
        String timeStr = "9:5";
        // act
        String result = TimeUtils.formatOvernightTime(timeStr);
        // assert
        assertEquals("09:05", result, "Should pad single digits with zeros");
    }

    /**
     * Test large hour value
     */
    @Test
    public void testFormatOvernightTimeWhenLargeHour() throws Throwable {
        // arrange
        String timeStr = "47:30";
        // act
        String result = TimeUtils.formatOvernightTime(timeStr);
        // assert
        assertEquals("次日23:30", result, "Should handle large hour values correctly");
    }

    /**
     * Test invalid time format
     */
    @Test
    public void testFormatOvernightTimeWhenInvalidFormat() throws Throwable {
        // arrange
        String timeStr = "invalid:time";
        // assert
        // act
        // act
        assertThrows(NumberFormatException.class, () -> TimeUtils.formatOvernightTime(timeStr), "Should throw NumberFormatException for invalid format");
    }

    /**
     * Test missing time parts
     */
    @Test
    public void testFormatOvernightTimeWhenMissingParts() throws Throwable {
        // arrange
        String timeStr = "14:";
        // assert
        // act
        // act
        assertThrows(ArrayIndexOutOfBoundsException.class, () -> TimeUtils.formatOvernightTime(timeStr), "Should throw ArrayIndexOutOfBoundsException for missing time parts");
    }

    /**
     * Test scenario: Input is null
     * Expected: Should return null
     */
    @Test
    public void testConvertDate2DayDotStringWithNullInput() throws Throwable {
        // arrange
        Date nullDate = null;
        // act
        String result = TimeUtils.convertDate2DayDotString(nullDate);
        // assert
        assertNull(result, "Result should be null when input date is null");
    }

    /**
     * Test scenario: Input is a valid date
     * Expected: Should return formatted date string in yyyy.MM.dd format
     */
    @Test
    public void testConvertDate2DayDotStringWithValidInput() throws Throwable {
        // arrange
        // 2021-06-01
        Date testDate = new Date(1622505600000L);
        // act
        String result = TimeUtils.convertDate2DayDotString(testDate);
        // assert
        assertEquals("2021.06.01", result, "Date should be formatted as yyyy.MM.dd");
    }

    /**
     * Test scenario: Input is a valid boundary date
     * Expected: Should return formatted date string in yyyy.MM.dd format
     */
    @Test
    public void testConvertDate2DayDotStringWithBoundaryDate() throws Throwable {
        // arrange
        // 1970-01-01
        Date testDate = new Date(0L);
        // act
        String result = TimeUtils.convertDate2DayDotString(testDate);
        // assert
        assertEquals("1970.01.01", result, "Should handle epoch date correctly");
    }

    /**
     * Test scenario: Input is current date
     * Expected: Should return formatted current date string in yyyy.MM.dd format
     */
    @Test
    public void testConvertDate2DayDotStringWithCurrentDate() throws Throwable {
        // arrange
        Date currentDate = new Date();
        // act
        String result = TimeUtils.convertDate2DayDotString(currentDate);
        // assert
        assertNotNull(result, "Result should not be null for current date");
        assertTrue(result.matches("\\d{4}\\.\\d{2}\\.\\d{2}"), "Result should match format yyyy.MM.dd");
    }

    /**
     * Test successful date conversion with valid date input
     */
    @Test
    public void testConvertDate2DayString_WithValidDate() throws Throwable {
        // arrange
        // 2022-01-01 00:00:00
        Date testDate = new Date(1640995200000L);
        // act
        String result = TimeUtils.convertDate2DayString(testDate);
        // assert
        assertEquals("2022-01-01", result);
    }

    /**
     * Test date conversion with null input
     */
    @Test
    public void testConvertDate2DayString_WithNullDate() throws Throwable {
        // arrange
        Date nullDate = null;
        // act
        String result = TimeUtils.convertDate2DayString(nullDate);
        // assert
        assertNull(result);
    }

    /**
     * Test date conversion with current date
     */
    @Test
    public void testConvertDate2DayString_WithCurrentDate() throws Throwable {
        // arrange
        Date currentDate = new Date();
        SimpleDateFormat expectedFormat = new SimpleDateFormat("yyyy-MM-dd");
        String expected = expectedFormat.format(currentDate);
        // act
        String result = TimeUtils.convertDate2DayString(currentDate);
        // assert
        assertEquals(expected, result);
    }

    /**
     * Test date conversion with invalid date that causes exception
     */
    @Test
    public void testConvertDate2DayString_WithInvalidDate() throws Throwable {
        // arrange
        Date invalidDate = new Date(Long.MIN_VALUE);
        // act
        String result = TimeUtils.convertDate2DayString(invalidDate);
        // assert
        // For this extreme date value, we just verify it returns some string
        // since the actual formatting might vary by JVM implementation
        assertNotNull(result);
    }

    /**
     * Test thread safety of date conversion
     */
    @Test
    public void testConvertDate2DayString_ThreadSafety() throws Throwable {
        // arrange
        // 2022-01-01 00:00:00
        Date testDate = new Date(1640995200000L);
        int threadCount = 10;
        Thread[] threads = new Thread[threadCount];
        String[] results = new String[threadCount];
        // act
        for (int i = 0; i < threadCount; i++) {
            final int index = i;
            threads[i] = new Thread(() -> {
                results[index] = TimeUtils.convertDate2DayString(testDate);
            });
            threads[i].start();
        }
        // Wait for all threads to complete
        for (Thread thread : threads) {
            try {
                thread.join();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                fail("Thread interrupted");
            }
        }
        // assert
        for (String result : results) {
            assertEquals("2022-01-01", result);
        }
    }

    /**
     * Test cleanup of ThreadLocal resources
     */
    @Test
    public void testConvertDate2DayString_ResourceCleanup() throws Throwable {
        // arrange
        Date testDate = new Date();
        // act
        String result = TimeUtils.convertDate2DayString(testDate);
        // assert
        assertNotNull(result);
        assertTrue(result.matches("\\d{4}-\\d{2}-\\d{2}"), "Result should match yyyy-MM-dd format");
    }

    /**
     * Test when the input date is exactly now
     */
    @Test
    public void testGetHourDateStringExactlyNow() throws Throwable {
        // arrange
        Date now = new Date();
        // act
        String result = TimeUtils.getHourDateString(now);
        // assert
        assertEquals("1分钟前", result);
    }

    /**
     * Test when the input date is in the future
     */
    @Test
    public void testGetHourDateStringFutureDate() throws Throwable {
        // arrange
        // 1 minute in the future
        Date futureDate = new Date(System.currentTimeMillis() + 60000);
        // act
        String result = TimeUtils.getHourDateString(futureDate);
        // assert
        assertEquals("1分钟前", result);
    }

    /**
     * Test when the input date is exactly 30 minutes ago
     */
    @Test
    public void testGetHourDateString30MinutesAgo() throws Throwable {
        // arrange
        Date thirtyMinutesAgo = new Date(System.currentTimeMillis() - 30 * 60000);
        // act
        String result = TimeUtils.getHourDateString(thirtyMinutesAgo);
        // assert
        assertEquals("30分钟前", result);
    }

    /**
     * Test when the input date is 59 minutes ago
     */
    @Test
    public void testGetHourDateString59MinutesAgo() throws Throwable {
        // arrange
        Date fiftyNineMinutesAgo = new Date(System.currentTimeMillis() - 59 * 60000);
        // act
        String result = TimeUtils.getHourDateString(fiftyNineMinutesAgo);
        // assert
        assertEquals("59分钟前", result);
    }

    /**
     * Test when the input date is exactly 1 hour ago
     */
    @Test
    public void testGetHourDateStringOneHourAgo() throws Throwable {
        // arrange
        Date oneHourAgo = new Date(System.currentTimeMillis() - 60 * 60000);
        // act
        String result = TimeUtils.getHourDateString(oneHourAgo);
        // assert
        assertEquals("1小时前", result);
    }

    /**
     * Test when the input date is 12 hours ago
     */
    @Test
    public void testGetHourDateString12HoursAgo() throws Throwable {
        // arrange
        Date twelveHoursAgo = new Date(System.currentTimeMillis() - 12 * 60 * 60000);
        // act
        String result = TimeUtils.getHourDateString(twelveHoursAgo);
        // assert
        assertEquals("12小时前", result);
    }

    /**
     * Test when the input date is 23 hours and 59 minutes ago
     */
    @Test
    public void testGetHourDateString23Hours59MinutesAgo() throws Throwable {
        // arrange
        Date almostOneDayAgo = new Date(System.currentTimeMillis() - (23 * 60 + 59) * 60000);
        // act
        String result = TimeUtils.getHourDateString(almostOneDayAgo);
        // assert
        assertEquals("23小时前", result);
    }

    /**
     * Test when the input date is exactly 24 hours ago (should return empty string)
     */
    @Test
    public void testGetHourDateString24HoursAgo() throws Throwable {
        // arrange
        Date oneDayAgo = new Date(System.currentTimeMillis() - 24 * 60 * 60000);
        // act
        String result = TimeUtils.getHourDateString(oneDayAgo);
        // assert
        assertEquals("", result);
    }

    /**
     * Test when the input date is more than 24 hours ago (should return empty string)
     */
    @Test
    public void testGetHourDateStringMoreThan24HoursAgo() throws Throwable {
        // arrange
        Date moreThanOneDayAgo = new Date(System.currentTimeMillis() - 25 * 60 * 60000);
        // act
        String result = TimeUtils.getHourDateString(moreThanOneDayAgo);
        // assert
        assertEquals("", result);
    }

    /**
     * Test when the input date is null (should throw NullPointerException)
     */
    @Test
    public void testGetHourDateStringWithNullDate() throws Throwable {
        // arrange
        Date nullDate = null;
        // act & assert
        assertThrows(NullPointerException.class, () -> {
            TimeUtils.getHourDateString(nullDate);
        });
    }

    /**
     * Test when the input date is exactly 1 minute ago
     */
    @Test
    public void testGetHourDateStringOneMinuteAgo() throws Throwable {
        // arrange
        Date oneMinuteAgo = new Date(System.currentTimeMillis() - 60000);
        // act
        String result = TimeUtils.getHourDateString(oneMinuteAgo);
        // assert
        assertEquals("1分钟前", result);
    }

    /**
     * Test when the input date is 24 hours or more ago (should return empty string)
     */
    @Test
    public void testGetHourDateString24HoursOrMoreAgo() throws Throwable {
        // arrange
        Date oneDayAgo = new Date(System.currentTimeMillis() - 24 * 60 * 60000);
        // act
        String result = TimeUtils.getHourDateString(oneDayAgo);
        // assert
        assertEquals("", result);
    }
}
