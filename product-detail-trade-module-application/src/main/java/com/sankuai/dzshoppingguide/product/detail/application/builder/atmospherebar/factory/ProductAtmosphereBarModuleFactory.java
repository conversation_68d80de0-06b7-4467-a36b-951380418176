package com.sankuai.dzshoppingguide.product.detail.application.builder.atmospherebar.factory;

import com.dianping.cat.Cat;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.BuilderTypeEnum;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.extend.BaseBuilderFactory;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.extend.BaseVariableBuilder;
import com.sankuai.dz.product.detail.RequestCustomParamEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.atmospherebar.builder.MagicalMemberAtmosphereBarModuleBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.atmospherebar.builder.ProductAtmosphereBarModuleBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.atmospherebar.builder.CountrySubsidiesAtmosphereBarModuleBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.FetcherDAGStarter;
import com.sankuai.dzshoppingguide.product.detail.spi.atmospherebar.vo.ProductAtmosphereModuleVO;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

import static com.sankuai.dzshoppingguide.product.detail.application.constants.Constants.COUNTRY_SUBSIDIES;
import static com.sankuai.dzshoppingguide.product.detail.application.constants.Constants.MAGIC_COUPON_ENHANCEMENT_FLOW_FLAG;

/**
 * <AUTHOR>
 * @date 2025/04/02
 */
@Builder(
        builderType = BuilderTypeEnum.BUILDER_FACTORY,
        moduleKey = ModuleKeyConstants.ATMOSPHERE_PRICE_SALE_BAR_MODULE,
        startFetcher = FetcherDAGStarter.class,
        dependentFetchers = {
                FetcherDAGStarter.class
        }
)
@Slf4j
public class ProductAtmosphereBarModuleFactory extends BaseBuilderFactory<ProductAtmosphereModuleVO> {

    @Override
    protected Class<? extends BaseVariableBuilder> selectVariableBuilder() {
        if (isCountrySubsidiesProduct()) {
            Cat.logMetricForCount("CountrySubsidiesProduct");
            return CountrySubsidiesAtmosphereBarModuleBuilder.class;
        } else if (showMagicCouponAtmosphereBar()) {
            return MagicalMemberAtmosphereBarModuleBuilder.class;
        }
        return ProductAtmosphereBarModuleBuilder.class;
    }

    private boolean isCountrySubsidiesProduct() {
        String flowFlag = request.getCustomParam(RequestCustomParamEnum.flowFlag);
        return Objects.equals(COUNTRY_SUBSIDIES, flowFlag);
    }

    private boolean showMagicCouponAtmosphereBar() {
        String flowFlag = request.getCustomParam(RequestCustomParamEnum.flowFlag);
        return Objects.equals(MAGIC_COUPON_ENHANCEMENT_FLOW_FLAG, flowFlag);
    }
}
