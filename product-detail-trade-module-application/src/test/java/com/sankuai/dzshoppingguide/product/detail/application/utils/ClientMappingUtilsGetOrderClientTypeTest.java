package com.sankuai.dzshoppingguide.product.detail.application.utils;

import static com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum.*;
import static org.junit.jupiter.api.Assertions.*;
import com.dianping.tuangu.dztg.usercenter.api.enums.ChannelEnum;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

class ClientMappingUtilsGetOrderClientTypeTest {

    /**
     * 测试MT_APP客户端类型返回APP渠道
     */
    @Test
    public void testGetOrderClientTypeWithMtApp() throws Throwable {
        // arrange
        ProductDetailPageRequest request = Mockito.mock(ProductDetailPageRequest.class);
        Mockito.when(request.getClientTypeEnum()).thenReturn(MT_APP);
        // act
        ChannelEnum result = ClientMappingUtils.getOrderClientType(request);
        // assert
        assertEquals(ChannelEnum.APP, result);
    }

    /**
     * 测试DP_APP客户端类型返回APP渠道
     */
    @Test
    public void testGetOrderClientTypeWithDpApp() throws Throwable {
        // arrange
        ProductDetailPageRequest request = Mockito.mock(ProductDetailPageRequest.class);
        Mockito.when(request.getClientTypeEnum()).thenReturn(DP_APP);
        // act
        ChannelEnum result = ClientMappingUtils.getOrderClientType(request);
        // assert
        assertEquals(ChannelEnum.APP, result);
    }

    /**
     * 测试DP_XCX客户端类型返回微信小程序渠道
     */
    @Test
    public void testGetOrderClientTypeWithDpXcx() throws Throwable {
        // arrange
        ProductDetailPageRequest request = Mockito.mock(ProductDetailPageRequest.class);
        Mockito.when(request.getClientTypeEnum()).thenReturn(DP_XCX);
        // act
        ChannelEnum result = ClientMappingUtils.getOrderClientType(request);
        // assert
        assertEquals(ChannelEnum.WX_MINI_PROGRAM, result);
    }

    /**
     * 测试MT_XCX客户端类型返回微信小程序渠道
     */
    @Test
    public void testGetOrderClientTypeWithMtXcx() throws Throwable {
        // arrange
        ProductDetailPageRequest request = Mockito.mock(ProductDetailPageRequest.class);
        Mockito.when(request.getClientTypeEnum()).thenReturn(MT_XCX);
        // act
        ChannelEnum result = ClientMappingUtils.getOrderClientType(request);
        // assert
        assertEquals(ChannelEnum.WX_MINI_PROGRAM, result);
    }

    /**
     * 测试DP_BAIDU_MAP_XCX客户端类型返回百度地图小程序渠道
     */
    @Test
    public void testGetOrderClientTypeWithDpBaiduMapXcx() throws Throwable {
        // arrange
        ProductDetailPageRequest request = Mockito.mock(ProductDetailPageRequest.class);
        Mockito.when(request.getClientTypeEnum()).thenReturn(DP_BAIDU_MAP_XCX);
        // act
        ChannelEnum result = ClientMappingUtils.getOrderClientType(request);
        // assert
        assertEquals(ChannelEnum.BAIDU_MAP_MINI_PROGRAM, result);
    }

    /**
     * 测试MT_LIVE_ORDER_XCX客户端类型返回私域直播小程序渠道
     */
    @Test
    public void testGetOrderClientTypeWithMtLiveOrderXcx() throws Throwable {
        // arrange
        ProductDetailPageRequest request = Mockito.mock(ProductDetailPageRequest.class);
        Mockito.when(request.getClientTypeEnum()).thenReturn(MT_LIVE_ORDER_XCX);
        // act
        ChannelEnum result = ClientMappingUtils.getOrderClientType(request);
        // assert
        assertEquals(ChannelEnum.SHANBO_MINI_PROGRAM, result);
    }

    /**
     * 测试MT_LIVE_XCX客户端类型返回私域直播小程序渠道
     */
    @Test
    public void testGetOrderClientTypeWithMtLiveXcx() throws Throwable {
        // arrange
        ProductDetailPageRequest request = Mockito.mock(ProductDetailPageRequest.class);
        Mockito.when(request.getClientTypeEnum()).thenReturn(MT_LIVE_XCX);
        // act
        ChannelEnum result = ClientMappingUtils.getOrderClientType(request);
        // assert
        assertEquals(ChannelEnum.SHANBO_MINI_PROGRAM, result);
    }

    /**
     * 测试未知客户端类型返回默认APP渠道
     */
    @Test
    public void testGetOrderClientTypeWithUnknownClientType() throws Throwable {
        // arrange
        ProductDetailPageRequest request = Mockito.mock(ProductDetailPageRequest.class);
        Mockito.when(request.getClientTypeEnum()).thenReturn(UNKNOWN);
        // act
        ChannelEnum result = ClientMappingUtils.getOrderClientType(request);
        // assert
        assertEquals(ChannelEnum.APP, result);
    }

    /**
     * 测试null客户端类型返回默认APP渠道
     */
    @Test
    public void testGetOrderClientTypeWithNullClientType() throws Throwable {
        // arrange
        ProductDetailPageRequest request = Mockito.mock(ProductDetailPageRequest.class);
        Mockito.when(request.getClientTypeEnum()).thenReturn(null);
        // act
        ChannelEnum result = ClientMappingUtils.getOrderClientType(request);
        // assert
        assertEquals(ChannelEnum.APP, result);
    }
}
