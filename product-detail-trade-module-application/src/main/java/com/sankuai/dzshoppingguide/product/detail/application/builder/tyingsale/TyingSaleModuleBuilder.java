package com.sankuai.dzshoppingguide.product.detail.application.builder.tyingsale;

import com.dianping.deal.sales.common.datatype.SalesDisplayDTO;
import com.dianping.lion.client.Lion;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.dealuser.price.display.api.enums.CouponAssignStatusEnum;
import com.sankuai.dealuser.price.display.api.enums.PromoTypeEnum;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.extend.BaseBuilder;
import com.sankuai.dz.product.detail.RequestCustomParamEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.abtest.AbTestFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.abtest.AbTestReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.FetcherDAGStarter;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.tying.CombinationDealInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.tying.TyingOrderUrlFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.tying.TyingPaddingResult;
import com.sankuai.dzshoppingguide.product.detail.application.utils.GsonUtils;
import com.sankuai.dzshoppingguide.product.detail.application.utils.PromoHelper;
import com.sankuai.dzshoppingguide.product.detail.domain.utils.LionConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.tyingsale.BuyMoreSaveMoreCardVO;
import com.sankuai.dzshoppingguide.product.detail.spi.tyingsale.BuyMoreSaveMoreVO;
import com.sankuai.dzshoppingguide.product.detail.spi.tyingsale.CouponDescItem;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.enums.DealGroupStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

@Builder(
        moduleKey = ModuleKeyConstants.MODULE_DETAIL_DEAL_TYING_SALE,
        startFetcher = FetcherDAGStarter.class,
        dependentFetchers = {
                TyingOrderUrlFetcher.class,
                AbTestFetcher.class,
        }
)
@Slf4j
public class TyingSaleModuleBuilder extends BaseBuilder<BuyMoreSaveMoreVO> {

    private TyingPaddingResult tyingPaddingResult;
    private AbTestReturnValue abTestReturnValue;

    private static final List<String> EXP_PASS_RESULT = Lists.newArrayList("c", "e");
    private static final String MT_DETAIL_URL_TEMPLATE = "imeituan://www.meituan.com/gc/deal/detail?did=%d&poiid=%s";
    private static final String DP_DETAIL_URL_TEMPLATE = "dianping://tuandeal?shopid=%s&id=%d";
    public static final String QI_TA_SERVICE_TYPE = "其他";
    public static final String TAO_CAN_TEXT = "套餐";

    @Override
    public BuyMoreSaveMoreVO doBuild() {
        tyingPaddingResult = getDependencyResult(TyingOrderUrlFetcher.class);
        abTestReturnValue = getDependencyResult(AbTestFetcher.class);
        if (abTestReturnValue == null) {
            return new BuyMoreSaveMoreVO();
        }
        return buildBuyMoreSaveMoreVO();
    }

    private BuyMoreSaveMoreVO buildBuyMoreSaveMoreVO() {
        BuyMoreSaveMoreVO buyMoreSaveMoreVO = new BuyMoreSaveMoreVO();
        // 客户端校验
        if (!request.getClientTypeEnum().isInApp()) {
            return buyMoreSaveMoreVO;
        }

        // 实验逻辑
        String expModuleName = request.getClientTypeEnum().isMtClientType() ? "jointBuyUnify_MT" : "jointBuyUnify_DP";
        String result = abTestReturnValue.getAbTestExpResult(expModuleName);
        buyMoreSaveMoreVO.setExpBiInfo(abTestReturnValue.getAbTestExpBiInfo(expModuleName));
        if (StringUtils.isBlank(result) || !EXP_PASS_RESULT.contains(result)) {
            return buyMoreSaveMoreVO;
        }

        // 数据判空
        if (tyingPaddingResult == null || CollectionUtils.isEmpty(tyingPaddingResult.getCombinationDealInfoList())) {
            return buyMoreSaveMoreVO;
        }
        List<BuyMoreSaveMoreCardVO> cardVOS = Lists.newArrayList();
        Map<String, String> textMap = Lion.getMap(LionConstants.DZTGDETAIL_APPKEY,
                LionConstants.BUY_MORE_SAVE_MORE_TEXT_MAP, String.class, Collections.emptyMap());
        tyingPaddingResult.getCombinationDealInfoList().forEach(combinationDealInfo -> {
            BuyMoreSaveMoreCardVO cardVO = buildCardVO(combinationDealInfo, tyingPaddingResult.getDealIdPriceDisplayMap(), textMap, tyingPaddingResult.getProductId2SubTitleMap());
            if (Objects.nonNull(cardVO)) {
                cardVOS.add(cardVO);
            }
        });
        buyMoreSaveMoreVO.setCardList(cardVOS);

        if (getSourceType() == 1) { // 团详页
            buyMoreSaveMoreVO.setDealModuleTitle(textMap.get("dealModuleTitle"));
            int size = buyMoreSaveMoreVO.getCardList().size();
            int showItemSize = Lion.getInt(LionConstants.DZTGDETAIL_APPKEY, LionConstants.BUY_MORE_SAVE_MORE_SHOW_ITEM_SIZE);
            buyMoreSaveMoreVO.setCardList(size > showItemSize ? buyMoreSaveMoreVO.getCardList().subList(0, showItemSize) : buyMoreSaveMoreVO.getCardList());
            if (size > showItemSize) {
                buyMoreSaveMoreVO.setFloatLayerEntranceTitle(textMap.get("floatLayerEntranceTitle"));
            }
        } else {    // 浮层页
            buyMoreSaveMoreVO.setFloatLayerTitle(textMap.get("floatLayerTitle"));
            // 产品要求不做分页，只展示第一页
            buyMoreSaveMoreVO.setEnd(true);
        }
        return buyMoreSaveMoreVO;
    }


    private BuyMoreSaveMoreCardVO buildCardVO(CombinationDealInfo combinationDealInfo,
                                              Map<Integer, PriceDisplayDTO> dealIdPriceDisplayMap,
                                              Map<String, String> textMap, Map<Integer, List<String>> productId2SubTitleMap) {
        BuyMoreSaveMoreCardVO cardVO = new BuyMoreSaveMoreCardVO();

        Map<Integer, DealGroupDTO> dealGroupDTOMap = tyingPaddingResult.getDealIdDealGroupDTOMap();
        Map<Integer, SalesDisplayDTO> productId2SaleMap = tyingPaddingResult.getProductId2SaleMap();
        Map<Integer, String> dealOrderPageUrlMap = tyingPaddingResult.getDealOrderPageUrlMap();
        Map<Integer, CombinationDealInfo> bindingDealCombinationInfoMap = tyingPaddingResult.getBindingDealCombinationInfoMap();

        Integer mainDealId = combinationDealInfo.getMainDealId();
        Integer bindingDealId = combinationDealInfo.getBindingDealId();
        if (!dealGroupDTOMap.containsKey(mainDealId) || !dealGroupDTOMap.containsKey(bindingDealId) || !bindingDealCombinationInfoMap.containsKey(bindingDealId)) {
            return null;
        }
        DealGroupDTO bindDealDTO = dealGroupDTOMap.get(bindingDealId);
        // 过滤掉不在线和私域分发搭售品
        if (bindDealDTO == null || bindDealDTO.getBasic() == null || bindDealDTO.getBasic().getStatus() != DealGroupStatusEnum.VISIBLE_ONLINE.getCode()) {
            return null;
        }
        cardVO.setCardTitle(getCardTitle(dealGroupDTOMap, mainDealId, bindingDealId));
        cardVO.setCombinationId(combinationDealInfo.getItemId());
        cardVO.setMainDealId(combinationDealInfo.getMainDealId());
        cardVO.setBindingDealId(combinationDealInfo.getBindingDealId());
        // 搭售来源
        cardVO.setBindingSource(combinationDealInfo.getBindingSource());
        // 品类型
        cardVO.setMainProductType(1);
        cardVO.setBindingProductType(1);
        // 头图
        cardVO.setMainDealHeaderImage(dealGroupDTOMap.get(mainDealId).getImage().getDefaultPicPath());
        cardVO.setBindingDealHeaderImage(dealGroupDTOMap.get(bindingDealId).getImage().getDefaultPicPath());
        // 标题
        cardVO.setBindingDealTitle(dealGroupDTOMap.get(bindingDealId).getBasic().getTitle());
        // 搭售商品副标题
        List<String> productTags = productId2SubTitleMap.getOrDefault(bindingDealId, Lists.newArrayList());
        String subTitle = Joiner.on("·").join(productTags);
        cardVO.setBindingDealSubTitle(subTitle);
        // 到手价
        Map<Integer, BigDecimal> dealIdPriceMap = Maps.newHashMap();
        // 获取所有团单及对应价格信息
        dealIdPriceDisplayMap.forEach((key, value) -> {
            dealIdPriceMap.put(key, value.getPrice());
        });
        if (dealIdPriceMap.containsKey(mainDealId) && dealIdPriceMap.containsKey(bindingDealId)) {
            cardVO.setMainDealPrice(textMap.get("rmb") + dealIdPriceMap.get(mainDealId).stripTrailingZeros().toPlainString());
            cardVO.setBindingDealPrice(textMap.get("rmb") + dealIdPriceMap.get(bindingDealId).stripTrailingZeros().toPlainString());
        }
        // 当前商品标签tag
        cardVO.setMainDealTag(textMap.get("mainDealTag"));
        // 组合价&优惠价
        if (Objects.isNull(bindingDealCombinationInfoMap.get(bindingDealId).getExtPrice())) {
            return null;
        }
        cardVO.setCardPriceText(textMap.get("cardPriceText"));
        cardVO.setCardPrice(bindingDealCombinationInfoMap.get(bindingDealId).getExtPrice().stripTrailingZeros().toPlainString());
        BigDecimal extPricePromoAmount = bindingDealCombinationInfoMap.get(bindingDealId).getExtPricePromoAmount();
        cardVO.setDiscountPriceDesc(Objects.isNull(extPricePromoAmount) || extPricePromoAmount.compareTo(BigDecimal.valueOf(0)) <= 0 ? "" : textMap.get("discountPriceDesc") + bindingDealCombinationInfoMap.get(bindingDealId).getExtPricePromoAmount().stripTrailingZeros().toPlainString());
        // 销量
        SalesDisplayDTO salesDisplayDTO = productId2SaleMap.get(bindingDealId);
        if (salesDisplayDTO != null) {
            cardVO.setSales(salesDisplayDTO.getSalesTag());
        }
        // 团详链接
        if (request.getClientTypeEnum().isMtClientType()) {
            cardVO.setBindingDealJumpUrl(String.format(MT_DETAIL_URL_TEMPLATE, bindingDealId, request.getPoiId()));
        } else {
            cardVO.setBindingDealJumpUrl(String.format(DP_DETAIL_URL_TEMPLATE, request.getPoiId(), bindingDealId));
        }
        cardVO.setBuyButtonText(textMap.get("buyButtonText"));
        // 如果没有对应跳链信息，则直接返回
        if (!dealOrderPageUrlMap.containsKey(bindingDealId)) {
            return null;
        }
        cardVO.setBuyButtonJumpUrl(dealOrderPageUrlMap.get(bindingDealId));

        //主品优惠信息
        PriceDisplayDTO mainPriceDisplayDTOS = dealIdPriceDisplayMap.get(combinationDealInfo.getMainDealId());
        //搭售品优惠信息
        PriceDisplayDTO bundPriceDisplayDTOS = dealIdPriceDisplayMap.get(combinationDealInfo.getBindingDealId());
        ArrayList<PriceDisplayDTO> priceDisplayDTOS = Lists.newArrayList(mainPriceDisplayDTOS,bundPriceDisplayDTOS);
        List<CouponDescItem> couponDescItems = priceDisplayDTOS.stream().map(dto -> {
            List<PromoDTO> couponPromos = dto.getUsedPromos();
            // 政府消费券
            List<PromoDTO> governmentCouponPromos = dto.getUsedPromos().stream()
                    .filter(promoDTO -> promoDTO != null && promoDTO.getIdentity() != null && promoDTO.getIdentity().getPromoType() == PromoTypeEnum.GOVERNMENT_CONSUME_COUPON.getType())
                    .collect(Collectors.toList());

            governmentCouponPromos.stream().findFirst().ifPresent(item -> {
                Map<String, Object> map = getTradeNeedMap(item);
                if (MapUtils.isNotEmpty(map) && !cardVO.getBuyButtonJumpUrl().contains("otherpromptinfo")) {
                    try {
                        String appendUrlArg = "&otherpromptinfo=" + URLEncoder.encode(GsonUtils.toJsonString(map), StandardCharsets.UTF_8.name());
                        cardVO.setBuyButtonJumpUrl(cardVO.getBuyButtonJumpUrl() + appendUrlArg);
                    } catch (UnsupportedEncodingException e) {
                        log.error("encode has error", e);
                    }
                }
            });

            if(org.apache.commons.collections4.CollectionUtils.isEmpty(couponPromos)) {
                return null;
            }

            return buildCouponItem(couponPromos, dto.getIdentity().getProductId());
        }).filter(Objects::nonNull).flatMap(List::stream).collect(Collectors.toList());
        cardVO.setCouponDescItems(couponDescItems);
        return cardVO;
    }

    private List<CouponDescItem> buildCouponItem(List<PromoDTO> couponPromos, int productId) {
        List<CouponDescItem> coupon = Lists.newArrayList();
        for (PromoDTO couponPromo : couponPromos){
            if(couponCanAssign(couponPromo) || govermentConsumeCouponCanAssign(couponPromo)) {
                CouponDescItem item = new CouponDescItem();
                item.setCouponGroupId((int) couponPromo.getIdentity().getPromoId());
                item.setUnifiedcoupongroupids(String.valueOf(couponPromo.getIdentity().getPromoId()));
                item.setDealGroupId(String.valueOf(productId));
                coupon.add(item);
            }
        }
        return coupon;
    }

    private boolean couponCanAssign(PromoDTO couponPromo) {
        return couponPromo.getIdentity().getPromoType() == PromoTypeEnum.COUPON.getType() && PromoHelper.canAssign(couponPromo);
    }

    private boolean govermentConsumeCouponCanAssign(PromoDTO couponPromo) {
        return couponPromo.getIdentity().getPromoType() == PromoTypeEnum.GOVERNMENT_CONSUME_COUPON.getType() && couponPromo.getCouponAssignStatus() != CouponAssignStatusEnum.ASSIGNED.getCode();
    }

    private Map<String, Object> getTradeNeedMap(PromoDTO item) {
        Map<String, Object> map = new HashMap<>();
        map.put("receiptBatchId", item.getCouponGroupId());
        if (item.getCouponAssignStatus().equals(CouponAssignStatusEnum.ASSIGNED.getCode())) {
            map.put("receiptCode", item.getIdentity().getPromoId());
        }
        map.put("sceneType", 1);
        return map;
    }

    private String getCardTitle(Map<Integer, DealGroupDTO> dealGroupDTOMap, Integer mainDealId, Integer bindingDealId) {
        if (!dealGroupDTOMap.containsKey(mainDealId)
                || !dealGroupDTOMap.containsKey(bindingDealId)
                || StringUtils.isBlank(dealGroupDTOMap.get(mainDealId).getCategory().getServiceType())
                || StringUtils.isBlank(dealGroupDTOMap.get(bindingDealId).getCategory().getServiceType())) {
            return StringUtils.EMPTY;
        }

        String cardTitle = StringUtils.EMPTY;
        String mainDealServiceType = dealGroupDTOMap.get(mainDealId).getCategory().getServiceType();
        String bindingDealServiceType = dealGroupDTOMap.get(bindingDealId).getCategory().getServiceType();
        if (mainDealServiceType.equals(bindingDealServiceType)) {
            if (QI_TA_SERVICE_TYPE.equals(mainDealServiceType)) {
                // 两个都是"其他"
                return cardTitle;
            } else {
                // 主品和搭售品三级类目相同
                cardTitle = mainDealServiceType + TAO_CAN_TEXT;
            }
        } else if (QI_TA_SERVICE_TYPE.equals(mainDealServiceType) || QI_TA_SERVICE_TYPE.equals(bindingDealServiceType)) {
            // 主品和搭售品有一个是其他
            cardTitle = (mainDealServiceType + bindingDealServiceType).replace(QI_TA_SERVICE_TYPE, StringUtils.EMPTY) + TAO_CAN_TEXT;
        } else {
            cardTitle = dealGroupDTOMap.get(mainDealId).getCategory().getServiceType()
                    + "+"
                    + dealGroupDTOMap.get(bindingDealId).getCategory().getServiceType();
        }

        return cardTitle.replace("套餐套餐", TAO_CAN_TEXT);
    }

    private int getSourceType() {
        String sourceType = request.getCustomParam(RequestCustomParamEnum.tyingSaleSourceType);
        return NumberUtils.toInt(sourceType, 1);
    }

}
