package com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.response.FetcherResponse;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.exception.QueryCenterException;
import com.sankuai.dzshoppingguide.product.detail.domain.query.center.QueryCenterAclService;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.enums.ResponseCodeEnum;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResponse;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResult;
import java.util.Collections;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.concurrent.ExecutionException;
import org.apache.thrift.TException;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.*;

public class QueryCenterAggregateFetcherTest {

    private final QueryCenterAggregateFetcher fetcher = new QueryCenterAggregateFetcher();

    private final QueryCenterAclService mockQueryCenterAclService = Mockito.mock(QueryCenterAclService.class);

    private final QueryByDealGroupIdRequestBuilder requestBuilder = new QueryByDealGroupIdRequestBuilder();

    public QueryCenterAggregateFetcherTest() {
        fetcher.queryCenterAclService = mockQueryCenterAclService;
    }

    /**
     * Test scenario where the service throws a TException.
     */
    @Test
    public void testInitFuture_TException() throws Throwable {
        // arrange
        TException expectedException = new TException("Service error");
        when(mockQueryCenterAclService.query(any(QueryByDealGroupIdRequest.class))).thenThrow(expectedException);
        // act
        CompletableFuture<FetcherResponse<QueryCenterAggregateReturnValue>> future = fetcher.initFuture(requestBuilder);
        FetcherResponse<QueryCenterAggregateReturnValue> fetcherResponse = future.get();
        // assert
        assertFalse(fetcherResponse.isSuccess());
        assertInstanceOf(QueryCenterException.class, fetcherResponse.getError());
        assertEquals(expectedException, fetcherResponse.getError().getCause());
    }
}
