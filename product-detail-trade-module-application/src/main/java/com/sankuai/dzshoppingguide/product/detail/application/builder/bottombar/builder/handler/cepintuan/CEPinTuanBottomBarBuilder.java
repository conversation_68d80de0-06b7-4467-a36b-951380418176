package com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.builder.handler.cepintuan;

import com.google.common.collect.Lists;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.BuilderTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.builder.BaseBottomBarBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.component.trade.block.buy.button.DefaultTradeButtonComponent;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.component.trade.block.buy.button.cepintuan.CEPTNormalBuyButtonComponent;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.component.trade.block.buy.button.cepintuan.CEPTPinTuanBuyButtonComponent;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.component.trade.block.cepintuan.CEPTMasterStrongButtonComponent;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.component.trade.block.cepintuan.CEPTMasterWeakButtonComponent;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.component.trade.block.cepintuan.CEPTStatusInfoComponent;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.exception.BottomBarFatalException;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.cepinpool.CostEffectivePinPoolFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.cepinpool.dto.CostEffectivePinTuan;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.OrderPageUrlResult;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.handler.cepintuan.CEPinTuanOrderPageUrlFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.handler.normal.NormalOrderPageUrlFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.ProductCostEffectivePriceFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.ProductNormalPriceFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.dto.ProductPriceReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.purchase.coupon.PurchaseCouponFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.purchase.coupon.PurchaseCouponReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sale.info.ProductSaleInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sale.info.ProductSaleInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sale.info.enums.ProductSaleStatusEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sale.info.enums.ProductSaleTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.FetcherDAGStarter;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.ProductBuyBarModule;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.extend.none.DoNothingActionVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.banner.BottomBarTopBannerVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.banner.enums.BottomBarBannerTypeEnums;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.block.cepintuan.CEPinTuanInfoBlockVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.block.cepintuan.CEPinTuanTradeBlockVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.block.trade.StandardTradeBlockVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.component.cepintuan.CEPinTuanInfoVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.component.trade.button.StandardTradeButtonVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.scene.CEPinTuanBottomBarVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.scene.StandardTradeBottomBarVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.backgroud.BottomBarBackgroundVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.rich.content.PicRichContentVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.rich.content.TextRichContentVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.rich.content.enums.TextStyleEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * @Author: guangyujie
 * @Date: 2025/3/12 23:58
 */
@Builder(
        builderType = BuilderTypeEnum.VARIABLE_BUILDER,
        moduleKey = ModuleKeyConstants.BOTTOM_BAR,
        startFetcher = FetcherDAGStarter.class,
        dependentFetchers = {
                ProductBaseInfoFetcher.class,
                ProductCostEffectivePriceFetcher.class,
                CostEffectivePinPoolFetcher.class,
                ProductNormalPriceFetcher.class,
                CEPinTuanOrderPageUrlFetcher.class,
                NormalOrderPageUrlFetcher.class,
                PurchaseCouponFetcher.class,
                ProductSaleInfoFetcher.class
        }
)
@Slf4j
public class CEPinTuanBottomBarBuilder extends BaseBottomBarBuilder {

    private CostEffectivePinTuan costEffectivePinTuan;//特团拼团信息
    private PriceDisplayDTO costEffectivePrice;//特团拼团价格
    private PriceDisplayDTO normalPrice;//普通价格
    private String normalOrderPageUrl;
    private String CEPinTuanOrderPageUrl;
    private PurchaseCouponReturnValue purchaseCouponReturnValue;
    private ProductBaseInfo productBaseInfo;
    private ProductSaleStatusEnum saleStatus;//特团拼团和普通购买的销售状态一致

    @Override
    public ProductBuyBarModule buildBuyBarModule() {
        saleStatus = getDependencyResult(ProductSaleInfoFetcher.class, ProductSaleInfo.class)
                .map(saleInfo -> saleInfo.getSaleStatus(ProductSaleTypeEnum.COMMON_DEAL))
                .orElse(ProductSaleStatusEnum.ONLINE);
        productBaseInfo = getDependencyResult(ProductBaseInfoFetcher.class);
        costEffectivePinTuan = getDependencyResult(CostEffectivePinPoolFetcher.class);
        if (costEffectivePinTuan == null || !costEffectivePinTuan.isCePinTuanScene()) {
            throw new BottomBarFatalException("获取不到特团拼团信息 or 不是特团拼团场景，但是走到了CEPinTuanBottomBarBuilder中!!!");
        }
        costEffectivePrice = getDependencyResult(ProductCostEffectivePriceFetcher.class, ProductPriceReturnValue.class)
                .map(ProductPriceReturnValue::getPriceDisplayDTO).orElse(null);
        normalPrice = getDependencyResult(ProductNormalPriceFetcher.class, ProductPriceReturnValue.class)
                .map(ProductPriceReturnValue::getPriceDisplayDTO).orElse(null);
        normalOrderPageUrl = getDependencyResult(NormalOrderPageUrlFetcher.class, OrderPageUrlResult.class)
                .map(OrderPageUrlResult::getUrl).orElse(null);
        CEPinTuanOrderPageUrl = getDependencyResult(CEPinTuanOrderPageUrlFetcher.class, OrderPageUrlResult.class)
                .map(OrderPageUrlResult::getUrl).orElse(null);
        purchaseCouponReturnValue = getDependencyResult(PurchaseCouponFetcher.class);
        CEPinTuanBottomBarVO productBottomBarVO = buildProductBottomBarVO();
        BottomBarTopBannerVO bannerVO = buildBanner();
        return ProductBuyBarModule.buildWithBanner(productBottomBarVO, bannerVO);
    }

    @Override
    protected ProductBuyBarModule buildDefaultBuyBarModule() {
        StandardTradeBottomBarVO bottomBarVO = new StandardTradeBottomBarVO();
        bottomBarVO.setRightBottomBar(StandardTradeBlockVO.buildSingleButtonStyle(
                DefaultTradeButtonComponent.build(request, normalOrderPageUrl)
        ));
        return ProductBuyBarModule.buildWithoutBanner(bottomBarVO);
    }

    /**
     * <a href="https://ingee.meituan.com/#/detail/151332">视觉稿</a>
     */
    private CEPinTuanBottomBarVO buildProductBottomBarVO() {
        if (!costEffectivePinTuan.isPinTuanOpened()
                || costEffectivePinTuan.getNeedMemberCount() <= 0) { // 不在拼团中 = 未开团 || （已开团&&团满）
            return buildNotInPinTuanBottomBar();
        } else {
            if (costEffectivePinTuan.isActivePinTuan()) { // 主态
                return buildMasterStatePinTuan();
            } else { // 客态
                if (costEffectivePinTuan.inPinTuan()) { // 已参团
                    return buildCustomStateInPinTuan();
                } else {    // 未参团
                    return buildCustomStateNotInPinTuan();
                }
            }
        }
    }

    /**
     * 未开团
     */
    private CEPinTuanBottomBarVO buildNotInPinTuanBottomBar() {
        StandardTradeButtonVO normalButton = CEPTNormalBuyButtonComponent.build(
                request, normalPrice, normalOrderPageUrl, purchaseCouponReturnValue, costEffectivePinTuan.enhancedStyle(), saleStatus
        );
        StandardTradeButtonVO pinButton = CEPTPinTuanBuyButtonComponent.build(
                request, costEffectivePrice, CEPinTuanOrderPageUrl, costEffectivePinTuan, saleStatus
        );
        List<StandardTradeButtonVO> buttonList;
        if (costEffectivePinTuan.enhancedStyle()) {
            buttonList = Lists.newArrayList(normalButton, pinButton);
        } else {
            buttonList = Lists.newArrayList(pinButton, normalButton);
        }
        CEPinTuanBottomBarVO cePinTuanBottomBarVO = new CEPinTuanBottomBarVO();
        cePinTuanBottomBarVO.setRightBottomBar(new CEPinTuanTradeBlockVO(buttonList));
        return cePinTuanBottomBarVO;
    }

    /**
     * 主态已开团
     * 主态强化样式 和 客态已参团样式一致
     */
    private CEPinTuanBottomBarVO buildMasterStatePinTuan() {
        if (costEffectivePinTuan.enhancedStyle()) {
            return buildCustomStateInPinTuan();
        } else {
            //主态弱化样式：左侧拼团中按钮（点击后跳转拼团详情页，点评直接跳微信分享但是目前点评侧无特团拼团），右侧直接购买（强化样式）
            StandardTradeButtonVO masterWeakButton = CEPTMasterWeakButtonComponent.build(
                    request, costEffectivePrice, costEffectivePinTuan
            );
            StandardTradeButtonVO normalButton = CEPTNormalBuyButtonComponent.build(
                    request, normalPrice, normalOrderPageUrl, purchaseCouponReturnValue, false, saleStatus
            );
            CEPinTuanBottomBarVO cePinTuanBottomBarVO = new CEPinTuanBottomBarVO();
            cePinTuanBottomBarVO.setRightBottomBar(new CEPinTuanTradeBlockVO(Lists.newArrayList(
                    masterWeakButton, normalButton
            )));
            return cePinTuanBottomBarVO;
        }
    }

    /**
     * 客态已参团
     * 主态强化样式 和 客态已参团样式一致
     */
    private CEPinTuanBottomBarVO buildCustomStateInPinTuan() {
        StandardTradeButtonVO pinButton = CEPTMasterStrongButtonComponent.build(
                request, costEffectivePinTuan, productBaseInfo
        );
        CEPinTuanInfoVO cePinTuanInfo = CEPTStatusInfoComponent.build(
                request, costEffectivePinTuan
        );
        return new CEPinTuanBottomBarVO(
                new CEPinTuanInfoBlockVO(cePinTuanInfo),
                new CEPinTuanTradeBlockVO(Lists.newArrayList(pinButton))
        );
    }

    /**
     * 客态未参团，只展示拼团购买按钮，不展示普通购买按钮
     */
    private CEPinTuanBottomBarVO buildCustomStateNotInPinTuan() {
        StandardTradeButtonVO pinButton = CEPTPinTuanBuyButtonComponent.build(
                request, costEffectivePrice, CEPinTuanOrderPageUrl, costEffectivePinTuan, saleStatus
        );
        CEPinTuanInfoVO cePinTuanInfo = CEPTStatusInfoComponent.build(
                request, costEffectivePinTuan
        );
        return new CEPinTuanBottomBarVO(
                new CEPinTuanInfoBlockVO(cePinTuanInfo),
                new CEPinTuanTradeBlockVO(Lists.newArrayList(pinButton))
        );
    }

    private BottomBarTopBannerVO buildBanner() {
        if (!costEffectivePinTuan.enhancedStyle()) {
            return null;
        }
        BottomBarTopBannerVO bannerVO = new BottomBarTopBannerVO();
        bannerVO.setBannerType(BottomBarBannerTypeEnums.COMMON_TYPE.getCode());
        bannerVO.setActionData(new DoNothingActionVO());
        bannerVO.setBackground(BottomBarBackgroundVO.buildSingleColor("#FFF3F0"));
        String newUserText = costEffectivePinTuan.isLimitNewCustomJoin() ? "新用户" : "好友";
        String bannerText = "邀请" + newUserText + "，一起拼团购买，享超低价！";
        bannerVO.add(new PicRichContentVO(24, 24, "https://p0.meituan.net/ingee/fbcd0deada26f83680d6470010a3bc081069.png"))
                .add(new TextRichContentVO(bannerText, TextStyleEnum.Default, 24, "#FF2727"));
        return bannerVO;
    }

}
