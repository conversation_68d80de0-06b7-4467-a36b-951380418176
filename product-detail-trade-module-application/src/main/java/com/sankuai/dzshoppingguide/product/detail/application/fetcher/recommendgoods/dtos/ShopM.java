package com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommendgoods.dtos;

import com.google.common.collect.Lists;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Created by float.lu on 2020/9/15.
 */
@Data
public class ShopM {
    /**
     * 门店ID
     */
    private int shopId;

    private long longShopId;


    public int getShopId() {
        return shopId > 0 ? shopId : (int) longShopId;
    }

    public void setLongShopId(long longShopId) {
        this.longShopId = longShopId;
        if (longShopId > 0 && shopId == 0) {
            this.shopId = (int) longShopId;
        }
    }

    /**
     * 门店名
     */
    private String shopName;

    /**
     * 分店名称
     */
    private String branchName;

    /**
     * 门店距离
     */
    private String distance;

    /**
     * 门店距离数值
     */
    private double distanceNum;

    /**
     * 商户头图
     */
    private String pic;

    /**
     * 商户详情跳转链接
     */
    private String detailUrl;

    /**
     * 商户标签列表
     */
    private List<String> shopTags;

    /**
     * 商户标签列表
     */
    private List<ShopLabelM> labels;

    /**
     * 门店以及类目
     */
    private int shopType;
    /**
     * 商户分类
     */
    private int category;

    /**
     * 主类目名
     */
    private String categoryName;

    /**
     * 纬度
     */
    private double lat;
    /**
     * 精度
     */
    private double lng;

    /**
     * 城市id
     */
    private int cityId;

    /**
     * 城市名
     */
    private String cityName;

    /**
     * 销量
     */
    private ProductSaleM sale;

    /**
     * 电话,已废弃,建议使用下面的电话列表
     */
    @Deprecated
    private String phoneNo;

    /**
     * 电话列表
     */
    private List<String> phoneNoList;

    /**
     * shopUuid
     */
    private String shopUuid;

    /**
     * 地址
     */
    private String address;

    /**
     * 星级数据
     */
    private Short shopPower;

    /**
     * 商户的星级，如：4.55
     */
    private String starStr;

    /**
     * 商户得分
     */
    private double score;
    /**
     * 商户评分文案
     */
    private String scoreTag;

    /**
     * 主商圈名称
     */
    private String mainRegionName;

    /**
     * 营业时间解析使用pdc提供的工具解析
     * https://km.sankuai.com/page/618457695
     *
     * @see com.dianping.poi.normalizer.BizHourGenesis
     */
    private String businessHours;

    /**
     * 商户状态（0:手机收录商户,1:已关,2:暂时歇业,4:尚未营业,5:正常,15:侵权商户)
     * https://km.sankuai.com/page/30032406
     */
    private int status;

    /**
     * 额外信息，透传主题的
     */
    private Map<String, Object> extInfos;

    /**
     * 评论数量
     */
    private String reviewCount;


    /**
     * 用户定位城市与商户城市是否相等
     */
    private boolean userEqShop;

    /**
     * 消费均价
     */
    private String avgPrice;

    /**
     * 扩展属性
     */
    private List<AttrM> extAttrs;


    /**
     * 商户地图地址
     */
    private String mapUrl;


    /**
     * 内部使用
     */
    private String innerRecommandTag;

    /**
     * 商户推荐语
     */
    private String recommendText;

    /**
     * 填充属性值
     *
     * @param attrName
     * @return
     */
    public void setAttr(String attrName, String attrValue) {
        if (CollectionUtils.isEmpty(this.getExtAttrs())) {
            this.setExtAttrs(Lists.newArrayList(new AttrM(attrName, attrValue)));
            return;
        }
        this.getExtAttrs().add(new AttrM(attrName, attrValue));
    }

    /**
     * 根据属性名获取属性值
     *
     * @param attrName
     * @return
     */
    public String getAttr(String attrName) {
        if (CollectionUtils.isEmpty(this.getExtAttrs())) {
            return null;
        }
        AttrM attrM = this.getExtAttrs().stream()
                .filter(Objects::nonNull)
                .filter(attr -> attr.getName().equals(attrName))
                .findFirst()
                .orElse(null);
        return attrM == null ? null : attrM.getValue();
    }
}
