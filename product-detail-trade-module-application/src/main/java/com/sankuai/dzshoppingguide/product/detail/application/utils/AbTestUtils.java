package com.sankuai.dzshoppingguide.product.detail.application.utils;

import java.util.Objects;
import java.util.List;

import com.google.common.collect.Lists;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import org.apache.commons.lang.StringUtils;

/**
 * <AUTHOR>
 * @date 2025-04-01
 * @desc ABTest工具类
 */
public class AbTestUtils {

    // moduleName: 包含多sku模块、顶部加项入口、底部加项入口
    public static boolean hideMultiSkuOrMakeUpEntrance(String moduleName, String expResult) {
       List<String> showMultiSkuAbTestResult = Lists.newArrayList("b", "d", "f");
       // 多sku模块，b、d、f时展示
       if (Objects.equals(moduleName, ModuleKeyConstants.MODULE_DETAIL_DEAL_MULTI_SKU_SELECT) && showMultiSkuAbTestResult.contains(expResult)) {
           return false;
        }
        List<String> showMakeUpEntranceAbTestResult = Lists.newArrayList("b", "c", "d", "e", "f", "g");
        // 顶部加项入口，b、c、d、e、f、g时展示
        if (Objects.equals(moduleName, ModuleKeyConstants.MODULE_DETAIL_DEAL_MARKUP_SELECT_BAR) && showMakeUpEntranceAbTestResult.contains(expResult)) {
            return false;
        }
        List<String> showBottomMakeUpEntranceAbTestResult = Lists.newArrayList("f", "g");
        // 底部加项入口，f、g时展示
        if (Objects.equals(moduleName, ModuleKeyConstants.MODULE_DETAIL_DEAL_MARKUP_DETAIL) && showBottomMakeUpEntranceAbTestResult.contains(expResult)) {
            return false;
        }
        return true;
    }

    /**
     * 控制团详价格标签收敛
     */
    public static boolean enableDealPriceLabelOptimization(String expResult) {
        List<String> experimentGroup = Lists.newArrayList("c");
        if (experimentGroup.contains(expResult)) {
            return false;
        }
        return true;
    }
}
