package com.sankuai.dzshoppingguide.product.detail.application.builder.promodetail.config;

import com.sankuai.dzshoppingguide.product.detail.application.builder.promodetail.enums.CouponTypeEnum;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-04-17
 * @desc 领券栏过滤和排序配置
 */
@Data
public class PromotionBarConfig {
    /**
     * 排除已享券类型
     */
    private List<CouponTypeEnum> excludeUsedCouponTypes;
    /**
     * 排序已享券
     */
    private List<CouponTypeEnum> sortUsedCouponTypes;
    /**
     * 排除更多券类型
     */
    private List<CouponTypeEnum> excludeMoreCouponTypes;
    /**
     * 排序更多券
     */
    private List<CouponTypeEnum> sortMoreCouponTypes;
}
