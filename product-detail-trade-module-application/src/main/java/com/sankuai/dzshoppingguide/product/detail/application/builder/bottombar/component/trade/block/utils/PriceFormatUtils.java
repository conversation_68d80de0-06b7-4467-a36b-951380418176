package com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.component.trade.block.utils;

import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import org.jetbrains.annotations.NotNull;

import java.math.RoundingMode;

/**
 * @Author: guang<PERSON>jie
 * @Date: 2025/3/17 16:32
 */
public class PriceFormatUtils {

    /**
     * 展示价格取一位小数，向上取整
     *
     * @param priceDisplayDTO 价格
     * @return 格式化的价格
     */
    public static String formatPrice(String title, PriceDisplayDTO priceDisplayDTO) {
        if (priceDisplayDTO == null || priceDisplayDTO.getPrice() == null) {
            return title;
        }
        return String.format(
                "%s ¥%s",
                title,
                priceDisplayDTO.getPrice()
                        .setScale(2, RoundingMode.CEILING)
                        .stripTrailingZeros()
                        .toPlainString()
        );
    }

    public static @NotNull String getPlainString(PriceDisplayDTO priceDisplayDTO) {
        if (priceDisplayDTO == null || priceDisplayDTO.getPrice() == null) {
            return "?";
        }
        return priceDisplayDTO.getPrice()
                .setScale(2, RoundingMode.CEILING)
                .stripTrailingZeros()
                .toPlainString();
    }

}
