package com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.ext.param;

import com.alibaba.fastjson.JSON;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.enums.OrderPageExtParamEnums;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.ext.param.request.ExtParamBuilderRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;

import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: guangyujie
 * @Date: 2025/3/12 17:15
 */
@Slf4j
public abstract class BaseMultiExtParamBuilder implements ExtParamBuilder {

    @Override
    public Map<String, String> buildExtParam(final ProductDetailPageRequest request,
                                             final ExtParamBuilderRequest builderRequest) {
        try {
            Map<OrderPageExtParamEnums, String> extParam = doMultiBuildExtParam(request, builderRequest);
            if (MapUtils.isEmpty(extParam)) {
                return null;
            }
            return extParam.entrySet().stream().collect(Collectors.toMap(
                    entry -> entry.getKey().name(),
                    Map.Entry::getValue,
                    (v1, v2) -> v1
            ));
        } catch (Throwable throwable) {
            log.error("{}.buildExtParam error!, request = {}, builderRequest={}",
                    this.getClass().getSimpleName(), JSON.toJSONString(request), JSON.toJSONString(builderRequest), throwable);
            return null;
        }
    }

    protected abstract Map<OrderPageExtParamEnums, String> doMultiBuildExtParam(final ProductDetailPageRequest request,
                                                                                final ExtParamBuilderRequest builderRequest) throws Exception;

}
