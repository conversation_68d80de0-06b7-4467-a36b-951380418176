package com.sankuai.dzshoppingguide.product.detail.application.builder.atmospherebar.builder;

import com.dianping.gmkt.scene.api.delivery.dto.res.DealGroupPromotionDTO;
import com.dianping.gmkt.scene.api.delivery.dto.res.LocationMaterialsDTO;
import com.dianping.gmkt.scene.api.delivery.dto.res.ResourceExposureResponseDTO;
import com.google.common.collect.Lists;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.BuilderTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.atmospherebar.builder.enums.PromoSceneEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.pricebar.builder.AbstractProductBarModuleBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.pricebar.builder.enums.CouponGuideTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.pricebar.component.InflateCouponTipsComponent;
import com.sankuai.dzshoppingguide.product.detail.application.builder.pricebar.config.IconConfig;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.abtest.AbTestFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.abtest.AbTestReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.membercard.card.integration.IntegrationMemberCard;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.membercard.card.integration.IntegrationMemberCardFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.OrderPageUrlResult;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.handler.normal.NormalOrderPageUrlFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.dto.ProductPriceReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.service.dto.ShopMemberDetailV;
import com.sankuai.dzshoppingguide.product.detail.application.utils.*;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.*;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.atmosphere.ProductAtmosphereFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.atmosphere.ProductAtmosphereReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategoryFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.ProductNormalPriceFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.ProductPromoPriceFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.attr.SkuAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.attr.SkuAttrFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.sale.ProductSaleFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.sale.ProductSaleReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.FetcherDAGStarter;
import com.sankuai.dzshoppingguide.product.detail.application.utils.LionConfigUtils;
import com.sankuai.dzshoppingguide.product.detail.application.utils.PriceHelper;
import com.sankuai.dzshoppingguide.product.detail.application.utils.PriceUtils;
import com.sankuai.dzshoppingguide.product.detail.application.utils.PromoHelper;
import com.sankuai.dzshoppingguide.product.detail.spi.atmospherebar.vo.ProductAtmosphereModuleVO;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.PromoTagEnum;
import com.sankuai.mpmctmember.query.common.enums.MemberChargeTypeEnum;
import com.sankuai.mpmctmember.query.thrift.dto.MemberInterestDetailDTO;
import com.sankuai.nibmkt.promotion.api.common.enums.PromotionPropertyEnum;
import com.sankuai.nibmkt.promotion.api.query.model.magicalmember.response.MMCTooltipTextDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2025/04/02
 * 神券感知增强-氛围条
 */
@Builder(
        builderType = BuilderTypeEnum.VARIABLE_BUILDER,
        moduleKey = ModuleKeyConstants.ATMOSPHERE_PRICE_SALE_BAR_MODULE,
        startFetcher = FetcherDAGStarter.class,
        dependentFetchers = {
                ProductAtmosphereFetcher.class,
                ProductNormalPriceFetcher.class,
                ProductSaleFetcher.class,
                ProductPromoPriceFetcher.class,
                ProductCategoryFetcher.class,
                ProductBaseInfoFetcher.class,
                NormalOrderPageUrlFetcher.class,
                AbTestFetcher.class,
                SkuAttrFetcher.class,
                IntegrationMemberCardFetcher.class
        }
)
@Slf4j
public class MagicalMemberAtmosphereBarModuleBuilder  extends AbstractProductBarModuleBuilder<ProductAtmosphereModuleVO> {
    private static final String PRICE_SYMBOL = "¥";
    private static final String MAGICAL_COUPON_ENHANCE_STYLE_VERSION_1 = "enhanceStyleV1";
    private static final String MAGICAL_COUPON_ENHANCE_STYLE_VERSION_2 = "enhanceStyleV2";

    private IntegrationMemberCard integrationMemberCard;

    private AbTestReturnValue abTestReturnValue;

    @Override
    public ProductAtmosphereModuleVO doBuild() {
        ProductPriceReturnValue normalPriceReturnValue = getDependencyResult(ProductNormalPriceFetcher.class);
        ProductPriceReturnValue promoPriceReturnValue = getDependencyResult(ProductPromoPriceFetcher.class);
        ProductSaleReturnValue productSaleReturnValue = getDependencyResult(ProductSaleFetcher.class);
        ProductAtmosphereReturnValue productAtmosphereReturnValue = getDependencyResult(ProductAtmosphereFetcher.class);
        abTestReturnValue = getDependencyResult(AbTestFetcher.class);
        ProductCategory productCategory = getDependencyResult(ProductCategoryFetcher.class);
        ProductBaseInfo productBaseInfo = getDependencyResult(ProductBaseInfoFetcher.class);
        OrderPageUrlResult orderPageUrlResult = getDependencyResult(NormalOrderPageUrlFetcher.class);
        SkuAttr skuAttr = getDependencyResult(SkuAttrFetcher.class);
        String orderPageUrl = Optional.ofNullable(orderPageUrlResult).map(OrderPageUrlResult::getUrl).orElse(null);
        ProductAtmosphereModuleVO result = new ProductAtmosphereModuleVO();
        integrationMemberCard = getDependencyResult(IntegrationMemberCardFetcher.class);
        try{
            ResourceExposureResponseDTO exposureResource = Optional.ofNullable(productAtmosphereReturnValue)
                    .map(ProductAtmosphereReturnValue::getResourceExposureResponse).orElse(null);

            // 价格
            ProductPriceParam priceParam = ProductPriceParam.builder()
                    .normalPriceReturnValue(normalPriceReturnValue)
                    .dealPromoPriceReturnValue(promoPriceReturnValue)
                    .productCategory(productCategory)
                    .productBaseInfo(productBaseInfo)
                    .isAtmosphere(isAtmosphere(exposureResource))
                    .skuAttr(skuAttr)
                    .build();

            DetailPriceVO priceVO = buildPriceVO(priceParam);
            if (Objects.isNull(priceVO)) {
                return null;
            }
            //最优价格算价公式
            PriceDisplayDTO dealPromDisplayDTO = Optional.ofNullable(promoPriceReturnValue).map(ProductPriceReturnValue::getPriceDisplayDTO).orElse(null);
            if (Objects.isNull(dealPromDisplayDTO)) {
                return null;
            }
            priceVO.setBestPromoFormula(buildBestPromoFormula(dealPromDisplayDTO, priceVO.getFinalPrice(), abTestReturnValue));

            MMCTooltipTextDTO mmcTooltipTextDTO = InflateCouponTipsComponent.getMMCTooltipTextDTO(dealPromDisplayDTO.getExtendDisplayInfo());
            List<PromoDTO> usedPromos = dealPromDisplayDTO.getUsedPromos();
            CouponGuideTypeEnum guideType = InflateCouponTipsComponent.getCouponGuideTypeEnum(usedPromos, new BigDecimal(priceVO.getFinalPrice()), mmcTooltipTextDTO);


            //神券提示条
            InflateCouponTips inflateCouponTips = InflateCouponTipsComponent.buildInflateCouponTips(dealPromDisplayDTO,
                    priceVO.getFinalPrice(), orderPageUrl, abTestReturnValue);
            priceVO.setInflateCouponTips(inflateCouponTips);
            result.setPrice(priceVO);

            // 销量
            DetailSaleVO saleVO = buildSaleInfo(productSaleReturnValue);
            result.setSale(saleVO);
            // 获取活动氛围信息
            DealGroupPromotionDTO promotionOpt = getPromotionDTO(exposureResource);
            IconConfig iconConfig = LionConfigUtils.getIconConfig();
            // 判断是否存在氛围条信息
            // 有活动氛围（pciV1 && picV2 有值）
            if(Objects.nonNull(exposureResource) && hasActivityAtmosphereInfo(promotionOpt)){
                // 1.1 如果有活动氛围
                AtmosphereInfoDetailVO atmosphereInfoDetailVO = buildHasActivityAtmosphereBackgroundImage(promoPriceReturnValue.getPriceDisplayDTO(), promotionOpt);
                result.setAtmosphere(buildHasActivityAtmosphereInfoOther(atmosphereInfoDetailVO, promotionOpt, exposureResource.getActivityId()));
            }else {
                // 1.2 如果无活动氛围
                AtmosphereInfoDetailVO atmosphereInfoDetailVO = buildNotActivityAtmosphereBackgroundImage(promoPriceReturnValue.getPriceDisplayDTO(), iconConfig, inflateCouponTips, saleVO, guideType);
                result.setAtmosphere(atmosphereInfoDetailVO);
            }
            // 团详神券感知强化样式下发
            buildInflateCouponTipsStyle(result, abTestReturnValue);
            // 价格营销点、折扣标签样式
            putPriceStyle(priceVO, guideType, getShowNewAtmosphereBar(result));
            // 如果没有氛围图，则不返回数据
            return Objects.isNull(result.getAtmosphere()) || StringUtils.isBlank(result.getAtmosphere().getBackgroundImage())
                    ? null :result;
        } catch (Exception e) {
            log.error("MagicalMemberAtmosphereBarModuleBuilder.doBuild() error:", e);
            return result;
        }
    }

    // 团详神券感知强化2期
    private void buildInflateCouponTipsStyle( ProductAtmosphereModuleVO result, AbTestReturnValue abTestReturnValue) {
        DetailPriceVO priceVO = result.getPrice();
        boolean showNewAtmosphereBar = getShowNewAtmosphereBar(result);
        // 团详神券感知强化2期实验只涉及 美团
        String expResult = abTestReturnValue.getAbTestExpResult("MtMagicalCouponStyleEnhancementV2");
        // 神券增强版本1
        if (StringUtils.equals("c", expResult) && showNewAtmosphereBar) {
            priceVO.setEnhanceStyleVersion(MAGICAL_COUPON_ENHANCE_STYLE_VERSION_1);
        }
        // 神券增强版本2
        if (StringUtils.equals("d", expResult) && showNewAtmosphereBar) {
            priceVO.setEnhanceStyleVersion(MAGICAL_COUPON_ENHANCE_STYLE_VERSION_2);
        }
    }

    private boolean getShowNewAtmosphereBar(ProductAtmosphereModuleVO result){
        return Objects.isNull(result) || Objects.isNull(result.getAtmosphere()) ? false : result.getAtmosphere().isShowNewAtmosphereBar();
    }

    private boolean isAtmosphere(ResourceExposureResponseDTO exposureResource) {
        return Optional.ofNullable(exposureResource)
                .map(ResourceExposureResponseDTO::getMaterialsDTOList)
                .filter(CollectionUtils::isNotEmpty)
                .map(list -> list.get(0))
                .map(LocationMaterialsDTO::getDealGroupPromotionDTO)
                .isPresent();
    }

    private void putPriceStyle(DetailPriceVO priceVO, CouponGuideTypeEnum guideType, boolean showNewAtmosphereBar) {
        // 折扣标签样式
        priceVO.setDiscountTagTextColor("#FF2727");
        List<ProductBestPromoFormula> bestPromoFormula = priceVO.getBestPromoFormula();
        boolean hasMagicCoupon = Optional.ofNullable(bestPromoFormula)
                .orElse(Collections.emptyList())
                .stream()
                .anyMatch(formula -> (Objects.nonNull(formula.getBlackBoxIcon()) || Objects.nonNull(formula.getWhiteBoxIcon())) && !(CouponGuideTypeEnum.GUIDE_PURCHASE == guideType));
        String productTag = priceVO.getProductTag();
        if (StringUtils.isNotBlank(productTag) && productTag.length() <= 6) {
            priceVO.setDiscountTag(productTag);
            return;
        }
        // 新客价 标签
        String discountTag = getHewCustomerDiscountTag(bestPromoFormula);
        if (StringUtils.isNotBlank(discountTag)) {
            priceVO.setDiscountTag(String.format("%s %s", priceVO.getDiscountTag(), discountTag));
            return;
        }
        // 付费会员 会员价 标签
        discountTag = getMerchantMemberDiscountTag(bestPromoFormula, integrationMemberCard, MemberChargeTypeEnum.CHARGE.getCode());
        if (StringUtils.isNotBlank(discountTag)) {
            priceVO.setDiscountTag(String.format("%s %s", priceVO.getDiscountTag(), discountTag));
            return;
        }
        // 神券价标签
        if (hasMagicCoupon) {
            priceVO.setDiscountTag(StringUtils.isBlank(priceVO.getDiscountTag())
                    ? "神券价" : String.format("%s%s", priceVO.getDiscountTag(), "神券价"));
            return;
        }
        // 免费会员 会员价 标签
        discountTag = getMerchantMemberDiscountTag(bestPromoFormula, integrationMemberCard, MemberChargeTypeEnum.FREE.getCode());
        if (StringUtils.isNotBlank(discountTag)) {
            priceVO.setDiscountTag(String.format("%s %s", priceVO.getDiscountTag(), discountTag));
            return;
        }
        // 强化样式下展示
        if (showNewAtmosphereBar){

        priceVO.setDiscountTag(StringUtils.isBlank(priceVO.getDiscountTag())
                ? "到手价" : String.format("%s %s", priceVO.getDiscountTag(), "到手价"));
        }

    }

    /**
     * 新客特惠-新客价
     * @param bestPromoFormula
     * @return
     */
    private String getHewCustomerDiscountTag(List<ProductBestPromoFormula> bestPromoFormula){
        String expModuleName = request.getClientTypeEnum().isMtClientType() ? "MtDiscountTag" : "DpDiscountTag";
        if (AbTestUtils.enableDealPriceLabelOptimization(abTestReturnValue.getAbTestExpResult(expModuleName))) {
            return null;
        }
        String discountTag ="";
        boolean flag = Optional.ofNullable(bestPromoFormula)
                .orElse(Collections.emptyList())
                .stream()
                .anyMatch(formula -> Objects.nonNull(formula.getPromoDesc())
                        && PromoTagEnum.NEW_CUSTOMER_DISCOUNT.getDesc().equals(formula.getPromoDesc()));
        if(flag){
            discountTag = "新客价";
        }
        return discountTag;
    }

    /**
     *
     * @param bestPromoFormula
     * @return
     */
    private String getMerchantMemberDiscountTag(List<ProductBestPromoFormula> bestPromoFormula,
                                                IntegrationMemberCard integrationMemberCard,
                                                Integer chargeType){
        String expModuleName = request.getClientTypeEnum().isMtClientType() ? "MtDiscountTag" : "DpDiscountTag";
        if (AbTestUtils.enableDealPriceLabelOptimization(abTestReturnValue.getAbTestExpResult(expModuleName))) {
            return null;
        }

        String discountTag ="";
        if (integrationMemberCard == null) {
            return discountTag;
        }

        if (!integrationMemberCard.isPremiumMemberCard()) {
            return discountTag;
        }

        ShopMemberDetailV shopMemberDetailV = integrationMemberCard.getMemberCardInterest();

        if (shopMemberDetailV == null) {
            return discountTag;
        }

        MemberInterestDetailDTO originalResult = shopMemberDetailV.getOriginalResult();

        if (originalResult == null || originalResult.getChargeType() == null) {
            return discountTag;
        }
        // 商品关联客户所关联的会员卡为付费会员卡
        if (!Objects.equals(chargeType, originalResult.getChargeType())) {
            return discountTag;
        }

        boolean flag = Optional.ofNullable(bestPromoFormula)
                .orElse(Collections.emptyList())
                .stream()
                .anyMatch(formula -> Objects.nonNull(formula.getPromoDesc())
                        && ( PromoTagEnum.MEMBER_BENEFITS.getDesc().equals(formula.getPromoDesc())
                        || PromoTagEnum.NEW_MEMBER_BENEFITS.getDesc().equals(formula.getPromoDesc())) );
        if(flag){
            discountTag = "会员价";
        }
        return discountTag;
    }

    /**
     * 构造有活动氛围背景图
     * @param promoPriceDisplayDTO
     * @param dealGroupPromotionDTO
     */
    private AtmosphereInfoDetailVO buildHasActivityAtmosphereBackgroundImage(PriceDisplayDTO promoPriceDisplayDTO,
                                                                             DealGroupPromotionDTO dealGroupPromotionDTO) {
        AtmosphereInfoDetailVO atmosphereDetailVO = new AtmosphereInfoDetailVO();
        // 氛围条背景图
        PromoSceneEnum promoSceneEnum = InflateCouponTipsComponent.getPromoScene(promoPriceDisplayDTO);
        // 1.1.1 仅团购优惠 -> picV1，newAtmosphere = false
        if (PromoSceneEnum.ONLY_DEAL_PROMO.getCode().equals(promoSceneEnum.getCode()) || !validMainTitleHeightAndWidth(dealGroupPromotionDTO)) {
            atmosphereDetailVO.setBackgroundImage(dealGroupPromotionDTO.getPicUrl());
            return atmosphereDetailVO;
        }
        // 1.1.2 除团购优惠外有其他优惠 -> picV2，要返回标题宽高; newAtmosphere = true
        atmosphereDetailVO.setBackgroundImage(dealGroupPromotionDTO.getPicUrlV2());
        atmosphereDetailVO.setMainTitleHeight(String.valueOf(dealGroupPromotionDTO.getMainTitleHeight()));
        atmosphereDetailVO.setMainTitleWidth(String.valueOf(dealGroupPromotionDTO.getMainTitleWidth()));
        atmosphereDetailVO.setShowNewAtmosphereBar(true);
        return atmosphereDetailVO;
    }

    private boolean validMainTitleHeightAndWidth(DealGroupPromotionDTO dealGroupPromotionDTO){
        return Objects.nonNull(dealGroupPromotionDTO.getMainTitleHeight()) && Objects.nonNull(dealGroupPromotionDTO.getMainTitleWidth());
    }

    /**
     * 构建有活动氛围其他信息如 倒计时、跳转链接等
     * @param atmosphereDetailVO
     * @param dealGroupPromotionDTO
     * @param activityId
     * @return
     */
    private AtmosphereInfoDetailVO buildHasActivityAtmosphereInfoOther(AtmosphereInfoDetailVO atmosphereDetailVO,
                                                                       DealGroupPromotionDTO dealGroupPromotionDTO,
                                                                       Long activityId) {
        if (Objects.isNull(atmosphereDetailVO)) {
            atmosphereDetailVO = new AtmosphereInfoDetailVO();
        }
        // 倒计时时间戳，倒计时48小时内才展示倒计时
        Long endTimeTs = dealGroupPromotionDTO.getCountDown();
        if (Objects.nonNull(endTimeTs)) {
            long currentTimeMs = System.currentTimeMillis();
            long remainingTimeMs = endTimeTs - currentTimeMs;
            if (remainingTimeMs > 0 && remainingTimeMs <= 48 * 60 * 60 * 1000) {
                atmosphereDetailVO.setCountDownTs(endTimeTs);
                // 倒计时文案
                String countDownDesc = StringUtils.isBlank(dealGroupPromotionDTO.getCountDownDesc()) ? "" : dealGroupPromotionDTO.getCountDownDesc();
                atmosphereDetailVO.setCountDownDesc(countDownDesc + " ");
                // 倒计时颜色
                atmosphereDetailVO.setCountDownColor("#FFFFFF");
                // 倒计时背景色
                atmosphereDetailVO.setCountDownBackgroundColor("transparent");
            }
        }
        // 跳转链接
        atmosphereDetailVO.setClickUrl(dealGroupPromotionDTO.getJumpLink());
        // 氛围条类型
        atmosphereDetailVO.setType(dealGroupPromotionDTO.getType());
        // 氛围条活动ID
        atmosphereDetailVO.setActivityId(String.valueOf(activityId));
        // 活动曝光id
        atmosphereDetailVO.setActivityExposureId(dealGroupPromotionDTO.getActivityId());
        return atmosphereDetailVO;
    }

    /**
     * 构造无活动氛围设置背景图
     * @param promoPriceDisplayDTO
\     * @param iconConfig
     * @param inflateCouponTips
     * @return
     */
    private AtmosphereInfoDetailVO buildNotActivityAtmosphereBackgroundImage(PriceDisplayDTO promoPriceDisplayDTO,
                                                                             IconConfig iconConfig,
                                                                             InflateCouponTips inflateCouponTips, DetailSaleVO saleVO, CouponGuideTypeEnum guideType){
        AtmosphereInfoDetailVO atmosphereDetailVO = new AtmosphereInfoDetailVO();
        // 氛围条背景图
        PromoSceneEnum promoSceneEnum = InflateCouponTipsComponent.getPromoScene(promoPriceDisplayDTO);
        // 1.2.1 有神券 -> 深色图; newAtmosphere = true
        if(PromoSceneEnum.HAS_MAGICAL_COUPON.getCode().equals(promoSceneEnum.getCode())){
            atmosphereDetailVO.setBackgroundImage(iconConfig.getMagicalCouponAtmosphereImage());
            atmosphereDetailVO.setShowNewAtmosphereBar(true);
            return atmosphereDetailVO;
        }
        //1.2.2 无神券
        // 且不是 无任何优惠场景
        if (!PromoSceneEnum.HAS_MAGICAL_COUPON.getCode().equals(promoSceneEnum.getCode()) &&
                !PromoSceneEnum.NO_PROMO.getCode().equals(promoSceneEnum.getCode())) {
            //1.2.2 一期逻辑（有除团购优惠外其他优惠 and 引导购买) -> 深色图; newAtmosphere = true）
            // 二期改为
            if(Objects.nonNull(inflateCouponTips)
                    && CouponGuideTypeEnum.GUIDE_PURCHASE.getCode().equals(inflateCouponTips.getCouponGuideType())) {
                atmosphereDetailVO.setBackgroundImage(iconConfig.getMagicalCouponAtmosphereImage());
                atmosphereDetailVO.setShowNewAtmosphereBar(true);
                return atmosphereDetailVO;
            }
            //1.2.3 无神券 and 有除团购优惠外其他优惠 -> 浅粉色图; newAtmosphere = true
            if(!promoSceneEnum.getCode().equals(PromoSceneEnum.ONLY_DEAL_PROMO.getCode()) && !promoSceneEnum.getCode().equals(PromoSceneEnum.NO_PROMO.getCode())){
                atmosphereDetailVO.setBackgroundImage(iconConfig.getNoMagicalCouponAtmosphereImage());
                atmosphereDetailVO.setShowNewAtmosphereBar(true);
                saleVO.setTextColor("#FF4B10");
                return atmosphereDetailVO;
            }
        }

        // 仅团购优惠 且 命中引导购买
        if(Objects.equals(CouponGuideTypeEnum.GUIDE_PURCHASE, guideType)
                && promoSceneEnum.getCode().equals(PromoSceneEnum.ONLY_DEAL_PROMO.getCode())){
            atmosphereDetailVO.setBackgroundImage(iconConfig.getMagicalCouponAtmosphereImage());
            atmosphereDetailVO.setShowNewAtmosphereBar(true);
            return atmosphereDetailVO;
        }
        //1.3 仅团购优惠 -> 无图; newAtmosphere = false
        if (promoSceneEnum.getCode().equals(PromoSceneEnum.ONLY_DEAL_PROMO.getCode())) {
            atmosphereDetailVO.setBackgroundImage("");
            return atmosphereDetailVO;
        }
        return null;
    }

    /**
     * 是否有活动氛围
     * @param promotionOpt
     * @return
     */
    private boolean hasActivityAtmosphereInfo(DealGroupPromotionDTO promotionOpt){
        if (Objects.nonNull(promotionOpt)
                && StringUtils.isNotBlank(promotionOpt.getPicUrl())
                && StringUtils.isNotBlank(promotionOpt.getPicUrlV2())) {
            return true;
        }
        return false;
    }

    /**
     * 获取活动氛围信息
     * @param exposureResource
     * @return
     */
    private DealGroupPromotionDTO getPromotionDTO(ResourceExposureResponseDTO exposureResource) {
        if (exposureResource == null
                || CollectionUtils.isEmpty(exposureResource.getMaterialsDTOList())) {
            return null;
        }

        return exposureResource.getMaterialsDTOList().get(0).getDealGroupPromotionDTO();
    }

    /**
     * 构建最优价格算价公式
     * @param promoPriceDisplayDTO
     * @param finalPrice
     * @return
     */
    protected List<ProductBestPromoFormula> buildBestPromoFormula(PriceDisplayDTO promoPriceDisplayDTO, String finalPrice, AbTestReturnValue abTestReturnValue) {
        if (Objects.isNull(promoPriceDisplayDTO)) {
            return null;
        }
        // 没有优惠无价格公示
        PromoSceneEnum promoSceneEnum = InflateCouponTipsComponent.getPromoScene(promoPriceDisplayDTO);
        if (PromoSceneEnum.NO_PROMO.getCode().equals(promoSceneEnum.getCode())) {
            return null;
        }
        IconConfig iconConfig = com.sankuai.dzshoppingguide.product.detail.application.utils.LionConfigUtils.getIconConfig();
        List<PromoDTO> usedPromos = promoPriceDisplayDTO.getUsedPromos();
        List<ProductBestPromoFormula> productBestPromoFormulaList = Lists.newArrayList();
        ProductBestPromoFormula marketPriceFormula = new ProductBestPromoFormula();
        marketPriceFormula.setPriceDesc(iconConfig.getMarketPriceIconText());
        marketPriceFormula.setPrice(PriceUtils.buildMarketPrice(promoPriceDisplayDTO.getMarketPrice()));
        //除神券价格外价格颜色都为#111111
        marketPriceFormula.setPriceColor("#111111");
        marketPriceFormula.setPromoDesc(PromoTagEnum.MARKET_PRICE.getDesc());
        marketPriceFormula.setPriceSymbol(PRICE_SYMBOL);
        productBestPromoFormulaList.add(marketPriceFormula);
        MMCTooltipTextDTO mmcTooltipTextDTO = InflateCouponTipsComponent.getMMCTooltipTextDTO(promoPriceDisplayDTO.getExtendDisplayInfo());
        // 获取-团详神券感知强化-实验结果
        String expResult = abTestReturnValue.getAbTestExpResult("MtMagicalCouponStyleEnhancementV2");
        CouponGuideTypeEnum guideType = InflateCouponTipsComponent.getCouponGuideTypeEnum(usedPromos, new BigDecimal(finalPrice), mmcTooltipTextDTO);

        for (PromoDTO promoDTO : usedPromos) {
            if (promoDTO.getIdentity() == null || promoDTO.getAmount() == null || StringUtils.isEmpty(promoDTO.getIdentity().getPromoTypeDesc())) {
                continue;
            }
            ProductBestPromoFormula productBestPromoFormula = new ProductBestPromoFormula();
            productBestPromoFormula.setPrice(PriceHelper.dropLastZero(promoDTO.getAmount()));
            productBestPromoFormula.setPromoDesc(PromoHelper.convertPromoTag(promoDTO.getIdentity().getPromoShowType(), promoDTO.getIdentity().getPromoTypeDesc()));
            productBestPromoFormula.setPriceSymbol(PRICE_SYMBOL);
            productBestPromoFormula.setPriceColor("#111111");
            if (PromoTagEnum.MAGICAL_MEMBER_PLATFORM_COUPON.getType().equals(promoDTO.getIdentity().getPromoShowType())) {
                Map<String, String> promotionOtherInfoMap = promoDTO.getPromotionOtherInfoMap();
                boolean afterInflate = Boolean.parseBoolean(promotionOtherInfoMap.get(PromotionPropertyEnum.AFTER_INFLATE.getValue()));
                productBestPromoFormula.setAfterInflate(afterInflate);
                //白盒红包icon
                Icon whiteBoxIcon = new Icon();
                whiteBoxIcon.setIcon(iconConfig.getAfterInflate());
                whiteBoxIcon.setIconWidth(iconConfig.getAfterInflateWidth());
                whiteBoxIcon.setIconHeight(iconConfig.getAfterInflateHeight());
                productBestPromoFormula.setWhiteBoxIcon(whiteBoxIcon);
                productBestPromoFormula.setPriceColor("#FF4B10");
                productBestPromoFormula.setPromoDesc(PromoTagEnum.MAGICAL_MEMBER_PLATFORM_COUPON.getDesc());
                if (guideType != null && CouponGuideTypeEnum.GUIDE_INFLATE.getCode().equals(guideType.getCode())) {
                    Optional.ofNullable(promoPriceDisplayDTO.getExtendDisplayInfo())
                            .filter(MapUtils::isNotEmpty)
                            .map(InflateCouponTipsComponent::getMMCTooltipTextDTO)
                            .ifPresent(mmcTooltipText -> {
                                productBestPromoFormula.setBlackBoxPriceColor("#FF2626");
                                productBestPromoFormula.setBlackBoxPrice(mmcTooltipText.getPreInflateDiscountAmount());
                            });
                    //黑盒红包icon
                    Icon blackBoxIcon = new Icon();
                    //引导膨胀价格公示展示红包icon
                    blackBoxIcon.setIcon(iconConfig.getDoubleRedPacket());
                    blackBoxIcon.setIconWidth(iconConfig.getDoubleRedPacketWidth());
                    blackBoxIcon.setIconHeight(iconConfig.getDoubleRedPacketHeight());
                    // 团详神券感知强化V2 实验, 引导膨胀
                    buildBlackBoxIconByGuideExp(blackBoxIcon, CouponGuideTypeEnum.GUIDE_INFLATE, expResult, LionConfigUtils.getGuideInflateIconConfig());
                    productBestPromoFormula.setBlackBoxIcon(blackBoxIcon);
                }
            }
            productBestPromoFormulaList.add(productBestPromoFormula);
        }

        // 神券强化感知二期实验
        if (StringUtils.equals("d", expResult) && guideType != null && CouponGuideTypeEnum.GUIDE_PURCHASE.getCode().equals(guideType.getCode())) {
            ProductBestPromoFormula productBestPromoFormula = new ProductBestPromoFormula();
            productBestPromoFormula.setPromoDesc(PromoTagEnum.MAGICAL_MEMBER_PLATFORM_COUPON.getDesc());
            // 引导购买黑盒红包
            //黑盒红包icon
            Icon blackBoxIcon = new Icon();
            // 团详神券感知强化V2 实验， 引导购买
            buildBlackBoxIconByGuideExp(blackBoxIcon, CouponGuideTypeEnum.GUIDE_PURCHASE, expResult, LionConfigUtils.getGuidePurcahseIconConfig());
            productBestPromoFormula.setBlackBoxIcon(blackBoxIcon);
            productBestPromoFormulaList.add(productBestPromoFormula);
        }

        // 按照 orderTags 列表的顺序排序
        List<String> orderedTags = PromoTagEnum.orderedTags;
        productBestPromoFormulaList.sort((o1, o2) -> compareByOrderedTags(o1.getPromoDesc(), o2.getPromoDesc(), orderedTags));
        // 排序后处理神券描述
        productBestPromoFormulaList.forEach(formula -> {
            if (Objects.equals(formula.getPromoDesc(), PromoTagEnum.MAGICAL_MEMBER_PLATFORM_COUPON.getDesc())) {
                formula.setPromoDesc("");
            }
        });
        return productBestPromoFormulaList;
    }

    // 构造黑盒Icon
    private void buildBlackBoxIconByGuideExp(Icon blackBoxIcon, CouponGuideTypeEnum guideTypeEnum, String expResult, IconConfig iconConfig){
        if (StringUtils.equals("d", expResult) && Objects.nonNull(blackBoxIcon) && Objects.nonNull(iconConfig)) {
            if (guideTypeEnum == CouponGuideTypeEnum.GUIDE_INFLATE){
                // 引导膨胀
                blackBoxIcon.setIcon(iconConfig.getDoubleRedPacket());
                blackBoxIcon.setIconWidth(iconConfig.getDoubleRedPacketWidth());
                blackBoxIcon.setIconHeight(iconConfig.getDoubleRedPacketHeight());
            } else if (guideTypeEnum == CouponGuideTypeEnum.GUIDE_PURCHASE){
                // 引导购买
                blackBoxIcon.setIcon(iconConfig.getDoubleRedPacket());
                blackBoxIcon.setIconWidth(iconConfig.getDoubleRedPacketWidth());
                blackBoxIcon.setIconHeight(iconConfig.getDoubleRedPacketHeight());
            }
        }
    }

    /**
     * 根据有序标签列表进行排序
     * @param desc1 标签1
     * @param desc2 标签2
     * @param orderedTags 有序标签列表
     * @return 比较结果
     */
    private int compareByOrderedTags(String desc1, String desc2, List<String> orderedTags) {
        // 处理空值情况
        if (desc1 == null && desc2 == null) {
            return 0;
        }
        if (desc1 == null) {
            return 1;
        }
        if (desc2 == null) {
            return -1;
        }

        // 获取在 orderedTags 中的位置
        int index1 = orderedTags.indexOf(desc1);
        int index2 = orderedTags.indexOf(desc2);

        // 如果标签不在 orderedTags 中,则排在后面
        if (index1 == -1 && index2 == -1) {
            return 0;
        }
        if (index1 == -1) {
            return 1;
        }
        if (index2 == -1) {
            return -1;
        }

        return Integer.compare(index1, index2);
    }


}
