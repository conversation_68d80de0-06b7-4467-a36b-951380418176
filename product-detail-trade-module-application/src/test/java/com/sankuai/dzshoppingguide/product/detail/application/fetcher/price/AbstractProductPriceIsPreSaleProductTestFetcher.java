package com.sankuai.dzshoppingguide.product.detail.application.fetcher.price;

import static org.junit.jupiter.api.Assertions.*;
import static org.junit.jupiter.api.Assertions.assertThrows;

import com.sankuai.dealuser.price.display.api.model.ClientEnv;
import com.sankuai.dealuser.price.display.api.model.ProductIdentity;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.dto.ProductPriceReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class AbstractProductPriceIsPreSaleProductTestFetcher {

    @InjectMocks
    private AbstractProductPriceFetcher<ProductPriceReturnValue> abstractProductPrice = new AbstractProductPriceFetcher<ProductPriceReturnValue>() {

        @Override
        protected void postProcessPriceRequest(ClientEnv clientEnv, List<ProductIdentity> productIdentities, Map<String, String> extension) {
            // Empty implementation for abstract method
        }

        @Override
        protected CompletableFuture<ProductPriceReturnValue> doFetch() {
            // Empty implementation for abstract method
            return null;
        }
    };

    /**
     * Test case: when preSaleTag contains "true", should return true
     */
    @Test
    public void testIsPreSaleProduct_WhenPreSaleTagContainsTrue_ShouldReturnTrue() throws Throwable {
        // arrange
        Map<String, AttrDTO> attrMap = new HashMap<>();
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setValue(Collections.singletonList("true"));
        attrMap.put("preSaleTag", attrDTO);
        ProductAttr productAttr = new ProductAttr(attrMap);
        // act
        boolean result = abstractProductPrice.isPreSaleProduct(productAttr);
        // assert
        assertTrue(result);
    }

    /**
     * Test case: when preSaleTag is blank, should return false
     */
    @Test
    public void testIsPreSaleProduct_WhenPreSaleTagIsBlank_ShouldReturnFalse() throws Throwable {
        // arrange
        Map<String, AttrDTO> attrMap = new HashMap<>();
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setValue(Collections.singletonList(""));
        attrMap.put("preSaleTag", attrDTO);
        ProductAttr productAttr = new ProductAttr(attrMap);
        // act
        boolean result = abstractProductPrice.isPreSaleProduct(productAttr);
        // assert
        assertFalse(result);
    }

    /**
     * Test case: when preSaleTag is null, should return false
     */
    @Test
    public void testIsPreSaleProduct_WhenPreSaleTagIsNull_ShouldReturnFalse() throws Throwable {
        // arrange
        Map<String, AttrDTO> attrMap = new HashMap<>();
        ProductAttr productAttr = new ProductAttr(attrMap);
        // act
        boolean result = abstractProductPrice.isPreSaleProduct(productAttr);
        // assert
        assertFalse(result);
    }

    /**
     * Test case: when preSaleTag contains other values, should return false
     */
    @Test
    public void testIsPreSaleProduct_WhenPreSaleTagContainsOtherValue_ShouldReturnFalse() throws Throwable {
        // arrange
        Map<String, AttrDTO> attrMap = new HashMap<>();
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setValue(Collections.singletonList("false"));
        attrMap.put("preSaleTag", attrDTO);
        ProductAttr productAttr = new ProductAttr(attrMap);
        // act
        boolean result = abstractProductPrice.isPreSaleProduct(productAttr);
        // assert
        assertFalse(result);
    }

    /**
     * Test case: when ProductAttr is null, should throw NullPointerException
     */
    @Test
    public void testIsPreSaleProduct_WhenProductAttrIsNull_ShouldReturnFalse() throws Throwable {
        // act & assert
        assertThrows(NullPointerException.class, () -> abstractProductPrice.isPreSaleProduct(null));
    }
}
