package com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommend.dto;

import com.dianping.deal.shop.dto.DealGroupShop;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Map;

/**
 * @Author: zhangyuan103
 * @Date: 2025/5/26
 * @Description 同店推荐美团-团单id映射
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
public class DealMapperReturnValue extends FetcherReturnValueDTO {
    /**
     * 美团团单id和点评团单id映射
     */
    private Map<Long, Long> mtDpDIdMap;

    /**
     * map: 点评团单id,DealGroupShop
     */
    private Map<Long, DealGroupShop> dpDealGroupShopMap;
}
