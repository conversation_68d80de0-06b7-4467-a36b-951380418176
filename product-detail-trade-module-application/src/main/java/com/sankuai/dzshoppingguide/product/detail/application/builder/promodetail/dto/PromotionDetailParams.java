package com.sankuai.dzshoppingguide.product.detail.application.builder.promodetail.dto;

import com.dianping.tgc.open.entity.PromoCouponInfo;
import com.dianping.tgc.open.entity.PromoExposureInfo;
import com.dianping.tgc.open.entity.PromoReturnInfo;
import com.dianping.tpfun.product.api.sku.pintuan.dto.PinProductBrief;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.abtest.AbTestReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.cityId.CityIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.shopid.ShopIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.membercard.card.discount.MemberCardDiscountReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.best.shop.ShopInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.dto.ProductPriceReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.defaultselect.SkuDefaultSelect;
import com.sankuai.dzshoppingguide.product.detail.spi.promo.vo.DealGift;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class PromotionDetailParams {
    // 价格信息（含优惠）
    private PriceDisplayDTO dealPromDisplayDTO;
    private ProductPriceReturnValue productPromoPriceReturnValue;
    // 商品分类
    private ProductCategory productCategory;
    // OPT券列表
    private List<PromoCouponInfo> optPromoCoupons;
    // 金融券
    private List<PromoCouponInfo> financialCoupons;
    // OPT券与金融券合并券列表
    private List<PromoCouponInfo> optAndFinancialCoupons;
    // 投放券(曝光券)列表
    private List<PromoExposureInfo> exposureCoupons;
    // mrn 版本
    private String mrnVersion;
    // 买赠活动
    private List<DealGift> dealGifts;
    // 下单返礼、返券、金融券活动
    private List<PromoReturnInfo> promoReturnActivities;
    // 拼团活动
    private PinProductBrief pinProductBrief;
    private ProductDetailPageRequest pageRequest;
    private BigDecimal pinPoolPromoAmount;
    private PriceDisplayDTO normalPriceDisplayDTO;
    private ShopInfo shopInfo;
    // 闲时价格
    private PriceDisplayDTO idleTimePriceDisplayDTO;
    // 会员卡折扣信息
    private MemberCardDiscountReturnValue memberCardDiscount;
    private SkuDefaultSelect skuDefaultSelect;
    private ShopIdMapper shopIdMapper;
    private ProductBaseInfo productBaseInfo;
    private CityIdMapper cityIdMapper;
    private AbTestReturnValue abTestReturnValue;
}
