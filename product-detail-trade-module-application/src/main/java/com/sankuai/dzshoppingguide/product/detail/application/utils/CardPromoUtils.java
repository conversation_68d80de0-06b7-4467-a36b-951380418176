package com.sankuai.dzshoppingguide.product.detail.application.utils;

import com.google.common.collect.Lists;
import com.sankuai.dzcard.navigation.api.enums.CardTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommendgoods.dtos.ProductPromoPriceM;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.timesdeal.dto.CardM;
import com.sankuai.dztheme.deal.res.enums.PromoTypeEnum;
import org.apache.commons.collections.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;

/**
 * 卡优惠工具类 [仅考虑了玩乐卡、会员卡、会员日、商家会员卡]
 *
 * <AUTHOR>
 * @date 2022/4/13
 */
public class CardPromoUtils {
    /**
     * 卡优惠集合
     */
    public static final List<Integer> CARD_PROMOS = Lists.newArrayList(PromoTypeEnum.DISCOUNT_CARD.getType(), PromoTypeEnum.JOY_CARD.getType(), PromoTypeEnum.MEMBER_DAY.getType());


    /**
     * 获取用户持卡的优惠(取第一个）
     *
     * @param promos
     * @param cardM
     * @return
     */
    public static ProductPromoPriceM getFirstUserHoldCardPromo(List<ProductPromoPriceM> promos, CardM cardM) {
        if (cardM == null || CollectionUtils.isEmpty(promos)) {
            return null;
        }
        return promos.stream().filter(o -> isUserHoldTargetCardPromo(o, cardM)).findFirst().orElse(null);
    }
    
    /**
     * 用户是否持有卡优惠对应的卡
     *
     * @param promo
     * @param cardM
     * @return true 持有
     */
    public static boolean isUserHoldTargetCardPromo(ProductPromoPriceM promo, CardM cardM) {
        if (promo == null || cardM == null || CollectionUtils.isEmpty(cardM.getUserCardList())) {
            return false;
        }
        if (promo.getPromoType() == PromoTypeEnum.DISCOUNT_CARD.getType() && cardM.getUserCardList().contains(CardTypeEnum.DISCOUNT_CARD.getCode())) {
            return true;
        }
        if (promo.getPromoType() == PromoTypeEnum.MEMBER_DAY.getType() && cardM.getUserCardList().contains(CardTypeEnum.DISCOUNT_CARD.getCode())) {
            return true;
        }
        return promo.getPromoType() == PromoTypeEnum.JOY_CARD.getType() && cardM.getUserCardList().contains(CardTypeEnum.JOY_CARD.getCode());
    }

    /**
     * 是否存在卡优惠
     *
     * @param promos
     * @return
     */
    public static boolean hasCardPromo(List<ProductPromoPriceM> promos) {
        return getFirstCardPromo(promos) != null;
    }

    /**
     * 卡优惠是否倒挂：卡优惠比立减优惠要贵，即不开卡反而更便宜
     *
     * @return true 有倒挂
     */
    public static boolean isCardDaoGuaPromo(List<ProductPromoPriceM> promos) {
        if (CollectionUtils.isEmpty(promos)) {
            return false;
        }
        ProductPromoPriceM directPromo = promos.stream().filter(o -> o.getPromoType() == PromoTypeEnum.DIRECT_PROMO.getType()).findFirst().orElse(null);
        ProductPromoPriceM cardPromo = getFirstCardPromo(promos);
        return isCardDaoGuaPromo(directPromo, cardPromo);
    }

    /**
     * 获取第一个卡优惠
     *
     * @param promos
     * @return
     */
    public static ProductPromoPriceM getFirstCardPromo(List<ProductPromoPriceM> promos) {
        if (CollectionUtils.isEmpty(promos)) {
            return null;
        }
        return promos.stream().filter(o -> CARD_PROMOS.contains(o.getPromoType())).findFirst().orElse(null);
    }

    /**
     * 卡优惠是否倒挂：卡优惠比立减优惠要贵，即不开卡反而更便宜
     *
     * @return true 有倒挂
     */
    public static boolean isCardDaoGuaPromo(ProductPromoPriceM directPromo, ProductPromoPriceM cardPromo) {
        if (directPromo == null || cardPromo == null) {
            return false;
        }
        BigDecimal directPrice = directPromo.getPromoPrice();
        BigDecimal cardPrice = cardPromo.getPromoPrice();
        //立减价 < 会员价 且 存在互斥
        return isShopPromoLowerThenMember(directPrice, cardPrice);
    }

    /**
     * 立减价 < 会员价，即存在倒挂
     *
     * @return
     */
    private static boolean isShopPromoLowerThenMember(BigDecimal shopPromoPrice, BigDecimal memberPromoPrice) {
        if (shopPromoPrice == null || memberPromoPrice == null) {
            return false;
        }
        return shopPromoPrice.compareTo(memberPromoPrice) < 0;
    }

}
