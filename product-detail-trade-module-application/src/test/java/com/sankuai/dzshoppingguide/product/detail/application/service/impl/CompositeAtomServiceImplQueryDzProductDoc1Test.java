package com.sankuai.dzshoppingguide.product.detail.application.service.impl;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.google.common.util.concurrent.SettableFuture;
import com.meituan.service.mobile.mtthrift.netty.ContextStore;
import com.sankuai.dzcard.navigation.api.DzCardExposureService;
import com.sankuai.dzshoppingguide.product.detail.domain.utils.ThriftAsyncUtils;
import com.sankuai.dztheme.massagebook.theme.ReserveThemeQueryService;
import com.sankuai.dztheme.massagebook.theme.req.ReserveQueryRequest;
import com.sankuai.dztheme.massagebook.theme.res.ReserveQueryResponse;
import com.sankuai.general.product.query.center.client.service.DealGroupQueryService;
import com.sankuai.meituan.charity.merchant.main.sdk.WelfareDocFacade;
import com.sankuai.meituan.charity.merchant.main.sdk.dto.request.DzVpoiReq;
import com.sankuai.meituan.charity.merchant.main.sdk.dto.response.DzProductDocResp;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import org.junit.*;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class CompositeAtomServiceImplQueryDzProductDoc1Test {

    @InjectMocks
    private CompositeAtomServiceImpl compositeAtomService;

    @Mock
    private WelfareDocFacade welfareDocFacade;

    private SettableFuture<Object> settableFuture;

    @BeforeEach
    void setUp() {
        settableFuture = SettableFuture.create();
        ContextStore.setFuture(settableFuture);
    }

    @AfterEach
    void tearDown() {
        ContextStore.removeFuture();
    }

    /**
     * 测试 queryDzProductDoc 方法正常情况
     * 期望：返回正确的 DzProductDocResp 对象
     */
    @Test
    public void testQueryDzProductDocNormal() throws Throwable {
        // arrange
        DzVpoiReq dzVpoiReq = new DzVpoiReq();
        DzProductDocResp expectedResp = new DzProductDocResp();
        expectedResp.setText("test");
        expectedResp.setSignCharity(true);
        when(welfareDocFacade.queryDzProductDoc(any(DzVpoiReq.class))).thenReturn(expectedResp);
        // act
        CompletableFuture<DzProductDocResp> future = compositeAtomService.queryDzProductDoc(dzVpoiReq);
        settableFuture.set(expectedResp);
        // assert
        Assertions.assertNotNull(future, "Future should not be null");
        DzProductDocResp result = future.get();
        Assertions.assertNotNull(result, "Result should not be null");
        Assertions.assertEquals(expectedResp.getText(), result.getText(), "Text should match");
        Assertions.assertEquals(expectedResp.getSignCharity(), result.getSignCharity(), "SignCharity should match");
    }

    /**
     * 测试 queryDzProductDoc 方法，当welfare返回null的情况
     * 期望：返回 null
     */
    @Test
    public void testQueryDzProductDocWithNullResponse() throws Throwable {
        // arrange
        DzVpoiReq dzVpoiReq = new DzVpoiReq();
        when(welfareDocFacade.queryDzProductDoc(any(DzVpoiReq.class))).thenReturn(null);
        // act
        CompletableFuture<DzProductDocResp> future = compositeAtomService.queryDzProductDoc(dzVpoiReq);
        settableFuture.set(null);
        // assert
        Assertions.assertNotNull(future, "Future should not be null");
        Assertions.assertNull(future.get(), "Result should be null when welfare returns null");
    }
}
