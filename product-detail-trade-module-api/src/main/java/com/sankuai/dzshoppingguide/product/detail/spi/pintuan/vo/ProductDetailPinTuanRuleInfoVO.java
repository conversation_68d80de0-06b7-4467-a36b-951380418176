package com.sankuai.dzshoppingguide.product.detail.spi.pintuan.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.dz.product.detail.gateway.spi.vo.AbstractModuleVO;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.pintuan.ProductDetailFeaturesLayerConfigVO;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.pintuan.ProductDetailGuaranteeItem;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2025/3/13
 */
@EqualsAndHashCode(callSuper = true)
@TypeDoc(description = "商品详情页-拼团规则模块")
@Data
@MobileDo(id = 0x118e)
public class ProductDetailPinTuanRuleInfoVO extends AbstractModuleVO {
    @Override
    public String getModuleKey() {
        return ModuleKeyConstants.MODULE_DETAIL_DEAL_PINTUAN_RULE;
    }

    /**
     * 规则信息
     */
    @MobileDo.MobileField(key = 0xe23d)
    private List<ProductDetailGuaranteeItem> items;

    /**
     * 浮层信息
     */
    @MobileDo.MobileField(key = 0xc522)
    private ProductDetailFeaturesLayerConfigVO featuresLayer;

    /**
     * 标题
     */
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

}
