package com.sankuai.dzshoppingguide.product.detail.application.fetcher.timescard;

import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.athena.inf.cache2.CacheClient;
import com.sankuai.athena.inf.cache2.Cache<PERSON>ey;
import com.sankuai.athena.inf.cache2.loader.DataLoader;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.dealgroup.DealGroupIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.dealgroup.DealGroupIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.pinpool.PinProductIdResult;
import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
import com.sankuai.dzshoppingguide.product.detail.domain.utils.RedisClientUtils;
import com.sankuai.merchantcard.timescard.exposure.res.QueryDealGroupCardBarSummaryRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @create 2025/3/10 15:35
 */
@Fetcher(
        previousLayerDependencies = {DealGroupIdMapperFetcher.class}
)
@Slf4j
public class TimesCardFetcher extends NormalFetcherContext<TimesCardResult> {


    @Autowired
    private CompositeAtomService compositeAtomService;

    @Override
    protected CompletableFuture<TimesCardResult> doFetch() {
        try {
            return compositeAtomService.preTimesCardsV2(buildRequest((int) request.getProductId(), request.getPoiId(), request.getUserId(), request.getClientTypeEnum().getPlatform().getCode())).thenApply(res ->{
                if (res != null && res.isSuccess()){
                    return new TimesCardResult(res.getData());
                }
                return null;
            });
        }catch (Exception e) {
            log.error(e.getMessage(), e);
            return CompletableFuture.completedFuture(null);
        }

    }

    public QueryDealGroupCardBarSummaryRequest buildRequest(int dealGroupId, long longShopId, long userId, int code){
        QueryDealGroupCardBarSummaryRequest request = new QueryDealGroupCardBarSummaryRequest();
        request.setLongShopId(longShopId);
        request.setPlatform(code);
        request.setDealGroupId(dealGroupId);
        request.setUserId(userId);
        return request;
    }

}
