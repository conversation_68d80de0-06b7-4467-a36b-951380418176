package com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.attr;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.response.FetcherResponse;
import com.sankuai.dz.product.detail.gateway.spi.enums.ProductTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.QueryCenterAggregateReturnValue;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.deal.DealGroupDealDTO;
import java.lang.reflect.Field;
import java.util.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.*;

public class SkuAttrFetcherTest {

    private SkuAttrFetcher fetcher;

    private ProductDetailPageRequest request;

    @BeforeEach
    public void setup() throws Exception {
        fetcher = new SkuAttrFetcher();
        request = mock(ProductDetailPageRequest.class);
        when(request.getProductTypeEnum()).thenReturn(ProductTypeEnum.DEAL);
        // Use reflection to set protected field
        Field requestField = fetcher.getClass().getSuperclass().getSuperclass().getDeclaredField("request");
        requestField.setAccessible(true);
        requestField.set(fetcher, request);
    }

    /**
     * 测试正常场景：aggregateResult 不为空，且所有嵌套对象都不为空
     */
    @Test
    public void testMapResultNormalCase() throws Throwable {
        // arrange
        FetcherResponse<QueryCenterAggregateReturnValue> aggregateResult = mock(FetcherResponse.class);
        QueryCenterAggregateReturnValue returnValue = mock(QueryCenterAggregateReturnValue.class);
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        DealGroupDealDTO dealGroupDealDTO = mock(DealGroupDealDTO.class);
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("color");
        attrDTO.setValue(Arrays.asList("red", "blue"));
        when(aggregateResult.getReturnValue()).thenReturn(returnValue);
        when(returnValue.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getDeals()).thenReturn(Arrays.asList(dealGroupDealDTO));
        when(dealGroupDealDTO.getAttrs()).thenReturn(Arrays.asList(attrDTO));
        when(dealGroupDealDTO.getDealId()).thenReturn(1L);
        // act
        FetcherResponse<SkuAttr> result = fetcher.mapResult(aggregateResult);
        // assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getReturnValue());
        Map<String, AttrDTO> skuAttrs = result.getReturnValue().getSkuAttrs(1L);
        assertNotNull(skuAttrs);
        assertEquals(1, skuAttrs.size());
        assertEquals("color", skuAttrs.get("color").getName());
    }

    /**
     * 测试边界场景：aggregateResult 为空
     */
    @Test
    public void testMapResultAggregateResultIsNull() throws Throwable {
        // act
        FetcherResponse<SkuAttr> result = fetcher.mapResult(null);
        // assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getReturnValue());
        assertTrue(result.getReturnValue().getSkuAttrs(1L).isEmpty());
    }

    /**
     * 测试边界场景：QueryCenterAggregateReturnValue 为空
     */
    @Test
    public void testMapResultReturnValueIsNull() throws Throwable {
        // arrange
        FetcherResponse<QueryCenterAggregateReturnValue> aggregateResult = mock(FetcherResponse.class);
        when(aggregateResult.getReturnValue()).thenReturn(null);
        // act
        FetcherResponse<SkuAttr> result = fetcher.mapResult(aggregateResult);
        // assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getReturnValue());
        assertTrue(result.getReturnValue().getSkuAttrs(1L).isEmpty());
    }

    /**
     * 测试边界场景：DealGroupDTO 为空
     */
    @Test
    public void testMapResultDealGroupDTOIsNull() throws Throwable {
        // arrange
        FetcherResponse<QueryCenterAggregateReturnValue> aggregateResult = mock(FetcherResponse.class);
        QueryCenterAggregateReturnValue returnValue = mock(QueryCenterAggregateReturnValue.class);
        when(aggregateResult.getReturnValue()).thenReturn(returnValue);
        when(returnValue.getDealGroupDTO()).thenReturn(null);
        // act
        FetcherResponse<SkuAttr> result = fetcher.mapResult(aggregateResult);
        // assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getReturnValue());
        assertTrue(result.getReturnValue().getSkuAttrs(1L).isEmpty());
    }

    /**
     * 测试边界场景：DealGroupDealDTO 列表为空
     */
    @Test
    public void testMapResultDealGroupDealDTOListIsEmpty() throws Throwable {
        // arrange
        FetcherResponse<QueryCenterAggregateReturnValue> aggregateResult = mock(FetcherResponse.class);
        QueryCenterAggregateReturnValue returnValue = mock(QueryCenterAggregateReturnValue.class);
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        when(aggregateResult.getReturnValue()).thenReturn(returnValue);
        when(returnValue.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getDeals()).thenReturn(Collections.emptyList());
        // act
        FetcherResponse<SkuAttr> result = fetcher.mapResult(aggregateResult);
        // assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getReturnValue());
        assertTrue(result.getReturnValue().getSkuAttrs(1L).isEmpty());
    }

    /**
     * 测试边界场景：DealGroupDealDTO 中的 AttrDTO 列表为空
     */
    @Test
    public void testMapResultAttrDTOListIsEmpty() throws Throwable {
        // arrange
        FetcherResponse<QueryCenterAggregateReturnValue> aggregateResult = mock(FetcherResponse.class);
        QueryCenterAggregateReturnValue returnValue = mock(QueryCenterAggregateReturnValue.class);
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        DealGroupDealDTO dealGroupDealDTO = mock(DealGroupDealDTO.class);
        when(aggregateResult.getReturnValue()).thenReturn(returnValue);
        when(returnValue.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getDeals()).thenReturn(Arrays.asList(dealGroupDealDTO));
        when(dealGroupDealDTO.getAttrs()).thenReturn(Collections.emptyList());
        when(dealGroupDealDTO.getDealId()).thenReturn(1L);
        // act
        FetcherResponse<SkuAttr> result = fetcher.mapResult(aggregateResult);
        // assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getReturnValue());
        assertTrue(result.getReturnValue().getSkuAttrs(1L).isEmpty());
    }
}
