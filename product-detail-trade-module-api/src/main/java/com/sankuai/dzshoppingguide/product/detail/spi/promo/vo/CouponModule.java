package com.sankuai.dzshoppingguide.product.detail.spi.promo.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-04-02
 */
@TypeDoc(description = "优惠券模块")
@Data
@MobileDo(id = 0xef3b)
public class CouponModule implements Serializable {

    @FieldDoc(description = "优惠栏模块")
    @MobileField(key = 0x4593)
    private PromotionBarModule promotionBarModule;

    @FieldDoc(description = "使用结束时间")
    @MobileField(key = 0x7950)
    private Long useEndTime;

    @FieldDoc(description = "rowkey，投放券专属字段")
    @MobileField(key = 0x2efe)
    private String rowKey;

    @FieldDoc(description = "投放活动id，活动投放券专属")
    @MobileField(key = 0x1b0d)
    private String resourceActivityId;

    @FieldDoc(description = "活动id，投放券专属字段")
    @MobileField(key = 0xe91)
    private String activityId;

    @FieldDoc(description = "物料id，投放券专属字段")
    @MobileField(key = 0x2a2c)
    private String materialId;

    @FieldDoc(description = "分流位id，投放券专属字段")
    @MobileField(key = 0xc2dd)
    private String flowId;

    @FieldDoc(description = "是否是曝光券")
    @MobileField(key = 0x79ba)
    private boolean isExposure;

    @FieldDoc(description = "券类型值，用于前端领券传参")
    @MobileField(key = 0x59e0)
    private int couponValueType;

    @FieldDoc(description = "券包ID列表，用于神券包")
    @MobileField(key = 0x55bf)
    private List<String> couponGroupIdList;

    @FieldDoc(description = "券数量")
    @MobileField(key = 0xf240)
    private int couponNum;

    @FieldDoc(description = "券ID")
    @MobileField(key = 0x860c)
    private String couponId;

    @FieldDoc(description = "券批次ID")
    @MobileField(key = 0xb991)
    private String applyId;

    @FieldDoc(description = "券使用步骤")
    @MobileField(key = 0x6fa)
    private MerchantPinTuanStep step;

    @FieldDoc(description = "政府金融券N选1，券包密钥")
    @MobileField(key = 0x37ce)
    private String packageSecretKey;

    @FieldDoc(description = "使用规则")
    @MobileField(key = 0x420f)
    private String useRule;

    @FieldDoc(description = "操作按钮")
    @MobileField(key = 0xadf8)
    private ActionButton actionButton;

    @FieldDoc(description = "使用门槛")
    @MobileField(key = 0xb8b)
    private CouponUsageThreshold couponUsageThreshold;

    @FieldDoc(description = "优惠券额度")
    @MobileField(key = 0xa712)
    private List<CouponAmount> couponAmount;

    @FieldDoc(description = "券主图")
    @MobileField(key = 0x169a)
    private String couponAvatar;

    @FieldDoc(description = "优惠券副标题")
    @MobileField(key = 0x8664)
    private List<CouponTitle> couponSubTitle;

    @FieldDoc(description = "券标题")
    @MobileField(key = 0xe7bf)
    private List<CouponTitle> couponTitle;

    @FieldDoc(description = "券标签")
    @MobileField(key = 0xe654)
    private CouponTitle couponTag;

    @FieldDoc(description = "券副标题icon")
    @MobileField(key = 0xab5a)
    private CouponSubTitleIcon couponSubTitleIcon;

    @FieldDoc(description = "0-可领取; 1-已领取")
    @MobileField(key = 0x3998)
    private int couponStatus;

    @FieldDoc(description = "券图标")
    @MobileField(key = 0x21ee)
    private CouponIcon couponIcon;

    @FieldDoc(description = "券样式")
    @MobileField(key = 0x1b4f)
    private int couponStyle;

    @FieldDoc(description = "券类型")
    @MobileField(key = 0x7cd6)
    private String couponType;

    @FieldDoc(description = "优惠类型")
    @MobileField(key = 0xdce2)
    private int promotionType;

    @FieldDoc(description = "券组件版本实验结果")
    @MobileField(key = 0xfeb9)
    private List<Integer> mmcPkgResult;
}