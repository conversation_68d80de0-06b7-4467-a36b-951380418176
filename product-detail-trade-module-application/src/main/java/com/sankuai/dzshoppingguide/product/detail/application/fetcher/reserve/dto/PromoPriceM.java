package com.sankuai.dzshoppingguide.product.detail.application.fetcher.reserve.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Data
public class PromoPriceM implements Serializable {

    /**
     * skuId
     */
    private long skuId;

    /**
     * 优惠时间点
     */
    private long priceTime;

    /**
     * 优惠后价格
     */
    private BigDecimal promoPrice;

    /**
     * 基础价
     */
    private BigDecimal basePrice;

    /**
     * 优惠金额
     */
    private BigDecimal promoAmount;

    /**
     * 优惠的价格标签, 如: 已省80
     */
    private String promoTag;

    /**
     * 优惠短文案，适用于详情页底bar
     */
    private String shortPromoTag;

    /**
     * 优惠类型标签
     * 详见 com.sankuai.dealuser.price.display.api.enums.PromoTagTypeEnum
     */
    private int promoTagType;

    /**
     * 计算公式 = 1 - ((basePrice - promoPrice) / basePrice)
     */
    private BigDecimal discount;

    /**
     * 优惠细节
     */
    private List<PromoItemM> promoItemList;

    /**
     * 总共优惠多少
     */
    private BigDecimal totalPromoPrice;

    /**
     * 总共优惠多少
     */
    private String totalPromoPriceTag;

    /**
     * 优惠icon
     */
    private String icon;

    /**
     * 神券扩展展示信息
     * map中key 枚举类：com.sankuai.dealuser.price.display.api.enums.ExtendDisplayInfoKeyEnum
     */
    private Map<String, String> extendDisplayInfo;

    /**
     * 价格优惠信息集合
     * 对应详情页横幅中的券包
     * key枚举 参考com.sankuai.dealuser.price.display.api.enums.PricePromoInfoTypeEnum
     */
    private Map<Integer, List<PromoItemM>> pricePromoInfoMap;
}
