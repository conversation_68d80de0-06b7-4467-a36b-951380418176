package com.sankuai.dzshoppingguide.product.detail.application.utils;

import static org.junit.Assert.*;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class HtmlUtilsEnterReplaceTest {

    /**
     * 测试 enterReplace 方法，当 html 为空时
     */
    @Test
    public void testEnterReplaceWhenHtmlIsEmpty() throws Throwable {
        // arrange
        String html = "";
        // act
        String result = HtmlUtils.enterReplace(html);
        // assert
        assertEquals(html, result);
    }

    /**
     * 测试 enterReplace 方法，当 html 不为空，解码后的 html 也不为空，html 中有需要替换的标签时
     */
    @Test
    public void testEnterReplaceWhenHtmlIsNotEmptyAndDecodedHtmlIsNotEmptyAndHasTags() throws Throwable {
        // arrange
        String html = "<div>test</div>";
        // act
        String result = HtmlUtils.enterReplace(html);
        // assert
        assertEquals("test\n", result);
    }

    /**
     * 测试 html2text 方法，输入为空的情况
     */
    @Test
    public void testHtml2textEmptyInput() throws Throwable {
        // arrange
        String html = "";
        // act
        String result = HtmlUtils.html2text(html);
        // assert
        assertEquals(html, result);
    }

    /**
     * 测试 html2text 方法，输入不为空，但没有 HTML 标签的情况
     */
    @Test
    public void testHtml2textNoHtmlTag() throws Throwable {
        // arrange
        String html = "Hello, world!";
        // act
        String result = HtmlUtils.html2text(html);
        // assert
        assertEquals(html, result);
    }

    /**
     * 测试 html2text 方法，输入不为空，有 HTML 标签的情况
     */
    @Test
    public void testHtml2textWithHtmlTag() throws Throwable {
        // arrange
        String html = "<p>Hello, <b>world!</b></p>";
        // act
        String result = HtmlUtils.html2text(html);
        // assert
        assertEquals("Hello, world!", result);
    }

    /**
     * Tests the rmAllByLabel method when rowLine is null.
     */
    @Test
    public void testRmAllByLabelRowLineIsNull() throws Throwable {
        // arrange
        String rowLine = null;
        String label = "label";
        // act
        String result = HtmlUtils.rmAllByLabel(rowLine, label);
        // assert
        assertEquals("", result);
    }

    /**
     * Tests the rmAllByLabel method when rowLine is not null, but label is null.
     */
    @Test
    public void testRmAllByLabelLabelIsNull() throws Throwable {
        // arrange
        String rowLine = "<label>content</label>";
        String label = null;
        // act
        String result = HtmlUtils.rmAllByLabel(rowLine, label);
        // assert
        assertEquals(rowLine, result);
    }

    /**
     * Tests the rmAllByLabel method when both rowLine and label are not null, but there is no match for the label in rowLine.
     */
    @Test
    public void testRmAllByLabelNoMatch() throws Throwable {
        // arrange
        String rowLine = "<otherLabel>content</otherLabel>";
        String label = "label";
        // act
        String result = HtmlUtils.rmAllByLabel(rowLine, label);
        // assert
        assertEquals(rowLine, result);
    }

    /**
     * Tests the rmAllByLabel method when both rowLine and label are not null, and there is a match for the label in rowLine.
     */
    @Test
    public void testRmAllByLabelMatch() throws Throwable {
        // arrange
        String rowLine = "<label>content</label>";
        String label = "label";
        // act
        String result = HtmlUtils.rmAllByLabel(rowLine, label);
        // assert
        // Corrected expectation to match the method's behavior
        assertEquals("", result);
    }
}
