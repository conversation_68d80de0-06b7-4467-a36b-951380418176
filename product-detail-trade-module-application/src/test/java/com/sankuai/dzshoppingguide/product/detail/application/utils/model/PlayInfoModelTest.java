package com.sankuai.dzshoppingguide.product.detail.application.utils.model;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import java.util.Arrays;
import java.util.Collections;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * Test cases for PlayInfoModel.getAttr method
 */
@ExtendWith(MockitoExtension.class)
public class PlayInfoModelTest {

    /**
     * Test when materialInfoList is null
     */
    @Test
    public void testGetAttr_WhenMaterialInfoListIsNull() throws Throwable {
        // arrange
        PlayInfoModel playInfoModel = new PlayInfoModel();
        String defaultValue = "default";
        // act
        String result = playInfoModel.getAttr("anyAttr", defaultValue);
        // assert
        assertEquals(defaultValue, result);
    }

    /**
     * Test when materialInfoList is empty
     */
    @Test
    public void testGetAttr_WhenMaterialInfoListIsEmpty() throws Throwable {
        // arrange
        PlayInfoModel playInfoModel = new PlayInfoModel();
        playInfoModel.setMaterialInfoList(Collections.emptyList());
        String defaultValue = "default";
        // act
        String result = playInfoModel.getAttr("anyAttr", defaultValue);
        // assert
        assertEquals(defaultValue, result);
    }

    /**
     * Test when attribute exists in materialInfoList
     */
    @Test
    public void testGetAttr_WhenAttributeExists() throws Throwable {
        // arrange
        PlayInfoModel playInfoModel = new PlayInfoModel();
        MaterialInfoModel materialInfo = new MaterialInfoModel("testKey", "testValue");
        playInfoModel.setMaterialInfoList(Arrays.asList(materialInfo));
        // act
        String result = playInfoModel.getAttr("testKey", "default");
        // assert
        assertEquals("testValue", result);
    }

    /**
     * Test when attribute doesn't exist in materialInfoList
     */
    @Test
    public void testGetAttr_WhenAttributeDoesNotExist() throws Throwable {
        // arrange
        PlayInfoModel playInfoModel = new PlayInfoModel();
        MaterialInfoModel materialInfo = new MaterialInfoModel("existingKey", "testValue");
        playInfoModel.setMaterialInfoList(Arrays.asList(materialInfo));
        String defaultValue = "default";
        // act
        String result = playInfoModel.getAttr("nonExistingKey", defaultValue);
        // assert
        assertEquals(defaultValue, result);
    }

    /**
     * Test when multiple materials exist but target attribute is not present
     */
    @Test
    public void testGetAttr_WithMultipleMaterialsButNoMatch() throws Throwable {
        // arrange
        PlayInfoModel playInfoModel = new PlayInfoModel();
        MaterialInfoModel materialInfo1 = new MaterialInfoModel("key1", "value1");
        MaterialInfoModel materialInfo2 = new MaterialInfoModel("key2", "value2");
        playInfoModel.setMaterialInfoList(Arrays.asList(materialInfo1, materialInfo2));
        String defaultValue = "default";
        // act
        String result = playInfoModel.getAttr("key3", defaultValue);
        // assert
        assertEquals(defaultValue, result);
    }

    /**
     * Test when multiple materials exist and target attribute is present
     */
    @Test
    public void testGetAttr_WithMultipleMaterialsAndMatch() throws Throwable {
        // arrange
        PlayInfoModel playInfoModel = new PlayInfoModel();
        MaterialInfoModel materialInfo1 = new MaterialInfoModel("key1", "value1");
        MaterialInfoModel materialInfo2 = new MaterialInfoModel("key2", "value2");
        playInfoModel.setMaterialInfoList(Arrays.asList(materialInfo1, materialInfo2));
        // act
        String result = playInfoModel.getAttr("key2", "default");
        // assert
        assertEquals("value2", result);
    }

    /**
     * Test when multiple materials exist and first match is returned
     */
    @Test
    public void testGetAttr_WithMultipleMaterialsAndFirstMatch() throws Throwable {
        // arrange
        PlayInfoModel playInfoModel = new PlayInfoModel();
        MaterialInfoModel materialInfo1 = new MaterialInfoModel("key1", "value1");
        MaterialInfoModel materialInfo2 = new MaterialInfoModel("key1", "value2");
        playInfoModel.setMaterialInfoList(Arrays.asList(materialInfo1, materialInfo2));
        // act
        String result = playInfoModel.getAttr("key1", "default");
        // assert
        assertEquals("value1", result);
    }

    /**
     * Test with empty string attribute name
     */
    @Test
    public void testGetAttr_WithEmptyAttributeName() throws Throwable {
        // arrange
        PlayInfoModel playInfoModel = new PlayInfoModel();
        MaterialInfoModel materialInfo = new MaterialInfoModel("", "value");
        playInfoModel.setMaterialInfoList(Arrays.asList(materialInfo));
        // act
        String result = playInfoModel.getAttr("", "default");
        // assert
        assertEquals("value", result);
    }

    /**
     * Test with empty string field value
     */
    @Test
    public void testGetAttr_WithEmptyFieldValue() throws Throwable {
        // arrange
        PlayInfoModel playInfoModel = new PlayInfoModel();
        MaterialInfoModel materialInfo = new MaterialInfoModel("key", "");
        playInfoModel.setMaterialInfoList(Arrays.asList(materialInfo));
        // act
        String result = playInfoModel.getAttr("key", "default");
        // assert
        assertEquals("", result);
    }

    /**
     * Test with empty string default value
     */
    @Test
    public void testGetAttr_WithEmptyDefaultValue() throws Throwable {
        // arrange
        PlayInfoModel playInfoModel = new PlayInfoModel();
        MaterialInfoModel materialInfo = new MaterialInfoModel("key", "value");
        playInfoModel.setMaterialInfoList(Arrays.asList(materialInfo));
        // act
        String result = playInfoModel.getAttr("nonexistent", "");
        // assert
        assertEquals("", result);
    }

    /**
     * Test with null default value
     */
    @Test
    public void testGetAttr_WithNullDefaultValue() throws Throwable {
        // arrange
        PlayInfoModel playInfoModel = new PlayInfoModel();
        MaterialInfoModel materialInfo = new MaterialInfoModel("key", "value");
        playInfoModel.setMaterialInfoList(Arrays.asList(materialInfo));
        // act
        String result = playInfoModel.getAttr("nonexistent", null);
        // assert
        assertNull(result);
    }
}
