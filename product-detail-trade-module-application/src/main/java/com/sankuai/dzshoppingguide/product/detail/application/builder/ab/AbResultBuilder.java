package com.sankuai.dzshoppingguide.product.detail.application.builder.ab;

import com.google.common.collect.Lists;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.extend.BaseBuilder;
import com.sankuai.dz.product.detail.gateway.spi.dto.ABDetailDTO;
import com.sankuai.dz.product.detail.gateway.spi.dto.ABResultDTO;
import com.sankuai.dzshoppingguide.product.detail.application.acl.douhu.AbTestResult;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.abtest.AbTestFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.abtest.AbTestReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.FetcherDAGStarter;
import com.sankuai.dzshoppingguide.product.detail.spi.ab.vo.AbResultVO;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2025/3/21 14:15
 */
@Builder(
        moduleKey = ModuleKeyConstants.PAGE_AB_RESULT,
        startFetcher = FetcherDAGStarter.class,
        dependentFetchers = {
            AbTestFetcher.class
        }
)
public class AbResultBuilder extends BaseBuilder<AbResultVO> {

    @Override
    public AbResultVO doBuild() {
        AbResultVO abResultVO = new AbResultVO();
        AbTestReturnValue abTestReturnValue = getDependencyResult(AbTestFetcher.class);
        if (abTestReturnValue == null || MapUtils.isEmpty(abTestReturnValue.getAbTestResultMap())) {
            return null;
        }
        List<ABDetailDTO> abDetailDTOList = abTestReturnValue
                .getAbTestResultMap()
                .values()
                .stream()
                .map(this::toABDetailDTO)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        abResultVO.setAbResultList(Lists.newArrayList(new ABResultDTO("", abDetailDTOList)));
        return abResultVO;
    }

    private ABDetailDTO toABDetailDTO(AbTestResult abTestResult) {
        if (abTestResult == null || StringUtils.isBlank(abTestResult.getExpStrategy())) {
            return null;
        }
        ABDetailDTO abDetailDTO = new ABDetailDTO();
        abDetailDTO.setExpId(abTestResult.getExpId());
        abDetailDTO.setExpBiInfo(abTestResult.getExpBiInfo());
        abDetailDTO.setExpStrategy(abTestResult.getExpStrategy());
        abDetailDTO.setExpResult(abTestResult.getExpResult());
        return abDetailDTO;
    }


}
