package com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.ext.param;

import com.alibaba.fastjson.JSON;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.enums.OrderPageExtParamEnums;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.ext.param.request.ExtParamBuilderRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: guangyujie
 * @Date: 2025/3/12 17:32
 */
@Slf4j
public abstract class BaseSingleExtParamBuilder implements ExtParamBuilder {

    @Override
    public Map<String, String> buildExtParam(final ProductDetailPageRequest request, final ExtParamBuilderRequest builderRequest) {
        try {
            OrderPageExtParamEnums key = buildKey();
            String value = doBuildExtParam(request, builderRequest);
            if (key == null || StringUtils.isBlank(value)) {
                return null;
            }
            Map<String, String> map = new HashMap<>();
            map.put(key.name(), value);
            return map;
        } catch (Throwable throwable) {
            log.error("{}.buildExtParam error!, request = {}, builderRequest={}",
                    this.getClass().getSimpleName(), JSON.toJSONString(request), JSON.toJSONString(builderRequest), throwable);
            return null;
        }
    }

    protected abstract OrderPageExtParamEnums buildKey();

    protected abstract String doBuildExtParam(final ProductDetailPageRequest request,
                                              final ExtParamBuilderRequest builderRequest) throws Exception;

}
