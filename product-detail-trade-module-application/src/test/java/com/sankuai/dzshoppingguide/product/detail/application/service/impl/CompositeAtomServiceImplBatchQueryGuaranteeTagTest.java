package com.sankuai.dzshoppingguide.product.detail.application.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.google.common.util.concurrent.SettableFuture;
import com.meituan.service.mobile.mtthrift.netty.ContextStore;
import com.sankuai.nib.price.operation.api.common.dto.SessionContextDTO;
import com.sankuai.nib.price.operation.api.common.response.Response;
import com.sankuai.nib.price.operation.api.guarantee.guide.standard.dto.ObjectGuaranteeTagDTO;
import com.sankuai.nib.price.operation.api.guarantee.guide.standard.request.BatchQueryGuaranteeTagRequest;
import com.sankuai.nib.price.operation.api.guarantee.guide.standard.service.GuaranteeQueryService;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import org.apache.thrift.TException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class CompositeAtomServiceImplBatchQueryGuaranteeTagTest {

    @Mock
    private GuaranteeQueryService guaranteeQueryService;

    @InjectMocks
    private CompositeAtomServiceImpl compositeAtomService;

    private SettableFuture<Response<List<ObjectGuaranteeTagDTO>>> settableFuture;

    @BeforeEach
    void setUp() {
        settableFuture = SettableFuture.create();
        ContextStore.setFuture(settableFuture);
    }

    /**
     * Test successful case with valid data
     */
    @Test
    public void testBatchQueryGuaranteeTag_Success() throws Throwable {
        // arrange
        SessionContextDTO sessionContext = new SessionContextDTO();
        BatchQueryGuaranteeTagRequest request = new BatchQueryGuaranteeTagRequest();
        List<ObjectGuaranteeTagDTO> expectedTags = new ArrayList<>();
        expectedTags.add(new ObjectGuaranteeTagDTO());
        Response<List<ObjectGuaranteeTagDTO>> response = new Response<>();
        response.setData(expectedTags);
        response.setCode(0);
        // act
        CompletableFuture<List<ObjectGuaranteeTagDTO>> result = compositeAtomService.batchQueryGuaranteeTag(sessionContext, request);
        settableFuture.set(response);
        // assert
        assertNotNull(result);
        assertEquals(expectedTags, result.join());
    }

    /**
     * Test case when service returns null response
     */
    @Test
    public void testBatchQueryGuaranteeTag_NullResponse() throws Throwable {
        // arrange
        SessionContextDTO sessionContext = new SessionContextDTO();
        BatchQueryGuaranteeTagRequest request = new BatchQueryGuaranteeTagRequest();
        // act
        CompletableFuture<List<ObjectGuaranteeTagDTO>> result = compositeAtomService.batchQueryGuaranteeTag(sessionContext, request);
        settableFuture.set(null);
        // assert
        assertNotNull(result);
        assertNull(result.join());
    }

    /**
     * Test case when service throws exception
     */
    @Test
    public void testBatchQueryGuaranteeTag_ServiceException() throws Throwable {
        // arrange
        SessionContextDTO sessionContext = new SessionContextDTO();
        BatchQueryGuaranteeTagRequest request = new BatchQueryGuaranteeTagRequest();
        doThrow(new TException("Service error")).when(guaranteeQueryService).batchQueryGuaranteeTag(any(), any());
        // act
        CompletableFuture<List<ObjectGuaranteeTagDTO>> result = compositeAtomService.batchQueryGuaranteeTag(sessionContext, request);
        settableFuture.setException(new TException("Service error"));
        // assert
        assertNotNull(result);
        assertNull(result.join());
    }

    /**
     * Test case with null parameters
     */
    @Test
    public void testBatchQueryGuaranteeTag_NullParameters() throws Throwable {
        // arrange
        Response<List<ObjectGuaranteeTagDTO>> response = new Response<>();
        response.setData(null);
        // act
        CompletableFuture<List<ObjectGuaranteeTagDTO>> result = compositeAtomService.batchQueryGuaranteeTag(null, null);
        settableFuture.set(response);
        // assert
        assertNotNull(result);
        assertNull(result.join());
    }

    /**
     * Test case when response data is null but response is not null
     */
    @Test
    public void testBatchQueryGuaranteeTag_NullResponseData() throws Throwable {
        // arrange
        SessionContextDTO sessionContext = new SessionContextDTO();
        BatchQueryGuaranteeTagRequest request = new BatchQueryGuaranteeTagRequest();
        Response<List<ObjectGuaranteeTagDTO>> response = new Response<>();
        response.setData(null);
        response.setCode(0);
        // act
        CompletableFuture<List<ObjectGuaranteeTagDTO>> result = compositeAtomService.batchQueryGuaranteeTag(sessionContext, request);
        settableFuture.set(response);
        // assert
        assertNotNull(result);
        assertNull(result.join());
    }
}
