package com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.customer;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.response.FetcherResponse;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.ComponentFetcherContext;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.QueryCenterAggregateReturnValue;
import com.sankuai.general.product.query.center.client.builder.model.DealGroupCustomerBuilder;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.dto.DealGroupCustomerDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import java.util.Optional;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class ProductCustomerFetcherTest {

    private ProductCustomerFetcher productCustomerFetcher;

    @BeforeEach
    public void setUp() {
        productCustomerFetcher = new ProductCustomerFetcher() {

            @Override
            protected FetcherResponse<ProductCustomer> mapResult(FetcherResponse<QueryCenterAggregateReturnValue> aggregateResult) {
                // 这个方法在测试 fulfillRequest 时不需要实现
                return null;
            }
        };
    }

    /**
     * 测试正常情况：所有字段都不为空
     */
    @Test
    public void testMapResultAllFieldsPresent() throws Throwable {
        // arrange
        ProductCustomerFetcher fetcher = new ProductCustomerFetcher();
        FetcherResponse<QueryCenterAggregateReturnValue> aggregateResult = mock(FetcherResponse.class);
        QueryCenterAggregateReturnValue returnValue = mock(QueryCenterAggregateReturnValue.class);
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        DealGroupCustomerDTO customerDTO = mock(DealGroupCustomerDTO.class);
        when(aggregateResult.getReturnValue()).thenReturn(returnValue);
        when(returnValue.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCustomer()).thenReturn(customerDTO);
        when(customerDTO.getOriginCustomerId()).thenReturn(123L);
        when(customerDTO.getPlatformCustomerId()).thenReturn(456L);
        // act
        FetcherResponse<ProductCustomer> result = fetcher.mapResult(aggregateResult);
        // assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(123L, result.getReturnValue().getOriginCustomerId());
        assertEquals(456L, result.getReturnValue().getPlatformCustomerId());
    }

    /**
     * 测试边界情况：aggregateResult 为空
     */
    @Test
    public void testMapResultAggregateResultIsNull() throws Throwable {
        // arrange
        ProductCustomerFetcher fetcher = new ProductCustomerFetcher();
        // act
        FetcherResponse<ProductCustomer> result = fetcher.mapResult(null);
        // assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNull(result.getReturnValue());
    }

    /**
     * 测试边界情况：aggregateResult.getReturnValue() 返回空
     */
    @Test
    public void testMapResultReturnValueIsNull() throws Throwable {
        // arrange
        ProductCustomerFetcher fetcher = new ProductCustomerFetcher();
        FetcherResponse<QueryCenterAggregateReturnValue> aggregateResult = mock(FetcherResponse.class);
        when(aggregateResult.getReturnValue()).thenReturn(null);
        // act
        FetcherResponse<ProductCustomer> result = fetcher.mapResult(aggregateResult);
        // assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNull(result.getReturnValue());
    }

    /**
     * 测试边界情况：QueryCenterAggregateReturnValue.getDealGroupDTO() 返回空
     */
    @Test
    public void testMapResultDealGroupDTOIsNull() throws Throwable {
        // arrange
        ProductCustomerFetcher fetcher = new ProductCustomerFetcher();
        FetcherResponse<QueryCenterAggregateReturnValue> aggregateResult = mock(FetcherResponse.class);
        QueryCenterAggregateReturnValue returnValue = mock(QueryCenterAggregateReturnValue.class);
        when(aggregateResult.getReturnValue()).thenReturn(returnValue);
        when(returnValue.getDealGroupDTO()).thenReturn(null);
        // act
        FetcherResponse<ProductCustomer> result = fetcher.mapResult(aggregateResult);
        // assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNull(result.getReturnValue());
    }

    /**
     * 测试边界情况：DealGroupDTO.getCustomer() 返回空
     */
    @Test
    public void testMapResultCustomerIsNull() throws Throwable {
        // arrange
        ProductCustomerFetcher fetcher = new ProductCustomerFetcher();
        FetcherResponse<QueryCenterAggregateReturnValue> aggregateResult = mock(FetcherResponse.class);
        QueryCenterAggregateReturnValue returnValue = mock(QueryCenterAggregateReturnValue.class);
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        when(aggregateResult.getReturnValue()).thenReturn(returnValue);
        when(returnValue.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCustomer()).thenReturn(null);
        // act
        FetcherResponse<ProductCustomer> result = fetcher.mapResult(aggregateResult);
        // assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNull(result.getReturnValue());
    }

    /**
     * 测试边界情况：DealGroupCustomerDTO.getOriginCustomerId() 和 getPlatformCustomerId() 返回空
     */
    @Test
    public void testMapResultCustomerIdsAreNull() throws Throwable {
        // arrange
        ProductCustomerFetcher fetcher = new ProductCustomerFetcher();
        FetcherResponse<QueryCenterAggregateReturnValue> aggregateResult = mock(FetcherResponse.class);
        QueryCenterAggregateReturnValue returnValue = mock(QueryCenterAggregateReturnValue.class);
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        DealGroupCustomerDTO customerDTO = mock(DealGroupCustomerDTO.class);
        when(aggregateResult.getReturnValue()).thenReturn(returnValue);
        when(returnValue.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getCustomer()).thenReturn(customerDTO);
        when(customerDTO.getOriginCustomerId()).thenReturn(null);
        when(customerDTO.getPlatformCustomerId()).thenReturn(null);
        // act
        FetcherResponse<ProductCustomer> result = fetcher.mapResult(aggregateResult);
        // assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(0L, result.getReturnValue().getOriginCustomerId());
        assertEquals(0L, result.getReturnValue().getPlatformCustomerId());
    }

    /**
     * 测试 fulfillRequest 方法的正常场景
     * 验证是否正确调用了 requestBuilder.customer() 方法
     */
    @Test
    public void testFulfillRequestNormalCase() throws Throwable {
        // arrange
        QueryByDealGroupIdRequestBuilder requestBuilder = Mockito.mock(QueryByDealGroupIdRequestBuilder.class);
        // act
        productCustomerFetcher.fulfillRequest(requestBuilder);
        // assert
        verify(requestBuilder).customer(any(DealGroupCustomerBuilder.class));
    }

    /**
     * 测试 fulfillRequest 方法中 requestBuilder 为空的异常场景
     * 验证是否抛出 NullPointerException
     */
    @Test
    public void testFulfillRequestNullRequestBuilder() throws Throwable {
        // act & assert
        assertThrows(NullPointerException.class, () -> productCustomerFetcher.fulfillRequest(null));
    }

    /**
     * 测试 fulfillRequest 方法中调用 customer() 方法的异常场景
     * 验证当 requestBuilder.customer() 抛出异常时的行为
     */
    @Test
    public void testFulfillRequestNullCustomerBuilder() throws Throwable {
        // arrange
        QueryByDealGroupIdRequestBuilder requestBuilder = Mockito.mock(QueryByDealGroupIdRequestBuilder.class);
        Mockito.doThrow(new NullPointerException()).when(requestBuilder).customer(any(DealGroupCustomerBuilder.class));
        // act & assert
        assertThrows(NullPointerException.class, () -> productCustomerFetcher.fulfillRequest(requestBuilder));
    }
}
