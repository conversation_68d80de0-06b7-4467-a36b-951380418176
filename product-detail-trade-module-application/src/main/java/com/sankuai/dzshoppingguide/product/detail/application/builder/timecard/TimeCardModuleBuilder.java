package com.sankuai.dzshoppingguide.product.detail.application.builder.timecard;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.extend.BaseBuilder;
import com.sankuai.dz.product.detail.gateway.spi.enums.ProductTypeEnum;
import com.sankuai.dzcard.navigation.api.dto.CardHoldStatusDTO;
import com.sankuai.dzshoppingguide.product.detail.application.builder.timecard.list.ProductListVP;
import com.sankuai.dzshoppingguide.product.detail.application.constants.PromoTagTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.abtest.AbTestFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.abtest.AbTestReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.category.ProductCategoryFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.shopid.ShopIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.shopid.ShopIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.creditpay.ExposeResult;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.creditpay.UserAndProductCreditPayFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommendgoods.dtos.ProductM;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommendgoods.dtos.ProductPromoPriceM;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.reserve.CardHoldStatusFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.reserve.CardHoldStatusResult;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.FetcherDAGStarter;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.timesdeal.TimesDealThemeFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.timesdeal.dto.CardM;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.timesdeal.dto.DealDetailBuildContext;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.timesdeal.dto.TimesDealTheme;
import com.sankuai.dzshoppingguide.product.detail.application.utils.CardPromoUtils;
import com.sankuai.dzshoppingguide.product.detail.application.utils.PriceDisplayUtils;
import com.sankuai.dzshoppingguide.product.detail.application.utils.TimesDealUtil;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.timecard.vo.TimeCardItemVO;
import com.sankuai.dzshoppingguide.product.detail.spi.timecard.vo.TimeCardSelectVO;
import com.sankuai.dztheme.deal.res.enums.PromoTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Builder(
        moduleKey = ModuleKeyConstants.MODULE_DETAIL_DEAL_TIME_CARD,
        startFetcher = FetcherDAGStarter.class,
        dependentFetchers = {
                TimesDealThemeFetcher.class,
                ProductCategoryFetcher.class,
                ShopIdMapperFetcher.class,
                AbTestFetcher.class,
                UserAndProductCreditPayFetcher.class,
                CardHoldStatusFetcher.class
        }
)
@Slf4j
public class TimeCardModuleBuilder extends BaseBuilder<TimeCardSelectVO> {
    
    @Autowired
    private List<ProductListVP> productListVPs;

    @Override
    public TimeCardSelectVO doBuild() {
        if (request.getProductTypeEnum() != ProductTypeEnum.DEAL) {
            return null;
        }

        List<String> antiRefreshModuleList = TimesDealUtil.getAntiRefreshModuleList(request);
        if (CollectionUtils.isNotEmpty(antiRefreshModuleList) && antiRefreshModuleList.contains(ModuleKeyConstants.MODULE_DETAIL_DEAL_TIME_CARD)) {
            return null;
        }

        ProductCategory productCategory = getDependencyResult(ProductCategoryFetcher.class);
        ShopIdMapper shopIdMapper = getDependencyResult(ShopIdMapperFetcher.class);
        AbTestReturnValue abTestReturnValue = getDependencyResult(AbTestFetcher.class);
        ExposeResult exposeResult = getDependencyResult(UserAndProductCreditPayFetcher.class);
        CardHoldStatusResult cardHoldStatusResult = getDependencyResult(CardHoldStatusFetcher.class);
        List<ProductM> productMS = getDependencyResult(TimesDealThemeFetcher.class, TimesDealTheme.class).map(TimesDealTheme::getProductMS).orElse(Lists.newArrayList());
        if (CollectionUtils.isEmpty(productMS)) {
            return null;
        }


        DealDetailBuildContext buildContext = DealDetailBuildContext.builder()
                .productCategory(productCategory)
                .shopIdMapper(shopIdMapper)
                .request(request)
                .abTestReturnValue(abTestReturnValue)
                .exposeResult(exposeResult)
                .cardHoldStatusResult(cardHoldStatusResult)
                .cardM(buildCardM(cardHoldStatusResult))
                .build();

        // 获取到手价，方便后续的排序、取值
        PriceDisplayUtils.batchSetSalePrice(productMS,buildContext);
        ProductListVP productListVP = productListVPs.stream().filter(Objects::nonNull).filter(item -> item.isHit(productCategory)).findFirst().orElse(null);
        if (productListVP == null) {
            return null;
        }

        List<ProductM> filteredProductMs = productListVP.compute(productMS,buildContext);
        // 获取次数最少的团购次卡,并打标记,方便后面构建团购次卡标题中的营销标签
        TimesDealUtil.setPricePromoTagFlag(filteredProductMs, buildContext);

        List<TimeCardItemVO> cardList = filteredProductMs.stream()
                .filter(Objects::nonNull)
                .map(productM -> buildDzProductVO(productM,buildContext))
                .collect(Collectors.toList());

        return new TimeCardSelectVO(cardList);
    }

    private CardM buildCardM(CardHoldStatusResult cardHoldStatusResult) {
        List<Integer> shopHasCard = Optional.ofNullable(cardHoldStatusResult).map(CardHoldStatusResult::getCardHoldStatusDTO).map(CardHoldStatusDTO::getShopHasCardTypeList).orElse(Lists.newArrayList());
        List<Integer> userHasCard = Optional.ofNullable(cardHoldStatusResult).map(CardHoldStatusResult::getCardHoldStatusDTO).map(CardHoldStatusDTO::getUserHoldCardTypeList).orElse(Lists.newArrayList());
        CardM cardM = new CardM();
        cardM.setShopCardList(shopHasCard);
        cardM.setUserCardList(userHasCard);
        return cardM;
    }


    private TimeCardItemVO buildDzProductVO(ProductM productM,DealDetailBuildContext buildContext) {
        TimeCardItemVO dzProductVO = new TimeCardItemVO();
        try {
            // CardM cardM = Optional.ofNullable(buildContext).map(DealDetailBuildContext::getCardM).orElse(null);
            dzProductVO.setProductId(productM.getProductId());
            dzProductVO.setJumpUrl(productM.getJumpUrl());
            dzProductVO.setSymbol("¥");

            // 非团购次卡,即次卡关联的团购本身
            if (!productM.isTimesDeal()) {
                String singlePrice = Optional.ofNullable(productM.getSalePrice()).map(BigDecimal::stripTrailingZeros)
                        .map(BigDecimal::toPlainString).orElse(StringUtils.EMPTY);
                dzProductVO.setTitle("单次服务");
                dzProductVO.setFinalPrice(singlePrice);
            } else {
                String times = productM.getAttr(TimesDealUtil.SYS_MULTI_SALE_NUMBER);
                String singlePrice = TimesDealUtil.getSinglePrice(productM.getSalePrice(), times);
                String salePrice = Optional.ofNullable(productM.getSalePrice()).map(BigDecimal::stripTrailingZeros)
                        .map(BigDecimal::toPlainString).orElse(StringUtils.EMPTY);
                dzProductVO.setPriceTag(PromoTagTypeEnum.getDescByCode(productM.getPromoTagType()));
                dzProductVO.setTitle(String.format("%s次卡", times));
                // 如果迁移素质教育行业的时候,这个地方的单位是 /节
                dzProductVO.setUnit("/次");
                dzProductVO.setPerPrice(singlePrice);
                dzProductVO.setFinalPrice(salePrice);
                dzProductVO.setText("到手");

            }

        } catch (Exception e) {
            log.error("buildDzProductVO error,productM:{}",JsonCodec.encode(productM),e);
        }
        return dzProductVO;
    }


    private String itemComponentSalePrice(ProductM productM, CardM cardM) {
        try {
            if (productM.getSalePrice() == null) {
                return null;
            }
            String price = getSalePrice(productM.getSalePrice());
            
            if (StringUtils.isNotBlank(price)) {
                return price;
            }
            return itemComponentDefaultSalePrice(productM, cardM);
        } catch (Exception e) {
            log.error("itemComponentSalePrice error",e);
            return StringUtils.EMPTY;
        }
    }

    private static final String PRE_PRICE = "¥";
    private String getSalePrice(BigDecimal salePrice) {
        return PRE_PRICE + salePrice.stripTrailingZeros().toPlainString();
    }

    /**
     * 商品售价默认逻辑
     * 0、取卡价（当前没有）
     * 1、取玩美季价
     * 2、取立减价
     * 3、取默认售价
     *
     * @param productM
     * @param cardM
     * @return
     */
    private String itemComponentDefaultSalePrice(ProductM productM, CardM cardM) {
        try {
            //卡
            ProductPromoPriceM cardPromo = CardPromoUtils.getFirstUserHoldCardPromo(productM.getPromoPrices(), cardM);
            if (cardPromo != null && StringUtils.isNotEmpty(cardPromo.getPromoPriceTag())) {
                //有卡优惠 且 卡优惠没有倒挂，才展示卡价
                if (!CardPromoUtils.isCardDaoGuaPromo(productM.getPromoPrices())) {
                    return cardPromo.getPromoPriceTag();
                }
            }
            //立减
            ProductPromoPriceM productPromoPriceM = productM.getPromo(PromoTypeEnum.DIRECT_PROMO.getType());
            if (productPromoPriceM != null && StringUtils.isNotEmpty(productPromoPriceM.getPromoPriceTag())) {
                return productPromoPriceM.getPromoPriceTag();
            }
        } catch (Exception e) {
            log.error("itemComponentDefaultSalePrice error,productM:{}", JsonCodec.encode(productM), e);
        }
        //团购
        return productM.getBasePriceTag();
    }

}
