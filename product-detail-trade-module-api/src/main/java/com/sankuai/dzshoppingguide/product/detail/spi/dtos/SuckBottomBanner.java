package com.sankuai.dzshoppingguide.product.detail.spi.dtos;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;


import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-03-28
 */
@Data
@MobileDo(id = 0x53ef)
@TypeDoc(description = "吸底条")
public class SuckBottomBanner implements Serializable {

    @FieldDoc(description = "icon")
    @MobileField(key = 0x3408)
    private String paybackIcon;

    @FieldDoc(description = "去膨胀")
    @MobileField(key = 0xe221)
    private String buttonText;

    @FieldDoc(description = "引导膨胀/购买券文案")
    @MobileField(key = 0x451b)
    private List<TextDisplayInfo> text;
}