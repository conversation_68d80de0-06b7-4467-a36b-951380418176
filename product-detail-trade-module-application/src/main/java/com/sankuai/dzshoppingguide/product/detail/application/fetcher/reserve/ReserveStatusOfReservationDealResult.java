package com.sankuai.dzshoppingguide.product.detail.application.fetcher.reserve;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author: guangyujie
 * @Date: 2025/3/20 11:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ReserveStatusOfReservationDealResult extends FetcherReturnValueDTO {

    private final boolean canReserve;

    public ReserveStatusOfReservationDealResult(boolean canReserve) {
        this.canReserve = canReserve;
    }

}
