package com.sankuai.dzshoppingguide.product.detail.spi.promo.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025-05-20
 * @desc
 */
@Data
@MobileDo(id = 0xb934)
public class NationalSubsidyInfo implements Serializable {
    /**
     * 领取状态 0未领取，1已领取
     */
    @MobileField(key = 0xc851)
    private int issueStatus;

    /**
     * 按钮跳链
     */
    @MobileField(key = 0x774e)
    private String jumpUrl;

    /**
     * 按钮图片
     */
    @MobileField(key = 0x6c48)
    private String buttonPic;

    /**
     * 国补优惠比例
     */
    @MobileField(key = 0xcf50)
    private String promo;

    /**
     * 副标题
     */
    @MobileField(key = 0xd894)
    private String subTitle;

    /**
     * 标题
     */
    @MobileField(key = 0x24cc)
    private String title;

    /**
     * 角标
     */
    @MobileField(key = 0x3c48)
    private String icon;
}