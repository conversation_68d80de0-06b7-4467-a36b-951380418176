package com.sankuai.dzshoppingguide.product.detail.application.builder.promodetail.enums;

import lombok.Getter;

@Getter
public enum CouponStatusEnum {
    // 未领取
    NOT_ASSIGN(0, "未领取"),
    // 已领取
    ASSIGNED(1, "已领取"),
    // 跳转链接
    JUMP_URL(2, "跳转链接"),
    // 倒计时
    COUNTDOWN(3, "倒计时"),
    // 不展示
    NOT_SHOW(4, "不展示")
    ;

    final int code;
    final String desc;

    CouponStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
