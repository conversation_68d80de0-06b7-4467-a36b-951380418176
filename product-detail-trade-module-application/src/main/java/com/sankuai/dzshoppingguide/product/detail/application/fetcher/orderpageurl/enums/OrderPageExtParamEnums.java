package com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.enums;

import lombok.Getter;

/**
 * @Author: guangyujie
 * @Date: 2025/4/7 11:14
 */
@Getter
public enum OrderPageExtParamEnums {

    trafficflag("优惠渠道，营销定义"),
    eventpromochannel("优惠渠道，营销定义"),
    promotionchannel("优惠渠道，营销定义"),

    otherpromptinfo(""),
    isMagicalPrice("使用方是LE预约那边，背景是当时神会员黑选单需求，需要预约页如果前级团详是神券算价，他们需要屏蔽神券门店，预约不涉及查价产品仅要求在神券场景需要屏蔽，当时argue很久，最后没办法由我们前级传递这个算价相关的标识"),
    lyyuserid(""),
    dealextparam(""),
    source("流量渠道（注意值需与trafficflag保持一致，否则组合品下单会被营销卡控）"),

    pass_param("订单归因，交易侧定义"),

    mmcinflate(""),
    mmcuse(""),
    mmcbuy(""),
    mmcfree(""),

    offlinecode("优惠码,线下码"),

    pricecipher("价格一致性加密字段"),

    @Deprecated
    trafficFlag("设置流量标识参数（该标识已废弃，仅用于LE预约场景，提单实际使用trafficflag作为流量标识传递）"),

    fromsource("流量渠道（for一品多态）")
    ;

    private final String desc;

    OrderPageExtParamEnums(String desc) {
        this.desc = desc;
    }

}