package com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.extend.buy;

import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.BottomBarActionDataVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.enums.BottomBarActionTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.enums.OpenTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2025/06/12
 * @description 吸底去购买
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SuckBottomBuyActionVO extends BottomBarActionDataVO{

    /**
     * 动作类型
     */
    private final int actionType = BottomBarActionTypeEnum.SUCK_BOTTOM_BUY.getCode();

    /**
     * 打开类型
     * redirect=直接跳转
     * modal=打开浮层
     *
     * @see OpenTypeEnum
     */
    private String openType;

    /**
     * 跳转链接
     */
    private String url;

    /**
     * 按钮文案
     */
    private String text;

    /**
     * 图标
     */
    private String icon;

    public SuckBottomBuyActionVO(OpenTypeEnum openType,
                                      String url,
                                      String text,
                                      String icon) {
        this.openType = openType.name();
        this.url = url;
        this.text = text;
        this.icon = icon;
    }

}
