package com.sankuai.dzshoppingguide.product.detail.spi.bookingtime.vo;

import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class BookingTimeCardVO implements Serializable {

    /**
     * 是否可点击
     * true-可点击 false-不可点击
     */
    @MobileField
    private boolean clickable;

    /**
     * 跳链
     */
    @MobileField
    private String clickUrl;

    /**
     * 展示信息点
     */
    @MobileField
    private List<BookingTimeItemVO> bookingInfo;

}