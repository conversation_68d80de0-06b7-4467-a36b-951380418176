package com.sankuai.dzshoppingguide.product.detail.application.fetcher.membercard.card.discount;


import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import com.sankuai.mpmctmember.query.common.enums.MemberChargeTypeEnum;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MemberCardDiscountReturnValue extends FetcherReturnValueDTO {
    /**
     * 会员卡类型
     */
    private MemberChargeTypeEnum memberCardTypeEnum;

    /**
     * 优惠金额
     */
    private String memberCardAmount;

    /**
     * 会员卡折扣
     */
    private String memberCardDiscount;

    /**
     * 使用规则
     */
    private String useRule;

    /**
     * 开卡费
     */
    private String openCardFee;

    /**
     * 是否是会员
     */
    private boolean isMember;

    /**
     * 有效期
     */
    private String validPeriod;

    /**
     * 会员卡落地页
     */
    private String memberCardUrl;
}
