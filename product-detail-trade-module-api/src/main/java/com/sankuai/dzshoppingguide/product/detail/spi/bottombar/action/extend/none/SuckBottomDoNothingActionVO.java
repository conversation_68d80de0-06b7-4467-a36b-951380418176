package com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.extend.none;

import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.BottomBarActionDataVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.enums.BottomBarActionTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2025/06/12
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SuckBottomDoNothingActionVO extends BottomBarActionDataVO {

    /**
     * 动作类型
     */
    private final int actionType = BottomBarActionTypeEnum.SUCK_BOTTOM_NOTHING.getCode();

    /**
     * 图标
     */
    private String icon;


}
