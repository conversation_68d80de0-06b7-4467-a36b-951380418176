package com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.component.trade.block.utils;

import com.google.common.collect.Lists;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sale.info.enums.ProductSaleStatusEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.BottomBarActionDataVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.component.trade.button.OnlyIconTradeButtonVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.component.trade.button.StandardTradeButtonVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.backgroud.BottomBarBackgroundVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.constant.ButtonStyle;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.rich.content.PicRichContentVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.rich.content.TextRichContentVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.rich.content.enums.TextStyleEnum;
import org.apache.commons.lang3.StringUtils;

import java.util.Optional;

/**
 * @Author: guangyujie
 * @Date: 2025/4/2 20:20
 */
public class TradeButtonBuildUtils {

    public static StandardTradeButtonVO buildTradeButtonVO(final ProductSaleStatusEnum saleStatus,
                                                           final String mainTitle,
                                                           final ButtonStyle buttonStyle,
                                                           final BottomBarActionDataVO actionData) {
        return buildTradeButtonVO(saleStatus, mainTitle, null, buttonStyle, actionData);
    }

    public static StandardTradeButtonVO buildTradeButtonVO(final ProductSaleStatusEnum saleStatus,
                                                           final String mainTitle,
                                                           final String subTitle,
                                                           final ButtonStyle buttonStyle,
                                                           final BottomBarActionDataVO actionData) {
        StandardTradeButtonVO standardTradeButtonVO = new StandardTradeButtonVO();
        standardTradeButtonVO.setDisable(!saleStatus.isForSale());
        standardTradeButtonVO.setMainTitle(Lists.newArrayList(new TextRichContentVO(
                Optional.ofNullable(mainTitle).orElse("立即抢购"),
                TextStyleEnum.Bold, 30, buttonStyle.getTitleColor()
        )));
        String finalSubTitle = saleStatus.isForSale() ? subTitle : saleStatus.getSubTitle();
        if (StringUtils.isNotBlank(finalSubTitle)) {
            standardTradeButtonVO.setSubTitle(Lists.newArrayList(new TextRichContentVO(
                    finalSubTitle, TextStyleEnum.Default, 24, buttonStyle.getTitleColor()
            )));
        }
        standardTradeButtonVO.setBackground(BottomBarBackgroundVO.buildLevelGradientColor(buttonStyle.getBackGroundColors()));
        standardTradeButtonVO.setActionData(actionData);
        return standardTradeButtonVO;
    }

    public static OnlyIconTradeButtonVO buildOnlyIconTradeButtonVO(final ProductSaleStatusEnum saleStatus,
                                                                   final PicRichContentVO icon,
                                                                   final ButtonStyle buttonStyle,
                                                                   final BottomBarActionDataVO actionData) {
        OnlyIconTradeButtonVO button = new OnlyIconTradeButtonVO();
        button.setDisable(false);
        button.setMainTitle(Lists.newArrayList(icon));
        button.setBackground(BottomBarBackgroundVO.buildLevelGradientColor(buttonStyle.getBackGroundColors()));
        button.setActionData(actionData);
        return button;
    }

}
