package com.sankuai.dzshoppingguide.product.detail.application.utils;


import com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommendgoods.dtos.AttrM;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/11/25 9:21 下午
 */
public class DealDetailUtils {

    public static String getAttrSingleValueByAttrName(List<AttrM> dealAttrs, String name) {
        if (CollectionUtils.isEmpty(dealAttrs) || StringUtils.isEmpty(name)) {
            return null;
        }
        AttrM attrM = dealAttrs.stream().filter(attr -> name.equals(attr.getName())).findFirst().orElse(null);
        if (attrM == null) {
            return null;
        }
        return attrM.getValue();
    }


}
