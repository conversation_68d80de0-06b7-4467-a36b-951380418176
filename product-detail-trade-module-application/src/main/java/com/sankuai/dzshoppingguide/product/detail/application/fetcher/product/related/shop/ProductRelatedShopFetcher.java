package com.sankuai.dzshoppingguide.product.detail.application.fetcher.product.related.shop;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Sets;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.FetcherDAGStarter;
import com.sankuai.dzshoppingguide.product.detail.domain.query.center.QueryCenterAclService;
import com.sankuai.general.product.query.center.client.builder.model.DealGroupDisplayShopBuilder;
import com.sankuai.general.product.query.center.client.builder.model.DealGroupShopRelationCheckBuilder;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResponse;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResult;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * @Author: guangyujie
 * @Date: 2025/2/17 17:20
 */
@Fetcher(
        previousLayerDependencies = {FetcherDAGStarter.class}
)
@Slf4j
public class ProductRelatedShopFetcher extends NormalFetcherContext<ProductRelatedShop> {

    @Resource
    public QueryCenterAclService queryCenterAclService;

    @Override
    protected CompletableFuture<ProductRelatedShop> doFetch() {
        QueryByDealGroupIdRequest baseRequest = null;
        try {
            QueryByDealGroupIdRequestBuilder baseRequestBuilder = QueryCenterAclService.getBaseRequestBuilder(this.request);
            baseRequestBuilder.displayShop(DealGroupDisplayShopBuilder.builder().all());
            baseRequestBuilder.checkDealGroupShopRelation(DealGroupShopRelationCheckBuilder.builder().shopIdType(QueryCenterAclService.getIdTypeEnum(request).getCode()).checkVerifyShop(getVerifyShopMap()));
            baseRequest = baseRequestBuilder.build();
            CompletableFuture<QueryDealGroupListResponse> queryCenterFuture = queryCenterAclService.query(baseRequest);
            return queryCenterFuture.thenApply(response -> {
                if (response == null) {
                    throw new ProductRelatedShopException("查询中心response为null");
                }
                DealGroupDTO dealGroupDTO = Optional.of(response)
                        .map(QueryDealGroupListResponse::getData)
                        .map(QueryDealGroupListResult::getList)
                        .orElse(new ArrayList<>()).stream()
                        .findFirst().orElse(null);
                if (dealGroupDTO == null || dealGroupDTO.getDisplayShopInfo() == null) {
                    throw new ProductRelatedShopException("查询中心未返回商品适用门店数据");
                }
                return new ProductRelatedShop(
                        dealGroupDTO.getDisplayShopInfo().getDpDisplayShopIds(),
                        dealGroupDTO.getDisplayShopInfo().getMtDisplayShopIds(),
                        dealGroupDTO.getVerifyShopCheckResult()
                );
            });
        } catch (TException e) {
            log.error("查询中心创建future失败，会导致商品适用门店查不到数据，request={}", JsonCodec.encodeWithUTF8(baseRequest), e);
            throw new ProductRelatedShopException(e);
        }
    }

    private Map<Long, Set<Long>> getVerifyShopMap() {
        Map<Long, Set<Long>> map = new HashMap<>();
        map.put(this.request.getProductId(), Sets.newHashSet(this.request.getPoiId()));
        return map;
    }

}
