package com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.rich.content;

import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.rich.content.enums.RichContentTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * @Author: guangyujie
 * @Date: 2025/3/16 15:15
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class PicRichContentVO extends RichContentVO {

    /**
     * icon宽度
     */
    private int iconWidth;

    /**
     * icon高度
     */
    private int iconHeight;

    /**
     * icon图片url
     */
    private String iconUrl;

    public PicRichContentVO(int iconWidth, int iconHeight, String iconUrl) {
        this.iconWidth = iconWidth / 2;
        this.iconHeight = iconHeight / 2;
        this.iconUrl = iconUrl;
    }

    @Override
    public int getType() {
        return RichContentTypeEnum.PIC.getCode();
    }
}
