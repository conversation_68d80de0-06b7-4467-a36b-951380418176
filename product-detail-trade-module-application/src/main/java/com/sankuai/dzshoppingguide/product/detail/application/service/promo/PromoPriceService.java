package com.sankuai.dzshoppingguide.product.detail.application.service.promo;

import com.dianping.cat.Cat;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.LeadsInfoFetcher.LeadsInfoResult;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.cepinpool.dto.CostEffectivePinTuan;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.dto.PriceReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.utils.*;
import com.sankuai.dzshoppingguide.product.detail.application.utils.context.DealCtxUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.promo.FreeDealConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * <AUTHOR>
 * @create 2025/3/3 19:37
 */
@Service
@Slf4j
public class PromoPriceService {

    public PriceReturnValue buildPrice(ProductDetailPageRequest request,
                                       CostEffectivePinTuan costEffectivePinTuan,
                                       ProductBaseInfo dealGroupBase,
                                       PriceDisplayDTO dealPromoPrice,
                                       PriceDisplayDTO normalPrice,
                                       PriceDisplayDTO costEffectivePrice,
                                       ClientTypeEnum clientTypeEnum,
                                       LeadsInfoResult leadsInfoResult){
        PriceReturnValue priceReturnValue = buildPromoDetailInfo( request,
                 dealGroupBase,
                 dealPromoPrice,
                 normalPrice);
        buildDealPromoDetailInfo(dealGroupBase,
                 costEffectivePinTuan,
                 clientTypeEnum,
                 dealPromoPrice,
                 priceReturnValue,
                 costEffectivePrice,
                leadsInfoResult
        );
        return priceReturnValue;
    }

    public PriceReturnValue buildPromoDetailInfo(ProductDetailPageRequest request,
                                                 ProductBaseInfo dealGroupBase,
                                                 PriceDisplayDTO dealPromoPrice,
                                                 PriceDisplayDTO normalPrice) {
        PriceReturnValue promoDetailModule = null;
        try {
            //0元预约场景专用
            if (DealCtxUtils.isFreeDeal(dealGroupBase)){
                buildFreeDealPromoDetailInfo(dealGroupBase, dealPromoPrice, promoDetailModule);
            }else if (DealCtxHelper.isOdpSource(request.getPageSource())) {
                promoDetailModule = buildOdpPromoDetailInfo(dealGroupBase, normalPrice);
            } else {
                promoDetailModule = buildNormalPromoDetailInfo(request.getClientTypeEnum(),dealGroupBase, normalPrice, request.getPageSource());
            }
            return promoDetailModule;
        } catch (Exception e) {
            log.error("buildPromoDetailInfo error", e);
            return promoDetailModule;
        }
    }

    public PriceReturnValue buildNormalPromoDetailInfo(ClientTypeEnum clientTypeEnum,
                                                       ProductBaseInfo dealGroupBase,
                                                       PriceDisplayDTO normalPrice,
                                                       String requestSource) {
        if (normalPrice == null || Objects.isNull(dealGroupBase) || Objects.isNull(dealGroupBase.getPrice())) {
            return null;
        }
        // 适配第三方平台 开店宝侧、阿波罗侧 + 特团覆盖小程序 + 小程序升级新团详
        if (DealCtxUtils.isExternal(clientTypeEnum) && dealGroupBase.getCategory().getCategoryId() != 712) {
            if (!DealCtxUtils.isThirdPArt(clientTypeEnum) && DealCtxUtils.notMiniProgramCostEffective(clientTypeEnum, requestSource)) {
                return null;
            }
        }

        PriceReturnValue promoDetailModule = new PriceReturnValue();
        BigDecimal marketPrice = new BigDecimal(dealGroupBase.getPrice().getMarketPrice());
        //没有商家优惠时全网低价就是团单价
        String marketPriceStr = PriceHelper.formatPrice(marketPrice);
        promoDetailModule.setMarketPrice(marketPriceStr);
        BigDecimal promoPrice = normalPrice.getPrice();
        promoDetailModule.setPromoPrice(PriceHelper.formatPrice(promoPrice));

        return promoDetailModule;
    }

    public void buildDealPromoDetailInfo(ProductBaseInfo dealGroupBase,
                                         CostEffectivePinTuan costEffectivePinTuan,
                                         ClientTypeEnum clientTypeEnum,
                                         PriceDisplayDTO dealPromoPrice,
                                         PriceReturnValue promoDetailModule,
                                         PriceDisplayDTO costEffectivePrice,
                                         LeadsInfoResult leadsInfoResult) {
        try {
            if (DealCtxHelper.isMtLiveMinApp(clientTypeEnum)) {
                // 私域直播价格无优惠
                return;
            }
            if ((dealPromoPrice == null || CollectionUtils.isEmpty(dealPromoPrice.getUsedPromos()))) {
                return;
            }
            //0元预约-不展示优惠信息
            if (DealCtxUtils.isFreeDeal(dealGroupBase)) {
                return;
            }
            // 留资型团单页面且（超值特惠套餐或小程序）不展示优惠信息
            if (DealUtils.isLeadsDeal(dealGroupBase) && (DealCtxUtils.isHitSpecialValueDeal(leadsInfoResult) || !(DealCtxUtils.judgeMainApp(clientTypeEnum)))) {
                return;
            }

            if (promoDetailModule == null) {
                promoDetailModule = new PriceReturnValue();
            }
            promoDetailModule.setFinalPrice(dealPromoPrice.getPrice() == null ? "0.01" : PriceHelper.dropLastZero(dealPromoPrice.getPrice()));
            promoDetailModule.setPriceDisplayDTO(dealPromoPrice);
            setMarketPromoDiscount(promoDetailModule, costEffectivePrice);
            if (isPinTuanStyle(costEffectivePinTuan)) {
                promoDetailModule.setFinalPrice(Objects.isNull(costEffectivePrice.getPrice()) ? "0.01" : PriceHelper.dropLastZero(costEffectivePrice.getPrice()));
                promoDetailModule.setPriceDisplayDTO(costEffectivePrice);
                setMarketPromoDiscount(promoDetailModule, costEffectivePrice);
            }
            //局改团详不展示优惠标签
            if (ReassuredRepairUtil.isTagPresent(dealGroupBase)) {
                //局改 团详页显示的是 门店售价
                promoDetailModule.setFinalPrice(dealPromoPrice.getMarketPrice() == null ? "0.01" : PriceHelper.dropLastZero(dealPromoPrice.getMarketPrice()));
                promoDetailModule.setPriceDisplayDTO(dealPromoPrice);
                promoDetailModule.setMarketPrice(StringUtils.EMPTY);
            }
        } catch (Exception e) {
            log.error("buildDealPromoDetailInfo error", e);
        }
    }

    // 需要展示拼团价的场景
    private boolean isPinTuanStyle(CostEffectivePinTuan costEffectivePinTuan) {
        if (CostEffectivePinTuanUtils.isCePinTuaScene(costEffectivePinTuan)) {
            return !CostEffectivePinTuanUtils.activePinTuan(costEffectivePinTuan);
        }
        return false;
    }

    /**
     * 广平分销场景优惠展示逻辑
     */
    private PriceReturnValue buildOdpPromoDetailInfo(ProductBaseInfo dealGroupBase, PriceDisplayDTO odpPrice) {
        PriceReturnValue promoDetailModule = new PriceReturnValue();
        BigDecimal dealGroupPrice = new BigDecimal(dealGroupBase.getPrice().getSalePrice());
        BigDecimal marketPrice = new BigDecimal(dealGroupBase.getPrice().getMarketPrice());
        BigDecimal promoPrice = odpPrice != null ? odpPrice.getPrice() : dealGroupPrice;

        String marketPriceStr = PriceHelper.format(marketPrice);
        String promoPriceStr = PriceHelper.format(promoPrice);

        promoDetailModule.setMarketPrice(marketPriceStr);
        promoDetailModule.setPromoPrice(promoPriceStr);
        return promoDetailModule;
    }

    public void buildFreeDealPromoDetailInfo(ProductBaseInfo dealGroupBase,
                                             PriceDisplayDTO dealPromoPrice,
                                             PriceReturnValue promoDetailModule) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.ParallDealBuilderProcessor.buildFreeDealPromoDetailInfo(DealCtx,DealGroupPBO)");
        if (promoDetailModule == null) {
            promoDetailModule = new PriceReturnValue();
        }
        // 价格信息
        BigDecimal dealGroupPrice = new BigDecimal(dealGroupBase.getPrice().getSalePrice());
        String dealGroupPriceStr = PriceHelper.format(dealGroupPrice);
        // promoDetailModule.setDealGroupPrice(dealGroupPriceStr);
        promoDetailModule.setFinalPrice(dealGroupPriceStr);
        promoDetailModule.setPriceDisplayDTO(dealPromoPrice);
        FreeDealConfig freeDealConfig = DealCtxUtils.getFreeDealConfig(dealGroupBase.getCategory().getCategoryId());
        if (freeDealConfig != null && freeDealConfig.isShowMarketPrice()) {
            BigDecimal marketPrice = new BigDecimal(dealGroupBase.getPrice().getMarketPrice());
            promoDetailModule.setMarketPrice(PriceHelper.formatPrice(marketPrice));
        }

        setMarketPromoDiscount(promoDetailModule, dealPromoPrice);
    }

    public void setMarketPromoDiscount(PriceReturnValue promoDetailModule, PriceDisplayDTO priceDisplayDTO) {
        if (priceDisplayDTO == null)
            return;
        BigDecimal marketPrice = priceDisplayDTO.getMarketPrice();
        BigDecimal finalPrice = priceDisplayDTO.getPrice();
        if (marketPrice == null || finalPrice == null) {
            return;
        }
        BigDecimal discountRate = PriceHelper.calcDiscountRateBigDecimal(marketPrice, finalPrice);

        String discountRateStr;
        if (discountRate == null || discountRate.compareTo(new BigDecimal("9.9")) > 0) {
            discountRateStr = "";
        } else if (discountRate.compareTo(new BigDecimal("0.1")) <= 0) {
            discountRateStr = "0.1折";
        } else {
            discountRateStr = discountRate + "折";
        }
        // marketPromoDiscount该字段在特殊行业(例如安心学),会在折扣后面追加特殊的内容,例如: 7.2折｜131.1/节
        promoDetailModule.setMarketPromoDiscount(discountRateStr);
    }
}
