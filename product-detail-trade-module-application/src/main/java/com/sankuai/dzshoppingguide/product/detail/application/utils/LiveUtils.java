package com.sankuai.dzshoppingguide.product.detail.application.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import com.sankuai.dealuser.price.display.api.enums.ConstantsSourceEnum;
import com.sankuai.dealuser.price.display.api.enums.ExtensionKeyEnum;
import com.sankuai.dz.product.detail.RequestSourceEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.net.URLDecoder;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025-03-10
 * @desc 直播工具类
 */
@Slf4j
public class LiveUtils {
    public static Map<String, String> getLiveSecKillParams(String passParam, String requestSource) {
        if (!RequestSourceEnum.fromLive(requestSource) || StringUtils.isBlank(passParam)) {
            return null;
        }
        String decodedPassParam;
        try {
            decodedPassParam = URLDecoder.decode(passParam, "UTF-8");
        } catch (Exception e) {
            log.error("decode pass params from mlive fail", e);
            return null;
        }
        String promoteChannelInfo = getStringFromJson(decodedPassParam, "PROMOTE_CHANNEL_INFO");
        if (StringUtils.isBlank(promoteChannelInfo)) {
            return null;
        }
        String promoteExtend = getStringFromJson(promoteChannelInfo, "promoteExtend");
        if (StringUtils.isBlank(promoteExtend)) {
            return null;
        }
        String mLiveId = getStringFromJson(promoteExtend, "mLiveId");
        String influencerId = getStringFromJson(promoteExtend, "influencerId");
        String seckillId = getStringFromJson(promoteExtend, "seckillId");
        Map<String, String> params = Maps.newHashMap();
        if (StringUtils.isNotBlank(mLiveId)) {
            params.put(ExtensionKeyEnum.Source.getDesc(), ConstantsSourceEnum.M_LIVE.getCode());
            // 直播场次ID
            params.put(ExtensionKeyEnum.LiveStreamingSceneId.getDesc(), mLiveId);
        }
        if (StringUtils.isNotBlank(influencerId)) {
            // 主播ID
            params.put(ExtensionKeyEnum.AnchorId.getDesc(), influencerId);
        }
        if (StringUtils.isNotBlank(seckillId)) {
            // 直播秒杀ID
            params.put(ExtensionKeyEnum.LiveSecKillActiveId.getDesc(), seckillId);
        }
        return  params;
    }

    private static String getStringFromJson(String json, String key) {
        if (StringUtils.isBlank(json)) {
            return StringUtils.EMPTY;
        }
        try {
            JSONObject jsonObject = JSON.parseObject(json);
            return jsonObject.getString(key);
        } catch (Exception e) {
            log.error("parse pass params from mlive fail", e);
            return StringUtils.EMPTY;
        }
    }
}
