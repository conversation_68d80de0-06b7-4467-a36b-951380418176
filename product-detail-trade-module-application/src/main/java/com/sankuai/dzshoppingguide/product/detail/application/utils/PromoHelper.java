package com.sankuai.dzshoppingguide.product.detail.application.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.dianping.pay.promo.display.api.dto.PromoDisplayDTO;
import com.google.common.collect.Lists;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.PromoIdentityEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.PromoTagEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Slf4j
public class PromoHelper {

    //神券 展示标签类型
    public static final String MAGIC_NORMAL_TAG_THEME = "MagicNormalTagTheme";
    public static final String MAGIC_VIP_TAG_THEME = "MagicVipTagTheme";
    public static final List<Integer> themeLists = Lists.newArrayList(0, 3, 5, 8);

    public static BigDecimal getPromoDisplayDTOAmount(PromoDisplayDTO promoDisplayDTO) {
        return promoDisplayDTO != null ? promoDisplayDTO.getPromoAmount() : BigDecimal.ZERO;
    }

    public static String getPromoDisplayDTODesc(PromoDisplayDTO promoDisplayDTO) {
        return promoDisplayDTO != null ? promoDisplayDTO.getTag() : null;
    }

    public static String convertPromoTag(String promoShowType, String promoTypeDesc) {
        if (PromoTagEnum.DISCOUNT_SELL.getType().equals(promoShowType)) {
            Boolean control = Lion.getBoolean(Environment.getAppName(), "com.sankuai.dzshoppingguide.detail.trademodule.medical.promo.tag.control", false);
            //医疗特殊逻辑，特惠促销展示为商家立减
            if(control && !ObjectUtils.isEmpty(promoTypeDesc)
                    && promoTypeDesc.contains(PromoTagEnum.MERCHANT_DISCOUNT.getDesc())) {
                return PromoTagEnum.MERCHANT_DISCOUNT.getDesc();
            }
        }

        return PromoTagEnum.getDescByType(promoShowType);
    }

    public static String convertSourceTag(PromoDTO promoDTO) {
        if (promoDTO == null) {
            return null;
        }
        if (promoDTO.getIdentity() == null || promoDTO.getIdentity().getPromoShowType() == null) {
            return PromoTagEnum.DEAL_PROMO.getDesc();
        }
        if (StringUtils.isNotBlank(promoDTO.getPromoIdentity())) {
            if(StringUtils.isNotBlank(PromoIdentityEnum.getDescByType(promoDTO.getPromoIdentity()))){
                return PromoIdentityEnum.getDescByType(promoDTO.getPromoIdentity());
            }
        }

        return PromoTagEnum.getDescByType(promoDTO.getIdentity().getPromoShowType());
    }

    /**
     * 从json字符串中获取指定key的值
     * @param activityText json字符串
     * @param key 需要获取的key
     * @param defaultValue 获取失败时的默认值
     * @return key对应的值或默认值
     */
    public static String getValueFromJson(String activityText, String key, String defaultValue) {
        if (StringUtils.isBlank(activityText) || StringUtils.isBlank(key)) {
            return defaultValue;
        }
        
        try {
            // 尝试解析为数组格式
            if (activityText.trim().startsWith("[")) {
                List<Map<String, Object>> list = JSON.parseObject(activityText, new TypeReference<List<Map<String, Object>>>() {});
                if (CollectionUtils.isNotEmpty(list) && list.get(0) != null) {
                    Object text = list.get(0).get(key);
                    return text != null ? text.toString() : defaultValue;
                }
            } 
            // 尝试解析为对象格式
            else if (activityText.trim().startsWith("{")) {
                Map<String, Object> map = JSON.parseObject(activityText, new TypeReference<Map<String, Object>>() {});
                if (map != null) {
                    Object text = map.get(key);
                    return text != null ? text.toString() : defaultValue;
                }
            }
        } catch (Exception e) {
            log.error("Unexpected error while parsing JSON, activityText:{}, key:{}", activityText, key, e);
        }
        return defaultValue;
    }

    public static String getTextFromJson(String activityText, String key) {
        return getValueFromJson(activityText, key, activityText);
    }

    public static String getColorFromJson(String activityText, String key) {
        return getValueFromJson(activityText, key, StringUtils.EMPTY);
    }

    public static boolean canAssign(PromoDTO couponPromo) {
        return couponPromo != null && couponPromo.isCanAssign();
    }
}
