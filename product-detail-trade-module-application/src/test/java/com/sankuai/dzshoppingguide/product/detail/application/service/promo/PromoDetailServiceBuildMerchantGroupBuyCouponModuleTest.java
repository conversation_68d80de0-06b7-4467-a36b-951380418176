package com.sankuai.dzshoppingguide.product.detail.application.service.promo;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.dianping.tpfun.product.api.sku.pintuan.dto.PinProductBrief;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import com.sankuai.dealuser.price.display.api.model.PromoIdentity;
import com.sankuai.dealuser.price.display.api.model.PromoTextDTO;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.builder.promodetail.dto.PromotionDetailParams;
import com.sankuai.dzshoppingguide.product.detail.application.builder.promodetail.enums.CouponStyleEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.promodetail.enums.CouponTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.best.shop.ShopInfo;
import com.sankuai.dzshoppingguide.product.detail.application.utils.ButtonStyleHelper;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.Icon;
import com.sankuai.dzshoppingguide.product.detail.spi.promo.vo.CouponModule;
import com.sankuai.dzshoppingguide.product.detail.spi.promo.vo.PromoDetailModule;
import java.math.BigDecimal;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

@ExtendWith(MockitoExtension.class)
public class PromoDetailServiceBuildMerchantGroupBuyCouponModuleTest {

    @InjectMocks
    private PromoDetailService promoDetailService;

    @Mock
    private ProductDetailPageRequest pageRequest;

    @Mock
    private PinProductBrief pinProductBrief;

    @Mock
    private ShopInfo shopInfo;

    private PromotionDetailParams promotionDetailParams;

    @BeforeEach
    void setUp() {
        promotionDetailParams = new PromotionDetailParams();
        promotionDetailParams.setPageRequest(pageRequest);
        promotionDetailParams.setPinProductBrief(pinProductBrief);
        promotionDetailParams.setShopInfo(shopInfo);
    }

    /**
     * Test case when pinProductBrief is null
     */
    @Test
    public void testBuildMerchantGroupBuyCouponModule_NullPinProductBrief() {
        // arrange
        promotionDetailParams.setPinProductBrief(null);
        // act & assert
        assertThrows(NullPointerException.class, () -> promoDetailService.buildMerchantGroupBuyCouponModule(promotionDetailParams));
    }

    /**
     * 测试输入为null的场景
     */
    @Test
    public void testConvertPromoTag_InputNull() throws Throwable {
        // arrange
        PromoDTO promoDTO = null;
        // act
        String result = promoDetailService.convertPromoTag(promoDTO);
        // assert
        assertNull(result);
    }

    /**
     * 测试特惠促销场景
     */
    @Test
    public void testConvertPromoTag_DiscountSell() throws Throwable {
        // arrange
        PromoIdentity identity = mock(PromoIdentity.class);
        when(identity.getPromoShowType()).thenReturn("DISCOUNT_SELL");
        when(identity.getPromoTypeDesc()).thenReturn("其他描述");
        PromoDTO promoDTO = new PromoDTO();
        promoDTO.setIdentity(identity);
        // act
        String result = promoDetailService.convertPromoTag(promoDTO);
        // assert
        assertEquals("特惠促销", result);
    }

    /**
     * 测试美团补贴场景
     */
    @Test
    public void testConvertPromoTag_MtSubsidy() throws Throwable {
        // arrange
        PromoIdentity identity = mock(PromoIdentity.class);
        when(identity.getPromoShowType()).thenReturn("MT_SUBSIDY");
        PromoDTO promoDTO = new PromoDTO();
        promoDTO.setIdentity(identity);
        // act
        String result = promoDetailService.convertPromoTag(promoDTO);
        // assert
        assertEquals("美团补贴", result);
    }

    /**
     * Test when input parameters are null
     */
    @Test
    public void testConvertPromoDescWhenInputIsNull() throws Throwable {
        // arrange
        PromoDTO promoDTO = null;
        PromoDetailModule promoDetailModule = null;
        String promoTag = null;
        // act
        String result = promoDetailService.convertPromoDesc(promoDTO, promoDetailModule, promoTag);
        // assert
        assertEquals("团购优惠", result);
    }

    /**
     * Test when promoDTO exists but identity is null
     */
    @Test
    public void testConvertPromoDescWhenIdentityIsNull() throws Throwable {
        // arrange
        PromoDTO promoDTO = mock(PromoDTO.class);
        PromoDetailModule promoDetailModule = mock(PromoDetailModule.class);
        when(promoDTO.getExtendDesc()).thenReturn("some desc");
        when(promoDTO.getIdentity()).thenReturn(null);
        // act
        String result = promoDetailService.convertPromoDesc(promoDTO, promoDetailModule, "tag");
        // assert
        assertEquals("团购优惠", result);
    }

    /**
     * Test normal promo with promoTextDTO and valid coupon type
     */
    @Test
    public void testConvertPromoDescWithPromoTextDTO() throws Throwable {
        // arrange
        PromoDTO promoDTO = mock(PromoDTO.class);
        PromoIdentity identity = mock(PromoIdentity.class);
        PromoTextDTO promoTextDTO = mock(PromoTextDTO.class);
        PromoDetailModule promoDetailModule = mock(PromoDetailModule.class);
        when(promoDTO.getExtendDesc()).thenReturn("满100减10，优惠券");
        when(promoDTO.getIdentity()).thenReturn(identity);
        when(identity.getPromoShowType()).thenReturn("NORMAL");
        when(promoDTO.getPromoTextDTO()).thenReturn(promoTextDTO);
        when(promoTextDTO.getPromoDivideType()).thenReturn("PLATFORM_COUPON");
        when(promoTextDTO.getTitle()).thenReturn("满100减10，优惠券");
        // act
        String result = promoDetailService.convertPromoDesc(promoDTO, promoDetailModule, "满100减10");
        // assert
        assertEquals("优惠券", result);
    }

    /**
     * Test magical member coupon with zero threshold
     */
    @Test
    public void testConvertPromoDescMagicalMemberWithZeroThreshold() throws Throwable {
        // arrange
        PromoDTO promoDTO = mock(PromoDTO.class);
        PromoIdentity identity = mock(PromoIdentity.class);
        PromoDetailModule promoDetailModule = mock(PromoDetailModule.class);
        when(promoDTO.getExtendDesc()).thenReturn("神券描述");
        when(promoDTO.getIdentity()).thenReturn(identity);
        when(identity.getPromoShowType()).thenReturn("MAGICAL_MEMBER_PLATFORM_COUPON");
        when(promoDTO.getMinConsumptionAmount()).thenReturn(BigDecimal.ZERO);
        when(promoDTO.getAmount()).thenReturn(new BigDecimal("10"));
        // act
        String result = promoDetailService.convertPromoDesc(promoDTO, promoDetailModule, null);
        // assert
        assertEquals("无门槛", result);
    }

    /**
     * Test magical member coupon with threshold
     */
    @Test
    public void testConvertPromoDescMagicalMemberWithThreshold() throws Throwable {
        // arrange
        PromoDTO promoDTO = mock(PromoDTO.class);
        PromoIdentity identity = mock(PromoIdentity.class);
        PromoDetailModule promoDetailModule = mock(PromoDetailModule.class);
        when(promoDTO.getExtendDesc()).thenReturn("神券描述");
        when(promoDTO.getIdentity()).thenReturn(identity);
        when(identity.getPromoShowType()).thenReturn("MAGICAL_MEMBER_PLATFORM_COUPON");
        when(promoDTO.getMinConsumptionAmount()).thenReturn(new BigDecimal("100"));
        when(promoDTO.getAmount()).thenReturn(new BigDecimal("10"));
        // act
        String result = promoDetailService.convertPromoDesc(promoDTO, promoDetailModule, null);
        // assert
        assertEquals("满100元减10元", result);
    }

    /**
     * Test exception handling
     */
    @Test
    public void testConvertPromoDescWhenExceptionOccurs() throws Throwable {
        // arrange
        PromoDTO promoDTO = mock(PromoDTO.class);
        PromoDetailModule promoDetailModule = mock(PromoDetailModule.class);
        when(promoDTO.getIdentity()).thenThrow(new RuntimeException("Test exception"));
        when(promoDTO.getExtendDesc()).thenReturn("原始描述");
        // act
        String result = promoDetailService.convertPromoDesc(promoDTO, promoDetailModule, null);
        // assert
        assertEquals("原始描述", result);
    }

    /**
     * Test when extendDesc is null in exception case
     */
    @Test
    public void testConvertPromoDescWhenExceptionAndExtendDescNull() throws Throwable {
        // arrange
        PromoDTO promoDTO = mock(PromoDTO.class);
        PromoDetailModule promoDetailModule = mock(PromoDetailModule.class);
        when(promoDTO.getExtendDesc()).thenReturn(null);
        // act
        String result = promoDetailService.convertPromoDesc(promoDTO, promoDetailModule, null);
        // assert
        assertEquals("团购优惠", result);
    }

    /**
     * Test normal promo without promoTextDTO
     */
    @Test
    public void testConvertPromoDescWithoutPromoTextDTO() throws Throwable {
        // arrange
        PromoDTO promoDTO = mock(PromoDTO.class);
        PromoIdentity identity = mock(PromoIdentity.class);
        PromoDetailModule promoDetailModule = mock(PromoDetailModule.class);
        when(promoDTO.getExtendDesc()).thenReturn("满100减10，优惠券");
        when(promoDTO.getIdentity()).thenReturn(identity);
        when(identity.getPromoShowType()).thenReturn("NORMAL");
        when(promoDTO.getPromoTextDTO()).thenReturn(null);
        // act
        String result = promoDetailService.convertPromoDesc(promoDTO, promoDetailModule, "满100减10");
        // assert
        assertEquals("优惠券", result);
    }
}
