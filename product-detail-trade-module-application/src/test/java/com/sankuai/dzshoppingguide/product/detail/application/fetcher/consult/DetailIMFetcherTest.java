package com.sankuai.dzshoppingguide.product.detail.application.fetcher.consult;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.dianping.csc.center.engine.access.dto.AccessResponseDTO;
import com.sankuai.dz.product.detail.gateway.spi.dto.ShepherdGatewayParam;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.utils.ClientTypeUtils;
import java.lang.reflect.Field;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class DetailIMFetcherTest {

    private DetailIMFetcher detailIMFetcher;

    private ProductDetailPageRequest request;

    private ShepherdGatewayParam shepherdGatewayParam;

    @BeforeEach
    public void setUp() throws Exception {
        detailIMFetcher = new DetailIMFetcher();
        request = Mockito.mock(ProductDetailPageRequest.class);
        shepherdGatewayParam = Mockito.mock(ShepherdGatewayParam.class);
        // Get the BaseFetcherContext class
        Class<?> baseFetcherContextClass = Class.forName("com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.BaseFetcherContext");
        // Get the request field from BaseFetcherContext
        Field requestField = baseFetcherContextClass.getDeclaredField("request");
        requestField.setAccessible(true);
        requestField.set(detailIMFetcher, request);
    }

    public boolean isHarmony() {
        String userAgent = request.getShepherdGatewayParam().getUserAgent();
        return ClientTypeUtils.isPureHarmony(userAgent);
    }

    public String getCsCenterUrl(AccessResponseDTO result) {
        if (result != null && result.isSuccess()) {
            return result.getUrl();
        }
        return null;
    }

    @Test
    public void testIsHarmonyUserAgentContainsOpenHarmony() throws Throwable {
        when(request.getShepherdGatewayParam()).thenReturn(shepherdGatewayParam);
        when(shepherdGatewayParam.getUserAgent()).thenReturn("Mozilla/5.0 (OpenHarmony; U; Android 10; en-us)");
        boolean result = detailIMFetcher.isHarmony();
        assertTrue(result);
    }

    @Test
    public void testIsHarmonyUserAgentDoesNotContainOpenHarmony() throws Throwable {
        when(request.getShepherdGatewayParam()).thenReturn(shepherdGatewayParam);
        when(shepherdGatewayParam.getUserAgent()).thenReturn("Mozilla/5.0 (Windows NT 10.0; Win64; x64)");
        boolean result = detailIMFetcher.isHarmony();
        assertFalse(result);
    }

    @Test
    public void testIsHarmonyUserAgentIsNull() throws Throwable {
        when(request.getShepherdGatewayParam()).thenReturn(shepherdGatewayParam);
        when(shepherdGatewayParam.getUserAgent()).thenReturn(null);
        boolean result = detailIMFetcher.isHarmony();
        assertFalse(result);
    }

    @Test
    public void testIsHarmonyUserAgentIsEmpty() throws Throwable {
        when(request.getShepherdGatewayParam()).thenReturn(shepherdGatewayParam);
        when(shepherdGatewayParam.getUserAgent()).thenReturn("");
        boolean result = detailIMFetcher.isHarmony();
        assertFalse(result);
    }

    @Test
    public void testIsHarmonyRequestIsNull() throws Throwable {
        // Get the BaseFetcherContext class
        Class<?> baseFetcherContextClass = Class.forName("com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.BaseFetcherContext");
        // Get the request field from BaseFetcherContext
        Field requestField = baseFetcherContextClass.getDeclaredField("request");
        requestField.setAccessible(true);
        requestField.set(detailIMFetcher, null);
        assertThrows(NullPointerException.class, () -> {
            detailIMFetcher.isHarmony();
        });
    }

    @Test
    public void testIsHarmonyShepherdGatewayParamIsNull() throws Throwable {
        when(request.getShepherdGatewayParam()).thenReturn(null);
        assertThrows(NullPointerException.class, () -> {
            detailIMFetcher.isHarmony();
        });
    }

    /**
     * Test scenario: result parameter is null
     * Expected: Should return null
     */
    @Test
    public void testGetCsCenterUrl_WhenResultIsNull() {
        // arrange
        DetailIMFetcher fetcher = new DetailIMFetcher();
        // act
        String url = fetcher.getCsCenterUrl(null);
        // assert
        assertNull(url);
    }

    /**
     * Test scenario: result is not null but isSuccess() returns false
     * Expected: Should return null
     */
    @Test
    public void testGetCsCenterUrl_WhenResultNotSuccessful() {
        // arrange
        DetailIMFetcher fetcher = new DetailIMFetcher();
        AccessResponseDTO result = mock(AccessResponseDTO.class);
        when(result.isSuccess()).thenReturn(false);
        // act
        String url = fetcher.getCsCenterUrl(result);
        // assert
        assertNull(url);
    }

    /**
     * Test scenario: result is not null and isSuccess() returns true
     * Expected: Should return the URL from result
     */
    @Test
    public void testGetCsCenterUrl_WhenResultSuccessful() {
        // arrange
        DetailIMFetcher fetcher = new DetailIMFetcher();
        AccessResponseDTO result = mock(AccessResponseDTO.class);
        String expectedUrl = "http://test.url";
        when(result.isSuccess()).thenReturn(true);
        when(result.getUrl()).thenReturn(expectedUrl);
        // act
        String url = fetcher.getCsCenterUrl(result);
        // assert
        assertEquals(expectedUrl, url);
    }
}
