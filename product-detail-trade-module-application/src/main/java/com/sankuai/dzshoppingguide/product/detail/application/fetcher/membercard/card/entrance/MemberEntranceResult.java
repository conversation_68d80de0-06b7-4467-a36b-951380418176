package com.sankuai.dzshoppingguide.product.detail.application.fetcher.membercard.card.entrance;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/3/13 16:32
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MemberEntranceResult extends FetcherReturnValueDTO {
    private boolean hasEntrance;
}
