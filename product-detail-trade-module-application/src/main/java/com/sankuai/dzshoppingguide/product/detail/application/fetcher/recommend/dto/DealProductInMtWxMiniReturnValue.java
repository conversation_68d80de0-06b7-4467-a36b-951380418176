package com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommend.dto;


import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import com.sankuai.dztheme.deal.res.DealProductResult;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author: zhangyuan103
 * @Date: 2025/5/26
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
public class DealProductInMtWxMiniReturnValue extends FetcherReturnValueDTO {
    private DealProductResult dealProductResult;
}
