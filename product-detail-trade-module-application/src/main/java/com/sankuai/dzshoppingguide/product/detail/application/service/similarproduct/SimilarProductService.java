package com.sankuai.dzshoppingguide.product.detail.application.service.similarproduct;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.sankuai.dz.product.detail.RequestCustomParamEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommendgoods.dtos.ProductM;
import com.sankuai.dzshoppingguide.product.detail.application.service.similarproduct.dtos.DealRepurchaseConfig;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2025/5/23 10:19
 */
@Component
public class SimilarProductService {

    // 过滤推荐列表中的商品
    public List<ProductM> filterProducts(ProductDetailPageRequest request, ProductM originalProduct, List<ProductM> recommendProducts) {
        if (originalProduct == null || recommendProducts == null || CollectionUtils.isEmpty(recommendProducts)) {
            return recommendProducts;
        }
        Long originProductId = originalProduct.getProductId();
        String pageSource = request.getPageSource();
        if (pageSource == null) {
            return limitProducts(recommendProducts);
        }
        return recommendProducts.stream()
                .filter(productM -> repurchaseNotLimitedFilter(request, productM))
                .filter(productM -> productM.getProductId() != originProductId)
                .limit(3)
                .collect(Collectors.toList());
    }

    public List<ProductM> limitProducts(List<ProductM> recommendProducts) {
        if (CollectionUtils.isEmpty(recommendProducts)) {
            return recommendProducts;
        }
        return recommendProducts.stream()
                .limit(3)
                .collect(Collectors.toList());
    }



    public boolean repurchaseNotLimitedFilter(ProductDetailPageRequest request, ProductM productM){
        // 判断是否是跨店推荐业务
        if (!isLimitPurchaseBiz(request)){
            // 如果不是 复购货架业务，则不过滤
            return Boolean.TRUE;
        }
        // 判断 行业、城市
        DealRepurchaseConfig repurchaseConfig = Lion.getBean("com.sankuai.dzu.tpbase.dztgdetailweb",
                "com.sankuai.dzu.tpbase.dztgdetailweb.repurchase.config", DealRepurchaseConfig.class);

        if (!enableRepurchase(request, productM.getCategoryId(), repurchaseConfig)){
            // 如果不是复购货架推广的行业、城市，则不过滤
            return Boolean.TRUE;
        }
        if (Objects.nonNull(productM) && CollectionUtils.isNotEmpty(productM.getExtAttrs())) {
            Map<String ,String> attrMap = productM.getExtAttrs().stream()
                    .filter(attrM -> Objects.nonNull(attrM) && StringUtils.isNotBlank(attrM.getName()) && StringUtils.isNotBlank(attrM.getValue()))
                    .collect(Collectors.toMap(attrM -> attrM.getName(), attrM -> attrM.getValue(),(a, b)->a));
            String buyRuleAttrStr = attrMap.get("dealGroupBuyRuleAttr");
            String userOrderCountAttrStr = attrMap.get("dealUserOrderCountAttr");
            return isNotLimitedByStr(buyRuleAttrStr, userOrderCountAttrStr);
        }
        // 默认不过滤
        return Boolean.TRUE;
    }

    public boolean isLimitPurchaseBiz(ProductDetailPageRequest request){
        String businessType = request.getCustomParam(RequestCustomParamEnum.businessType);
        return "purchase_limit".equals(businessType);
    }
    public boolean isNotLimitedByStr(String buyRuleAttrStr, String userOrderCountAttrStr){
        return !isLimitedByStr(buyRuleAttrStr, userOrderCountAttrStr);
    }

    public boolean isLimitedByStr(String buyRuleAttrStr, String userOrderCountAttrStr){
        if (StringUtils.isNotBlank(buyRuleAttrStr)
                && Objects.nonNull(JSON.parseObject(buyRuleAttrStr))){
            Integer maxPerUser = (Integer) JSON.parseObject(buyRuleAttrStr).get("maxPerUser");
            // 限购次数为1的团单不展示
            if (maxPerUser == 1){
                return Boolean.TRUE;
            }
        }
        if (StringUtils.isNotBlank(buyRuleAttrStr)
                && StringUtils.isNotBlank(userOrderCountAttrStr)
                && Objects.nonNull(JSON.parseObject(userOrderCountAttrStr))
                && Objects.nonNull(JSON.parseObject(buyRuleAttrStr))){
            Integer orderCount =  (Integer) JSON.parseObject(userOrderCountAttrStr).get("userOrderCount");
            Integer maxPerUser = (Integer) JSON.parseObject(buyRuleAttrStr).get("maxPerUser");
            return isLimited(maxPerUser, orderCount);
        }
        return Boolean.FALSE;
    }

    public boolean isLimited(Integer maxPerUser, Integer orderCount){
        Cat.logEvent("INVALID_METHOD_3", "com.sankuai.dzviewscene.product.listing.options.products.product.ListingPriceCompareProductsOpt.isLimited(java.lang.Integer,java.lang.Integer)");
        if (Objects.nonNull(maxPerUser) && Objects.nonNull(orderCount)){
            if (maxPerUser == 0){
                return Boolean.FALSE;
            }
            if (maxPerUser == 1){
                return Boolean.TRUE;
            }
            if(maxPerUser >= 2){
                return orderCount >= maxPerUser;
            }
        }
        return Boolean.FALSE;
    }

    public boolean enableRepurchase(ProductDetailPageRequest request, int categoryId, DealRepurchaseConfig repurchaseConfig){
        if (Objects.nonNull(repurchaseConfig) && repurchaseConfig.isEnableSwitch()){
            if (repurchaseConfig.isAllPass()){
                // 全行业推广
                return Boolean.TRUE;
            }
            List<Integer> categoryIds = repurchaseConfig.getCategoryIds();
            List<Integer> cityIds;
            int cityId = request.getCityId();
            if (request.getClientTypeEnum().isMtClientType()){
                cityIds = repurchaseConfig.getMtCityIds();
            }else {
                cityIds = repurchaseConfig.getDpCityIds();
            }
            // 在“指定行业”和“指定城市”下推广
            if (CollectionUtils.isNotEmpty(categoryIds) && CollectionUtils.isNotEmpty(cityIds)){
                return categoryIds.contains(categoryId) && cityIds.contains(cityId);
            }
            // 在“指定行业”下推广
            if (CollectionUtils.isNotEmpty(categoryIds) ){
                return categoryIds.contains(categoryId);
            }
            // 在“指定城市”下推广
            if ( CollectionUtils.isNotEmpty(cityIds)){
                return cityIds.contains(cityId);
            }
        }
        // 兜底逻辑
        return Boolean.FALSE;
    }

}
