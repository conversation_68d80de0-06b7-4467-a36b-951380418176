package com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommend.dto;


import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @Author: zhangyuan103
 * @Date: 2025/5/19
 * @Description 点评门店中在线售卖团单
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
public class DpDealsInShopReturnValue extends FetcherReturnValueDTO {
    // 点评门店中所有的 在线售卖团购
    private List<Long> dpAllDealIds;
}
