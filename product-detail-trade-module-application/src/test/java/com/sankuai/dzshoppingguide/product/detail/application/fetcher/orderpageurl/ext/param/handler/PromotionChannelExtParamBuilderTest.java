package com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.ext.param.handler;

import static org.junit.Assert.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.powermock.api.mockito.PowerMockito.mockStatic;
import com.dianping.lion.client.Lion;
import com.sankuai.dealuser.price.display.api.enums.PromoIdentityEnum;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import com.sankuai.dealuser.price.display.api.model.PromoIdentity;
import com.sankuai.dz.product.detail.RequestCustomParamEnum;
import com.sankuai.dz.product.detail.RequestSourceEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.ext.param.request.ExtParamBuilderRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.dto.ProductPriceReturnValue;
import com.sankuai.dzshoppingguide.product.detail.domain.utils.LionConfigUtils;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.modules.junit4.PowerMockRunnerDelegate;
import org.springframework.test.context.junit.jupiter.SpringExtension;

@RunWith(PowerMockRunner.class)
@PrepareForTest({ Lion.class, LionConfigUtils.class })
@PowerMockIgnore({ "javax.management.*", "javax.script.*" })
public class PromotionChannelExtParamBuilderTest {

    @InjectMocks
    private PromotionChannelExtParamBuilder builder;

    @Mock
    private ProductDetailPageRequest request;

    @Mock
    private ExtParamBuilderRequest builderRequest;

    @Mock
    private ProductPriceReturnValue normalPrice;

    @Mock
    private PriceDisplayDTO priceDisplayDTO;

    @Before
    public void setup() {
        mockStatic(Lion.class);
        mockStatic(LionConfigUtils.class);
        when(builderRequest.getNormalPrice()).thenReturn(normalPrice);
        when(normalPrice.getPriceDisplayDTO()).thenReturn(priceDisplayDTO);
    }

    /**
     * Test when has exclusive deduction and promotion channel exists
     */
    @Test
    public void testDoBuildExtParam_ExclusiveDeduction_WithChannel() throws Throwable {
        // arrange
        PromoDTO promoDTO = mock(PromoDTO.class);
        when(promoDTO.getPromoIdentity()).thenReturn("exclusiveDeduction");
        when(priceDisplayDTO.getUsedPromos()).thenReturn(Collections.singletonList(promoDTO));
        Map<String, String> channelMap = new HashMap<>();
        channelMap.put("testSource", "testChannel");
        when(Lion.getMap(anyString(), anyString(), eq(String.class), any(HashMap.class))).thenReturn(channelMap);
        when(request.getPageSource()).thenReturn("testSource");
        // act
        String result = builder.doBuildExtParam(request, builderRequest);
        // assert
        org.junit.Assert.assertEquals("testChannel", result);
    }

    /**
     * Test when no exclusive deduction but promotion channel exists
     */
    @Test
    public void testDoBuildExtParam_NoExclusiveDeduction_WithChannel() throws Throwable {
        // arrange
        when(priceDisplayDTO.getUsedPromos()).thenReturn(Collections.emptyList());
        Map<String, String> channelMap = new HashMap<>();
        channelMap.put("testSource", "testChannel");
        when(Lion.getMap(anyString(), anyString(), eq(String.class), any(HashMap.class))).thenReturn(channelMap);
        when(request.getPageSource()).thenReturn("testSource");
        // act
        String result = builder.doBuildExtParam(request, builderRequest);
        // assert
        org.junit.Assert.assertEquals("testChannel", result);
    }

    /**
     * Test when no exclusive deduction and no promotion channel
     */
    @Test
    public void testDoBuildExtParam_NoExclusiveDeduction_NoChannel() throws Throwable {
        // arrange
        when(priceDisplayDTO.getUsedPromos()).thenReturn(Collections.emptyList());
        Map<String, String> emptyMap = new HashMap<>();
        when(Lion.getMap(anyString(), anyString(), eq(String.class), any(HashMap.class))).thenReturn(emptyMap);
        // act
        String result = builder.doBuildExtParam(request, builderRequest);
        // assert
        org.junit.Assert.assertNull(result);
    }

    /**
     * Test caixi source with new source config enabled and hitGuessLikeSubsidy=true
     */
    @Test
    public void testDoBuildExtParam_CaixiSource_NewConfig_HitSubsidy() throws Throwable {
        // arrange
        when(priceDisplayDTO.getUsedPromos()).thenReturn(Collections.emptyList());
        Map<String, String> channelMap = new HashMap<>();
        channelMap.put(RequestSourceEnum.CAI_XI.getSource(), "caixiChannel");
        when(Lion.getMap(anyString(), anyString(), eq(String.class), any(HashMap.class))).thenReturn(channelMap);
        when(LionConfigUtils.useNewSourceForCaixi()).thenReturn(true);
        when(request.getPageSource()).thenReturn(RequestSourceEnum.CAI_XI.getSource());
        when(request.getCustomParam(RequestCustomParamEnum.extparam)).thenReturn("{\"subsidyScene\":\"hitGuessLikeSubsidy\"}");
        // act
        String result = builder.doBuildExtParam(request, builderRequest);
        // assert
        org.junit.Assert.assertEquals("caixiChannel", result);
    }

    /**
     * Test caixi source with JSON parsing exception
     */
    @Test
    public void testDoBuildExtParam_CaixiSource_JsonParseException() throws Throwable {
        // arrange
        when(priceDisplayDTO.getUsedPromos()).thenReturn(Collections.emptyList());
        Map<String, String> channelMap = new HashMap<>();
        channelMap.put(RequestSourceEnum.CAI_XI.getSource(), "caixiChannel");
        when(Lion.getMap(anyString(), anyString(), eq(String.class), any(HashMap.class))).thenReturn(channelMap);
        when(LionConfigUtils.useNewSourceForCaixi()).thenReturn(true);
        when(request.getPageSource()).thenReturn(RequestSourceEnum.CAI_XI.getSource());
        when(request.getCustomParam(RequestCustomParamEnum.extparam)).thenReturn("invalid json");
        // act
        String result = builder.doBuildExtParam(request, builderRequest);
        // assert
        org.junit.Assert.assertEquals("caixiChannel", result);
    }
}
