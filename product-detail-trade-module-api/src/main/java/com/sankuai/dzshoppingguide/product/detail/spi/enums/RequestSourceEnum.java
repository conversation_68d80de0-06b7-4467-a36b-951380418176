package com.sankuai.dzshoppingguide.product.detail.spi.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;


@AllArgsConstructor
@Getter
public enum RequestSourceEnum {
    /**
     * 美团猜喜页进入团详页路径标识，历史原因有2个标识
     */
    CAI_XI("caixi", "猜喜页进入团详页路径标识"),
    HOME_PAGE("homepage", "猜喜页进入团详页路径标识"),

    /**
     * 分销场景进入团详页路径标识
     */
    ODP("odp", "分销场景进入团详页路径标识"),

    /**
     * 特价团购
     */
    COST_EFFECTIVE("cost_effective", "特价团购"),

    /**
     *  直播场景进入团详页路径标识
     */
    LIVE_STREAM("mlive", "直播场景进入团详页路径标识"),
    /**
     * 从小视界进入团详页路径标识
     */
    MINI_VISION("mini_vision", "从小视界进入团详页路径标识"),
    /**
     * 上单预览渠道，开店宝、Apollo访问
     */
    CREATE_ORDER_PREVIEW("create_order_preview", "上单预览渠道，开店宝、Apollo访问"),
    /**
     * 从商家详情页进入团详页路径标识
     */
    POI_PAGE("poi_page", "从商家详情页进入团详页路径标识"),

    M_VIDEO("mvideo", "M_VIDEO"),

    /**
     * 营销入口美甲款式路径标识
     */
    CUBE_EXHIBIT_NAIL("cube_exhibit_nail", "营销入口美甲款式路径标识"),
    /**
     * 从款式组件进入团详
     */
    STYLE("style", "从款式组件进入团详"),
    /**
     * 新客活动
     */
    NEW_CUSTOMER_ACTIVITY("new_customer_activity", "新客活动"),
    /**
     * 丽人常买清单
     */
    BEAUTY_BUYING_LIST("beauty_buyinglist", "丽人常买清单"),
    /**
     * 预订团单渠道标识
     */
    PRE_ORDER_DEAL("pre_order_deal", "预订团单渠道标识"),

    /**
     * 开店宝Web端预览
     */
    MERCHANT_PREVIEW("merchant_preview", "开店宝Web端预览"),

    /**
     * 交易快照
     */
    TRADE_SNAPSHOT("trade_snapshot", "交易快照"),
    /**
     * 保洁自营自建货架
     */
    SELF_OPERATED_CLEANING_POI_PAGE("self_operated_cleaning_poi_page", "保洁自营自建货架"),
    /**
     * 保洁自营全切落地页
     */
    SELF_OPERATED_CLEANING_PAGE("self_operated_cleaning_page", "保洁自营全切落地页"),

    YOU_HUI_MA_MINI_CHANNEL("youhuimaMini", "优惠码小程序"),
    ZDXHYMD("zdxhymd", "中低线会员免单")
    ;

    final String source;

    final String desc;

    /**
     * 判断是否来自优惠码小程序
     * @param source 请求来源
     * @return true表示来自优惠码小程序，false表示不是来自优惠码小程序
     */
    public static boolean fromYouHuiMaMiniChannel(String source) {
        return Objects.equals(source, YOU_HUI_MA_MINI_CHANNEL.getSource());
    }

    /**
     * 判断是否来自直播
     * @param source 请求来源
     * @return true表示来自直播，false表示不是来自直播
     */
    public static boolean fromMLive(String source) {
        return Objects.equals(source, LIVE_STREAM.getSource());
    }

    /**
     * 判断是否来自特价团购
     * @param source 请求来源
     * @return true表示来自特价团购，false表示不是来自特价团购
     */
    public static boolean fromCostEffective(String source) {
        return Objects.equals(source, COST_EFFECTIVE.getSource());
    }
    public static boolean needDyeAndReport(String source) {
        if (StringUtils.isBlank(source)) {
            return false;
        }

        return ODP.source.equals(source) || COST_EFFECTIVE.source.equals(source);
    }

    /**
     * 判断是否来自分销
     * @param source 请求来源
     * @return true表示来自分销，false表示不是来自分销
     */
    public static boolean fromOdp(String source) {
        return Objects.equals(source, ODP.getSource());
    }

    public static boolean fromTradeSnapshot(String pageSource) {
        return TRADE_SNAPSHOT.getSource().equals(pageSource);
    }

    /**
     * 是否来源丽人常买清单
     * @param pageSource 页面来源
     * @return 是否来源丽人常买清单
     */
    public static boolean fromBeautyBuyingList(String pageSource) {
        return BEAUTY_BUYING_LIST.getSource().equals(pageSource);
    }

}
