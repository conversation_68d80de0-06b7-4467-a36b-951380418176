package com.sankuai.dzshoppingguide.product.detail.spi.promo.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@MobileDo(id = 0xd9bb)
public class CouponItemInfoDTO implements Serializable {
    /**
     * 券到期时间（仅用于排序）
     */
    private long endTime;
    private long couponId;

    /**
     * 券使用门槛，已膨胀券取minConsumptionAmount，可膨胀券取AFTER_INFLATE_REQUIRE_AMOUNT
     */
    private BigDecimal thresholdAmount;

    private BigDecimal amount;

    /**
    * 券码
    */
    @MobileDo.MobileField(key = 0x537d)
    private String couponCode;

    /**
    * 券批次id
    */
    @MobileDo.MobileField(key = 0xb991)
    private String applyId;

    /**
    * 到期时间文案
    */
    @MobileDo.MobileField(key = 0x8f8a)
    private String couponValidTimeText;

    /**
    * 券名称
    */
    @MobileDo.MobileField(key = 0x6782)
    private String couponName;

    /**
    * 顶部icon链接
    */
    @MobileDo.MobileField(key = 0x8d6f)
    private String logoIconUrl;

    /**
    * 券张数
    */
    @MobileDo.MobileField(key = 0xf240)
    private int couponNum;

    /**
    * 最大可膨金额（单位是元）
    */
    @MobileDo.MobileField(key = 0xe399)
    private String couponDesc;

    /**
    * 券类型 1-付费神券 2-免费神券
    */
    @MobileDo.MobileField(key = 0x7cd6)
    private int couponType;

    /**
    * 资产类型 1：到家券类型（包括到家、家店通用券）、2到店券类型、3到家通用券
    */
    @MobileDo.MobileField(key = 0xb82f)
    private int assetType;

    /**
    * 券按钮文案
    */
    @MobileDo.MobileField(key = 0x95fd)
    private String couponButtonText;

    /**
    * 业务线
    */
    @MobileDo.MobileField(key = 0x258d)
    private String bizLine;

    /**
    * 实膨参数透传
    */
    @MobileDo.MobileField(key = 0xcb02)
    private String bizToken;

    /**
    * 门槛值（满XX可用）
    */
    @MobileDo.MobileField(key = 0x77c8)
    private String thresholdDesc;

    /**
    * 券金额
    */
    @MobileDo.MobileField(key = 0xa712)
    private String couponAmount;

    /**
    * 膨胀后金额（单位为元）
    */
    @MobileDo.MobileField(key = 0xea09)
    private long originalReduceAmount;

    /**
    * 膨胀前金额（单位为元）
    */
    @MobileDo.MobileField(key = 0xdbd2)
    private long originalRequiredAmount;

    /**
    * 有效时间戳
    */
    @MobileDo.MobileField(key = 0x453a)
    private String validTime;

    /**
    * 是否已膨胀 1-已膨胀 0-未膨胀
    */
    @MobileDo.MobileField(key = 0x73d9)
    private int inflatedStatus;

    /**
    * 是否可膨胀 1-可膨胀 0-不可膨胀
    */
    @MobileDo.MobileField(key = 0x2055)
    private int canInflate;

    /**
    * 券是否失效 1:有效 0:失效
    */
    @MobileDo.MobileField(key = 0xc894)
    private int validStatus;

    /**
    * 是否锁券，1-锁券；0-未锁券，已锁券应该排在券包第一个
    */
    @MobileDo.MobileField(key = 0xee48)
    private int queryInflateFlag;

    /**
    * 新顶部icon链接
    */
    @MobileDo.MobileField(key = 0x21d0)
    private String topLeftNewIconInfo;

}