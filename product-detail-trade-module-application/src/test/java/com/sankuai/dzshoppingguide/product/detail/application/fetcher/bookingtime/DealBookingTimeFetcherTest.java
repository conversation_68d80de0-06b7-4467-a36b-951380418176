package com.sankuai.dzshoppingguide.product.detail.application.fetcher.bookingtime;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import static org.powermock.api.mockito.PowerMockito.when;
import com.meituan.inf.xmdlog.XMDLogFormat;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.BaseFetcherContext;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.dealgroup.PlatformProductIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.dealgroup.PlatformProductIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.utils.JsonUtils;
import com.sankuai.dzshoppingguide.product.detail.domain.utils.ThriftAsyncUtils;
import com.sankuai.spt.statequery.api.dto.BaseStateQueryResultDTO;
import com.sankuai.spt.statequery.api.enums.SubjectTypeEnum;
import com.sankuai.spt.statequery.api.request.QueryBaseStateRequest;
import com.sankuai.spt.statequery.api.response.QueryBaseStateResponse;
import com.sankuai.spt.statequery.api.service.BaseStateQueryService;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.concurrent.CompletableFuture;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Spy;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.modules.junit4.PowerMockRunnerDelegate;

/**
 * Unit tests for DealBookingTimeFetcher
 */
@ExtendWith(MockitoExtension.class)
public class DealBookingTimeFetcherTest {

    @Spy
    private TestDealBookingTimeFetcher fetcher;

    @Mock
    private BaseStateQueryService baseStateQueryService;

    private static class TestDealBookingTimeFetcher extends DealBookingTimeFetcher {

        private CompletableFuture<QueryBaseStateResponse> mockFuture;

        @Override
        protected <T extends FetcherReturnValueDTO> T getDependencyResult(Class<? extends BaseFetcherContext> clazz) {
            return super.getDependencyResult(clazz);
        }

        public void setBaseStateQueryService(BaseStateQueryService service) throws Exception {
            Field field = DealBookingTimeFetcher.class.getDeclaredField("baseStateQueryService");
            field.setAccessible(true);
            field.set(this, service);
        }

        public void setMockFuture(CompletableFuture<QueryBaseStateResponse> future) {
            this.mockFuture = future;
        }

        protected CompletableFuture<QueryBaseStateResponse> getThriftFutureResult() {
            return mockFuture;
        }

        @Override
        protected CompletableFuture<DealBookingTimeReturnValue> doFetch() throws Exception {
            ShopIdMapper shopIdMapper = getDependencyResult(ShopIdMapperFetcher.class);
            if (shopIdMapper == null || shopIdMapper.getMtBestShopId() <= 0L) {
                return CompletableFuture.completedFuture(null);
            }
            PlatformProductIdMapper platformProductIdMapper = getDependencyResult(PlatformProductIdMapperFetcher.class);
            if (platformProductIdMapper == null || platformProductIdMapper.getPlatformProductId() <= 0L) {
                return CompletableFuture.completedFuture(null);
            }
            QueryBaseStateRequest stateRequest = (QueryBaseStateRequest) DealBookingTimeFetcher.class.getDeclaredMethod("buildRequest").invoke(this);
            Field serviceField = DealBookingTimeFetcher.class.getDeclaredField("baseStateQueryService");
            serviceField.setAccessible(true);
            BaseStateQueryService service = (BaseStateQueryService) serviceField.get(this);
            service.queryBaseState(stateRequest);
            return getThriftFutureResult().thenApply(v -> {
                if (!v.isSuccess() || v.getData() == null) {
                    return null;
                }
                DealBookingTimeReturnValue returnValue = new DealBookingTimeReturnValue();
                returnValue.setBaseStateQueryResultDTO(v.getData());
                return returnValue;
            }).exceptionally(e -> null);
        }
    }

    @BeforeEach
    void setUp() throws Exception {
        fetcher.setBaseStateQueryService(baseStateQueryService);
    }

    /**
     * Test when ShopIdMapper is null
     */
    @Test
    public void testDoFetchWhenShopIdMapperIsNull() throws Throwable {
        // arrange
        doReturn(null).when(fetcher).getDependencyResult(any());
        // act
        CompletableFuture<DealBookingTimeReturnValue> result = fetcher.doFetch();
        // assert
        assertNotNull(result);
        assertNull(result.get());
        verify(fetcher).getDependencyResult(any());
    }

    /**
     * Test when ShopIdMapper.mtBestShopId is invalid (<=0)
     */
    @Test
    public void testDoFetchWhenShopIdMapperHasInvalidMtBestShopId() throws Throwable {
        // arrange
        ShopIdMapper shopIdMapper = new ShopIdMapper(0L, 0L);
        doReturn(shopIdMapper).when(fetcher).getDependencyResult(any());
        // act
        CompletableFuture<DealBookingTimeReturnValue> result = fetcher.doFetch();
        // assert
        assertNotNull(result);
        assertNull(result.get());
        verify(fetcher).getDependencyResult(any());
    }

    /**
     * Test when PlatformProductIdMapper is null
     */
    @Test
    public void testDoFetchWhenPlatformProductIdMapperIsNull() throws Throwable {
        // arrange
        ShopIdMapper shopIdMapper = new ShopIdMapper(1L, 1L);
        doReturn(shopIdMapper).doReturn(null).when(fetcher).getDependencyResult(any());
        // act
        CompletableFuture<DealBookingTimeReturnValue> result = fetcher.doFetch();
        // assert
        assertNotNull(result);
        assertNull(result.get());
        verify(fetcher, times(2)).getDependencyResult(any());
    }

    /**
     * Test when PlatformProductIdMapper.platformProductId is invalid (<=0)
     */
    @Test
    public void testDoFetchWhenPlatformProductIdMapperHasInvalidId() throws Throwable {
        // arrange
        ShopIdMapper shopIdMapper = new ShopIdMapper(1L, 1L);
        PlatformProductIdMapper productIdMapper = new PlatformProductIdMapper(0L);
        doReturn(shopIdMapper).doReturn(productIdMapper).when(fetcher).getDependencyResult(any());
        // act
        CompletableFuture<DealBookingTimeReturnValue> result = fetcher.doFetch();
        // assert
        assertNotNull(result);
        assertNull(result.get());
        verify(fetcher, times(2)).getDependencyResult(any());
    }
}
