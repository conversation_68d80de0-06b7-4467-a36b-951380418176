package com.sankuai.dzshoppingguide.product.detail.spi.dtos;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @create 2025/2/7 16:19
 * 移动之家：https://mobile.sankuai.com/studio/model/info/41240
 */
@Builder
@Data
@TypeDoc(description = "新版预订详情页（服务设施模块）")
@MobileDo(id = 0x3975)
public class FacilitiesVO implements Serializable {
    @FieldDoc(description = "内容详情")
    @MobileDo.MobileField(key = 0x8535)
    private List<String > contents;

    @FieldDoc(description = "标题")
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    @FieldDoc(description = "类型目前1-3三种跟前端对接")
    @MobileDo.MobileField(key = 0x8f0c)
    private int type;
}
