package com.sankuai.dzshoppingguide.product.detail.spi.bookingtime.vo;

import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.sankuai.dz.product.detail.gateway.spi.vo.AbstractModuleVO;
import lombok.Data;

import java.util.List;

/**
 * https://mobile.sankuai.com/studio/model/info/42811
 */
@Data
public abstract class BookingTimeSelectionVO extends AbstractModuleVO {

    /**
     * 行业类型：
     * @see com.sankuai.dzshoppingguide.product.detail.application.builder.bookingtime.enums.MultiTradeBizEnum
     */
    @MobileField
    private Integer biz;

    /**
     * 场域信息：
     * @see com.sankuai.dzshoppingguide.product.detail.application.builder.bookingtime.enums.MultiTradeSourceTypeEnum
     */
    @MobileField
    private Integer sourceType;

    /**
     * 更多跳链
     */
    @MobileField
    private String moreUrl;

    /**
     * 更多文案
     */
    @MobileField
    private String moreText;

    /**
     * 点击更多行动方式
     * @see com.sankuai.dzshoppingguide.product.detail.application.builder.bookingtime.enums.MoreActionEnum
     */
    @MobileField
    private int moreAction;

    /**
     * 提示文案
     */
    @MobileField
    private String tips;

    /**
     * 模块名称
     */
    @MobileField
    private String title;

    /**
     * 时间选择Tab
     */
    @MobileField
    private List<BookingDateTab> tabList;

}
