package com.sankuai.dzshoppingguide.product.detail.application.fetcher.consult;

import com.dianping.dzim.common.enums.ImSendUnitType;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzim.cliententry.dto.ClientEntryReqDTO;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.dealgroup.DealGroupIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapper;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class DetailIMFetcherBuildClientEntryReqDTOTest {

    @InjectMocks
    private DetailIMFetcher detailIMFetcher;

    @Mock
    private ProductDetailPageRequest request;

    /**
     * Test normal case with valid parameters
     */
    @Test
    public void testBuildClientEntryReqDTO_NormalCase() {
        // arrange
        DealGroupIdMapper dealGroupIdMapper = new DealGroupIdMapper();
        dealGroupIdMapper.setDpDealGroupId(12345L);
        ShopIdMapper shopIdMapper = new ShopIdMapper();
        shopIdMapper.setDpBestShopId(67890L);
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.DP_APP);
        // act
        ClientEntryReqDTO result = detailIMFetcher.buildClientEntryReqDTO(dealGroupIdMapper, shopIdMapper);
        // assert
        assertNotNull(result);
        assertEquals(67890L, result.getDpShopId());
        assertEquals("12345", result.getSendUnitId());
        assertEquals(ImSendUnitType.DEALGROUP.unitType, result.getSendUnitType());
        assertNotNull(result.getAdditionalParam());
        assertEquals("deal_detail", result.getAdditionalParam().get("source"));
    }

    /**
     * Test case with zero values in mappers
     */
    @Test
    public void testBuildClientEntryReqDTO_ZeroValues() {
        // arrange
        DealGroupIdMapper dealGroupIdMapper = new DealGroupIdMapper();
        dealGroupIdMapper.setDpDealGroupId(0L);
        ShopIdMapper shopIdMapper = new ShopIdMapper();
        shopIdMapper.setDpBestShopId(0L);
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.DP_APP);
        // act
        ClientEntryReqDTO result = detailIMFetcher.buildClientEntryReqDTO(dealGroupIdMapper, shopIdMapper);
        // assert
        assertNotNull(result);
        assertEquals(0L, result.getDpShopId());
        assertEquals("0", result.getSendUnitId());
        assertEquals(ImSendUnitType.DEALGROUP.unitType, result.getSendUnitType());
        assertNotNull(result.getAdditionalParam());
        assertEquals("deal_detail", result.getAdditionalParam().get("source"));
    }

    /**
     * Test case with negative values in mappers
     */
    @Test
    public void testBuildClientEntryReqDTO_NegativeValues() {
        // arrange
        DealGroupIdMapper dealGroupIdMapper = new DealGroupIdMapper();
        dealGroupIdMapper.setDpDealGroupId(-1L);
        ShopIdMapper shopIdMapper = new ShopIdMapper();
        shopIdMapper.setDpBestShopId(-1L);
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.DP_APP);

        // act
        ClientEntryReqDTO result = detailIMFetcher.buildClientEntryReqDTO(dealGroupIdMapper, shopIdMapper);
        // assert
        assertNotNull(result);
        assertEquals(-1L, result.getDpShopId());
        assertEquals("-1", result.getSendUnitId());
        assertEquals(ImSendUnitType.DEALGROUP.unitType, result.getSendUnitType());
        assertNotNull(result.getAdditionalParam());
        assertEquals("deal_detail", result.getAdditionalParam().get("source"));
    }

}
