package com.sankuai.dzshoppingguide.product.detail.application.utils;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class RequestUtilsTest {

    @Mock
    private ProductDetailPageRequest request;

    /**
     * Test case for DP_XCX client type
     * Expected: Should return true
     */
    @Test
    public void testIsDpMiniApp_WithDpXcx_ReturnsTrue() {
        // arrange
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.DP_XCX);
        // act
        boolean result = RequestUtils.isDpMiniApp(request);
        // assert
        assertTrue(result);
    }

    /**
     * Test case for DP_BAIDU_MAP_XCX client type
     * Expected: Should return true
     */
    @Test
    public void testIsDpMiniApp_WithDpBaiduMapXcx_ReturnsTrue() {
        // arrange
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.DP_BAIDU_MAP_XCX);
        // act
        boolean result = RequestUtils.isDpMiniApp(request);
        // assert
        assertTrue(result);
    }

    /**
     * Test case for MT_XCX client type
     * Expected: Should return false
     */
    @Test
    public void testIsDpMiniApp_WithMtXcx_ReturnsFalse() {
        // arrange
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.MT_XCX);
        // act
        boolean result = RequestUtils.isDpMiniApp(request);
        // assert
        assertFalse(result);
    }

    /**
     * Test case for DP_APP client type
     * Expected: Should return false
     */
    @Test
    public void testIsDpMiniApp_WithDpApp_ReturnsFalse() {
        // arrange
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.DP_APP);
        // act
        boolean result = RequestUtils.isDpMiniApp(request);
        // assert
        assertFalse(result);
    }

    /**
     * Test case for UNKNOWN client type
     * Expected: Should return false
     */
    @Test
    public void testIsDpMiniApp_WithUnknown_ReturnsFalse() {
        // arrange
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.UNKNOWN);
        // act
        boolean result = RequestUtils.isDpMiniApp(request);
        // assert
        assertFalse(result);
    }

    /**
     * Test case for null client type
     * Expected: Should return false
     */
    @Test
    public void testIsDpMiniApp_WithNullClientType_ReturnsFalse() {
        // arrange
        when(request.getClientTypeEnum()).thenReturn(null);
        // act
        boolean result = RequestUtils.isDpMiniApp(request);
        // assert
        assertFalse(result);
    }

    /**
     * Test when client type is MT_XCX (in the list)
     */
    @Test
    public void testIsMtMiniApp_WithMtXcx_ReturnsTrue() {
        // arrange
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.MT_XCX);
        // act
        boolean result = RequestUtils.isMtMiniApp(request);
        // assert
        assertTrue(result);
    }

    /**
     * Test when client type is MT_ZJ_XCX (in the list)
     */
    @Test
    public void testIsMtMiniApp_WithMtZjXcx_ReturnsTrue() {
        // arrange
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.MT_ZJ_XCX);
        // act
        boolean result = RequestUtils.isMtMiniApp(request);
        // assert
        assertTrue(result);
    }

    /**
     * Test when client type is MT_WWJKZ_XCX (in the list)
     */
    @Test
    public void testIsMtMiniApp_WithMtWwjkzXcx_ReturnsTrue() {
        // arrange
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.MT_WWJKZ_XCX);
        // act
        boolean result = RequestUtils.isMtMiniApp(request);
        // assert
        assertTrue(result);
    }

    /**
     * Test when client type is MT_KUAI_SHOU_XCX (in the list)
     */
    @Test
    public void testIsMtMiniApp_WithMtKuaiShouXcx_ReturnsTrue() {
        // arrange
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.MT_KUAI_SHOU_XCX);
        // act
        boolean result = RequestUtils.isMtMiniApp(request);
        // assert
        assertTrue(result);
    }

    /**
     * Test when client type is MT_MAO_YAN_XCX (in the list)
     */
    @Test
    public void testIsMtMiniApp_WithMtMaoYanXcx_ReturnsTrue() {
        // arrange
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.MT_MAO_YAN_XCX);
        // act
        boolean result = RequestUtils.isMtMiniApp(request);
        // assert
        assertTrue(result);
    }

    /**
     * Test when client type is MT_APP (not in the list)
     */
    @Test
    public void testIsMtMiniApp_WithMtApp_ReturnsFalse() {
        // arrange
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.MT_APP);
        // act
        boolean result = RequestUtils.isMtMiniApp(request);
        // assert
        assertFalse(result);
    }

    /**
     * Test when client type is DP_XCX (not in the list)
     */
    @Test
    public void testIsMtMiniApp_WithDpXcx_ReturnsFalse() {
        // arrange
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.DP_XCX);
        // act
        boolean result = RequestUtils.isMtMiniApp(request);
        // assert
        assertFalse(result);
    }

    /**
     * Test when request is null
     */
    @Test
    public void testIsMtMiniApp_WithNullRequest_ThrowsNullPointerException() {
        // act & assert
        assertThrows(NullPointerException.class, () -> RequestUtils.isMtMiniApp(null));
    }

    /**
     * Test when client type enum is null
     */
    @Test
    public void testIsMtMiniApp_WithNullClientType_ReturnsFalse() {
        // arrange
        when(request.getClientTypeEnum()).thenReturn(null);
        // act
        boolean result = RequestUtils.isMtMiniApp(request);
        // assert
        assertFalse(result);
    }
}
