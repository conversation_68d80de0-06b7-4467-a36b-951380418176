package com.sankuai.dzshoppingguide.product.detail.application.fetcher.atmosphere;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.best.shop.ShopInfo;
import com.sankuai.sinai.data.api.dto.DpPoiBackCategoryDTO;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.sinai.data.api.dto.MtPoiDTO;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.apache.commons.lang3.math.NumberUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class ProductAtmosphereFetcherGetFirstPoiCategoryIds1Test {

    @InjectMocks
    private ProductAtmosphereFetcher productAtmosphereFetcher;

    @Mock
    private ProductDetailPageRequest pageRequest;

    @Mock
    private ShopInfo shopInfo;

    @Mock
    private MtPoiDTO mtPoiDTO;

    @Mock
    private DpPoiDTO dpPoiDTO;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    private DpPoiBackCategoryDTO createDpPoiBackCategoryDTO(int level, int categoryId) {
        DpPoiBackCategoryDTO dto = new DpPoiBackCategoryDTO();
        dto.setCategoryLevel(level);
        dto.setCategoryId(categoryId);
        return dto;
    }


}
