package com.sankuai.dzshoppingguide.product.detail.application.builder.pricebar.config;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2025/04/01
 * 引导使用条件配置信息
 */
@Data
public class ProductPriceConfig {

    /**
     * 商品价格上限
     */
    private String upperLimit;

    /**
     * 商品价格下限
     */
    private String lowerLimit;

    /**
     * 券面额
     */
    private String couponAmount;

}
