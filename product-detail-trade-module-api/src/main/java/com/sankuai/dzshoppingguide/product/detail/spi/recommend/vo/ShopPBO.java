package com.sankuai.dzshoppingguide.product.detail.spi.recommend.vo;

/**
 * @Author: zhangyuan103
 * @Date: 2025/5/19
 *        https://mobile.sankuai.com/studio/model/info/14455
 */

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.sig.botdefender.adapter.annotation.EncryptedField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

import static com.sankuai.sig.botdefender.core.enums.AssetIdType.SHOP_UUID;

@Data
@TypeDoc(description = "到综团单适用商户数据模型")
@MobileDo(id = 0x5baa)
public class ShopPBO implements Serializable {

    @FieldDoc(description = "商户ID")
    @MobileDo.MobileField(key = 0x349b)
    @EncryptedField(targetFieldName = "shopIdEncrypt")
    private long shopId;
    @MobileDo.MobileField(key = 0x8143)
    private String shopIdEncrypt;

    @FieldDoc(description = "商户UUID")
    @MobileDo.MobileField
    @EncryptedField(targetFieldName = "shopUuidEncrypt", assetIdType = SHOP_UUID)
    private String shopUuid;
    @MobileDo.MobileField(key = 0xd071)
    private String shopUuidEncrypt;

    @FieldDoc(description = "商户名称")
    @MobileDo.MobileField(key = 0xb5c9)
    private String shopName;

    @FieldDoc(description = "分支名称")
    @MobileDo.MobileField(key = 0x23e9)
    private String branchName;

    @FieldDoc(description = "商户电话")
    @MobileDo.MobileField(key = 0x6536)
    private List<String> phoneNos;

    @FieldDoc(description = "商户地址")
    @MobileDo.MobileField(key = 0x2063)
    private String address;

    @FieldDoc(description = "商户评分")
    @MobileDo.MobileField(key = 0xa669)
    private int shopPower;

    @FieldDoc(description = "在线咨询跳转地址")
    @MobileDo.MobileField(key = 0x7851)
    private String imUrl;

    @FieldDoc(description = "预约按钮")
    @MobileDo.MobileField(key = 0x352a)
    private List<ComBtn> recallBtns;

    @FieldDoc(description = "适用门店列表标题")
    @MobileDo.MobileField(key = 0x8c2a)
    private String shopListTitle;

    @FieldDoc(description = "适用商户列表地址")
    @MobileDo.MobileField(key = 0x46c8)
    private String shopListUrl;

    @FieldDoc(description = "适用商户列表描述")
    @MobileDo.MobileField(key = 0x82e3)
    private String shopListDesc;

    @FieldDoc(description = "地图地址")
    @MobileDo.MobileField(key = 0xe5f8)
//    @EncryptedLinkField(queries = {"shopid","poi_id"})
    private String mapUrl;

    @FieldDoc(description = "用户商户距离描述文案")
    @MobileDo.MobileField(key = 0x3936)
    private String distanceDesc;

    @FieldDoc(description = "用户商户距离")
    @MobileDo.MobileField(key = 0x9ac4)
    private String distance;

    @FieldDoc(description = "商户数量")
    @MobileDo.MobileField(key = 0x9784)
    private int shopNum;

    @FieldDoc(description = "商户详情页地址")
    @MobileDo.MobileField(key = 0x7dac)
//    @EncryptedLinkField(queries = {"shopid","id"})
    private String shopUrl;

    @FieldDoc(description = "商户纬度")
    @MobileDo.MobileField(key = 0xa19e)
    private double lat;

    @FieldDoc(description = "商户经度")
    @MobileDo.MobileField(key = 0xa324)
    private double lng;

    @FieldDoc(description = "门店信息展示位置")
    @MobileDo.MobileField(key = 0xd15d)
    private int displayPosition = 1;

    @FieldDoc(description = "点评门店类型")
    @MobileDo.MobileField(key = 0x983b)
    private int shopType;

    @MobileDo.MobileField(key = 0xfc19)
    private String showType;

    @FieldDoc(description = "是否隐藏门店地址")
    @MobileDo.MobileField(key = 0x26fb)
    private boolean hideAddrEnable;

    @FieldDoc(description = "是否是lyy门店")
    @MobileDo.MobileField(key = 0x16c5)
    private boolean isLyyShop;

    @FieldDoc(description = "门店业务类型，1-疫苗虚拟门店")
    @MobileDo.MobileField(key = 0x613e)
    private int shopBizType;

    @FieldDoc(description = "关联门店")
    @MobileDo.MobileField(key = 0xd256)
    private List<RelatedShop> relatedShops;

    @FieldDoc(description = "门店描述")
    @MobileDo.MobileField(key = 0x9c4)
    private String shopDesc;

    @FieldDoc(description = "是否隐藏星级")
    @MobileDo.MobileField(key = 0xc207)
    private boolean hideStars;

    @FieldDoc(description = "门店图片")
    @MobileDo.MobileField(key = 0x8980)
    private String shopPic;

    @FieldDoc(description = "门店人均")
    @MobileDo.MobileField(key = 0x4b46)
    private String avgPrice;

    @FieldDoc(description = "门店营业状态")
    @MobileDo.MobileField(key = 0xc855)
    private String businessState;

    @FieldDoc(description = "门店营业时间")
    @MobileDo.MobileField(key = 0x9e25)
    private String businessHour;

    @FieldDoc(description = "购买Bar的Icon样式,0为默认")
    @MobileDo.MobileField(key = 0x9ec2)
    private int buyBarIconType;

    @FieldDoc(description = "门店类别，1表示穿戴甲专卖店 2表示穿戴甲寄售店")
    @MobileDo.MobileField(key = 0xe045)
    private int shopCategoryId;
}