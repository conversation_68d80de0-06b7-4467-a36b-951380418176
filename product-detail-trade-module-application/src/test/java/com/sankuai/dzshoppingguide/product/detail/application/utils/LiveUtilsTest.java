package com.sankuai.dzshoppingguide.product.detail.application.utils;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sankuai.dealuser.price.display.api.enums.ConstantsSourceEnum;
import com.sankuai.dealuser.price.display.api.enums.ExtensionKeyEnum;
import java.util.Map;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class LiveUtilsTest {

    /**
     * Test when requestSource is not from MLive
     */
    @Test
    public void testGetLiveSecKillParams_NotFromMLive() throws Throwable {
        // arrange
        String passParam = "validParam";
        String requestSource = "other";
        // act
        Map<String, String> result = LiveUtils.getLiveSecKillParams(passParam, requestSource);
        // assert
        assertNull(result);
    }

    /**
     * Test when passParam is null
     */
    @Test
    public void testGetLiveSecKillParams_NullPassParam() throws Throwable {
        // arrange
        String passParam = null;
        String requestSource = "mlive";
        // act
        Map<String, String> result = LiveUtils.getLiveSecKillParams(passParam, requestSource);
        // assert
        assertNull(result);
    }

    /**
     * Test when passParam is empty
     */
    @Test
    public void testGetLiveSecKillParams_EmptyPassParam() throws Throwable {
        // arrange
        String passParam = "";
        String requestSource = "mlive";
        // act
        Map<String, String> result = LiveUtils.getLiveSecKillParams(passParam, requestSource);
        // assert
        assertNull(result);
    }

    /**
     * Test when URL decoding fails
     */
    @Test
    public void testGetLiveSecKillParams_UrlDecodingFails() throws Throwable {
        // arrange
        String passParam = "%invalid";
        String requestSource = "mlive";
        // act
        Map<String, String> result = LiveUtils.getLiveSecKillParams(passParam, requestSource);
        // assert
        assertNull(result);
    }

    /**
     * Test when PROMOTE_CHANNEL_INFO is missing
     */
    @Test
    public void testGetLiveSecKillParams_MissingPromoteChannelInfo() throws Throwable {
        // arrange
        // {"other":"value"}
        String passParam = "%7B%22other%22%3A%22value%22%7D";
        String requestSource = "mlive";
        // act
        Map<String, String> result = LiveUtils.getLiveSecKillParams(passParam, requestSource);
        // assert
        assertNull(result);
    }

    /**
     * Test when promoteExtend is missing
     */
    @Test
    public void testGetLiveSecKillParams_MissingPromoteExtend() throws Throwable {
        // arrange
        String passParam = "%7B%22PROMOTE_CHANNEL_INFO%22%3A%7B%22other%22%3A%22value%22%7D%7D";
        String requestSource = "mlive";
        // act
        Map<String, String> result = LiveUtils.getLiveSecKillParams(passParam, requestSource);
        // assert
        assertNull(result);
    }

    /**
     * Test when all IDs are empty in promoteExtend
     */
    @Test
    public void testGetLiveSecKillParams_AllIdsEmpty() throws Throwable {
        // arrange
        String passParam = "%7B%22PROMOTE_CHANNEL_INFO%22%3A%7B%22promoteExtend%22%3A%7B%7D%7D%7D";
        String requestSource = "mlive";
        // act
        Map<String, String> result = LiveUtils.getLiveSecKillParams(passParam, requestSource);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test when only mLiveId is present
     */
    @Test
    public void testGetLiveSecKillParams_OnlyMLiveId() throws Throwable {
        // arrange
        String passParam = "%7B%22PROMOTE_CHANNEL_INFO%22%3A%7B%22promoteExtend%22%3A%7B%22mLiveId%22%3A%22123%22%7D%7D%7D";
        String requestSource = "mlive";
        // act
        Map<String, String> result = LiveUtils.getLiveSecKillParams(passParam, requestSource);
        // assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("123", result.get(ExtensionKeyEnum.LiveStreamingSceneId.getDesc()));
        assertEquals(ConstantsSourceEnum.M_LIVE.getCode(), result.get(ExtensionKeyEnum.Source.getDesc()));
    }

    /**
     * Test when all IDs are present
     */
    @Test
    public void testGetLiveSecKillParams_AllIdsPresent() throws Throwable {
        // arrange
        String passParam = "%7B%22PROMOTE_CHANNEL_INFO%22%3A%7B%22promoteExtend%22%3A%7B%22mLiveId%22%3A%22123%22%2C%22influencerId%22%3A%22456%22%2C%22seckillId%22%3A%22789%22%7D%7D%7D";
        String requestSource = "mlive";
        // act
        Map<String, String> result = LiveUtils.getLiveSecKillParams(passParam, requestSource);
        // assert
        assertNotNull(result);
        assertEquals(4, result.size());
        assertEquals("123", result.get(ExtensionKeyEnum.LiveStreamingSceneId.getDesc()));
        assertEquals("456", result.get(ExtensionKeyEnum.AnchorId.getDesc()));
        assertEquals("789", result.get(ExtensionKeyEnum.LiveSecKillActiveId.getDesc()));
        assertEquals(ConstantsSourceEnum.M_LIVE.getCode(), result.get(ExtensionKeyEnum.Source.getDesc()));
    }

    /**
     * Test when invalid JSON format in passParam
     */
    @Test
    public void testGetLiveSecKillParams_InvalidJson() throws Throwable {
        // arrange
        String passParam = "%7Binvalid-json%7D";
        String requestSource = "mlive";
        // act
        Map<String, String> result = LiveUtils.getLiveSecKillParams(passParam, requestSource);
        // assert
        assertNull(result);
    }
}
