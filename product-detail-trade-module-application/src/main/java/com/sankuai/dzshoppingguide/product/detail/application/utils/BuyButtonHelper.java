package com.sankuai.dzshoppingguide.product.detail.application.utils;

import com.alibaba.fastjson.JSON;
import com.dianping.lion.client.Lion;
import com.dianping.tpfun.product.api.sku.pintuan.dto.PinProductBrief;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dzshoppingguide.product.detail.application.constants.LionConstants;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.merchantcard.timescard.exposure.dto.CardSummaryBarDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

@Slf4j
public class BuyButtonHelper {
    public static boolean isValidPinPool(long dealGroupId, PinProductBrief pinProductBrief, BigDecimal pinPoolPromoAmount, PriceDisplayDTO normalPrice) {

        if (pinProductBrief == null || pinProductBrief.getProductId() <= 0) {
            return false;
        }

        if (pinProductBrief.isSoldOut() || pinProductBrief.getPrice() == null || pinProductBrief.getPrice().doubleValue() <= 0) {
            return false;
        }

        if (StringUtils.isNotEmpty(pinProductBrief.getUrl())) {
            BigDecimal pinPoolPrice = pinProductBrief.getPrice();

            if (pinPoolPromoAmount != null && pinPoolPromoAmount.compareTo(BigDecimal.ZERO) > 0) {
                pinPoolPrice = pinPoolPrice.subtract(pinPoolPromoAmount).setScale(1, RoundingMode.CEILING);
            }

            if (isDaoGua(pinPoolPrice, normalPrice)) {

                log.debug("团单id={},拼团倒挂,拼团={},拼团优惠={},常规价格={}",
                        dealGroupId, JSON.toJSONString(pinProductBrief), pinPoolPromoAmount, JSON.toJSONString(normalPrice));
                return false;

            } else {
                return true;
            }
        }

        return false;
    }


    public static boolean isValidTimesCard(int dealGroupId, CardSummaryBarDTO timesCard, DealGroupCategoryDTO categoryDTO, PriceDisplayDTO normalPrice) {
        // 预订场景不展示次卡
        if(DealCtxHelper.isPreOrderDeal(categoryDTO, null, null)) {
            return false;
        }

        if (timesCard == null || timesCard.getPrice() == null || timesCard.getTimes() <= 0) {
            return false;
        }

        if (isDaoGua(timesCard.getPrice(), normalPrice)) {
            log.debug("团单id={},次卡倒挂,次卡={},常规价格={}", dealGroupId, JSON.toJSONString(timesCard), JSON.toJSONString(normalPrice));
            return false;
        }

        return true;
    }

    /**
     * 是否倒挂
     * @param price
     * @return
     */
    public static boolean isDaoGua(BigDecimal price, PriceDisplayDTO normalPrice) {

        if (price == null || normalPrice == null) {
            return false;
        }
        /**
         * 足疗市场价button的PK先不考虑价格倒挂，在后续PK逻辑中处理
         */
        return price.compareTo(normalPrice.getPrice()) >= 0;
    }

}
