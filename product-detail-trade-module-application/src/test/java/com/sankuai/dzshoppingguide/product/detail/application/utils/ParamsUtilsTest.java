package com.sankuai.dzshoppingguide.product.detail.application.utils;

import static org.junit.jupiter.api.Assertions.assertEquals;
import com.dianping.vc.sdk.dp.client.ClientType;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.enums.MobileOSTypeEnum;
import org.junit.jupiter.api.Test;

public class ParamsUtilsTest {

    @Test
    public void testGetClientTypeDpAppIos() {
        ClientTypeEnum clientType = ClientTypeEnum.DP_APP;
        MobileOSTypeEnum mobileOSType = MobileOSTypeEnum.IOS;
        int result = ParamsUtils.getClientType(clientType, mobileOSType);
        assertEquals(ClientType.DP_IPHONE_NATIVE, result);
    }

    @Test
    public void testGetClientTypeDpAppAndroid() {
        ClientTypeEnum clientType = ClientTypeEnum.DP_APP;
        MobileOSTypeEnum mobileOSType = MobileOSTypeEnum.ANDROID;
        int result = ParamsUtils.getClientType(clientType, mobileOSType);
        assertEquals(ClientType.DP_ANDROID_NATIVE, result);
    }

    @Test
    public void testGetClientTypeDpPc() {
        ClientTypeEnum clientType = ClientTypeEnum.DP_PC;
        MobileOSTypeEnum mobileOSType = MobileOSTypeEnum.ANDROID;
        int result = ParamsUtils.getClientType(clientType, mobileOSType);
        assertEquals(ClientType.M_WEB, result);
    }

    @Test
    public void testGetClientTypeDpBaiduMapXcxIos() {
        ClientTypeEnum clientType = ClientTypeEnum.DP_BAIDU_MAP_XCX;
        MobileOSTypeEnum mobileOSType = MobileOSTypeEnum.IOS;
        int result = ParamsUtils.getClientType(clientType, mobileOSType);
        assertEquals(ClientType.DP_IPHONE_WEB, result);
    }

    @Test
    public void testGetClientTypeDpBaiduMapXcxAndroid() {
        ClientTypeEnum clientType = ClientTypeEnum.DP_BAIDU_MAP_XCX;
        MobileOSTypeEnum mobileOSType = MobileOSTypeEnum.ANDROID;
        int result = ParamsUtils.getClientType(clientType, mobileOSType);
        assertEquals(ClientType.DP_ANDROID_WEB, result);
    }

    @Test
    public void testGetClientTypeDpWx() {
        ClientTypeEnum clientType = ClientTypeEnum.DP_WX;
        MobileOSTypeEnum mobileOSType = MobileOSTypeEnum.ANDROID;
        int result = ParamsUtils.getClientType(clientType, mobileOSType);
        assertEquals(ClientType.WEIXIN, result);
    }

    @Test
    public void testGetClientTypeMtWx() {
        ClientTypeEnum clientType = ClientTypeEnum.MT_WX;
        MobileOSTypeEnum mobileOSType = MobileOSTypeEnum.ANDROID;
        int result = ParamsUtils.getClientType(clientType, mobileOSType);
        assertEquals(ClientType.WEIXIN, result);
    }

    @Test
    public void testGetClientTypeMtWaiMaiAppIos() {
        ClientTypeEnum clientType = ClientTypeEnum.MT_WAI_MAI_APP;
        MobileOSTypeEnum mobileOSType = MobileOSTypeEnum.IOS;
        int result = ParamsUtils.getClientType(clientType, mobileOSType);
        assertEquals(ClientType.MT_IPHONE_NATIVE, result);
    }

    @Test
    public void testGetClientTypeMtWaiMaiAppAndroid() {
        ClientTypeEnum clientType = ClientTypeEnum.MT_WAI_MAI_APP;
        MobileOSTypeEnum mobileOSType = MobileOSTypeEnum.ANDROID;
        int result = ParamsUtils.getClientType(clientType, mobileOSType);
        assertEquals(ClientType.MT_ANDROID_NATIVE, result);
    }

    @Test
    public void testGetClientTypeKaiDianBao() {
        ClientTypeEnum clientType = ClientTypeEnum.KAI_DIAN_BAO;
        MobileOSTypeEnum mobileOSType = MobileOSTypeEnum.ANDROID;
        int result = ParamsUtils.getClientType(clientType, mobileOSType);
        assertEquals(ClientType.DP_ANDROID_NATIVE, result);
    }

    @Test
    public void testGetClientTypeOther() {
        ClientTypeEnum clientType = ClientTypeEnum.UNKNOWN;
        MobileOSTypeEnum mobileOSType = MobileOSTypeEnum.ANDROID;
        int result = ParamsUtils.getClientType(clientType, mobileOSType);
        assertEquals(ClientType.DP_ANDROID_NATIVE, result);
    }
}
