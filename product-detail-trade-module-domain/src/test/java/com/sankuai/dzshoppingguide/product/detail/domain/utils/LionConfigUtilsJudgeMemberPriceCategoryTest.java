package com.sankuai.dzshoppingguide.product.detail.domain.utils;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import com.dianping.lion.client.Lion;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.After;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

/**
 * Test class for LionConfigUtils.judgeMemberPriceCategory method
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({ Lion.class })
@PowerMockIgnore({ "javax.management.*", "javax.script.*" })
public class LionConfigUtilsJudgeMemberPriceCategoryTest {

    /**
     * Test judgeMemberPriceCategory when memberPriceCategoryIds is empty
     * This test covers the code block that returns false when the list is empty
     */
    @Test
    public void testJudgeMemberPriceCategoryWhenListIsEmpty() throws Throwable {
        // arrange
        PowerMockito.mockStatic(Lion.class);
        PowerMockito.when(Lion.getList(anyString(), anyString(), eq(Integer.class), any(List.class))).thenReturn(Collections.emptyList());
        // act
        boolean result = LionConfigUtils.judgeMemberPriceCategory(123);
        // assert
        assertFalse("Should return false when memberPriceCategoryIds is empty", result);
        // verify
        PowerMockito.verifyStatic(Lion.class);
        Lion.getList(anyString(), anyString(), eq(Integer.class), any(List.class));
    }

    /**
     * Test judgeMemberPriceCategory when memberPriceCategoryIds is null
     * This test covers the code block that returns false when the list is null
     */
    @Test
    public void testJudgeMemberPriceCategoryWhenListIsNull() throws Throwable {
        // arrange
        PowerMockito.mockStatic(Lion.class);
        PowerMockito.when(Lion.getList(anyString(), anyString(), eq(Integer.class), any(List.class))).thenReturn(null);
        // act
        boolean result = LionConfigUtils.judgeMemberPriceCategory(123);
        // assert
        assertFalse("Should return false when memberPriceCategoryIds is null", result);
        // verify
        PowerMockito.verifyStatic(Lion.class);
        Lion.getList(anyString(), anyString(), eq(Integer.class), any(List.class));
    }
}
