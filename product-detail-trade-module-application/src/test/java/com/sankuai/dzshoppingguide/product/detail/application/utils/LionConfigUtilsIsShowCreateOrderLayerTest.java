package com.sankuai.dzshoppingguide.product.detail.application.utils;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.powermock.api.mockito.PowerMockito.mockStatic;
import static org.powermock.api.mockito.PowerMockito.when;
import com.dianping.lion.client.Lion;
import com.sankuai.dzshoppingguide.product.detail.application.constants.LionConstants;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

/**
 * Test class for LionConfigUtils.isShowCreateOrderLayer method
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest(Lion.class)
@PowerMockIgnore({ "javax.management.*", "javax.net.ssl.*" })
public class LionConfigUtilsIsShowCreateOrderLayerTest {

    private static final String APP_KEY = "com.sankuai.dzu.tpbase.dztgdetailweb";

    private static final String SERVICE_TYPE = "serviceType";

    private static final int CATEGORY_ID = 1;

    @Before
    public void setUp() {
        mockStatic(Lion.class);
    }

    /**
     * Test scenario: No configuration exists
     * Expected: Should return false
     */
    @Test
    public void testIsShowCreateOrderLayer_NoConfig() throws Throwable {
        // Given
        Map<String, MultiServiceTypeSwitchConfig> emptyConfig = new HashMap<>();
        when(Lion.getMap(APP_KEY, LionConstants.SHOW_CREATE_ORDER_LAYER_DEAL_CATEGORY_SERVICE_TYPE_WHITE, MultiServiceTypeSwitchConfig.class, Collections.emptyMap())).thenReturn(emptyConfig);
        // When
        boolean result = LionConfigUtils.isShowCreateOrderLayer(CATEGORY_ID, SERVICE_TYPE);
        // Then
        assertFalse("Should return false when no configuration exists", result);
    }

    /**
     * Test scenario: Category ID not found in configuration
     * Expected: Should return false
     */
    @Test
    public void testIsShowCreateOrderLayer_NoCategoryIdConfig() throws Throwable {
        // Given
        Map<String, MultiServiceTypeSwitchConfig> config = new HashMap<>();
        config.put("2", new MultiServiceTypeSwitchConfig());
        when(Lion.getMap(APP_KEY, LionConstants.SHOW_CREATE_ORDER_LAYER_DEAL_CATEGORY_SERVICE_TYPE_WHITE, MultiServiceTypeSwitchConfig.class, Collections.emptyMap())).thenReturn(config);
        // When
        boolean result = LionConfigUtils.isShowCreateOrderLayer(CATEGORY_ID, SERVICE_TYPE);
        // Then
        assertFalse("Should return false when category ID is not in configuration", result);
    }

    /**
     * Test scenario: MultiSku is false (no service types configured)
     * Expected: Should return false
     */
    @Test
    public void testIsShowCreateOrderLayer_IsMultiSkuFalse() throws Throwable {
        // Given
        Map<String, MultiServiceTypeSwitchConfig> config = new HashMap<>();
        MultiServiceTypeSwitchConfig currentConfig = new MultiServiceTypeSwitchConfig();
        currentConfig.setAllServiceType(false);
        currentConfig.setServiceTypes(null);
        config.put(String.valueOf(CATEGORY_ID), currentConfig);
        when(Lion.getMap(APP_KEY, LionConstants.SHOW_CREATE_ORDER_LAYER_DEAL_CATEGORY_SERVICE_TYPE_WHITE, MultiServiceTypeSwitchConfig.class, Collections.emptyMap())).thenReturn(config);
        // When
        boolean result = LionConfigUtils.isShowCreateOrderLayer(CATEGORY_ID, SERVICE_TYPE);
        // Then
        assertFalse("Should return false when MultiSku is false", result);
    }

    /**
     * Test scenario: MultiSku is true (service type matches)
     * Expected: Should return true
     */
    @Test
    public void testIsShowCreateOrderLayer_IsMultiSkuTrue() throws Throwable {
        // Given
        Map<String, MultiServiceTypeSwitchConfig> config = new HashMap<>();
        MultiServiceTypeSwitchConfig currentConfig = new MultiServiceTypeSwitchConfig();
        currentConfig.setAllServiceType(false);
        currentConfig.setServiceTypes(Arrays.asList(SERVICE_TYPE));
        config.put(String.valueOf(CATEGORY_ID), currentConfig);
        when(Lion.getMap(APP_KEY, LionConstants.SHOW_CREATE_ORDER_LAYER_DEAL_CATEGORY_SERVICE_TYPE_WHITE, MultiServiceTypeSwitchConfig.class, Collections.emptyMap())).thenReturn(config);
        // When
        boolean result = LionConfigUtils.isShowCreateOrderLayer(CATEGORY_ID, SERVICE_TYPE);
        // Then
        assertTrue("Should return true when service type matches", result);
    }

    /**
     * Test scenario: AllServiceType is true
     * Expected: Should return true regardless of service type
     */
    @Test
    public void testIsShowCreateOrderLayer_AllServiceTypeTrue() throws Throwable {
        // Given
        Map<String, MultiServiceTypeSwitchConfig> config = new HashMap<>();
        MultiServiceTypeSwitchConfig currentConfig = new MultiServiceTypeSwitchConfig();
        currentConfig.setAllServiceType(true);
        currentConfig.setServiceTypes(null);
        config.put(String.valueOf(CATEGORY_ID), currentConfig);
        when(Lion.getMap(APP_KEY, LionConstants.SHOW_CREATE_ORDER_LAYER_DEAL_CATEGORY_SERVICE_TYPE_WHITE, MultiServiceTypeSwitchConfig.class, Collections.emptyMap())).thenReturn(config);
        // When
        boolean result = LionConfigUtils.isShowCreateOrderLayer(CATEGORY_ID, SERVICE_TYPE);
        // Then
        assertTrue("Should return true when allServiceType is true", result);
    }
}
