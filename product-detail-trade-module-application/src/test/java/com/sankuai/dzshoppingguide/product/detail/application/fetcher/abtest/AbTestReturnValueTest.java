package com.sankuai.dzshoppingguide.product.detail.application.fetcher.abtest;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;
import com.sankuai.dzshoppingguide.product.detail.domain.douhu.AbTestResult;
import java.util.HashMap;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class AbTestReturnValueTest {

    @Mock
    private AbTestResult mockAbTestResult;

    /**
     * 测试当expModuleName存在且AbTestResult不为空时，返回正确的expResult
     */
    @Test
    public void testGetAbTestExpResult_WhenExpModuleNameExists_ReturnsExpResult() throws Throwable {
        // arrange
        String expModuleName = "testModule";
        String expectedResult = "testResult";
        Map<String, AbTestResult> testMap = new HashMap<>();
        testMap.put(expModuleName, new AbTestResult("exp1", expectedResult, null, null));
        AbTestReturnValue abTestReturnValue = new AbTestReturnValue(testMap);
        // act
        String actualResult = abTestReturnValue.getAbTestExpResult(expModuleName);
        // assert
        assertEquals(expectedResult, actualResult);
    }

    /**
     * 测试当expModuleName不存在时，返回空字符串
     */
    @Test
    public void testGetAbTestExpResult_WhenExpModuleNameNotExists_ReturnsEmptyString() throws Throwable {
        // arrange
        String expModuleName = "nonExistModule";
        Map<String, AbTestResult> testMap = new HashMap<>();
        testMap.put("otherModule", new AbTestResult("exp1", "result1", null, null));
        AbTestReturnValue abTestReturnValue = new AbTestReturnValue(testMap);
        // act
        String actualResult = abTestReturnValue.getAbTestExpResult(expModuleName);
        // assert
        assertEquals(StringUtils.EMPTY, actualResult);
    }

    /**
     * 测试当abTestResultMap为null时的情况
     * 注意：根据代码实现，这种情况需要在构造函数中处理，确保map不为null
     */
    @Test
    public void testGetAbTestExpResult_WhenMapIsNull_ReturnsEmptyString() throws Throwable {
        // arrange
        String expModuleName = "testModule";
        // 使用空map替代null
        AbTestReturnValue abTestReturnValue = new AbTestReturnValue(new HashMap<>());
        // act
        String actualResult = abTestReturnValue.getAbTestExpResult(expModuleName);
        // assert
        assertEquals(StringUtils.EMPTY, actualResult);
    }

    /**
     * 测试当expModuleName为null时，返回空字符串
     */
    @Test
    public void testGetAbTestExpResult_WhenExpModuleNameIsNull_ReturnsEmptyString() throws Throwable {
        // arrange
        Map<String, AbTestResult> testMap = new HashMap<>();
        testMap.put("testModule", new AbTestResult("exp1", "result1", null, null));
        AbTestReturnValue abTestReturnValue = new AbTestReturnValue(testMap);
        // act
        String actualResult = abTestReturnValue.getAbTestExpResult(null);
        // assert
        assertEquals(StringUtils.EMPTY, actualResult);
    }

    /**
     * 测试当AbTestResult的expResult为null时，返回null
     */
    @Test
    public void testGetAbTestExpResult_WhenExpResultIsNull_ReturnsNull() throws Throwable {
        // arrange
        String expModuleName = "testModule";
        Map<String, AbTestResult> testMap = new HashMap<>();
        testMap.put(expModuleName, new AbTestResult("exp1", null, null, null));
        AbTestReturnValue abTestReturnValue = new AbTestReturnValue(testMap);
        // act
        String actualResult = abTestReturnValue.getAbTestExpResult(expModuleName);
        // assert
        assertEquals(null, actualResult);
    }

    /**
     * 测试使用Mockito模拟AbTestResult时的行为
     */
    @Test
    public void testGetAbTestExpResult_WithMockedAbTestResult_ReturnsMockedValue() throws Throwable {
        // arrange
        String expModuleName = "mockModule";
        String expectedResult = "mockedResult";
        Map<String, AbTestResult> testMap = new HashMap<>();
        when(mockAbTestResult.getExpResult()).thenReturn(expectedResult);
        testMap.put(expModuleName, mockAbTestResult);
        AbTestReturnValue abTestReturnValue = new AbTestReturnValue(testMap);
        // act
        String actualResult = abTestReturnValue.getAbTestExpResult(expModuleName);
        // assert
        assertEquals(expectedResult, actualResult);
    }
}
