package com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.extend.redirect;

import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.BottomBarActionDataVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.enums.BottomBarActionTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.enums.OpenTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/04/11
 * 神券感知增强吸底条 去购买按钮
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class SuckBottomRedirectActionVO extends BottomBarActionDataVO {

    /**
     * 动作类型
     */
    private  int actionType;

    /**
     * 打开类型
     * redirect=直接跳转
     * modal=打开浮层
     *
     * @see OpenTypeEnum
     */
    private String openType;

    /**
     * 跳转链接
     */
    private String url;

    /**
     * 按钮文案
     */
    private String text;

    /**
     * 图标
     */
    private String icon;



    public SuckBottomRedirectActionVO(BottomBarActionTypeEnum actionType,
                                      OpenTypeEnum openType,
                                      String url,
                                      String text,
                                      String icon) {
        this.actionType = actionType.getCode();
        this.openType = openType.name();
        this.url = url;
        this.text = text;
        this.icon = icon;
    }

}
