package com.sankuai.dzshoppingguide.product.detail.application.builder.navbar.handler;

import static org.junit.Assert.assertEquals;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class NavBarReserveProductHandlerTest {

    private NavBarReserveProductHandler navBarReserveProductHandler = new NavBarReserveProductHandler();

    /**
     * 测试 buildImage 方法，当 image 为空字符串时
     */
    @Test
    public void testBuildImageWhenImageIsEmpty() throws Throwable {
        // arrange
        String image = "";
        // act
        String result = navBarReserveProductHandler.buildImage(image);
        // assert
        assertEquals("", result);
    }

    /**
     * 测试 buildImage 方法，当 image 只包含空格时
     */
    @Test
    public void testBuildImageWhenImageIsBlank() throws Throwable {
        // arrange
        String image = " ";
        // act
        String result = navBarReserveProductHandler.buildImage(image);
        // assert
        assertEquals("", result);
    }

    /**
     * 测试 buildImage 方法，当 image 为非空字符串时
     */
    @Test
    public void testBuildImageWhenImageIsNotBlank() throws Throwable {
        // arrange
        String image = "test";
        // act
        String result = navBarReserveProductHandler.buildImage(image);
        // assert
        assertEquals("test@200w_200h_1e_1c_1l", result);
    }
}
