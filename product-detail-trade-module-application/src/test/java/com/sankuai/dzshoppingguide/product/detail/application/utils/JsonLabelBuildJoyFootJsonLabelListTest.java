package com.sankuai.dzshoppingguide.product.detail.application.utils;

import static org.junit.Assert.assertEquals;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class JsonLabelBuildJoyFootJsonLabelListTest {

    /**
     * 测试buildJoyFootJsonLabelList方法，输入为空字符串
     */
    @Test
    public void testBuildJoyFootJsonLabelListEmptyFormat() throws Throwable {
        // arrange
        String format = "";
        String plainColor = "#FFFFFF";
        String specialColor = "#FFFFFF";
        int plainTextSize = 16;
        int specialTextSize = 16;
        // act
        String result = JsonLabel.buildJoyFootJsonLabelList(format, plainColor, specialColor, plainTextSize, specialTextSize);
        // assert
        assertEquals("[]", result);
    }

    /**
     * 测试buildJoyFootJsonLabelList方法，输入只包含{
     */
    @Test
    public void testBuildJoyFootJsonLabelListOnlyOpenBrace() throws Throwable {
        // arrange
        String format = "{";
        String plainColor = "#FFFFFF";
        String specialColor = "#FFFFFF";
        int plainTextSize = 16;
        int specialTextSize = 16;
        // act
        String result = JsonLabel.buildJoyFootJsonLabelList(format, plainColor, specialColor, plainTextSize, specialTextSize);
        // assert
        assertEquals("[]", result);
    }

    /**
     * 测试buildJoyFootJsonLabelList方法，输入只包含}
     */
    @Test
    public void testBuildJoyFootJsonLabelListOnlyCloseBrace() throws Throwable {
        // arrange
        String format = "}";
        String plainColor = "#FFFFFF";
        String specialColor = "#FFFFFF";
        int plainTextSize = 16;
        int specialTextSize = 16;
        // act
        String result = JsonLabel.buildJoyFootJsonLabelList(format, plainColor, specialColor, plainTextSize, specialTextSize);
        // assert
        assertEquals("[]", result);
    }

    /**
     * 测试buildJoyFootJsonLabelList方法，输入包含{和}，但没有其他字符
     */
    @Test
    public void testBuildJoyFootJsonLabelListBracesOnly() throws Throwable {
        // arrange
        String format = "{}";
        String plainColor = "#FFFFFF";
        String specialColor = "#FFFFFF";
        int plainTextSize = 16;
        int specialTextSize = 16;
        // act
        String result = JsonLabel.buildJoyFootJsonLabelList(format, plainColor, specialColor, plainTextSize, specialTextSize);
        // assert
        assertEquals("[]", result);
    }

    /**
     * 测试buildJoyFootJsonLabelList方法，输入包含{和}，以及其他的字符
     */
    @Test
    public void testBuildJoyFootJsonLabelListWithOtherCharacters() throws Throwable {
        // arrange
        String format = "{abc}";
        String plainColor = "#FFFFFF";
        String specialColor = "#FFFFFF";
        int plainTextSize = 16;
        int specialTextSize = 16;
        // act
        String result = JsonLabel.buildJoyFootJsonLabelList(format, plainColor, specialColor, plainTextSize, specialTextSize);
        // assert
        assertEquals("[{\"backgroundcolor\":\"#00FFFFFF\",\"fontweight\":0,\"strikethrough\":false,\"text\":\"abc\",\"textcolor\":\"#FFFFFF\",\"textsize\":8}]", result);
    }

    /**
     * 测试buildJoyFootJsonLabelList方法，plainColor为空字符串
     */
    @Test
    public void testBuildJoyFootJsonLabelListEmptyPlainColor() throws Throwable {
        // arrange
        String format = "{abc}";
        String plainColor = "";
        String specialColor = "#FFFFFF";
        int plainTextSize = 16;
        int specialTextSize = 16;
        // act
        String result = JsonLabel.buildJoyFootJsonLabelList(format, plainColor, specialColor, plainTextSize, specialTextSize);
        // assert
        assertEquals("[{\"backgroundcolor\":\"#00FFFFFF\",\"fontweight\":0,\"strikethrough\":false,\"text\":\"abc\",\"textcolor\":\"#FFFFFF\",\"textsize\":8}]", result);
    }

    /**
     * 测试buildJoyFootJsonLabelList方法，specialColor为空字符串
     */
    @Test
    public void testBuildJoyFootJsonLabelListEmptySpecialColor() throws Throwable {
        // arrange
        String format = "{abc}";
        String plainColor = "#FFFFFF";
        String specialColor = "";
        int plainTextSize = 16;
        int specialTextSize = 16;
        // act
        String result = JsonLabel.buildJoyFootJsonLabelList(format, plainColor, specialColor, plainTextSize, specialTextSize);
        // assert
        assertEquals("[{\"backgroundcolor\":\"#00FFFFFF\",\"fontweight\":0,\"strikethrough\":false,\"text\":\"abc\",\"textcolor\":\"\",\"textsize\":8}]", result);
    }

    /**
     * 测试buildJoyFootJsonLabelList方法，plainTextSize为0
     */
    @Test
    public void testBuildJoyFootJsonLabelListZeroPlainTextSize() throws Throwable {
        // arrange
        String format = "abc{xyz}def";
        String plainColor = "#FFFFFF";
        String specialColor = "#FFFFFF";
        int plainTextSize = 0;
        int specialTextSize = 16;
        // act
        String result = JsonLabel.buildJoyFootJsonLabelList(format, plainColor, specialColor, plainTextSize, specialTextSize);
        // assert
        String expected = "[" + "{\"backgroundcolor\":\"#00FFFFFF\",\"fontweight\":0,\"strikethrough\":false,\"text\":\"abc\",\"textcolor\":\"#FFFFFF\",\"textsize\":0}," + "{\"backgroundcolor\":\"#00FFFFFF\",\"fontweight\":0,\"strikethrough\":false,\"text\":\"xyz\",\"textcolor\":\"#FFFFFF\",\"textsize\":8}," + "{\"backgroundcolor\":\"#00FFFFFF\",\"fontweight\":0,\"strikethrough\":false,\"text\":\"def\",\"textcolor\":\"#FFFFFF\",\"textsize\":0}" + "]";
        assertEquals(expected, result);
    }

    /**
     * 测试buildJoyFootJsonLabelList方法，specialTextSize为0
     */
    @Test
    public void testBuildJoyFootJsonLabelListZeroSpecialTextSize() throws Throwable {
        // arrange
        String format = "abc{xyz}def";
        String plainColor = "#FFFFFF";
        String specialColor = "#FFFFFF";
        int plainTextSize = 16;
        int specialTextSize = 0;
        // act
        String result = JsonLabel.buildJoyFootJsonLabelList(format, plainColor, specialColor, plainTextSize, specialTextSize);
        // assert
        String expected = "[" + "{\"backgroundcolor\":\"#00FFFFFF\",\"fontweight\":0,\"strikethrough\":false,\"text\":\"abc\",\"textcolor\":\"#FFFFFF\",\"textsize\":8}," + "{\"backgroundcolor\":\"#00FFFFFF\",\"fontweight\":0,\"strikethrough\":false,\"text\":\"xyz\",\"textcolor\":\"#FFFFFF\",\"textsize\":0}," + "{\"backgroundcolor\":\"#00FFFFFF\",\"fontweight\":0,\"strikethrough\":false,\"text\":\"def\",\"textcolor\":\"#FFFFFF\",\"textsize\":8}" + "]";
        assertEquals(expected, result);
    }
}
