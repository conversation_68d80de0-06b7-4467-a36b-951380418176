package com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.component.trade.block.utils;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import java.math.BigDecimal;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
@DisplayName("PriceFormatUtils Test")
public class PriceFormatUtilsTest {

    @Mock
    private PriceDisplayDTO priceDisplayDTO;

    /**
     * Test case for null PriceDisplayDTO input
     */
    @Test
    public void testGetPlainStringWithNullInput() {
        // arrange
        PriceDisplayDTO priceDisplayDTO = null;
        // act
        String result = PriceFormatUtils.getPlainString(priceDisplayDTO);
        // assert
        assertEquals("?", result);
    }

    /**
     * Test case for PriceDisplayDTO with null price
     */
    @Test
    public void testGetPlainStringWithNullPrice() {
        // arrange
        PriceDisplayDTO priceDisplayDTO = mock(PriceDisplayDTO.class);
        when(priceDisplayDTO.getPrice()).thenReturn(null);
        // act
        String result = PriceFormatUtils.getPlainString(priceDisplayDTO);
        // assert
        assertEquals("?", result);
    }

    /**
     * Test case for PriceDisplayDTO with integer price
     */
    @Test
    public void testGetPlainStringWithIntegerPrice() {
        // arrange
        PriceDisplayDTO priceDisplayDTO = mock(PriceDisplayDTO.class);
        when(priceDisplayDTO.getPrice()).thenReturn(new BigDecimal("100"));
        // act
        String result = PriceFormatUtils.getPlainString(priceDisplayDTO);
        // assert
        assertEquals("100", result);
    }

    /**
     * Test case for PriceDisplayDTO with decimal price
     */
    @Test
    public void testGetPlainStringWithDecimalPrice() {
        // arrange
        PriceDisplayDTO priceDisplayDTO = mock(PriceDisplayDTO.class);
        when(priceDisplayDTO.getPrice()).thenReturn(new BigDecimal("99.99"));
        // act
        String result = PriceFormatUtils.getPlainString(priceDisplayDTO);
        // assert
        assertEquals("99.99", result);
    }

    /**
     * Test case for PriceDisplayDTO with price requiring rounding up
     */
    @Test
    public void testGetPlainStringWithRoundingUp() {
        // arrange
        PriceDisplayDTO priceDisplayDTO = mock(PriceDisplayDTO.class);
        when(priceDisplayDTO.getPrice()).thenReturn(new BigDecimal("99.991"));
        // act
        String result = PriceFormatUtils.getPlainString(priceDisplayDTO);
        // assert
        assertEquals("100", result);
    }

    /**
     * Test case for PriceDisplayDTO with price having trailing zeros
     */
    @Test
    public void testGetPlainStringWithTrailingZeros() {
        // arrange
        PriceDisplayDTO priceDisplayDTO = mock(PriceDisplayDTO.class);
        when(priceDisplayDTO.getPrice()).thenReturn(new BigDecimal("100.00"));
        // act
        String result = PriceFormatUtils.getPlainString(priceDisplayDTO);
        // assert
        assertEquals("100", result);
    }

    /**
     * Test case when PriceDisplayDTO is null
     */
    @Test
    @DisplayName("Should return title when PriceDisplayDTO is null")
    public void testFormatPrice_WhenPriceDisplayDTOIsNull() {
        // arrange
        String title = "Test Title";
        // act
        String result = PriceFormatUtils.formatPrice(title, null);
        // assert
        assertEquals(title, result);
    }

    /**
     * Test case when price in PriceDisplayDTO is null
     */
    @Test
    @DisplayName("Should return title when price is null")
    public void testFormatPrice_WhenPriceIsNull() {
        // arrange
        String title = "Test Title";
        when(priceDisplayDTO.getPrice()).thenReturn(null);
        // act
        String result = PriceFormatUtils.formatPrice(title, priceDisplayDTO);
        // assert
        assertEquals(title, result);
    }

    /**
     * Test case with integer price value
     */
    @Test
    @DisplayName("Should format integer price correctly")
    public void testFormatPrice_WithIntegerPrice() {
        // arrange
        String title = "Test Title";
        when(priceDisplayDTO.getPrice()).thenReturn(new BigDecimal("100"));
        // act
        String result = PriceFormatUtils.formatPrice(title, priceDisplayDTO);
        // assert
        assertEquals("Test Title ¥100", result);
    }

    /**
     * Test case with decimal price value
     */
    @Test
    @DisplayName("Should format decimal price correctly")
    public void testFormatPrice_WithDecimalPrice() {
        // arrange
        String title = "Test Title";
        when(priceDisplayDTO.getPrice()).thenReturn(new BigDecimal("99.99"));
        // act
        String result = PriceFormatUtils.formatPrice(title, priceDisplayDTO);
        // assert
        assertEquals("Test Title ¥99.99", result);
    }

    /**
     * Test case with price having trailing zeros
     */
    @Test
    @DisplayName("Should remove trailing zeros from price")
    public void testFormatPrice_WithTrailingZeros() {
        // arrange
        String title = "Test Title";
        when(priceDisplayDTO.getPrice()).thenReturn(new BigDecimal("100.00"));
        // act
        String result = PriceFormatUtils.formatPrice(title, priceDisplayDTO);
        // assert
        assertEquals("Test Title ¥100", result);
    }

    /**
     * Test case with price requiring rounding up
     */
    @Test
    @DisplayName("Should round up price correctly")
    public void testFormatPrice_WithRoundingUp() {
        // arrange
        String title = "Test Title";
        when(priceDisplayDTO.getPrice()).thenReturn(new BigDecimal("99.991"));
        // act
        String result = PriceFormatUtils.formatPrice(title, priceDisplayDTO);
        // assert
        assertEquals("Test Title ¥100", result);
    }

    /**
     * Test case with empty title
     */
    @Test
    @DisplayName("Should handle empty title correctly")
    public void testFormatPrice_WithEmptyTitle() {
        // arrange
        String title = "";
        when(priceDisplayDTO.getPrice()).thenReturn(new BigDecimal("100"));
        // act
        String result = PriceFormatUtils.formatPrice(title, priceDisplayDTO);
        // assert
        assertEquals(" ¥100", result);
    }

    /**
     * Test case with null title
     */
    @Test
    @DisplayName("Should handle null title correctly")
    public void testFormatPrice_WithNullTitle() {
        // arrange
        when(priceDisplayDTO.getPrice()).thenReturn(new BigDecimal("100"));
        // act
        String result = PriceFormatUtils.formatPrice(null, priceDisplayDTO);
        // assert
        assertEquals("null ¥100", result);
    }
}
