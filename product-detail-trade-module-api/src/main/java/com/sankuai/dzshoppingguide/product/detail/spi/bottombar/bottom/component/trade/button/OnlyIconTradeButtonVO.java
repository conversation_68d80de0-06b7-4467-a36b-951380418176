package com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.component.trade.button;

import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.enums.BottomBarComponentTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.backgroud.BottomBarBackgroundVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.rich.content.RichContentVO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Author: guangyujie
 * @Date: 2025/4/1 13:43
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class OnlyIconTradeButtonVO extends BaseTradeButtonVO {

    /**
     * 主标题
     */
    private List<RichContentVO> mainTitle;

    /**
     * 背景色
     */
    private BottomBarBackgroundVO background;

    @Override
    public int getComponentType() {
        return BottomBarComponentTypeEnum.ONLY_ICON_TRADE_BUTTON.getCode();
    }

}
