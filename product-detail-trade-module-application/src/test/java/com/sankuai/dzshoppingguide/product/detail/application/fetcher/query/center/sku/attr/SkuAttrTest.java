package com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.attr;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSON;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class SkuAttrTest {

    @Mock
    private AttrDTO attrDTO;

    @Test
    public void testGetSkuAttrValueJsonWithValidData() {
        // arrange
        Map<String, AttrDTO> attrMap = new HashMap<>();
        when(attrDTO.getValue()).thenReturn(Arrays.asList("value1", "value2"));
        attrMap.put("testAttr", attrDTO);
        Map<Long, Map<String, AttrDTO>> skuAttrMap = new HashMap<>();
        skuAttrMap.put(1L, attrMap);
        SkuAttr skuAttr = new SkuAttr(skuAttrMap);
        // act
        String result = skuAttr.getSkuAttrValueJson(1L, "testAttr");
        // assert
        assertEquals("[\"value1\",\"value2\"]", result);
    }

    /**
     * Test case for null skuAttr map
     */
    @Test
    public void testGetSkuAttrValueJsonWithNullMap() {
        // arrange
        SkuAttr skuAttr = new SkuAttr(null);
        // act
        String result = skuAttr.getSkuAttrValueJson(1L, "testAttr");
        // assert
        assertNull(result);
    }

    /**
     * Test case for non-existent skuId
     */
    @Test
    public void testGetSkuAttrValueJsonWithNonExistentSkuId() {
        // arrange
        Map<Long, Map<String, AttrDTO>> skuAttrMap = new HashMap<>();
        SkuAttr skuAttr = new SkuAttr(skuAttrMap);
        // act
        String result = skuAttr.getSkuAttrValueJson(1L, "testAttr");
        // assert
        assertNull(result);
    }

    /**
     * Test case for non-existent attribute name
     */
    @Test
    public void testGetSkuAttrValueJsonWithNonExistentAttrName() {
        // arrange
        Map<String, AttrDTO> attrMap = new HashMap<>();
        Map<Long, Map<String, AttrDTO>> skuAttrMap = new HashMap<>();
        skuAttrMap.put(1L, attrMap);
        SkuAttr skuAttr = new SkuAttr(skuAttrMap);
        // act
        String result = skuAttr.getSkuAttrValueJson(1L, "testAttr");
        // assert
        assertNull(result);
    }

    /**
     * Test case for null value list in AttrDTO
     */
    @Test
    public void testGetSkuAttrValueJsonWithNullValueList() {
        // arrange
        Map<String, AttrDTO> attrMap = new HashMap<>();
        when(attrDTO.getValue()).thenReturn(null);
        attrMap.put("testAttr", attrDTO);
        Map<Long, Map<String, AttrDTO>> skuAttrMap = new HashMap<>();
        skuAttrMap.put(1L, attrMap);
        SkuAttr skuAttr = new SkuAttr(skuAttrMap);
        // act
        String result = skuAttr.getSkuAttrValueJson(1L, "testAttr");
        // assert
        assertNull(result);
    }

    /**
     * Test case for empty value list in AttrDTO
     */
    @Test
    public void testGetSkuAttrValueJsonWithEmptyValueList() {
        // arrange
        Map<String, AttrDTO> attrMap = new HashMap<>();
        when(attrDTO.getValue()).thenReturn(Collections.emptyList());
        attrMap.put("testAttr", attrDTO);
        Map<Long, Map<String, AttrDTO>> skuAttrMap = new HashMap<>();
        skuAttrMap.put(1L, attrMap);
        SkuAttr skuAttr = new SkuAttr(skuAttrMap);
        // act
        String result = skuAttr.getSkuAttrValueJson(1L, "testAttr");
        // assert
        assertNull(result);
    }

    /**
     * Test case for single value in AttrDTO
     */
    @Test
    public void testGetSkuAttrValueJsonWithSingleValue() {
        // arrange
        Map<String, AttrDTO> attrMap = new HashMap<>();
        when(attrDTO.getValue()).thenReturn(Collections.singletonList("singleValue"));
        attrMap.put("testAttr", attrDTO);
        Map<Long, Map<String, AttrDTO>> skuAttrMap = new HashMap<>();
        skuAttrMap.put(1L, attrMap);
        SkuAttr skuAttr = new SkuAttr(skuAttrMap);
        // act
        String result = skuAttr.getSkuAttrValueJson(1L, "testAttr");
        // assert
        assertEquals("[\"singleValue\"]", result);
    }

    /**
     * Test getting first value when attribute exists with multiple values
     */
    @Test
    @DisplayName("Should return first value when multiple values exist")
    public void testGetSkuAttrFirstValue_WithMultipleValues() {
        // arrange
        Map<Long, Map<String, AttrDTO>> skuAttrMap = new HashMap<>();
        Map<String, AttrDTO> attrMap = new HashMap<>();
        when(attrDTO.getValue()).thenReturn(Arrays.asList("value1", "value2"));
        attrMap.put("attr1", attrDTO);
        skuAttrMap.put(1L, attrMap);
        SkuAttr skuAttr = new SkuAttr(skuAttrMap);
        // act
        String result = skuAttr.getSkuAttrFirstValue(1L, "attr1");
        // assert
        assertEquals("value1", result);
    }

    /**
     * Test with null skuAttr map
     */
    @Test
    @DisplayName("Should return null when skuAttr map is null")
    public void testGetSkuAttrFirstValue_WithNullMap() {
        // arrange
        SkuAttr skuAttr = new SkuAttr(null);
        // act
        String result = skuAttr.getSkuAttrFirstValue(1L, "attr1");
        // assert
        assertNull(result);
    }

    /**
     * Test with non-existent skuId
     */
    @Test
    @DisplayName("Should return null when skuId does not exist")
    public void testGetSkuAttrFirstValue_NonExistentSkuId() {
        // arrange
        Map<Long, Map<String, AttrDTO>> skuAttrMap = new HashMap<>();
        SkuAttr skuAttr = new SkuAttr(skuAttrMap);
        // act
        String result = skuAttr.getSkuAttrFirstValue(999L, "attr1");
        // assert
        assertNull(result);
    }

    /**
     * Test with non-existent attribute name
     */
    @Test
    @DisplayName("Should return null when attribute name does not exist")
    public void testGetSkuAttrFirstValue_NonExistentAttrName() {
        // arrange
        Map<Long, Map<String, AttrDTO>> skuAttrMap = new HashMap<>();
        Map<String, AttrDTO> attrMap = new HashMap<>();
        skuAttrMap.put(1L, attrMap);
        SkuAttr skuAttr = new SkuAttr(skuAttrMap);
        // act
        String result = skuAttr.getSkuAttrFirstValue(1L, "nonExistentAttr");
        // assert
        assertNull(result);
    }

    /**
     * Test with empty value list
     */
    @Test
    @DisplayName("Should return null when value list is empty")
    public void testGetSkuAttrFirstValue_EmptyValueList() {
        // arrange
        Map<Long, Map<String, AttrDTO>> skuAttrMap = new HashMap<>();
        Map<String, AttrDTO> attrMap = new HashMap<>();
        when(attrDTO.getValue()).thenReturn(Collections.emptyList());
        attrMap.put("attr1", attrDTO);
        skuAttrMap.put(1L, attrMap);
        SkuAttr skuAttr = new SkuAttr(skuAttrMap);
        // act
        String result = skuAttr.getSkuAttrFirstValue(1L, "attr1");
        // assert
        assertNull(result);
    }

    /**
     * Test with null value list
     */
    @Test
    @DisplayName("Should return null when value list is null")
    public void testGetSkuAttrFirstValue_NullValueList() {
        // arrange
        Map<Long, Map<String, AttrDTO>> skuAttrMap = new HashMap<>();
        Map<String, AttrDTO> attrMap = new HashMap<>();
        when(attrDTO.getValue()).thenReturn(null);
        attrMap.put("attr1", attrDTO);
        skuAttrMap.put(1L, attrMap);
        SkuAttr skuAttr = new SkuAttr(skuAttrMap);
        // act
        String result = skuAttr.getSkuAttrFirstValue(1L, "attr1");
        // assert
        assertNull(result);
    }

    /**
     * Test with single value in list
     */
    @Test
    @DisplayName("Should return the only value when list contains single element")
    public void testGetSkuAttrFirstValue_SingleValue() {
        // arrange
        Map<Long, Map<String, AttrDTO>> skuAttrMap = new HashMap<>();
        Map<String, AttrDTO> attrMap = new HashMap<>();
        when(attrDTO.getValue()).thenReturn(Collections.singletonList("singleValue"));
        attrMap.put("attr1", attrDTO);
        skuAttrMap.put(1L, attrMap);
        SkuAttr skuAttr = new SkuAttr(skuAttrMap);
        // act
        String result = skuAttr.getSkuAttrFirstValue(1L, "attr1");
        // assert
        assertEquals("singleValue", result);
    }

    /**
     * Test getSkuAttrValue when all conditions are met
     * Should return the value list successfully
     */
    @Test
    public void testGetSkuAttrValueSuccess() {
        // arrange
        List<String> expectedValues = Arrays.asList("value1", "value2");
        when(attrDTO.getValue()).thenReturn(expectedValues);
        Map<String, AttrDTO> attrMap = new HashMap<>();
        attrMap.put("testAttr", attrDTO);
        Map<Long, Map<String, AttrDTO>> skuAttr = new HashMap<>();
        skuAttr.put(1L, attrMap);
        SkuAttr subject = new SkuAttr(skuAttr);
        // act
        List<String> result = subject.getSkuAttrValue(1L, "testAttr");
        // assert
        assertEquals(expectedValues, result);
    }

    /**
     * Test getSkuAttrValue with null skuAttr map
     * Should return null
     */
    @Test
    public void testGetSkuAttrValueWithNullMap() {
        // arrange
        SkuAttr subject = new SkuAttr(null);
        // act
        List<String> result = subject.getSkuAttrValue(1L, "testAttr");
        // assert
        assertNull(result);
    }

    /**
     * Test getSkuAttrValue with non-existing skuId
     * Should return null
     */
    @Test
    public void testGetSkuAttrValueWithNonExistingSkuId() {
        // arrange
        Map<Long, Map<String, AttrDTO>> skuAttr = new HashMap<>();
        SkuAttr subject = new SkuAttr(skuAttr);
        // act
        List<String> result = subject.getSkuAttrValue(999L, "testAttr");
        // assert
        assertNull(result);
    }

    /**
     * Test getSkuAttrValue with non-existing attrName
     * Should return null
     */
    @Test
    public void testGetSkuAttrValueWithNonExistingAttrName() {
        // arrange
        Map<String, AttrDTO> attrMap = new HashMap<>();
        Map<Long, Map<String, AttrDTO>> skuAttr = new HashMap<>();
        skuAttr.put(1L, attrMap);
        SkuAttr subject = new SkuAttr(skuAttr);
        // act
        List<String> result = subject.getSkuAttrValue(1L, "nonExistingAttr");
        // assert
        assertNull(result);
    }

    /**
     * Test getSkuAttrValue with AttrDTO returning null value list
     * Should return null
     */
    @Test
    public void testGetSkuAttrValueWithNullValueList() {
        // arrange
        when(attrDTO.getValue()).thenReturn(null);
        Map<String, AttrDTO> attrMap = new HashMap<>();
        attrMap.put("testAttr", attrDTO);
        Map<Long, Map<String, AttrDTO>> skuAttr = new HashMap<>();
        skuAttr.put(1L, attrMap);
        SkuAttr subject = new SkuAttr(skuAttr);
        // act
        List<String> result = subject.getSkuAttrValue(1L, "testAttr");
        // assert
        assertNull(result);
    }

    /**
     * Test getSkuAttrValue with empty value list from AttrDTO
     * Should return empty list
     */
    @Test
    public void testGetSkuAttrValueWithEmptyValueList() {
        // arrange
        List<String> emptyList = Arrays.asList();
        when(attrDTO.getValue()).thenReturn(emptyList);
        Map<String, AttrDTO> attrMap = new HashMap<>();
        attrMap.put("testAttr", attrDTO);
        Map<Long, Map<String, AttrDTO>> skuAttr = new HashMap<>();
        skuAttr.put(1L, attrMap);
        SkuAttr subject = new SkuAttr(skuAttr);
        // act
        List<String> result = subject.getSkuAttrValue(1L, "testAttr");
        // assert
        assertEquals(emptyList, result);
    }

    /**
     * Test when skuAttr map is null
     */
    @Test
    public void testGetSkuAttrsWhenSkuAttrMapIsNull() throws Throwable {
        // arrange
        SkuAttr skuAttr = new SkuAttr(null);
        // act
        Map<String, AttrDTO> result = skuAttr.getSkuAttrs(1L);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test when skuId parameter is null
     */
    @Test
    public void testGetSkuAttrsWhenSkuIdIsNull() throws Throwable {
        // arrange
        Map<Long, Map<String, AttrDTO>> skuAttrMap = new HashMap<>();
        SkuAttr skuAttr = new SkuAttr(skuAttrMap);
        // act
        Map<String, AttrDTO> result = skuAttr.getSkuAttrs(null);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test when skuId doesn't exist in skuAttr map
     */
    @Test
    public void testGetSkuAttrsWhenSkuIdNotExists() throws Throwable {
        // arrange
        Map<Long, Map<String, AttrDTO>> skuAttrMap = new HashMap<>();
        Map<String, AttrDTO> attrMap = new HashMap<>();
        skuAttrMap.put(1L, attrMap);
        SkuAttr skuAttr = new SkuAttr(skuAttrMap);
        // act
        Map<String, AttrDTO> result = skuAttr.getSkuAttrs(2L);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test when skuId exists with valid mapping
     */
    @Test
    public void testGetSkuAttrsWhenSkuIdExists() throws Throwable {
        // arrange
        Map<Long, Map<String, AttrDTO>> skuAttrMap = new HashMap<>();
        Map<String, AttrDTO> attrMap = new HashMap<>();
        when(attrDTO.getName()).thenReturn("testAttr");
        attrMap.put("testAttr", attrDTO);
        skuAttrMap.put(1L, attrMap);
        SkuAttr skuAttr = new SkuAttr(skuAttrMap);
        // act
        Map<String, AttrDTO> result = skuAttr.getSkuAttrs(1L);
        // assert
        assertNotNull(result);
        assertFalse(result.isEmpty());
        assertEquals(1, result.size());
        assertEquals(attrDTO, result.get("testAttr"));
        assertEquals("testAttr", attrDTO.getName());
    }

    /**
     * Test when skuId exists but has empty attribute map
     */
    @Test
    public void testGetSkuAttrsWhenSkuIdExistsWithEmptyMap() throws Throwable {
        // arrange
        Map<Long, Map<String, AttrDTO>> skuAttrMap = new HashMap<>();
        skuAttrMap.put(1L, new HashMap<>());
        SkuAttr skuAttr = new SkuAttr(skuAttrMap);
        // act
        Map<String, AttrDTO> result = skuAttr.getSkuAttrs(1L);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test when skuId exists with multiple attributes
     */
    @Test
    public void testGetSkuAttrsWhenSkuIdExistsWithMultipleAttrs() throws Throwable {
        // arrange
        Map<Long, Map<String, AttrDTO>> skuAttrMap = new HashMap<>();
        Map<String, AttrDTO> attrMap = new HashMap<>();
        AttrDTO attr1 = new AttrDTO();
        attr1.setName("attr1");
        AttrDTO attr2 = new AttrDTO();
        attr2.setName("attr2");
        attrMap.put("attr1", attr1);
        attrMap.put("attr2", attr2);
        skuAttrMap.put(1L, attrMap);
        SkuAttr skuAttr = new SkuAttr(skuAttrMap);
        // act
        Map<String, AttrDTO> result = skuAttr.getSkuAttrs(1L);
        // assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.containsKey("attr1"));
        assertTrue(result.containsKey("attr2"));
        assertEquals(attr1, result.get("attr1"));
        assertEquals(attr2, result.get("attr2"));
    }

    /**
     * Test when skuAttr map is null
     */
    @Test
    @DisplayName("Should return empty Optional when skuAttr map is null")
    public void testGetSkuAttr_WhenSkuAttrMapIsNull() throws Throwable {
        // arrange
        SkuAttr skuAttr = new SkuAttr(null);
        // act
        Optional<AttrDTO> result = skuAttr.getSkuAttr(1L, "test");
        // assert
        assertNotNull(result);
        assertFalse(result.isPresent());
    }

    /**
     * Test when skuId does not exist in the map
     */
    @Test
    @DisplayName("Should return empty Optional when skuId does not exist")
    public void testGetSkuAttr_SkuIdNotExists() throws Throwable {
        // arrange
        Map<Long, Map<String, AttrDTO>> skuAttrMap = new HashMap<>();
        SkuAttr skuAttr = new SkuAttr(skuAttrMap);
        // act
        Optional<AttrDTO> result = skuAttr.getSkuAttr(1L, "test");
        // assert
        assertNotNull(result);
        assertFalse(result.isPresent());
    }

    /**
     * Test when attrName does not exist for the given skuId
     */
    @Test
    @DisplayName("Should return empty Optional when attrName does not exist for skuId")
    public void testGetSkuAttr_WhenAttrNameNotExists() throws Throwable {
        // arrange
        Map<Long, Map<String, AttrDTO>> skuAttrMap = new HashMap<>();
        Map<String, AttrDTO> attrMap = new HashMap<>();
        skuAttrMap.put(1L, attrMap);
        SkuAttr skuAttr = new SkuAttr(skuAttrMap);
        // act
        Optional<AttrDTO> result = skuAttr.getSkuAttr(1L, "test");
        // assert
        assertNotNull(result);
        assertFalse(result.isPresent());
    }

    /**
     * Test successful retrieval of AttrDTO
     */
    @Test
    @DisplayName("Should return Optional containing AttrDTO when attribute exists")
    public void testGetSkuAttr_WhenAttrExists() throws Throwable {
        // arrange
        Map<Long, Map<String, AttrDTO>> skuAttrMap = new HashMap<>();
        Map<String, AttrDTO> attrMap = new HashMap<>();
        attrMap.put("test", attrDTO);
        skuAttrMap.put(1L, attrMap);
        SkuAttr skuAttr = new SkuAttr(skuAttrMap);
        // act
        Optional<AttrDTO> result = skuAttr.getSkuAttr(1L, "test");
        // assert
        assertNotNull(result);
        assertTrue(result.isPresent());
        // Removed the verification of mockAttrDTO.getName() being called
    }

    /**
     * Test with empty string as attrName
     */
    @Test
    @DisplayName("Should handle empty string as attrName")
    public void testGetSkuAttr_WithEmptyAttrName() throws Throwable {
        // arrange
        Map<Long, Map<String, AttrDTO>> skuAttrMap = new HashMap<>();
        Map<String, AttrDTO> attrMap = new HashMap<>();
        skuAttrMap.put(1L, attrMap);
        SkuAttr skuAttr = new SkuAttr(skuAttrMap);
        // act
        Optional<AttrDTO> result = skuAttr.getSkuAttr(1L, "");
        // assert
        assertNotNull(result);
        assertFalse(result.isPresent());
    }

    /**
     * Test with negative skuId
     */
    @Test
    @DisplayName("Should handle negative skuId")
    public void testGetSkuAttr_WithNegativeSkuId() throws Throwable {
        // arrange
        Map<Long, Map<String, AttrDTO>> skuAttrMap = new HashMap<>();
        SkuAttr skuAttr = new SkuAttr(skuAttrMap);
        // act
        Optional<AttrDTO> result = skuAttr.getSkuAttr(-1L, "test");
        // assert
        assertNotNull(result);
        assertFalse(result.isPresent());
    }
}
