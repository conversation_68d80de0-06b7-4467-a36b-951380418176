package com.sankuai.dzshoppingguide.product.detail.application.builder.similargoods;

import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.producttheme.ProductThemeResult;
import com.sankuai.dzshoppingguide.product.detail.spi.similargoods.vo.DealTinyInfoVO;
import com.sankuai.dzshoppingguide.product.detail.spi.similargoods.vo.dtos.DzProductVO;
import com.sankuai.dzshoppingguide.product.detail.spi.similargoods.vo.SimilarStyleGoods;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @create 2025/5/26 10:40
 */
@Component
public class ListingResponseBuilder {
    public SimilarStyleGoods buildResponse(ProductDetailPageRequest request, DzProductVO originalProductVO, List<DzProductVO> dzProductVOList, ProductThemeResult similarRecommendProducts) {

        int total = Objects.nonNull(similarRecommendProducts) ? similarRecommendProducts.getTotal() : 0;
        boolean hasNext = Objects.nonNull(similarRecommendProducts) ? similarRecommendProducts.isHasNext() : false;
        SimilarStyleGoods similarStyleGoods = new SimilarStyleGoods();
        similarStyleGoods.setHasNext(hasNext);
        similarStyleGoods.setTotalCount(total);
        similarStyleGoods.setMoreText(buildMoreText(total));
        similarStyleGoods.setCurrentDealInfo(buildDealTinyInfoVO(request, originalProductVO));
        similarStyleGoods.setProductItems(dzProductVOList);
        similarStyleGoods.setModuleTitle("同款好价");
        similarStyleGoods.setPopUpTitle("比价助手");
        similarStyleGoods.setPopUpModuleTitle("查看更多商品");
        return similarStyleGoods;
    }

    public DealTinyInfoVO buildDealTinyInfoVO(ProductDetailPageRequest request, DzProductVO productVO) {
        if (productVO == null) {
            return null;
        }
        DealTinyInfoVO dealTinyInfoVO = new DealTinyInfoVO();
        dealTinyInfoVO.setDealGroupId(Math.toIntExact(productVO.getLongProductId()));
        dealTinyInfoVO.setTitle(productVO.getTitle());
        dealTinyInfoVO.setMarketPrice(productVO.getMarketPrice());
        dealTinyInfoVO.setFinalPrice(productVO.getSalePrice());
        dealTinyInfoVO.setDiscount(productVO.getDiscountTag());
        dealTinyInfoVO.setBtnText(productVO.getButtonName());
        dealTinyInfoVO.setHeadPic(productVO.getPic());
        dealTinyInfoVO.setDirectBuyJumpUrl(productVO.getButtonJumpUrl());
        dealTinyInfoVO.setSaleTag(productVO.getSale());
        return dealTinyInfoVO;
    }

    public String buildMoreText(int totalCount) {
        if(totalCount > 50) {
            return "查看更多50+商品";
        }

        if(0 < totalCount && totalCount<= 10) {
            return "查看更多商品";
        }

        if (totalCount <= 0) {
            return StringUtils.EMPTY;
        }

        int base = totalCount / 10 * 10; // 计算最近的10的倍数
        return "查看更多"+ base + "+商品";

    }
}
