package com.sankuai.dzshoppingguide.product.detail.application.builder.promodetail.config;

import com.sankuai.dzshoppingguide.product.detail.application.builder.promodetail.enums.CouponTypeEnum;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-04-17
 * @desc 优惠浮层配置
 */
@Data
public class PromotionPopUpConfig {
    private String moduleName;
    /**
     * 已享模块名称
     */
    private String usedCouponModuleName;
    /**
     * 排除已享券类型
     */
    private List<CouponTypeEnum> excludeUsedCouponTypes;
    /**
     * 排序已享券
     */
    private List<CouponTypeEnum> sortUsedCouponTypes;
    /**
     * 更多模块名称
     */
    private String moreCouponModuleName;
    /**
     * 排除更多券类型
     */
    private List<CouponTypeEnum> excludeMoreCouponTypes;
    /**
     * 排序更多券
     */
    private List<CouponTypeEnum> sortMoreCouponTypes;
}
