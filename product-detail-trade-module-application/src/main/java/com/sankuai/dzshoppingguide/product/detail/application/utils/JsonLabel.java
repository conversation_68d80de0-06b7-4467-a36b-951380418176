/*
 * Create Author  : l<PERSON><PERSON><PERSON>
 * Create Date    : 2018-09-11
 * Project        : gm-marketing-member-card
 * File Name      : JsonLabelUtils.java
 *
 * Copyright (c) 2010-2015 by Shanghai HanTao Information Co., Ltd.
 * All rights reserved.
 *
 */
package com.sankuai.dzshoppingguide.product.detail.application.utils;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 功能描述:  <p>
 *
 * <AUTHOR> linyikai <p>
 * @version 1.0 2018-09-11
 * @since gm-marketing-member-card 1.0
 */
@Data
@NoArgsConstructor
public class JsonLabel {
    public static final String YELLOW_TEXT_COLOR = "#F4D19F";
    public static final String GREY_TEXT_COLOR = "#777777";
    public static final String LIGHT_YELLOW = "#ECD8BC";
    public static final String USE_PROMO_TEXT_COLOR = "#B3ECD8BC";
    public static final String YELLOW_TIPS_USE_PROMO_VALUE_COLOR = "#FF6600";
    public static final String USER_CARD_EXPIRED_COLOR= "F4D19F";
    public static final String YELLOW_TIPS_COLOR = "#CCCCCC";
    public static final String MT_YELLOW= "#FF6200";
    public static final String DP_YELLOW= "#FF6633";
    public static final String BROWN_COLOR= "#5F382D";
    public static final String LIGHT_BROWN = "#FFF1D8";
    public static final String DARK_GRAY = "#222222";
    public static final String STOOL_RED = "#FF4B10";

    public static final String WHITE= "#FFFFFF";
    public static final String LIGHT_BLACK = "#2D2D2D";
    public static final String BLACK = "#111111";
    public static final String RED = "#F6361A";
    public static final String LIGHT_RED = "#F6361A";
    /**
     * 加粗 textStyle
     */
    public static final String BOLD = "Bold";




    public static final int TITLE_TEXT_SIZE = 16;
    public static final int YELLOW_TIPS_TEXT_SIZE = 11;
    public static final int TEXT_SIZE_MEDICAL_BEAUTY = 14;

    private String text;
    private int textsize;
    private String textcolor;
    private String backgroundcolor = "#00FFFFFF";
    private boolean strikethrough = false;
    private String textstyle;
    private int fontweight;

    public static JsonLabel buildJsonLabel(String text, int textsize, String textcolor) {
        JsonLabel jsonLabel = new JsonLabel();
        jsonLabel.setText(text);
        jsonLabel.setTextsize(textsize);
        jsonLabel.setTextcolor(textcolor);
        return jsonLabel;
    }

    public static JsonLabel buildJsonLabel(String text, int textsize, String textcolor, String textstyle) {
        JsonLabel jsonLabel = new JsonLabel();
        jsonLabel.setText(text);
        jsonLabel.setTextsize(textsize);
        jsonLabel.setTextcolor(textcolor);
        jsonLabel.setTextstyle(textstyle);
        return jsonLabel;
    }

    public JsonLabel(String text, int textsize, String textcolor) {
        this.text = text;
        this.textsize = textsize;
        this.textcolor = textcolor;
    }

    public JsonLabel(String text, int textsize, String textcolor, int fontweight) {
        this.text = text;
        this.textsize = textsize;
        this.textcolor = textcolor;
        this.fontweight = fontweight;

    }

    public JsonLabel(String text, int textsize, String textcolor, int fontweight, String backGroundColor) {
        this.text = text;
        this.textsize = textsize;
        this.textcolor = textcolor;
        this.fontweight = fontweight;
        this.backgroundcolor = backGroundColor;
    }

    public static String getJsonString(String text, int textSize, String color) {
        return JSON.toJSONString(new JsonLabel(text, textSize, color));
    }

    public static String convertToTitleJsonLabelStr(String title) {
        List<JsonLabel> jsonLabelList = Lists.newArrayListWithCapacity(1);
        jsonLabelList.add(convertToTitleJsonLabel(title, LIGHT_YELLOW));
        return JSON.toJSONString(jsonLabelList);
    }


    public static JsonLabel convertToTitleJsonLabel(String title, String textcolor) {
        return JsonLabel.buildJsonLabel(title, 13, textcolor);
    }

    /**
     * input：格式化字符串、普通文案格式、特殊文案格式（特殊文案使用{}标志，不允许特殊文案包含特殊文案{{}}）
     * output：Json富文本标签，{}圈住的文案会使用特殊样式
     * 注意：字体大小直接取视觉稿上的字体大小，实际展示到前端的字体大小需要除2
     */
    public static String buildJoyFootJsonLabelList(String format, String plainColor, String specialColor, int plainTextSize, int specialTextSize) {

        List<JsonLabel> labels = new ArrayList<>();
        int index = 0, len = format.length();
        StringBuilder fragment = new StringBuilder();
        while (index < len) {
            if (format.charAt(index) == '{') {
                if (StringUtils.isNotBlank(fragment.toString())) {
                    labels.add(JsonLabel.buildJsonLabel(fragment.toString(), plainTextSize/2, plainColor));
                }
                fragment = new StringBuilder();
            } else if (format.charAt(index) == '}') {
                if (StringUtils.isNotBlank(fragment.toString())) {
                    labels.add(JsonLabel.buildJsonLabel(fragment.toString(), specialTextSize/2, specialColor));
                }
                fragment = new StringBuilder();
            } else {
                fragment.append(format.charAt(index));
            }
            index++;
        }
        if (StringUtils.isNotBlank(fragment.toString())) {
            labels.add(JsonLabel.buildJsonLabel(fragment.toString(), plainTextSize/2, plainColor));
        }
        return JSON.toJSONString(labels);
    }
}
