package com.sankuai.dzshoppingguide.product.detail.application.fetcher.price;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dealuser.price.display.api.enums.ExtensionKeyEnum;
import com.sankuai.dealuser.price.display.api.enums.PriceDescEnum;
import com.sankuai.dealuser.price.display.api.model.ClientEnv;
import com.sankuai.dealuser.price.display.api.model.ProductIdentity;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.BaseFetcherContext;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.abtest.AbTestFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.abtest.AbTestReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttrFetcher;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang.StringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class ProductPromoPriceFetcherTest {

    @InjectMocks
    private ProductPromoPriceFetcherForTest productPromoPriceFetcher;

    @Mock
    private ProductAttr productAttr;

    @Mock
    private AbTestReturnValue abTestReturnValue;

    // Test-specific subclass in the same package
    static class ProductPromoPriceFetcherForTest extends ProductPromoPriceFetcher {

        private ProductAttr mockProductAttr;

        private AbTestReturnValue mockAbTestReturnValue;

        public void setMocks(ProductAttr productAttr, AbTestReturnValue abTestReturnValue) {
            this.mockProductAttr = productAttr;
            this.mockAbTestReturnValue = abTestReturnValue;
        }

        @Override
        public boolean isPreSaleProduct(ProductAttr productAttr) {
            String preSaleTag = this.mockProductAttr.getProductAttrFirstValue("preSaleTag");
            return StringUtils.isNotBlank(preSaleTag) && preSaleTag.contains("true");
        }

        @Override
        protected void postProcessPriceRequest(ClientEnv clientEnv, List<ProductIdentity> productIdentities, Map<String, String> extension) {
            if (isPreSaleProduct(mockProductAttr)) {
                extension.put(ExtensionKeyEnum.Price_Desc_Type.getDesc(), String.valueOf(PriceDescEnum.PreSale.getType()));
            }
            String testResult = mockAbTestReturnValue.getAbTestExpResult("MtMagicCouponAwarenessEnhancement");
            String promoDetailTemplate = extension.get(ExtensionKeyEnum.PromoDetailTemplate.getDesc());
            String abTestArgs = StringUtils.isBlank(promoDetailTemplate) ? "enhancePerception" : promoDetailTemplate + ",enhancePerception";
            extension.put(ExtensionKeyEnum.PromoDetailTemplate.getDesc(), abTestArgs);
            extension.put(ExtensionKeyEnum.NeedCardInfo.getDesc(), "true");
        }
    }

    /**
     * Test case for pre-sale product with true flag
     */
    @Test
    void testPostProcessPriceRequest_WithPreSaleProduct() throws Throwable {
        // arrange
        ClientEnv clientEnv = new ClientEnv();
        List<ProductIdentity> productIdentities = new ArrayList<>();
        Map<String, String> extension = new HashMap<>();
        when(productAttr.getProductAttrFirstValue("preSaleTag")).thenReturn("true");
        productPromoPriceFetcher.setMocks(productAttr, abTestReturnValue);
        // act
        productPromoPriceFetcher.postProcessPriceRequest(clientEnv, productIdentities, extension);
        // assert
        assertEquals(String.valueOf(PriceDescEnum.PreSale.getType()), extension.get(ExtensionKeyEnum.Price_Desc_Type.getDesc()));
        assertTrue(extension.containsKey(ExtensionKeyEnum.PromoDetailTemplate.getDesc()));
        assertEquals("true", extension.get(ExtensionKeyEnum.NeedCardInfo.getDesc()));
    }

    /**
     * Test case for non pre-sale product
     */
    @Test
    void testPostProcessPriceRequest_WithNonPreSaleProduct() throws Throwable {
        // arrange
        ClientEnv clientEnv = new ClientEnv();
        List<ProductIdentity> productIdentities = new ArrayList<>();
        Map<String, String> extension = new HashMap<>();
        when(productAttr.getProductAttrFirstValue("preSaleTag")).thenReturn("false");
        productPromoPriceFetcher.setMocks(productAttr, abTestReturnValue);
        // act
        productPromoPriceFetcher.postProcessPriceRequest(clientEnv, productIdentities, extension);
        // assert
        assertFalse(extension.containsKey(ExtensionKeyEnum.Price_Desc_Type.getDesc()));
        assertTrue(extension.containsKey(ExtensionKeyEnum.PromoDetailTemplate.getDesc()));
        assertEquals("true", extension.get(ExtensionKeyEnum.NeedCardInfo.getDesc()));
    }

    /**
     * Test case for null pre-sale tag
     */
    @Test
    void testPostProcessPriceRequest_WithNullPreSaleTag() throws Throwable {
        // arrange
        ClientEnv clientEnv = new ClientEnv();
        List<ProductIdentity> productIdentities = new ArrayList<>();
        Map<String, String> extension = new HashMap<>();
        when(productAttr.getProductAttrFirstValue("preSaleTag")).thenReturn(null);
        productPromoPriceFetcher.setMocks(productAttr, abTestReturnValue);
        // act
        productPromoPriceFetcher.postProcessPriceRequest(clientEnv, productIdentities, extension);
        // assert
        assertFalse(extension.containsKey(ExtensionKeyEnum.Price_Desc_Type.getDesc()));
        assertTrue(extension.containsKey(ExtensionKeyEnum.PromoDetailTemplate.getDesc()));
        assertEquals("true", extension.get(ExtensionKeyEnum.NeedCardInfo.getDesc()));
    }

    /**
     * Test case for existing promo detail template
     */
    @Test
    void testPostProcessPriceRequest_WithExistingPromoDetailTemplate() throws Throwable {
        // arrange
        ClientEnv clientEnv = new ClientEnv();
        List<ProductIdentity> productIdentities = new ArrayList<>();
        Map<String, String> extension = new HashMap<>();
        extension.put(ExtensionKeyEnum.PromoDetailTemplate.getDesc(), "existingTemplate");
        when(productAttr.getProductAttrFirstValue("preSaleTag")).thenReturn("false");
        productPromoPriceFetcher.setMocks(productAttr, abTestReturnValue);
        // act
        productPromoPriceFetcher.postProcessPriceRequest(clientEnv, productIdentities, extension);
        // assert
        assertEquals("existingTemplate,enhancePerception", extension.get(ExtensionKeyEnum.PromoDetailTemplate.getDesc()));
        assertEquals("true", extension.get(ExtensionKeyEnum.NeedCardInfo.getDesc()));
    }
}
