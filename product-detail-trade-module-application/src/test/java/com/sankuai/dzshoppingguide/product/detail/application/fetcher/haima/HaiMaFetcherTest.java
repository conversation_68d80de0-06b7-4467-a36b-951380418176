package com.sankuai.dzshoppingguide.product.detail.application.fetcher.haima;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.dianping.haima.client.HaimaClient;
import com.dianping.haima.client.request.HaimaRequest;
import com.dianping.haima.client.response.HaimaResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;

@ExtendWith(MockitoExtension.class)
class HaiMaFetcherTest {

    @Mock
    private HaimaClient haimaClient;

    @InjectMocks
    private HaiMaFetcher haiMaFetcher;

    private HaimaRequest request;

    private HaimaResponse successResponse;

    private HaimaResponse failureResponse;

    @BeforeEach
    void setUp() {
        request = new HaimaRequest("testScene");
        successResponse = new HaimaResponse(true, null, "success");
        failureResponse = new HaimaResponse(false, null, "failure");
    }

    /**
     * Tests successful query with successful response from HaimaClient
     */
    @Test
    public void testQueryHaimaConfig_SuccessfulResponse() throws Throwable {
        // arrange
        when(haimaClient.query(request)).thenReturn(successResponse);
        // act
        HaimaResponse result = haiMaFetcher.queryHaimaConfig(request);
        // assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(haimaClient).query(request);
    }

    /**
     * Tests successful query with unsuccessful response from HaimaClient
     */
    @Test
    public void testQueryHaimaConfig_UnsuccessfulResponse() throws Throwable {
        // arrange
        when(haimaClient.query(request)).thenReturn(failureResponse);
        // act
        HaimaResponse result = haiMaFetcher.queryHaimaConfig(request);
        // assert
        assertNotNull(result);
        assertFalse(result.isSuccess());
        verify(haimaClient).query(request);
    }

    /**
     * Tests exception during query to HaimaClient
     */
    @Test
    public void testQueryHaimaConfig_ExceptionThrown() throws Throwable {
        // arrange
        RuntimeException exception = new RuntimeException("Test exception");
        when(haimaClient.query(request)).thenThrow(exception);
        // act
        HaimaResponse result = haiMaFetcher.queryHaimaConfig(request);
        // assert
        assertNull(result);
        verify(haimaClient).query(request);
    }

    /**
     * Tests null response from HaimaClient
     */
    @Test
    public void testQueryHaimaConfig_NullResponse() throws Throwable {
        // arrange
        when(haimaClient.query(request)).thenReturn(null);
        // act
        HaimaResponse result = haiMaFetcher.queryHaimaConfig(request);
        // assert
        assertNull(result);
        verify(haimaClient).query(request);
    }
}
