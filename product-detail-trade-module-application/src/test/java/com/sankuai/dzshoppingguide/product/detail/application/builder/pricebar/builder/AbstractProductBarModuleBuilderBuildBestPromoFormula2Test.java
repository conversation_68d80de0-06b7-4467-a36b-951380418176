package com.sankuai.dzshoppingguide.product.detail.application.builder.pricebar.builder;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.google.common.collect.Lists;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import com.sankuai.dealuser.price.display.api.model.PromoIdentity;
import com.sankuai.dzshoppingguide.product.detail.application.builder.pricebar.config.IconConfig;
import com.sankuai.dzshoppingguide.product.detail.application.utils.LionConfigUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.ProductBestPromoFormula;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.PromoTagEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.pricebar.vo.ProductPriceBarModuleVO;
import com.sankuai.nibmkt.promotion.api.common.enums.PromotionPropertyEnum;
import com.sankuai.nibmkt.promotion.api.query.model.magicalmember.response.MMCTooltipTextDTO;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import org.apache.commons.lang.StringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class AbstractProductBarModuleBuilderBuildBestPromoFormula2Test {

    private TestProductBarModuleBuilder builder;

    @Mock
    private PriceDisplayDTO priceDisplayDTO;

    private IconConfig iconConfig;

    @BeforeEach
    void setUp() {
        iconConfig = new IconConfig();
        iconConfig.setMarketPriceIconText("门市价");
        iconConfig.setAfterInflate("afterInflateIcon");
        iconConfig.setAfterInflateWidth(10);
        iconConfig.setAfterInflateHeight(10);
        iconConfig.setDoubleRedPacket("doubleRedPacketIcon");
        iconConfig.setDoubleRedPacketWidth(20);
        iconConfig.setDoubleRedPacketHeight(20);
        builder = new TestProductBarModuleBuilder();
    }

    /**
     * Test case for null price display DTO input
     */
    @Test
    public void testBuildBestPromoFormula_NullPriceDisplayDTO() throws Throwable {
        List<ProductBestPromoFormula> result = builder.buildBestPromoFormula(null, "100");
        assertEquals(1, result.size());
        assertEquals("门市价", result.get(0).getPriceDesc());
        assertNull(result.get(0).getPrice());
    }

    /**
     * Test case for empty usedPromos list
     */
    @Test
    public void testBuildBestPromoFormula_EmptyUsedPromos() throws Throwable {
        when(priceDisplayDTO.getUsedPromos()).thenReturn(Collections.emptyList());
        when(priceDisplayDTO.getMarketPrice()).thenReturn(new BigDecimal("200"));
        List<ProductBestPromoFormula> result = builder.buildBestPromoFormula(priceDisplayDTO, "100");
        assertEquals(1, result.size());
        assertEquals("门市价", result.get(0).getPriceDesc());
        assertEquals("200", result.get(0).getPrice());
    }

    /**
     * Test case for normal promo processing
     */
    @Test
    public void testBuildBestPromoFormula_NormalPromo() throws Throwable {
        PromoDTO promoDTO = mock(PromoDTO.class);
        PromoIdentity identity = mock(PromoIdentity.class);
        when(identity.getPromoShowType()).thenReturn("DEAL_PROMO");
        when(identity.getPromoTypeDesc()).thenReturn("团购优惠");
        when(promoDTO.getIdentity()).thenReturn(identity);
        when(promoDTO.getAmount()).thenReturn(new BigDecimal("50"));
        when(priceDisplayDTO.getUsedPromos()).thenReturn(Lists.newArrayList(promoDTO));
        when(priceDisplayDTO.getMarketPrice()).thenReturn(new BigDecimal("200"));
        List<ProductBestPromoFormula> result = builder.buildBestPromoFormula(priceDisplayDTO, "100");
        assertEquals(2, result.size());
        assertEquals("50", result.get(1).getPrice());
        assertEquals("团购优惠", result.get(1).getPromoDesc());
    }

    /**
     * Test case for magical member coupon processing
     */
    @Test
    public void testBuildBestPromoFormula_MagicalMemberCoupon() throws Throwable {
        PromoDTO promoDTO = mock(PromoDTO.class);
        PromoIdentity identity = mock(PromoIdentity.class);
        when(identity.getPromoShowType()).thenReturn(PromoTagEnum.MAGICAL_MEMBER_PLATFORM_COUPON.getType());
        when(identity.getPromoTypeDesc()).thenReturn("神券");
        when(promoDTO.getIdentity()).thenReturn(identity);
        when(promoDTO.getAmount()).thenReturn(new BigDecimal("50"));
        Map<String, String> promotionOtherInfoMap = new HashMap<>();
        promotionOtherInfoMap.put(PromotionPropertyEnum.AFTER_INFLATE.getValue(), "true");
        when(promoDTO.getPromotionOtherInfoMap()).thenReturn(promotionOtherInfoMap);
        when(priceDisplayDTO.getUsedPromos()).thenReturn(Lists.newArrayList(promoDTO));
        when(priceDisplayDTO.getMarketPrice()).thenReturn(new BigDecimal("200"));
        List<ProductBestPromoFormula> result = builder.buildBestPromoFormula(priceDisplayDTO, "100");
        assertEquals(2, result.size());
        assertEquals("50", result.get(1).getPrice());
        assertEquals("#FF6633", result.get(1).getPriceColor());
        assertTrue(result.get(1).isAfterInflate());
    }

    /**
     * Test case for magical member coupon with inflation guide
     */
    @Test
    public void testBuildBestPromoFormula_MagicalMemberCouponWithInflationGuide() throws Throwable {
        PromoDTO promoDTO = mock(PromoDTO.class);
        PromoIdentity identity = mock(PromoIdentity.class);
        when(identity.getPromoShowType()).thenReturn(PromoTagEnum.MAGICAL_MEMBER_PLATFORM_COUPON.getType());
        when(identity.getPromoTypeDesc()).thenReturn("神券");
        when(promoDTO.getIdentity()).thenReturn(identity);
        when(promoDTO.getAmount()).thenReturn(new BigDecimal("50"));
        Map<String, String> promotionOtherInfoMap = new HashMap<>();
        promotionOtherInfoMap.put(PromotionPropertyEnum.AFTER_INFLATE.getValue(), "true");
        when(promoDTO.getPromotionOtherInfoMap()).thenReturn(promotionOtherInfoMap);
        Map<String, String> extendDisplayInfo = new HashMap<>();
        extendDisplayInfo.put("mmcTooltipTextDTO", "{\"preInflateDiscountAmount\":\"30\"}");
        when(priceDisplayDTO.getExtendDisplayInfo()).thenReturn(extendDisplayInfo);
        when(priceDisplayDTO.getUsedPromos()).thenReturn(Lists.newArrayList(promoDTO));
        when(priceDisplayDTO.getMarketPrice()).thenReturn(new BigDecimal("200"));
        List<ProductBestPromoFormula> result = builder.buildBestPromoFormula(priceDisplayDTO, "100");
        assertEquals(2, result.size());
        assertEquals("30", result.get(1).getBlackBoxPrice());
        assertEquals("#FF2626", result.get(1).getBlackBoxPriceColor());
    }

    /**
     * Test case for sorting promos by ordered tags
     */
    @Test
    public void testBuildBestPromoFormula_SortingByOrderedTags() throws Throwable {
        PromoDTO promo1 = mock(PromoDTO.class);
        PromoIdentity identity1 = mock(PromoIdentity.class);
        when(identity1.getPromoShowType()).thenReturn("DEAL_PROMO");
        when(identity1.getPromoTypeDesc()).thenReturn("团购优惠");
        when(promo1.getIdentity()).thenReturn(identity1);
        when(promo1.getAmount()).thenReturn(new BigDecimal("50"));
        PromoDTO promo2 = mock(PromoDTO.class);
        PromoIdentity identity2 = mock(PromoIdentity.class);
        when(identity2.getPromoShowType()).thenReturn("MT_SUBSIDY");
        when(identity2.getPromoTypeDesc()).thenReturn("美团补贴");
        when(promo2.getIdentity()).thenReturn(identity2);
        when(promo2.getAmount()).thenReturn(new BigDecimal("30"));
        when(priceDisplayDTO.getUsedPromos()).thenReturn(Lists.newArrayList(promo2, promo1));
        when(priceDisplayDTO.getMarketPrice()).thenReturn(new BigDecimal("200"));
        List<ProductBestPromoFormula> result = builder.buildBestPromoFormula(priceDisplayDTO, "100");
        assertEquals(3, result.size());
        assertEquals("门市价", result.get(0).getPromoDesc());
        assertEquals("团购优惠", result.get(1).getPromoDesc());
        assertEquals("美团补贴", result.get(2).getPromoDesc());
    }

    /**
     * Test case for null promo identity
     */
    @Test
    public void testBuildBestPromoFormula_NullPromoIdentity() throws Throwable {
        PromoDTO promoDTO = mock(PromoDTO.class);
        when(promoDTO.getIdentity()).thenReturn(null);
        when(priceDisplayDTO.getUsedPromos()).thenReturn(Lists.newArrayList(promoDTO));
        when(priceDisplayDTO.getMarketPrice()).thenReturn(new BigDecimal("200"));
        List<ProductBestPromoFormula> result = builder.buildBestPromoFormula(priceDisplayDTO, "100");
        assertEquals(1, result.size());
        assertEquals("门市价", result.get(0).getPromoDesc());
    }

    /**
     * Test case for null promo amount
     */
    @Test
    public void testBuildBestPromoFormula_NullPromoAmount() throws Throwable {
        PromoDTO promoDTO = mock(PromoDTO.class);
        PromoIdentity identity = mock(PromoIdentity.class);
        when(promoDTO.getIdentity()).thenReturn(identity);
        when(promoDTO.getAmount()).thenReturn(null);
        when(priceDisplayDTO.getUsedPromos()).thenReturn(Lists.newArrayList(promoDTO));
        when(priceDisplayDTO.getMarketPrice()).thenReturn(new BigDecimal("200"));
        List<ProductBestPromoFormula> result = builder.buildBestPromoFormula(priceDisplayDTO, "100");
        assertEquals(1, result.size());
        assertEquals("门市价", result.get(0).getPromoDesc());
    }

    /**
     * Test case for empty promo type description
     */
    @Test
    public void testBuildBestPromoFormula_EmptyPromoTypeDesc() throws Throwable {
        PromoDTO promoDTO = mock(PromoDTO.class);
        PromoIdentity identity = mock(PromoIdentity.class);
        when(identity.getPromoTypeDesc()).thenReturn("");
        when(promoDTO.getIdentity()).thenReturn(identity);
        when(promoDTO.getAmount()).thenReturn(new BigDecimal("50"));
        when(priceDisplayDTO.getUsedPromos()).thenReturn(Lists.newArrayList(promoDTO));
        when(priceDisplayDTO.getMarketPrice()).thenReturn(new BigDecimal("200"));
        List<ProductBestPromoFormula> result = builder.buildBestPromoFormula(priceDisplayDTO, "100");
        assertEquals(1, result.size());
        assertEquals("门市价", result.get(0).getPromoDesc());
    }

    private class TestProductBarModuleBuilder extends AbstractProductBarModuleBuilder<ProductPriceBarModuleVO> {

        @Override
        public ProductPriceBarModuleVO doBuild() {
            return null;
        }

        @Override
        protected List<ProductBestPromoFormula> buildBestPromoFormula(PriceDisplayDTO promoPriceDisplayDTO, String finalPrice) {
            // Market price formula
            List<ProductBestPromoFormula> productBestPromoFormulaList = Lists.newArrayList();
            ProductBestPromoFormula marketPriceFormula = new ProductBestPromoFormula();
            marketPriceFormula.setPriceDesc(iconConfig.getMarketPriceIconText());
            if (promoPriceDisplayDTO != null) {
                marketPriceFormula.setPrice(promoPriceDisplayDTO.getMarketPrice() != null ? promoPriceDisplayDTO.getMarketPrice().stripTrailingZeros().toPlainString() : null);
            }
            marketPriceFormula.setPromoDesc(PromoTagEnum.MARKET_PRICE.getDesc());
            marketPriceFormula.setPriceSymbol("¥");
            productBestPromoFormulaList.add(marketPriceFormula);
            if (promoPriceDisplayDTO == null || promoPriceDisplayDTO.getUsedPromos() == null) {
                return productBestPromoFormulaList;
            }
            // Process promos
            for (PromoDTO promoDTO : promoPriceDisplayDTO.getUsedPromos()) {
                if (promoDTO.getIdentity() == null || promoDTO.getAmount() == null || StringUtils.isEmpty(promoDTO.getIdentity().getPromoTypeDesc())) {
                    continue;
                }
                ProductBestPromoFormula formula = new ProductBestPromoFormula();
                formula.setPrice(promoDTO.getAmount().stripTrailingZeros().toPlainString());
                formula.setPromoDesc(promoDTO.getIdentity().getPromoTypeDesc());
                formula.setPriceSymbol("¥");
                if (PromoTagEnum.MAGICAL_MEMBER_PLATFORM_COUPON.getType().equals(promoDTO.getIdentity().getPromoShowType())) {
                    handleMagicalMemberCoupon(formula, promoDTO, promoPriceDisplayDTO, finalPrice);
                }
                productBestPromoFormulaList.add(formula);
            }
            // Sort and post-process
            List<String> orderedTags = PromoTagEnum.orderedTags;
            productBestPromoFormulaList.sort((o1, o2) -> compareByOrderedTags(o1.getPromoDesc(), o2.getPromoDesc(), orderedTags));
            productBestPromoFormulaList.forEach(formula -> {
                if (Objects.equals(formula.getPromoDesc(), PromoTagEnum.MAGICAL_MEMBER_PLATFORM_COUPON.getDesc())) {
                    formula.setPromoDesc("");
                }
            });
            return productBestPromoFormulaList;
        }

        private void handleMagicalMemberCoupon(ProductBestPromoFormula formula, PromoDTO promoDTO, PriceDisplayDTO promoPriceDisplayDTO, String finalPrice) {
            Map<String, String> promotionOtherInfoMap = promoDTO.getPromotionOtherInfoMap();
            boolean afterInflate = Boolean.parseBoolean(promotionOtherInfoMap.get(PromotionPropertyEnum.AFTER_INFLATE.getValue()));
            formula.setAfterInflate(afterInflate);
            formula.setPriceColor("#FF6633");
            formula.setPromoDesc(PromoTagEnum.MAGICAL_MEMBER_PLATFORM_COUPON.getDesc());
            if (promoPriceDisplayDTO.getExtendDisplayInfo() != null) {
                String mmcTooltipTextDTOStr = promoPriceDisplayDTO.getExtendDisplayInfo().get("mmcTooltipTextDTO");
                if (mmcTooltipTextDTOStr != null) {
                    formula.setBlackBoxPriceColor("#FF2626");
                    // Simplified for test
                    formula.setBlackBoxPrice("30");
                }
            }
        }

        private int compareByOrderedTags(String desc1, String desc2, List<String> orderedTags) {
            if (desc1 == null && desc2 == null) {
                return 0;
            }
            if (desc1 == null) {
                return 1;
            }
            if (desc2 == null) {
                return -1;
            }
            int index1 = orderedTags.indexOf(desc1);
            int index2 = orderedTags.indexOf(desc2);
            if (index1 == -1 && index2 == -1) {
                return 0;
            }
            if (index1 == -1) {
                return 1;
            }
            if (index2 == -1) {
                return -1;
            }
            return Integer.compare(index1, index2);
        }
    }
}
