package com.sankuai.dzshoppingguide.product.detail.application.fetcher.abtest;

import com.sankuai.dzshoppingguide.product.detail.application.acl.douhu.AbTestResult;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

/**
 * Unit tests for AbTestReturnValue's getAbTestExpResult method
 */
@ExtendWith(MockitoExtension.class)
public class AbTestReturnValueGetAbTestExpResultTest {

    /**
     * Test when expModuleName is null
     */
    @Test
    @DisplayName("Should return empty string when expModuleName is null")
    public void testGetAbTestExpResult_WhenExpModuleNameIsNull_ReturnsEmptyString() throws Throwable {
        // arrange
        Map<String, AbTestResult> testMap = mock(HashMap.class);
        AbTestReturnValue abTestReturnValue = new AbTestReturnValue(testMap);
        // act
        String actualResult = abTestReturnValue.getAbTestExpResult(null);
        // assert
        assertEquals(StringUtils.EMPTY, actualResult);
    }

    /**
     * Test when expModuleName is empty
     */
    @Test
    @DisplayName("Should return empty string when expModuleName is empty")
    public void testGetAbTestExpResult_WhenExpModuleNameIsEmpty_ReturnsEmptyString() throws Throwable {
        // arrange
        Map<String, AbTestResult> testMap = mock(HashMap.class);
        AbTestReturnValue abTestReturnValue = new AbTestReturnValue(testMap);
        // act
        String actualResult = abTestReturnValue.getAbTestExpResult("");
        // assert
        assertEquals(StringUtils.EMPTY, actualResult);
    }

    /**
     * Test when abTestResultMap is null
     */
    @Test
    @DisplayName("Should return empty string when abTestResultMap is null")
    public void testGetAbTestExpResult_WhenAbTestResultMapIsNull_ReturnsEmptyString() throws Throwable {
        // arrange
        AbTestReturnValue abTestReturnValue = new AbTestReturnValue(null);
        // act
        String actualResult = abTestReturnValue.getAbTestExpResult("testModule");
        // assert
        assertEquals(StringUtils.EMPTY, actualResult);
    }

    /**
     * Test when abTestResultMap is empty
     */
    @Test
    @DisplayName("Should return empty string when abTestResultMap is empty")
    public void testGetAbTestExpResult_WhenAbTestResultMapIsEmpty_ReturnsEmptyString() throws Throwable {
        // arrange
        Map<String, AbTestResult> testMap = mock(HashMap.class);
        when(testMap.isEmpty()).thenReturn(true);
        AbTestReturnValue abTestReturnValue = new AbTestReturnValue(testMap);
        // act
        String actualResult = abTestReturnValue.getAbTestExpResult("testModule");
        // assert
        assertEquals(StringUtils.EMPTY, actualResult);
    }

    /**
     * Test when expModuleName doesn't exist in map
     */
    @Test
    @DisplayName("Should return empty string when expModuleName is not found in map")
    public void testGetAbTestExpResult_WhenExpModuleNameNotFound_ReturnsEmptyString() throws Throwable {
        // arrange
        Map<String, AbTestResult> testMap = mock(HashMap.class);
        when(testMap.isEmpty()).thenReturn(false);
        when(testMap.get("nonExistingModule")).thenReturn(null);
        AbTestReturnValue abTestReturnValue = new AbTestReturnValue(testMap);
        // act
        String actualResult = abTestReturnValue.getAbTestExpResult("nonExistingModule");
        // assert
        assertEquals(StringUtils.EMPTY, actualResult);
    }

    /**
     * Test successful case when all parameters are valid
     */
    @Test
    @DisplayName("Should return expResult when all parameters are valid")
    public void testGetAbTestExpResult_WhenValidParameters_ReturnsExpResult() throws Throwable {
        // arrange
        String expectedResult = "expectedResult";
        AbTestResult abTestResult = mock(AbTestResult.class);
        when(abTestResult.getExpResult()).thenReturn(expectedResult);
        Map<String, AbTestResult> testMap = mock(HashMap.class);
        when(testMap.isEmpty()).thenReturn(false);
        when(testMap.get("testModule")).thenReturn(abTestResult);
        AbTestReturnValue abTestReturnValue = new AbTestReturnValue(testMap);
        // act
        String actualResult = abTestReturnValue.getAbTestExpResult("testModule");
        // assert
        assertEquals(expectedResult, actualResult);
    }

    @Test
    public void testGetAbTestExpBiInfo_WhenExpModuleNameIsBlank() {
        // arrange
        Map<String, AbTestResult> mockMap = mock(Map.class);
        AbTestReturnValue returnValue = new AbTestReturnValue(mockMap);
        // act
        String result = returnValue.getAbTestExpBiInfo("");
        // assert
        assertEquals(StringUtils.EMPTY, result);
        verifyNoInteractions(mockMap);
    }

    @Test
    public void testGetAbTestExpBiInfo_WhenResultMapIsEmpty() {
        // arrange
        Map<String, AbTestResult> mockMap = mock(Map.class);
        when(mockMap.isEmpty()).thenReturn(true);
        AbTestReturnValue returnValue = new AbTestReturnValue(mockMap);
        // act
        String result = returnValue.getAbTestExpBiInfo("test");
        // assert
        assertEquals(StringUtils.EMPTY, result);
        verify(mockMap).isEmpty();
        verifyNoMoreInteractions(mockMap);
    }

    @Test
    public void testGetAbTestExpBiInfo_WhenExpModuleNameNotFound() {
        // arrange
        Map<String, AbTestResult> mockMap = mock(Map.class);
        when(mockMap.isEmpty()).thenReturn(false);
        when(mockMap.get("test")).thenReturn(null);
        AbTestReturnValue returnValue = new AbTestReturnValue(mockMap);
        // act
        String result = returnValue.getAbTestExpBiInfo("test");
        // assert
        assertEquals(StringUtils.EMPTY, result);
        verify(mockMap).isEmpty();
        verify(mockMap).get("test");
        verifyNoMoreInteractions(mockMap);
    }

    @Test
    public void testGetAbTestExpBiInfo_WhenAbTestResultIsNull() {
        // arrange
        Map<String, AbTestResult> mockMap = mock(Map.class);
        when(mockMap.isEmpty()).thenReturn(false);
        when(mockMap.get("test")).thenReturn(null);
        AbTestReturnValue returnValue = new AbTestReturnValue(mockMap);
        // act
        String result = returnValue.getAbTestExpBiInfo("test");
        // assert
        assertEquals(StringUtils.EMPTY, result);
        verify(mockMap).isEmpty();
        verify(mockMap).get("test");
        verifyNoMoreInteractions(mockMap);
    }

    @Test
    public void testGetAbTestExpBiInfo_WhenValidAbTestResult() {
        // arrange
        String expectedBiInfo = "test_bi_info";
        AbTestResult mockResult = mock(AbTestResult.class);
        when(mockResult.getExpBiInfo()).thenReturn(expectedBiInfo);
        Map<String, AbTestResult> mockMap = mock(Map.class);
        when(mockMap.isEmpty()).thenReturn(false);
        when(mockMap.get("test")).thenReturn(mockResult);
        AbTestReturnValue returnValue = new AbTestReturnValue(mockMap);
        // act
        String result = returnValue.getAbTestExpBiInfo("test");
        // assert
        assertEquals(expectedBiInfo, result);
        verify(mockMap).isEmpty();
        verify(mockMap).get("test");
        verify(mockResult).getExpBiInfo();
        verifyNoMoreInteractions(mockMap, mockResult);
    }
}
