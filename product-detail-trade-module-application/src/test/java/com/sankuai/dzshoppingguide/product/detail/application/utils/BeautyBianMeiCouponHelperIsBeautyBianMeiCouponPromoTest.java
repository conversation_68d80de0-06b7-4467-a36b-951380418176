package com.sankuai.dzshoppingguide.product.detail.application.utils;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dealuser.price.display.api.enums.PromoIdentityEnum;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class BeautyBianMeiCouponHelperIsBeautyBianMeiCouponPromoTest {

    @Mock
    private PromoDTO promoDTO;

    /**
     * Test case for a valid Beauty Bian Mei Coupon promo
     */
    @Test
    public void testIsBeautyBianMeiCouponPromoWithValidPromo() throws Throwable {
        // arrange
        when(promoDTO.getPromoIdentity()).thenReturn("beBeautifulGodCoupon");
        // act
        boolean result = BeautyBianMeiCouponHelper.isBeautyBianMeiCouponPromo(promoDTO);
        // assert
        assertTrue(result);
        verify(promoDTO, times(1)).getPromoIdentity();
    }

    /**
     * Test case for a different promo identity
     */
    @Test
    public void testIsBeautyBianMeiCouponPromoWithDifferentPromo() throws Throwable {
        // arrange
        when(promoDTO.getPromoIdentity()).thenReturn("otherPromo");
        // act
        boolean result = BeautyBianMeiCouponHelper.isBeautyBianMeiCouponPromo(promoDTO);
        // assert
        assertFalse(result);
        verify(promoDTO, times(1)).getPromoIdentity();
    }

    /**
     * Test case for empty promo identity
     */
    @Test
    public void testIsBeautyBianMeiCouponPromoWithEmptyPromoIdentity() throws Throwable {
        // arrange
        when(promoDTO.getPromoIdentity()).thenReturn("");
        // act
        boolean result = BeautyBianMeiCouponHelper.isBeautyBianMeiCouponPromo(promoDTO);
        // assert
        assertFalse(result);
        verify(promoDTO, times(1)).getPromoIdentity();
    }

    /**
     * Test case for null promo identity
     */
    @Test
    public void testIsBeautyBianMeiCouponPromoWithNullPromoIdentity() throws Throwable {
        // arrange
        when(promoDTO.getPromoIdentity()).thenReturn(null);
        // act
        boolean result = BeautyBianMeiCouponHelper.isBeautyBianMeiCouponPromo(promoDTO);
        // assert
        assertFalse(result);
        verify(promoDTO, times(1)).getPromoIdentity();
    }

    /**
     * Test case for null PromoDTO
     * Expects NullPointerException since the method doesn't handle null input
     */
    @Test
    public void testIsBeautyBianMeiCouponPromoWithNullPromoDTO() throws Throwable {
        // act & assert
        assertThrows(NullPointerException.class, () -> BeautyBianMeiCouponHelper.isBeautyBianMeiCouponPromo(null));
    }
}
