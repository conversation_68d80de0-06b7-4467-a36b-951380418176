package com.sankuai.dzshoppingguide.product.detail.application.service.promo;

import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import com.sankuai.dzshoppingguide.product.detail.spi.promo.vo.PromoDetailModule;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertEquals;

@RunWith(MockitoJUnitRunner.class)
public class PromoDetailServiceConvertPromoDescTest {

    @InjectMocks
    private PromoDetailService promoDetailService;

    @Mock
    private PromoDTO promoDTO;

    @Mock
    private PromoDetailModule promoDetailModule;

    @Test
    public void testConvertPromoDescWhenPromoShowTypeIsNotMagicalMemberPlatformCoupon() throws Throwable {
        // Stub the behavior of getPromoShowType() on the mock PromoIdentity object
        String result = promoDetailService.convertPromoDesc(promoDTO, promoDetailModule, "promoTag");
        assertEquals("团购优惠", result);
    }

    @Test
    public void testConvertPromoDescWhenPromoDTOIsNull() throws Throwable {
        String result = promoDetailService.convertPromoDesc(null, promoDetailModule, "promoTag");
        assertEquals("团购优惠", result);
    }

    @Test
    public void testConvertPromoDescWhenPromoDetailModuleIsNull() throws Throwable {
        String result = promoDetailService.convertPromoDesc(promoDTO, null, "promoTag");
        assertEquals("团购优惠", result);
    }

    @Test
    public void testConvertPromoDescWhenPromoTagIsNull() throws Throwable {
        String result = promoDetailService.convertPromoDesc(promoDTO, promoDetailModule, null);
        assertEquals("团购优惠", result);
    }
}
