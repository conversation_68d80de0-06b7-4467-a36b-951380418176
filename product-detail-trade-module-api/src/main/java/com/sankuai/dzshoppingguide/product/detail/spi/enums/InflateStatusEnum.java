package com.sankuai.dzshoppingguide.product.detail.spi.enums;

/**
 * 功能描述:  <p>
 *
 * <AUTHOR> lint<PERSON><PERSON><PERSON> <p>
 * @version 2024/3/20
 * @since mapi-dztgdetail-web
 */
//神券膨胀状态
public enum InflateStatusEnum {

    NO_INFLATE(0, "未膨胀"),
    AFTER_INFLATE(1, "已膨胀");


    final private int code;
    final private String name;

    InflateStatusEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }



}
