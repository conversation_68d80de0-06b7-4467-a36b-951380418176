package com.sankuai.dzshoppingguide.product.detail.application.fetcher.price;

import com.sankuai.dealuser.price.display.api.enums.ExtensionKeyEnum;
import com.sankuai.dealuser.price.display.api.enums.PriceDescEnum;
import com.sankuai.dealuser.price.display.api.enums.RequestSceneEnum;
import com.sankuai.dealuser.price.display.api.model.BatchPriceRequest;
import com.sankuai.dealuser.price.display.api.model.ClientEnv;
import com.sankuai.dealuser.price.display.api.model.ProductIdentity;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.abtest.AbTestFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.abtest.AbTestReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategoryFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.membercard.card.integration.IntegrationMemberCard;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.membercard.card.integration.IntegrationMemberCardFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.membercard.card.integration.IntegrationMemberCardNoEntranceFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.dto.ProductPriceReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttrFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.defaultselect.SkuDefaultSelect;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.defaultselect.SkuDefaultSelectFetcher;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @date 2025-03-06
 * @desc 团单立减查询
 */
@Fetcher(
        previousLayerDependencies = {
                ProductAttrFetcher.class,
                ProductCategoryFetcher.class,
                SkuDefaultSelectFetcher.class,
                AbTestFetcher.class,
                IntegrationMemberCardFetcher.class
        }
)
public class ProductPromoPriceFetcher extends AbstractProductPriceFetcher<ProductPriceReturnValue> {

    private final List<String> MAGIC_ENHANCEMENT_TEST_RESULTS = Arrays.asList("c","d"); // 一、二期实验结果
    private static final String MT_MAGIC_COUPON_PROMO_DETAIL_TEMPLATE ="enhancePerception";

    @Override
    protected CompletableFuture<ProductPriceReturnValue> doFetch() {
        SkuDefaultSelect skuDefaultSelect = getDependencyResult(SkuDefaultSelectFetcher.class);
        CompletableFuture<ProductPriceReturnValue> dealPromoPriceDisplayResult = buildDealPromoPriceDisplayReturnValue(request, skuDefaultSelect);
        ProductCategory productCategory = getDependencyResult(ProductCategoryFetcher.class);
        IntegrationMemberCard integrationMemberCard = getDependencyResult(IntegrationMemberCardFetcher.class);
        CompletableFuture<ProductPriceReturnValue> merchantMemberPromoPriceResult = buildMerchantMemberPromoPriceDisplayReturnValue(request, skuDefaultSelect);
        return CompletableFuture.allOf(dealPromoPriceDisplayResult, merchantMemberPromoPriceResult).thenApply(v -> {
            ProductPriceReturnValue dealPromoPriceDisplayReturnValue = dealPromoPriceDisplayResult.join();
            ProductPriceReturnValue merchantMemberPromoPriceDisplayReturnValue = merchantMemberPromoPriceResult.join();
            // 门店类目和团单类目限制
            if (isMerchantMember(request.getClientTypeEnum(), productCategory.getProductSecondCategoryId()) && needCompare(integrationMemberCard)) {
                // 比价
                if (useMerchantMemberPrice(merchantMemberPromoPriceDisplayReturnValue, dealPromoPriceDisplayReturnValue, request.getProductId())) {
                    merchantMemberPromoPriceDisplayReturnValue.setMemberPromoPrice(true);
                    merchantMemberPromoPriceDisplayReturnValue.setOriginPriceDisplayDTO(dealPromoPriceDisplayReturnValue.getPriceDisplayDTO());
                    return merchantMemberPromoPriceDisplayReturnValue;
                }
            }
            dealPromoPriceDisplayReturnValue.setMemberPromoPrice(false);
            dealPromoPriceDisplayReturnValue.setOriginPriceDisplayDTO(merchantMemberPromoPriceDisplayReturnValue.getPriceDisplayDTO());
            return dealPromoPriceDisplayReturnValue;
        });
    }

    private CompletableFuture<ProductPriceReturnValue> buildDealPromoPriceDisplayReturnValue(ProductDetailPageRequest pageRequest, SkuDefaultSelect skuDefaultSelect) {
        BatchPriceRequest priceRequest = buildBatchPriceRequest(pageRequest, skuDefaultSelect);
        priceRequest.setScene(RequestSceneEnum.PROMO_DETAIL_DESC_WithDealPromo.getScene());
        return getProductPriceDisplayDTO(priceRequest).thenApply(ProductPriceReturnValue::new);
    }

    private CompletableFuture<ProductPriceReturnValue> buildMerchantMemberPromoPriceDisplayReturnValue(ProductDetailPageRequest pageRequest, SkuDefaultSelect skuDefaultSelect) {
        BatchPriceRequest priceRequest = buildBatchPriceRequest(pageRequest, skuDefaultSelect);
        priceRequest.setScene(RequestSceneEnum.PROMO_DETAIL_DESC_WithMerchantMember.getScene());
        return getProductPriceDisplayDTO(priceRequest).thenApply(ProductPriceReturnValue::new);
    }

    @Override
    protected void postProcessPriceRequest(ClientEnv clientEnv, List<ProductIdentity> productIdentities, Map<String, String> extension) {
        ProductAttr productAttr = getDependencyResult(ProductAttrFetcher.class);
        if (isPreSaleProduct(productAttr)) {
            extension.put(ExtensionKeyEnum.Price_Desc_Type.getDesc(), String.valueOf(PriceDescEnum.PreSale.getType()));
        }
        AbTestReturnValue abTestReturnValue = getDependencyResult(AbTestFetcher.class);
        // 撤出一期实验，用二期实验代替
        String expResult = abTestReturnValue.getAbTestExpResult("MtMagicalCouponStyleEnhancementV2");
        // 满足实验结果，则传实验标给报价
        if (MAGIC_ENHANCEMENT_TEST_RESULTS.contains(expResult)) {
            //神券感知增强优惠提示条
            String abTestArgs = String.join(",", extension.get(ExtensionKeyEnum.PromoDetailTemplate.getDesc()),
                    MT_MAGIC_COUPON_PROMO_DETAIL_TEMPLATE);
            extension.put(ExtensionKeyEnum.PromoDetailTemplate.getDesc(), abTestArgs);
        }
    }
}
