package com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.product.detail.gateway.spi.enums.ProductTypeEnum;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class ProductBaseInfoTest {

    @Mock
    private DealGroupDTO dealGroupDTO;

    private ProductBaseInfo productBaseInfo;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        // 使用DEAL类型初始化ProductBaseInfo
        productBaseInfo = new ProductBaseInfo(ProductTypeEnum.DEAL, dealGroupDTO);
    }

    /**
     * 测试当传入的 num 为 null 时，返回 0
     */
    @Test
    public void testSaveGetLongWhenNumIsNull() throws Throwable {
        // arrange
        // 已在setUp中完成
        // act
        long result = productBaseInfo.saveGetLong(null);
        // assert
        assertEquals(0, result);
    }

    /**
     * 测试当传入的 num 不为 null 时，返回 num 的值
     */
    @Test
    public void testSaveGetLongWhenNumIsNotNull() throws Throwable {
        // arrange
        Long num = 12345L;
        // act
        long result = productBaseInfo.saveGetLong(num);
        // assert
        assertEquals(12345L, result);
    }

    /**
     * 测试当传入的 num 为 Long.MIN_VALUE 时，返回 Long.MIN_VALUE
     */
    @Test
    public void testSaveGetLongWhenNumIsLongMinValue() throws Throwable {
        // arrange
        Long num = Long.MIN_VALUE;
        // act
        long result = productBaseInfo.saveGetLong(num);
        // assert
        assertEquals(Long.MIN_VALUE, result);
    }

    /**
     * 测试当传入的 num 为 Long.MAX_VALUE 时，返回 Long.MAX_VALUE
     */
    @Test
    public void testSaveGetLongWhenNumIsLongMaxValue() throws Throwable {
        // arrange
        Long num = Long.MAX_VALUE;
        // act
        long result = productBaseInfo.saveGetLong(num);
        // assert
        assertEquals(Long.MAX_VALUE, result);
    }
}
