package com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.ext.param.handler;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.product.detail.RequestCustomParamEnum;
import com.sankuai.dz.product.detail.gateway.spi.dto.CustomParam;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.enums.OrderPageExtParamEnums;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.ext.param.request.ExtParamBuilderRequest;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * Unit tests for {@link OfflineCodeExtParamBuilder}
 */
@ExtendWith(MockitoExtension.class)
class OfflineCodeExtParamBuilderTest {

    private final OfflineCodeExtParamBuilder builder = new OfflineCodeExtParamBuilder();

    /**
     * Test that buildKey() returns the correct enum value
     */
    @Test
    void testBuildKeyReturnsCorrectOfflineCodeEnum() {
        // arrange
        OfflineCodeExtParamBuilder builder = new OfflineCodeExtParamBuilder();
        // act
        OrderPageExtParamEnums result = builder.buildKey();
        // assert
        assertNotNull(result, "Returned enum should not be null");
        assertEquals(OrderPageExtParamEnums.offlinecode, result, "Should return offlinecode enum");
        assertEquals("优惠码,线下码", result.getDesc(), "Enum description should match expected value");
    }

    /**
     * Test that buildKey() consistently returns the same enum value
     */
    @Test
    void testBuildKeyReturnsConsistentValue() {
        // arrange
        OfflineCodeExtParamBuilder builder = new OfflineCodeExtParamBuilder();
        // act
        OrderPageExtParamEnums firstCall = builder.buildKey();
        OrderPageExtParamEnums secondCall = builder.buildKey();
        // assert
        assertEquals(firstCall, secondCall, "Multiple calls should return the same enum value");
    }

    /**
     * Test when request has no custom params - should return null
     */
    @Test
    public void testDoBuildExtParamWhenNoCustomParams() throws Exception {
        // arrange
        ProductDetailPageRequest request = mock(ProductDetailPageRequest.class);
        when(request.getCustomParam(RequestCustomParamEnum.offlinecode)).thenReturn(null);
        ExtParamBuilderRequest builderRequest = mock(ExtParamBuilderRequest.class);
        // act
        String result = builder.doBuildExtParam(request, builderRequest);
        // assert
        assertNull(result);
        verify(request).getCustomParam(RequestCustomParamEnum.offlinecode);
    }

    /**
     * Test when custom params exist but no offlinecode - should return null
     */
    @Test
    public void testDoBuildExtParamWhenNoOfflineCode() throws Exception {
        // arrange
        ProductDetailPageRequest request = mock(ProductDetailPageRequest.class);
        when(request.getCustomParam(RequestCustomParamEnum.offlinecode)).thenReturn(null);
        ExtParamBuilderRequest builderRequest = mock(ExtParamBuilderRequest.class);
        // act
        String result = builder.doBuildExtParam(request, builderRequest);
        // assert
        assertNull(result);
        verify(request).getCustomParam(RequestCustomParamEnum.offlinecode);
    }

    /**
     * Test when offlinecode is empty string - should return null
     */
    @Test
    public void testDoBuildExtParamWhenOfflineCodeIsEmpty() throws Exception {
        // arrange
        ProductDetailPageRequest request = mock(ProductDetailPageRequest.class);
        when(request.getCustomParam(RequestCustomParamEnum.offlinecode)).thenReturn("");
        ExtParamBuilderRequest builderRequest = mock(ExtParamBuilderRequest.class);
        // act
        String result = builder.doBuildExtParam(request, builderRequest);
        // assert
        assertNull(result);
        verify(request).getCustomParam(RequestCustomParamEnum.offlinecode);
    }

    /**
     * Test when offlinecode is blank string - should return null
     */
    @Test
    public void testDoBuildExtParamWhenOfflineCodeIsBlank() throws Exception {
        // arrange
        ProductDetailPageRequest request = mock(ProductDetailPageRequest.class);
        when(request.getCustomParam(RequestCustomParamEnum.offlinecode)).thenReturn("   ");
        ExtParamBuilderRequest builderRequest = mock(ExtParamBuilderRequest.class);
        // act
        String result = builder.doBuildExtParam(request, builderRequest);
        // assert
        assertNull(result);
        verify(request).getCustomParam(RequestCustomParamEnum.offlinecode);
    }

    /**
     * Test when offlinecode has valid value - should return the value
     */
    @Test
    public void testDoBuildExtParamWhenOfflineCodeExists() throws Exception {
        // arrange
        String expectedCode = "test123";
        ProductDetailPageRequest request = mock(ProductDetailPageRequest.class);
        when(request.getCustomParam(RequestCustomParamEnum.offlinecode)).thenReturn(expectedCode);
        ExtParamBuilderRequest builderRequest = mock(ExtParamBuilderRequest.class);
        // act
        String result = builder.doBuildExtParam(request, builderRequest);
        // assert
        assertEquals(expectedCode, result);
        verify(request).getCustomParam(RequestCustomParamEnum.offlinecode);
    }

    /**
     * Test when request is null - should throw NullPointerException
     */
    @Test
    public void testDoBuildExtParamWhenRequestIsNull() {
        // arrange
        ExtParamBuilderRequest builderRequest = mock(ExtParamBuilderRequest.class);
        // act & assert
        assertThrows(NullPointerException.class, () -> {
            builder.doBuildExtParam(null, builderRequest);
        });
    }
}
