package com.sankuai.dzshoppingguide.product.detail.spi.promo.vo;

import java.io.Serializable;
import java.util.List;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import lombok.Data;

/**
 * @desc 弹窗内容模块
 */
@TypeDoc(description = "弹窗内容模块")
@Data
@MobileDo(id = 0xbd6a)
public class PopUpContentModule implements Serializable {
    /**
    * 弹窗详细信息
    */
    @FieldDoc(description = "弹窗详细信息")
    @MobileField(key = 0xcce)
    private List<PopUpContentItem> content;

    /**
    * 弹窗标题
    */
    @FieldDoc(description = "弹窗标题")
    @MobileField(key = 0x24cc)
    private String title;
}
