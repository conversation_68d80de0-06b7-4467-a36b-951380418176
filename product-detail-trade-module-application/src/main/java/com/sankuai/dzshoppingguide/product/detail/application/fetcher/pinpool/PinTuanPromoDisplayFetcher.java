package com.sankuai.dzshoppingguide.product.detail.application.fetcher.pinpool;

import com.dianping.pay.common.enums.PayPlatform;
import com.dianping.pay.promo.common.enums.ProductType;
import com.dianping.pay.promo.display.api.dto.Product;
import com.dianping.pay.promo.display.api.dto.PromoDisplayDTO;
import com.dianping.pay.promo.display.api.dto.QueryPromoDisplayRequest;
import com.dianping.tpfun.product.api.sku.pintuan.dto.PinProductBrief;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.cityId.CityIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.cityId.CityIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
import com.sankuai.dzshoppingguide.product.detail.application.utils.PromoHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Objects;
import java.util.concurrent.CompletableFuture;

import static com.sankuai.dz.product.detail.gateway.spi.enums.MobileOSTypeEnum.ANDROID;
import static com.sankuai.dz.product.detail.gateway.spi.enums.MobileOSTypeEnum.IOS;

/**
 * <AUTHOR>
 * @create 2025/3/11 10:59
 */
@Fetcher(
        previousLayerDependencies = {
                PricePinPoolFetcher.class,
                CityIdMapperFetcher.class
        }
)
public class PinTuanPromoDisplayFetcher extends NormalFetcherContext<PinTuanPromoDisplayResult> {
    private static final int PIN_POOL_PROMO_TEMPLATE_ID = 226;

    @Autowired
    private CompositeAtomService compositeAtomService;

    @Override
    protected CompletableFuture<PinTuanPromoDisplayResult> doFetch() {
        // 取数
        PricePinPoolResult pricePinPoolResult = getDependencyResult(PricePinPoolFetcher.class);
        CityIdMapper cityIdMapper = getDependencyResult(CityIdMapperFetcher.class);

        if ( Objects.nonNull(pricePinPoolResult)){
            PinProductBrief pinProductBrief = pricePinPoolResult.getPinProductBrief();
            PinTuanPromoDisplayResult result = buildPricePinPoolResult(pinProductBrief, cityIdMapper);
            return CompletableFuture.completedFuture(result);
        }
        return CompletableFuture.completedFuture(null);
    }

    public PinTuanPromoDisplayResult buildPricePinPoolResult(PinProductBrief pinProductBrief, CityIdMapper cityIdMapper){
        PinTuanPromoDisplayResult result = new PinTuanPromoDisplayResult();
        if (pinProductBrief != null && pinProductBrief.getPrice() != null && pinProductBrief.getPrice().doubleValue() > 0){
            QueryPromoDisplayRequest queryPromoDisplayRequest = buildTimesPromoReq(pinProductBrief, cityIdMapper);
            compositeAtomService.queryPromoDisplayDTO(queryPromoDisplayRequest).thenApply(res->{
                if (CollectionUtils.isNotEmpty(res)){
                    PromoDisplayDTO displayDTO = res.get(0);
                    result.setPinPoolPromoDesc(PromoHelper.getPromoDisplayDTODesc(displayDTO));
                    result.setPinPoolPromoAmount(PromoHelper.getPromoDisplayDTOAmount(displayDTO));
                }
                return  null;
            });
        }
        return result;
    }

    private QueryPromoDisplayRequest buildTimesPromoReq(PinProductBrief pinProductBrief, CityIdMapper cityIdMapper) {
        Product product = new Product();
        product.setProductId(pinProductBrief.getItemId());
        product.setPrice(pinProductBrief.getPrice());

        QueryPromoDisplayRequest request = new QueryPromoDisplayRequest();
        request.setProduct(product);
        request.setClientVersion(this.request.getShepherdGatewayParam().getAppVersion());
        request.setTemplateID(PIN_POOL_PROMO_TEMPLATE_ID); //商户页模板
        request.setPlatform(toPayPlatformCode(this.request.getClientTypeEnum()));
        if (this.request.getClientTypeEnum().isMtClientType()) {
            request.setCityId(cityIdMapper.getMtCityId());
            request.setUserId(this.request.getMtUserId());//已确认判断平台后再使用
            request.setDpId(this.request.getShepherdGatewayParam().getDeviceId());
            request.setProductType(ProductType.mt_generalPinTuan.value);
        } else {
            request.setCityId(cityIdMapper.getDpCityId());
            request.setUserId(this.request.getDpUserId());
            request.setDpId(this.request.getShepherdGatewayParam().getDeviceId());
            request.setProductType(ProductType.generalPinTuan.value);
        }
        return request;
    }

    //clientType转PayPlatform
    public int toPayPlatformCode(ClientTypeEnum clientTypeEnum) {
        if (clientTypeEnum == ClientTypeEnum.MT_XCX) {
            return PayPlatform.mt_weixin_api.getCode();
        }
        if (clientTypeEnum == ClientTypeEnum.MT_APP && IOS.equals(request.getMobileOSType())) {
            return PayPlatform.mt_iphone_native.getCode();
        }
        if (clientTypeEnum == ClientTypeEnum.MT_APP && ANDROID.equals(request.getMobileOSType())) {
            return PayPlatform.mt_android_native.getCode();
        }
        if (clientTypeEnum == ClientTypeEnum.MT_I) {
            return PayPlatform.mt_wap_m.getCode();
        }
        if (clientTypeEnum == ClientTypeEnum.MT_PC) {
            return PayPlatform.mt_pc.getCode();
        }
        /***下方为点评各个站点***/
        if (clientTypeEnum == ClientTypeEnum.DP_XCX) {
            return PayPlatform.weixin_api.getCode();
        }
        if (clientTypeEnum == ClientTypeEnum.DP_APP && ANDROID.equals(request.getMobileOSType())) {
            return PayPlatform.dp_android_native.getCode();
        }
        if (clientTypeEnum == ClientTypeEnum.DP_APP && IOS.equals(request.getMobileOSType())) {
            return PayPlatform.dp_iphone_native.getCode();
        }
        if (clientTypeEnum == ClientTypeEnum.DP_M) {
            return PayPlatform.tg_wap_m.getCode();
        }
        if (clientTypeEnum == ClientTypeEnum.DP_PC) {
            return PayPlatform.tg_pc.getCode();
        }
        return 0;
    }
}
