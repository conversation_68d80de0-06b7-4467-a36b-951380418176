package com.sankuai.dzshoppingguide.product.detail.spi.tags.vo.edu;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.tags.vo.TagsModuleVO;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TypeDoc(description = "美团月付·先学后付等标签")
@MobileDo(id = 0x2b8)
public class EduStudyBeforePayTagsModuleVO extends TagsModuleVO {
    @Override
    public String getModuleKey() {
        return ModuleKeyConstants.EDU_STUDY_BEFORE_PAY;
    }
}
