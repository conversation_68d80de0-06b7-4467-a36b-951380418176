package com.sankuai.dzshoppingguide.product.detail.application.service.dealtinyinfo;

import com.dianping.cat.Cat;
import com.dianping.lion.common.util.JsonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.sankuai.dz.product.detail.RequestCustomParamEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.acl.douhu.AbTestResult;
import com.sankuai.dzshoppingguide.product.detail.application.builder.similargoods.dtos.ShowLowPriceItemRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.shopid.ShopIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.best.shop.ShopInfo;
import com.sankuai.dzshoppingguide.product.detail.application.service.dealtinyinfo.dto.DealLowPriceItemEntranceVO;
import com.sankuai.dzshoppingguide.product.detail.domain.utils.LionConfigUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.RequestSourceEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.similargoods.vo.DealTinyInfoVO;
import com.sankuai.dztheme.deal.dto.DealProductAttrDTO;
import com.sankuai.dztheme.deal.dto.DealProductDTO;
import com.sankuai.dztheme.deal.dto.DealProductSaleDTO;
import com.sankuai.dztheme.deal.res.DealProductResult;
import com.sankuai.dztheme.deal.res.DealSkuSummaryDTO;
import com.sankuai.tpfun.skuoperationapi.price.dto.PriceRangeDO;
import com.sankuai.tpfun.skuoperationapi.price.dto.PriceRangeItemDTO;
import com.sankuai.tpfun.skuoperationapi.price.dto.SubjectDTO;
import com.sankuai.tpfun.skuoperationapi.price.dto.enums.PriceRangeItemTypeEnum;
import com.sankuai.tpfun.skuoperationapi.price.dto.response.BatchPriceRangeInfoResponse;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023-10-09
 * @desc 实现查询团单简要信息
 */
@Component
@Slf4j
public class DealTinyInfoFacade {
        /**
     * 页面来源-购物车未选中
     */
    private static final String PAGE_SOURCE_SHOP_CAR_NO_SELECT = "shopcarnoselect";
    /**
     * 页面来源-购物车选中
     */
    private static final String PAGE_SOURCE_SHOP_CAR_SELECT = "shopcarselect";
     /**
     * 页面来源-猜喜
     */
    private static final String PAGE_SOURCE_CAIXI = "caixi";

    /**
     * 对应竞争圈价格排名前20%的价格的key
     */
    private static final String TOP_TWENTY_PERCENT_PRICE = "topTwentyPercentPrice";
    /**
     * 对应竞争圈价格排名前50%的价格的key
     */
    private static final String TOP_FIFTY_PERCENT_PRICE = "topFiftyPercentPrice";
    
    /**
     * 判断是否展示同款低价入口
     * @param lowPriceItemRequest 参数请求
     * @return 是否展示同款低价入口
     */
    public DealLowPriceItemEntranceVO isShowLowPriceItemEntrance(ShowLowPriceItemRequest lowPriceItemRequest) {
        ProductDetailPageRequest request = lowPriceItemRequest.getRequest();
        
        boolean isMt = request.getClientTypeEnum().isMtClientType();
        DealLowPriceItemEntranceVO priceItemEntranceVO = new DealLowPriceItemEntranceVO();
        // 判断门店是否在黑名单中
        ShopIdMapper shopIdMapper = lowPriceItemRequest.getShopIdMapper();
        if (isShopInBlackList(String.valueOf(isMt ? shopIdMapper.getMtBestShopId() : shopIdMapper.getDpBestShopId()), isMt, 
                lowPriceItemRequest.isHitShopInBlackList())) {
            priceItemEntranceVO.setShowLowPriceDealList(false);
            return priceItemEntranceVO;
        }
        int dealId = Math.toIntExact(request.getProductId());
        // 转成点评团单ID
        int dpDealId = Math.toIntExact(request.getClientTypeEnum().isMtClientType() ? lowPriceItemRequest.getDealGroupIdMapper().getDpDealGroupId() : dealId);
        // 查询图单类目Id
        int categoryId = Math.toIntExact(lowPriceItemRequest.getProductBaseInfo().getCategory().getCategoryId());
        // 复购货架-团单被限购才会请求
        if ("purchase_limit".equals(request.getCustomParam(RequestCustomParamEnum.businessType))){
            priceItemEntranceVO.setShowLowPriceDealList(false);
            if(LionConfigUtils.enableRepurchase(categoryId, request.getClientTypeEnum().isMtClientType(), request.getCityId())){
                // 命中实验结果c 可展示
                priceItemEntranceVO.setShowLowPriceDealList(true);
            }
            return priceItemEntranceVO;
        }
        // 转成点评门店Id
        long dpShopId = shopIdMapper.getDpBestShopId();
        // 查询团单价格
        DealTinyInfoVO dealTinyInfoVO = queryDealTinyInfo(request, lowPriceItemRequest.getShopInfo(), lowPriceItemRequest.getSkuSummary(), lowPriceItemRequest.getDealProductResult());
        BigDecimal dealFinalPrice = new BigDecimal(
                Optional.ofNullable(dealTinyInfoVO).map(DealTinyInfoVO::getFinalPrice).orElse("0"));
        boolean isShow;
        String topPriceStr;
        boolean hasPricePowerTag = false;
        if (Objects.equals(request.getPageSource(), PAGE_SOURCE_CAIXI)) {
            // 竞争圈前20%低价
            topPriceStr = getTopTwentyPercentPrice(dpDealId, dpShopId, lowPriceItemRequest.getBatchPriceRangeInfoResponse());
        } else {
            // 竞争圈前50%价格带
            topPriceStr = getTopFiftyPercentPrice(dpDealId, dpShopId, lowPriceItemRequest.getBatchPriceRangeInfoResponse());
            hasPricePowerTag = CollectionUtils.isNotEmpty(Optional.ofNullable(dealTinyInfoVO)
                    .map(DealTinyInfoVO::getPricePowerTag).orElse(Lists.newArrayList()));
        }
        if (StringUtils.isBlank(topPriceStr) || hasPricePowerTag) {
            priceItemEntranceVO.setShowLowPriceDealList(false);
            return priceItemEntranceVO;
        }
        BigDecimal topPrice = new BigDecimal(topPriceStr);
        // 猜喜路径：当前团单价格 > 竞争圈前20%价格，则展示
        // 其他路径：当前团单无价格力标签 & 价格 > 竞争圈前50%价格，则展示
        isShow = dealFinalPrice.compareTo(topPrice) > 0;
        // 查询允许展示比价浮层的类目列表
        boolean comparePriceAbTestSwitch = isShow && showComparePrice(lowPriceItemRequest.getAbTestResult());
        priceItemEntranceVO.setShowLowPriceDealList(comparePriceAbTestSwitch);
        return priceItemEntranceVO;
    }

    private boolean showComparePrice(AbTestResult abTestResult) {
        if (Objects.isNull(abTestResult)) {
            return false;
        }
        String expResult = abTestResult.getExpResult();
        // 同款低价品：c
        return Objects.equals(expResult, "c");
    }

    /**
     * 查询团单简要信息，不包含副标题
     *
     * @param request 团单Id、页面来源、门店Id
     * @return 团单信息
     */
    private DealTinyInfoVO queryDealTinyInfo(ProductDetailPageRequest request, ShopInfo shopInfo,
                                             DealSkuSummaryDTO skuSummary, DealProductResult dealProductResult) {
        boolean isMt = request.getClientTypeEnum().isMtClientType();
        // 预请求团单概要信息
        int skuId = Objects.nonNull(skuSummary) && (skuSummary.getDefaultSkuId() > 0) ? Math.toIntExact(skuSummary.getDefaultSkuId()) : 0;
        return getDealResult(dealProductResult, skuId, request.getPageSource(), shopInfo);
    }
    
    private boolean isShopInBlackList(String shopId, boolean isMt, boolean hitShopInBlackList) {
        return hitShopInBlackList
                || LionConfigUtils.isComparePriceShopBlackList(isMt, shopId);
    }



    private String getTopTwentyPercentPrice(int dpDealId, long dpShopId, BatchPriceRangeInfoResponse response) {
        return getTopPrice(dpDealId, dpShopId, TOP_TWENTY_PERCENT_PRICE, response);
    }

    private String getTopFiftyPercentPrice(int dpDealId, long dpShopId, BatchPriceRangeInfoResponse response) {
        return getTopPrice(dpDealId, dpShopId, TOP_FIFTY_PERCENT_PRICE, response);
    }

    private String getTopPrice(int dpDealId, long dpShopId, String topPriceKey, BatchPriceRangeInfoResponse response) {

        SubjectDTO subjectDTO = new SubjectDTO();
        subjectDTO.setDpShopId(dpShopId);
        subjectDTO.setProductId((long)dpDealId);
        List<PriceRangeItemDTO> priceRangeItems = Objects.nonNull(response) ? response.getSubjectDTO2PriceRangesMap()
                .getOrDefault(subjectDTO, Collections.emptyList()) : Lists.newArrayList();
        if (CollectionUtils.isEmpty(priceRangeItems)) {
            return StringUtils.EMPTY;
        }
        List<PriceRangeItemDTO> priceRangeItemDTOList = priceRangeItems.stream()
                .filter(e -> Objects.equals(e.getPriceRangeItemType(),
                        PriceRangeItemTypeEnum.SHOPPING_CART_SPACE_PRICE_RANGE_ITEM.getCode()))
                .collect(Collectors.toList());
        PriceRangeDO priceRangeDO = priceRangeItemDTOList.get(0).getPriceRangeDO();
        if (Objects.isNull(priceRangeDO) || Objects.isNull(priceRangeDO.getExtra())) {
            return StringUtils.EMPTY;
        }
        return priceRangeDO.getExtra().getOrDefault(topPriceKey, StringUtils.EMPTY);
    }
    
    private DealTinyInfoVO getDealResult(DealProductResult dealProductResult, Integer skuId, String pageSource, ShopInfo shopInfo) {
        if (Objects.isNull(dealProductResult) || CollectionUtils.isEmpty(dealProductResult.getDeals())) {
            return null;
        }
        DealProductDTO dealProductDTO = dealProductResult.getDeals().get(0);
        DealTinyInfoVO dealTinyInfoVO = new DealTinyInfoVO();
        dealTinyInfoVO.setDealGroupId(dealProductDTO.getProductId());
        dealTinyInfoVO.setTitle(dealProductDTO.getName());
        DealProductSaleDTO sale = dealProductDTO.getSale();
        dealTinyInfoVO.setSaleTag(Optional.ofNullable(sale).map(DealProductSaleDTO::getSaleTag).orElse(StringUtils.EMPTY));
        dealTinyInfoVO.setHeadPic(dealProductDTO.getHeadPic());
        dealTinyInfoVO.setBtnText("抢购");
        List<DealProductAttrDTO> attrs = dealProductDTO.getAttrs();
        // 副标题
        String dealSubTitleJson = getDealProductAttrValue(attrs, "dealSubTitle");
        if (StringUtils.isNotBlank(dealSubTitleJson)) {
            List<String> dealSubTitles = JsonUtils.fromJson(dealSubTitleJson, new TypeReference<List<String>>() {});
            if (dealSubTitles != null) {
                if (dealSubTitles.contains(null)) {
                    Cat.logEvent("NullPointer", "dealSubTitles contain null");
                }
                dealTinyInfoVO.setSubTitle(Joiner.on("·").join(dealSubTitles.stream().filter(Objects::nonNull).collect(Collectors.toList())));
            } else {
                Cat.logEvent("NullPointer", "dealSubTitles is null");
            }
        }
        dealTinyInfoVO.setSkuId(skuId);
        // 价格力标签
        String pricePowerTag = getDealProductAttrValue(attrs, "highestPriorityPricePowerTagAttr");
        dealTinyInfoVO.setPricePowerTag(StringUtils.isBlank(pricePowerTag) ? null : Collections.singletonList(pricePowerTag));
        // 提单页
        if (Objects.equals(pageSource, PAGE_SOURCE_SHOP_CAR_SELECT)
                || Objects.equals(pageSource, PAGE_SOURCE_SHOP_CAR_NO_SELECT)) {
            dealTinyInfoVO.setDirectBuyJumpUrl(getDealProductAttrValue(attrs, "dzShopCarOrderUrl"));
        } else {
            dealTinyInfoVO.setDirectBuyJumpUrl(dealProductDTO.getOrderUrl());
        }
        // 到手价
        if (Objects.equals(pageSource, PAGE_SOURCE_SHOP_CAR_SELECT)) {
            String finalPriceForTradeStr = getDealProductAttrValue(attrs, "finalPriceForTrade");
            String marketPriceForTradeStr = getDealProductAttrValue(attrs, "marketPriceForTrade");
            BigDecimal finalPriceForTrade = new BigDecimal(finalPriceForTradeStr);
            BigDecimal marketPriceForTrade = new BigDecimal(marketPriceForTradeStr);
            dealTinyInfoVO.setDiscount(getDealProductAttrValue(attrs, "discountForTrade"));
            dealTinyInfoVO.setFinalPrice(finalPriceForTrade.stripTrailingZeros().toPlainString());
            dealTinyInfoVO.setMarketPrice(marketPriceForTrade.stripTrailingZeros().toPlainString());
        } else {
            // 团详场景 or 购物车场景且未被选中
            BigDecimal finalPrice = Optional.ofNullable(dealProductDTO.getPromoPrice()).orElse(BigDecimal.ZERO);
            BigDecimal marketPrice = new BigDecimal(
                    Optional.ofNullable(dealProductDTO.getMarketPriceTag()).orElse("0"));
            String discount = StringUtils.EMPTY;
            if (finalPrice != null) {
                dealTinyInfoVO.setFinalPrice(finalPrice.stripTrailingZeros().toPlainString());
                BigDecimal discountDecimal = calcDiscount(finalPrice, marketPrice);
                if (Objects.nonNull(discountDecimal)) { discount = discountDecimal.toPlainString(); }
            }
            dealTinyInfoVO.setMarketPrice(marketPrice.stripTrailingZeros().toPlainString());
            dealTinyInfoVO.setDiscount(StringUtils.isBlank(discount) ? StringUtils.EMPTY : discount + "折");
        }
        if (RequestSourceEnum.fromBeautyBuyingList(pageSource)) {
            dealTinyInfoVO.setBtnText("再次购买");
        }
        // 团单二级类目
        dealTinyInfoVO.setCategoryId(dealProductDTO.getCategoryId());
        return dealTinyInfoVO;
    }
    /**
     * 计算折扣(保留1位)四舍五入，即 9.9 折
     * @param finalPrice 优惠后价
     * @param marketPrice  优惠前价
     * @return 折扣
     */
    private BigDecimal calcDiscount(BigDecimal finalPrice, BigDecimal marketPrice) {
        if (finalPrice == null || marketPrice == null || marketPrice.compareTo(BigDecimal.ZERO) == 0 || finalPrice.compareTo(BigDecimal.ZERO) == 0) {
            return null;
        }
        return finalPrice.divide(marketPrice, 3, RoundingMode.DOWN)
                .multiply(new BigDecimal(10)).setScale(1, RoundingMode.HALF_UP);
    }

    private String getDealProductAttrValue(List<DealProductAttrDTO> attrs, String name) {
        if (CollectionUtils.isEmpty(attrs)) {
            return StringUtils.EMPTY;
        }
        DealProductAttrDTO attrDTO = attrs.stream().filter(Objects::nonNull)
                .filter(attr -> Objects.equals(attr.getName(), name)).findFirst().orElse(null);
        return attrDTO == null ? StringUtils.EMPTY : attrDTO.getValue();
    }

    @Data
    @Builder
    private static class DealProductRequestParam {
        private int dealId;
        private long shopId;
        /**
         * 页面请求来源，团详/购物车（选中/未选中）
         */
        private String pageSource;
        private String planId;
        private int skuId;
        /**
         * 是否需要查询价格趋势
         */
        private boolean needPriceTrend;
        /**
         * 用户定位城市
         */
        private Integer cityId;
        /**
         * 用户定位纬度
         */
        private Double lat;
        /**
         * 用户定位经度
         */
        private Double lng;
    }

}
