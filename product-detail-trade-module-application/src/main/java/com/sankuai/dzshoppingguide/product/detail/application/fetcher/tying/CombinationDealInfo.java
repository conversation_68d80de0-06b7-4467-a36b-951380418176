package com.sankuai.dzshoppingguide.product.detail.application.fetcher.tying;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: zheng<PERSON><EMAIL>
 * @Date: 2023/7/25
 */
@Data
public class CombinationDealInfo {
    /**
     * 组合id
     */
    private String itemId;

    /**
     * 主品ID
     */
    private int mainDealId;

    /**
     * 搭售品ID
     */
    private int bindingDealId;

    /**
     * 优惠金额
     */
    private BigDecimal extPricePromoAmount;

    /**
     * 组合价
     */
    private BigDecimal extPrice;

    /**
     * 搭售品skuId
     */
    private long tyingSaleSkuId;

    /**
     * 搭售商品来源：1-算法组品，2-运营组品
     */
    private int bindingSource;
}
