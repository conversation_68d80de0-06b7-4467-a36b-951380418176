package com.sankuai.dzshoppingguide.product.detail.application.builder.membercard.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.reserve.dto.PeriodStockM;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.reserve.dto.ProductItemM;
import com.sankuai.dzshoppingguide.product.detail.application.utils.LionConfigUtils;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

@ExtendWith(MockitoExtension.class)
public class ReserveMemberCardBuilderTest {

    @InjectMocks
    private ReserveMemberCardBuilder builder;

    /**
     * Test when shop is tech stock shop
     */
    @Test
    public void testGetStockGranularity_TechStockShop() throws Throwable {
        // arrange
        boolean isTechStockShop = true;
        ProductItemM productItemM = new ProductItemM();
        // act
        int result = builder.getStockGranularity(isTechStockShop, productItemM);
        // assert
        assertEquals(15, result);
    }

    /**
     * Test when productItemM is null
     */
    @Test
    public void testGetStockGranularity_NullProductItem() throws Throwable {
        // arrange
        boolean isTechStockShop = false;
        // act
        int result = builder.getStockGranularity(isTechStockShop, null);
        // assert
        assertEquals(30, result);
    }

    /**
     * Test when bookPeriodStocks is empty
     */
    @Test
    public void testGetStockGranularity_EmptyStocks() throws Throwable {
        // arrange
        boolean isTechStockShop = false;
        ProductItemM productItemM = new ProductItemM();
        productItemM.setBookPeriodStocks(new ArrayList<>());
        // act
        int result = builder.getStockGranularity(isTechStockShop, productItemM);
        // assert
        assertEquals(30, result);
    }

    /**
     * Test when stockGranularity is 30
     */
    @Test
    public void testGetStockGranularity_Granularity30() throws Throwable {
        // arrange
        boolean isTechStockShop = false;
        ProductItemM productItemM = new ProductItemM();
        PeriodStockM periodStockM = new PeriodStockM();
        periodStockM.setStockGranularity(30);
        productItemM.setBookPeriodStocks(Arrays.asList(periodStockM));
        // act
        int result = builder.getStockGranularity(isTechStockShop, productItemM);
        // assert
        assertEquals(30, result);
    }

    /**
     * Test when stockGranularity is 0
     */
    @Test
    public void testGetStockGranularity_Granularity0() throws Throwable {
        // arrange
        boolean isTechStockShop = false;
        ProductItemM productItemM = new ProductItemM();
        PeriodStockM periodStockM = new PeriodStockM();
        periodStockM.setStockGranularity(0);
        productItemM.setBookPeriodStocks(Arrays.asList(periodStockM));
        // act
        int result = builder.getStockGranularity(isTechStockShop, productItemM);
        // assert
        assertEquals(30, result);
    }

    /**
     * Test when periodStockM is null
     */
    @Test
    public void testGetStockGranularity_NullPeriodStock() throws Throwable {
        // arrange
        boolean isTechStockShop = false;
        ProductItemM productItemM = new ProductItemM();
        productItemM.setBookPeriodStocks(Arrays.asList((PeriodStockM) null));
        // act
        int result = builder.getStockGranularity(isTechStockShop, productItemM);
        // assert
        assertEquals(30, result);
    }

    /**
     * Test when bookPeriodStocks is null
     */
    @Test
    public void testGetStockGranularity_NullBookPeriodStocks() throws Throwable {
        // arrange
        boolean isTechStockShop = false;
        ProductItemM productItemM = new ProductItemM();
        productItemM.setBookPeriodStocks(null);
        // act
        int result = builder.getStockGranularity(isTechStockShop, productItemM);
        // assert
        assertEquals(30, result);
    }

    /**
     * Test when stockGranularity has non-standard value and in whitelist
     */
    @Test
    public void testGetStockGranularity_NonStandardValueInWhitelist() throws Throwable {
        // arrange
        boolean isTechStockShop = false;
        ProductItemM productItemM = new ProductItemM();
        PeriodStockM periodStockM = new PeriodStockM();
        periodStockM.setStockGranularity(45);
        productItemM.setBookPeriodStocks(Arrays.asList(periodStockM));
        // act
        int result = builder.getStockGranularity(isTechStockShop, productItemM);
        // assert
        assertEquals(45, result);
    }

    /**
     * Test when stockGranularity is 15 and in whitelist
     */
    @Test
    public void testGetStockGranularity_Granularity15InWhitelist() throws Throwable {
        // arrange
        boolean isTechStockShop = false;
        ProductItemM productItemM = new ProductItemM();
        PeriodStockM periodStockM = new PeriodStockM();
        periodStockM.setStockGranularity(15);
        productItemM.setBookPeriodStocks(Arrays.asList(periodStockM));
        // act
        int result = builder.getStockGranularity(isTechStockShop, productItemM);
        // assert
        assertEquals(15, result);
    }
}
