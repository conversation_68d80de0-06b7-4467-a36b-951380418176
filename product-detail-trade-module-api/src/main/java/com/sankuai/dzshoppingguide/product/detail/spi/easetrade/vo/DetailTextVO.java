package com.sankuai.dzshoppingguide.product.detail.spi.easetrade.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import lombok.Data;

import java.io.Serializable;

@Data
public class DetailTextVO implements Serializable {
    /**
     * 字重
     */
    @MobileDo.MobileField
    private String weight;

    /**
     * 字号
     */
    @MobileField
    private String size;

    /**
     * 颜色
     */
    @MobileField
    private String color;

    /**
     * 文本内容/图标
     */
    @MobileField
    private String content;


    public DetailTextVO(String content, String size, String color, String weight) {
        this.weight = weight;
        this.size = size;
        this.color = color;
        this.content = content;
    }

}
