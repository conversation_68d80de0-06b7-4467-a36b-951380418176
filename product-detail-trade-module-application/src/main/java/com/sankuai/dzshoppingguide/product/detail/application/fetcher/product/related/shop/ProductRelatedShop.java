package com.sankuai.dzshoppingguide.product.detail.application.fetcher.product.related.shop;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupShopRelationCheckResultDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupVerifyShopCheckDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * @Author: guangyujie
 * @Date: 2025/2/17 17:20
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProductRelatedShop extends FetcherReturnValueDTO {

    /**
     * 点评展示商户ID（查询中心做了大客截断，最多返回2000家门店）
     */
    private List<Long> dpDisplayShopIds;

    /**
     * 美团展示商户ID（查询中心做了大客截断，最多返回2000家门店）
     */
    private List<Long> mtDisplayShopIds;

    /**
     * 商品和门店核销门店关系
     */
    private DealGroupVerifyShopCheckDTO verifyShopCheckResult;

    /**
     * @param shopId 双平台门店id，取决于isMT
     * @param isMT   是否美团侧
     * @return 是否包含门店
     */
    public boolean containsShopId(final long shopId, final boolean isMT) {
        if (verifyShopCheckResult != null && isVerifyShop(shopId, isMT ? verifyShopCheckResult.getMtVerifyShopCheckResult() : verifyShopCheckResult.getDpVerifyShopCheckResult())) {
            return true;
        }
        if (isMT) {
            if (CollectionUtils.isEmpty(mtDisplayShopIds)) {
                return true;
            }
            return mtDisplayShopIds.contains(shopId);
        } else {
            if (CollectionUtils.isEmpty(dpDisplayShopIds)) {
                return true;
            }
            return dpDisplayShopIds.contains(shopId);
        }
    }

    public long getDefaultDpShopId() {
        return CollectionUtils.isEmpty(dpDisplayShopIds) ? 0 : dpDisplayShopIds.get(0);
    }

    private Boolean isVerifyShop(long shopId, List<DealGroupShopRelationCheckResultDTO> relationCheckResultDTOList) {
        if (CollectionUtils.isEmpty(relationCheckResultDTOList)) {
            return false;
        }
        DealGroupShopRelationCheckResultDTO relationCheckResultDTO = relationCheckResultDTOList.stream().filter(Objects::nonNull).filter(r -> Long.valueOf(shopId).equals(r.getShopId())).findFirst().orElse(null);
        return relationCheckResultDTO != null && relationCheckResultDTO.getRelationCheckSuccess();
    }


}
