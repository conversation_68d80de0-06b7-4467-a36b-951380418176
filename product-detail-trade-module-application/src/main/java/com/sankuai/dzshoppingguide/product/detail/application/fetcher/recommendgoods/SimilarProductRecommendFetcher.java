package com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommendgoods;

import com.dianping.martgeneral.recommend.api.entity.RecommendParameters;
import com.dianping.martgeneral.recommend.api.enums.PlatformEnum;
import com.google.common.collect.Maps;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dz.product.detail.RequestCustomParamEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @create 2025/5/21 14:33
 */
@Fetcher(
        previousLayerDependencies = {
                ProductBaseInfoFetcher.class
        }
)
@Slf4j
public  class SimilarProductRecommendFetcher extends AbstractProductRecommendFetcher {

    private static final int BIJIA_PRODUCT_RECOMMEND = 514;
    @Override
    protected Map<String, Object> buildBizParams() {
        Map<String, Object> bizParams = Maps.newHashMap();
        bizParams.put("productTagIds", request.getProductId());
        bizParams.put("applyType", "display");
        //比价推荐商品列表参数补齐
        bizParams.put("productId", request.getProductId());
        bizParams.put("shopId", request.getPoiId());
        String joinNumStr = request.getCustomParam(RequestCustomParamEnum.joinNum);
        int joinNum = StringUtils.isNotBlank(joinNumStr) ? Integer.parseInt(joinNumStr) : 1;
        bizParams.put("restPersons", joinNum);
        bizParams.put("flowFlag", "001");
        bizParams.put("productType", "1001");
        return bizParams;
    }

    @Override
    int getBizId() {
        return BIJIA_PRODUCT_RECOMMEND;
    }

    @Override
    int getMinQueryNum() {
        return 0;
    }

}
