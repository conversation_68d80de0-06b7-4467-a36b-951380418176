package com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sale.info.enums;

import lombok.Getter;

/**
 * @Author: g<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025/4/2 16:35
 */
@Getter
public enum ProductSaleTypeEnum {

    COMMON_DEAL("普通团单", true),
    PLATFORM_PIN_TUAN("平台普通拼团", true),
    COST_EFFECTIVE_PIN_TUAN("特团拼团", true),
    PREVIEW_DEAL("预览团单", false),
//    WARM_UP_DEAL("秒杀预热团单", true),
    LEADS_DEAL("留资团单", false),
    MEMBER_EXCLUSIVE_DEAL("商家会员专属团单", false),
    PREPAY_DEAL("预付团单", true),
    FITNESS_CROSS_DEAL("健身通团单", false),
    FREE_RESERVATION_DEAL("0元预约团单", false),
    LIVE_SOURCE_DEAL("直播团单", true);

    private final String desc;

    /**
     * 是否支持直接交易
     */
    private final boolean supportForBuyDirectly;

    ProductSaleTypeEnum(String desc, boolean supportForBuyDirectly) {
        this.desc = desc;
        this.supportForBuyDirectly = supportForBuyDirectly;
    }

}