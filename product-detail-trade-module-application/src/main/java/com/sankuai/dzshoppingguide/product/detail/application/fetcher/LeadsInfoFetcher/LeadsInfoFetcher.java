package com.sankuai.dzshoppingguide.product.detail.application.fetcher.LeadsInfoFetcher;
;
import com.dianping.deal.detail.enums.PlatformEnum;
import com.dianping.product.shelf.common.dto.activity.ActivityDetailDTO;
import com.dianping.product.shelf.common.request.activities.ActivityProductQueryRequest;
import com.google.common.collect.Lists;
import com.sankuai.clr.content.process.gateway.thrift.dto.leads.LoadLeadsInfoReqDTO;
import com.sankuai.clr.content.process.gateway.thrift.dto.leads.LoadLeadsInfoRespDTO;
import com.sankuai.dealuser.price.display.api.enums.ProductTypeEnum;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.dealgroup.DealGroupIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
import com.sankuai.dzshoppingguide.product.detail.application.utils.DealUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @create 2025/3/7 15:35
 */
@Fetcher(previousLayerDependencies = {DealGroupIdMapperFetcher.class, ProductBaseInfoFetcher.class})
@Slf4j
public class LeadsInfoFetcher extends NormalFetcherContext<LeadsInfoResult> {

    @Resource
    CompositeAtomService compositeAtomService;


    @Override
    protected CompletableFuture<LeadsInfoResult> doFetch() throws TException {
        ProductBaseInfo dealGroupInfo = getDependencyResult(ProductBaseInfoFetcher.class);
        if (Objects.nonNull(dealGroupInfo) && DealUtils.isLeadsDeal(dealGroupInfo)) {
            CompletableFuture<LoadLeadsInfoRespDTO> loadLeadsInfoCf = compositeAtomService.loadLeadsInfo(buildLoadLeadsInfoReq());
            CompletableFuture<ActivityDetailDTO> activityDetailCf = compositeAtomService.querySpecialValueDeal(buildActivityProductQueryReq());
            return CompletableFuture.allOf(loadLeadsInfoCf, activityDetailCf).thenApply(a -> new LeadsInfoResult(activityDetailCf.join(), loadLeadsInfoCf.join()));

        }
        return CompletableFuture.completedFuture(new LeadsInfoResult());
    }

    public LoadLeadsInfoReqDTO buildLoadLeadsInfoReq(){
        LoadLeadsInfoReqDTO reqDTO = new LoadLeadsInfoReqDTO();
        reqDTO.setPoiId(request.getPoiId());
        reqDTO.setPlatform(request.getClientTypeEnum().isMtClientType() ? PlatformEnum.MEI_TUAN.getType() : PlatformEnum.DIAN_PING.getType());
        reqDTO.setMerchantType(1);
        reqDTO.setEntranceCode(20);
        long dealGroupId = request.getProductId();
        if ( request.getClientTypeEnum().isMtClientType()) {
            reqDTO.setMtDealGroupId(dealGroupId);
        } else {
            reqDTO.setDpDealGroupId(dealGroupId);
        }
        return reqDTO;
    }

    public ActivityProductQueryRequest buildActivityProductQueryReq(){
        ActivityProductQueryRequest req = new ActivityProductQueryRequest();
        req.setMt(request.getClientTypeEnum().isMtClientType());
        req.setShopId(request.getPoiId());
        req.setProductIds(Lists.newArrayList(Math.toIntExact(request.getProductId())));
        req.setProductType(ProductTypeEnum.DEAL.getType()); // 团单类型为1
        return req;
    }

}
