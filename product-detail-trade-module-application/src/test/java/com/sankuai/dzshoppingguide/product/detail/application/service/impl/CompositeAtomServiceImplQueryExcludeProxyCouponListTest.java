package com.sankuai.dzshoppingguide.product.detail.application.service.impl;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.dianping.spc.common.Response;
import com.dianping.tgc.open.entity.BatchExProxyCouponRequest;
import com.dianping.tgc.open.entity.BatchExProxyCouponResponseDTO;
import com.dianping.tgc.open.v2.TGCGetCouponComponentQueryService;
import java.util.concurrent.CompletableFuture;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class CompositeAtomServiceImplQueryExcludeProxyCouponListTest {

    @InjectMocks
    private CompositeAtomServiceImpl compositeAtomService;

    @Mock
    private TGCGetCouponComponentQueryService couponComponentQueryServiceFuture;

    /**
     * 测试queryExcludeProxyCouponList方法，正常情况
     */
    @Test
    public void testQueryExcludeProxyCouponListNormal() throws Throwable {
        // arrange
        BatchExProxyCouponRequest request = new BatchExProxyCouponRequest();
        BatchExProxyCouponResponseDTO responseDTO = new BatchExProxyCouponResponseDTO();
        Response<BatchExProxyCouponResponseDTO> response = new Response<>(true, 200, "Success", responseDTO);
        when(couponComponentQueryServiceFuture.queryExcludeProxyCouponList(request)).thenReturn(response);
        // act
        CompletableFuture<BatchExProxyCouponResponseDTO> result = compositeAtomService.queryExcludeProxyCouponList(request);
        // assert
        assertNotNull(result);
        verify(couponComponentQueryServiceFuture, times(1)).queryExcludeProxyCouponList(request);
    }

    /**
     * 测试queryExcludeProxyCouponList方法，异常情况
     */
    @Test(expected = Exception.class)
    public void testQueryExcludeProxyCouponListException() throws Throwable {
        // arrange
        BatchExProxyCouponRequest request = new BatchExProxyCouponRequest();
        when(couponComponentQueryServiceFuture.queryExcludeProxyCouponList(request)).thenThrow(new Exception());
        // act
        compositeAtomService.queryExcludeProxyCouponList(request);
        // assert
        verify(couponComponentQueryServiceFuture, times(1)).queryExcludeProxyCouponList(request);
    }
}
