package com.sankuai.dzshoppingguide.product.detail.spi.bookingtime.vo;

import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class BookingDateTab implements Serializable {
    /**
     * 子标题
     */
    @MobileField
    private String subTitle;

    /**
     * 主标题
     */
    @MobileField
    private String title;

    /**
     * 无可订时间提示信息
     */
    @MobileField
    private String tips;

    /**
     * 时间卡片
     */
    @MobileField
    private List<BookingTimeCardVO> timeCardList;

}
