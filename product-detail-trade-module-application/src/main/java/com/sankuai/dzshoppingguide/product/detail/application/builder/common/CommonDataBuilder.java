package com.sankuai.dzshoppingguide.product.detail.application.builder.common;

import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.BaseBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategoryFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.defaultselect.SkuDefaultSelect;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.defaultselect.SkuDefaultSelectFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.FetcherDAGStarter;
import com.sankuai.dzshoppingguide.product.detail.spi.common.CommonDataVO;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import org.apache.commons.lang.StringUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 * @create 2025/3/21 14:15
 */
@Builder(
        moduleKey = ModuleKeyConstants.TRADE_COMMON_DATA,
        startFetcher = FetcherDAGStarter.class,
        dependentFetchers = {
                ProductBaseInfoFetcher.class,
                ProductCategoryFetcher.class,
                SkuDefaultSelectFetcher.class
        }
)
public class CommonDataBuilder extends BaseBuilder<CommonDataVO> {

    @Override
    public CommonDataVO doBuild() {
        long selectedSkuId = getDependencyResult(SkuDefaultSelectFetcher.class, SkuDefaultSelect.class)
                .map(SkuDefaultSelect::getSelectedSkuId)
                .orElse(0L);
        ProductBaseInfo productBaseInfo = getDependencyResult(ProductBaseInfoFetcher.class);
        ProductCategory productCategory = getDependencyResult(ProductCategoryFetcher.class);
        if (productBaseInfo == null || productCategory == null) {
            throw new IllegalArgumentException("productBaseInfo or productCategory is null");
        }
        CommonDataVO vo = new CommonDataVO();
        vo.setProductType(productBaseInfo.getProductIdDTO().getProductType().getCode());
        vo.setDpProductId(productBaseInfo.getProductIdDTO().getDpProductId());
        vo.setMtProductId(productBaseInfo.getProductIdDTO().getMtProductId());
        vo.setProductFirstCategoryId(productCategory.getProductFirstCategoryId());
        vo.setProductSecondCategoryId(productCategory.getProductSecondCategoryId());
        vo.setProductThirdCategoryId(productCategory.getProductThirdCategoryId());
        vo.setSkuId(selectedSkuId);
        vo.setSkuName(getSkuName(vo.getSkuId(), productBaseInfo));
        return vo;
    }

    private String getSkuName(long skuId, ProductBaseInfo productBaseInfo) {
        return productBaseInfo.getDeals().stream()
                .filter(deal -> deal.getDealId() == skuId)
                .filter(deal -> Objects.nonNull(deal.getBasic()) && StringUtils.isNotBlank(deal.getBasic().getTitle()))
                .map(deal -> deal.getBasic().getTitle()).findFirst().orElse("");
    }

}
