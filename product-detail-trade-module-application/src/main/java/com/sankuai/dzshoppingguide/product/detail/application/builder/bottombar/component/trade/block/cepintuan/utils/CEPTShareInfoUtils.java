package com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.component.trade.block.cepintuan.utils;

import com.sankuai.dzshoppingguide.product.detail.application.fetcher.cepinpool.dto.CostEffectivePinTuan;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.utils.AppImageSize;
import com.sankuai.dzshoppingguide.product.detail.application.utils.ImageHelper;
import com.sankuai.dzshoppingguide.product.detail.application.utils.NumbersUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.extend.cepintuanshare.CEPinTuanShareActionVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.extend.cepintuanshare.CEPinTuanShareData;
import com.sankuai.general.product.query.center.client.dto.DealGroupImageDTO;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

import javax.annotation.Nullable;
import java.util.Optional;

/**
 * @Author: guangyujie
 * @Date: 2025/3/20 23:28
 */
public class CEPTShareInfoUtils {

    public static CEPinTuanShareActionVO buildShareInfo(@NotNull final CostEffectivePinTuan costEffectivePinTuan,
                                                         @Nullable final ProductBaseInfo productBaseInfo) {
        CEPinTuanShareData shareInfo = new CEPinTuanShareData();
        shareInfo.setTitle(costEffectivePinTuan.getPinTuanPassParamConfig().getTitle());
        shareInfo.setJumpUrl(CEPTUrlUtils.getWxShareJumpUrl(costEffectivePinTuan));
        shareInfo.setBackground(costEffectivePinTuan.getPinTuanPassParamConfig().getBackground());
        shareInfo.setDealImage(getDealImage(productBaseInfo));
        shareInfo.setTemplateId(costEffectivePinTuan.getPinTuanPassParamConfig().getTemplateId());
        shareInfo.setOrderGroupId(costEffectivePinTuan.getShareToken());
        shareInfo.setSceneType(costEffectivePinTuan.getSceneType());
        shareInfo.setPintuanActivityId(costEffectivePinTuan.getPinTuanActivityId());
        //todo 先写死
        shareInfo.setFinalPrice("38.8");
        shareInfo.setPromoPrice("38.8");
        shareInfo.setMarketPromoDiscount("4折");
        shareInfo.setMarketPrice("100");
        return new CEPinTuanShareActionVO(shareInfo);
    }

    /**
     * 获取分享团单图片
     */
    private static String getDealImage(@Nullable final ProductBaseInfo productBaseInfo) {
        String defaultPic = Optional.ofNullable(productBaseInfo)
                .map(ProductBaseInfo::getImage)
                .map(DealGroupImageDTO::getDefaultPicPath)
                .orElse(null);
        if (StringUtils.isBlank(defaultPic)) {
            return null;
        }
        int[] imageSize = convertWidthHeight(AppImageSize.MINI_PROGRAM_SQUARE_SHARE.width, AppImageSize.MINI_PROGRAM_SQUARE_SHARE.height);
        if (imageSize != null) {
            return ImageHelper.formatWithoutWatermark(defaultPic, imageSize[0], imageSize[1], true);
        } else {
            return ImageHelper.formatWithoutWatermark(defaultPic, AppImageSize.MEDIUM.width, AppImageSize.MEDIUM.height, true);
        }
    }

    /**
     * 输入图片宽高，高不变，宽高比5:4，输出新的宽高
     */
    private static int[] convertWidthHeight(int width, int height) {
        if (!NumbersUtils.greaterThanZero(width) || !NumbersUtils.greaterThanZero(height)) {
            return null;
        }
        int newWidth = (int) (height * 5.0 / 4.0);
        int newHeight = height;
        if (newWidth > width) {
            newWidth = width;
            newHeight = (int) (width * 4.0 / 5.0);
        }
        return new int[]{newWidth, newHeight};
    }


}
