package com.sankuai.dzshoppingguide.product.detail.application.fetcher.dealgift;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import com.sankuai.mktplay.center.mkt.play.center.client.PlayExecuteResponse;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @create 2025/3/10 10:19
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DealGiftResult extends FetcherReturnValueDTO {
    private PlayExecuteResponse playActivityResponse;
    private PlayExecuteResponse playResponse;
}
