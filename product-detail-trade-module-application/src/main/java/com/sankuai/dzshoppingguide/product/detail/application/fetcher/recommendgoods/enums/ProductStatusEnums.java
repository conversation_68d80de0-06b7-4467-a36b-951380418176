package com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommendgoods.enums;

/**
 * 商品状态枚举
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2021/11/22.
 */
public enum ProductStatusEnums {
    ONLINE(0, "在线状态"),
    BLACKLIST(1, "黑名单列表"),
    OFFLINE(2, "下线状态");

    private int status;
    private String desc;

    ProductStatusEnums(int status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public int getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }
}
