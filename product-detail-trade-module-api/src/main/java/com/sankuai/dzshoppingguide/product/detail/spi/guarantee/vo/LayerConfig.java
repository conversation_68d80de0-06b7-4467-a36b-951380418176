package com.sankuai.dzshoppingguide.product.detail.spi.guarantee.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dz.product.detail.gateway.spi.vo.AbstractModuleVO;
import lombok.Data;

import java.io.Serializable;

@MobileDo(id = 0x30c1)
@Data
public class LayerConfig implements Serializable {


    /**
    * 小程序跳链
    */
    @MobileDo.MobileField(key = 0xf6a0)
    private String miniJumpUrl;

    /**
    * 不同样式对应的值，比如高亮下发对应颜色值
    */
    @MobileDo.MobileField(key = 0xddfb)
    private String textStyle;

    /**
    * 信息展示类型，0-纯文本，1-高亮
    */
    @MobileDo.MobileField(key = 0x2e2a)
    private int textType;

    /**
    * 价保类型 1-退改协议，2-价保
    */
    @MobileDo.MobileField(key = 0x8f0c)
    private int type;

    /**
    * 价保描述
    */
    @MobileDo.MobileField(key = 0xfebf)
    private String desc;

    /**
    * 价保跳链
    */
    @MobileDo.MobileField(key = 0x774e)
    private String jumpUrl;

    /**
    * 价保标题
    */
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    /**
    * 价保标签
    */
    @MobileDo.MobileField(key = 0x3c48)
    private String icon;

    public String getMiniJumpUrl() {
        return miniJumpUrl;
    }

    public void setMiniJumpUrl(String miniJumpUrl) {
        this.miniJumpUrl = miniJumpUrl;
    }

    public String getTextStyle() {
        return textStyle;
    }

    public void setTextStyle(String textStyle) {
        this.textStyle = textStyle;
    }

    public int getTextType() {
        return textType;
    }

    public void setTextType(int textType) {
        this.textType = textType;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getJumpUrl() {
        return jumpUrl;
    }

    public void setJumpUrl(String jumpUrl) {
        this.jumpUrl = jumpUrl;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }


}