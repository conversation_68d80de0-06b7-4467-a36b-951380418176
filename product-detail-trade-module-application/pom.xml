<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.sankuai.dzshoppingguide.detail</groupId>
        <artifactId>product-detail-trade-module</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>product-detail-trade-module-application</artifactId>
    <version>${revision}</version>
    <packaging>jar</packaging>
    <name>product-detail-trade-module-application</name>

    <dependencies>
        <!-- 模块化编排框架 -->
        <dependency>
            <groupId>com.sankuai.dz</groupId>
            <artifactId>product-detail-module-arrange-framework</artifactId>
        </dependency>
        <!-- 模块化编排框架 -->
        <!-- Project module -->
        <dependency>
            <groupId>com.sankuai.dzshoppingguide.detail</groupId>
            <artifactId>product-detail-trade-module-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.dzshoppingguide.detail</groupId>
            <artifactId>product-detail-trade-module-domain</artifactId>
        </dependency>
        <!-- Project module -->
        <!-- 提单页跳链 -->
        <dependency>
            <groupId>com.dianping.tuangou</groupId>
            <artifactId>dztg-usercenter-api</artifactId>
            <version>2.3.8</version>
            <exclusions>
                <exclusion>
                    <artifactId>deal-common-api</artifactId>
                    <groupId>com.dianping</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- 提单页跳链 -->
        <!-- POI查询 -->
        <dependency>
            <groupId>com.meituan.service.mobile.poi</groupId>
            <artifactId>sinai.client</artifactId>
            <version>3.1.13</version>
            <exclusions>
                <exclusion>
                    <groupId>org.scala-lang</groupId>
                    <artifactId>scala-library</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>activation</artifactId>
                    <groupId>javax.activation</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- POI查询 -->
        <!-- 头图相关 -->
        <dependency>
            <groupId>com.dianping.piccentercloud</groupId>
            <artifactId>piccenter-display-api</artifactId>
            <version>0.2.31</version>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>lion-facade</artifactId>
            <version>2.2.5</version>
        </dependency>
        <!-- 头图相关 -->
        <dependency>
            <groupId>com.sankuai.spt</groupId>
            <artifactId>spt-statequery-api</artifactId>
            <version>1.0.4</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.dztrade</groupId>
            <artifactId>dztrade-common</artifactId>
            <version>2.4.0</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.pay</groupId>
            <artifactId>pay-promo-rule-api</artifactId>
            <version>0.1.9</version>
                <exclusions>
                    <exclusion>
                        <artifactId>javassist</artifactId>
                        <groupId>javassist</groupId>
                    </exclusion>
                </exclusions>
        </dependency>
        <!--折扣卡-->
        <dependency>
            <groupId>com.dianping.dp</groupId>
            <artifactId>gm-marketing-member-card-api</artifactId>
            <version>0.2.34</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.mdp</groupId>
            <artifactId>dztheme-massagebook-api</artifactId>
            <version>0.0.19</version>
            <exclusions>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.nib.mkt</groupId>
            <artifactId>promotion-api</artifactId>
            <version>2.0.41</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.technician</groupId>
            <artifactId>technician-trade-api</artifactId>
            <version>0.5.1</version>
            <exclusions>
                <exclusion>
                    <artifactId>avatar-dao</artifactId>
                    <groupId>com.dianping</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping.joy</groupId>
            <artifactId>joy-booking-shop-api</artifactId>
            <version>1.0.9</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai</groupId>
            <artifactId>athena-inf-cache</artifactId>
            <version>0.1.1</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.dzcard</groupId>
            <artifactId>dzcard-navigation-api</artifactId>
            <version>0.0.32</version>
            <exclusions>
                <exclusion>
                    <artifactId>validation-api</artifactId>
                    <groupId>javax.validation</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>arts-client</artifactId>
                    <groupId>com.dp.arts</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>sku-common</artifactId>
                    <groupId>com.dianping.tpfun</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>technician-vc-api</artifactId>
            <version>1.19.15</version>
            <exclusions>
                <exclusion>
                    <groupId>com.sankuai</groupId>
                    <artifactId>beautycontent.store.api</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>arts-client</artifactId>
                    <groupId>com.dp.arts</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--折扣卡-->
        <!--用户收藏-->
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>user-remote</artifactId>
            <version>2.3.12</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.user.collection</groupId>
            <artifactId>coll-client</artifactId>
            <version>1.5.8</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.wpt.collection</groupId>
            <artifactId>thrift-api</artifactId>
            <version>1.2.2</version>
        </dependency>
        <!--用户收藏-->
        <!--图文详情-->
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>deal-detail-api</artifactId>
            <version>2.0.3</version>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>mapi-usercenter-api</artifactId>
            <version>0.0.30</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai</groupId>
            <artifactId>dztheme-generalproduct-api</artifactId>
            <version>2.0.1.5</version>
        </dependency>
        <!--图文详情-->
        <!--idmapper-->

        <!--cityId转换服务,新增加的服务不支持接入了,移步openplatform-dependency-->
        <!-- https://km.sankuai.com/page/435322840 -->
        <!--<dependency>-->
        <!--    <groupId>com.dianping.poi</groupId>-->
        <!--    <artifactId>poi-gis-api</artifactId>-->
        <!--    <version>0.5.67</version>-->
        <!--</dependency>-->
        <dependency>
            <groupId>com.sankuai.map.maf</groupId>
            <artifactId>openplatform-dependency</artifactId>
            <version>1.2.50</version>
        </dependency>
        <!--idmapper-->
        <!--bestShop-->
        <dependency>
            <groupId>com.sankuai.sinai</groupId>
            <artifactId>sinai-api</artifactId>
            <version>1.0.87</version>
        </dependency>
        <!-- 批量查询搭售黑名单 -->
        <dependency>
            <groupId>com.sankuai.mppack.product</groupId>
            <artifactId>mppack-product-client</artifactId>
            <version>1.1.41</version>
        </dependency>
        <!--替换下面的deal-shop-api接口-->
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>general-unified-search-api</artifactId>
            <version>1.8.15</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.deal</groupId>
            <artifactId>deal-shop-api</artifactId>
            <version>2.3.20</version>
            <exclusions>
                <exclusion>
                    <artifactId>deal-common-api</artifactId>
                    <groupId>com.dianping</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--bestShop-->
<!--        &lt;!&ndash;到综团购团单详情结构化信息查询服务&ndash;&gt;-->
<!--        <dependency>-->
<!--            <groupId>com.dianping.deal</groupId>-->
<!--            <artifactId>deal-struct-query-api</artifactId>-->
<!--            <version>1.0.5</version>-->
<!--        </dependency>-->
<!--        &lt;!&ndash;到综团购团单详情结构化信息查询服务&ndash;&gt;-->

        <!-- 调用交易接口trade-general-reserve-api判断当前团单是否支持在线预约 -->
        <dependency>
            <groupId>com.sankuai</groupId>
            <artifactId>trade-general-reserve-api</artifactId>
            <version>0.0.6</version>
        </dependency>
        <!-- 调用交易接口trade-general-reserve-api判断当前团单是否支持在线预约 -->

        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-junit4</artifactId>
            <!--<version>2.0.0</version>-->
            <version>2.0.7</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.mpproduct.plugin</groupId>
            <artifactId>pp-misc-common</artifactId>
            <version>1.1.4</version>
        </dependency>

        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-api-mockito2</artifactId>
            <version>2.0.0</version>
        </dependency>
        <!--标签查询接口-->
        <dependency>
            <groupId>com.dianping.deal</groupId>
            <artifactId>deal-tag-query-api</artifactId>
            <version>1.0.25</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.deal</groupId>
            <artifactId>deal-tag-common</artifactId>
            <version>1.0.26</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.deal</groupId>
            <artifactId>deal-meta-tag-manage-api</artifactId>
            <version>1.0.25</version>
            <exclusions>
                <exclusion>
                    <artifactId>asm</artifactId>
                    <groupId>asm</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jaxb-api</artifactId>
                    <groupId>javax.xml.bind</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>javax.activation-api</artifactId>
                    <groupId>javax.activation</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--标签查询接口-->
        <!--新商家会员接口-->

        <dependency>
            <groupId>com.sankuai.mpmctmember</groupId>
            <artifactId>mpmctmember-query-thrift</artifactId>
            <version>1.0.11</version>
            <exclusions>
                <exclusion>
                    <artifactId>validation-api</artifactId>
                    <groupId>javax.validation</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.mpmctmember</groupId>
            <artifactId>mpmctmember-common-utils</artifactId>
            <version>1.0.0</version>
        </dependency>
        <!--新商家会员接口-->
        <!-- 公益商家相关 -->
        <dependency>
            <groupId>com.sankuai.meituan.charity.merchant.main</groupId>
            <artifactId>charity-merchant-main-sdk</artifactId>
            <version>3.3.24</version>
        </dependency>
        <!-- 公益商家相关 -->

        <dependency>
            <groupId>com.dianping.tgc</groupId>
            <artifactId>tgc-open-remote</artifactId>
            <version>0.1.63</version>
            <exclusions>
                <exclusion>
                    <groupId>com.meituan.mafka</groupId>
                    <artifactId>mafka-client_2.9</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.clr</groupId>
            <artifactId>clr-content-process-gateway-thrift</artifactId>
            <version>0.0.42</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping.deal</groupId>
            <artifactId>product-shelf-query-api</artifactId>
            <version>1.9.5</version>
            <exclusions>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-annotations</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-databind</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.meituan.mdp.boot</groupId>
                    <artifactId>mdp-boot-starter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.meituan.mdp.boot</groupId>
                    <artifactId>mdp-boot-starter-log</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.nibscp.common</groupId>
            <artifactId>flow-identify-sdk</artifactId>
            <version>1.0.18</version>
            <scope>compile</scope>
        </dependency>
        <!-- 团单销量查询接口 -->
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>deal-sales-display-api</artifactId>
            <version>2.1.3</version>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>deal-sales-common-api</artifactId>
            <version>2.1.3</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.spt</groupId>
            <artifactId>spt-common-api</artifactId>
            <version>1.0.4</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.gmkt</groupId>
            <artifactId>gmkt-scene-engine-api</artifactId>
            <version>2.0.10</version>
            <exclusions>
                <exclusion>
                    <artifactId>validation-api</artifactId>
                    <groupId>javax.validation</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.inf.leaf</groupId>
            <artifactId>leaf-idl</artifactId>
        </dependency>
        <!--立减标签服务-->
        <dependency>
            <groupId>com.dianping.pay</groupId>
            <artifactId>pay-promo-display-api</artifactId>
            <version>0.2.16.1_jdk7</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.vc</groupId>
            <artifactId>dztheme-deal-api</artifactId>
            <version>1.1.15</version>
            <exclusions>
                <exclusion>
                    <groupId>com.sankuai.general.product</groupId>
                    <artifactId>client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--在线咨询链接服务-->
        <dependency>
            <groupId>com.sankuai.dzim</groupId>
            <artifactId>cliententry-api</artifactId>
            <version>0.0.31</version>
        </dependency>
        <!-- 智能客服 -->
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>csc-access-facade-api</artifactId>
            <version>0.0.66</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.tuangou</groupId>
            <artifactId>dzim-common</artifactId>
            <version>1.2.33</version>
            <exclusions>
                <exclusion>
                    <artifactId>mtrace-api</artifactId>
                    <groupId>com.meituan.mtrace</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.merchantcard</groupId>
            <artifactId>timescard-exposure-api</artifactId>
            <version>0.1.9</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.mlive.goods.trade</groupId>
            <artifactId>mlive-goods-trade-api</artifactId>
            <version>1.0.14</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-jetty</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.sankuai.mlive.goods</groupId>
                    <artifactId>mlive-goods-common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.nib.price.operation</groupId>
            <artifactId>price-operation-api</artifactId>
            <version>1.0.26</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.shangou.statesubsidies</groupId>
            <artifactId>c-client</artifactId>
            <version>1.0.14</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.tpfun</groupId>
            <artifactId>sku-underlayer-api</artifactId>
            <version>1.2.14</version>
        </dependency>

        <!-- 月付分期标签查询 -->
        <dependency>
            <groupId>com.sankuai.meituan</groupId>
            <artifactId>xy-trade-creditpay-access-api</artifactId>
            <version>1.1.48</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.general</groupId>
            <artifactId>martgeneral-recommend-api</artifactId>
            <version>1.7.6</version>
        </dependency>

        <!--价格带查询接口依赖包-->
        <dependency>
            <groupId>com.sankuai.tpfun</groupId>
            <artifactId>sku-operation-api</artifactId>
            <version>0.0.20</version>
            <exclusions>
                <exclusion>
                    <groupId>javax.validation</groupId>
                    <artifactId>validation-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--海马平台-->
        <dependency>
            <groupId>com.dianping.haima</groupId>
            <artifactId>haima-client</artifactId>
        </dependency>
        <!--团购次卡先用后付-->
        <dependency>
            <groupId>com.sankuai.fincreditpay</groupId>
            <artifactId>bnpl-client</artifactId>
            <version>1.0.24</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.mpmctmember</groupId>
            <artifactId>mpmctmember-process-common</artifactId>
            <version>0.0.3</version>
        </dependency>

        <!-- <dependency> -->
        <!--     <groupId>com.sankuai</groupId> -->
        <!--     <artifactId>athena-product-nr</artifactId> -->
        <!--     <version>0.0.24</version> -->
        <!--     <exclusions> -->
        <!--         <exclusion> -->
        <!--             <groupId>om.dianping.deal</groupId> -->
        <!--             <artifactId>deal-meta-tag-manage-api</artifactId> -->
        <!--         </exclusion> -->
        <!--         <exclusion> -->
        <!--             <groupId>org.mortbay.jetty</groupId> -->
        <!--             <artifactId>jetty</artifactId> -->
        <!--         </exclusion> -->
        <!--         <exclusion> -->
        <!--             <groupId>org.mortbay.jetty</groupId> -->
        <!--             <artifactId>servlet-api</artifactId> -->
        <!--         </exclusion> -->
        <!--         <exclusion> -->
        <!--             <groupId>com.dianping.joy</groupId> -->
        <!--             <artifactId>joy-stock-api</artifactId> -->
        <!--         </exclusion> -->
        <!--         <exclusion> -->
        <!--             <groupId>com.dianping.deal</groupId> -->
        <!--             <artifactId>product-shelf-query-api</artifactId> -->
        <!--         </exclusion> -->
        <!--         <exclusion> -->
        <!--             <groupId>com.dianping.deal</groupId> -->
        <!--             <artifactId>deal-shop-api</artifactId> -->
        <!--         </exclusion> -->
        <!--         <exclusion> -->
        <!--             <artifactId>joy-aggregate-api</artifactId> -->
        <!--             <groupId>com.dianping.joy</groupId> -->
        <!--         </exclusion> -->
        <!--     </exclusions> -->
        <!-- </dependency> -->
    </dependencies>
</project>
