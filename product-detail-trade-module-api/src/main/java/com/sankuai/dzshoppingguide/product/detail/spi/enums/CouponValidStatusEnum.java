package com.sankuai.dzshoppingguide.product.detail.spi.enums;

/**
 * 功能描述:  <p>
 *
 * <AUTHOR> lintao<PERSON>i <p>
 * @version 2024/3/20
 * @since mapi-dztgdetail-web
 */
public enum CouponValidStatusEnum {

    UN_VALID(0, "无效"),
    VALID(1, "有效");


    final private int code;
    final private String name;

    CouponValidStatusEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }


}
