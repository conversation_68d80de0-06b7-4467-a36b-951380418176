package com.sankuai.dzshoppingguide.product.detail.application.fetcher.LeadsInfoFetcher;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.dianping.deal.detail.enums.PlatformEnum;
import com.dianping.product.shelf.common.dto.activity.ActivityDetailDTO;
import com.dianping.product.shelf.common.request.activities.ActivityProductQueryRequest;
import com.sankuai.clr.content.process.gateway.thrift.dto.leads.LoadLeadsInfoReqDTO;
import com.sankuai.clr.content.process.gateway.thrift.dto.leads.LoadLeadsInfoRespDTO;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
import java.lang.reflect.Field;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import org.apache.thrift.TException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

@ExtendWith(MockitoExtension.class)
class LeadsInfoFetcherDoFetch1Test {

    @Mock
    private CompositeAtomService compositeAtomService;

    @Mock
    private ProductDetailPageRequest request;

    @InjectMocks
    private LeadsInfoFetcher leadsInfoFetcher;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    /**
     * 通过反射设置protected的request字段
     */
    private void setRequest(ProductDetailPageRequest request) throws Exception {
        Field requestField = leadsInfoFetcher.getClass().getSuperclass().getSuperclass().getDeclaredField("request");
        requestField.setAccessible(true);
        requestField.set(leadsInfoFetcher, request);
    }

    /**
     * 测试点评客户端场景
     * 验证当客户端类型为DP_APP时，正确设置点评平台和点评团购ID
     */
    @Test
    void testBuildLoadLeadsInfoReq_DPClient() throws Throwable {
        // arrange
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.DP_APP);
        when(request.getPoiId()).thenReturn(54321L);
        when(request.getProductId()).thenReturn(98765L);
        setRequest(request);
        // act
        LoadLeadsInfoReqDTO result = leadsInfoFetcher.buildLoadLeadsInfoReq();
        // assert
        assertNotNull(result);
        assertEquals(54321L, result.getPoiId());
        assertEquals(98765L, result.getDpDealGroupId());
        assertNull(result.getMtDealGroupId());
        assertEquals(PlatformEnum.DIAN_PING.getType(), result.getPlatform());
        assertEquals(1, result.getMerchantType());
        assertEquals(20, result.getEntranceCode());
    }

    /**
     * 测试最大值场景
     * 验证当输入值为Long.MAX_VALUE时的处理逻辑
     */
    @Test
    void testBuildLoadLeadsInfoReq_MaxValues() throws Throwable {
        // arrange
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.DP_APP);
        when(request.getPoiId()).thenReturn(Long.MAX_VALUE);
        when(request.getProductId()).thenReturn(Long.MAX_VALUE);
        setRequest(request);
        // act
        LoadLeadsInfoReqDTO result = leadsInfoFetcher.buildLoadLeadsInfoReq();
        // assert
        assertNotNull(result);
        assertEquals(Long.MAX_VALUE, result.getPoiId());
        assertEquals(Long.MAX_VALUE, result.getDpDealGroupId());
        assertNull(result.getMtDealGroupId());
        assertEquals(PlatformEnum.DIAN_PING.getType(), result.getPlatform());
        assertEquals(1, result.getMerchantType());
        assertEquals(20, result.getEntranceCode());
    }

    /**
     * 测试未知客户端类型默认处理为点评平台
     * 验证当客户端类型未知时，默认使用点评平台的处理逻辑
     */
    @Test
    void testBuildLoadLeadsInfoReq_UnknownClientType() throws Throwable {
        // arrange
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.DP_APP);
        when(request.getPoiId()).thenReturn(11111L);
        when(request.getProductId()).thenReturn(22222L);
        setRequest(request);
        // act
        LoadLeadsInfoReqDTO result = leadsInfoFetcher.buildLoadLeadsInfoReq();
        // assert
        assertNotNull(result);
        assertEquals(11111L, result.getPoiId());
        assertEquals(22222L, result.getDpDealGroupId());
        assertNull(result.getMtDealGroupId());
        assertEquals(PlatformEnum.DIAN_PING.getType(), result.getPlatform());
        assertEquals(1, result.getMerchantType());
        assertEquals(20, result.getEntranceCode());
    }

    /**
     * 测试空请求场景
     * 验证当请求为null时抛出NullPointerException
     */
    @Test
    void testBuildLoadLeadsInfoReq_NullRequest() throws Throwable {
        // arrange
        setRequest(null);
        // act & assert
        assertThrows(NullPointerException.class, () -> {
            leadsInfoFetcher.buildLoadLeadsInfoReq();
        });
    }

    /**
     * Test case for non-MT_APP client type (e.g., DP_APP)
     */
    @Test
    public void testBuildActivityProductQueryReq_WhenNotMtApp() throws Throwable {
        // arrange
        // Using enum code value (100)
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.DP_APP);
        when(request.getPoiId()).thenReturn(123L);
        when(request.getProductId()).thenReturn(456L);
        // act
        ActivityProductQueryRequest result = leadsInfoFetcher.buildActivityProductQueryReq();
        // assert
        assertFalse(result.isMt());
        assertEquals(123L, result.getShopId());
        assertEquals(456, result.getProductIds().get(0));
        assertEquals(1, result.getProductType());
    }
}
