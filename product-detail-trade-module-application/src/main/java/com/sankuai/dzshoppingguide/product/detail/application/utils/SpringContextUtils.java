package com.sankuai.dzshoppingguide.product.detail.application.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

import java.util.Map;

@Slf4j
public class SpringContextUtils implements ApplicationContextAware {

    private static ApplicationContext APPLICATION_CONTEXT;

    /**
     * 获取类型为requiredType的对象 如果bean不能被类型转换，相应的异常将会被抛出（BeanNotOfRequiredTypeException）
     *
     * @param name         bean注册名
     * @param requiredType 返回对象类型
     * @param <T>          bean 的类型
     * @return Object 返回requiredType类型对象
     */
    public static synchronized <T> T getBean(String name, Class<T> requiredType) {
        try {
            return APPLICATION_CONTEXT.getBean(name, requiredType);
        } catch (NullPointerException | BeansException e) {
            log.error(" BeanName:{} not exist，Exception => {}", name, e);
        }
        return null;
    }

    @Override
    public void setApplicationContext(ApplicationContext context) {
        APPLICATION_CONTEXT = context;
    }

    public static synchronized <T> Map<String, T> getBeansOfType(Class<T> clazz) {
        return APPLICATION_CONTEXT.getBeansOfType(clazz);
    }

}
