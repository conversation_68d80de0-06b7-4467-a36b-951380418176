package com.sankuai.dzshoppingguide.product.detail.application.service.promo;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.dianping.cat.Cat;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.utils.context.DealCtxUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.promo.vo.PromoDetailModule;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.PriceDTO;
import java.math.BigDecimal;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Spy;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class PromoDetailServiceBuildFreeDealPromoDetailInfoTest {

    @Mock
    private ProductBaseInfo dealGroupBase;

    @Mock
    private DealGroupCategoryDTO category;

    @Mock
    private PriceDTO price;

    private PromoDetailService promoDetailService;

    @BeforeEach
    void setUp() {
        promoDetailService = new PromoDetailService();
        when(dealGroupBase.getCategory()).thenReturn(category);
        when(category.getCategoryId()).thenReturn(1L);
        when(dealGroupBase.getPrice()).thenReturn(price);
        when(price.getSalePrice()).thenReturn("100.00");
    }

    /**
     * 测试当 promoDetailModule 为 null 时，会创建一个新的 PromoDetailModule 对象
     */
    @Test
    public void testBuildFreeDealPromoDetailInfoPromoDetailModuleIsNull() throws Throwable {
        // act
        promoDetailService.buildFreeDealPromoDetailInfo(dealGroupBase, null);
        // assert
        verify(dealGroupBase).getPrice();
        verify(price).getSalePrice();
    }

    /**
     * 测试当 promoDetailModule 不为 null 时，直接设置 finalPrice
     */
    @Test
    public void testBuildFreeDealPromoDetailInfoPromoDetailModuleIsNotNull() throws Throwable {
        // arrange
        PromoDetailModule promoDetailModule = new PromoDetailModule();
        // act
        promoDetailService.buildFreeDealPromoDetailInfo(dealGroupBase, promoDetailModule);
        // assert
        assertEquals("100", promoDetailModule.getFinalPrice());
        verify(dealGroupBase).getPrice();
        verify(price).getSalePrice();
    }

    /**
     * 测试当 FreeDealConfig 不为 null 且 showMarketPrice 为 false 时，不设置 marketPrice
     */
    @Test
    public void testBuildFreeDealPromoDetailInfoShowMarketPriceFalse() throws Throwable {
        // arrange
        PromoDetailModule promoDetailModule = new PromoDetailModule();
        // act
        promoDetailService.buildFreeDealPromoDetailInfo(dealGroupBase, promoDetailModule);
        // assert
        assertEquals("100", promoDetailModule.getFinalPrice());
        assertNull(promoDetailModule.getMarketPrice());
        verify(dealGroupBase).getPrice();
        verify(price).getSalePrice();
        verify(price, never()).getMarketPrice();
    }

    /**
     * 测试当 FreeDealConfig 为 null 时，不设置 marketPrice
     */
    @Test
    public void testBuildFreeDealPromoDetailInfoFreeDealConfigIsNull() throws Throwable {
        // arrange
        PromoDetailModule promoDetailModule = new PromoDetailModule();
        // act
        promoDetailService.buildFreeDealPromoDetailInfo(dealGroupBase, promoDetailModule);
        // assert
        assertEquals("100", promoDetailModule.getFinalPrice());
        assertNull(promoDetailModule.getMarketPrice());
        verify(dealGroupBase).getPrice();
        verify(price).getSalePrice();
        verify(price, never()).getMarketPrice();
    }
}
