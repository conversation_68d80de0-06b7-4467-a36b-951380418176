package com.sankuai.dzshoppingguide.product.detail.application.fetcher.atmosphere;

import com.dianping.gmkt.scene.api.delivery.dto.res.ResourceExposureResponseDTO;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.response.FetcherResponse;
import com.sankuai.dz.product.detail.gateway.spi.dto.ShepherdGatewayParam;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.best.shop.ShopInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.best.shop.ShopInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class ProductAtmosphereFetcherDoFetchTest {

    @Mock
    private CompositeAtomService compositeAtomService;

    private TestableProductAtmosphereFetcher productAtmosphereFetcher;

    private ProductDetailPageRequest pageRequest;

    private ShopInfo shopInfo;

    // Test class in the same package to access protected methods
    class TestableProductAtmosphereFetcher extends ProductAtmosphereFetcher {

        private FetcherResponse<ShopInfo> mockResponse;

        public void setMockResponse(FetcherResponse<ShopInfo> response) {
            this.mockResponse = response;
        }

        @Override
        protected FetcherResponse getDependencyResponse(Class dependencyFetcherClass) {
            if (dependencyFetcherClass == ShopInfoFetcher.class) {
                return mockResponse;
            }
            return super.getDependencyResponse(dependencyFetcherClass);
        }


    }

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        // Initialize productAtmosphereFetcher
        productAtmosphereFetcher = new TestableProductAtmosphereFetcher();
        // Set CompositeAtomService
        ReflectionTestUtils.setField(productAtmosphereFetcher, "compositeAtomService", compositeAtomService);
        // Initialize pageRequest
        pageRequest = new ProductDetailPageRequest();
        pageRequest.setProductId(123L);
        pageRequest.setPoiId(456L);
        pageRequest.setCityId(1);
        pageRequest.setClientType(100);
        ShepherdGatewayParam shepherdGatewayParam = new ShepherdGatewayParam();
        shepherdGatewayParam.setAppVersion("1.0.0");
        pageRequest.setShepherdGatewayParam(shepherdGatewayParam);
        // Set request
        ReflectionTestUtils.setField(productAtmosphereFetcher, "request", pageRequest);
        // Initialize shopInfo and response
        shopInfo = new ShopInfo();
        FetcherResponse<ShopInfo> response = new FetcherResponse<>();
        response.setSuccess(true);
        response.setReturnValue(shopInfo);
        productAtmosphereFetcher.setMockResponse(response);
    }

    /**
     * Test case for fetch with empty response
     */
    @Test
    public void testDoFetch_EmptyResponse() throws Throwable {
        // arrange
        when(compositeAtomService.queryExposureResources(any())).thenReturn(CompletableFuture.completedFuture(Collections.emptyList()));
        // act
        CompletableFuture<ProductAtmosphereReturnValue> future = productAtmosphereFetcher.doFetch();
        ProductAtmosphereReturnValue result = future.get();
        // assert
        assertNotNull(result);
        assertNull(result.getResourceExposureResponse());
        verify(compositeAtomService).queryExposureResources(any());
    }

    /**
     * Test case for fetch with service exception
     */
    @Test
    public void testDoFetch_ServiceException() throws Throwable {
        // arrange
        CompletableFuture<List<ResourceExposureResponseDTO>> failedFuture = new CompletableFuture<>();
        failedFuture.completeExceptionally(new RuntimeException("Service error"));
        when(compositeAtomService.queryExposureResources(any())).thenReturn(failedFuture);
        // act & assert
        CompletableFuture<ProductAtmosphereReturnValue> future = productAtmosphereFetcher.doFetch();
        Exception exception = assertThrows(Exception.class, () -> future.get());
        assertTrue(exception.getCause().getMessage().contains("Service error"));
    }
}
