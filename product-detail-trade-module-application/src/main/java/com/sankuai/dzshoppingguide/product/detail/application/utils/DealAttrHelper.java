package com.sankuai.dzshoppingguide.product.detail.application.utils;

import com.sankuai.dzshoppingguide.product.detail.application.constants.DealAttrKeys;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.List;

@Slf4j
public class DealAttrHelper {

    public static boolean isJavaListString(String listString) {
        // 使用正则表达式判断是否符合 Java List 的 toString() 格式
        return listString.matches("^\\[.*\\]$");
    }


    public static String getTimes(List<AttrDTO> attrs){
        if (CollectionUtils.isEmpty(attrs)) {
            return null;
        }
        return getFirstValueV2(attrs, DealAttrKeys.SYS_MULTI_SALE_NUMBER);
    }

    public static boolean onlyVerificationOne(List<AttrDTO> attrs){
        if (CollectionUtils.isEmpty(attrs)) {
            return false;
        }
        String firstValue = getFirstValueV2(attrs, DealAttrKeys.SINGLE_VERIFICATION_QUANTITY_DESC);
        if (StringUtils.isBlank(firstValue)){
            return false;
        }
        return "单次到店仅可核销一次，仅能一人使用".equals(firstValue);
    }

    public static String getFirstValueV2(List<AttrDTO> attributeDtoList, String key) {
        List<String> values = getAttributeValuesV2(attributeDtoList, key);
        return CollectionUtils.isEmpty(values) ? StringUtils.EMPTY : values.get(0);
    }

    public static List<String> getAttributeValuesV2(List<AttrDTO> attributeDtoList, String key) {
        if (attributeDtoList != null) {
            for (AttrDTO attr : attributeDtoList) {
                if (StringUtils.equals(attr.getName(), key)) {
                    return attr.getValue();
                }
            }
        }
        return Collections.emptyList();
    }

    public static boolean validAttr(List<AttrDTO> attrs, String attrName, String attrValue){
        if (CollectionUtils.isEmpty(attrs) || StringUtils.isEmpty(attrValue)) {
            return false;
        }
        String firstValue = getFirstValueV2(attrs, attrName);
        if (StringUtils.isBlank(firstValue)){
            return false;
        }
        return attrValue.equals(firstValue);
    }

    public static boolean hasAttr(List<AttrDTO> attributeDtoList, String key) {
        List<String> values = getAttributeValuesV2(attributeDtoList, key);
        return CollectionUtils.isNotEmpty(values);
    }

}
