package com.sankuai.dzshoppingguide.product.detail.spi.dtos;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-02-26
 * https://mobile.sankuai.com/studio/model/info/41549
 */
@Data
@MobileDo(id = 0x2ecb)
@TypeDoc(description = "价格信息模型")
public class DetailPriceVO implements Serializable {

    @FieldDoc(description = "神券提示条")
    @MobileField(key = 0xe338)
    private InflateCouponTips inflateCouponTips;

    @FieldDoc(description = "最优价格算价公式")
    @MobileField(key = 0xa62)
    private List<ProductBestPromoFormula> bestPromoFormula;

    @FieldDoc(description = "价格前缀，如'券后价'")
    @MobileField(key = 0x238c)
    private String pricePrefix;

    @FieldDoc(description = "价格营销点标签，如'神券价'")
    @MobileField(key = 0x208)
    private String priceMarketingPointTag;

    @FieldDoc(description = "商品标签，如'价保15天'")
    @MobileField(key = 0xc857)
    private String productTag;

    @FieldDoc(description = "商品标签点击跳转链接")
    @MobileField(key = 0xf72e)
    private String productTagClickUrl;

    @FieldDoc(description = "单次价，如'230/次'")
    @MobileField(key = 0x3e12)
    private String singlePrice;

    @FieldDoc(description = "折扣标签，如'9.5折'")
    @MobileField(key = 0xde9f)
    private String discountTag;

    @FieldDoc(description = "折扣标签背景颜色")
    @MobileField(key = 0xc292)
    private String discountTagBackgroundColor;

    @FieldDoc(description = "折扣标签圆角角度")
    @MobileField(key = 0xb467)
    private int discountTagRadius;

    @FieldDoc(description = "折扣标签是否有边线")
    @MobileField(key = 0x8d3e)
    private boolean discountTagHasBorder;

    @FieldDoc(description = "折扣标签字体颜色")
    @MobileField(key = 0xa38c)
    private String discountTagTextColor;

    @FieldDoc(description = "折扣标签边框颜色")
    @MobileField(key = 0x2bcc)
    private String discountTagBorderColor;

    @FieldDoc(description = "市场价")
    @MobileField(key = 0x6242)
    private String marketPrice;

    @FieldDoc(description = "到手价")
    @MobileField(key = 0xe949)
    private String finalPrice;

    @FieldDoc(description = "价格符号，如'¥'")
    @MobileField(key = 0x647d)
    private String priceSymbol;

    @FieldDoc(description = "团详神券感知强化版本")
    @MobileField(key = 0x2b75)
    private String enhanceStyleVersion;
}
