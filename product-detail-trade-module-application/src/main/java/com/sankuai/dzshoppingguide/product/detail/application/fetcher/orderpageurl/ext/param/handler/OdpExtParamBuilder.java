package com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.ext.param.handler;

import com.google.common.collect.Lists;
import com.google.gson.reflect.TypeToken;
import com.sankuai.dz.product.detail.RequestCustomParamEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.enums.OrderPageExtParamEnums;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.ext.param.BaseSingleExtParamBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.ext.param.request.ExtParamBuilderRequest;
import com.sankuai.dzshoppingguide.product.detail.application.utils.GsonUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: guangyujie
 * @Date: 2025/3/12 15:58
 */
@Component
public class OdpExtParamBuilder extends BaseSingleExtParamBuilder {

    public static final List<String> REMAIN_EXT_PARAM_KEYS = Lists.newArrayList(
            "odpflowinfo", "odpLaunchId", "odpFloorId", "odpChannelType"
    );

    @Override
    public OrderPageExtParamEnums buildKey() {
        return OrderPageExtParamEnums.dealextparam;
    }

    /**
     * 填充扩展参数 dealextparam 目前只支持json转map
     */
    @Override
    public String doBuildExtParam(final ProductDetailPageRequest request,
                                  final ExtParamBuilderRequest builderRequest) {
        String extParam = request.getCustomParam(RequestCustomParamEnum.extparam);
        Map<String, String> extParamMap = GsonUtils.fromJsonString(extParam, new TypeToken<Map<String, String>>() {
        }.getType());
        if (MapUtils.isEmpty(extParamMap)) {
            return null;
        }
        Map<String, String> remainParamMap = extParamMap.entrySet().stream()
                .filter(entry -> REMAIN_EXT_PARAM_KEYS.contains(entry.getKey()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        String dealExtParam = GsonUtils.toJsonString(remainParamMap);
        if (StringUtils.isBlank(dealExtParam)) {
            return null;
        }
        return dealExtParam;
    }

}
