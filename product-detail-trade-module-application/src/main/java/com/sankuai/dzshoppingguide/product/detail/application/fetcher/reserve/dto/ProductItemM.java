package com.sankuai.dzshoppingguide.product.detail.application.fetcher.reserve.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
public class ProductItemM {

    /**
     * spuId
     */
    private int productId;

    /**
     * 预订时段模型
     */
    private PeriodM periodM;

    /**
     * 时间点价格,不包含优惠
     */
    private List<DayDetailPriceM> dayDetailPrices;

    /**
     * sku基准价，不包含优惠（预订价）
     */
    private BigDecimal originalSalePrice;

    /**
     * sku优惠信息
     */
    private List<PromoPriceM> skuPromos = new ArrayList<>();

    /**
     * sku未来优惠信息,足疗场景下是会员优惠
     */
    private List<PromoPriceM> futureSkuPromos = new ArrayList<>();

    /**
     * 时段价格模型，将商家营业时间分成多个时段，包含时间和价格信息
     */
    private List<PeriodPriceM> periodPriceM;

    /**
     * 价格信息：原价（市场价）
     */
    private String marketPrice;

    /**
     * 预订时段库存
     */
    private List<PeriodStockM> bookPeriodStocks;

}
