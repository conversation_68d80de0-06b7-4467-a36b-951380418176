package com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.scene;

import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.ProductBottomBarVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.block.cepintuan.CEPinTuanInfoBlockVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.block.cepintuan.CEPinTuanTradeBlockVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.component.trade.button.BaseTradeButtonVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.enums.BottomBarStyleEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Author: guangyujie
 * @Date: 2025/3/16 14:25
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CEPinTuanBottomBarVO extends ProductBottomBarVO {

    /**
     * 左侧拼团成团信息
     */
    private CEPinTuanInfoBlockVO leftBottomBar;

    /**
     * 右侧拼团交易按钮 or 分享按钮（根据主客态变化）
     */
    private CEPinTuanTradeBlockVO rightBottomBar;

    @Override
    public List<BaseTradeButtonVO> queryAllTradeButtons() {
        return Optional.ofNullable(rightBottomBar)
                .map(CEPinTuanTradeBlockVO::getButtonList)
                .map(buttonList -> buttonList.stream()
                        .map(button -> (BaseTradeButtonVO) button)
                        .collect(Collectors.toList()))
                .orElse(new ArrayList<>());
    }

    @Override
    public int getBottomBarStyle() {
        return BottomBarStyleEnum.COST_EFFECTIVE_PIN_TUAN_STYLE.getCode();
    }

}
