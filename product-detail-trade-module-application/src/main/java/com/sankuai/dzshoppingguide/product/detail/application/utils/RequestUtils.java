package com.sankuai.dzshoppingguide.product.detail.application.utils;

import com.google.common.collect.Lists;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;

import java.util.List;

/**
 * <AUTHOR>
 * @create 2025/3/6 20:31
 */
public class RequestUtils {

    private static final List<ClientTypeEnum> mtMiniAppList = Lists.newArrayList(
            ClientTypeEnum.MT_XCX,
            ClientTypeEnum.MT_ZJ_XCX,
            ClientTypeEnum.MT_WWJKZ_XCX,
            ClientTypeEnum.MT_KUAI_SHOU_XCX,
            ClientTypeEnum.MT_MAO_YAN_XCX
    );

    private static final List<ClientTypeEnum> dpMiniAppList = Lists.newArrayList(
            ClientTypeEnum.DP_XCX,
            ClientTypeEnum.DP_BAIDU_MAP_XCX
    );

    public static boolean isMtMiniApp(ProductDetailPageRequest request){
        ClientTypeEnum clientTypeEnum = request.getClientTypeEnum();
        return mtMiniAppList.contains(clientTypeEnum);
    }

    public static boolean isDpMiniApp(ProductDetailPageRequest request){
        ClientTypeEnum clientTypeEnum = request.getClientTypeEnum();
        return dpMiniAppList.contains(clientTypeEnum);
    }
}
