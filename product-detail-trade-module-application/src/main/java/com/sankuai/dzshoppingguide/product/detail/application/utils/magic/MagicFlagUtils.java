package com.sankuai.dzshoppingguide.product.detail.application.utils.magic;

import com.sankuai.dz.product.detail.RequestCustomParamEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import static com.sankuai.dz.product.detail.RequestCustomParamEnum.*;

/**
 * 神会员生效标识工具类
 * https://km.sankuai.com/collabpage/2489971380
 */
public class MagicFlagUtils {

    private static final int VACANCY = 0;
    private static final int EFFECTIVE = 1;
    private static final int INEFFECTIVE = 2;

    /**
     * 神券可用
     *
     * @return
     */
    public static boolean canUse(Integer magicFlag) {
        return magicFlag != null && magicFlag == EFFECTIVE;
    }

    /**
     * 神券可膨
     *
     * @return
     */
    public static boolean canInflate(Integer magicFlag) {
        return magicFlag != null && magicFlag == EFFECTIVE;
    }

    /**
     * 券包可买
     *
     * @return
     */
    public static boolean canBuy(Integer magicFlag) {
        return magicFlag != null && magicFlag == EFFECTIVE;
    }

    /**
     * 是否有效值
     *
     * @return
     */
    public static boolean isValid(Integer magicFlag) {
        return magicFlag != null && (magicFlag == EFFECTIVE || magicFlag == INEFFECTIVE);
    }

    public static String toString(Integer magicFlag) {
        if (magicFlag == null || magicFlag == 0) {
            return "";
        }
        return String.valueOf(magicFlag);
    }

    public static String getMmcInflate(ProductDetailPageRequest pageRequest) {
        return getMagicFlag(pageRequest, mmcinflate);
    }

    public static String getMmcUse(ProductDetailPageRequest pageRequest) {
        return getMagicFlag(pageRequest, mmcuse);
    }

    public static String getMmcBuy(ProductDetailPageRequest pageRequest) {
        return getMagicFlag(pageRequest, mmcbuy);
    }

    public static String getMmcFree(ProductDetailPageRequest pageRequest) {
        return getMagicFlag(pageRequest, mmcfree);
    }

    public static String getMmcPakVersion(ProductDetailPageRequest pageRequest) {
        return pageRequest.getCustomParam(mmcpkgversion);
    }

    private static String getMagicFlag(ProductDetailPageRequest pageRequest, RequestCustomParamEnum key) {
        String mmcInflate = pageRequest.getCustomParam(key);
        if (StringUtils.isBlank(mmcInflate) || !NumberUtils.isCreatable(mmcInflate)) {
            return "0";
        }
        return mmcInflate;
    }
}
