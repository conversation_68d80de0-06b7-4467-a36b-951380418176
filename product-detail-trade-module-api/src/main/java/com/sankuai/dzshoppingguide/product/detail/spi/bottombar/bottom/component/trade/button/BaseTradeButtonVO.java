package com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.component.trade.button;

import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.BottomBarActionDataVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.ProductBottomBarComponentVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author: guangyujie
 * @Date: 2025/4/1 14:38
 */
@EqualsAndHashCode(callSuper = true)
@Data
public abstract class BaseTradeButtonVO extends ProductBottomBarComponentVO {

    /**
     * 是否禁用
     */
    private boolean disable;

    /**
     * ***按钮动作***
     * 非常重要!!!
     */
    private BottomBarActionDataVO actionData;

}
