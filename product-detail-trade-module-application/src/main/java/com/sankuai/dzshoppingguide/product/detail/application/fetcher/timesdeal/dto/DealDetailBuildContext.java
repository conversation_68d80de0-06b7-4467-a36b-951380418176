package com.sankuai.dzshoppingguide.product.detail.application.fetcher.timesdeal.dto;

import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.abtest.AbTestReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.creditpay.ExposeResult;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.defaultselect.SkuDefaultSelect;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.reserve.CardHoldStatusResult;
import lombok.Builder;
import lombok.Data;

/**
 * Context class to hold all parameters needed for building deal details
 */
@Data
@Builder
public class DealDetailBuildContext {
    private ProductAttr productAttr;
    private ProductCategory productCategory;
    private ProductDetailPageRequest request;
    private SkuDefaultSelect skuDefaultSelect;
    private ShopIdMapper shopIdMapper;
    private AbTestReturnValue abTestReturnValue;
    private ExposeResult exposeResult;
    private CardHoldStatusResult cardHoldStatusResult;
    private CardM cardM;
}