package com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommendgoods.dtos;

import lombok.Data;

/**
 * 漂浮标签
 * <p>
 * Created by float.lu on 2020/8/21.
 */
@Data
public class AttrM {

    /**
     * 属性名
     */
    private String name;
    /**
     * 属性值
     */
    private String value;

    /**
     * 属性值-object
     */
    private Object valueObj;

    public AttrM(String name, String value) {
        this.name = name;
        this.value = value;
    }

    public AttrM(String name, String value, Object valueObj) {
        this.name = name;
        this.value = value;
        this.valueObj = valueObj;
    }

    public AttrM() {

    }
}
