package com.sankuai.dzshoppingguide.product.detail.application.utils;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.*;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Random;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * Test class for ShareUtils
 */
@ExtendWith(MockitoExtension.class)
public class ShareUtilsTest {

    private String repeatString(String str, int count) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < count; i++) {
            sb.append(str);
        }
        return sb.toString();
    }

    /**
     * Test shareIdGenerator with a valid UUID
     * Verifies that:
     * 1. The result contains the timestamp (first 14 characters)
     * 2. The result contains the input UUID
     * 3. The result ends with 3 digits
     * 4. The total length is correct
     */
    @Test
    public void testShareIdGeneratorWithValidUuid() throws Throwable {
        // arrange
        String uuid = "abc123";
        // act
        String result = ShareUtils.shareIdGenerator(uuid);
        // assert
        // Verify the length is correct (14 chars for timestamp + uuid length + 3 chars for random digits)
        assertEquals(14 + uuid.length() + 3, result.length());
        // Verify the middle part contains the uuid
        assertTrue(result.substring(14, 14 + uuid.length()).equals(uuid));
        // Verify the timestamp part is 14 digits
        assertTrue(result.substring(0, 14).matches("\\d{14}"));
        // Verify the last 3 characters are digits
        assertTrue(result.substring(result.length() - 3).matches("\\d{3}"));
    }
}
