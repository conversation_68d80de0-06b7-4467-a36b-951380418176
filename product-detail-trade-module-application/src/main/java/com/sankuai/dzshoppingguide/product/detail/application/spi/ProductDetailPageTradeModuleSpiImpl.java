package com.sankuai.dzshoppingguide.product.detail.application.spi;

import com.alibaba.fastjson.JSON;
import com.dianping.lion.client.Lion;
import com.dianping.pigeon.remoting.common.codec.json.SafeJacksonUtils;
import com.meituan.inf.xmdlog.XMDLogFormat;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonServer;
import com.sankuai.dz.api.module.arrange.framework.application.ModuleArrangeFrameworkRunner;
import com.sankuai.dz.api.module.arrange.framework.application.request.ModuleArrangeRequest;
import com.sankuai.dz.api.module.arrange.framework.application.response.FrameworkRunnerResult;
import com.sankuai.dz.product.detail.gateway.spi.dto.ABResultDTO;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dz.product.detail.gateway.spi.response.ModuleResponse;
import com.sankuai.dz.product.detail.gateway.spi.response.ProductDetailPageResponse;
import com.sankuai.dz.product.detail.gateway.spi.service.ProductDetailPageService;
import com.sankuai.dzshoppingguide.product.detail.application.constants.LionConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.ab.vo.AbResultVO;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;

import java.util.HashSet;
import java.util.List;

/**
 * @Author: guangyujie
 * @Date: 2025/1/24 15:47
 */
@MdpPigeonServer(
        serviceInterface = ProductDetailPageService.class,
        url = "com.sankuai.dzshoppingguide.ProductDetailPageTradeModuleSpiService",
        useSharedPool = false,
        poolName = "ProductDetailPageTradeModuleSpiService"
)
@Slf4j
public class ProductDetailPageTradeModuleSpiImpl implements ProductDetailPageService {

    private static final String LOG_PRINT_CONDITION_CONFIG = "com.sankuai.dzshoppingguide.detail.commonmodule.log.print.condition.config";

    @Override
    public ProductDetailPageResponse query(ProductDetailPageRequest request) {
        try {
            request.checkParam();
            if (CollectionUtils.isEmpty(request.getModuleKeys())) {
                throw new IllegalArgumentException("入参ModuleKey为空");
            }
            //默认带上打点模块
            request.getModuleKeys().add(ModuleKeyConstants.TRADE_COMMON_DATA);
            //默认带上实验上报模块
            request.getModuleKeys().add(ModuleKeyConstants.PAGE_AB_RESULT);
            final ModuleArrangeRequest moduleArrangeRequest = new ModuleArrangeRequest(
                    request, new HashSet<>(request.getModuleKeys())
            );
            FrameworkRunnerResult result = ModuleArrangeFrameworkRunner.run(moduleArrangeRequest);

            try {
                // 线下以及RC环境打印日志,方便调试定位问题
                if (Lion.getBoolean(LionConstants.COMMON_SERVICE_APPKEY, LOG_PRINT_CONDITION_CONFIG, false)) {
                    log.info(XMDLogFormat.build().putTag("interfaceName", "ProductDetailPageTradeModuleSpi")
                            .putTag("request", SafeJacksonUtils.serialize(request))
                            .message("ProductDetailPageTradeModuleSpi.query: request"));
                }
            } catch (Exception e) {
                log.error("ProductDetailPageTradeModuleSpi log error", e);
            }
            // 提取AB实验上报信息
            result.getProductDetailPageResponse().setAbResultList(getAbResult(result.getProductDetailPageResponse()));
            return result.getProductDetailPageResponse();
        } catch (Exception e) {
            log.error("ProductDetailPageTradeModuleSpi,request:{}", JSON.toJSONString(request), e);
            return ProductDetailPageResponse.fail(e.getMessage());
        }
    }

    private List<ABResultDTO> getAbResult(ProductDetailPageResponse productDetailPageResponse) {
        if (productDetailPageResponse == null || MapUtils.isEmpty(productDetailPageResponse.getModuleResponse())) {
            return null;
        }
        ModuleResponse moduleResponse = productDetailPageResponse.getModuleResponse().get(ModuleKeyConstants.PAGE_AB_RESULT);
        productDetailPageResponse.getModuleResponse().remove(ModuleKeyConstants.PAGE_AB_RESULT);
        if (moduleResponse == null || moduleResponse.getModuleVO() == null) {
            return null;
        }
        AbResultVO abResultVO = (AbResultVO) moduleResponse.getModuleVO();
        return abResultVO == null ? null : abResultVO.getAbResultList();
    }

}
