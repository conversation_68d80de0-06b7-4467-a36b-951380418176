package com.sankuai.dzshoppingguide.product.detail.spi.structureddetail.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DealDetailStructuredDetailVO;
import com.sankuai.dz.product.detail.gateway.spi.vo.AbstractModuleVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

import static com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants.DEAL_DETAILS;


/**
 * <AUTHOR>
 * @create 2025/2/6 14:40
 * 移动之家：https://mobile.sankuai.com/studio/model/info/41268
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TypeDoc(description = "结构化团详模块")
@MobileDo(id = 0x691e)
public class ModuleDetailStructuredDetailVO extends AbstractModuleVO {

    @Override
    public String getModuleKey() {
        return DEAL_DETAILS;
    }

    @FieldDoc(description = "新版团购详情")
    @MobileDo.MobileField(key = 0xb77b)
    private List<DealDetailStructuredDetailVO> dealDetails;
}
