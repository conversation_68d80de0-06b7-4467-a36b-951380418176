package com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommendgoods.dtos;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import java.util.Arrays;
import java.util.Collections;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import com.sankuai.dztheme.deal.dto.enums.DealActivityTypeEnum;

@ExtendWith(MockitoExtension.class)
class ProductActivityMTest {

    /**
     * 测试当promos为null时，方法应返回null
     */
    @Test
    void testGetPromoPrice_WhenPromosIsNull_ShouldReturnNull() throws Throwable {
        // arrange
        ProductActivityM productActivityM = new ProductActivityM();
        productActivityM.setPromos(null);
        int type = 1;
        // act
        ProductPromoPriceM result = productActivityM.getPromoPrice(type);
        // assert
        assertNull(result, "当promos为null时应返回null");
    }

    /**
     * 测试当promos为空列表时，方法应返回null
     */
    @Test
    void testGetPromoPrice_WhenPromosIsEmpty_ShouldReturnNull() throws Throwable {
        // arrange
        ProductActivityM productActivityM = new ProductActivityM();
        productActivityM.setPromos(Collections.emptyList());
        int type = 1;
        // act
        ProductPromoPriceM result = productActivityM.getPromoPrice(type);
        // assert
        assertNull(result, "当promos为空列表时应返回null");
    }

    /**
     * 测试当promos不为空但找不到匹配type时，方法应返回null
     */
    @Test
    void testGetPromoPrice_WhenNoMatchingType_ShouldReturnNull() throws Throwable {
        // arrange
        ProductPromoPriceM promo1 = new ProductPromoPriceM();
        promo1.setPromoType(2);
        ProductPromoPriceM promo2 = new ProductPromoPriceM();
        promo2.setPromoType(3);
        ProductActivityM productActivityM = new ProductActivityM();
        productActivityM.setPromos(Arrays.asList(promo1, promo2));
        int type = 1;
        // act
        ProductPromoPriceM result = productActivityM.getPromoPrice(type);
        // assert
        assertNull(result, "当没有匹配的promoType时应返回null");
    }

    /**
     * 测试当promos中有匹配type时，方法应返回对应的ProductPromoPriceM对象
     */
    @Test
    void testGetPromoPrice_WhenMatchingTypeExists_ShouldReturnCorrectObject() throws Throwable {
        // arrange
        ProductPromoPriceM expectedPromo = new ProductPromoPriceM();
        expectedPromo.setPromoType(1);
        ProductPromoPriceM otherPromo = new ProductPromoPriceM();
        otherPromo.setPromoType(2);
        ProductActivityM productActivityM = new ProductActivityM();
        productActivityM.setPromos(Arrays.asList(expectedPromo, otherPromo));
        int type = 1;
        // act
        ProductPromoPriceM result = productActivityM.getPromoPrice(type);
        // assert
        assertNotNull(result, "当有匹配的promoType时不应返回null");
        assertEquals(expectedPromo, result, "应返回匹配的ProductPromoPriceM对象");
        assertEquals(1, result.getPromoType(), "返回对象的promoType应该是1");
    }

    /**
     * 测试当promos中有多个匹配type时，方法应返回第一个匹配的对象
     */
    @Test
    void testGetPromoPrice_WhenMultipleMatches_ShouldReturnFirstMatch() throws Throwable {
        // arrange
        ProductPromoPriceM firstMatch = new ProductPromoPriceM();
        firstMatch.setPromoType(1);
        firstMatch.setPromoTag("First");
        ProductPromoPriceM secondMatch = new ProductPromoPriceM();
        secondMatch.setPromoType(1);
        secondMatch.setPromoTag("Second");
        ProductActivityM productActivityM = new ProductActivityM();
        productActivityM.setPromos(Arrays.asList(firstMatch, secondMatch));
        int type = 1;
        // act
        ProductPromoPriceM result = productActivityM.getPromoPrice(type);
        // assert
        assertNotNull(result, "当有匹配的promoType时不应返回null");
        assertSame(firstMatch, result, "应返回第一个匹配的ProductPromoPriceM对象");
        assertEquals("First", result.getPromoTag(), "应返回第一个匹配对象的promoTag");
        assertEquals(1, result.getPromoType(), "返回对象的promoType应该是1");
    }

    @Test
    public void testIfMatchActivityTypeWhenBothNull() throws Throwable {
        // arrange
        ProductActivityM productActivity = spy(new ProductActivityM());
        when(productActivity.getShelfActivityType()).thenReturn(null);
        // act
        boolean result = productActivity.ifMatchActivityType(null);
        // assert
        assertFalse(result);
        verify(productActivity, times(1)).getShelfActivityType();
    }

    @Test
    public void testIfMatchActivityTypeWhenShelfActivityTypeNull() throws Throwable {
        // arrange
        ProductActivityM productActivity = spy(new ProductActivityM());
        when(productActivity.getShelfActivityType()).thenReturn(null);
        // act
        boolean result = productActivity.ifMatchActivityType(DealActivityTypeEnum.SEC_KILL);
        // assert
        assertFalse(result);
        verify(productActivity, times(1)).getShelfActivityType();
    }

    @Test
    public void testIfMatchActivityTypeWhenActivityTypeEnumNull() throws Throwable {
        // arrange
        ProductActivityM productActivity = spy(new ProductActivityM());
        when(productActivity.getShelfActivityType()).thenReturn(2);
        // act
        boolean result = productActivity.ifMatchActivityType(null);
        // assert
        assertFalse(result);
        verify(productActivity, times(1)).getShelfActivityType();
    }

    @Test
    public void testIfMatchActivityTypeWhenTypesMatch() throws Throwable {
        // arrange
        ProductActivityM productActivity = spy(new ProductActivityM());
        // SEC_KILL type
        when(productActivity.getShelfActivityType()).thenReturn(2);
        // act
        boolean result = productActivity.ifMatchActivityType(DealActivityTypeEnum.SEC_KILL);
        // assert
        assertTrue(result);
        verify(productActivity, times(2)).getShelfActivityType();
    }

    @Test
    public void testIfMatchActivityTypeWhenTypesNotMatch() throws Throwable {
        // arrange
        ProductActivityM productActivity = spy(new ProductActivityM());
        // WAN_MEI_JI type
        when(productActivity.getShelfActivityType()).thenReturn(1);
        // act
        boolean result = productActivity.ifMatchActivityType(DealActivityTypeEnum.SEC_KILL);
        // assert
        assertFalse(result);
        verify(productActivity, times(2)).getShelfActivityType();
    }

    @Test
    public void testIfMatchActivityTypeWithErrorType() throws Throwable {
        // arrange
        ProductActivityM productActivity = spy(new ProductActivityM());
        when(productActivity.getShelfActivityType()).thenReturn(-1);
        // act
        boolean result = productActivity.ifMatchActivityType(DealActivityTypeEnum.ERROR);
        // assert
        assertTrue(result);
        verify(productActivity, times(2)).getShelfActivityType();
    }

    @Test
    public void testIfMatchActivityTypeWithLargeTypeValue() throws Throwable {
        // arrange
        ProductActivityM productActivity = spy(new ProductActivityM());
        when(productActivity.getShelfActivityType()).thenReturn(1001);
        // act
        boolean result = productActivity.ifMatchActivityType(DealActivityTypeEnum.JU_HUA_SUAN);
        // assert
        assertTrue(result);
        verify(productActivity, times(2)).getShelfActivityType();
    }
}
