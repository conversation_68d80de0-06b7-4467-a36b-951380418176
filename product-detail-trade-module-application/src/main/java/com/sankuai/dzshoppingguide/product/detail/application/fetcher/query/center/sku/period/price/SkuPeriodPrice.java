package com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.period.price;

import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.period.price.dto.PeriodPriceMergeDTO;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import lombok.EqualsAndHashCode;
import lombok.Setter;

import java.util.Map;
import java.util.Optional;

/**
 * @Author: guangyujie
 * @Date: 2025/2/5 17:13
 */
@EqualsAndHashCode(callSuper = true)
@Setter
public class SkuPeriodPrice extends FetcherReturnValueDTO {

    /**
     * Map<SkuId,PeriodPriceDTO>
     */
    private final Map<Long, PeriodPriceMergeDTO> periodPriceMap;

    public SkuPeriodPrice(Map<Long, PeriodPriceMergeDTO> periodPriceMap) {
        this.periodPriceMap = periodPriceMap;
    }

    public Optional<PeriodPriceMergeDTO> getPeriodPriceDTO(long skuId) {
        return Optional.ofNullable(periodPriceMap).map(map -> map.get(skuId));
    }

}
