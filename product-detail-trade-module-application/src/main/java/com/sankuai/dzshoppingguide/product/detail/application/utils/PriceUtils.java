package com.sankuai.dzshoppingguide.product.detail.application.utils;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommendgoods.dtos.ProductM;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommendgoods.dtos.ProductPromoPriceM;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.timesdeal.dto.CardM;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.timesdeal.dto.MerchantMemberProductPromoData;
import com.sankuai.dztheme.deal.res.enums.PromoTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/04/09
 */
@Slf4j
public class PriceUtils {

    /**
     * 兼容下比价，取最优promoPrice，与到手价取值逻辑保持一致
     * @param productM
     * @param cardM
     * @return
     */
    public static ProductPromoPriceM getUserHasPromoPrice(ProductM productM, CardM cardM){
        ProductPromoPriceM directPromo = null;
        try {
            if(Objects.isNull(productM) || CollectionUtils.isEmpty(productM.getPromoPrices())){
                return null;
            }
            directPromo = productM.getPromoPrices().stream().filter(o -> o.getPromoType() == PromoTypeEnum.DIRECT_PROMO.getType()).findFirst().orElse(null);
            //含商家会员卡
            MerchantMemberProductPromoData merchantMemberPromo = MerchantMemberPromoUtils.getHasMerchantMemberPromo(productM);
            //有商家会员卡优惠且没有倒挂，返回商家会员卡优惠,商家会员卡跟折扣卡玩乐卡互斥的
            if(Objects.nonNull(merchantMemberPromo) && Objects.nonNull(merchantMemberPromo.getProductPromoPrice()) && !CardPromoUtils.isCardDaoGuaPromo(directPromo, merchantMemberPromo.getProductPromoPrice())){
                return merchantMemberPromo.getProductPromoPrice();
            }
            //含折扣卡、玩乐卡等
            ProductPromoPriceM cardPromo = CardPromoUtils.getFirstUserHoldCardPromo(productM.getPromoPrices(), cardM);
            //有卡优惠且卡优惠没有倒挂，返回持卡优惠
            if (Objects.nonNull(cardPromo) && !CardPromoUtils.isCardDaoGuaPromo(directPromo, cardPromo)) {
                return cardPromo;
            }
            return directPromo;
        } catch (Exception e) {
            log.error(String.format("getUserHasPromoPrice.error,productM:%s", JsonCodec.encode(productM)),e);
            return directPromo;
        }
    }

    public static String buildFinalPrice(BigDecimal price) {
        if (Objects.isNull(price) || price.compareTo(BigDecimal.ZERO) < 0) {
            return "0.01";
        }
        DecimalFormat df = new DecimalFormat("0.##");
        return df.format(price);
    }

    public static String buildMarketPrice(BigDecimal marketPrice) {
        if (Objects.isNull(marketPrice) || marketPrice.compareTo(BigDecimal.ZERO) <= 0) {
            return null;
        }
        return marketPrice.stripTrailingZeros().toPlainString();
    }

    public static long convertPriceToCents(BigDecimal price) {
        if (Objects.isNull(price) || price.compareTo(BigDecimal.ZERO) <= 0) {
            return 0L;
        }
        return price.multiply(BigDecimal.valueOf(100)).longValue();
    }
}
