package com.sankuai.dzshoppingguide.product.detail.application.constants;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/2/6 15:45
 */
public interface LionConstants {

    /**
     * 券聚合开关
     */
    public static final String MAGIC_MEMBER_COUPON_AGGREGATE_USER_ID_RATIO = "com.sankuai.dzu.tpbase.dztgdetailweb.magic.member.coupon.aggregate.userid.ratio";
    public static final String MAGIC_MEMBER_COUPON_AGGREGATE_WHITE_LIST = "com.sankuai.dzu.tpbase.dztgdetailweb.magic.member.coupon.aggregate.white.list";

    /**
     * N选1政府消费券MRN版本控制
     */
    public static final String N_SELECT_ONE_GOVERNMENT_COUPON_MRN_VERSION = "n.select.one.government.coupon.mrn.version";

    /**
     * 美团美播小程序是否开启internal
     */
    public static final String ENABLE_MT_LIVE_WEIXIN_MINIAPP_INTERNAL = "enable.meituan.live.weixin.miniapp.internal";

    /**
     * 拼团参数配置
     */
    public static final String COST_EFFECTIVE_PIN_TUAN_CONFIG = "com.sankuai.dzu.tpbase.dztgdetailweb.cost.effective.pin.tuan.config";

    /**
     * 留资型行业团单类目配置
     */
    public static final String LEADS_DEAL_CATE_CONFIG = "com.sankuai.dzu.tpbase.dztgdetailweb.leads.deal.cate.config";

    /**
     * 团单详情页赠品展示playId
     */
    public static final String DEAL_GIFT_PLAY_ID_CONFIG = "com.sankuai.dzu.tpbase.dztgdetailweb.deal.gift.play.id.config";

    /**
     * 过滤无效sku开关
     */
    public static final String DEALS_FILTER_SWITCH = "com.sankuai.dzu.tpbase.dztgdetailweb.deal.status.filter.switch";

    public static final String PRICE_ZHILI_CATEGORIES = "com.sankuai.dzu.tpbase.dztgdetailweb.price.zhili.categories";

    public static final String ZU_LIAO_SHOP_CATEGORY_IDS = "com.sankuai.dzu.tpbase.dztgdetailweb.zuliao.shop.category.ids";
    public static final String ZU_LIAO_CATEGORY_IDS = "com.sankuai.dzu.tpbase.dztgdetailweb.zuliao.category.ids";
    public static final String SNAPSHOT_BACK_CATEGORY = "snapshot.back.category.ids";
    /**
     * 使用提单页浮层的分类
     */
    public static final String SHOW_CREATE_ORDER_LAYER_DEAL_CATEGORY_SERVICE_TYPE_WHITE  = "showcreateorderlayer.deal.category.servicetype.whitelist";
    /**
     * 新客自动化测试开关
     */
    public static final String NEW_CUSTOMER_AUTOMATED_TESTING = "cleaning.product.information.new.customer.automated.testing";

    String RESERVE_APPKEY = "com.sankuai.dzviewscene.reserve";

    String STOCK_GRANULARITY = "com.sankuai.dzviewscene.reserve.mid.stock.granularity.config";

    /**
     * 管道疏通类型 - 服务商团单 - 属性value列表
     */
    public static final String PIPING_SERVICE_DEAL_GROUP_ATTR_VALUE = "dztrade-mapi-web.piping.service.deal.group.attr.value";
    public static final String DZ_TRADE_MAPI_WEB_APP_KEY = "dztrade-mapi-web";

    public static final String EDU_ONLINE_DEAL_SERVICE_LEAF_IDS = "com.sankuai.dzu.tpbase.dztgdetailweb.edu.online.serviceleafids";

    /**
     * 强预订团单类目
     */
    public static final String PRE_ORDER_CATEGORY_IDS = "pre.order.category.ids";

    String COMMON_SERVICE_APPKEY = "com.sankuai.dzshoppingguide.detail.commonmodule";
    /**
     * 赠品类别配置——用于判断是否展示赠品
     */
    String DEAL_GIFT_CATEGORY_CONFIG = "com.sankuai.dzu.tpbase.dztgdetailweb.deal.gift.category.config";


    /**
     * 神会员感知增强-引导使用，商品价格区间&神券面额配置
     */
    String MAGICAL_MEMBER_COUPON_GUIDE_FAKE_RECEIVE_CONFIG = "magical.member.coupon.guide.fake.receive.config";

    /**
     * 神会员感知增强-图标配置
     */
    String MAGICAL_MEMBER_COUPON_ICON_CONFIG = "magical.member.coupon.icon.config";
    /**
     * 神会员感知增强V2-引导购买-图标配置
     */
    String MAGICAL_MEMBER_COUPON_GUIDE_PURCHASE_ICON_CONFIG = "magical.member.coupon.enhance.v2.guide.purchase.icon.config";
    /**
     * 神会员感知增强V2-引导膨胀-图标配置
     */
    String MAGICAL_MEMBER_COUPON_GUIDE_INFLATE_ICON_CONFIG = "magical.member.coupon.enhance.v2.guide.inflate.icon.config";

    /**
     * 神会员感知增强-文本展示配置
     */
    String MAGICAL_MEMBER_COUPON_TEXT_DISPLAY_INFO_CONFIG = "magical.member.coupon.text.display.info.config";
    /**
     * 优惠浮层排序与过滤配置
     */
    String PROMOTION_POP_UP_COUPON_FILTER_SORT = "promotion.pop.up.coupon.filter.sort";
    /**
     * 领券栏排序与过滤配置
     */
    String PROMOTION_BAR_FILTER_SORT = "promotion.bar.filter.sort";
    /**
     * 留资类目
     */
    String LEADS_DEAL_CATEGORY_IDS = "leads.deal.category.ids";
    /**
     * 国家补贴团单三级类目
     */
    String COUNTRY_SUBSIDY_SERVICE_TYPE_IDS = "country.subsidy.service.type.ids";
    /**
     * 神券感知强化屏蔽的页面来源
     */
    String MAGIC_COUPON_ENHANCEMENT_HIDE_PAGE_SOURCE = "magic.coupon.enhancement.hide.page.source";
    /**
     * 结婚行业留资型行业团单类目配置
     */
    String WEDDING_LEADS_DEAL_CATE_CONFIG = "com.sankuai.dzu.tpbase.dztgdetailweb.wedding.leads.deal.cate.config";
    /**
     * 神券感知强化黑名单城市
     */
    String MAGIC_COUPON_ENHANCEMENT_CITY_BLACK_LIST = "magic.coupon.enhancement.city.black.list";

    String PRODUCT_PRICE_POWER_TAG_CONFIG = "product.price.power.tag.config";

    /**
     * 支持一品多态商品分类
     */
    String SUPPORT_MULTI_TRADE_CATEGORY= "support.multi.trade.category";

    /**
     * 一品多态营销场域
     */
    String MULTI_TRADE_TYPE_MARKETING_SOURCE = "multi.trade.type.marketing.source";

    /**
     * 不支持一品多态的营销场域
     */
    String NONE_MULTI_TRADE_TYPE_MARKETING_SOURCE = "none.multi.trade.type.marketing.source";

    String COUNTRY_SUBSIDIES_PRODUCT_ID_2_GOV_CODE_MAP = "country.subsidy.product.service.id.to.gov.code";

    String COUNTRY_SUBSIDIES_PRODUCT_2_SKU_ATTR_MAP = "country.subsidy.service.id.to.sku.attr.config";

    String COUNTRY_SUBSIDIES_URL = "country.subsidy.url";
    String MEMBER_FREE_CONFIG = "member.free.config";

    /**
     * 休娱特团渠道同店升单&跨店搭售
     * 搭售玩法id
     */
    public static final String PLAY_ID = "com.sankuai.dzu.tpbase.dztgdetailweb.playId";
    /**
     * 国家补贴配置
     */
    String COUNTRY_SUBSIDIES_CONFIG = "country.subsidies.config";
    /**
     * 国家补贴团单三级类目
     */
    String COUNTRY_SUBSIDIES_SERVICE_TYPE_IDS = "country.subsidy.service.type.ids";
}