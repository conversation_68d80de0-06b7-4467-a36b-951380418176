package com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.block.cepintuan;

import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.ProductBottomBarBlockVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.component.cepintuan.CEPinTuanInfoVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.enums.BottomBarBlockTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * @Author: guang<PERSON><PERSON>e
 * @Date: 2025/3/16 14:34
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CEPinTuanInfoBlockVO extends ProductBottomBarBlockVO {

    /**
     * 拼团成团信息（主客态）
     */
    private CEPinTuanInfoVO cePinTuanInfo;

    @Override
    public int getBlockType() {
        return BottomBarBlockTypeEnum.COST_EFFECTIVE_INFO.getCode();
    }

}
