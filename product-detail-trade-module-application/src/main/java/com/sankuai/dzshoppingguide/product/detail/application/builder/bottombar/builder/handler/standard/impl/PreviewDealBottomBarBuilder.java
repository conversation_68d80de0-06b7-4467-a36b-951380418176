package com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.builder.handler.standard.impl;

import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.BuilderTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.builder.handler.standard.StandardBottomBarBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.component.trade.block.buy.button.DefaultTradeButtonComponent;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.component.trade.block.buy.button.StandardTradeButtonComponent;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sale.info.enums.ProductSaleStatusEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.FetcherDAGStarter;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.extend.none.DoNothingActionVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.ProductBottomBarVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.block.quick.entrance.QuickEntranceBlockVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.block.trade.StandardTradeBlockVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.component.trade.button.StandardTradeButtonVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.scene.StandardTradeBottomBarVO;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import org.jetbrains.annotations.NotNull;

import java.util.ArrayList;

/**
 * @Author: guangyujie
 * @Date: 2025/3/12 04:05
 */
@Builder(
        builderType = BuilderTypeEnum.VARIABLE_BUILDER,
        moduleKey = ModuleKeyConstants.BOTTOM_BAR,
        startFetcher = FetcherDAGStarter.class,
        dependentFetchers = {
        }
)
public class PreviewDealBottomBarBuilder extends StandardBottomBarBuilder {


    @Override
    protected void prepareData() {
    }

    @Override
    protected ProductBottomBarVO buildProductBottomBarVO() {
        StandardTradeBottomBarVO standardTradeBottomBarVO = new StandardTradeBottomBarVO();
        standardTradeBottomBarVO.setRightBottomBar(buildStandardTradeBlock());
        standardTradeBottomBarVO.setLeftBottomBar(new QuickEntranceBlockVO(new ArrayList<>()));
        return standardTradeBottomBarVO;
    }

    /**
     * 交易按钮区域
     */
    private @NotNull StandardTradeBlockVO buildStandardTradeBlock() {
        //构建右侧交易按钮
        //逻辑：只出交易按钮，且置灰
        StandardTradeButtonVO rightButton = StandardTradeButtonComponent.build(
                request, productCategory, normalPriceReturnValue, orderPageUrl, null,
                ProductSaleStatusEnum.OFFLINE
        );
        if (rightButton == null) {  //最终兜底
            rightButton = DefaultTradeButtonComponent.build(request, orderPageUrl);
        }
        //预览页购买按钮不能点击
        if (rightButton != null) {
            rightButton.setDisable(true);
            rightButton.setActionData(new DoNothingActionVO());
        }
        return StandardTradeBlockVO.buildSingleButtonStyle(rightButton);
    }

}
