package com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.component.trade.block.buy.button.cepintuan;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.component.trade.block.utils.PriceFormatUtils;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.exception.TradeButtonBuildFatalException;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.purchase.coupon.PurchaseCouponReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sale.info.enums.ProductSaleStatusEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.enums.OpenTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.extend.buy.BuyActionVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.component.trade.button.StandardTradeButtonVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.backgroud.BottomBarBackgroundVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.constant.ButtonStyle;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.rich.content.RichContentVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.rich.content.TextRichContentVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.rich.content.enums.TextStyleEnum;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Nullable;
import java.util.List;
import java.util.Optional;

import static com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.constant.ButtonStyleConstant.CE_PIN_TUAN_STRONG_BUTTON;
import static com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.constant.ButtonStyleConstant.CE_PIN_TUAN_WEAK_BUTTON;

/**
 * @Author: guangyujie
 * @Date: 2025/3/20 17:38
 */
@Slf4j
public class CEPTNormalBuyButtonComponent {

    public static StandardTradeButtonVO build(final ProductDetailPageRequest request,
                                              @Nullable final PriceDisplayDTO normalPrice,
                                              @Nullable final String orderPageUrl,
                                              @Nullable final PurchaseCouponReturnValue purchaseCouponReturnValue,
                                              final boolean enhanceStyle,
                                              final ProductSaleStatusEnum saleStatus) {
        try {
            StandardTradeButtonVO button = new StandardTradeButtonVO();
            button.setDisable(!saleStatus.isForSale());
            button.setActionData(new BuyActionVO(
                    OpenTypeEnum.modal,
                    orderPageUrl,
                    Optional.ofNullable(purchaseCouponReturnValue)
                            .map(PurchaseCouponReturnValue::getCouponGroupIdsForPurchase)
                            .orElse(null)
            ));
            if (enhanceStyle) {//普通购买按钮的强弱样式与拼团强弱样式相反
                button.setMainTitle(buildMainTitle(CE_PIN_TUAN_WEAK_BUTTON, normalPrice));
                button.setBackground(BottomBarBackgroundVO.buildSingleColorWithBorder(
                        CE_PIN_TUAN_WEAK_BUTTON.getBackgroundSingleColor(), CE_PIN_TUAN_WEAK_BUTTON.getTitleColor(), 2
                ));
            } else {
                button.setMainTitle(buildMainTitle(CE_PIN_TUAN_STRONG_BUTTON, normalPrice));
                button.setBackground(BottomBarBackgroundVO.buildVerticalGradientColor(
                        CE_PIN_TUAN_STRONG_BUTTON.getBackGroundColors()
                ));
            }
            return button;
        } catch (Throwable throwable) {
            log.error("CEPTNormalBuyButtonComponent.build,request:{}",
                    JSON.toJSONString(request), new TradeButtonBuildFatalException(throwable));
            return null;
        }
    }

    private static List<RichContentVO> buildMainTitle(final ButtonStyle cePinTuanStrongButton,
                                                      final PriceDisplayDTO normalPrice) {
        return Lists.newArrayList(
                new TextRichContentVO("¥", TextStyleEnum.Bold, 24, cePinTuanStrongButton.getTitleColor()),
                new TextRichContentVO(
                        PriceFormatUtils.getPlainString(normalPrice),
                        TextStyleEnum.Bold, 48, cePinTuanStrongButton.getTitleColor()),
                new TextRichContentVO(
                        " 直接购买",
                        TextStyleEnum.Bold, 32, cePinTuanStrongButton.getTitleColor())
        );
    }

}
