<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.meituan.mdp</groupId>
        <artifactId>mdp-basic-parent</artifactId>
        <version>1.8.7.2</version>
        <relativePath/>
    </parent>

    <groupId>com.sankuai.dzshoppingguide.detail</groupId>
    <artifactId>product-detail-trade-module</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>
    <name>product-detail-trade-module</name>

    <modules>
        <module>product-detail-trade-module-api</module>
        <module>product-detail-trade-module-starter</module>
        <module>product-detail-trade-module-application</module>
        <module>product-detail-trade-module-domain</module>
        <module>product-detail-trade-module-infrastructure</module>
    </modules>

    <properties>
        <revision>1.0.0-SNAPSHOT</revision>
        <product-detail-trade-module-api.version>1.0.0</product-detail-trade-module-api.version>
        <product-detail-gateway-api.version>2.0.1</product-detail-gateway-api.version>
        <product-detail-module-arrange-framework.version>1.0.5</product-detail-module-arrange-framework.version>
        <product-detail-common.version>1.0.8</product-detail-common.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.sankuai.dzshoppingguide.detail</groupId>
                <artifactId>product-detail-trade-module-api</artifactId>
                <version>${product-detail-trade-module-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.dz</groupId>
                <artifactId>product-detail-common</artifactId>
                <version>${product-detail-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.dz</groupId>
                <artifactId>product-detail-gateway-api</artifactId>
                <version>${product-detail-gateway-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.dz</groupId>
                <artifactId>product-detail-module-arrange-framework</artifactId>
                <version>${product-detail-module-arrange-framework.version}</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.dzshoppingguide.detail</groupId>
                <artifactId>product-detail-trade-module-starter</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.dzshoppingguide.detail</groupId>
                <artifactId>product-detail-trade-module-domain</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.dzshoppingguide.detail</groupId>
                <artifactId>product-detail-trade-module-application</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>com.sankuai.dzshoppingguide.detail</groupId>
                <artifactId>product-detail-trade-module-infrastructure</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-transport-native-unix-common</artifactId>
                <version>4.1.117.Final</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>1.18.32</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>20.0</version>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>3.8.0</version>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-inline</artifactId>
                <version>3.8.0</version>
            </dependency>
            <dependency>
                <groupId>org.powermock</groupId>
                <artifactId>powermock-module-junit4</artifactId>
                <version>2.0.7</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>com.dianping.general</groupId>
                <artifactId>martgeneral-recommend-api</artifactId>
                <version>1.7.6</version>
            </dependency>
            <!--海马平台-->
            <dependency>
                <groupId>com.dianping.haima</groupId>
                <artifactId>haima-client</artifactId>
                <version>1.1.16</version>
                <exclusions>
                    <exclusion>
                        <groupId>javax.servlet</groupId>
                        <artifactId>servlet-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>log4j</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>log4j</groupId>
                        <artifactId>log4j</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>org.slf4j</groupId>
                        <artifactId>slf4j-log4j12</artifactId>
                    </exclusion>

                </exclusions>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                        <configuration>
                            <flattenMode>defaults</flattenMode>
                            <updatePomFile>true</updatePomFile>
                        </configuration>
                    </execution>
                    <execution>
                        <id>flatten-clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
</project>
