package com.sankuai.dzshoppingguide.product.detail.application.utils;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.sankuai.dzshoppingguide.product.detail.application.constants.LionConstants;
import com.sankuai.dzshoppingguide.product.detail.application.utils.model.MaterialInfoModel;
import com.sankuai.dzshoppingguide.product.detail.application.utils.model.PrizeInfoModel;
import com.sankuai.dzshoppingguide.product.detail.spi.promo.vo.DealGift;
import java.util.List;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
@DisplayName("PlayCenterWrapper buildGiftsForNewCustomActivity Test")
class PlayCenterWrapperTest {

    private static final String EXPECTED_TITLE = "Test Prize";

    private static final String EXPECTED_THUMBNAIL = "test.jpg";

    private static final long EXPECTED_ACTIVITY_ID = 12345;

    /**
     * 测试 buildMaterialInfoList 方法，当 materialInfoList 为 null 的情况
     */
    @Test
    void testBuildMaterialInfoListWhenMaterialInfoListIsNull() throws Throwable {
        // arrange
        JSONArray materialInfoList = null;
        // act
        List<MaterialInfoModel> result = PlayCenterWrapper.buildMaterialInfoList(materialInfoList);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试 buildMaterialInfoList 方法，当 materialInfoList 为空数组的情况
     */
    @Test
    void testBuildMaterialInfoListWhenMaterialInfoListIsEmpty() throws Throwable {
        // arrange
        JSONArray materialInfoList = new JSONArray();
        // act
        List<MaterialInfoModel> result = PlayCenterWrapper.buildMaterialInfoList(materialInfoList);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试 buildMaterialInfoList 方法，当 materialInfoList 包含单个有效元素的情况
     */
    @Test
    void testBuildMaterialInfoListWithSingleValidElement() throws Throwable {
        // arrange
        JSONArray materialInfoList = new JSONArray();
        JSONObject materialInfoObj = new JSONObject();
        materialInfoObj.put("fieldKey", "testKey");
        materialInfoObj.put("fieldValue", "testValue");
        materialInfoList.add(materialInfoObj);
        // act
        List<MaterialInfoModel> result = PlayCenterWrapper.buildMaterialInfoList(materialInfoList);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("testKey", result.get(0).getFieldKey());
        assertEquals("testValue", result.get(0).getFieldValue());
    }

    /**
     * 测试 buildMaterialInfoList 方法，当 materialInfoList 包含多个有效元素的情况
     */
    @Test
    void testBuildMaterialInfoListWithMultipleValidElements() throws Throwable {
        // arrange
        JSONArray materialInfoList = new JSONArray();
        JSONObject materialInfoObj1 = new JSONObject();
        materialInfoObj1.put("fieldKey", "key1");
        materialInfoObj1.put("fieldValue", "value1");
        JSONObject materialInfoObj2 = new JSONObject();
        materialInfoObj2.put("fieldKey", "key2");
        materialInfoObj2.put("fieldValue", "value2");
        materialInfoList.add(materialInfoObj1);
        materialInfoList.add(materialInfoObj2);
        // act
        List<MaterialInfoModel> result = PlayCenterWrapper.buildMaterialInfoList(materialInfoList);
        // assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("key1", result.get(0).getFieldKey());
        assertEquals("value1", result.get(0).getFieldValue());
        assertEquals("key2", result.get(1).getFieldKey());
        assertEquals("value2", result.get(1).getFieldValue());
    }

    /**
     * 测试 buildMaterialInfoList 方法，当 materialInfoList 中包含缺失字段的元素的情况
     */
    @Test
    void testBuildMaterialInfoListWithMissingFields() throws Throwable {
        // arrange
        JSONArray materialInfoList = new JSONArray();
        JSONObject materialInfoObj = new JSONObject();
        // 只设置 fieldKey，不设置 fieldValue
        materialInfoObj.put("fieldKey", "testKey");
        materialInfoList.add(materialInfoObj);
        // act
        List<MaterialInfoModel> result = PlayCenterWrapper.buildMaterialInfoList(materialInfoList);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("testKey", result.get(0).getFieldKey());
        assertNull(result.get(0).getFieldValue());
    }

    /**
     * 测试 buildMaterialInfoList 方法，当 materialInfoList 中包含空字段值的元素的情况
     */
    @Test
    void testBuildMaterialInfoListWithEmptyFieldValues() throws Throwable {
        // arrange
        JSONArray materialInfoList = new JSONArray();
        JSONObject materialInfoObj = new JSONObject();
        materialInfoObj.put("fieldKey", "");
        materialInfoObj.put("fieldValue", "");
        materialInfoList.add(materialInfoObj);
        // act
        List<MaterialInfoModel> result = PlayCenterWrapper.buildMaterialInfoList(materialInfoList);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("", result.get(0).getFieldKey());
        assertEquals("", result.get(0).getFieldValue());
    }

    /**
     * 测试 buildMaterialInfoList 方法，当 materialInfoList 不为空，其中的元素也不为空的情况
     */
    @Test
    public void testBuildMaterialInfoListWhenMaterialInfoListIsNotEmptyAndElementIsNotNull() throws Throwable {
        // arrange
        JSONArray materialInfoList = new JSONArray();
        JSONObject materialInfoListJSONObject = new JSONObject();
        materialInfoListJSONObject.put("fieldKey", "key");
        materialInfoListJSONObject.put("fieldValue", "value");
        materialInfoList.add(materialInfoListJSONObject);
        // act
        List<MaterialInfoModel> result = PlayCenterWrapper.buildMaterialInfoList(materialInfoList);
        // assert
        assertEquals(1, result.size());
        assertEquals("key", result.get(0).getFieldKey());
        assertEquals("value", result.get(0).getFieldValue());
    }

    /**
     * Test case for null input
     */
    @Test
    public void testBuildFilterPrizeInfoList_NullInput() throws Throwable {
        // arrange
        JSONArray prizeInfoList = null;
        // act
        List<PrizeInfoModel> result = PlayCenterWrapper.buildFilterPrizeInfoList(prizeInfoList);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test case for empty JSONArray input
     */
    @Test
    public void testBuildFilterPrizeInfoList_EmptyInput() throws Throwable {
        // arrange
        JSONArray prizeInfoList = new JSONArray();
        // act
        List<PrizeInfoModel> result = PlayCenterWrapper.buildFilterPrizeInfoList(prizeInfoList);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test case for single valid prize info
     */
    @Test
    public void testBuildFilterPrizeInfoList_SingleValidPrize() throws Throwable {
        // arrange
        JSONArray prizeInfoList = new JSONArray();
        JSONObject prizeInfo = new JSONObject();
        prizeInfo.put("prizeName", "Test Prize");
        prizeInfo.put("image", "test.jpg");
        prizeInfoList.add(prizeInfo);
        // act
        List<PrizeInfoModel> result = PlayCenterWrapper.buildFilterPrizeInfoList(prizeInfoList);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("Test Prize", result.get(0).getPrizeName());
        assertEquals("test.jpg", result.get(0).getPrizeImage());
    }

    /**
     * Test case for multiple valid prize info
     */
    @Test
    public void testBuildFilterPrizeInfoList_MultipleValidPrizes() throws Throwable {
        // arrange
        JSONArray prizeInfoList = new JSONArray();
        JSONObject prizeInfo1 = new JSONObject();
        prizeInfo1.put("prizeName", "Prize 1");
        prizeInfo1.put("image", "image1.jpg");
        prizeInfoList.add(prizeInfo1);
        JSONObject prizeInfo2 = new JSONObject();
        prizeInfo2.put("prizeName", "Prize 2");
        prizeInfo2.put("image", "image2.jpg");
        prizeInfoList.add(prizeInfo2);
        // act
        List<PrizeInfoModel> result = PlayCenterWrapper.buildFilterPrizeInfoList(prizeInfoList);
        // assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("Prize 1", result.get(0).getPrizeName());
        assertEquals("image1.jpg", result.get(0).getPrizeImage());
        assertEquals("Prize 2", result.get(1).getPrizeName());
        assertEquals("image2.jpg", result.get(1).getPrizeImage());
    }

    /**
     * Test case for prize info with missing fields
     */
    @Test
    public void testBuildFilterPrizeInfoList_MissingFields() throws Throwable {
        // arrange
        JSONArray prizeInfoList = new JSONArray();
        JSONObject prizeInfo = new JSONObject();
        prizeInfoList.add(prizeInfo);
        // act
        List<PrizeInfoModel> result = PlayCenterWrapper.buildFilterPrizeInfoList(prizeInfoList);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertNull(result.get(0).getPrizeName());
        assertNull(result.get(0).getPrizeImage());
    }

    /**
     * Test case for test environment with valid task
     * Verifies that a valid task with event type 17 is correctly processed
     */
    @Test
    @DisplayName("Test environment with valid task")
    public void testBuildGiftsForNewCustomActivityTestEnvironment() throws Throwable {
        // arrange
        String playResult = "{\"hasParticipate\":true,\"endTime\":1625097600000,\"activityId\":12345," + "\"taskInfoList\":[{\"status\":1,\"eventType\":17,\"prizeCount\":2," + "\"prizeInfoList\":[{\"prizeName\":\"Test Prize\",\"prizeImage\":\"test.jpg\"}]}]}";
        // act
        List<DealGift> result = PlayCenterWrapper.buildGiftsForNewCustomActivity(playResult);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        DealGift gift = result.get(0);
        assertEquals(EXPECTED_TITLE, gift.getTitle());
        assertEquals("下单后发放", gift.getProductTag());
        assertEquals(2, gift.getCouponNum());
        assertEquals(EXPECTED_ACTIVITY_ID, gift.getActivityId());
        assertEquals(EXPECTED_THUMBNAIL, gift.getThumbnail());
    }

    /**
     * Test case for empty playResult
     * Verifies that empty input returns default gift in test environment
     */
    @Test
    @DisplayName("Empty playResult returns default gift")
    public void testBuildGiftsForNewCustomActivityEmptyPlayResult() throws Throwable {
        // act
        List<DealGift> result = PlayCenterWrapper.buildGiftsForNewCustomActivity("");
        // assert
        assertNull(result);
    }

    /**
     * Test case for hasParticipate false
     * Verifies that non-participating case returns default gift in test environment
     */
    @Test
    @DisplayName("HasParticipate false returns default gift")
    public void testBuildGiftsForNewCustomActivityHasParticipateFalse() throws Throwable {
        // arrange
        String playResult = "{\"hasParticipate\":false,\"taskInfoList\":[]}";
        // act
        List<DealGift> result = PlayCenterWrapper.buildGiftsForNewCustomActivity(playResult);
        // assert
        assertNull(result);
    }

    /**
     * Test case for valid task with event type 17
     * Verifies correct processing of order-after delivery type
     */
    @Test
    @DisplayName("Valid task with event type 17")
    public void testBuildGiftsForNewCustomActivityValidTaskEventType17() throws Throwable {
        // arrange
        String playResult = "{\"hasParticipate\":true,\"endTime\":1625097600000,\"activityId\":12345," + "\"taskInfoList\":[{\"status\":1,\"eventType\":17,\"prizeCount\":2," + "\"prizeInfoList\":[{\"prizeName\":\"Test Prize\",\"prizeImage\":\"test.jpg\"}]}]}";
        // act
        List<DealGift> result = PlayCenterWrapper.buildGiftsForNewCustomActivity(playResult);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        DealGift gift = result.get(0);
        assertEquals(EXPECTED_TITLE, gift.getTitle());
        assertEquals("下单后发放", gift.getProductTag());
        assertEquals(2, gift.getCouponNum());
        assertEquals(EXPECTED_ACTIVITY_ID, gift.getActivityId());
    }

    /**
     * Test case for valid task with event type 18
     * Verifies correct processing of verification-after delivery type
     */
    @Test
    @DisplayName("Valid task with event type 18")
    public void testBuildGiftsForNewCustomActivityValidTaskEventType18() throws Throwable {
        // arrange
        String playResult = "{\"hasParticipate\":true,\"endTime\":1625097600000,\"activityId\":12345," + "\"taskInfoList\":[{\"status\":1,\"eventType\":18,\"prizeCount\":1," + "\"prizeInfoList\":[{\"prizeName\":\"Test Prize\",\"prizeImage\":\"test.jpg\"}]}]}";
        // act
        List<DealGift> result = PlayCenterWrapper.buildGiftsForNewCustomActivity(playResult);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        DealGift gift = result.get(0);
        assertEquals(EXPECTED_TITLE, gift.getTitle());
        assertEquals("核销后发放", gift.getProductTag());
        assertEquals(1, gift.getCouponNum());
    }

    /**
     * Test case for multiple tasks with status 1
     * Verifies that only the first valid task is processed
     */
    @Test
    @DisplayName("Multiple tasks with status 1")
    public void testBuildGiftsForNewCustomActivityMultipleTasks() throws Throwable {
        // arrange
        String playResult = "{\"hasParticipate\":true,\"endTime\":1625097600000,\"activityId\":12345," + "\"taskInfoList\":[" + "{\"status\":1,\"eventType\":17,\"prizeCount\":2,\"prizeInfoList\":[{\"prizeName\":\"Prize 1\",\"prizeImage\":\"test1.jpg\"}]}," + "{\"status\":1,\"eventType\":18,\"prizeCount\":1,\"prizeInfoList\":[{\"prizeName\":\"Prize 2\",\"prizeImage\":\"test2.jpg\"}]}" + "]}";
        // act
        List<DealGift> result = PlayCenterWrapper.buildGiftsForNewCustomActivity(playResult);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        DealGift gift = result.get(0);
        assertEquals("Prize 1", gift.getTitle());
        assertEquals("下单后发放", gift.getProductTag());
        assertEquals(2, gift.getCouponNum());
    }

    /**
     * Test case for null playResult
     * Verifies that null input returns default gift in test environment
     */
    @Test
    @DisplayName("Null playResult returns default gift")
    public void testBuildGiftsForNewCustomActivityNullPlayResult() throws Throwable {
        // act
        List<DealGift> result = PlayCenterWrapper.buildGiftsForNewCustomActivity(null);
        // assert
        assertNull(result);
    }

    /**
     * 测试空输入返回null
     */
    @Test
    @DisplayName("Should return null when playResult is null")
    void testBuildGiftsForNewCustomActivityWithNullInput() throws Throwable {
        // act
        List<DealGift> result = PlayCenterWrapper.buildGiftsForNewCustomActivity(null);
        // assert
        assertNull(result);
    }

    /**
     * 测试空字符串输入返回null
     */
    @Test
    @DisplayName("Should return null when playResult is empty")
    void testBuildGiftsForNewCustomActivityWithEmptyInput() throws Throwable {
        // act
        List<DealGift> result = PlayCenterWrapper.buildGiftsForNewCustomActivity("");
        // assert
        assertNull(result);
    }

    /**
     * 测试未参与活动返回null
     */
    @Test
    @DisplayName("Should return null when hasParticipate is false")
    void testBuildGiftsForNewCustomActivityWhenNotParticipated() throws Throwable {
        // arrange
        String playResult = "{\"hasParticipate\":false,\"taskInfoList\":[]}";
        // act
        List<DealGift> result = PlayCenterWrapper.buildGiftsForNewCustomActivity(playResult);
        // assert
        assertNull(result);
    }

    /**
     * 测试参与活动但无有效任务返回空列表
     */
    @Test
    @DisplayName("Should return empty list when no valid task")
    void testBuildGiftsForNewCustomActivityWithNoValidTask() throws Throwable {
        // arrange
        String playResult = "{\"hasParticipate\":true,\"taskInfoList\":[{\"status\":0,\"eventType\":17,\"prizeCount\":0,\"prizeInfoList\":[{\"prizeName\":\"Test Prize\"}]}]}";
        // act
        List<DealGift> result = PlayCenterWrapper.buildGiftsForNewCustomActivity(playResult);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试下单后发放赠品场景
     */
    @Test
    @DisplayName("Should return gift with order delivery tag")
    void testBuildGiftsForNewCustomActivityWithOrderDelivery() throws Throwable {
        // arrange
        String playResult = "{\"hasParticipate\":true,\"activityId\":123,\"taskInfoList\":[{\"status\":1,\"eventType\":17,\"prizeCount\":2,\"prizeInfoList\":[{\"prizeName\":\"Test Prize\",\"prizeImage\":\"test.jpg\"}]}]}";
        // act
        List<DealGift> result = PlayCenterWrapper.buildGiftsForNewCustomActivity(playResult);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        DealGift gift = result.get(0);
        assertEquals("Test Prize", gift.getTitle());
        assertEquals("下单后发放", gift.getProductTag());
        assertEquals(2, gift.getCouponNum());
        assertEquals(123L, gift.getActivityId());
        assertEquals("test.jpg", gift.getThumbnail());
    }

    /**
     * 测试核销后发放赠品场景
     */
    @Test
    @DisplayName("Should return gift with verification delivery tag")
    void testBuildGiftsForNewCustomActivityWithVerificationDelivery() throws Throwable {
        // arrange
        String playResult = "{\"hasParticipate\":true,\"activityId\":456,\"taskInfoList\":[{\"status\":1,\"eventType\":18,\"prizeCount\":1,\"prizeInfoList\":[{\"prizeName\":\"Verify Prize\",\"prizeImage\":\"verify.jpg\"}]}]}";
        // act
        List<DealGift> result = PlayCenterWrapper.buildGiftsForNewCustomActivity(playResult);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        DealGift gift = result.get(0);
        assertEquals("Verify Prize", gift.getTitle());
        assertEquals("核销后发放", gift.getProductTag());
        assertEquals(1, gift.getCouponNum());
        assertEquals(456L, gift.getActivityId());
        assertEquals("verify.jpg", gift.getThumbnail());
    }

    /**
     * 测试包含奖励规则说明场景
     */
    @Test
    @DisplayName("Should include reward rules when present")
    void testBuildGiftsForNewCustomActivityWithRewardRules() throws Throwable {
        // arrange
        String playResult = "{\"hasParticipate\":true,\"activityId\":789,\"materialInfoList\":[{\"fieldKey\":\"rewardRulesExplanation\",\"fieldValue\":\"Test Rules\"}],\"taskInfoList\":[{\"status\":1,\"eventType\":17,\"prizeCount\":3,\"prizeInfoList\":[{\"prizeName\":\"Rule Prize\",\"prizeImage\":\"rule.jpg\"}]}]}";
        // act
        List<DealGift> result = PlayCenterWrapper.buildGiftsForNewCustomActivity(playResult);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        DealGift gift = result.get(0);
        assertEquals("Test Rules", gift.getUseRule());
    }

    /**
     * 测试包含结束时间场景
     */
    @Test
    @DisplayName("Should format end time correctly")
    void testBuildGiftsForNewCustomActivityWithEndTime() throws Throwable {
        // arrange
        String playResult = "{\"hasParticipate\":true,\"endTime\":1625097600000,\"activityId\":101,\"taskInfoList\":[{\"status\":1,\"eventType\":17,\"prizeCount\":1,\"prizeInfoList\":[{\"prizeName\":\"Time Prize\",\"prizeImage\":\"time.jpg\"}]}]}";
        // act
        List<DealGift> result = PlayCenterWrapper.buildGiftsForNewCustomActivity(playResult);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        DealGift gift = result.get(0);
        assertEquals("活动有效期至2021.07.01", gift.getTimeDesc());
    }

    /**
     * 测试JSON解析异常场景
     */
    @Test
    @DisplayName("Should handle JSON parse exception")
    void testBuildGiftsForNewCustomActivityWithInvalidJson() throws Throwable {
        // arrange
        String invalidJson = "{invalid json}";
        // act & assert
        assertThrows(com.alibaba.fastjson.JSONException.class, () -> {
            PlayCenterWrapper.buildGiftsForNewCustomActivity(invalidJson);
        });
    }

    /**
     * 测试空taskInfoList场景
     */
    @Test
    @DisplayName("Should return empty list when taskInfoList is empty")
    void testBuildGiftsForNewCustomActivityInTestEnvironment() throws Throwable {
        // arrange
        String playResult = "{\"hasParticipate\":true,\"activityId\":999,\"taskInfoList\":[]}";
        // act
        List<DealGift> result = PlayCenterWrapper.buildGiftsForNewCustomActivity(playResult);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }
}
