package com.sankuai.dzshoppingguide.product.detail.spi.similargoods.vo.dtos;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;

/**
 * 每条优惠信息
 * @auther: liweilong06
 * @date: 2021/5/5 下午4:33
 */
@Data
@MobileDo(id = 0x858a)
public class DzPromoPerItemVO implements Serializable {
    /**
     *
     */
    @MobileDo.MobileField(key = 0x762e)
    private CouponPromoItem couponPromoItem;

    /**
     * 优惠icon
     */
    @MobileDo.MobileField(key = 0x8a61)
    private String promoIcon;

    /**
     * 优惠标签
     */
    @MobileDo.MobileField(key = 0xbf9b)
    private DzIconTagVO tag;

    /**
     * 优惠金额, 例如-¥10
     */
    @MobileDo.MobileField(key = 0x7031)
    private String promoPrice;

    /**
     * 优惠描述
     */
    @MobileDo.MobileField(key = 0xfebf)
    private String desc;

    /**
     * 优惠标题
     */
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    /**
     * 来自于营销
     */
    @MobileDo.MobileField(key = 0x7ca7)
    private int promoType;

    /**
     * 优惠的ID，来自于营销
     */
    @MobileDo.MobileField(key = 0x271b)
    private long promoId;

    public CouponPromoItem getCouponPromoItem() {
        return couponPromoItem;
    }

    public void setCouponPromoItem(CouponPromoItem couponPromoItem) {
        this.couponPromoItem = couponPromoItem;
    }

    public String getPromoIcon() {
        return promoIcon;
    }

    public void setPromoIcon(String promoIcon) {
        this.promoIcon = promoIcon;
    }

    public DzIconTagVO getTag() {
        return tag;
    }

    public void setTag(DzIconTagVO tag) {
        this.tag = tag;
    }

    public String getPromoPrice() {
        return promoPrice;
    }

    public void setPromoPrice(String promoPrice) {
        this.promoPrice = promoPrice;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public int getPromoType() {
        return promoType;
    }

    public void setPromoType(int promoType) {
        this.promoType = promoType;
    }

    public long getPromoId() {
        return promoId;
    }

    public void setPromoId(long promoId) {
        this.promoId = promoId;
    }
}