package com.sankuai.dzshoppingguide.product.detail.application.fetcher.membercard;

import com.sankuai.dealuser.price.display.api.enums.RequestSceneEnum;
import lombok.Getter;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/2/28 16:39
 */
@Getter
public enum PriceSceneEnum {
    PREMIUM_MEMBER_CARD_NORMAL(RequestSceneEnum.PROMO_DETAIL_DESC_WithDealPromo, "商家会员查价场景 - 无优惠场景"),
    PREMIUM_MEMBER_CARD_PROMO(RequestSceneEnum.PROMO_DETAIL_DESC_WithBestPrice, "商家会员查价场景 - 有优惠场景"),
    DISCOUNT_CARD_NORMAL(RequestSceneEnum.PROMO_DETAIL_NO_ITEM_DESC, "折扣卡查价 - 无优惠场景"),
    DISCOUNT_CARD_PROMO(RequestSceneEnum.MERGE_CARD_FUSION_WithDealPromo, "折扣卡查价 - 有优惠场景"),
    DISCOUNT_CARD_COST_EFFECTIVE(RequestSceneEnum.PROMO_DETAIL_DESC, "折扣卡查价 - 是否为聚划算场景");

    private final RequestSceneEnum scene;
    private final String desc;

    PriceSceneEnum(RequestSceneEnum scene, String desc) {
        this.scene = scene;
        this.desc = desc;
    }
}
