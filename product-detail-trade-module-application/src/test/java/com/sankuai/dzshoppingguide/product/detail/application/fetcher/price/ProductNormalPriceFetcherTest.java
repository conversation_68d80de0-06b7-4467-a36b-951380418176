package com.sankuai.dzshoppingguide.product.detail.application.fetcher.price;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.enums.ProductTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.dto.ProductPriceReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import java.math.BigDecimal;
import java.util.concurrent.CompletableFuture;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class ProductNormalPriceFetcherTest {

    @Mock
    private ProductDetailPageRequest request;

    @Mock
    private DealGroupDTO dealGroupDTO;

    private TestableProductNormalPriceFetcher fetcher;

    // Test-specific subclass that exposes protected methods
    static class TestableProductNormalPriceFetcher extends ProductNormalPriceFetcher {

        @Override
        public CompletableFuture<ProductPriceReturnValue> doFetch() {
            return super.doFetch();
        }

        public void setRequest(ProductDetailPageRequest request) {
            this.request = request;
        }

        public void setTestResult(ProductBaseInfo result) {
            doReturn(result).when(this).getDependencyResult(any());
        }

        public void setTestPriceResult(PriceDisplayDTO priceDisplayDTO) {
            CompletableFuture<ProductPriceReturnValue> future = CompletableFuture.completedFuture(new ProductPriceReturnValue(priceDisplayDTO));
            doReturn(future).when(this).doFetch();
        }
    }

    @BeforeEach
    void setUp() {
        fetcher = spy(new TestableProductNormalPriceFetcher());
        fetcher.setRequest(request);
    }

    /**
     * Test case for unsupported client type
     */
    @Test
    void testDoFetch_UnsupportedClientType() throws Throwable {
        // arrange
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.UNKNOWN);
        ProductBaseInfo baseInfo = new ProductBaseInfo(ProductTypeEnum.DEAL, dealGroupDTO);
        fetcher.setTestResult(baseInfo);
        // act
        CompletableFuture<ProductPriceReturnValue> result = fetcher.doFetch();
        // assert
        assertNotNull(result);
        assertTrue(result.isDone());
        assertNull(result.get().getPriceDisplayDTO());
    }

    /**
     * Test case for ODP source
     */
    @Test
    void testDoFetch_ODPSource() throws Throwable {
        // arrange
        PriceDisplayDTO odpPrice = new PriceDisplayDTO();
        odpPrice.setPrice(new BigDecimal("15.00"));
        fetcher.setTestPriceResult(odpPrice);
        // act
        CompletableFuture<ProductPriceReturnValue> result = fetcher.doFetch();
        // assert
        assertNotNull(result);
        assertEquals(new BigDecimal("15.00"), result.get().getPriceDisplayDTO().getPrice());
    }

    /**
     * Test case for Baidu Map Mini Program
     */
    @Test
    void testDoFetch_DpBaiduMapXcx() throws Throwable {
        // arrange
        PriceDisplayDTO baiduPrice = new PriceDisplayDTO();
        baiduPrice.setPrice(new BigDecimal("25.00"));
        fetcher.setTestPriceResult(baiduPrice);
        // act
        CompletableFuture<ProductPriceReturnValue> result = fetcher.doFetch();
        // assert
        assertNotNull(result);
        assertEquals(new BigDecimal("25.00"), result.get().getPriceDisplayDTO().getPrice());
    }

    /**
     * Test case for MT Live Mini Program
     */
    @Test
    void testDoFetch_MtLiveXcx() throws Throwable {
        // arrange
        PriceDisplayDTO livePrice = new PriceDisplayDTO();
        livePrice.setPrice(new BigDecimal("30.00"));
        fetcher.setTestPriceResult(livePrice);
        // act
        CompletableFuture<ProductPriceReturnValue> result = fetcher.doFetch();
        // assert
        assertNotNull(result);
        assertEquals(new BigDecimal("30.00"), result.get().getPriceDisplayDTO().getPrice());
    }

    /**
     * Test case for merchant member price comparison
     */
    @Test
    void testDoFetch_MerchantMemberPrice() throws Throwable {
        // arrange
        PriceDisplayDTO memberPrice = new PriceDisplayDTO();
        memberPrice.setPrice(new BigDecimal("20.00"));
        fetcher.setTestPriceResult(memberPrice);
        // act
        CompletableFuture<ProductPriceReturnValue> result = fetcher.doFetch();
        // assert
        assertNotNull(result);
        assertEquals(new BigDecimal("20.00"), result.get().getPriceDisplayDTO().getPrice());
    }

    /**
     * Test case for default normal price
     */
    @Test
    void testDoFetch_DefaultNormalPrice() throws Throwable {
        // arrange
        PriceDisplayDTO defaultPrice = new PriceDisplayDTO();
        defaultPrice.setPrice(new BigDecimal("40.00"));
        fetcher.setTestPriceResult(defaultPrice);
        // act
        CompletableFuture<ProductPriceReturnValue> result = fetcher.doFetch();
        // assert
        assertNotNull(result);
        assertEquals(new BigDecimal("40.00"), result.get().getPriceDisplayDTO().getPrice());
    }

    /**
     * Test case for null price display DTO
     */
    @Test
    void testDoFetch_NullPriceDisplayDTO() throws Throwable {
        // arrange
        fetcher.setTestPriceResult(null);
        // act
        CompletableFuture<ProductPriceReturnValue> result = fetcher.doFetch();
        // assert
        assertNotNull(result);
        assertNull(result.get().getPriceDisplayDTO());
    }
}
