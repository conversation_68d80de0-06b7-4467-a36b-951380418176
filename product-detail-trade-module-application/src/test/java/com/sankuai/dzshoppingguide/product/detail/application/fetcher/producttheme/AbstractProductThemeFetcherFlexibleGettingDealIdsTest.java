package com.sankuai.dzshoppingguide.product.detail.application.fetcher.producttheme;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommendgoods.dtos.ProductM;
import com.sankuai.dztheme.deal.req.DealProductRequest;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommendgoods.ProductRecommendResult;
import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
import com.sankuai.dztheme.deal.dto.DealProductDTO;
import com.sankuai.dztheme.deal.res.DealProductResult;
import org.junit.jupiter.api.BeforeEach;

/**
 * Unit tests for AbstractProductThemeFetcher.flexibleGettingDealIds method
 */
@ExtendWith(MockitoExtension.class)
class AbstractProductThemeFetcherFlexibleGettingDealIdsTest {

    @InjectMocks
    private TestProductThemeFetcher fetcher;

    private AbstractProductThemeFetcher abstractProductThemeFetcher;

    @Mock
    private CompositeAtomService compositeAtomService;

    /**
     * Test implementation of AbstractProductThemeFetcher
     */
    private static class TestProductThemeFetcher extends AbstractProductThemeFetcher {

        @Override
        boolean enableNewService() {
            return false;
        }

        @Override
        DealProductRequest buildDealProductRequest(List<ProductM> products, Map<Long, Long> dealId2ShopId) {
            return null;
        }


        @Override
        protected CompletableFuture<ProductThemeResult> doFetch() throws Exception {
            return null;
        }
    }

    /**
     * Test empty product list
     * Expected: Return empty list
     */
    @Test
    void testFlexibleGettingDealIdsWithEmptyList() {
        // arrange
        List<ProductM> emptyList = Collections.emptyList();
        // act
        List<Integer> result = fetcher.flexibleGettingDealIds(emptyList);
        // assert
        assertNotNull(result, "Result should not be null");
        assertTrue(result.isEmpty(), "Result should be empty");
    }

    /**
     * Test null product list
     * Expected: Throw NullPointerException
     */
    @Test
    void testFlexibleGettingDealIdsWithNullList() {
        // act & assert
        assertThrows(NullPointerException.class, () -> fetcher.flexibleGettingDealIds(null), "Should throw NullPointerException for null input");
    }

    /**
     * Test list with valid product IDs (all positive)
     * Expected: Return list of converted IDs
     */
    @Test
    void testFlexibleGettingDealIdsWithValidIds() {
        // arrange
        List<ProductM> products = new ArrayList<>();
        ProductM product1 = new ProductM();
        product1.setProductId(1L);
        ProductM product2 = new ProductM();
        product2.setProductId(2L);
        products.addAll(Arrays.asList(product1, product2));
        // act
        List<Integer> result = fetcher.flexibleGettingDealIds(products);
        // assert
        assertNotNull(result, "Result should not be null");
        assertEquals(2, result.size(), "Should return 2 valid IDs");
        assertTrue(result.containsAll(Arrays.asList(1, 2)), "Should contain all valid IDs");
    }

    /**
     * Test list with invalid product IDs (zero and negative)
     * Expected: Return empty list
     */
    @Test
    void testFlexibleGettingDealIdsWithInvalidIds() {
        // arrange
        List<ProductM> products = new ArrayList<>();
        ProductM product1 = new ProductM();
        product1.setProductId(0L);
        ProductM product2 = new ProductM();
        product2.setProductId(-1L);
        products.addAll(Arrays.asList(product1, product2));
        // act
        List<Integer> result = fetcher.flexibleGettingDealIds(products);
        // assert
        assertNotNull(result, "Result should not be null");
        assertTrue(result.isEmpty(), "Result should be empty for invalid IDs");
    }

    /**
     * Test list with mixed valid and invalid product IDs
     * Expected: Return list containing only valid IDs
     */
    @Test
    void testFlexibleGettingDealIdsWithMixedIds() {
        // arrange
        List<ProductM> products = new ArrayList<>();
        ProductM product1 = new ProductM();
        product1.setProductId(1L);
        ProductM product2 = new ProductM();
        product2.setProductId(0L);
        ProductM product3 = new ProductM();
        product3.setProductId(3L);
        ProductM product4 = new ProductM();
        product4.setProductId(-1L);
        products.addAll(Arrays.asList(product1, product2, product3, product4));
        // act
        List<Integer> result = fetcher.flexibleGettingDealIds(products);
        // assert
        assertNotNull(result, "Result should not be null");
        assertEquals(2, result.size(), "Should return only valid IDs");
        assertTrue(result.containsAll(Arrays.asList(1, 3)), "Should contain only positive IDs");
    }

    /**
     * Test list with maximum valid product ID
     * Expected: Return list containing the converted max ID
     */
    @Test
    void testFlexibleGettingDealIdsWithMaxValue() {
        // arrange
        List<ProductM> products = new ArrayList<>();
        ProductM product = new ProductM();
        product.setProductId(Integer.MAX_VALUE + 1L);
        products.add(product);
        // act & assert
        assertThrows(ArithmeticException.class, () -> fetcher.flexibleGettingDealIds(products), "Should throw ArithmeticException for ID larger than Integer.MAX_VALUE");
    }

    @BeforeEach
    void setUp() {
        abstractProductThemeFetcher = new AbstractProductThemeFetcher() {

            @Override
            boolean enableNewService() {
                return false;
            }

            @Override
            DealProductRequest buildDealProductRequest(List<ProductM> products, Map<Long, Long> dealId2ShopId) {
                return null;
            }


            @Override
            protected CompletableFuture<ProductThemeResult> doFetch() throws Exception {
                return null;
            }

            @Override
            protected void paddingWithDealProductResult(ProductRecommendResult productGroupM, DealProductResult dealProductResult) {
                if (productGroupM == null || dealProductResult == null || productGroupM.getProducts() == null || dealProductResult.getDeals() == null || dealProductResult.getDeals().isEmpty()) {
                    return;
                }
                super.paddingWithDealProductResult(productGroupM, dealProductResult);
            }
        };
        abstractProductThemeFetcher.compositeAtomService = compositeAtomService;
    }

    @Test
    void testPaddingWithDealProductResult_NullDealProductResult() throws Throwable {
        // arrange
        ProductRecommendResult productGroupM = new ProductRecommendResult();
        productGroupM.setProducts(new ArrayList<>());
        // act & assert
        assertDoesNotThrow(() -> abstractProductThemeFetcher.paddingWithDealProductResult(productGroupM, null));
        verify(compositeAtomService, never()).getProductBaseInfo(any(), anyBoolean());
    }

    @Test
    void testPaddingWithDealProductResult_EmptyDeals() throws Throwable {
        // arrange
        ProductRecommendResult productGroupM = new ProductRecommendResult();
        productGroupM.setProducts(new ArrayList<>());
        DealProductResult dealProductResult = new DealProductResult();
        dealProductResult.setDeals(Collections.emptyList());
        // act & assert
        assertDoesNotThrow(() -> abstractProductThemeFetcher.paddingWithDealProductResult(productGroupM, dealProductResult));
        verify(compositeAtomService, never()).getProductBaseInfo(any(), anyBoolean());
    }

    @Test
    void testPaddingWithDealProductResult_ValidDealsWithMatch() throws Throwable {
        // arrange
        ProductRecommendResult productGroupM = new ProductRecommendResult();
        ProductM product = new ProductM();
        product.setProductId(1);
        productGroupM.setProducts(Arrays.asList(product));
        DealProductResult dealProductResult = new DealProductResult();
        DealProductDTO dealProductDTO = new DealProductDTO();
        dealProductDTO.setProductId(1);
        dealProductDTO.setName("Test Product");
        dealProductResult.setDeals(Arrays.asList(dealProductDTO));
        // act
        abstractProductThemeFetcher.paddingWithDealProductResult(productGroupM, dealProductResult);
        // assert
        assertEquals("Test Product", product.getTitle());
    }

    @Test
    void testPaddingWithDealProductResult_ValidDealsNoMatch() throws Throwable {
        // arrange
        ProductRecommendResult productGroupM = new ProductRecommendResult();
        ProductM product = new ProductM();
        product.setProductId(1);
        String originalTitle = "Original Title";
        product.setTitle(originalTitle);
        productGroupM.setProducts(Arrays.asList(product));
        DealProductResult dealProductResult = new DealProductResult();
        DealProductDTO dealProductDTO = new DealProductDTO();
        // Different ID
        dealProductDTO.setProductId(2);
        dealProductDTO.setName("Test Product");
        dealProductResult.setDeals(Arrays.asList(dealProductDTO));
        // act
        abstractProductThemeFetcher.paddingWithDealProductResult(productGroupM, dealProductResult);
        // assert
        assertEquals(originalTitle, product.getTitle());
    }

    @Test
    void testPaddingWithDealProductResult_NullProducts() throws Throwable {
        // arrange
        ProductRecommendResult productGroupM = new ProductRecommendResult();
        // products list is null by default
        DealProductResult dealProductResult = new DealProductResult();
        dealProductResult.setDeals(Arrays.asList(new DealProductDTO()));
        // act & assert
        assertDoesNotThrow(() -> abstractProductThemeFetcher.paddingWithDealProductResult(productGroupM, dealProductResult));
        verify(compositeAtomService, never()).getProductBaseInfo(any(), anyBoolean());
    }

    @Test
    void testPaddingWithDealProductResult_AllNull() throws Throwable {
        // act & assert
        assertDoesNotThrow(() -> abstractProductThemeFetcher.paddingWithDealProductResult(null, null));
        verify(compositeAtomService, never()).getProductBaseInfo(any(), anyBoolean());
    }

    @Test
    void testPaddingWithDealProductResult_NullProductGroupM() throws Throwable {
        // arrange
        DealProductResult dealProductResult = new DealProductResult();
        dealProductResult.setDeals(Arrays.asList(new DealProductDTO()));
        // act & assert
        assertDoesNotThrow(() -> abstractProductThemeFetcher.paddingWithDealProductResult(null, dealProductResult));
        verify(compositeAtomService, never()).getProductBaseInfo(any(), anyBoolean());
    }
}
