// package com.sankuai.dzshoppingguide.product.detail.application.builder.pintuan.rule;
//
// import static org.junit.jupiter.api.Assertions.*;
// import static org.mockito.Mockito.doReturn;
// import com.sankuai.dzshoppingguide.product.detail.spi.dtos.pintuan.ProductDetailFeaturesLayerConfigVO;
// import com.sankuai.dzshoppingguide.product.detail.spi.dtos.pintuan.ProductDetailLayerItemConfig;
// import java.util.ArrayList;
// import java.util.List;
// import org.junit.jupiter.api.Test;
// import org.junit.jupiter.api.extension.ExtendWith;
// import org.mockito.InjectMocks;
// import org.mockito.Spy;
// import org.mockito.junit.jupiter.MockitoExtension;
//
// @ExtendWith(MockitoExtension.class)
// public class CostEffectivePinTuanRuleModuleBuilderTest {
//
//     @Spy
//     @InjectMocks
//     private CostEffectivePinTuanRuleModuleBuilder builder;
//
//     /**
//      * Test buildPinTuanRuleFeaturesLayer with layer configs
//      */
//     @Test
//     public void testBuildPinTuanRuleFeaturesLayer_WithLayerConfigs() {
//         // arrange
//         List<ProductDetailLayerItemConfig> layerConfigs = new ArrayList<>();
//         ProductDetailLayerItemConfig config = new ProductDetailLayerItemConfig();
//         config.setTitle("Test Title");
//         config.setDesc("Test Desc");
//         layerConfigs.add(config);
//         doReturn(layerConfigs).when(builder).buildPinTuanLayerConfigs();
//         // act
//         ProductDetailFeaturesLayerConfigVO result = builder.buildPinTuanRuleFeaturesLayer();
//         // assert
//         assertNotNull(result);
//         assertEquals("查看拼团规则", result.getTitle());
//         assertEquals(layerConfigs, result.getLayerConfigs());
//         assertEquals(1, result.getLayerConfigs().size());
//         assertEquals("Test Title", result.getLayerConfigs().get(0).getTitle());
//         assertEquals("Test Desc", result.getLayerConfigs().get(0).getDesc());
//     }
//
//     /**
//      * Test buildPinTuanRuleFeaturesLayer with empty layer configs
//      */
//     @Test
//     public void testBuildPinTuanRuleFeaturesLayer_WithEmptyLayerConfigs() {
//         // arrange
//         List<ProductDetailLayerItemConfig> emptyLayerConfigs = new ArrayList<>();
//         doReturn(emptyLayerConfigs).when(builder).buildPinTuanLayerConfigs();
//         // act
//         ProductDetailFeaturesLayerConfigVO result = builder.buildPinTuanRuleFeaturesLayer();
//         // assert
//         assertNotNull(result);
//         assertEquals("查看拼团规则", result.getTitle());
//         assertNotNull(result.getLayerConfigs());
//         assertTrue(result.getLayerConfigs().isEmpty());
//     }
//
//     /**
//      * Test buildPinTuanRuleFeaturesLayer with null layer configs
//      */
//     @Test
//     public void testBuildPinTuanRuleFeaturesLayer_WithNullLayerConfigs() {
//         // arrange
//         doReturn(null).when(builder).buildPinTuanLayerConfigs();
//         // act
//         ProductDetailFeaturesLayerConfigVO result = builder.buildPinTuanRuleFeaturesLayer();
//         // assert
//         assertNotNull(result);
//         assertEquals("查看拼团规则", result.getTitle());
//         assertNull(result.getLayerConfigs());
//     }
// }
