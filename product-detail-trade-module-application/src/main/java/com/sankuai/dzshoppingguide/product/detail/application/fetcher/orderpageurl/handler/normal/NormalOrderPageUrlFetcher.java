package com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.handler.normal;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.BaseOrderPageUrlFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.ext.param.request.ExtParamBuilderRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.FinalPriceFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.FetcherDAGStarter;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: guangyujie
 * @Date: 2025/3/12 14:43
 */
@Fetcher(
        previousLayerDependencies = {
                FetcherDAGStarter.class,
                FinalPriceFetcher.class
        }
)
@Slf4j
public class NormalOrderPageUrlFetcher extends BaseOrderPageUrlFetcher {

    @Override
    protected boolean isContinue() {
        return true;
    }

    @Override
    protected Map<String, String> buildCustomExtParams() {
        return new HashMap<>();
    }

    protected void fulfillExtParamBuilderRequest(final ExtParamBuilderRequest builderRequest) {
        builderRequest.setFinalPrice(getDependencyResult(FinalPriceFetcher.class));
    }

}
