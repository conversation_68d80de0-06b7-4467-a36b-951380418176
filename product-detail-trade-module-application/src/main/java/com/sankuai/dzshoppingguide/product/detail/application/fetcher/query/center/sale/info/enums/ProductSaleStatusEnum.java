package com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sale.info.enums;

import lombok.Getter;

/**
 * @Author: g<PERSON><PERSON><PERSON>e
 * @Date: 2025/4/2 14:55
 */
@Getter
public enum ProductSaleStatusEnum {

    //通用状态
    ONLINE("在线可购买", true, null),
    OFFLINE("商品已下线", false, "已结束"),
    BEFORE_SALE_BEGIN_TIME("尚未开售", false, "即将开始"),
    AFTER_SALE_END_TIME("销售结束", false, "已结束"),
    SOLD_OUT("已买光，库存=0", false, "已卖光"),

    //商家会员
    MEMBER_EXCLUSIVE("会员专属但是该用户不是会员", false, null),

    //直播
    LIVE_EXCLUSIVE("直播专享但不是直播渠道", false, "直播专享");

    private final String desc;

    private final boolean forSale;

    private final String subTitle;

    ProductSaleStatusEnum(String desc, boolean forSale, String subTitle) {
        this.desc = desc;
        this.forSale = forSale;
        this.subTitle = subTitle;
    }

}