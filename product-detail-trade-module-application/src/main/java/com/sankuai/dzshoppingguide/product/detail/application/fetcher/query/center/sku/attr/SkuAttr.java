package com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.attr;

import com.alibaba.fastjson.JSON;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import lombok.EqualsAndHashCode;
import org.apache.commons.collections4.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * @Author: guangyujie
 * @Date: 2025/2/5 17:13
 */
@EqualsAndHashCode(callSuper = true)
public class SkuAttr extends FetcherReturnValueDTO {

    public SkuAttr(Map<Long, Map<String, AttrDTO>> skuAttr) {
        this.skuAttr = skuAttr;
    }

    /**
     * SkuId,attrName,attrValue
     */
    private final Map<Long, Map<String, AttrDTO>> skuAttr;

    public Map<String, AttrDTO> getSkuAttrs(Long skuId) {
        return Optional.ofNullable(skuAttr)
                .map(map -> map.get(skuId))
                .orElse(new HashMap<>());
    }

    public Optional<AttrDTO> getSkuAttr(long skuId, String attrName) {
        return Optional.ofNullable(skuAttr)
                .map(map -> map.get(skuId))
                .map(map -> map.get(attrName));
    }


    public List<String> getSkuAttrValue(long skuId, String attrName) {
        return getSkuAttr(skuId, attrName).map(AttrDTO::getValue).orElse(null);
    }

    public String getSkuAttrValueJson(long skuId, String attrName) {
        return getSkuAttr(skuId, attrName)
                .map(AttrDTO::getValue)
                .filter(CollectionUtils::isNotEmpty)
                .map(JSON::toJSONString)
                .orElse(null);
    }

    public String getSkuAttrFirstValue(long skuId, String attrName) {
        return getSkuAttr(skuId, attrName)
                .map(AttrDTO::getValue)
                .filter(CollectionUtils::isNotEmpty)
                .map(list -> list.get(0))
                .orElse(null);
    }

}
