package com.sankuai.dzshoppingguide.product.detail.application.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.dzshoppingguide.product.detail.application.constants.LionConstants;
import com.sankuai.dzshoppingguide.product.detail.application.utils.model.*;
import com.sankuai.dzshoppingguide.product.detail.spi.promo.vo.DealGift;
import com.sankuai.mktplay.center.mkt.play.center.client.play.entity.ActiveRequestUnitV2;
import com.sankuai.nib.mkt.common.base.enums.DepartmentTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;
import java.util.*;
/**
 * @author: wuwenqiang
 * @create: 2024-08-07
 * @description: 玩法平台查询
 */
@Slf4j
@Component
public class PlayCenterWrapper {


    private static final String DZ_DEAL_BIZ_CODE = "nib.general.groupbuy";
    private static final String COST_EFFECTIVE_SOURCE = "costEffective";
    private static final String SPECIAL_DEAL_PRODUCT_TYPE = "6";


    /**
     * 解析新客活动返回的JSON转换成实体对象
     * @param playResult
     * @return
     */
    public static List<DealGift> buildGiftsForNewCustomActivity(String playResult) {
//        if (Environment.isTestEnv()) {
//            playResult = Lion.getString("com.sankuai.dzu.tpbase.dztgdetailweb", LionConstants.NEW_CUSTOMER_AUTOMATED_TESTING);
//        }
        if (StringUtils.isEmpty(playResult)) {
            return null;
        }
        JSONObject rootNode = JSON.parseObject(playResult);
        boolean hasParticipate = rootNode.getBoolean("hasParticipate");
        if (!hasParticipate) {
            return null;
        }
        JSONArray taskInfoList = rootNode.getJSONArray("taskInfoList");
        List<MaterialInfoModel> materialInfoList = buildMaterialInfoList(rootNode.getJSONArray("materialInfoList"));
        DealGift dealGift = null;

        for (int i = 0; i < taskInfoList.size(); i++) {
            JSONObject taskInfo = taskInfoList.getJSONObject(i);
            int status = taskInfo.getInteger("status");
            int eventType = taskInfo.getInteger("eventType");
            if (status == 1) {
                JSONObject prizeInfo = taskInfo.getJSONArray("prizeInfoList").getJSONObject(0);
                dealGift = new DealGift();
                dealGift.setTitle(prizeInfo.getString("prizeName"));
                if (rootNode.getLong("endTime") != null) {
                    dealGift.setTimeDesc("活动有效期至" + formatDate(rootNode.getLong("endTime")));
                }
                dealGift.setCouponNum(taskInfo.getInteger("prizeCount"));
                switch (eventType) {
                    case 17:
                        dealGift.setProductTag("下单后发放");
                        break;
                    case 18:
                        dealGift.setProductTag("核销后发放");
                        break;
                    default:
                        break;
                }
                dealGift.setCustomerActivityPrefix("下单赠");
                dealGift.setSpecificTag("赠品");
                dealGift.setLeftIconTag(null);
                dealGift.setThumbnail(prizeInfo.getString("prizeImage"));
                dealGift.setActivityId(rootNode.getLong("activityId"));
                // 买赠规则
                dealGift.setUseRule(getBuyGiftUseRule(materialInfoList));
                break;
            }
        }
        if (dealGift != null) {
            return Lists.newArrayList(dealGift);
        }
        return Collections.emptyList();
    }

    private static String getBuyGiftUseRule(List<MaterialInfoModel> materialInfoList) {
        if (CollectionUtils.isEmpty(materialInfoList)) {
            return null;
        }
        return materialInfoList.stream()
                .filter(materialInfoModel -> "rewardRulesExplanation".equals(materialInfoModel.getFieldKey()))
                .map(MaterialInfoModel::getFieldValue)
                .findFirst()
                .orElse(null);
    }

    private ActiveRequestUnitV2 createActiveRequestUnitV2(String idKey, Long idValue) {
        if (idValue == null) {
            return null;
        }
        Map<String, String> properties = initBaseMap();
        properties.put(idKey, String.valueOf(idValue));
        ActiveRequestUnitV2 activeRequestUnitV2 = new ActiveRequestUnitV2();
        activeRequestUnitV2.setRowId(idValue);
        activeRequestUnitV2.setProperties(properties);
        return activeRequestUnitV2;
    }

    private Map<String, String> initBaseMap() {
        Map<String, String> properties = Maps.newHashMap();
        properties.put("departmentType", String.valueOf(DepartmentTypeEnum.DAOZONG.getCode()));
        properties.put("bizcode", DZ_DEAL_BIZ_CODE);
        return properties;
    }


    public static FilterPlayActivityModel convert2FilterPlayActivityModel(String result) {
        if (StringUtils.isEmpty(result)) {
            return null;
        }
        try {
            JSONObject playModel = JSONObject.parseObject(result);
            FilterPlayActivityModel filterPlayActivityModel = new FilterPlayActivityModel();
            filterPlayActivityModel.setActivityId(playModel.getString("activityId"));
            filterPlayActivityModel.setSubTitle(playModel.getString("subTitle"));
            filterPlayActivityModel.setPlayId(playModel.getLong("playId"));
            filterPlayActivityModel.setMaterialInfoList(buildMaterialInfoList(playModel.getJSONArray("materialInfoList")));
            filterPlayActivityModel.setTaskInfo(buildTaskInfo(playModel.getJSONObject("taskInfo")));
            filterPlayActivityModel.setActiveRequestUnit(JSONArray.parseArray(playModel.getString("activeRequestUnit"), ActiveRequestUnitV2.class));
            return filterPlayActivityModel;
        } catch (Exception e) {
            log.error("[PlayCenterWrapper] convert2FilterPlayActivityModel error!, result={}", result, e);
        }
        return null;
    }

    public static List<MaterialInfoModel> buildMaterialInfoList(JSONArray materialInfoList) {
        List<MaterialInfoModel> materialInfoModels = Lists.newArrayList();
        if (CollectionUtils.isEmpty(materialInfoList)) {
            return materialInfoModels;
        }
        for (int i = 0; i < materialInfoList.size(); i++) {
            JSONObject materialInfoListJSONObject = materialInfoList.getJSONObject(i);
            MaterialInfoModel materialInfoModel = new MaterialInfoModel();
            materialInfoModel.setFieldKey(materialInfoListJSONObject.getString("fieldKey"));
            materialInfoModel.setFieldValue(materialInfoListJSONObject.getString("fieldValue"));
            materialInfoModels.add(materialInfoModel);
        }
        return materialInfoModels;
    }

    public static TaskInfoModel buildTaskInfo(JSONObject taskInfoObj) {
        if (Objects.isNull(taskInfoObj)) {
            return null;
        }
        TaskInfoModel taskInfoModel = new TaskInfoModel();
        taskInfoModel.setPrizeCount(taskInfoObj.getInteger("prizeCount"));
        taskInfoModel.setTaskEndTime(taskInfoObj.getLong("taskEndTime"));
        taskInfoModel.setPrizeInfoList(buildFilterPrizeInfoList(taskInfoObj.getJSONArray("prizeInfoList")));
        return taskInfoModel;
    }

    public static List<PrizeInfoModel> buildFilterPrizeInfoList(JSONArray prizeInfoList) {
        List<PrizeInfoModel> prizeInfoModels = Lists.newArrayList();
        if (CollectionUtils.isEmpty(prizeInfoList)) {
            return prizeInfoModels;
        }
        for (int i = 0; i < prizeInfoList.size(); i++) {
            JSONObject prizeInfoListJSONObject = prizeInfoList.getJSONObject(i);
            PrizeInfoModel prizeInfoModel = new PrizeInfoModel();
            prizeInfoModel.setPrizeName(prizeInfoListJSONObject.getString("prizeName"));
            prizeInfoModel.setPrizeImage(prizeInfoListJSONObject.getString("image"));
            prizeInfoModels.add(prizeInfoModel);
        }
        return prizeInfoModels;
    }

    private static List<PrizeInfoModel> buildPrizeInfoModelList(JSONArray prizeInfoList) {
        List<PrizeInfoModel> prizeInfoModels = Lists.newArrayList();
        if (CollectionUtils.isEmpty(prizeInfoList)) {
            return prizeInfoModels;
        }
        for (int i = 0; i < prizeInfoList.size(); i++) {
            JSONObject prizeInfoListJSONObject = prizeInfoList.getJSONObject(i);
            PrizeInfoModel prizeInfoModel = new PrizeInfoModel();
            prizeInfoModel.setPrizeName(prizeInfoListJSONObject.getString("prizeName"));
            prizeInfoModel.setPrizeImage(prizeInfoListJSONObject.getString("prizeImage"));
            prizeInfoModels.add(prizeInfoModel);
        }
        return prizeInfoModels;
    }

    //Copy From团购主题DealScenePlayFetcher
    public static Map<Long, List<PlayActivityModel>> convertPlayActivityModelMap(String result) {
        try {
            if (StringUtils.isEmpty(result)) {
                return new HashMap<>();
            }
            JSONObject obj = JSONObject.parseObject(result);
            Map<Long, List<PlayActivityModel>> map = new HashMap<>();
            obj.forEach((key, value) -> {
                if (key == null || value == null) {
                    return;
                }
                map.put(Long.valueOf(key), buildPlayActivityModelList(obj.getJSONArray(key)));
            });
            return map;
        } catch (Exception e) {
            log.error("ObjectConvertUtils convertPlayActivityModelMap error", e);
            Cat.logError("ObjectConvertUtils convertPlayActivityModelMap error", e);
            return Maps.newHashMap();
        }
    }

    public static List<PlayActivityModel> buildPlayActivityModelList(JSONArray array) {
        List<PlayActivityModel> list = Lists.newArrayList();
        if (array == null) {
            return list;
        }
        //手动取
        for (int i = 0; i < array.size(); i++) {
            JSONObject obj = array.getJSONObject(i);
            JSONObject playInfo = obj.getJSONObject("playInfo");
            PlayActivityModel playActivityModel = new PlayActivityModel();
            playActivityModel.setPlayInfo(buildPlayInfo(playInfo));
            list.add(playActivityModel);
        }
        return list;
    }

    public static PlayInfoModel buildPlayInfo(JSONObject playInfo) {
        if (playInfo == null) {
            return null;
        }
        PlayInfoModel playInfoModel = new PlayInfoModel();
        playInfoModel.setTitle(playInfo.getString("title"));
        playInfoModel.setSubTitle(playInfo.getString("subTitle"));
        playInfoModel.setStartTime(playInfo.getLong("startTime"));
        playInfoModel.setEndTime(playInfo.getLong("endTime"));
        playInfoModel.setActivityId(playInfo.getLong("activityId"));
        playInfoModel.setMaterialInfoList(buildMaterialInfoList(playInfo.getJSONArray("materialInfoList")));
        playInfoModel.setTaskInfoList(buildTaskInfoList(playInfo.getJSONArray("taskInfoList")));
        return playInfoModel;
    }

    private static List<TaskInfoModel> buildTaskInfoList(JSONArray taskInfoList) {
        List<TaskInfoModel> taskInfoModels = Lists.newArrayList();
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(taskInfoList)) {
            return taskInfoModels;
        }
        for (int i = 0; i < taskInfoList.size(); i++) {
            JSONObject taskInfoListJSONObject = taskInfoList.getJSONObject(i);
            TaskInfoModel taskInfoModel = new TaskInfoModel();
            taskInfoModel.setPrizeCount(taskInfoListJSONObject.getInteger("prizeCount"));
            taskInfoModel.setStatus(taskInfoListJSONObject.getInteger("status"));
            taskInfoModel.setPrizeInfoList(buildPrizeInfoModelList(taskInfoListJSONObject.getJSONArray("prizeInfoList")));
            taskInfoModels.add(taskInfoModel);
        }
        return taskInfoModels;
    }

    /**
     * 格式化日期
     * @param timestamp
     * @return
     */
    public static String formatDate(long timestamp) {
        java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy.MM.dd");
        return sdf.format(new Date(timestamp));
    }

    public static boolean validSummerPlayExist( long dealGroupId, long poiId, FilterPlayActivityModel filterPlayActivityModel) {
        if (filterPlayActivityModel == null || CollectionUtils.isEmpty(filterPlayActivityModel.getActiveRequestUnit())
                || filterPlayActivityModel.getTaskInfo() == null || CollectionUtils.isEmpty(filterPlayActivityModel.getTaskInfo().getPrizeInfoList())) {
            return false;
        }
        // 判断暑促活动中该团单或者门店是否存在
        List<ActiveRequestUnitV2> activeRequestUnits = filterPlayActivityModel.getActiveRequestUnit();
        for (ActiveRequestUnitV2 activeRequestUnit : activeRequestUnits) {
            if (validActiveRequestUnit(activeRequestUnit, dealGroupId, poiId)) {
                return true;
            }
        }
        return false;
    }

    private static boolean validActiveRequestUnit(ActiveRequestUnitV2 activeRequestUnit, long dealId, long poiId) {
        if (activeRequestUnit == null || MapUtils.isEmpty(activeRequestUnit.getProperties())) {
            return false;
        }
        Map<String, String> properties = activeRequestUnit.getProperties();
        String dealIdStr = properties.getOrDefault("dealId", "");
        String poiIdStr = properties.getOrDefault("poiId", "");
        boolean dealIdMatch = Objects.equals(dealIdStr, String.valueOf(dealId));
        boolean poiIdMatch = Objects.equals(poiIdStr, String.valueOf(poiId));
        return dealIdMatch || poiIdMatch;
    }
}
