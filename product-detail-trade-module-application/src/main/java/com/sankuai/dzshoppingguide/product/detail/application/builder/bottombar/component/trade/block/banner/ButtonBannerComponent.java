package com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.component.trade.block.banner;

import com.dianping.lion.client.Lion;
import com.google.common.collect.Lists;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.builder.handler.standard.StandardBottomBarBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.pricebar.component.InflateCouponTipsComponent;
import com.sankuai.dzshoppingguide.product.detail.application.builder.pricebar.config.ButtonTextConfig;
import com.sankuai.dzshoppingguide.product.detail.application.constants.Constants;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.abtest.AbTestReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.model.CountrySubsidiesConfig;
import com.sankuai.dzshoppingguide.product.detail.application.model.MemberFreeConfig;
import com.sankuai.dzshoppingguide.product.detail.application.utils.LionConfigUtils;
import com.sankuai.dzshoppingguide.product.detail.application.utils.PriceUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.enums.BottomBarActionTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.enums.OpenTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.extend.buy.SuckBottomBuyActionVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.extend.none.SuckBottomDoNothingActionVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.extend.redirect.SuckBottomRedirectActionVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.banner.BottomBarTopBannerVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.banner.enums.BottomBarBannerTypeEnums;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.banner.enums.SuckBottomBarBannerTypeEnums;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.backgroud.BottomBarBackgroundVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.rich.content.PicRichContentVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.rich.content.TextRichContentVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.rich.content.enums.TextStyleEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.InflateCouponTips;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.SuckBottomBanner;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.TextDisplayInfo;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.RequestSourceEnum;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;

import static com.sankuai.dzshoppingguide.product.detail.domain.utils.LionConstants.TRADE_MODULE_APPKEY;

/**
 * <AUTHOR>
 * @date 2025-05-23
 * @desc
 */
public class ButtonBannerComponent {

    public static BottomBarTopBannerVO buildZdxhymdButtonBanner(String pageSource) {
        if (!Objects.equals(RequestSourceEnum.ZDXHYMD.getSource(), pageSource)) {
            return null;
        }
        MemberFreeConfig memberFreeConfig = LionConfigUtils.getMemberFreeConfig();
        BottomBarTopBannerVO bannerVO = new BottomBarTopBannerVO();
        bannerVO.setBannerType(BottomBarBannerTypeEnums.COMMON_TYPE.getCode());
        TextRichContentVO textRichContentVO = new TextRichContentVO(memberFreeConfig.getMemberFreeText(),
                TextStyleEnum.Default, 24, "#111111");
        bannerVO.setBackground(BottomBarBackgroundVO.buildLevelGradientColor(Lists.newArrayList("#FFE0E6", "#FEE7E1")));
        bannerVO.add(textRichContentVO);
        return bannerVO;
    }

    public static BottomBarTopBannerVO buildCountrySubsidiesButtonBanner(String flowFlag, int gpsCityId, boolean isMt) {
        if (!StringUtils.equals(flowFlag, Constants.COUNTRY_SUBSIDIES)) {
            return null;
        }
        CountrySubsidiesConfig countrySubsidiesConfig = LionConfigUtils.getCountrySubsidiesConfig();
        boolean countrySubsidiesCity = isMt ? countrySubsidiesConfig.getMtCityIds().contains(gpsCityId) : countrySubsidiesConfig.getDpCityIds().contains(gpsCityId);
        if (countrySubsidiesCity) {
            return null;
        }
        BottomBarTopBannerVO bannerVO = new BottomBarTopBannerVO();
        bannerVO.setBannerType(BottomBarBannerTypeEnums.COMMON_TYPE.getCode());
        bannerVO.setBackground(BottomBarBackgroundVO.buildLevelGradientColor(Lists.newArrayList("#DEF6E5", "#DEF6E5")));
        PicRichContentVO picRichContentVO = new PicRichContentVO();
        picRichContentVO.setIconUrl("https://p0.meituan.net/ingee/ef1faae49d155d402656ac28f2f473fa3478.png");
        picRichContentVO.setIconHeight(16);
        picRichContentVO.setIconWidth(52);
        TextRichContentVO text0 = new TextRichContentVO("当前定位", TextStyleEnum.Default, 24, "#00A72D");
        TextRichContentVO text1 = new TextRichContentVO("不在", TextStyleEnum.Default, 24, "#222222");
        TextRichContentVO text2 = new TextRichContentVO("国补城市", TextStyleEnum.Default, 24, "#00A72D");
        TextRichContentVO text3 = new TextRichContentVO(", 前往国补城市方可享受补贴!", TextStyleEnum.Default, 24, "#222222");
        bannerVO.setBannerData(Lists.newArrayList(picRichContentVO, text0, text1, text2, text3));
        return bannerVO;
    }

    public static BottomBarTopBannerVO buildSuckBottomBanner(String flowFlag, StandardBottomBarBuilder.SuckBottomBannerParam suckBottomBannerParam) {
        if (!StringUtils.equals(flowFlag, Constants.MAGIC_COUPON_ENHANCEMENT_FLOW_FLAG)) {
            return null;
        }
        // 到手价
        if (Objects.isNull(suckBottomBannerParam.getNormalPrice()) || Objects.isNull(suckBottomBannerParam.getDealPromoPrice())) {
            return null;
        }
        String finalPrice = PriceUtils.buildFinalPrice(suckBottomBannerParam.getNormalPrice().getPrice());
        AbTestReturnValue abTestReturnValue = suckBottomBannerParam.getAbTestReturnValue();
        InflateCouponTips inflateCouponTips = InflateCouponTipsComponent.buildInflateCouponTips(suckBottomBannerParam.getDealPromoPrice(),
                finalPrice, suckBottomBannerParam.getOrderPageUrl(), abTestReturnValue);
        if (Objects.isNull(inflateCouponTips)) {
            return null;
        }
        SuckBottomBanner suckBottomBanner = inflateCouponTips.getSuckBottomBanner();
        if (Objects.isNull(suckBottomBanner)) {
            return null;
        }
        // 构建banner
        BottomBarTopBannerVO bannerVO = new BottomBarTopBannerVO();
        List<TextDisplayInfo> textDisplayInfos = suckBottomBanner.getText();
        // 设置banner类型
        bannerVO.setBannerType(BottomBarBannerTypeEnums.SUCK_BOTTOM_TYPE.getCode());
        for(TextDisplayInfo textDisplayInfo : textDisplayInfos) {
            if (SuckBottomBarBannerTypeEnums.AMOUNT.getCode().equals(textDisplayInfo.getType())) {
                bannerVO.add(new TextRichContentVO(textDisplayInfo.getText(), TextStyleEnum.Bold, 24, textDisplayInfo.getTextColor()));
            }else {
                bannerVO.add(new TextRichContentVO(textDisplayInfo.getText(), TextStyleEnum.Default, 24, textDisplayInfo.getTextColor()));
            }
        }
        // 设置背景
        bannerVO.setBackground(BottomBarBackgroundVO.buildLevelGradientColor(Lists.newArrayList("#FAE5E9", "#FBE9E3")));

        // 设置banner动作
        if (ButtonTextConfig.GO_TO_BUY.equals(suckBottomBanner.getButtonText())) {
            bannerVO.setActionData(new SuckBottomBuyActionVO(
                    OpenTypeEnum.redirect, suckBottomBannerParam.getOrderPageUrl(),
                    suckBottomBanner.getButtonText(), suckBottomBanner.getPaybackIcon()

            ));
        } else if (ButtonTextConfig.GO_TO_INFLATE.equals(suckBottomBanner.getButtonText())) {
            bannerVO.setActionData(new SuckBottomRedirectActionVO(
                    BottomBarActionTypeEnum.FLOATING_LAYER, OpenTypeEnum.redirect, "",
                    suckBottomBanner.getButtonText(), suckBottomBanner.getPaybackIcon()
            ));
        } else {
            SuckBottomDoNothingActionVO suckBottomDoNothingActionVO = new SuckBottomDoNothingActionVO();
            suckBottomDoNothingActionVO.setIcon(suckBottomBanner.getPaybackIcon());
            bannerVO.setActionData(suckBottomDoNothingActionVO);
        }
        return bannerVO;
    }
}
