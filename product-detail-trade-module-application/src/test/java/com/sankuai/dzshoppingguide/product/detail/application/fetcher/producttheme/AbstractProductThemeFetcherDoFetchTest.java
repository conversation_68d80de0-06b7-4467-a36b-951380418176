package com.sankuai.dzshoppingguide.product.detail.application.fetcher.producttheme;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.BaseFetcherContext;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.cityId.CityIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.cityId.CityIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.shopid.ShopIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.shopid.ShopIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.best.shop.ShopInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.best.shop.ShopInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommendgoods.ProductRecommendResult;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommendgoods.SimilarProductRecommendFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommendgoods.dtos.ProductM;
import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
import com.sankuai.dztheme.deal.req.DealProductRequest;
import com.sankuai.dztheme.deal.res.DealProductResult;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class AbstractProductThemeFetcherDoFetchTest {

    @Mock
    private CompositeAtomService compositeAtomService;

    private TestProductThemeFetcher fetcher;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        fetcher = spy(new TestProductThemeFetcher());
        fetcher.compositeAtomService = compositeAtomService;
    }

    private ProductM createTestProduct(Long productId) {
        ProductM product = new ProductM();
        product.setProductId(productId);
        return product;
    }

    /**
     * Test case for empty product list scenario
     */
    @Test
    public void testDoFetch_WhenProductListEmpty_ShouldReturnEmptyResult() throws Throwable {
        // arrange
        ProductRecommendResult emptyResult = new ProductRecommendResult();
        fetcher.testProductRecommendResult = emptyResult;
        // act
        CompletableFuture<ProductThemeResult> future = fetcher.doFetch();
        ProductThemeResult result = future.get();
        // assert
        assertNotNull(result);
        assertTrue(result.getProducts() == null || result.getProducts().isEmpty());
        verify(compositeAtomService, never()).queryDealProductTheme(any());
        verify(compositeAtomService, never()).queryNewDealProductTheme(any());
    }

    /**
     * Test case for single product with new service enabled
     */
    @Test
    public void testDoFetch_WithSingleProduct_NewServiceEnabled() throws Throwable {
        // arrange
        ProductRecommendResult recommendResult = new ProductRecommendResult();
        List<ProductM> products = new ArrayList<>();
        products.add(createTestProduct(1L));
        recommendResult.setProducts(products);
        fetcher.testProductRecommendResult = recommendResult;
        fetcher.enableNewService = true;
        DealProductResult mockDealResult = new DealProductResult();
        when(compositeAtomService.queryNewDealProductTheme(any())).thenReturn(CompletableFuture.completedFuture(mockDealResult));
        // act
        CompletableFuture<ProductThemeResult> future = fetcher.doFetch();
        ProductThemeResult result = future.get();
        // assert
        assertNotNull(result);
        assertEquals(1, result.getProducts().size());
        verify(compositeAtomService).queryNewDealProductTheme(any());
        verify(compositeAtomService, never()).queryDealProductTheme(any());
    }

    /**
     * Test case for multiple products requiring batch processing with old service
     */
    @Test
    public void testDoFetch_WithMultipleProducts_OldService() throws Throwable {
        // arrange
        ProductRecommendResult recommendResult = new ProductRecommendResult();
        List<ProductM> products = new ArrayList<>();
        for (int i = 0; i < 150; i++) {
            products.add(createTestProduct((long) i));
        }
        recommendResult.setProducts(products);
        fetcher.testProductRecommendResult = recommendResult;
        fetcher.enableNewService = false;
        DealProductResult mockDealResult = new DealProductResult();
        when(compositeAtomService.queryDealProductTheme(any())).thenReturn(CompletableFuture.completedFuture(mockDealResult));
        // act
        CompletableFuture<ProductThemeResult> future = fetcher.doFetch();
        ProductThemeResult result = future.get();
        // assert
        assertNotNull(result);
        assertEquals(150, result.getProducts().size());
        verify(compositeAtomService, times(2)).queryDealProductTheme(any());
    }

    /**
     * Test case for exactly 100 products batch boundary
     */
    @Test
    public void testDoFetch_WithExactly100Products_NewService() throws Throwable {
        // arrange
        ProductRecommendResult recommendResult = new ProductRecommendResult();
        List<ProductM> products = new ArrayList<>();
        for (int i = 0; i < 100; i++) {
            products.add(createTestProduct((long) i));
        }
        recommendResult.setProducts(products);
        fetcher.testProductRecommendResult = recommendResult;
        fetcher.enableNewService = true;
        DealProductResult mockDealResult = new DealProductResult();
        when(compositeAtomService.queryNewDealProductTheme(any())).thenReturn(CompletableFuture.completedFuture(mockDealResult));
        // act
        CompletableFuture<ProductThemeResult> future = fetcher.doFetch();
        ProductThemeResult result = future.get();
        // assert
        assertNotNull(result);
        assertEquals(100, result.getProducts().size());
        verify(compositeAtomService, times(1)).queryNewDealProductTheme(any());
    }

    private class TestProductThemeFetcher extends AbstractProductThemeFetcher {

        private boolean enableNewService;

        private ProductRecommendResult testProductRecommendResult;

        private CityIdMapper testCityIdMapper = new CityIdMapper();

        private ShopIdMapper testShopIdMapper = new ShopIdMapper();

        private ShopInfo testShopInfo = new ShopInfo();

        @Override
        boolean enableNewService() {
            return enableNewService;
        }

        @Override
        DealProductRequest buildDealProductRequest(List<ProductM> products, Map<Long, Long> dealId2ShopId) {
            DealProductRequest request = new DealProductRequest();
            List<Integer> productIds = new ArrayList<>();
            for (ProductM product : products) {
                productIds.add((int) product.getProductId());
            }
            request.setProductIds(productIds);
            return request;
        }

        @Override
        protected void paddingWithDealProductResult(ProductRecommendResult productGroupM, DealProductResult dealProductResult) {
            // Do nothing for test
        }

        @Override
        protected <T extends FetcherReturnValueDTO> T getDependencyResult(Class<? extends BaseFetcherContext> dependencyFetcherClass) {
            if (dependencyFetcherClass == SimilarProductRecommendFetcher.class) {
                return (T) testProductRecommendResult;
            } else if (dependencyFetcherClass == CityIdMapperFetcher.class) {
                return (T) testCityIdMapper;
            } else if (dependencyFetcherClass == ShopIdMapperFetcher.class) {
                return (T) testShopIdMapper;
            } else if (dependencyFetcherClass == ShopInfoFetcher.class) {
                return (T) testShopInfo;
            }
            return null;
        }
    }
}
