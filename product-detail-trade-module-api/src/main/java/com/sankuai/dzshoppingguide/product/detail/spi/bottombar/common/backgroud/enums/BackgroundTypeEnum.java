package com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.backgroud.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: guang<PERSON><PERSON>e
 * @Date: 2025/3/17 10:41
 */
@Getter
public enum BackgroundTypeEnum {

    COLOR(1, "纯色"),
    PIC(2, "图片"),
    LEVEL_GRADIENT_COLOR(3, "水平渐变色，从左到右"),
    VERTICAL_GRADIENT_COLOR(4, "垂直渐变色，从上到下");

    private final int code;

    private final String desc;

    BackgroundTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static BackgroundTypeEnum fromCode(int code) {
        for (BackgroundTypeEnum value : BackgroundTypeEnum.values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        throw new IllegalArgumentException("无效code:" + code);
    }

    public static boolean containsCode(int code) {
        for (BackgroundTypeEnum value : BackgroundTypeEnum.values()) {
            if (value.getCode() == code) {
                return true;
            }
        }
        return false;
    }

    public static Map<Integer, String> buildCodeDescMap() {
        return Arrays.stream(BackgroundTypeEnum.values()).collect(Collectors.toMap(
                BackgroundTypeEnum::getCode,
                BackgroundTypeEnum::getDesc
        ));
    }

    public static Map<String, String> buildNameDescMap() {
        return Arrays.stream(BackgroundTypeEnum.values()).collect(Collectors.toMap(
                BackgroundTypeEnum::name,
                BackgroundTypeEnum::getDesc
        ));
    }

}