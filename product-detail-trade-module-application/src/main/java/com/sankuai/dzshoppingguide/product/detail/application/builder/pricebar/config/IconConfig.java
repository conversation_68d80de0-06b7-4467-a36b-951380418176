package com.sankuai.dzshoppingguide.product.detail.application.builder.pricebar.config;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/04/02
 * 神券感知增强 icon配置
 */
@Data
public class IconConfig {

    /**
     * 市场价图标文案
     */
    private String marketPriceIconText;

    /**
     * 双红包图标
     */
    private String doubleRedPacket;

    /**
     * 双红包图标-宽
     */
    private int doubleRedPacketWidth;

    /**
     * 双红包图标-高
     */
    private int doubleRedPacketHeight;

    /**
     * 已膨胀神券图标
     */
    private String afterInflate;

    /**
     * 已膨胀神券图标-宽
     */
    private int afterInflateWidth;

    /**
     * 已膨胀神券图标-高
     */
    private int afterInflateHeight;

    /**
     * 免费膨胀图标
     */
    private String freeInflateArrow;
    /**
     * 免费膨胀图标-宽
     */
    private int freeInflateArrowWidth;

    /**
     * 免费膨胀图标-高
     */
    private int freeInflateArrowHeight;

    /**
     * 有神券氛围图
     */
    private String magicalCouponAtmosphereImage;

    /**
     * 无神券氛围图
     */
    private String noMagicalCouponAtmosphereImage;

    /**
     * 提示条背景图
     */
    private String tipsBackgroundImageUrl;

    /**
     * 吸底条前-icon SuckBottomBanner.pan
     */
    private String suckBottomBannerIcon;
}
