package com.sankuai.dzshoppingguide.product.detail.application.builder.atmospherebar.builder.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/04/02
 * 优惠场景枚举
 */
@Getter
public enum PromoSceneEnum {

    /**
     * 仅团购优惠
     */
    ONLY_DEAL_PROMO("ONLY_DEAL_PROMO", "仅团购优惠"),

    /**
     * 除团购优惠以外的优惠类型，不含神券
     */
    NON_DEAL_PROMO_NO_MAGICAL_COUPON("NON_DEAL_PROMO_NO_MAGICAL_COUPON", "除团购优惠以外的优惠，不含神券"),

    /**
     * 有神券
     */
    HAS_MAGICAL_COUPON("HAS_MAGICAL_COUPON", "有神券"),

    /**
     * 无优惠
     */
    NO_PROMO("NO_PROMO","无优惠");


    private final String code;
    private final String desc;

    PromoSceneEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}
