package com.sankuai.dzshoppingguide.product.detail.spi.similargoods.vo.dtos;

import com.dianping.mobile.framework.annotation.MobileDo;

import java.io.Serializable;

@MobileDo(id = 0x7635)
public class DzPromoVO implements Serializable {
    /**
    * 优惠名称：如“已优惠10"、"共省20"
    */
    @MobileDo.MobileField(key = 0x7ab8)
    private String name;

    /**
    * 图片后是否需要取消间距
    */
    @MobileDo.MobileField(key = 0x2d9d)
    private boolean noGapBetweenPicText;

    /**
    * 边框颜色
    */
    @MobileDo.MobileField(key = 0xad82)
    private String borderColor;

    /**
    * 是否有边框
    */
    @MobileDo.MobileField(key = 0xc51b)
    private boolean hasBorder;

    /**
    * 边框圆角
    */
    @MobileDo.MobileField(key = 0xabb4)
    private double borderRadius;

    /**
    * 文字颜色
    */
    @MobileDo.MobileField(key = 0xeead)
    private String textColor;

    /**
    * 背景填充色
    */
    @MobileDo.MobileField(key = 0xba62)
    private String backgroundColor;

    /**
    * 优惠手段类型 （0、无特殊手段 1、超级补贴 2、会员 3、回购特惠）
    */
    @MobileDo.MobileField(key = 0xbedd)
    private int favorableMeasuresType;

    /**
    *  后置图片
    */
    @MobileDo.MobileField(key = 0xb464)
    private DzPictureComponent postPic;

    /**
    * 前置图片icon
    */
    @MobileDo.MobileField(key = 0x9a73)
    private DzPictureComponent prePic;

    /**
    * 优惠细节
    */
    @MobileDo.MobileField(key = 0x5cb1)
    private DzPromoDetailVO promoDetail;

    /**
    * 优惠简述
    */
    @MobileDo.MobileField(key = 0xcf50)
    private String promo;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public boolean getNoGapBetweenPicText() {
        return noGapBetweenPicText;
    }

    public void setNoGapBetweenPicText(boolean noGapBetweenPicText) {
        this.noGapBetweenPicText = noGapBetweenPicText;
    }

    public String getBorderColor() {
        return borderColor;
    }

    public void setBorderColor(String borderColor) {
        this.borderColor = borderColor;
    }

    public boolean getHasBorder() {
        return hasBorder;
    }

    public void setHasBorder(boolean hasBorder) {
        this.hasBorder = hasBorder;
    }

    public double getBorderRadius() {
        return borderRadius;
    }

    public void setBorderRadius(double borderRadius) {
        this.borderRadius = borderRadius;
    }

    public String getTextColor() {
        return textColor;
    }

    public void setTextColor(String textColor) {
        this.textColor = textColor;
    }

    public String getBackgroundColor() {
        return backgroundColor;
    }

    public void setBackgroundColor(String backgroundColor) {
        this.backgroundColor = backgroundColor;
    }

    public int getFavorableMeasuresType() {
        return favorableMeasuresType;
    }

    public void setFavorableMeasuresType(int favorableMeasuresType) {
        this.favorableMeasuresType = favorableMeasuresType;
    }

    public DzPictureComponent getPostPic() {
        return postPic;
    }

    public void setPostPic(DzPictureComponent postPic) {
        this.postPic = postPic;
    }

    public DzPictureComponent getPrePic() {
        return prePic;
    }

    public void setPrePic(DzPictureComponent prePic) {
        this.prePic = prePic;
    }

    public DzPromoDetailVO getPromoDetail() {
        return promoDetail;
    }

    public void setPromoDetail(DzPromoDetailVO promoDetail) {
        this.promoDetail = promoDetail;
    }

    public String getPromo() {
        return promo;
    }

    public void setPromo(String promo) {
        this.promo = promo;
    }
}