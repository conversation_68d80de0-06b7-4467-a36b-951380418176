package com.sankuai.dzshoppingguide.product.detail.application.fetcher.price;

import static org.mockito.Mockito.*;
import com.sankuai.dealuser.price.display.api.enums.DzPriceSceneEnum;
import com.sankuai.dealuser.price.display.api.enums.ExtensionKeyEnum;
import com.sankuai.dealuser.price.display.api.model.ClientEnv;
import com.sankuai.dealuser.price.display.api.model.ProductIdentity;
import com.sankuai.dz.product.detail.RequestCustomParamEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.dto.ProductPriceReturnValue;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * Test class for ProductCostEffectivePriceFetcher's postProcessPriceRequest method
 */
@ExtendWith(MockitoExtension.class)
class ProductCostEffectivePriceFetcherTest {

    private TestableProductCostEffectivePriceFetcher productCostEffectivePriceFetcher;

    private ProductDetailPageRequest mockRequest;

    private ClientEnv clientEnv;

    private List<ProductIdentity> productIdentityList;

    private Map<String, String> extension;

    private ProductIdentity productIdentity;

    private static class TestableProductCostEffectivePriceFetcher extends ProductCostEffectivePriceFetcher {

        public void setRequest(ProductDetailPageRequest request) {
            this.request = request;
        }

        @Override
        protected CompletableFuture<ProductPriceReturnValue> doFetch() {
            return CompletableFuture.completedFuture(new ProductPriceReturnValue());
        }
    }

    @BeforeEach
    void setUp() {
        // Initialize the fetcher and mock request
        productCostEffectivePriceFetcher = new TestableProductCostEffectivePriceFetcher();
        mockRequest = mock(ProductDetailPageRequest.class);
        productCostEffectivePriceFetcher.setRequest(mockRequest);
        // Initialize test data
        clientEnv = new ClientEnv();
        productIdentityList = new ArrayList<>();
        productIdentity = new ProductIdentity();
        productIdentity.setExtParams(new HashMap<>());
        productIdentityList.add(productIdentity);
        extension = new HashMap<>();
    }

    /**
     * Test case: When pintuanActivityId and orderGroupId are both null
     */
    @Test
    void testPostProcessPriceRequest_BothIdsNull() throws Throwable {
        // arrange
        when(mockRequest.getCustomParam(RequestCustomParamEnum.pintuanActivityId)).thenReturn(null);
        when(mockRequest.getCustomParam(RequestCustomParamEnum.orderGroupId)).thenReturn(null);
        // act
        productCostEffectivePriceFetcher.postProcessPriceRequest(clientEnv, productIdentityList, extension);
        // assert
        Assertions.assertEquals(String.valueOf(DzPriceSceneEnum.COST_EFFECTIVE_PIN_TUAN_DEDUCTION.getCode()), extension.get(ExtensionKeyEnum.DzPriceScene.getDesc()));
        Assertions.assertTrue(productIdentity.getExtParams().isEmpty());
    }

    /**
     * Test case: When only pintuanActivityId is present
     */
    @Test
    void testPostProcessPriceRequest_OnlyPintuanActivityId() throws Throwable {
        // arrange
        when(mockRequest.getCustomParam(RequestCustomParamEnum.pintuanActivityId)).thenReturn("123");
        when(mockRequest.getCustomParam(RequestCustomParamEnum.orderGroupId)).thenReturn(null);
        // act
        productCostEffectivePriceFetcher.postProcessPriceRequest(clientEnv, productIdentityList, extension);
        // assert
        Assertions.assertEquals(String.valueOf(DzPriceSceneEnum.COST_EFFECTIVE_PIN_TUAN_DEDUCTION.getCode()), extension.get(ExtensionKeyEnum.DzPriceScene.getDesc()));
        Assertions.assertEquals("123", productIdentity.getExtParams().get(ExtensionKeyEnum.PinTuanActivityId.getDesc()));
        Assertions.assertNull(productIdentity.getExtParams().get(ExtensionKeyEnum.ShareToken.getDesc()));
    }

    /**
     * Test case: When only orderGroupId is present
     */
    @Test
    void testPostProcessPriceRequest_OnlyOrderGroupId() throws Throwable {
        // arrange
        when(mockRequest.getCustomParam(RequestCustomParamEnum.pintuanActivityId)).thenReturn(null);
        when(mockRequest.getCustomParam(RequestCustomParamEnum.orderGroupId)).thenReturn("456");
        // act
        productCostEffectivePriceFetcher.postProcessPriceRequest(clientEnv, productIdentityList, extension);
        // assert
        Assertions.assertEquals(String.valueOf(DzPriceSceneEnum.COST_EFFECTIVE_PIN_TUAN_DEDUCTION.getCode()), extension.get(ExtensionKeyEnum.DzPriceScene.getDesc()));
        Assertions.assertNull(productIdentity.getExtParams().get(ExtensionKeyEnum.PinTuanActivityId.getDesc()));
        Assertions.assertEquals("456", productIdentity.getExtParams().get(ExtensionKeyEnum.ShareToken.getDesc()));
    }

    /**
     * Test case: When both pintuanActivityId and orderGroupId are present
     */
    @Test
    void testPostProcessPriceRequest_BothIdsPresent() throws Throwable {
        // arrange
        when(mockRequest.getCustomParam(RequestCustomParamEnum.pintuanActivityId)).thenReturn("123");
        when(mockRequest.getCustomParam(RequestCustomParamEnum.orderGroupId)).thenReturn("456");
        // act
        productCostEffectivePriceFetcher.postProcessPriceRequest(clientEnv, productIdentityList, extension);
        // assert
        Assertions.assertEquals(String.valueOf(DzPriceSceneEnum.COST_EFFECTIVE_PIN_TUAN_DEDUCTION.getCode()), extension.get(ExtensionKeyEnum.DzPriceScene.getDesc()));
        Assertions.assertEquals("123", productIdentity.getExtParams().get(ExtensionKeyEnum.PinTuanActivityId.getDesc()));
        Assertions.assertEquals("456", productIdentity.getExtParams().get(ExtensionKeyEnum.ShareToken.getDesc()));
    }

    /**
     * Test case: When productIdentityList is empty
     */
    @Test
    void testPostProcessPriceRequest_EmptyProductIdentityList() throws Throwable {
        // arrange
        productIdentityList.clear();
        when(mockRequest.getCustomParam(RequestCustomParamEnum.pintuanActivityId)).thenReturn("123");
        when(mockRequest.getCustomParam(RequestCustomParamEnum.orderGroupId)).thenReturn("456");
        // act
        productCostEffectivePriceFetcher.postProcessPriceRequest(clientEnv, productIdentityList, extension);
        // assert
        Assertions.assertEquals(String.valueOf(DzPriceSceneEnum.COST_EFFECTIVE_PIN_TUAN_DEDUCTION.getCode()), extension.get(ExtensionKeyEnum.DzPriceScene.getDesc()));
        Assertions.assertTrue(productIdentityList.isEmpty());
    }

    /**
     * Test case: When extension map is null
     */
    @Test
    void testPostProcessPriceRequest_NullExtension() throws Throwable {
        // arrange
        // act & assert
        Assertions.assertThrows(NullPointerException.class, () -> productCostEffectivePriceFetcher.postProcessPriceRequest(clientEnv, productIdentityList, null));
    }
}
