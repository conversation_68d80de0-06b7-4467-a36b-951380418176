package com.sankuai.dzshoppingguide.product.detail.application.utils;

import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 美团安心改
 */
public class ReassuredRepairUtil {

    //美团安心改标签
    public static final Long LABEL_ID = 100218044L;

    //判断当前团单是否为美团安心改
    public static boolean isTagPresent(ProductBaseInfo dealGroupBase) {
        return Objects.nonNull(dealGroupBase) && CollectionUtils.isNotEmpty(dealGroupBase.getTags())
                && dealGroupBase.getTags().stream()
                .map(DealGroupTagDTO::getId)
                .collect(Collectors.toList())
                .contains(LABEL_ID);
    }


}
