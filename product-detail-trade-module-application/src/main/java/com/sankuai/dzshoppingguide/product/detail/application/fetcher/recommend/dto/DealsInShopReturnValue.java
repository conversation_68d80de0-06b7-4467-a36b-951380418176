package com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommend.dto;


import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @Author: zhangyuan103
 * @Date: 2025/5/23
 * @Description 门店适用的在线售卖团单，区分平台
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
public class DealsInShopReturnValue extends FetcherReturnValueDTO {
    private List<Long> allDealIds;
}
