package com.sankuai.dzshoppingguide.product.detail.application.spi;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSON;
import com.dianping.lion.client.Lion;
import com.sankuai.dz.api.module.arrange.framework.application.ModuleArrangeFrameworkRunner;
import com.sankuai.dz.api.module.arrange.framework.application.request.ModuleArrangeRequest;
import com.sankuai.dz.api.module.arrange.framework.application.response.FrameworkRunnerResult;
import com.sankuai.dz.product.detail.gateway.spi.dto.ABResultDTO;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dz.product.detail.gateway.spi.response.ModuleResponse;
import com.sankuai.dz.product.detail.gateway.spi.response.ProductDetailPageResponse;
import com.sankuai.dz.product.detail.gateway.spi.vo.AbstractModuleVO;
import com.sankuai.dzshoppingguide.product.detail.application.constants.LionConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.ab.vo.AbResultVO;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import java.util.*;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class ProductDetailPageTradeModuleSpiImplTest {

    private ProductDetailPageTradeModuleSpiImpl spi;

    @Mock
    private ProductDetailPageRequest request;

    @BeforeEach
    void setUp() {
        spi = new ProductDetailPageTradeModuleSpiImpl();
    }

    /**
     * Test query with empty moduleKeys
     */
    @Test
    public void testQueryWithEmptyModuleKeys() throws Throwable {
        // arrange
        when(request.getModuleKeys()).thenReturn(null);
        // act
        ProductDetailPageResponse response = spi.query(request);
        // assert
        assertNotNull(response);
        assertEquals("入参ModuleKey为空", response.getMsg());
        verify(request, atLeastOnce()).getModuleKeys();
    }

    /**
     * Test query with request validation failure
     */
    @Test
    public void testQueryWithRequestValidationFailure() throws Throwable {
        // arrange
        Set<String> moduleKeys = new HashSet<>();
        moduleKeys.add("testModule");
        when(request.getModuleKeys()).thenReturn(moduleKeys);
        doThrow(new IllegalArgumentException("Invalid request")).when(request).checkParam();
        // act
        ProductDetailPageResponse response = spi.query(request);
        // assert
        assertNotNull(response);
        assertEquals("Invalid request", response.getMsg());
        verify(request).checkParam();
    }

    /**
     * Test query verifies default module keys are added
     */
    @Test
    public void testQueryDefaultModuleKeysAdded() throws Throwable {
        // arrange
        Set<String> moduleKeys = spy(new HashSet<>());
        moduleKeys.add("testModule");
        when(request.getModuleKeys()).thenReturn(moduleKeys);
        // act
        try {
            spi.query(request);
        } catch (Exception e) {
            // Expected exception due to mock framework runner
        }
        // assert
        assertTrue(moduleKeys.contains(ModuleKeyConstants.TRADE_COMMON_DATA));
        assertTrue(moduleKeys.contains(ModuleKeyConstants.PAGE_AB_RESULT));
        verify(request, atLeastOnce()).getModuleKeys();
    }

    /**
     * Test query with empty module response
     */
    @Test
    public void testQueryWithEmptyModuleResponse() throws Throwable {
        // arrange
        Set<String> moduleKeys = new HashSet<>();
        moduleKeys.add("testModule");
        when(request.getModuleKeys()).thenReturn(moduleKeys);
        // act
        ProductDetailPageResponse response = spi.query(request);
        // assert
        assertNotNull(response);
        assertNull(response.getAbResultList());
    }

    // Helper class for testing
    private static class InvalidModuleVO extends AbstractModuleVO {

        @Override
        public String getModuleKey() {
            return "invalid";
        }
    }
}
