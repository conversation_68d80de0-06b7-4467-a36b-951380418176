package com.sankuai.dzshoppingguide.product.detail.spi.dtos;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: <EMAIL>
 * @Date: 2025/2/8
 */
@Data
@TypeDoc(description = "商品详情页分享模块")
@MobileDo(id = 0x4623)
public class NavBarShareModuleVO implements Serializable {
    @FieldDoc(description = "商品名称")
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    @FieldDoc(description = "分享描述")
    @MobileDo.MobileField(key = 0xfebf)
    private String desc = "";

    @FieldDoc(description = "分享链接")
    @MobileDo.MobileField(key = 0xc56e)
    private String url;

    @FieldDoc(description = "小程序配置信息")
    @MobileDo.MobileField(key = 0xe66e)
    private ShareModuleMiniProgramConfig miniProgramConfig;

    @FieldDoc(description = "分享图标")
    @MobileDo.MobileField(key = 0xaca1)
    private String image;
}
