package com.sankuai.dzshoppingguide.product.detail.application.utils;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.general.product.query.center.client.dto.DealGroupTagDTO;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

/**
 * Test cases for ReassuredRepairUtil.isTagPresent method
 */
class ReassuredRepairUtilTest {

    private static final Long TARGET_LABEL_ID = 100218044L;

    /**
     * Test case for null tags list
     * Expected: should return false when tags list is null
     */
    @Test
    public void testIsTagPresentWithNullTags() throws Throwable {
        // arrange
        ProductBaseInfo dealGroupBase = Mockito.mock(ProductBaseInfo.class);
        when(dealGroupBase.getTags()).thenReturn(null);
        // act & assert
        assertFalse(ReassuredRepairUtil.isTagPresent(dealGroupBase));
    }

    /**
     * Test case for empty tags list
     * Expected: should return false when tags list is empty
     */
    @Test
    public void testIsTagPresentWithEmptyTags() throws Throwable {
        // arrange
        ProductBaseInfo dealGroupBase = Mockito.mock(ProductBaseInfo.class);
        when(dealGroupBase.getTags()).thenReturn(Collections.emptyList());
        // act & assert
        assertFalse(ReassuredRepairUtil.isTagPresent(dealGroupBase));
    }

    /**
     * Test case for tags list without the target tag
     * Expected: should return false when tags list doesn't contain target tag
     */
    @Test
    public void testIsTagPresentWithoutTargetTag() throws Throwable {
        // arrange
        ProductBaseInfo dealGroupBase = Mockito.mock(ProductBaseInfo.class);
        DealGroupTagDTO tag = new DealGroupTagDTO();
        // Different ID from target
        tag.setId(TARGET_LABEL_ID + 1);
        List<DealGroupTagDTO> tags = Collections.singletonList(tag);
        when(dealGroupBase.getTags()).thenReturn(tags);
        // act & assert
        assertFalse(ReassuredRepairUtil.isTagPresent(dealGroupBase));
    }

    /**
     * Test case for tags list containing the target tag
     * Expected: should return true when tags list contains target tag
     */
    @Test
    public void testIsTagPresentWithTargetTag() throws Throwable {
        // arrange
        ProductBaseInfo dealGroupBase = Mockito.mock(ProductBaseInfo.class);
        DealGroupTagDTO tag1 = new DealGroupTagDTO();
        tag1.setId(TARGET_LABEL_ID);
        DealGroupTagDTO tag2 = new DealGroupTagDTO();
        tag2.setId(TARGET_LABEL_ID + 1);
        List<DealGroupTagDTO> tags = Arrays.asList(tag1, tag2);
        when(dealGroupBase.getTags()).thenReturn(tags);
        // act & assert
        assertTrue(ReassuredRepairUtil.isTagPresent(dealGroupBase));
    }

    /**
     * Test case for tags list containing multiple instances of target tag
     * Expected: should return true when tags list contains target tag multiple times
     */
    @Test
    public void testIsTagPresentWithMultipleTargetTags() throws Throwable {
        // arrange
        ProductBaseInfo dealGroupBase = Mockito.mock(ProductBaseInfo.class);
        DealGroupTagDTO tag1 = new DealGroupTagDTO();
        tag1.setId(TARGET_LABEL_ID);
        DealGroupTagDTO tag2 = new DealGroupTagDTO();
        tag2.setId(TARGET_LABEL_ID);
        List<DealGroupTagDTO> tags = Arrays.asList(tag1, tag2);
        when(dealGroupBase.getTags()).thenReturn(tags);
        // act & assert
        assertTrue(ReassuredRepairUtil.isTagPresent(dealGroupBase));
    }
}
