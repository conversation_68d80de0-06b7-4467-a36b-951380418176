package com.sankuai.dzshoppingguide.product.detail.application.utils;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import java.util.Calendar;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DateAndTimeUtilsConvertToLongTimeTest {

    /**
     * 测试 convertToLongTime 方法，当 calendarUnit 等于 Calendar.MINUTE 时
     */
    @Test
    public void testConvertToLongTimeWhenCalendarUnitIsMinute() throws Throwable {
        // arrange
        int count = 2;
        int calendarUnit = Calendar.MINUTE;
        // act
        long result = DateAndTimeUtils.convertToLongTime(count, calendarUnit);
        // assert
        assertEquals(120000, result);
    }

    /**
     * 测试 convertToLongTime 方法，当 calendarUnit 等于 Calendar.HOUR 时
     */
    @Test
    public void testConvertToLongTimeWhenCalendarUnitIsHour() throws Throwable {
        // arrange
        int count = 2;
        int calendarUnit = Calendar.HOUR;
        // act
        long result = DateAndTimeUtils.convertToLongTime(count, calendarUnit);
        // assert
        assertEquals(7200000, result);
    }

    /**
     * 测试 convertToLongTime 方法，当 calendarUnit 不等于 Calendar.MINUTE 且不等于 Calendar.HOUR 时
     */
    @Test
    public void testConvertToLongTimeWhenCalendarUnitIsNotMinuteOrHour() throws Throwable {
        // arrange
        int count = 2;
        int calendarUnit = Calendar.DAY_OF_MONTH;
        // act
        long result = DateAndTimeUtils.convertToLongTime(count, calendarUnit);
        // assert
        assertEquals(0, result);
    }

    /**
     * 测试 convertWeekDay 方法，输入周日（DAY_OF_WEEK = 1）
     * 预期返回 7
     */
    @Test
    public void testConvertWeekDay_Sunday() throws Throwable {
        // arrange
        Calendar calendar = Calendar.getInstance();
        // 2024-01-07 是周日
        calendar.set(2024, Calendar.JANUARY, 7);
        long timeInMillis = calendar.getTimeInMillis();
        // act
        int result = DateAndTimeUtils.convertWeekDay(timeInMillis);
        // assert
        assertEquals("Sunday should be converted to 7", 7, result);
    }

    /**
     * 测试 convertWeekDay 方法，输入周一（DAY_OF_WEEK = 2）
     * 预期返回 1
     */
    @Test
    public void testConvertWeekDay_Monday() throws Throwable {
        // arrange
        Calendar calendar = Calendar.getInstance();
        // 2024-01-08 是周一
        calendar.set(2024, Calendar.JANUARY, 8);
        long timeInMillis = calendar.getTimeInMillis();
        // act
        int result = DateAndTimeUtils.convertWeekDay(timeInMillis);
        // assert
        assertEquals("Monday should be converted to 1", 1, result);
    }

    /**
     * 测试 convertWeekDay 方法，输入周三（DAY_OF_WEEK = 4）
     * 预期返回 3
     */
    @Test
    public void testConvertWeekDay_Wednesday() throws Throwable {
        // arrange
        Calendar calendar = Calendar.getInstance();
        // 2024-01-10 是周三
        calendar.set(2024, Calendar.JANUARY, 10);
        long timeInMillis = calendar.getTimeInMillis();
        // act
        int result = DateAndTimeUtils.convertWeekDay(timeInMillis);
        // assert
        assertEquals("Wednesday should be converted to 3", 3, result);
    }

    /**
     * 测试 convertWeekDay 方法，输入周六（DAY_OF_WEEK = 7）
     * 预期返回 6
     */
    @Test
    public void testConvertWeekDay_Saturday() throws Throwable {
        // arrange
        Calendar calendar = Calendar.getInstance();
        // 2024-01-13 是周六
        calendar.set(2024, Calendar.JANUARY, 13);
        long timeInMillis = calendar.getTimeInMillis();
        // act
        int result = DateAndTimeUtils.convertWeekDay(timeInMillis);
        // assert
        assertEquals("Saturday should be converted to 6", 6, result);
    }

    /**
     * 测试 convertWeekDay 方法，输入最小时间戳
     * 预期返回值应在1-7范围内
     */
    @Test
    public void testConvertWeekDay_MinTimestamp() throws Throwable {
        // arrange
        long timeInMillis = Long.MIN_VALUE;
        // act
        int result = DateAndTimeUtils.convertWeekDay(timeInMillis);
        // assert
        assertTrue("Result should be between 1 and 7", result >= 1 && result <= 7);
    }

    /**
     * 测试 convertWeekDay 方法，输入最大时间戳
     * 预期返回值应在1-7范围内
     */
    @Test
    public void testConvertWeekDay_MaxTimestamp() throws Throwable {
        // arrange
        long timeInMillis = Long.MAX_VALUE;
        // act
        int result = DateAndTimeUtils.convertWeekDay(timeInMillis);
        // assert
        assertTrue("Result should be between 1 and 7", result >= 1 && result <= 7);
    }

    /**
     * Test normal case where method returns seven consecutive days starting from today
     */
    @Test
    public void testGetRecentSevenDayZeroTime_NormalCase() throws Throwable {
        // act
        List<Long> result = DateAndTimeUtils.getRecentSevenDayZeroTime();
        // assert
        assertNotNull(result);
        assertEquals(7, result.size());
        // Since we cannot mock static methods to control the return value of getRecentSevenDayZeroTime,
        // this test assumes the method's correct behavior based on the system's current time.
        // This is a limitation of the test approach and highlights the importance of refactoring
        // for testability or using a different testing strategy.
    }

    /**
     * 测试 convertFormatDateToLongTime 方法，当 formatDate 为空字符串时，应返回0
     */
    @Test
    public void testConvertFormatDateToLongTimeEmptyString() throws Throwable {
        // arrange
        String formatDate = "";
        // act
        long result = DateAndTimeUtils.convertFormatDateToLongTime(formatDate);
        // assert
        assertEquals(0, result);
    }

    /**
     * 测试 convertFormatDateToLongTime 方法，当 formatDate 为非空字符串，但无法解析为日期时，应抛出 RuntimeException
     */
    @Test(expected = RuntimeException.class)
    public void testConvertFormatDateToLongTimeInvalidString() throws Throwable {
        // arrange
        String formatDate = "invalid";
        // act
        DateAndTimeUtils.convertFormatDateToLongTime(formatDate);
        // assert is in the annotation
    }
}
