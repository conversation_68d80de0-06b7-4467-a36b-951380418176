package com.sankuai.dzshoppingguide.product.detail.application.fetcher.reserve.dto;

import com.google.common.collect.Lists;
import com.sankuai.dealuser.price.display.api.enums.PromotionExplanatoryTagEnum;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Data
public class PeriodPromoPriceM implements Serializable {

    /**
     * 时段开始时间
     */
    private Long periodStartTime;

    /**
     * 时段优惠最低价
     */
    private BigDecimal periodPromoPrice;

    /**
     * 时段优惠最低价描述
     */
    private String periodPromoPriceDesc;

    /**
     * 时段优惠金额
     */
    private BigDecimal periodPromoAmount;

    /**
     * 优惠的价格标签, 如: 已省80
     */
    private String periodPromoTag;

    /**
     * 优惠短文案，适用于详情页底bar
     */
    private String periodShortPromoTag;

    /**
     * 优惠类型标签
     * 详见 com.sankuai.dealuser.price.display.api.enums.PromoTagTypeEnum
     */
    private int periodPromoTagType;

    /**
     * 计算公式 = 1 - ((basePrice - promoPrice) / basePrice)
     */
    private BigDecimal periodDiscount;

    /**
     * 优惠详情
     */
    private List<PromoItemM> promoItemList;

    /**
     * 总共优惠多少
     */
    private BigDecimal totalPromoPrice;

    /**
     * 优惠icon
     */
    private String periodIcon;

    /**
     * 神券扩展展示信息
     * map中key 枚举类：com.sankuai.dealuser.price.display.api.enums.ExtendDisplayInfoKeyEnum
     */
    private Map<String, String> extendDisplayInfo;

    /**
     * 价格优惠信息集合
     * 对应详情页横幅中的券包
     * key枚举 参考com.sankuai.dealuser.price.display.api.enums.PricePromoInfoTypeEnum
     */
    private Map<Integer, List<PromoItemM>> pricePromoInfoMap;

    private static final List<Integer> MAGIC_MEMBER_ITEM_CODE = Lists.newArrayList(
            PromotionExplanatoryTagEnum.MAGICAL_MEMBER_COUPON.getCode(),
            PromotionExplanatoryTagEnum.MAGICAL_MEMBER_COUPON_FREE.getCode());

    public boolean hitMagicMemberPromo() {
        if (CollectionUtils.isEmpty(this.promoItemList)) {
            return Boolean.FALSE;
        }
        return this.promoItemList.stream().anyMatch(this::containsMagicMemberCouponItem);
    }

    private boolean containsMagicMemberCouponItem(PromoItemM promoItemM) {
        if (CollectionUtils.isEmpty(promoItemM.getPromotionExplanatoryTags())) {
            return Boolean.FALSE;
        }
        List<Integer> explanatoryTagList = promoItemM.getPromotionExplanatoryTags();
        explanatoryTagList.retainAll(MAGIC_MEMBER_ITEM_CODE);
        return CollectionUtils.isNotEmpty(explanatoryTagList);
    }

}
