package com.sankuai.dzshoppingguide.product.detail.application.builder.navbar;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapper;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * Test cases for NavigationBarModuleBuilder.getShopUrl method
 */
@ExtendWith(MockitoExtension.class)
public class NavigationBarModuleBuilderTest {

    @Mock
    private ProductDetailPageRequest request;

    /**
     * Test getShopUrl when client type is MT_APP
     */
    @Test
    public void testGetShopUrl_WhenMTApp_ShouldReturnMTUrl() {
        // arrange
        ProductDetailPageRequest request = mock(ProductDetailPageRequest.class);
        // MT_APP code
        when(request.getClientType()).thenReturn(200);
        long dpShopId = 123L;
        long mtShopId = 456L;
        // act
        String result = NavigationBarModuleBuilder.getShopUrl(request, dpShopId, mtShopId);
        // assert
        assertEquals("imeituan://www.meituan.com/gc/poi/detail?id=456", result);
    }

    /**
     * Test getShopUrl when client type is DP_APP
     */
    @Test
    public void testGetShopUrl_WhenDPApp_ShouldReturnDPUrl() {
        // arrange
        ProductDetailPageRequest request = mock(ProductDetailPageRequest.class);
        // DP_APP code
        when(request.getClientType()).thenReturn(100);
        long dpShopId = 123L;
        long mtShopId = 456L;
        // act
        String result = NavigationBarModuleBuilder.getShopUrl(request, dpShopId, mtShopId);
        // assert
        assertEquals("dianping://shopinfo?shopid=123", result);
    }

    /**
     * Test getShopUrl when client type is unknown
     */
    @Test
    public void testGetShopUrl_WhenUnknownClientType_ShouldReturnNull() {
        // arrange
        ProductDetailPageRequest request = mock(ProductDetailPageRequest.class);
        // Unknown client type
        when(request.getClientType()).thenReturn(999);
        long dpShopId = 123L;
        long mtShopId = 456L;
        // act
        String result = NavigationBarModuleBuilder.getShopUrl(request, dpShopId, mtShopId);
        // assert
        assertNull(result);
    }

    /**
     * Test getHomeUrl when client type is MT_APP
     */
    @Test
    @DisplayName("Should return MT home URL when client type is MT_APP")
    public void testGetHomeUrl_WhenMTApp_ShouldReturnMTHomeUrl() {
        // arrange
        when(request.getClientType()).thenReturn(ClientTypeEnum.MT_APP.getCode());
        // act
        String result = NavigationBarModuleBuilder.getHomeUrl(request);
        // assert
        assertEquals("imeituan://www.meituan.com/home", result);
    }

    /**
     * Test getHomeUrl when client type is DP_APP
     */
    @Test
    @DisplayName("Should return DP home URL when client type is DP_APP")
    public void testGetHomeUrl_WhenDPApp_ShouldReturnDPHomeUrl() {
        // arrange
        when(request.getClientType()).thenReturn(ClientTypeEnum.DP_APP.getCode());
        // act
        String result = NavigationBarModuleBuilder.getHomeUrl(request);
        // assert
        assertEquals("dianping://home", result);
    }

    /**
     * Test getHomeUrl when client type is neither MT_APP nor DP_APP
     */
    @Test
    @DisplayName("Should return null when client type is neither MT_APP nor DP_APP")
    public void testGetHomeUrl_WhenOtherClientType_ShouldReturnNull() {
        // arrange
        when(request.getClientType()).thenReturn(ClientTypeEnum.MT_PC.getCode());
        // act
        String result = NavigationBarModuleBuilder.getHomeUrl(request);
        // assert
        assertNull(result);
    }

    /**
     * Test getHomeUrl when request is null
     */
    @Test
    @DisplayName("Should throw NullPointerException when request is null")
    public void testGetHomeUrl_WhenRequestIsNull_ShouldThrowNullPointerException() {
        // arrange
        ProductDetailPageRequest nullRequest = null;
        // act & assert
        NullPointerException exception = assertThrows(NullPointerException.class, () -> {
            NavigationBarModuleBuilder.getHomeUrl(nullRequest);
        });
        // Additional verification of the exception if needed
        assertNotNull(exception, "NullPointerException should be thrown");
    }

    /**
     * Test getHomeUrl with unknown client type
     */
    @Test
    @DisplayName("Should return null when client type is unknown")
    public void testGetHomeUrl_WhenUnknownClientType_ShouldReturnNull() {
        // arrange
        when(request.getClientType()).thenReturn(ClientTypeEnum.UNKNOWN.getCode());
        // act
        String result = NavigationBarModuleBuilder.getHomeUrl(request);
        // assert
        assertNull(result);
    }

    /**
     * Test getShareUrl for MT_APP client type with valid ShopIdMapper
     */
    @Test
    @DisplayName("MT_APP client type with valid ShopIdMapper should return correct MT share URL")
    public void testGetShareUrl_MtApp_WithValidMapper() {
        // arrange
        when(request.getClientType()).thenReturn(ClientTypeEnum.MT_APP.getCode());
        when(request.getProductId()).thenReturn(12345L);
        ShopIdMapper idMapper = new ShopIdMapper();
        idMapper.setMtBestShopId(67890L);
        // act
        String result = NavigationBarModuleBuilder.getShareUrl(request, idMapper);
        // assert
        String expected = "https://w.dianping.com/cube/evoke/meituan.html?url=imeituan%3A%2F%2Fwww.meituan.com%2Fmrn%3Fmrn_biz%3Dgc%26mrn_entry%3Dmrn-gc-bookdetail%26mrn_component%3Dbookdetail%26poiId%3D67890%26productType%3D2%26productId%3D12345";
        assertEquals(expected, result);
    }

    /**
     * Test getShareUrl for MT_APP client type with null ShopIdMapper
     */
    @Test
    @DisplayName("MT_APP client type with null ShopIdMapper should return URL with poiId=0")
    public void testGetShareUrl_MtApp_WithNullMapper() {
        // arrange
        when(request.getClientType()).thenReturn(ClientTypeEnum.MT_APP.getCode());
        when(request.getProductId()).thenReturn(12345L);
        // act
        String result = NavigationBarModuleBuilder.getShareUrl(request, null);
        // assert
        String expected = "https://w.dianping.com/cube/evoke/meituan.html?url=imeituan%3A%2F%2Fwww.meituan.com%2Fmrn%3Fmrn_biz%3Dgc%26mrn_entry%3Dmrn-gc-bookdetail%26mrn_component%3Dbookdetail%26poiId%3D0%26productType%3D2%26productId%3D12345";
        assertEquals(expected, result);
    }

    /**
     * Test getShareUrl for DP_APP client type with valid ShopIdMapper
     */
    @Test
    @DisplayName("DP_APP client type with valid ShopIdMapper should return correct DP share URL")
    public void testGetShareUrl_DpApp_WithValidMapper() {
        // arrange
        when(request.getClientType()).thenReturn(ClientTypeEnum.DP_APP.getCode());
        when(request.getProductId()).thenReturn(12345L);
        ShopIdMapper idMapper = new ShopIdMapper();
        idMapper.setDpBestShopId(67890L);
        // act
        String result = NavigationBarModuleBuilder.getShareUrl(request, idMapper);
        // assert
        String expected = "https://w.dianping.com/cube/evoke/dianping.html?url=dianping%3A%2F%2Fmrn%3Fmrn_biz%3Dgc%26mrn_entry%3Dmrn-gc-bookdetail%26mrn_component%3Dbookdetail%26poiId%3D67890%26productType%3D2%26productId%3D12345";
        assertEquals(expected, result);
    }

    /**
     * Test getShareUrl for DP_APP client type with null ShopIdMapper
     */
    @Test
    @DisplayName("DP_APP client type with null ShopIdMapper should return URL with poiId=0")
    public void testGetShareUrl_DpApp_WithNullMapper() {
        // arrange
        when(request.getClientType()).thenReturn(ClientTypeEnum.DP_APP.getCode());
        when(request.getProductId()).thenReturn(12345L);
        // act
        String result = NavigationBarModuleBuilder.getShareUrl(request, null);
        // assert
        String expected = "https://w.dianping.com/cube/evoke/dianping.html?url=dianping%3A%2F%2Fmrn%3Fmrn_biz%3Dgc%26mrn_entry%3Dmrn-gc-bookdetail%26mrn_component%3Dbookdetail%26poiId%3D0%26productType%3D2%26productId%3D12345";
        assertEquals(expected, result);
    }

    /**
     * Test getShareUrl for unsupported client type
     */
    @Test
    @DisplayName("Unsupported client type should return null")
    public void testGetShareUrl_UnsupportedClientType() {
        // arrange
        when(request.getClientType()).thenReturn(ClientTypeEnum.UNKNOWN.getCode());
        ShopIdMapper idMapper = new ShopIdMapper();
        // act
        String result = NavigationBarModuleBuilder.getShareUrl(request, idMapper);
        // assert
        assertNull(result);
    }
}
