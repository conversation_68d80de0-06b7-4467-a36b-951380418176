package com.sankuai.dzshoppingguide.product.detail.application.fetcher.tag.edu;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class EduStudyBeforePayBannerTag extends FetcherReturnValueDTO {

    private String text;

    private String url;
}
