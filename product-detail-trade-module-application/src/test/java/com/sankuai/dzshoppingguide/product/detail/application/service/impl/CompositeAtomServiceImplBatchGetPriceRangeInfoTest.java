package com.sankuai.dzshoppingguide.product.detail.application.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.google.common.util.concurrent.SettableFuture;
import com.meituan.service.mobile.mtthrift.netty.ContextStore;
import com.sankuai.tpfun.skuoperationapi.price.dto.request.BatchPriceRangeInfoRequest;
import com.sankuai.tpfun.skuoperationapi.price.dto.response.BatchPriceRangeInfoResponse;
import com.sankuai.tpfun.skuoperationapi.price.dto.response.Response;
import com.sankuai.tpfun.skuoperationapi.price.service.PriceRangeQueryService;
import java.util.concurrent.CompletableFuture;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class CompositeAtomServiceImplBatchGetPriceRangeInfoTest {

    @Mock
    private PriceRangeQueryService priceRangeQueryService;

    @InjectMocks
    private CompositeAtomServiceImpl compositeAtomService;

    @BeforeEach
    void setUp() {
        // Set up SettableFuture in ThreadLocal for each test
        SettableFuture<Response<BatchPriceRangeInfoResponse>> future = SettableFuture.create();
        ContextStore.setFuture(future);
    }

    /**
     * Test successful execution of batchGetPriceRangeInfo
     */
    @Test
    public void testBatchGetPriceRangeInfo_Success() throws Throwable {
        // arrange
        BatchPriceRangeInfoRequest request = new BatchPriceRangeInfoRequest();
        request.setPriceRangeSceneType(1);
        // act
        CompletableFuture<Response<BatchPriceRangeInfoResponse>> result = compositeAtomService.batchGetPriceRangeInfo(request);
        // assert
        assertNotNull(result);
        verify(priceRangeQueryService, times(1)).batchGetPriceRangeInfo(request);
    }

    /**
     * Test batchGetPriceRangeInfo when exception occurs
     */
    @Test
    public void testBatchGetPriceRangeInfo_WhenExceptionOccurs() throws Throwable {
        // arrange
        BatchPriceRangeInfoRequest request = new BatchPriceRangeInfoRequest();
        doThrow(new RuntimeException("Service error")).when(priceRangeQueryService).batchGetPriceRangeInfo(any());
        // act
        CompletableFuture<Response<BatchPriceRangeInfoResponse>> result = compositeAtomService.batchGetPriceRangeInfo(request);
        // assert
        assertNotNull(result);
        verify(priceRangeQueryService, times(1)).batchGetPriceRangeInfo(request);
    }

    /**
     * Test batchGetPriceRangeInfo with null request
     */
    @Test
    public void testBatchGetPriceRangeInfo_WithNullRequest() throws Throwable {
        // arrange
        BatchPriceRangeInfoRequest request = null;
        // act
        CompletableFuture<Response<BatchPriceRangeInfoResponse>> result = compositeAtomService.batchGetPriceRangeInfo(request);
        // assert
        assertNotNull(result);
        verify(priceRangeQueryService, times(1)).batchGetPriceRangeInfo(null);
    }

    /**
     * Test batchGetPriceRangeInfo with empty request fields
     */
    @Test
    public void testBatchGetPriceRangeInfo_WithEmptyRequestFields() throws Throwable {
        // arrange
        BatchPriceRangeInfoRequest request = new BatchPriceRangeInfoRequest();
        // act
        CompletableFuture<Response<BatchPriceRangeInfoResponse>> result = compositeAtomService.batchGetPriceRangeInfo(request);
        // assert
        assertNotNull(result);
        verify(priceRangeQueryService, times(1)).batchGetPriceRangeInfo(request);
    }
}
