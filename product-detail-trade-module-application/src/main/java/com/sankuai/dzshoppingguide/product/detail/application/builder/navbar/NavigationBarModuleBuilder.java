package com.sankuai.dzshoppingguide.product.detail.application.builder.navbar;

import com.dianping.lion.client.Lion;
import com.sankuai.dealuser.price.display.api.enums.PromoIdentityEnum;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.extend.BaseBuilder;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.builder.navbar.handler.NavBarProductTypeHandler;
import com.sankuai.dzshoppingguide.product.detail.application.constants.Constants;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.shopid.ShopIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.shopid.ShopIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.favor.UserFavorStatusFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.favor.UserFavorStatusReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.best.shop.ShopInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.best.shop.ShopInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.ProductCostEffectivePriceFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.ProductNormalPriceFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.dto.ProductPriceReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.FetcherDAGStarter;
import com.sankuai.dzshoppingguide.product.detail.application.model.CustomShareAbleConfig;
import com.sankuai.dzshoppingguide.product.detail.application.utils.*;
import com.sankuai.dzshoppingguide.product.detail.domain.utils.LionConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DetailNavBarStatusInfo;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.NavBarItem;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.NavBarShareModuleVO;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.ShareModuleMiniProgramConfig;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.NavBarItemTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.navbar.vo.NavigationBarVO;
import com.sankuai.general.product.query.center.client.dto.DealGroupBasicDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupImageDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * @Author: <EMAIL>
 * @Date: 2025/2/8
 */
@Builder(
        moduleKey = ModuleKeyConstants.NAVIGATION_BAR,
        startFetcher = FetcherDAGStarter.class,
        dependentFetchers = {
                UserFavorStatusFetcher.class,
                ProductBaseInfoFetcher.class,
                ShopInfoFetcher.class,
                ShopIdMapperFetcher.class,
                ProductCostEffectivePriceFetcher.class,
                ProductNormalPriceFetcher.class
        }
)
public class NavigationBarModuleBuilder extends BaseBuilder<NavigationBarVO> {
    @Resource
    private Map<String, NavBarProductTypeHandler> productTypeHandlerMap;

    @Override
    public NavigationBarVO doBuild() {
        NavigationBarVO navigationBarVO = new NavigationBarVO();

        // 获取依赖数据
        UserFavorStatusReturnValue userFavorStatusReturnValue = getDependencyResult(UserFavorStatusFetcher.class);
        ProductBaseInfo baseInfo = getDependencyResult(ProductBaseInfoFetcher.class);
        ShopIdMapper idMapper = getDependencyResult(ShopIdMapperFetcher.class);
        ShopInfo shopInfo = getDependencyResult(ShopInfoFetcher.class);
        ProductPriceReturnValue costEffectivePrice = getDependencyResult(ProductCostEffectivePriceFetcher.class);
        ProductPriceReturnValue normalPrice = getDependencyResult(ProductNormalPriceFetcher.class);

        // 组装分享信息
        NavBarShareModuleVO navBarShareModuleVO = buildShareModuleVO(baseInfo, idMapper, shopInfo, costEffectivePrice, normalPrice);
        // 组装导航栏静态信息
        List<NavBarItem> navBarItemList = buildNavBarItemList(idMapper);
        // 组装状态信息
        DetailNavBarStatusInfo statusInfo = buildStatusInfo(userFavorStatusReturnValue);

        navigationBarVO.setShare(navBarShareModuleVO);
        navigationBarVO.setNavbar(navBarItemList);
        navigationBarVO.setStatusInfo(statusInfo);
        return navigationBarVO;
    }

    private DetailNavBarStatusInfo buildStatusInfo(UserFavorStatusReturnValue userFavorStatusReturnValue) {
        DetailNavBarStatusInfo statusInfo = new DetailNavBarStatusInfo();
        if (Objects.isNull(userFavorStatusReturnValue))  return statusInfo;
        statusInfo.setFavorStatus(userFavorStatusReturnValue.isFavored() ? 1 : 0);
        return statusInfo;
    }

    private NavBarShareModuleVO buildShareModuleVO(ProductBaseInfo baseInfo, ShopIdMapper idMapper, ShopInfo shopInfo, ProductPriceReturnValue costEffectivePrice, ProductPriceReturnValue normalPrice) {
        boolean hasExclusiveDeduction = hasExclusiveDeduction(normalPrice);
        boolean enableSharableByPageSource = enableSharableByPageSource();
        // 指定渠道 && 渠道专享立减 不可分享
        if (hasExclusiveDeduction || enableSharableByPageSource) {
            return null;
        }

        // 后续如果涉及不可分享场景，直接返回null即可
        if (Objects.isNull(baseInfo)) return null;
        NavBarShareModuleVO navBarShareModuleVO = new NavBarShareModuleVO();
        String title = Optional.ofNullable(baseInfo)
                .map(ProductBaseInfo::getBasic)
                .filter(Objects::nonNull)
                .map(DealGroupBasicDTO::getTitle)
                .orElse(StringUtils.EMPTY);
        String image = Optional.ofNullable(baseInfo)
                .map(ProductBaseInfo::getImage)
                .filter(Objects::nonNull)
                .map(DealGroupImageDTO::getDefaultPicPath)
                .orElse(StringUtils.EMPTY);
        navBarShareModuleVO.setTitle(buildTitle(title, baseInfo, costEffectivePrice));
        navBarShareModuleVO.setDesc(buildDesc(baseInfo));
        navBarShareModuleVO.setImage(buildImage(image));
        navBarShareModuleVO.setUrl(buildShareUrl(request, idMapper, shopInfo));
        navBarShareModuleVO.setMiniProgramConfig(buildMiniProgramConfig(idMapper));
        return navBarShareModuleVO;
    }

    private boolean hasExclusiveDeduction(ProductPriceReturnValue normalPrice) {
        if (Objects.isNull(normalPrice)
                || Objects.isNull(normalPrice.getPriceDisplayDTO())
                || CollectionUtils.isNotEmpty(normalPrice.getPriceDisplayDTO().getUsedPromos())) {
            return false;
        }

        return normalPrice.getPriceDisplayDTO().getUsedPromos().stream()
                .filter(Objects::nonNull)
                .anyMatch(promoDTO -> PromoIdentityEnum.EXCLUSIVE_DEDUCTION.isType(promoDTO.getPromoIdentity()));
    }

    private boolean enableSharableByPageSource() {
        CustomShareAbleConfig customConfig = Lion.getBean(LionConstants.DZTGDETAIL_APPKEY, LionConstants.CUSTOM_PAGESOURCE_SHARE_CONFIG, CustomShareAbleConfig.class);
        // 无配置或者总开关关闭，则不能分享
        if (Objects.isNull(customConfig) || !customConfig.isEnable()) {
            return false;
        }

        // 总开关打开且无定制渠道配置，则默认所有渠道支持分享
        if (CollectionUtils.isEmpty(customConfig.getPageSourceList())) {
            return true;
        }

        if (customConfig.getPageSourceList().contains(request.getPageSource())) {
            return true;
        }
        return false;
    }

    private String buildTitle(String title, ProductBaseInfo baseInfo, ProductPriceReturnValue costEffectivePrice) {
        NavBarProductTypeHandler handler = getNavBarProductTypeHandler();
        return handler.buildTitle(request, title, costEffectivePrice);
    }

    private String buildDesc(ProductBaseInfo baseInfo) {
        NavBarProductTypeHandler handler = getNavBarProductTypeHandler();
        return handler.buildDesc(request, baseInfo);
    }

    private String buildShareUrl(ProductDetailPageRequest request, ShopIdMapper idMapper, ShopInfo shopInfo) {
        NavBarProductTypeHandler handler = getNavBarProductTypeHandler();
        return handler.buildShareUrl(request, idMapper, shopInfo);
    }

    private String buildImage(String image) {
        NavBarProductTypeHandler handler = getNavBarProductTypeHandler();
        return handler.buildImage(image);
    }

    private ShareModuleMiniProgramConfig buildMiniProgramConfig(ShopIdMapper idMapper) {
        NavBarProductTypeHandler handler = getNavBarProductTypeHandler();
        return handler.buildMiniProgramConfig(request, idMapper);
    }

    private List<NavBarItem> buildNavBarItemList(ShopIdMapper idMapper) {
        List<NavBarItem> navBarItems = com.sankuai.dzshoppingguide.product.detail.domain.utils.LionConfigUtils.getNavBarItemList();
        navBarItems.stream()
                .filter(item -> item.getType() == NavBarItemTypeEnum.NAV_BAR_MORE.getCode())
                .filter(Objects::nonNull)
                .flatMap(item -> item.getPopover().stream())
                .forEach(item -> {
                    if (item.getType() == NavBarItemTypeEnum.NAV_BAR_POI.getCode() && Objects.nonNull(idMapper)) {
                        item.setJumpUrl(UrlHelper.getShopUrl(request, idMapper.getDpBestShopId(), idMapper.getMtBestShopId()));
                    }
                    if (item.getType() == NavBarItemTypeEnum.NAV_BAR_HOME_PAGE.getCode()) {
                        item.setJumpUrl(UrlHelper.getHomeUrl(request));
                    }
                });
        return navBarItems;
    }

    private NavBarProductTypeHandler getNavBarProductTypeHandler() {
        String handlerName = StringUtils.EMPTY;
        switch (request.getProductTypeEnum()) {
            case RESERVE:
                handlerName = Constants.NAV_BAR_RESERVE_PRODUCT_HANDLER;
                break;
            case DEAL:
                handlerName = Constants.NAV_BAR_DEAL_PRODUCT_HANDLER;
                break;
        }
        NavBarProductTypeHandler handler = productTypeHandlerMap.get(handlerName);
        if (handler == null) {
            throw new IllegalArgumentException("Unsupported NavBarProductTypeEnum: " + handlerName);
        }
        return handler;
    }

    // 获取商户地址
    public static String getShopUrl(ProductDetailPageRequest request, long dpShopId, long mtShopId) {
        int clientType = request.getClientType();
        if (ClientTypeEnum.MT_APP.getCode() == clientType) {
            return String.format("imeituan://www.meituan.com/gc/poi/detail?id=%s", mtShopId);
        }
        if (ClientTypeEnum.DP_APP.getCode() == clientType) {
            return String.format("dianping://shopinfo?shopid=%s", dpShopId);
        }
        return null;
    }

    // 跳转首页
    public static String getHomeUrl(ProductDetailPageRequest request) {
        int clientType = request.getClientType();
        if (ClientTypeEnum.MT_APP.getCode() == clientType) {
            return String.format("imeituan://www.meituan.com/home");
        }
        if (ClientTypeEnum.DP_APP.getCode() == clientType) {
            return String.format("dianping://home");
        }
        return null;
    }

    public static String getShareUrl(ProductDetailPageRequest request, ShopIdMapper idMapper)  {
        int clientType = request.getClientType();
        if (ClientTypeEnum.MT_APP.getCode() == clientType) {
            long shopId = Objects.nonNull(idMapper) ? idMapper.getMtBestShopId() : 0L;
            return "https://w.dianping.com/cube/evoke/meituan.html?url=imeituan%3A%2F%2Fwww.meituan.com%2Fmrn%3Fmrn_biz%3Dgc%26mrn_entry%3Dmrn-gc-bookdetail%26mrn_component%3Dbookdetail%26poiId%3D" + shopId +  "%26productType%3D2%26productId%3D" + request.getProductId();
        }
        if (ClientTypeEnum.DP_APP.getCode() == clientType) {
            long shopId = Objects.nonNull(idMapper) ? idMapper.getDpBestShopId() : 0L;
            return "https://w.dianping.com/cube/evoke/dianping.html?url=dianping%3A%2F%2Fmrn%3Fmrn_biz%3Dgc%26mrn_entry%3Dmrn-gc-bookdetail%26mrn_component%3Dbookdetail%26poiId%3D" + shopId + "%26productType%3D2%26productId%3D" + request.getProductId();
        }
        return null;
    }
}
