package com.sankuai.dzshoppingguide.product.detail.application.utils;

import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.promo.FreeDealConfig;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.FreeDealEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;

public class FreeDealUtils {

    private static final String FREE_DEAL_INFO_CONFIG_LION_KEY = "free.deal.info.config";
    private static final String FREE_DEAL_INFO_CONFIG_LIST_LION_KEY = "free.deal.category.list";
    private static final String FREE_DEAL_INDEPENDENT_TAB_LIST_LION_KEY = "free.deal.independent.tab.category.list";

    public static FreeDealConfig getFreeDealConfig(FreeDealEnum freeDealType) {
        if (Objects.isNull(freeDealType)) {
            return null;
        }
        String config = Lion.getString(Environment.getAppName(), String.format("%s.%s", FREE_DEAL_INFO_CONFIG_LION_KEY, freeDealType.getType()));
        if (StringUtils.isBlank(config)) {
            return null;
        }
        return JsonUtils.fromJson(config, FreeDealConfig.class);
    }

    // 是否在免费团购白名单（使用二级团单分类）
    public static boolean inCategoryList(Long categoryId) {
        List<Long> whiteList = Lion.getList(Environment.getAppName(), FREE_DEAL_INFO_CONFIG_LIST_LION_KEY, Long.class);
        return !CollectionUtils.isEmpty(whiteList) && whiteList.contains(categoryId);
    }

    public static boolean useIndependentTab(Long categoryId) {
        List<Long> whiteList = Lion.getList(Environment.getAppName(), FREE_DEAL_INDEPENDENT_TAB_LIST_LION_KEY, Long.class);
        return !CollectionUtils.isEmpty(whiteList) && whiteList.contains(categoryId);
    }

}
