package com.sankuai.dzshoppingguide.product.detail.application.fetcher.discountcard;

import com.dianping.cat.util.StringUtils;
import com.dianping.pigeon.remoting.common.codec.json.JacksonUtils;
import com.dianping.pigeon.util.CollectionUtils;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.dzcard.navigation.api.dto.QueryDiscountCardReq;
import com.sankuai.dzcard.navigation.api.enums.LiveStreamEnum;
import com.sankuai.dzshoppingguide.product.detail.application.utils.ParamsUtils;
import org.apache.commons.collections.MapUtils;
import org.junit.Test;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import static org.junit.Assert.*;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/3/3 13:02
 */
public class DiscountCardFetcherTest {
    @Test
    public void test() {
        Map<String, List<String>> map = Maps.newHashMap();
        map.put(LiveStreamEnum.BLACKLIST.getCode(), Lists.newArrayList(LiveStreamEnum.LIVE_STREAMING.getCode()));
        Map<String,String> extMap = Maps.newHashMap();
        extMap.put(LiveStreamEnum.TRAFFICFLAG.getCode(),JsonCodec.encode(map));
        String encode = JsonCodec.encode(extMap);
        System.out.println("encode = " + encode);
        boolean judge = judge(encode);
        System.out.println("judge = " + judge);
    }
    
    private boolean judge(String sourceStr){
        Map<String, String> extMap = Maps.newHashMap();
        if ( org.apache.commons.lang3.StringUtils.isNotEmpty(sourceStr)){
            extMap = JsonCodec.decode(sourceStr, Map.class);
        }
        
        
        if( MapUtils.isEmpty(extMap)){
            //extMap不传值,或者传的值不是直播间的流量标识,那么参考目前线上的逻辑(默认展示折扣卡)
            return true;
        }
        String blackListJson = extMap.get(LiveStreamEnum.TRAFFICFLAG.getCode());

        if( StringUtils.isEmpty(blackListJson)){
            return true;
        }

        Map<String,List<String>> map = JsonCodec.decode(blackListJson, Map.class);
        if( CollectionUtils.isEmpty(map)){
            return true;
        }

        List<String> blackList = map.get(LiveStreamEnum.BLACKLIST.getCode());
        if(CollectionUtils.isEmpty(blackList) || !blackList.contains(LiveStreamEnum.LIVE_STREAMING.getCode())){
            return true;
        }
        return true;
    }
    
    @Test
    public void test2() {
        QueryDiscountCardReq queryDiscountCardReq = buildReq();
        String serialize = JacksonUtils.serialize(queryDiscountCardReq);
        System.out.println("serialize = " + serialize);
    }


    private QueryDiscountCardReq buildReq() {
        QueryDiscountCardReq req = new QueryDiscountCardReq();
        req.setPlatform(2);
        req.setUserId(1910038552);
        req.setUnionId("23a1cdf8d2794ca79a6e52948ea22228a171946598025470584");
        req.setShopId(1055886722215454L);
        req.setDealGroupId(1033783795);
        req.setCityId(10);
        req.setClientType(3);
        // req.setDpId();
        req.setClientVersion("12.27.200");
        // todo
        // req.setVersion();
        return req;
    }
    

}