package com.sankuai.dzshoppingguide.product.detail.spi.similargoods.vo.dtos;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;

@Data
@MobileDo(id = 0x7d1b)
public class DzPictureComponent implements Serializable {
    /**
    * 边框样式类型，0：不带边框，1：丽人S/A大促渐变边框
    */
    @MobileDo.MobileField(key = 0x5f1c)
    private int borderStyle;

    /**
    *
    */
    @MobileDo.MobileField(key = 0x31a3)
    private int picHeight;

    /**
    * 图片URL
    */
    @MobileDo.MobileField(key = 0x7291)
    private String picUrl;

    /**
    * 宽高比
    */
    @MobileDo.MobileField(key = 0x479a)
    private double aspectRadio;
}