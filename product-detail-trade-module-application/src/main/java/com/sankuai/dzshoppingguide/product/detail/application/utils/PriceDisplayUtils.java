package com.sankuai.dzshoppingguide.product.detail.application.utils;

import com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommendgoods.dtos.ProductM;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommendgoods.dtos.ProductPromoPriceM;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.timesdeal.dto.CardM;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.timesdeal.dto.DealDetailBuildContext;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.timesdeal.dto.MerchantMemberProductPromoData;
import com.sankuai.dztheme.deal.res.enums.PromoTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;


/**
 * <AUTHOR>
 * @date 2025/6/14 13:19
 */
@Slf4j
public class PriceDisplayUtils {

    /**
     * 获取团单的到手价
     */
    public static BigDecimal getSalePrice(ProductM productM,DealDetailBuildContext buildContext) {
        try {
            //会员价
            MerchantMemberProductPromoData merchantMemberPromoPriceM = MerchantMemberPromoUtils.getMerchantMemberPromo(productM);
            if(merchantMemberPromoPriceM.getProductPromoPrice() != null && merchantMemberPromoPriceM.getProductPromoPrice().getPromoPrice() != null){
                return merchantMemberPromoPriceM.getProductPromoPrice().getPromoPrice();
            }
            //卡
            CardM cardM = Optional.ofNullable(buildContext).map(DealDetailBuildContext::getCardM).orElse(null);
            ProductPromoPriceM cardPromo = CardPromoUtils.getFirstUserHoldCardPromo(productM.getPromoPrices(), cardM);
            if (cardPromo != null && cardPromo.getPromoPrice() != null) {
                //有卡优惠 且 卡优惠没有倒挂，才展示卡价
                if (!CardPromoUtils.isCardDaoGuaPromo(productM.getPromoPrices())) {
                    return cardPromo.getPromoPrice();
                }
            }
            //玩美季
            BigDecimal perfectActivityPrice = PerfectActivityBuildUtils.getPerfectActivityPromoPrice(productM);
            if (perfectActivityPrice != null && PerfectActivityBuildUtils.isDuringPerfectActivity(productM)) {
                return perfectActivityPrice;
            }
            //立减
            ProductPromoPriceM productPromoPriceM = productM.getPromo(PromoTypeEnum.DIRECT_PROMO.getType());
            if (productPromoPriceM != null && productPromoPriceM.getPromoPrice() != null) {
                return productPromoPriceM.getPromoPrice();
            }
        } catch (Exception e) {
            log.error("getSalePrice error",e);
        }
        return productM.getBasePrice();
    }

    /**
     * 填充团单的到手价
     */
    public static void batchSetSalePrice(List<ProductM> products, DealDetailBuildContext buildContext) {
        if (CollectionUtils.isEmpty(products)) {
            return;
        }
        for (ProductM product : products) {
            product.setSalePrice(getSalePrice(product,buildContext));
        }
    }
}
