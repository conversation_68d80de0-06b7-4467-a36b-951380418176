package com.sankuai.dzshoppingguide.product.detail.spi.recommend.vo;


import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: zhangyuan103
 * @Date: 2025/5/19
 */
@Data
@TypeDoc(description = "item文案信息")
@MobileDo(id = 0xdca7)
public class ItemTextInfo implements Serializable {
    @FieldDoc(description = "文案类型 1-人气精选 2-广告 3-相似好价")
    @MobileDo.MobileField(key = 0x8f0c)
    private int type;

    @FieldDoc(description = "文案")
    @MobileDo.MobileField(key = 0x451b)
    private String text;

    @FieldDoc(description = "icon链接")
    @MobileDo.MobileField(key = 0x3c48)
    private String icon;

    @FieldDoc(description = "广告：代表非像素标 横线：表示像素标-横线，竖线：表示像素标-竖线，圆: 表示像素标-圆形")
    @MobileDo.MobileField(key = 0xbf9b)
    private String tag;

    @FieldDoc(description = "icon长度")
    @MobileDo.MobileField(key = 0x84e8)
    private String length;

    @FieldDoc(description = "icon宽度")
    @MobileDo.MobileField(key = 0x2b78)
    private String width;
}
