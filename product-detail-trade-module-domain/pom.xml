<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.sankuai.dzshoppingguide.detail</groupId>
        <artifactId>product-detail-trade-module</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>product-detail-trade-module-domain</artifactId>
    <version>${revision}</version>
    <packaging>jar</packaging>
    <name>product-detail-trade-module-domain</name>

    <dependencies>
        <dependency>
            <groupId>com.sankuai.dz</groupId>
            <artifactId>product-detail-common</artifactId>
        </dependency>
        <!-- Project module -->
        <dependency>
            <groupId>com.sankuai.dzshoppingguide.detail</groupId>
            <artifactId>product-detail-trade-module-infrastructure</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.dzshoppingguide.detail</groupId>
            <artifactId>product-detail-trade-module-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.map.maf</groupId>
            <artifactId>openplatform-dependency</artifactId>
            <version>1.2.50</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.deal</groupId>
            <artifactId>deal-idmapper-api</artifactId>
            <version>1.0.7</version>
        </dependency>
        <dependency>
            <groupId>org.jetbrains</groupId>
            <artifactId>annotations</artifactId>
            <version>26.0.2</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.sankuai</groupId>
            <artifactId>athena-inf-cache</artifactId>
            <version>0.1.1</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.poi</groupId>
            <artifactId>poi-relation-service-api</artifactId>
            <version>1.1.13</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.general.product</groupId>
            <artifactId>client</artifactId>
            <version>1.0.57</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai</groupId>
            <artifactId>athena-bom</artifactId>
            <version>1.0.1</version>
            <exclusions>
                <exclusion>
                    <artifactId>validation-api</artifactId>
                    <groupId>javax.validation</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>servlet-api</artifactId>
                    <groupId>javax.servlet</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>log4j</artifactId>
                    <groupId>log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.douhu</groupId>
            <artifactId>douhu-absdk</artifactId>
            <version>2.10.5</version>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-junit4</artifactId>
            <!--<version>2.0.0</version>-->
            <version>2.0.7</version>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-api-mockito2</artifactId>
            <version>2.0.0</version>
        </dependency>
    </dependencies>

</project>
