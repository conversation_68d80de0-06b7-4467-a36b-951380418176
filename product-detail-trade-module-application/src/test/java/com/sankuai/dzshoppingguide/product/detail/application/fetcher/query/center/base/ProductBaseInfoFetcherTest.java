package com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.response.FetcherResponse;
import com.sankuai.dz.product.detail.gateway.spi.enums.ProductTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.QueryCenterAggregateReturnValue;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test cases for the fulfillRequest method in ProductBaseInfoFetcher.
 */
public class ProductBaseInfoFetcherTest {

    private TestProductBaseInfoFetcher fetcher;

    private ProductDetailPageRequest mockRequest;

    @BeforeEach
    void setUp() {
        fetcher = new TestProductBaseInfoFetcher();
        // Create and setup mock request
        mockRequest = Mockito.mock(ProductDetailPageRequest.class);
        Mockito.when(mockRequest.getProductTypeEnum()).thenReturn(ProductTypeEnum.DEAL);
        // Set the mock request using the test-specific method
        fetcher.setTestRequest(mockRequest);
    }

    /**
     * Test that a NullPointerException is thrown when the request builder is null.
     */
    @Test
    public void testFulfillRequestNullBuilder() throws Throwable {
        // arrange
        ProductBaseInfoFetcher fetcher = new ProductBaseInfoFetcher();
        // act & assert
        assertThrows(NullPointerException.class, () -> fetcher.fulfillRequest(null));
    }

    // Test-specific subclass to access protected field
    private static class TestProductBaseInfoFetcher extends ProductBaseInfoFetcher {

        public void setTestRequest(ProductDetailPageRequest request) {
            this.request = request;
        }
    }

    /**
     * Test mapResult when aggregateResult is null.
     */
    @Test
    public void testMapResult_WithNullAggregateResult() throws Throwable {
        FetcherResponse<QueryCenterAggregateReturnValue> aggregateResult = null;
        FetcherResponse<ProductBaseInfo> result = fetcher.mapResult(aggregateResult);
        assertNull(result.getReturnValue());
        assertEquals(true, result.isSuccess());
    }

    /**
     * Test mapResult when aggregateResult has null returnValue.
     */
    @Test
    public void testMapResult_WithNullReturnValue() throws Throwable {
        FetcherResponse<QueryCenterAggregateReturnValue> aggregateResult = FetcherResponse.succeed(null);
        FetcherResponse<ProductBaseInfo> result = fetcher.mapResult(aggregateResult);
        assertNull(result.getReturnValue());
        assertEquals(true, result.isSuccess());
    }

    /**
     * Test mapResult when aggregateResult has returnValue but DealGroupDTO is null.
     */
    @Test
    public void testMapResult_WithNullDealGroupDTO() throws Throwable {
        QueryCenterAggregateReturnValue returnValue = new QueryCenterAggregateReturnValue();
        returnValue.setDealGroupDTO(null);
        FetcherResponse<QueryCenterAggregateReturnValue> aggregateResult = FetcherResponse.succeed(returnValue);
        FetcherResponse<ProductBaseInfo> result = fetcher.mapResult(aggregateResult);
        assertNull(result.getReturnValue());
        assertEquals(true, result.isSuccess());
    }

    /**
     * Test mapResult when aggregateResult has returnValue with valid DealGroupDTO.
     */
    @Test
    public void testMapResult_WithValidDealGroupDTO() throws Throwable {
        // Prepare test data
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setDpDealGroupId(12345L);
        dealGroupDTO.setMtDealGroupId(67890L);
        QueryCenterAggregateReturnValue returnValue = new QueryCenterAggregateReturnValue();
        returnValue.setDealGroupDTO(dealGroupDTO);
        FetcherResponse<QueryCenterAggregateReturnValue> aggregateResult = FetcherResponse.succeed(returnValue);
        // Execute test
        FetcherResponse<ProductBaseInfo> result = fetcher.mapResult(aggregateResult);
        // Verify results
        ProductBaseInfo productBaseInfo = result.getReturnValue();
        assertEquals(12345L, productBaseInfo.getProductIdDTO().getDpProductId());
        assertEquals(67890L, productBaseInfo.getProductIdDTO().getMtProductId());
        assertEquals(dealGroupDTO.getBasic(), productBaseInfo.getBasic());
        assertEquals(dealGroupDTO.getImage(), productBaseInfo.getImage());
        assertEquals(dealGroupDTO.getBgBu(), productBaseInfo.getBgBu());
        assertEquals(dealGroupDTO.getChannel(), productBaseInfo.getChannel());
        assertEquals(dealGroupDTO.getRule(), productBaseInfo.getRule());
        assertEquals(dealGroupDTO.getDisplayShopInfo(), productBaseInfo.getDisplayShopInfo());
        assertEquals(dealGroupDTO.getTags(), productBaseInfo.getTags());
        assertEquals(dealGroupDTO.getPrice(), productBaseInfo.getPrice());
        assertEquals(dealGroupDTO.getSaleChannelAggregation(), productBaseInfo.getSaleChannelAggregation());
        assertEquals(dealGroupDTO.getDeals(), productBaseInfo.getDeals());
        assertEquals(dealGroupDTO.getCategory(), productBaseInfo.getCategory());
        // assertEquals(dealGroupDTO.getAttrs(), productBaseInfo.getAttrs());
        assertEquals(true, result.isSuccess());
    }
}
