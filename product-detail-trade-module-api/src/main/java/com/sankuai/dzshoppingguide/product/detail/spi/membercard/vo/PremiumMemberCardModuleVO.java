package com.sankuai.dzshoppingguide.product.detail.spi.membercard.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/2/26 22:24
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TypeDoc(description = "付费商家会员卡")
@MobileDo(id = 0x9a69)
public class PremiumMemberCardModuleVO extends BasicCardModuleVO{
    @Override
    public String getModuleKey() {
        return ModuleKeyConstants.MEMBER_CARD;
    }

    @FieldDoc(description = "会员价")
    @MobileDo.MobileField(key = 0xb2e7)
    private String dealPriceWithPromo;

    @FieldDoc(description = "非会员价")
    @MobileDo.MobileField(key = 0x8a6c)
    private String dealPriceWithNormal;
}
