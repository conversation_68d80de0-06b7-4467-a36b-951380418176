package com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.dto;

import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;


/**
 * <AUTHOR>
 * @date 2025-03-04
 * @desc 商品价格返回值
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class ProductPriceReturnValue extends FetcherReturnValueDTO {

    /**
     * 最终价格模型
     * 比价后价格较低的价格模型
     */
    private PriceDisplayDTO priceDisplayDTO;

    /**
     * 是否使用的是会员价
     */
    private boolean isMemberPromoPrice;

    /**
     * 原价价格模型
     * 比价后价格较高的价格模型
     */
    private PriceDisplayDTO originPriceDisplayDTO;

    /**
     * 价格密文,用于计算价格一致率
     */
    private String priceSecretInfo;

    public ProductPriceReturnValue(PriceDisplayDTO priceDisplayDTO) {
        this.priceDisplayDTO = priceDisplayDTO;
    }

    public ProductPriceReturnValue(LocalPriceDisplayDTO localPriceDisplayDTO) {
        if (localPriceDisplayDTO == null) {
            return;
        }
        this.priceDisplayDTO = localPriceDisplayDTO.getPriceDisplayDTO();
        this.priceSecretInfo = localPriceDisplayDTO.getPriceSecretInfo();
    }

    public PriceDisplayDTO getFinalPriceDisplayDTO() {
        // 如果发生了比价，则使用比价后的，如果未发生比价，则使用原始价格模型
        return isMemberPromoPrice ? priceDisplayDTO : originPriceDisplayDTO;
    }
}
