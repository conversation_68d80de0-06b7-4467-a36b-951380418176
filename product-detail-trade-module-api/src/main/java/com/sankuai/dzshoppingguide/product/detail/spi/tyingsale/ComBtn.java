package com.sankuai.dzshoppingguide.product.detail.spi.tyingsale;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;

import java.io.Serializable;

@MobileDo(id = 0xd98)
public class ComBtn implements Serializable {
    /**
     * 0仅展示；1点击跳转 ；具体指以后端枚举为准
     */
    @MobileField(key = 0xb783)
    private int actionType;

    /**
     * 跳转地址
     */
    @MobileField(key = 0xa706)
    private String clickUrl;

    /**
     * 按钮标题
     */
    @MobileField(key = 0x36e9)
    private String title;

}
