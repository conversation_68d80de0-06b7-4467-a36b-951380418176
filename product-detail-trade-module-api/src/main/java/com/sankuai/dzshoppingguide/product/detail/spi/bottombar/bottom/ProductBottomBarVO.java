package com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom;

import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.component.trade.button.BaseTradeButtonVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.enums.BottomBarStyleEnum;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: guangyujie
 * @Date: 2025/3/16 14:07
 */
@Data
public abstract class ProductBottomBarVO implements Serializable {

    /**
     * 查询底bar所有交易按钮的操作类型
     */
    public abstract List<BaseTradeButtonVO> queryAllTradeButtons();

    /**
     * buyBar类型
     *
     * @see BottomBarStyleEnum
     */
    public abstract int getBottomBarStyle();

}
