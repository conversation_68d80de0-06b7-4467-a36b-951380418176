package com.sankuai.dzshoppingguide.product.detail.application.fetcher.favor;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * @Author: <EMAIL>
 * @Date: 2025/2/10
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserFavorStatusReturnValue extends FetcherReturnValueDTO {
    private boolean favored;
}