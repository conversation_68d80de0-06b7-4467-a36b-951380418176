package com.sankuai.dzshoppingguide.product.detail.spi.tyingsale;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.sankuai.dz.product.detail.gateway.spi.vo.AbstractModuleVO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

import static com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants.MODULE_DETAIL_DEAL_TYING_SALE;

@MobileDo(id = 0x77fc)
@Data
public class BuyMoreSaveMoreVO extends AbstractModuleVO implements Serializable {
    /**
     * 实验数据
     */
    @MobileField(key = 0xddc3)
    private String expBiInfo;

    /**
     * 列表页入口文案
     */
    @MobileField(key = 0xb23a)
    private String floatLayerEntranceTitle;

    /**
     * 浮层整体名称
     */
    @MobileField(key = 0x9415)
    private String floatLayerTitle;

    /**
     * 列表页跳转链接
     */
    @MobileField(key = 0xbc0)
    private String floatLayerJumpUrl;

    /**
     * 团单搭售模块名
     */
    @MobileField(key = 0x6f1f)
    private String dealModuleTitle;

    /**
     * 搭售卡片列表信息
     */
    @MobileField(key = 0x68b0)
    private List<BuyMoreSaveMoreCardVO> cardList;

    @MobileDo.MobileField(key = 0xf0b)
    private boolean isEnd;

    @Override
    public String getModuleKey() {
        return MODULE_DETAIL_DEAL_TYING_SALE;
    }
}