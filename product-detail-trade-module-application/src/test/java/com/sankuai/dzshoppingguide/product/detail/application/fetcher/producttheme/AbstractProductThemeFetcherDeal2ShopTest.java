package com.sankuai.dzshoppingguide.product.detail.application.fetcher.producttheme;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommendgoods.ProductRecommendResult;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommendgoods.dtos.ProductM;
import com.sankuai.dztheme.deal.dto.DealProductDTO;
import com.sankuai.dztheme.deal.req.DealProductRequest;
import com.sankuai.dztheme.deal.res.DealProductResult;
import java.util.*;
import java.util.concurrent.CompletableFuture;

import org.apache.commons.collections.MapUtils;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

/**
 * Unit tests for AbstractProductThemeFetcher.deal2Shop(List<Integer> dealIds)
 */
public class AbstractProductThemeFetcherDeal2ShopTest {

    /**
     * Helper concrete class for testing the abstract class
     */
    static class TestProductThemeFetcher extends AbstractProductThemeFetcher {

        @Override
        boolean enableNewService() {
            return false;
        }

        @Override
        DealProductRequest buildDealProductRequest(List<ProductM> products, Map<Long, Long> dealId2ShopId) {
            return null;
        }


        @Override
        protected CompletableFuture<ProductThemeResult> doFetch() {
            return null;
        }

        @Override
        protected void paddingWithDealProductResult(ProductRecommendResult productGroupM, DealProductResult dealProductResult) {
        }

        @Override
        public List<Integer> flexibleGettingDealIds(List<ProductM> products) {
            return null;
        }


        @Override
        protected void paddingProductM(ProductM productM, DealProductDTO dealProductDTO) {
        }

        // Add setter method for testing
        public void setTestRequest(ProductDetailPageRequest request) {
            this.request = request;
        }
    }

    /**
     * Test when dealIds is null, should return empty map
     */
    @Test
    public void testDeal2Shop_NullDealIds_ReturnsEmptyMap() throws Throwable {
        // arrange
        TestProductThemeFetcher fetcher = new TestProductThemeFetcher();
        // act
        Map<Integer, Integer> result = fetcher.deal2Shop(null, MapUtils.EMPTY_MAP);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test when dealIds is empty, should return empty map
     */
    @Test
    public void testDeal2Shop_EmptyDealIds_ReturnsEmptyMap() throws Throwable {
        // arrange
        TestProductThemeFetcher fetcher = new TestProductThemeFetcher();
        // act
        Map<Integer, Integer> result = fetcher.deal2Shop(Collections.emptyList(), MapUtils.EMPTY_MAP);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test when client type is MT, should use getMtBestShopId for all dealIds
     */
    @Test
    public void testDeal2Shop_MtClientType_UsesMtBestShopId() throws Throwable {
        // arrange
        TestProductThemeFetcher fetcher = new TestProductThemeFetcher();
        // Mock request
        ProductDetailPageRequest request = mock(ProductDetailPageRequest.class);
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.MT_APP);
        ShopIdMapper shopIdMapper = new ShopIdMapper();
        long mtBestShopId = 12345L;
        shopIdMapper.setMtBestShopId(mtBestShopId);
        shopIdMapper.setDpBestShopId(54321L);
        fetcher.setTestRequest(request);
        fetcher.shopIdMapper = shopIdMapper;
        List<Integer> dealIds = Arrays.asList(1, 2, 3);
        // act
        Map<Integer, Integer> result = fetcher.deal2Shop(dealIds, MapUtils.EMPTY_MAP);
        // assert
        assertNotNull(result);
        assertEquals(0, result.size());

    }

    /**
     * Test when client type is DP, should use getDpBestShopId for all dealIds
     */
    @Test
    public void testDeal2Shop_DpClientType_UsesDpBestShopId() throws Throwable {
        // arrange
        TestProductThemeFetcher fetcher = new TestProductThemeFetcher();
        // Mock request
        ProductDetailPageRequest request = mock(ProductDetailPageRequest.class);
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.DP_APP);
        ShopIdMapper shopIdMapper = new ShopIdMapper();
        long mtBestShopId = 12345L;
        long dpBestShopId = 54321L;
        shopIdMapper.setMtBestShopId(mtBestShopId);
        shopIdMapper.setDpBestShopId(dpBestShopId);
        fetcher.setTestRequest(request);
        fetcher.shopIdMapper = shopIdMapper;
        List<Integer> dealIds = Arrays.asList(10, 20);
        // act
        Map<Integer, Integer> result = fetcher.deal2Shop(dealIds, MapUtils.EMPTY_MAP);
        // assert
        assertNotNull(result);
        assertEquals(0, result.size());
    }

    /**
     * Test when dealIds contains duplicate values, map should contain unique keys
     */
    @Test
    public void testDeal2Shop_DuplicateDealIds_OnlyUniqueKeys() throws Throwable {
        // arrange
        TestProductThemeFetcher fetcher = new TestProductThemeFetcher();
        ProductDetailPageRequest request = mock(ProductDetailPageRequest.class);
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.MT_APP);
        ShopIdMapper shopIdMapper = new ShopIdMapper();
        long mtBestShopId = 11111L;
        shopIdMapper.setMtBestShopId(mtBestShopId);
        shopIdMapper.setDpBestShopId(22222L);
        fetcher.setTestRequest(request);
        fetcher.shopIdMapper = shopIdMapper;
        List<Integer> dealIds = Arrays.asList(5, 5, 6);
        // act
        Map<Long, Long> dealId2ShopId = new HashMap<>();
        dealId2ShopId.put(1L, 2L);
        dealId2ShopId.put(2L, 3L);
        Map<Integer, Integer> result = fetcher.deal2Shop(dealIds, dealId2ShopId);
        // assert
        assertNotNull(result);
        assertEquals(3, result.size());
    }

}
