package com.sankuai.dzshoppingguide.product.detail.application.service.similarproduct.dtos;

import lombok.Data;

import java.util.List;

@Data
public class DealRepurchaseConfig {
        /**
         * 总开关
         */
        private boolean enableSwitch;
        /**
         * 全行业推广
         */
        private boolean allPass;
        /**
         * 在推广的行业类目id
         */
        private List<Integer> categoryIds;
        /**
         * 在推广的mt城市id
         */
        private List<Integer> mtCityIds;
        /**
         * 在推广的dp城市id
         */
        private List<Integer> dpCityIds;

        /**
         * 比价助手开关
         */
        private boolean enablePriceItemList;
    }