package com.sankuai.dzshoppingguide.product.detail.application.fetcher.tying;

import com.alibaba.fastjson.JSON;
import com.dianping.lion.client.Lion;
import com.dianping.martgeneral.recommend.api.entity.RecommendParameters;
import com.dianping.martgeneral.recommend.api.entity.RecommendResult;
import com.dianping.martgeneral.recommend.api.entity.Response;
import com.dianping.martgeneral.recommend.api.enums.PlatformEnum;
import com.google.common.collect.Lists;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dz.product.detail.RequestCustomParamEnum;
import com.sankuai.dz.product.detail.gateway.spi.enums.MobileOSTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.abtest.AbTestFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.abtest.AbTestReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.cityId.CityIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.dealgroup.DealGroupIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
import com.sankuai.dzshoppingguide.product.detail.application.utils.GsonUtils;
import com.sankuai.dzshoppingguide.product.detail.application.utils.JsonUtils;
import com.sankuai.dzshoppingguide.product.detail.application.utils.LionConfigUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.RequestSourceEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.TyingSourceEnum;
import com.sankuai.mktplay.center.mkt.play.center.client.PlayExecuteRequest;
import com.sankuai.mktplay.center.mkt.play.center.client.PlayExecuteResponse;
import com.sankuai.mktplay.center.mkt.play.center.client.UserInfo;
import com.sankuai.mktplay.center.mkt.play.center.client.UserSourceEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import static com.sankuai.dzshoppingguide.product.detail.domain.utils.LionConstants.DZTGDETAIL_APPKEY;


@Fetcher(previousLayerDependencies = {
        DealGroupIdMapperFetcher.class,
        ShopIdMapperFetcher.class,
        CityIdMapperFetcher.class,
        AbTestFetcher.class}
)
@Slf4j
public class TyingRecallFetcher extends NormalFetcherContext<TyingRecallResult> {

    @Autowired
    private CompositeAtomService compositeAtomService;

    private static final List<String> EXP_PASS_RESULT = Lists.newArrayList("c", "e");

    @Override
    protected CompletableFuture<TyingRecallResult> doFetch() throws Exception {
        // 减少无效请求，做客户端校验+实验逻辑 || 非多买多省白名单渠道
        if (!request.getClientTypeEnum().isInApp() || !isWhiteTrafficFlag()) {
            return CompletableFuture.completedFuture(null);
        }
        AbTestReturnValue abTestReturnValue = getDependencyResult(AbTestFetcher.class);
        String expModuleName = request.getClientTypeEnum().isMtClientType() ? "jointBuyUnify_MT" : "jointBuyUnify_DP";
        String result = abTestReturnValue.getAbTestExpResult(expModuleName);
        if (StringUtils.isBlank(result) || !EXP_PASS_RESULT.contains(result)) {
            return CompletableFuture.completedFuture(null);
        }

        CompletableFuture<PlayExecuteResponse> playActivityCf = getRecommendCombineDealV2(1, getPageSize());
        CompletableFuture<Response<RecommendResult>> recommendCf = getRecommendCombineDealV3();
        return CompletableFuture.allOf(playActivityCf, recommendCf).thenApply(a -> new TyingRecallResult(playActivityCf.join(), recommendCf.join()));
    }

    private boolean isWhiteTrafficFlag() {
        Map<String, String> pageSource2OrderTrafficFlag = LionConfigUtils.getSourceToTrafficFlagMap();
        String trafficFlag = pageSource2OrderTrafficFlag.get(request.getPageSource());
        return StringUtils.isBlank(trafficFlag) || LionConfigUtils.getBuyMoreTrafficFlagWhiteList().contains(trafficFlag);
    }

    private int getPageSize() {
        String limitStr = request.getCustomParam(RequestCustomParamEnum.tyingSaleLimit);
        if (StringUtils.isNotBlank(limitStr) && NumberUtils.isDigits(limitStr)) {
           return NumberUtils.toInt(limitStr);
        }
        String sourceType = request.getCustomParam(RequestCustomParamEnum.tyingSaleSourceType);
        if (StringUtils.isNotBlank(sourceType) && NumberUtils.isDigits(sourceType)) {
            return NumberUtils.toInt(sourceType) == TyingSourceEnum.DEAL_MODULE.getValue() ? 2 : 10;
        }
        return 2;
    }

    private CompletableFuture<PlayExecuteResponse> getRecommendCombineDealV2(int pageNo, int pageSize) {
        PlayExecuteRequest playExecuteRequest = new PlayExecuteRequest();
        playExecuteRequest.setPlayId(LionConfigUtils.getPlayId());
        playExecuteRequest.setUserInfo(buildUserInfo());
        playExecuteRequest.setRequest(buildRequest(pageNo, pageSize));
        try {
            return compositeAtomService.queryExecutePlay(playExecuteRequest);
        } catch (TException e) {
            log.error("PlayCenterService executePlay error!, request = " + GsonUtils.toJsonString(playExecuteRequest), e);
        }
        return CompletableFuture.completedFuture(null);
    }

    private UserInfo buildUserInfo() {
        UserInfo userInfo = new UserInfo();
        userInfo.setUserId(request.getUserId());
        userInfo.setUserSource(request.getClientTypeEnum().isMtClientType() ? UserSourceEnum.MT.getValue() : UserSourceEnum.DP.getValue());
        return userInfo;
    }

    private Map<String, String> buildRequest(int pageNo, int pageSize) {

        Map<String, String> request = new HashMap<>();
        // 527写死
        request.put("bizId", "527");
        boolean isMt = this.request.getClientTypeEnum().isMtClientType();
        //1-美团APP；2-点评APP；5-美团微信小程序
        request.put("platformType", isMt ? "1" : "2");
        // 业务线 3-到餐 7-到综
        request.put("productType", "7");
        //0 ios 1安卓
        request.put("osType", this.request.getMobileOSType() == MobileOSTypeEnum.IOS ? "0" : "1");
        // mainChannel:主渠道，costEffective:特团渠道
        request.put("locationCode", "mainChannel");
        if (fromCostEffective()){
            request.put("locationCode", "costEffective");
        }
        request.put("cityId", String.valueOf(this.request.getCityId()));
        if (!isMt){
            request.put("dpCityId", String.valueOf(this.request.getCityId()));
        }
        request.put("lat", String.valueOf(this.request.getUserLat()));
        request.put("lng", String.valueOf(this.request.getUserLng()));
        request.put("pageNumber", String.valueOf(pageNo));
        request.put("pageSize", String.valueOf(pageSize));

        request.put("bizParams", JSON.toJSONString(buildBizParam()));
        return request;
    }

    private Map<String, String> buildBizParam(){
        Map<String, String> bizParams = new HashMap<>();
        // 流量标记 001 - 综团提单页；002 - 餐团提单页  003 -新综团提单页  004-新餐团提单页 005-团详
        bizParams.put("flowFlag", "005");
        bizParams.put("shopId", String.valueOf(request.getPoiId()));
        // 待组合商品id列表 ；商品类型（1001： 综侧团单，3005：综侧预订，3007：综侧预付，9999：餐侧商品）
        //事例：123_1001,234_3007,205_9999
        bizParams.put("productList", request.getProductId() + "_1001");
        // 1 - 同店、0 - 跨店,见红色部分  2同店&跨店
        bizParams.put("isSameShop", "1");
        return bizParams;
    }

    private boolean fromCostEffective(){
        return RequestSourceEnum.COST_EFFECTIVE.getSource().equals(request.getPageSource());
    }

    /**
     * 算法搭售
     * https://km.sankuai.com/collabpage/2551440458
     * @return
     */
    private CompletableFuture<Response<RecommendResult>> getRecommendCombineDealV3() throws TException {
        RecommendParameters recommendParameters = new RecommendParameters();
        recommendParameters.setBizId(527); //商品组合id推荐
        recommendParameters.setCityId(request.getCityId()); //美团城市id，美团传美团城市ID，点评传点评城市ID
        recommendParameters.setOriginUserId(String.valueOf(request.getUserId())); // 用户Id字符串，缺失传入null，待确认，美团端：美团实；点评端：点评实
        if (request.getClientTypeEnum().isMtClientType()) {
            recommendParameters.setUuid(request.getShepherdGatewayParam().getDeviceId()); //美团侧需要uuid
            recommendParameters.setPlatformEnum(PlatformEnum.MT);
        } else {
            recommendParameters.setDpid(request.getShepherdGatewayParam().getDeviceId()); //点评侧需要dpid
            recommendParameters.setPlatformEnum(PlatformEnum.DP);
        }
        recommendParameters.setLat(request.getUserLat()); // 纬度，真实定位，火星坐标系
        recommendParameters.setLng(request.getUserLng()); // 经度
        recommendParameters.setPageNumber(1); // 当前页
        recommendParameters.setPageSize(10); // 分页大小

        Map<String, Object> bizParams = new HashMap<>();
        // 流量标记: 003 - 综团提单页；004 - 餐团提单页 005 - 团详页
        bizParams.put("flowFlag", "005");
        bizParams.put("isSameShop", "1"); // 1 - 同店、0 - 跨店、2 - 同店和跨店，团详传1，交易填单传2
        // 限制商户（必传）
        bizParams.put("shopId", String.valueOf(request.getPoiId()));
        // 待组合商品id列表 ；商品类型（1001： 综侧团单，3005：综侧预订，3007：综侧预付，9999：餐侧商品，9998：标品）
        bizParams.put("productList", String.format("%d_1001", request.getProductId()));
        bizParams.put("expVersion", "tuanxiangye_01"); // 001 - 老版搭售方案； 002 -新版搭售方案+同店； 003 -新版搭售方案+跨店； 004 -新版搭售方案+同店跨店，交易透传 提单页传斗斛实验ID，团详页传 tuanxiangye_01

        recommendParameters.setBizParams(bizParams);

        log.info("RecommendServiceWrapper.getFromRecommend, recommendParameters={}", JsonUtils.toJson(recommendParameters));
        return compositeAtomService.recommend(recommendParameters);
    }

}