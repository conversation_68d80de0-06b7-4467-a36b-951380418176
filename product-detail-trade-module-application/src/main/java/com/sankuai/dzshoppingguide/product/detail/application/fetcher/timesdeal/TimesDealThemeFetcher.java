package com.sankuai.dzshoppingguide.product.detail.application.fetcher.timesdeal;

import com.dianping.vc.enums.VCClientTypeEnum;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dz.product.detail.gateway.spi.enums.MobileOSTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.enums.ProductTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.constants.PmfConstants;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategoryFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.cityId.CityIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.cityId.CityIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.best.shop.ShopInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.best.shop.ShopInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommendgoods.dtos.*;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.reserve.dto.PromoItemTextM;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.timesdeal.dto.OrderUserM;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.timesdeal.dto.TagM;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.timesdeal.dto.TimesDealRelation;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.timesdeal.dto.TimesDealTheme;
import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
import com.sankuai.dzshoppingguide.product.detail.application.utils.TimesDealUtil;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dztheme.deal.dto.*;
import com.sankuai.dztheme.deal.dto.enums.ThemeReqSourceEnum;
import com.sankuai.dztheme.deal.req.DealProductRequest;
import com.sankuai.dztheme.deal.res.DealProductResult;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.sinai.data.api.dto.MtPoiDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/14 13:19
 */
@Fetcher(
        previousLayerDependencies = {
                TimesDealRelationFetcher.class, 
                ProductCategoryFetcher.class,
                CityIdMapperFetcher.class,
                ShopIdMapperFetcher.class,
                ShopInfoFetcher.class
        },
        timeout = 500000L
)
@Slf4j
public class TimesDealThemeFetcher extends NormalFetcherContext<TimesDealTheme> {
    
    private static final String DEFAULT_PLAN_ID = "10002550";

    private List<Long> productIds;

    @Autowired
    private CompositeAtomService compositeAtomService;
    
    private CityIdMapper cityIdMapper;
    private ProductCategory productCategory;
    private ShopIdMapper shopIdMapper;
    private ShopInfo shopInfo;

    @Override
    protected CompletableFuture<TimesDealTheme> doFetch() throws Exception {
        if (request.getProductTypeEnum() != ProductTypeEnum.DEAL) {
            return CompletableFuture.completedFuture(null);
        }

        List<String> antiRefreshModuleList = TimesDealUtil.getAntiRefreshModuleList(request);
        if (CollectionUtils.isNotEmpty(antiRefreshModuleList) && antiRefreshModuleList.contains(ModuleKeyConstants.MODULE_DETAIL_DEAL_TIME_CARD)) {
            return CompletableFuture.completedFuture(null);
        }

        TimesDealRelation timesDealRelation = getDependencyResult(TimesDealRelationFetcher.class);
        if (timesDealRelation == null || CollectionUtils.isEmpty(timesDealRelation.getCombineDealIds())) {
            return CompletableFuture.completedFuture(null);
        }

        cityIdMapper = getDependencyResult(CityIdMapperFetcher.class);
        productCategory = getDependencyResult(ProductCategoryFetcher.class);
        shopIdMapper = getDependencyResult(ShopIdMapperFetcher.class);
        shopInfo = getDependencyResult(ShopInfoFetcher.class);

        productIds = Lists.newArrayList(timesDealRelation.getCombineDealIds());

        // 调用主题
        CompletableFuture<List<DealProductResult>> dealProductResultListFuture = batchQueryProductTheme();
        return dealProductResultListFuture.thenApply(dealProductResultList -> {
            List<ProductM> itemVOS = dealProductResultList.stream()
                    .filter(Objects::nonNull)
                    .map(this::convertDealProductDTOS)
                    .filter(CollectionUtils::isNotEmpty)
                    .flatMap(Collection::stream)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            return TimesDealTheme.builder().productMS(itemVOS).build();
        });
    }

    private List<ProductM> convertDealProductDTOS(DealProductResult dealProductResult) {
        if (dealProductResult == null || org.apache.commons.collections.CollectionUtils.isEmpty(dealProductResult.getDeals())) {
            return Collections.emptyList();
        }
        List<DealProductDTO> dealProductDTOs = dealProductResult.getDeals();

        return dealProductDTOs.stream().filter(Objects::nonNull).map(this::convertDealProductDTO).filter(Objects::nonNull).collect(Collectors.toList());
    }

    private ProductM convertDealProductDTO(DealProductDTO dealProductDTO) {
        if (dealProductDTO == null) {
            // miss
            return null;
        }
        ProductM productM = new ProductM();
        // 1. 团单ID
        productM.setProductId(dealProductDTO.getProductId());
        // 2. 团单标题
        productM.setTitle(dealProductDTO.getName());
        // 3. 团单详情跳转URL
        productM.setJumpUrl(dealProductDTO.getDetailUr());
        // 4. 团单销量
        productM.setSale(buildProductSale(dealProductDTO.getSale()));
        // 5. 团单售卖价格标签
        productM.setBasePriceTag(dealProductDTO.getBasePriceTag());
        // 6. 团单售卖价格
        productM.setBasePriceTag(dealProductDTO.getBasePriceTag());
        // 7. 团单市场价格
        productM.setMarketPrice(dealProductDTO.getMarketPriceTag());
        // 8. 融合优惠啊
        productM.setPromoPrices(buildDealPromoPrice(productM, dealProductDTO.getPromoPrices()));
        // 9. 团单购买信息
        productM.setPurchase(dealProductDTO.getPurchase());
        // 10. 团单拼团标签
        productM.setPinPrice(buildProductPriceM(dealProductDTO.getPinPrice()));
        // 11. 团单次卡标签
        productM.setCardPrice(buildProductPriceM(dealProductDTO.getCardPrice()));
        // 12. 团单跳转链接
        productM.setJumpUrl(dealProductDTO.getDetailUr());
        // 13. 团单标准商品标签
        productM.setProductTags(dealProductDTO.getProductTags());
        // 13. 团单商品头图
        productM.setPicUrl(dealProductDTO.getHeadPic());
        // 14. 团单扩展属性
        paddingProductMAttrs(dealProductDTO, productM);
        // 15. 关联商户信息
        productM.setShopMs(buildShopMs(productM, dealProductDTO));
        // 16、消费返券
        productM.setCoupons(buildCoupons(dealProductDTO.getCoupons()));
        //17.商品原始售卖价格
        productM.setBasePrice(dealProductDTO.getBasePrice());
        //18.商品类目
        productM.setCategoryId(dealProductDTO.getCategoryId());
        productM.setCategoryName(dealProductDTO.getCategory());
        //19.商品活动
        productM.setActivities(buildDealActivities(dealProductDTO.getActivities()));
        //20.团单售卖价格描述
        productM.setBasePriceDesc(dealProductDTO.getBasePriceDesc());
        //21.库存
        paddingProductMStock(dealProductDTO, productM);
        //22.标签列表
        productM.setProductTagList(dealProductDTO.getProductTagList());
        productM.setBeginDate(dealProductDTO.getBeginDate());
        productM.setEndDate(dealProductDTO.getEndDate());
        //23.商品关联的订单信息
        productM.setOrderUsers(buildOrderUsers(dealProductDTO.getOrderUsers()));
        //24.下单链接
        productM.setOrderUrl(dealProductDTO.getOrderUrl());

        //25.SPU
        productM.setSpuM(dealProductDTO.getSpu());
        productM.setSpuMList(dealProductDTO.getSpuList());

        //标记下已题填充
        // productM.setPrePadded(true);

        // 26.rank
        // productM.setResourceRank(buildRank(dealProductDTO));
        // 27.扩展商品图片信息
        // productM.setExtendImages(buildExtendImages(dealProductDTO.getMultiRatioHeadPicDTO()));

        // 28. 团单是否为太极团单
        // productM.setUnifyProduct(dealProductDTO.isUnifyProduct());
        // 29. 团单交易类型
        productM.setTradeType(dealProductDTO.getTradeType());
        // 30. 团单使用规则
        // productM.setUseRuleM(buildUseRule(dealProductDTO.getUseRule()));
        // productM.setMaterialList(buildMaterialList(dealProductDTO.getMaterialList(), productM));
        // 服务项目
        // productM.setDealDetailStructuredDTO(dealProductDTO.getDealDetailStructuredDTO());
        // productM.setStandardServiceProjectDTO(dealProductDTO.getStandardServiceProjectDTO());
        // // 三级类目ID
        // productM.setServiceTypeId(dealProductDTO.getServiceTypeId());


        // if(org.apache.commons.collections.CollectionUtils.isNotEmpty(productM.getMaterialList())){
        //     productM.setSale(null);
        //     DealProductMaterialM materialM = productM.getMaterialList().get(0);
        //     if(materialM.getSale() > 0) {
        //         ProductSaleM productSaleM = new ProductSaleM();
        //         productSaleM.setSale((int) materialM.getSale());
        //         productSaleM.setSaleTag(materialM.getSaleTag());
        //         productM.setSale(productSaleM);
        //     }
        // }
        // 31. 团单加项列表
        // productM.setAdditionalProjectList(buildAdditionalProjectList(dealProductDTO.getAdditionalProjectList()));
        // 32. 团单品牌名称
        // productM.setBrandName(dealProductDTO.getBrandName());
        // 33. 团单sku列表
        // productM.setProductSkuList(buildSkuList(dealProductDTO.getSkuList(), dealProductDTO.getProductId()));
        return productM;
    }


    private List<OrderUserM> buildOrderUsers(List<OrderUserDTO> orderUsers) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(orderUsers)) {
            return new ArrayList<>();
        }
        return orderUsers.stream().filter(Objects::nonNull).map(orderUser -> {
            OrderUserM orderUserM = new OrderUserM();
            BeanUtils.copyProperties(orderUser, orderUserM);
            return orderUserM;
        }).collect(Collectors.toList());
    }

    private List<TagM> buildProductTags(List<TagDTO> productTagList) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(productTagList)) {
            return new ArrayList<>();
        }
        return productTagList.stream().filter(Objects::nonNull)
                .map(dto -> new TagM(dto.getType(), dto.getId(), dto.getName()))
                .collect(Collectors.toList());
    }

    private void paddingProductMStock(DealProductDTO themeProductDTO, ProductM productM) {
        DealProductStockDTO productStockDTO = themeProductDTO.getStock();
        if (productStockDTO == null) {
            return;
        }
        ProductStockM stockM = new ProductStockM();
        stockM.setRemainStock(productStockDTO.getRemain());
        stockM.setTotalStock(productStockDTO.getTotal());
        stockM.setSoldOut(productStockDTO.isSoldOut());
        productM.setStock(stockM);
    }

    private List<ProductActivityM> buildDealActivities(List<DealProductActivityDTO> dealProductActivityDTOs) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(dealProductActivityDTOs)) {
            return Lists.newArrayList();
        }
        return dealProductActivityDTOs.stream().filter(dealProductActivityDTO -> dealProductActivityDTO != null).map(dealProductActivityDTO -> {
            ProductActivityM productActivityM = new ProductActivityM();
            productActivityM.setRemainingTime(dealProductActivityDTO.getRemainingTime());
            productActivityM.setUrl(dealProductActivityDTO.getUrl());
            productActivityM.setLable(dealProductActivityDTO.getTitle());
            productActivityM.setActivityBeginTime(dealProductActivityDTO.getActivityBeginTime());
            productActivityM.setActivityEndTime(dealProductActivityDTO.getActivityEndTime());
            productActivityM.setActivityPriceColor(dealProductActivityDTO.getActivityPriceColor());
            productActivityM.setActivityPriceText(dealProductActivityDTO.getActivityPriceText());
            productActivityM.setShelfActivityType(dealProductActivityDTO.getShelfActivityType());
            productActivityM.setPreheat(dealProductActivityDTO.isPreheat());
            productActivityM.setPageId(dealProductActivityDTO.getPageId());
            productActivityM.setActivityScene(dealProductActivityDTO.getActivityScene());
            productActivityM.setPriceStrengthTime(dealProductActivityDTO.getPriceStrengthTime());
            productActivityM.setUrlAspectRadio(dealProductActivityDTO.getUrlAspectRadio());
            productActivityM.setActivityExtraAttrs(dealProductActivityDTO.getActivityExtraAttrs());
            productActivityM.setActivityPicUrlMap(dealProductActivityDTO.getPicUrlDTOMap());
            productActivityM.setExposurePromotionType(dealProductActivityDTO.getExposurePromotionType());
            return productActivityM;
        }).collect(Collectors.toList());
    }

    private List<ProductCouponM> buildCoupons(List<DealProductCouponDTO> couponDTOS) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(couponDTOS)) {
            return null;
        }
        return couponDTOS.stream().filter(Objects::nonNull).map(couponDTO -> {
            ProductCouponM productCouponM = new ProductCouponM();
            productCouponM.setCouponTag(couponDTO.getBonusTag());
            return productCouponM;
        }).collect(Collectors.toList());
    }

    private ProductPriceM buildProductPriceM(DealProductPriceDTO productPriceDTO) {
        if (productPriceDTO == null) {
            return null;
        }
        ProductPriceM productPriceM = new ProductPriceM();
        productPriceM.setPriceTag(productPriceDTO.getPriceTag());
        productPriceM.setPriceDesc(productPriceDTO.getPriceDesc());
        return productPriceM;
    }

    private ProductSaleM buildProductSale(DealProductSaleDTO saleDTO) {
        if (saleDTO == null) {
            return null;
        }
        ProductSaleM productSaleM = new ProductSaleM();
        productSaleM.setSale(saleDTO.getSale());
        productSaleM.setSaleTag(saleDTO.getSaleTag());
        return productSaleM;
    }

    private List<AttrM> collectThemeAttrs(DealProductDTO themeProductDTO) {
        if (themeProductDTO == null || org.apache.commons.collections.CollectionUtils.isEmpty(themeProductDTO.getAttrs())) {
            return Lists.newArrayList();
        }
        return themeProductDTO.getAttrs().stream().filter(Objects::nonNull).map(attr -> new AttrM(attr.getName(), attr.getValue(), attr.getValueList())).collect(Collectors.toList());
    }

    private void paddingProductMAttrs(DealProductDTO themeProductDTO, ProductM productM) {
        List<AttrM> themeAttrs = collectThemeAttrs(themeProductDTO);
        mergeThemeAttrs2ProductMAttrs(productM, themeAttrs);
    }

    private void mergeThemeAttrs2ProductMAttrs(ProductM productM, List<AttrM> themeAttrs) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(productM.getExtAttrs())) {
            productM.setExtAttrs(Lists.newArrayList());
        }
        productM.getExtAttrs().addAll(themeAttrs);
    }

    private List<ShopM> buildShopMs(ProductM productM, DealProductDTO productDTO) {
        //如果调用主题之前已经填充，则终止继续填充主题层返回的数据
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(productM.getShopMs())) {
            return productM.getShopMs();
        }
        if (org.apache.commons.collections.CollectionUtils.isEmpty(productDTO.getShops())) {
            return Lists.newArrayList();
        }
        return productDTO.getShops().stream().map(shop -> {
            ShopM shopM = new ShopM();
            shopM.setShopId(shop.getShopId());
            shopM.setShopName(shop.getShopName());
            shopM.setLongShopId(shop.getShopIdAsLong());
            shopM.setShopUuid(shop.getShopUuid());
            shopM.setLat(shop.getLat());
            shopM.setLng(shop.getLng());
            shopM.setAddress(shop.getAddress());
            shopM.setDistance(shop.getDistance());
            shopM.setDistanceNum(shop.getDistanceNum());
            shopM.setPic(shop.getHeadPic());
            shopM.setLabels(buildShopLabels(shop.getLabels()));
            shopM.setDetailUrl(shop.getShopUrl());
            shopM.setMainRegionName(shop.getBusinessRegionName());
            shopM.setStarStr(shop.getStarStr());
            shopM.setUserEqShop(userEqShop(shop.getRemote()));
            if (shop.getProductSale() != null) {
                ProductSaleM productSaleM = new ProductSaleM();
                productSaleM.setSale(shop.getProductSale().getSale());
                productSaleM.setSaleTag(shop.getProductSale().getSaleTag());
                shopM.setSale(productSaleM);
            }
            return shopM;
        }).collect(Collectors.toList());
    }

    private boolean userEqShop(RemoteDTO remoteDTO) {
        if (remoteDTO == null) {
            return false;
        }
        return remoteDTO.isUserEqShop();
    }

    private List<ShopLabelM> buildShopLabels(List<ShopLabelDTO> labels) {
        List<ShopLabelM> shopLabelMS = new ArrayList<>();
        if (org.apache.commons.collections.CollectionUtils.isEmpty(labels)) {
            return shopLabelMS;
        }
        labels.forEach(shopLabelDTO -> {
            ShopLabelM shopLabelM = new ShopLabelM();
            shopLabelM.setType(shopLabelDTO.getType());
            shopLabelM.setTitle(shopLabelDTO.getTitle());
            shopLabelMS.add(shopLabelM);
        });
        return shopLabelMS;
    }

    private List<ProductPromoPriceM> buildDealPromoPrice(ProductM productM, List<DealProductPromoDTO> promoDTOs) {
        //如果调用主题之前已经填充，则终止继续填充主题层返回的数据
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(productM.getPromoPrices())) {
            return productM.getPromoPrices();
        }
        if (org.apache.commons.collections.CollectionUtils.isEmpty(promoDTOs)) {
            return Lists.newArrayList();
        }
        return buildPromoPrice(promoDTOs, productM.getMarketPrice(), productM.getProductId());
    }

    private List<ProductPromoPriceM> buildPromoPrice(List<DealProductPromoDTO> promoDTOs, String marketPrice, long productId) {
        return promoDTOs.stream().filter(Objects::nonNull).map(promoDTO -> {
            ProductPromoPriceM productPromoPriceM = new ProductPromoPriceM();
            productPromoPriceM.setProductId(productId);
            productPromoPriceM.setPromoType(promoDTO.getPromoType());
            productPromoPriceM.setPromoPrice(promoDTO.getPromoPrice());
            productPromoPriceM.setPromoPriceTag(promoDTO.getPromoPriceTag());
            productPromoPriceM.setNationalSubsidyPrice(promoDTO.getNationalSubsidyPrice());
            productPromoPriceM.setDiscount(promoDTO.getDiscount());
            productPromoPriceM.setPromoTag(promoDTO.getPromoTag());
            productPromoPriceM.setDiscountTag(promoDTO.getDiscountTag());
            productPromoPriceM.setAvailableTime(promoDTO.getAvailableTime());
            productPromoPriceM.setTotalPromoPrice(promoDTO.getTotalPromoPrice());
            productPromoPriceM.setTotalPromoPriceTag(promoDTO.getTotalPromoPriceTag());
            productPromoPriceM.setPromoItemList(buildPromoItemList(promoDTO.getPromoItemList()));
            productPromoPriceM.setMarketPrice(marketPrice);
            productPromoPriceM.setNationalSubsidyMarketPrice(promoDTO.getMarketPrice());
            productPromoPriceM.setPromoTagType(promoDTO.getPromoTagType());
            productPromoPriceM.setIcon(promoDTO.getPromoTagIcon());
            productPromoPriceM.setIconText(promoDTO.getPromoTagIconText());
            productPromoPriceM.setSinglePrice(promoDTO.getSinglePrice());
            productPromoPriceM.setPricePromoInfoMap(promoDTO.getPricePromoInfoMap());
            productPromoPriceM.setExtendDisplayInfo(promoDTO.getExtendDisplayInfo());
            return productPromoPriceM;
        }).collect(Collectors.toList());
    }

    private List<PromoItemM> buildPromoItemList(List<DealPromoItemDTO> promoItemList) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(promoItemList)) {
            return null;
        }
        return promoItemList.stream().map(this::buildPromoItemM).collect(Collectors.toList());
    }

    private PromoItemM buildPromoItemM(DealPromoItemDTO promoItem) {
        if (promoItem == null) {
            return null;
        }
        PromoItemM promoItemM = new PromoItemM();
        promoItemM.setPromoId(promoItem.getPromoId());
        promoItemM.setPromoTypeCode(promoItem.getPromoTypeCode());
        promoItemM.setDesc(promoItem.getDesc());
        promoItemM.setPromoPrice(promoItem.getPromoPrice());
        promoItemM.setAmount(promoItem.getAmount());
        promoItemM.setCouponId(promoItem.getCouponId());
        promoItemM.setCouponGroupId(promoItem.getCouponGroupId());
        promoItemM.setPromoTag(promoItem.getPromoTag());
        promoItemM.setPromoType(promoItem.getPromoType());
        promoItemM.setPromoIdentity(promoItem.getPromoIdentity());
        promoItemM.setSourceType(promoItem.getSourceType());
        promoItemM.setIcon(promoItem.getIcon());
        promoItemM.setNewUser(promoItem.isNewUser());
        promoItemM.setMinConsumptionAmount(promoItem.getMinConsumptionAmount());
        promoItemM.setRemainStock(promoItem.getRemainStock());
        promoItemM.setEndTime(promoItem.getEndTime());
        promoItemM.setEffectiveEndTime(promoItem.getEffectiveEndTime());
        promoItemM.setPromoItemText(convertPromoItemText(promoItem.getPromoItemTextDTO()));
        promoItemM.setPromotionExplanatoryTags(promoItem.getPromotionExplanatoryTags());
        promoItemM.setPromotionOtherInfoMap(promoItem.getPromotionOtherInfoMap());
        return promoItemM;
    }

    private static PromoItemTextM convertPromoItemText(DealPromoItemTextDTO dealPromoItemTextDTO){
        if (dealPromoItemTextDTO == null){
            return null;
        }
        PromoItemTextM promoItemTextM = new PromoItemTextM();
        promoItemTextM.setTitle(dealPromoItemTextDTO.getTitle());
        promoItemTextM.setSubTitle(dealPromoItemTextDTO.getSubTitle());
        promoItemTextM.setIcon(dealPromoItemTextDTO.getIcon());
        promoItemTextM.setPromoDivideType(dealPromoItemTextDTO.getPromoDivideType());
        promoItemTextM.setPromoDivideTypeDesc(dealPromoItemTextDTO.getPromoDivideTypeDesc());
        promoItemTextM.setPromoStatusText(dealPromoItemTextDTO.getPromoStatusText());
        promoItemTextM.setAtmosphereBarText(dealPromoItemTextDTO.getAtmosphereBarText());
        promoItemTextM.setAtmosphereBarIcon(dealPromoItemTextDTO.getAtmosphereBarIcon());
        promoItemTextM.setAtmosphereBarButtonText(dealPromoItemTextDTO.getAtmosphereBarButtonText());
        promoItemTextM.setAtmosphereBarButtonUrl(dealPromoItemTextDTO.getAtmosphereBarButtonUrl());
        return promoItemTextM;
    }

    private CompletableFuture<List<DealProductResult>> batchQueryProductTheme() {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(productIds)) {
            return CompletableFuture.completedFuture(Lists.newArrayList());
        }

        List<CompletableFuture<DealProductResult>> partFutureList = Lists.partition(productIds, 100).stream().filter(CollectionUtils::isNotEmpty).map(partProductIds -> {
            DealProductRequest dealProductRequest = buildDealProductRequest(partProductIds);
            return compositeAtomService.queryDealProductTheme(dealProductRequest);
        }).collect(Collectors.toList());

        return CompletableFuture.allOf(partFutureList.toArray(new CompletableFuture[0]))
                .thenApply(aVoid -> partFutureList.stream().map(CompletableFuture::join).collect(Collectors.toList()));
    }

    private <T> CompletableFuture<List<T>> assemble(List<CompletableFuture<T>> futures) {
        CompletableFuture<Void> allDoneFuture = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
        return allDoneFuture.thenApply(v -> futures.stream().map(CompletableFuture::join).collect(Collectors.toList()));
    }

    private DealProductRequest buildDealProductRequest(List<Long> subProductIds) {
        List<Integer> partProductIds = subProductIds.stream().filter(Objects::nonNull).map(Long::intValue).collect(Collectors.toList());
        DealProductRequest dealProductRequest = new DealProductRequest();
        dealProductRequest.setPlanId(DEFAULT_PLAN_ID);
        dealProductRequest.setProductIds(partProductIds);
        dealProductRequest.setExtParams(buildQueryExtParams(partProductIds));
        return dealProductRequest;
    }

    private Map<String, Object> buildQueryExtParams(List<Integer> partProductIds) {
        int platform = request.getPlatformEnum().getCode();
        int dpCityId = Optional.ofNullable(cityIdMapper).map(CityIdMapper::getDpCityId).orElse(0);
        int mtCityId = Optional.ofNullable(cityIdMapper).map(CityIdMapper::getMtCityId).orElse(0);
        long dpUserId = request.getDpUserId();
        long mtUserId = request.getMtUserId();
        long mtVirtualUserId = request.getShepherdGatewayParam().getMtVirtualUserId();
        long dpShopId = Optional.ofNullable(shopIdMapper).map(ShopIdMapper::getDpBestShopId).orElse(0L);
        long mtShopId = Optional.ofNullable(shopIdMapper).map(ShopIdMapper::getMtBestShopId).orElse(0L);
        boolean isMt = request.getClientTypeEnum().isMtClientType();

        Map<String, Object> extParams = new HashMap<>();
        // 这个地方的productId不能传Long类型的,不然团购主题会报错导致查询不到结果 具体报错的位置com.sankuai.athena.theme.framework.transformer.datadag.DataDagTransformer#compute execute拿不到
        extParams.put("dealIds", partProductIds);
        // 1. 点评商户ID
        extParams.put("dpShopId", Long.valueOf(dpShopId).intValue());
        extParams.put("dpShopIdForLong", dpShopId);
        // 2. 点评1, 美团2
        extParams.put("platform", platform);
        // 3. 城市ID, 点评侧为点评城市ID, 美团侧为美团城市ID
        extParams.put("cityId", isMt ? mtCityId : dpCityId);
        extParams.put("mtCityId", mtCityId);
        // 商户所在城市id，区分环境
        extParams.put("shopDpCityId", Optional.ofNullable(shopInfo).map(ShopInfo::getDpPoiDTO).map(DpPoiDTO::getCityId).orElse(0));
        extParams.put("shopMtCityId", Optional.ofNullable(shopInfo).map(ShopInfo::getMtPoiDTO).map(MtPoiDTO::getMtCityLocationId).orElse(0));
        // 4. 用户ID, 点评侧为点评用户ID, 美团侧为美团用户ID
        extParams.put("userId", isMt ? mtUserId : dpUserId);
        extParams.put(PmfConstants.Params.mtUserId, mtUserId);
        extParams.put(PmfConstants.Params.dpUserId, dpUserId);
        extParams.put("mtVirtualUserId", mtVirtualUserId);
        // 5. 设备ID
        extParams.put("deviceId", request.getShepherdGatewayParam().getDeviceId());
        // 6. 经纬度
        extParams.put("lat", request.getUserLat());
        extParams.put("lng", request.getUserLng());
        extParams.put("coordType", "GCJ02");
        // 7. app版本
        extParams.put("appVersion", request.getShepherdGatewayParam().getAppVersion());
        // 8. 门店ID
        extParams.put("shopIdForLong", isMt ? mtShopId : dpShopId);
        //不需要强转int，主题层使用是long类型mtShopId
        extParams.put("mtShopId", mtShopId);
        extParams.put("mtShopIdForLong", mtShopId);
        // 9. 客户端类型
        extParams.put("clientType", isMt ? VCClientTypeEnum.MT_APP.getCode() : VCClientTypeEnum.DP_APP.getCode());
        // 11. unionId
        extParams.put("unionId", request.getShepherdGatewayParam().getUnionid());
        // 12. osType
        extParams.put("osType", getOsType());
        // 13. appId
        extParams.put("appId", request.getShepherdGatewayParam().getMpAppId());
        // 14. openId
        extParams.put("openId", request.getShepherdGatewayParam().getOpenId());


        // 以下非标准字段跟着主题走
        extParams.put("dealId2ShopIdForLong", buildDealId2ShopId(partProductIds, isMt ? mtShopId : dpShopId));

        extParams.put("attributeKeys", Lists.newArrayList("service_type","pay_method"));
        extParams.put("dealAttributeKeys", Lists.newArrayList("sys_multi_sale_number"));
        // 和pmf DealQueryFetcher里面的 scene 对应
        extParams.put("scene", 400201);
        // 和pmf DealQueryFetcher里面的directPromoScene对应
        extParams.put("directPromoSceneCode", 400200);

        extParams.put("shopCategory", Optional.ofNullable(shopInfo).map(ShopInfo::getDpPoiDTO).map(DpPoiDTO::getMainCategoryId).orElse(0));
        //主题请求来源，后续可能细分列表、货架，当前默认传货架即可
        extParams.put("themeReqSource", ThemeReqSourceEnum.POI_SHELF.getType());
        extParams.put("pageSource", request.getPageSource());
        extParams.put("querySceneStrategy", 3);
        extParams.put("enablePreSalePromoTag", true);
        extParams.put("priceDescType", 4);

        //优惠明细类型
        extParams.put("promoDetailTemplate", "oldVersion");
        //曝光活动场景
        extParams.put("activityQueryScene", 1);

        return extParams;
    }


    private static final int OS_TYPE_HARMONY_INT = 3;
    public static final int OS_TYPE_ANDROID_INT = 2;
    public static final int OS_TYPE_IOS_INT = 1;
    private static final String ANDROID = "android";
    private static final String HARMONY = "harmony";

    private int getOsType() {
        MobileOSTypeEnum mobileOSType = request.getMobileOSType();
        String osTypeStr = mobileOSType.getCode();
        if (ANDROID.equals(osTypeStr)) {
            return OS_TYPE_ANDROID_INT;
        }
        if (HARMONY.equalsIgnoreCase(osTypeStr)) {
            return OS_TYPE_HARMONY_INT;
        }
        return OS_TYPE_IOS_INT;
    }

    public Map<Integer, Long> buildDealId2ShopId(List<Integer> productIds, long shopId) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(productIds)) {
            return Maps.newHashMap();
        }
        return productIds.stream().collect(HashMap::new, (map, productId) -> map.put(productId, shopId), HashMap::putAll);
    }
}
