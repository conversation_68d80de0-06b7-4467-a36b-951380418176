package com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.period.price;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.period.price.dto.PeriodPriceMergeDTO;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class SkuPeriodPriceTest {

    /**
     * Test case for getPeriodPriceDTO when periodPriceMap is null
     */
    @Test
    public void testGetPeriodPriceDTOWithNullMap() throws Throwable {
        // arrange
        SkuPeriodPrice skuPeriodPrice = new SkuPeriodPrice(null);
        // act
        Optional<PeriodPriceMergeDTO> result = skuPeriodPrice.getPeriodPriceDTO(1L);
        // assert
        assertFalse(result.isPresent());
    }

    /**
     * Test case for getPeriodPriceDTO when periodPriceMap doesn't contain the given skuId
     */
    @Test
    public void testGetPeriodPriceDTOWithNonExistentSkuId() throws Throwable {
        // arrange
        Map<Long, PeriodPriceMergeDTO> periodPriceMap = new HashMap<>();
        SkuPeriodPrice skuPeriodPrice = new SkuPeriodPrice(periodPriceMap);
        // act
        Optional<PeriodPriceMergeDTO> result = skuPeriodPrice.getPeriodPriceDTO(1L);
        // assert
        assertFalse(result.isPresent());
    }

    /**
     * Test case for getPeriodPriceDTO when periodPriceMap contains the given skuId
     */
    @Test
    public void testGetPeriodPriceDTOWithExistentSkuId() throws Throwable {
        // arrange
        Map<Long, PeriodPriceMergeDTO> periodPriceMap = new HashMap<>();
        PeriodPriceMergeDTO expectedDTO = new PeriodPriceMergeDTO(null, null, null);
        periodPriceMap.put(1L, expectedDTO);
        SkuPeriodPrice skuPeriodPrice = new SkuPeriodPrice(periodPriceMap);
        // act
        Optional<PeriodPriceMergeDTO> result = skuPeriodPrice.getPeriodPriceDTO(1L);
        // assert
        assertTrue(result.isPresent());
        assertEquals(expectedDTO, result.get());
    }
}
