package com.sankuai.dzshoppingguide.product.detail.application.fetcher.timesdeal;

import com.google.common.collect.Lists;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dz.product.detail.gateway.spi.enums.PlatformEnum;
import com.sankuai.dz.product.detail.gateway.spi.enums.ProductTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategoryFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.timesdeal.dto.TimesDealRelatedProduct;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.timesdeal.dto.TimesDealRelation;
import com.sankuai.dzshoppingguide.product.detail.domain.cpv.ProductAttrConfigService;
import com.sankuai.dzshoppingguide.product.detail.domain.query.center.QueryCenterAclService;
import com.sankuai.general.product.query.center.client.builder.model.*;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.enums.AttrSubjectEnum;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResponse;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResult;
import com.sankuai.general.product.query.center.client.util.DealGroupUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/14 09:55
 */
@Fetcher(
        previousLayerDependencies = {TimesDealRelationFetcher.class,ProductCategoryFetcher.class}
)
@Slf4j
public class TimesDealRelationProductFetcher extends NormalFetcherContext<TimesDealRelatedProduct> {

    @Resource
    public QueryCenterAclService queryCenterAclService;

    @Resource
    private ProductAttrConfigService productAttrConfigService;

    @Override
    protected CompletableFuture<TimesDealRelatedProduct> doFetch() throws Exception {

        if (request.getProductTypeEnum() != ProductTypeEnum.DEAL) {
            return CompletableFuture.completedFuture(null);
        }

        TimesDealRelation timesDealRelation = getDependencyResult(TimesDealRelationFetcher.class);
        if (timesDealRelation == null || CollectionUtils.isEmpty(timesDealRelation.getCombineDealIds())) {
            return CompletableFuture.completedFuture(null);
        }

        Set<Long> combineDealIds = timesDealRelation.getCombineDealIds();

        IdTypeEnum idTypeEnum = request.getPlatformEnum() == PlatformEnum.MT ? IdTypeEnum.MT : IdTypeEnum.DP;


        // Stream API处理每个分组
        List<CompletableFuture<QueryDealGroupListResponse>> futures = Lists.partition(Lists.newArrayList(combineDealIds), 30).stream()
                .map(batchList -> {
                    Set<Long> batch = new HashSet<>(batchList);
                    QueryByDealGroupIdRequestBuilder requestBuilder = QueryByDealGroupIdRequestBuilder.builder().dealGroupIds(batch, idTypeEnum);

                    // baseinfo
                    requestBuilder.category(DealGroupCategoryBuilder.builder().all())//品类
                            .channel(DealGroupChannelBuilder.builder().all())//团购频道信息
                            .image(DealGroupImageBuilder.builder().all())//商品图片视频信息
                            .region(DealGroupRegionBuilder.builder().all())//商品城市
                            .dealGroupTag(DealGroupTagBuilder.builder().all())//标签
                            .dealGroupPrice(DealGroupPriceBuilder.builder().all())//商品价格
                            .displayShop(DealGroupDisplayShopBuilder.builder().all())//适用门店
                            .rule(DealGroupRuleBuilder.builder()
                                    .refundRule()
                                    .buyRule()
                                    .useRule(DealGroupUtils.convertDate2String(new Date())))//交易规则
                            .dealGroupSaleChannelAggregation(DealGroupSaleChannelAggregationBuilder.builder().all());

                    // 属性信息
                    ProductCategory productCategory = getDependencyResult(ProductCategoryFetcher.class);
                    Set<String> productAttrConfig = productAttrConfigService.getProductAttrConfig(
                            request.getProductTypeEnum(), productCategory.getProductSecondCategoryId()
                    );
                    if (CollectionUtils.isNotEmpty(productAttrConfig)) {
                        requestBuilder.attrsByKey(AttrSubjectEnum.DEAL_GROUP, productAttrConfig);
                    }
                    try {
                        return queryCenterAclService.query(requestBuilder.build());
                    } catch (Exception e) {
                        log.error("queryCenterAclService.query exception for batch: " + batch, e);
                        return CompletableFuture.<QueryDealGroupListResponse>completedFuture(null);
                    }
                })
                .collect(Collectors.toList());

        // Combine all futures
        return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .thenApply(v -> {
                    List<DealGroupDTO> allDealGroups = futures.stream()
                            .map(future -> {
                                try {
                                    return future.get();
                                } catch (Exception e) {
                                    log.error("Error processing batch response", e);
                                    return null;
                                }
                            })
                            .filter(Objects::nonNull)
                            .map(response -> Optional.of(response)
                                    .map(QueryDealGroupListResponse::getData)
                                    .map(QueryDealGroupListResult::getList)
                                    .orElse(new ArrayList<>()))
                            .flatMap(List::stream)
                            .collect(Collectors.toList());
                    return TimesDealRelatedProduct.builder().dealGroupDTOList(allDealGroups).build();
                })
                .exceptionally(throwable -> {
                    log.error("查询中心调用失败!!!未查到关联团购或次卡的信息", throwable);
                    return null;
                });
    }
}
