package com.sankuai.dzshoppingguide.product.detail.application.utils;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * Unit tests for ShopUuidUtils
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("ShopUuidUtils Test")
public class ShopUuidUtilsTest {

    /**
     * Test case for null URL input
     */
    @Test
    @DisplayName("Should return null when URL is null")
    public void testFilterUrl_WhenUrlIsNull() {
        // arrange
        String url = null;
        String dpShopUuid = "123456";
        // act
        String result = ShopUuidUtils.filterUrl(url, dpShopUuid);
        // assert
        assertNull(result);
    }

    /**
     * Test case for empty URL input
     */
    @Test
    @DisplayName("Should return empty string when URL is empty")
    public void testFilterUrl_WhenUrlIsEmpty() {
        // arrange
        String url = "";
        String dpShopUuid = "123456";
        // act
        String result = ShopUuidUtils.filterUrl(url, dpShopUuid);
        // assert
        assertEquals("", result);
    }

    /**
     * Test case when URL already contains shopuuid
     */
    @Test
    @DisplayName("Should return original URL when it already contains shopuuid")
    public void testFilterUrl_WhenUrlContainsShopUuid() {
        // arrange
        String url = "http://example.com?shopid=123&shopuuid=456";
        String dpShopUuid = "789";
        // act
        String result = ShopUuidUtils.filterUrl(url, dpShopUuid);
        // assert
        assertEquals("http://example.com?shopid=123&shopuuid=456", result);
    }

    /**
     * Test case when URL contains shopid and dpShopUuid is provided
     */
    @Test
    @DisplayName("Should append shopuuid when URL contains shopid and dpShopUuid is provided")
    public void testFilterUrl_WhenUrlContainsShopIdAndDpShopUuidProvided() {
        // arrange
        String url = "http://example.com?shopid=123";
        String dpShopUuid = "456";
        // act
        String result = ShopUuidUtils.filterUrl(url, dpShopUuid);
        // assert
        assertEquals("http://example.com?shopid=123&shopuuid=456", result);
    }

    /**
     * Test case when URL contains shopid but dpShopUuid is empty
     */
    @Test
    @DisplayName("Should return original URL when it contains shopid but dpShopUuid is empty")
    public void testFilterUrl_WhenUrlContainsShopIdButDpShopUuidEmpty() {
        // arrange
        String url = "http://example.com?shopid=123";
        String dpShopUuid = "";
        // act
        String result = ShopUuidUtils.filterUrl(url, dpShopUuid);
        // assert
        assertEquals("http://example.com?shopid=123", result);
    }

    /**
     * Test case when URL doesn't contain shopid pattern
     */
    @Test
    @DisplayName("Should return original URL when it doesn't contain shopid")
    public void testFilterUrl_WhenUrlDoesNotContainShopId() {
        // arrange
        String url = "http://example.com?param=value";
        String dpShopUuid = "456";
        // act
        String result = ShopUuidUtils.filterUrl(url, dpShopUuid);
        // assert
        assertEquals("http://example.com?param=value", result);
    }
}
