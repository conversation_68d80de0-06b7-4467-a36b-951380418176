package com.sankuai.dzshoppingguide.product.detail.application.builder.makeup.layer;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.MarkupDetailModuleConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * Unit tests for {@link MarkupLayerModuleBuilder#buildModuleConfig}
 */
@ExtendWith(MockitoExtension.class)
class MarkupLayerModuleBuilderTest {

    @InjectMocks
    private MarkupLayerModuleBuilder markupLayerModuleBuilder;

    /**
     * Test building module config with valid order page URL
     */
    @Test
    @DisplayName("Should successfully build module config with valid order page URL")
    void testBuildModuleConfigWithValidUrl() {
        // arrange
        String orderPageUrl = "https://example.com/order/123";
        // act
        MarkupDetailModuleConfig result = markupLayerModuleBuilder.buildModuleConfig(orderPageUrl);
        // assert
        assertNotNull(result, "Module config should not be null");
        assertThat(result).hasFieldOrPropertyWithValue("title", "付费升级").hasFieldOrPropertyWithValue("jumpUrl", orderPageUrl);
    }

    /**
     * Test building module config with empty order page URL
     */
    @Test
    @DisplayName("Should successfully build module config with empty order page URL")
    void testBuildModuleConfigWithEmptyUrl() {
        // arrange
        String orderPageUrl = "";
        // act
        MarkupDetailModuleConfig result = markupLayerModuleBuilder.buildModuleConfig(orderPageUrl);
        // assert
        assertNotNull(result, "Module config should not be null");
        assertEquals("付费升级", result.getTitle(), "Title should be set to '付费升级'");
        assertEquals("", result.getJumpUrl(), "Jump URL should be empty");
    }

    /**
     * Test building module config with null order page URL
     */
    @Test
    @DisplayName("Should successfully build module config with null order page URL")
    void testBuildModuleConfigWithNullUrl() {
        // arrange
        String orderPageUrl = null;
        // act
        MarkupDetailModuleConfig result = markupLayerModuleBuilder.buildModuleConfig(orderPageUrl);
        // assert
        assertNotNull(result, "Module config should not be null");
        assertEquals("付费升级", result.getTitle(), "Title should be set to '付费升级'");
        assertThat(result.getJumpUrl()).isNull();
    }
}
