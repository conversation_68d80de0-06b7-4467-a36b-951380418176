package com.sankuai.dzshoppingguide.product.detail.application.builder.makeup.detail;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.MarkupDetailModuleConfig;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.MarkupDetailPopup;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * Unit tests for MarkupDetailModuleBuilder.buildModuleConfig method
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("MarkupDetailModuleBuilder BuildModuleConfig Tests")
public class MarkupDetailModuleBuilderBuildModuleConfigTest {

    @InjectMocks
    private MarkupDetailModuleBuilder markupDetailModuleBuilder;

    /**
     * Test case to verify the correct configuration of MarkupDetailModuleConfig
     * Verifies:
     * - Title is set to "加价详情"
     * - Popup object is created with correct text
     * - All required fields are properly initialized
     */
    @Test
    @DisplayName("Should return properly configured MarkupDetailModuleConfig")
    public void testBuildModuleConfig_ShouldReturnConfiguredObject() {
        // arrange
        // No specific arrangement needed as method has no dependencies
        // act
        MarkupDetailModuleConfig result = markupDetailModuleBuilder.buildModuleConfig();
        // assert
        assertNotNull(result, "Module config should not be null");
        assertEquals("付费升级", result.getTitle(), "Title should be set to '加价详情'");
        MarkupDetailPopup popup = result.getPopup();
        assertNotNull(popup, "Popup should not be null");
        assertEquals("去升级", popup.getText(), "Popup text should be set to '去升级'");
        assertNull(popup.getJumpUrl(), "Popup jumpUrl should be null by default");
    }

    /**
     * Test case to verify that each call creates a new instance
     * Verifies:
     * - Multiple calls return different object instances
     * - Each instance is properly configured
     */
    @Test
    @DisplayName("Should create new instance on each call")
    public void testBuildModuleConfig_ShouldCreateNewInstance() {
        // arrange
        // No specific arrangement needed as method has no dependencies
        // act
        MarkupDetailModuleConfig result1 = markupDetailModuleBuilder.buildModuleConfig();
        MarkupDetailModuleConfig result2 = markupDetailModuleBuilder.buildModuleConfig();
        // assert
        assertNotNull(result1, "First result should not be null");
        assertNotNull(result2, "Second result should not be null");
        assertNotSame(result1, result2, "Should return different instances");
        // Verify both instances are properly configured
        assertEquals("付费升级", result1.getTitle(), "First instance title should be correct");
        assertEquals("付费升级", result2.getTitle(), "Second instance title should be correct");
        assertNotNull(result1.getPopup(), "First instance popup should not be null");
        assertNotNull(result2.getPopup(), "Second instance popup should not be null");
        assertEquals("去升级", result1.getPopup().getText(), "First instance popup text should be correct");
        assertEquals("去升级", result2.getPopup().getText(), "Second instance popup text should be correct");
    }
}
