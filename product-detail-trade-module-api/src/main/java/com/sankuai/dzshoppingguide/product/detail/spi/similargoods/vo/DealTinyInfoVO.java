package com.sankuai.dzshoppingguide.product.detail.spi.similargoods.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@MobileDo(id = 0xf1f408b3)
@Data
public class DealTinyInfoVO implements Serializable {
    /**
     * 按钮文案
     */
    @MobileField(key = 0xa5e3)
    private String btnText;

    /**
     * 团单二级类目
     */
    @MobileField(key = 0x33fe)
    private int categoryId;

    /**
     * 提单页
     */
    @MobileField(key = 0x60b)
    private String directBuyJumpUrl;

    /**
     * skuId，团购商品的套餐id
     */
    @MobileField(key = 0xf59e)
    private int skuId;

    /**
     * 门市价（划线价）
     */
    @MobileField(key = 0x6242)
    private String marketPrice;

    /**
     * 到手价
     */
    @MobileField(key = 0xe949)
    private String finalPrice;

    /**
     * 折扣，如5.0折
     */
    @MobileField(key = 0x6509)
    private String discount;

    /**
     * 价格力标签
     */
    @MobileField(key = 0x2617)
    private List<String> pricePowerTag;

    /**
     * 1:1头图
     */
    @MobileField(key = 0x30ed)
    private String headPic;

    /**
     * 年销量，已售1万
     */
    @MobileField(key = 0xa2c0)
    private String saleTag;

    /**
     * 团单副标题
     */
    @MobileField(key = 0xd894)
    private String subTitle;

    /**
     * 团单标题
     */
    @MobileField(key = 0x24cc)
    private String title;

    /**
     * 团单Id
     */
    @MobileField(key = 0x9a1c)
    private int dealGroupId;

}
