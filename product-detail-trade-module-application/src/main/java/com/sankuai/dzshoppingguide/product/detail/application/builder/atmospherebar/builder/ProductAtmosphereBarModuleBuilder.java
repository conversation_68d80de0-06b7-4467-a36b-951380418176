package com.sankuai.dzshoppingguide.product.detail.application.builder.atmospherebar.builder;

import com.dianping.gmkt.scene.api.delivery.dto.res.DealGroupPromotionDTO;
import com.dianping.gmkt.scene.api.delivery.dto.res.ResourceExposureResponseDTO;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.BuilderTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.pricebar.builder.AbstractProductBarModuleBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.atmosphere.ProductAtmosphereFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.atmosphere.ProductAtmosphereReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.category.ProductCategoryFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.ProductPromoPriceFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.ProductNormalPriceFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.dto.ProductPriceReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.attr.SkuAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.attr.SkuAttrFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.sale.ProductSaleFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.sale.ProductSaleReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.FetcherDAGStarter;
import com.sankuai.dzshoppingguide.product.detail.spi.atmospherebar.vo.ProductAtmosphereModuleVO;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.AtmosphereInfoDetailVO;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DetailPriceVO;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DetailSaleVO;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025-03-10
 * @desc 商品氛围条模块
 */
@Builder(
        builderType = BuilderTypeEnum.VARIABLE_BUILDER,
        moduleKey = ModuleKeyConstants.ATMOSPHERE_PRICE_SALE_BAR_MODULE,
        startFetcher = FetcherDAGStarter.class,
        dependentFetchers = {
                ProductAtmosphereFetcher.class,
                ProductNormalPriceFetcher.class,
                ProductSaleFetcher.class,
                ProductPromoPriceFetcher.class,
                ProductCategoryFetcher.class,
                ProductBaseInfoFetcher.class,
                SkuAttrFetcher.class
        }
)
public class ProductAtmosphereBarModuleBuilder extends AbstractProductBarModuleBuilder<ProductAtmosphereModuleVO> {

    @Override
    public ProductAtmosphereModuleVO doBuild() {
        ProductPriceReturnValue normalPriceReturnValue = getDependencyResult(ProductNormalPriceFetcher.class);
        ProductPriceReturnValue promoPriceReturnValue = getDependencyResult(ProductPromoPriceFetcher.class);
        ProductSaleReturnValue productSaleReturnValue = getDependencyResult(ProductSaleFetcher.class);
        ProductAtmosphereReturnValue productAtmosphereReturnValue = getDependencyResult(ProductAtmosphereFetcher.class);
        ProductCategory productCategory = getDependencyResult(ProductCategoryFetcher.class);
        ProductBaseInfo productBaseInfo = getDependencyResult(ProductBaseInfoFetcher.class);
        SkuAttr skuAttr = getDependencyResult(SkuAttrFetcher.class);

        ResourceExposureResponseDTO exposureResource = productAtmosphereReturnValue.getResourceExposureResponse();
        if (Objects.isNull(exposureResource) || CollectionUtils.isEmpty(exposureResource.getMaterialsDTOList())
                || Objects.isNull(exposureResource.getMaterialsDTOList().get(0))
                || Objects.isNull(exposureResource.getMaterialsDTOList().get(0).getDealGroupPromotionDTO())) {
            return null;
        }
        DealGroupPromotionDTO dealGroupPromotionDTO = exposureResource.getMaterialsDTOList().get(0).getDealGroupPromotionDTO();
        ProductAtmosphereModuleVO result = new ProductAtmosphereModuleVO();
        // 氛围条
        AtmosphereInfoDetailVO atmosphereDetailVO = buildAtmosphereDetailVO(dealGroupPromotionDTO, exposureResource.getActivityId());
        result.setAtmosphere(atmosphereDetailVO);
        // 销量
        DetailSaleVO saleVO = buildSaleInfo(productSaleReturnValue);
        result.setSale(saleVO);
        // 价格
        ProductPriceParam priceParam = ProductPriceParam.builder()
                .normalPriceReturnValue(normalPriceReturnValue)
                .dealPromoPriceReturnValue(promoPriceReturnValue)
                .productCategory(productCategory)
                .productBaseInfo(productBaseInfo)
                // 确保一定是有氛围条的时候再设置该字段
                .isAtmosphere(true)
                .skuAttr(skuAttr)
                .build();
        DetailPriceVO priceVO = buildPriceVO(priceParam);
        if (Objects.isNull(priceVO)) {
            return null;
        }
        // 价格营销点、折扣标签样式
        putPriceStyle(priceVO, dealGroupPromotionDTO);
        result.setPrice(priceVO);
        return result;
    }

    private void putPriceStyle(DetailPriceVO priceVO, DealGroupPromotionDTO dealGroupPromotionDTO) {
        // 折扣标签样式
        priceVO.setDiscountTagTextColor("#FF2727");
        priceVO.setDiscountTagBackgroundColor("#FFFFFF");
        priceVO.setPriceMarketingPointTag(dealGroupPromotionDTO.getPromoPriceDesc());
    }

    private AtmosphereInfoDetailVO buildAtmosphereDetailVO(DealGroupPromotionDTO dealGroupPromotionDTO, Long activityId) {
        AtmosphereInfoDetailVO atmosphereDetailVO = new AtmosphereInfoDetailVO();
        // 氛围条背景图
        atmosphereDetailVO.setBackgroundImage(dealGroupPromotionDTO.getPicUrl());
        // 倒计时文案
        atmosphereDetailVO.setCountDownDesc(dealGroupPromotionDTO.getCountDownDesc());
        // 倒计时颜色
        atmosphereDetailVO.setCountDownColor(dealGroupPromotionDTO.getCountDownColor());
        // 倒计时时间戳
        atmosphereDetailVO.setCountDownTs(dealGroupPromotionDTO.getCountDown());
        // 倒计时背景色
        atmosphereDetailVO.setCountDownBackgroundColor(dealGroupPromotionDTO.getCountDownBackgroundColor());
        // 跳转链接
        atmosphereDetailVO.setClickUrl(dealGroupPromotionDTO.getJumpLink());
        // 氛围条类型
        atmosphereDetailVO.setType(dealGroupPromotionDTO.getType());
        // 氛围条活动ID
        atmosphereDetailVO.setActivityId(String.valueOf(activityId));
        // 展示新氛围条
        //atmosphereDetailVO.setShowNewAtmosphereBar();
        return atmosphereDetailVO;
    }
}
