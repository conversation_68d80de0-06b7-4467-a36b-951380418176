package com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.component.trade.block.cepintuan;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.component.trade.block.cepintuan.utils.CEPTUrlUtils;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.component.trade.block.utils.PriceFormatUtils;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.exception.TradeButtonBuildFatalException;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.cepinpool.dto.CostEffectivePinTuan;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.enums.OpenTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.extend.redirect.SimpleRedirectActionVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.component.trade.button.StandardTradeButtonVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.backgroud.BottomBarBackgroundVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.rich.content.CountDownRichContentVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.rich.content.TextRichContentVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.rich.content.enums.TextStyleEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.tag.ButtonTopTagVO;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;

import javax.annotation.Nullable;

import static com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.constant.ButtonStyleConstant.CE_PIN_TUAN_STRONG_BUTTON;
import static com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.constant.ButtonStyleConstant.CE_PIN_TUAN_WEAK_BUTTON;

/**
 * @Author: guangyujie
 * @Date: 2025/3/20 18:16
 */
@Slf4j
public class CEPTMasterWeakButtonComponent {

    public static StandardTradeButtonVO build(final ProductDetailPageRequest request,
                                              @Nullable final PriceDisplayDTO costEffectivePrice,
                                              @NotNull final CostEffectivePinTuan costEffectivePinTuan) {
        try {
            StandardTradeButtonVO button = new StandardTradeButtonVO();
            button.setActionData(new SimpleRedirectActionVO(
                    OpenTypeEnum.redirect,
                    request.getClientTypeEnum() == ClientTypeEnum.MT_APP ?
                            CEPTUrlUtils.getPinTuanResultUrl(costEffectivePinTuan) :
                            CEPTUrlUtils.getWxShareJumpUrl(costEffectivePinTuan)
            ));
            button.setMainTitle(Lists.newArrayList(
                    new TextRichContentVO("¥", TextStyleEnum.Bold, 24, CE_PIN_TUAN_WEAK_BUTTON.getTitleColor()),
                    new TextRichContentVO(
                            PriceFormatUtils.getPlainString(costEffectivePrice),
                            TextStyleEnum.Bold, 48, CE_PIN_TUAN_WEAK_BUTTON.getTitleColor()),
                    new TextRichContentVO(
                            " 拼团中 >",
                            TextStyleEnum.Bold, 32, CE_PIN_TUAN_WEAK_BUTTON.getTitleColor())
            ));
            button.setBackground(BottomBarBackgroundVO.buildSingleColorWithBorder(
                    CE_PIN_TUAN_WEAK_BUTTON.getBackgroundSingleColor(), CE_PIN_TUAN_WEAK_BUTTON.getTitleColor(), 2
            ));
            button.setButtonTag(new ButtonTopTagVO(
                    Lists.newArrayList(
                            new TextRichContentVO(
                                    String.format("还差 %s 人成团 | ", costEffectivePinTuan.getNeedMemberCount()),
                                    TextStyleEnum.Default, 24, CE_PIN_TUAN_WEAK_BUTTON.getBackgroundSingleColor()
                            ),
                            new CountDownRichContentVO(
                                    costEffectivePinTuan.getExpireTime(),
                                    TextStyleEnum.Default, 24, CE_PIN_TUAN_WEAK_BUTTON.getBackgroundSingleColor()
                            )
                    ),
                    BottomBarBackgroundVO.buildLevelGradientColor(CE_PIN_TUAN_STRONG_BUTTON.getBackGroundColors()),
                    1
            ));
            return button;
        } catch (Throwable throwable) {
            log.error("CEPTMasterWeakButtonComponent.build,request:{}",
                    JSON.toJSONString(request), new TradeButtonBuildFatalException(throwable));
            return null;
        }
    }

}
