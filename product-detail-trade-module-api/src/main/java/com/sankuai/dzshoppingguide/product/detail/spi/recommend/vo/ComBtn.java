package com.sankuai.dzshoppingguide.product.detail.spi.recommend.vo;


import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import java.io.Serializable;

/**
 * @Author: zhangyuan103
 * @Date: 2025/5/19
 */
@TypeDoc(description = "常用按钮")
@MobileDo(id = 0xd98)
public class ComBtn implements Serializable {

    @FieldDoc(description = "按钮标题")
    @MobileDo.MobileField(key = 0x36e9)
    private String title;

    @FieldDoc(description = "跳转地址")
    @MobileDo.MobileField(key = 0xa706)
    private String clickUrl;

    @FieldDoc(description = "0仅展示；1点击跳转 ；具体指以后端枚举为准")
    @MobileDo.MobileField(key = 0xb783)
    private int actionType;

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getClickUrl() {
        return clickUrl;
    }

    public void setClickUrl(String clickUrl) {
        this.clickUrl = clickUrl;
    }

    public int getActionType() {
        return actionType;
    }

    public void setActionType(int actionType) {
        this.actionType = actionType;
    }

    //0-只展示 1-点击跳转 2-点击分享
    public enum ActionEnum{
        SHOW(0),
        REDIRECT(1),
        SHARE(3);

        public int type;

        ActionEnum(int type) {
            this.type = type;
        }
    }

}
