package com.sankuai.dzshoppingguide.product.detail.application.fetcher.atmosphere;

import com.dianping.gmkt.scene.api.delivery.dto.req.ExecuteResourcesReqDTO;
import com.dianping.gmkt.scene.api.delivery.dto.req.QueryExposureResourcesReqDTO;
import com.dianping.gmkt.scene.api.delivery.dto.req.RiskContextDTO;
import com.dianping.gmkt.scene.api.delivery.dto.res.ResourceExposureResponseDTO;
import com.dianping.gmkt.scene.api.delivery.enums.ClientTypeEnum;
import com.google.common.collect.Lists;
import com.sankuai.dealuser.price.display.api.enums.ExtensionKeyEnum;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dz.product.detail.RequestCustomParamEnum;
import com.sankuai.dz.product.detail.gateway.spi.dto.ShepherdGatewayParam;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.best.shop.ShopInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.best.shop.ShopInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.FetcherDAGStarter;
import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
import com.sankuai.dzshoppingguide.product.detail.application.utils.LiveUtils;
import com.sankuai.dzshoppingguide.product.detail.domain.utils.LionConfigUtils;
import com.sankuai.sinai.data.api.dto.DpPoiBackCategoryDTO;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.sinai.data.api.dto.MtPoiDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-03-10
 * @desc 商品氛围信息
 */
@Fetcher(
        previousLayerDependencies = {FetcherDAGStarter.class, ShopInfoFetcher.class}
)
public class ProductAtmosphereFetcher extends NormalFetcherContext<ProductAtmosphereReturnValue> {

    private Optional<ShopInfo> shopInfoOptional;

    @Resource
    private CompositeAtomService compositeAtomService;

    @Override
    protected CompletableFuture<ProductAtmosphereReturnValue> doFetch() {
        boolean isMt = request.getClientTypeEnum().isMtClientType();
        long resourceLocationId = LionConfigUtils.getAtmosphereResourceLocationId(isMt);
        shopInfoOptional = getDependencyResult(ShopInfoFetcher.class, ShopInfo.class);
        QueryExposureResourcesReqDTO reqDTO = buildExposureResourcesRequest(request);
        return compositeAtomService.queryExposureResources(reqDTO).thenApply(response -> {
            if (CollectionUtils.isEmpty(response)) {
                return new ProductAtmosphereReturnValue();
            }
            Optional<ResourceExposureResponseDTO> exposureResourceOpt = response.stream()
                    .filter(resp -> Objects.equals(resp.getResourceLocationId(), resourceLocationId))
                    .findFirst();
            return new ProductAtmosphereReturnValue(exposureResourceOpt.orElse(null));
        });
    }

    private QueryExposureResourcesReqDTO buildExposureResourcesRequest(ProductDetailPageRequest pageRequest) {
        boolean isMt = pageRequest.getClientTypeEnum().isMtClientType();
        long resourceLocationId = LionConfigUtils.getAtmosphereResourceLocationId(isMt);
        ShepherdGatewayParam shepherdGatewayParam = pageRequest.getShepherdGatewayParam();
        QueryExposureResourcesReqDTO reqDTO = new QueryExposureResourcesReqDTO();
        reqDTO.setCityId(pageRequest.getCityId());
        reqDTO.setLatitude(pageRequest.getUserLat());
        reqDTO.setLongitude(pageRequest.getUserLng());
        reqDTO.setResourceLocationId(resourceLocationId);
        reqDTO.setUuid(shepherdGatewayParam.getDeviceId());
        reqDTO.setUserId(pageRequest.getUserId());
        reqDTO.setPlatform(isMt ? 2 : 1);
        reqDTO.setBizIds(Collections.singletonList(String.valueOf(pageRequest.getProductId())));
        reqDTO.setBizIdsType(ExecuteResourcesReqDTO.BizIdType.DEAL_GROUP_ID.getCode());
        reqDTO.setShopIdL(pageRequest.getPoiId());
        reqDTO.setCountDownVersion(6);
        reqDTO.setClientType(getClientType(pageRequest));

        reqDTO.setClientVersion(shepherdGatewayParam.getAppVersion());
        reqDTO.setLocationCityId(pageRequest.getGpsCityId());

        // 直播参数
        String passParam = pageRequest.getCustomParam(RequestCustomParamEnum.pass_param);
        Map<String, String> liveSecKillParams = LiveUtils.getLiveSecKillParams(passParam, pageRequest.getPageSource());
        String liveRoomId = MapUtils.isNotEmpty(liveSecKillParams) ? liveSecKillParams.get(ExtensionKeyEnum.LiveStreamingSceneId.getDesc()) : null;
        if (StringUtils.isNotBlank(liveRoomId) && NumberUtils.isCreatable(liveRoomId)) {
            reqDTO.setLiveRoomId(Long.parseLong(liveRoomId));
            // 直播AB参数
            String mliveAbTestArgs = pageRequest.getCustomParam(RequestCustomParamEnum.mliveAbTestArgs);
            reqDTO.setLiveAbParam(mliveAbTestArgs);
        }
        reqDTO.setPageSource(pageRequest.getPageSource());
        // 风控参数
        reqDTO.setRiskContextDTO(buildRiskContextDTO(pageRequest));
        // 门店一级类目
        List<Integer> firstPoiCategoryIds = getDpPoiCategoryIds(shopInfoOptional.map(ShopInfo::getDpPoiDTO).orElse(null), NumberUtils.INTEGER_ONE);
        reqDTO.setFirstCategoryList(firstPoiCategoryIds);
        // 门店二级类目
        List<Integer> secondPoiCategoryIds = getDpPoiCategoryIds(shopInfoOptional.map(ShopInfo::getDpPoiDTO).orElse(null), NumberUtils.INTEGER_TWO);
        reqDTO.setSecondCategoryList(secondPoiCategoryIds);
        return reqDTO;
    }

    private RiskContextDTO buildRiskContextDTO(ProductDetailPageRequest pageRequest) {
        RiskContextDTO riskContextDTO = new RiskContextDTO();
        ShepherdGatewayParam shepherdGatewayParam = pageRequest.getShepherdGatewayParam();
        riskContextDTO.setUnionId(shepherdGatewayParam.getUnionid());
        return riskContextDTO;
    }

    private String getClientType(ProductDetailPageRequest pageRequest) {
        switch (pageRequest.getClientTypeEnum()) {
            case MT_XCX:
                return ClientTypeEnum.MT_WE_CHAT_APPLET.getClientType();
            case DP_APP:
                return ClientTypeEnum.DP_APP.getClientType();
            case DP_XCX:
                return ClientTypeEnum.DP_WE_CHAT_APPLET.getClientType();
            default:
                return ClientTypeEnum.MT_APP.getClientType();
        }
    }

    public List<Integer> getMtPoiCategoryIds(MtPoiDTO mtPoiDTO, int level) {
        if (Objects.isNull(mtPoiDTO) || CollectionUtils.isEmpty(mtPoiDTO.getDpBackCategoryList())) {
            return Lists.newArrayList();
        }
        return mtPoiDTO.getDpBackCategoryList()
                .stream()
                .filter(cate -> Objects.equals(cate.getCategoryLevel(), level))
                .map(DpPoiBackCategoryDTO::getCategoryId)
                .collect(Collectors.toList());
    }

    public List<Integer> getDpPoiCategoryIds(DpPoiDTO dpPoiDTO, int level) {
        if (Objects.isNull(dpPoiDTO) || CollectionUtils.isEmpty(dpPoiDTO.getBackMainCategoryPath())) {
            return Lists.newArrayList();
        }
        return dpPoiDTO.getBackMainCategoryPath()
                .stream()
                .filter(cate -> Objects.equals(cate.getCategoryLevel(), level))
                .map(DpPoiBackCategoryDTO::getCategoryId)
                .collect(Collectors.toList());
    }
}
