package com.sankuai.dzshoppingguide.product.detail.application.utils;

import com.dianping.deal.common.enums.ClientTypeEnum;
import com.dianping.tuangu.dztg.usercenter.api.enums.ChannelEnum;
import com.sankuai.dz.product.detail.gateway.spi.enums.MobileOSTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;

import static com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum.*;
import static com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum.MT_KUAI_SHOU_XCX;

public class ClientMappingUtils {

    /**
     * 获取商品BP端
     * @param pageRequest
     * @return
     */
    public static ClientTypeEnum getBpClientType(ProductDetailPageRequest pageRequest) {
        com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum sourceClientTypeEnum = pageRequest.getClientTypeEnum();
        MobileOSTypeEnum mobileOSTypeEnum = pageRequest.getMobileOSType();
        // 点评App
        if (sourceClientTypeEnum == DP_APP) {
            if (mobileOSTypeEnum == MobileOSTypeEnum.IOS) {
                return ClientTypeEnum.dp_mainApp_ios;
            }
            return ClientTypeEnum.dp_mainApp_android;
        }
        // 美团App
        if (sourceClientTypeEnum == MT_APP) {
            if (mobileOSTypeEnum == MobileOSTypeEnum.IOS) {
                return ClientTypeEnum.mt_mainApp_ios;
            }
            return ClientTypeEnum.mt_mainApp_android;
        }
        // 美团微信小程序
        if (sourceClientTypeEnum == MT_WX) {
            return ClientTypeEnum.mt_weApp;
        }
        // 点评微信小程序
        if (sourceClientTypeEnum == DP_WX) {
            return ClientTypeEnum.dp_weApp;
        }
        // 点评百度地图小程序
        if (sourceClientTypeEnum == DP_BAIDU_MAP_XCX) {
            return ClientTypeEnum.dp_baiduMap_weApp;
        }
        // 快手小程序
        if (sourceClientTypeEnum == MT_KUAI_SHOU_XCX) {
            return ClientTypeEnum.mt_mainApp_ios;
        }
        // 兜底使用美团ios app查价
        return ClientTypeEnum.mt_mainApp_ios;
    }

    /**
     * 获取交易BP端
     * @param pageRequest
     * @return
     */
    public static ChannelEnum getOrderClientType(ProductDetailPageRequest pageRequest) {
        com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum sourceClientTypeEnum = pageRequest.getClientTypeEnum();
        if (sourceClientTypeEnum == MT_APP || sourceClientTypeEnum == DP_APP) {
            return ChannelEnum.APP;
        }
        if (sourceClientTypeEnum == DP_XCX || sourceClientTypeEnum == MT_XCX) {
            return ChannelEnum.WX_MINI_PROGRAM;
        }
        if (sourceClientTypeEnum == DP_BAIDU_MAP_XCX) {
            return ChannelEnum.BAIDU_MAP_MINI_PROGRAM;
        }
        if (sourceClientTypeEnum == MT_LIVE_ORDER_XCX || sourceClientTypeEnum == MT_LIVE_XCX) {
            return ChannelEnum.SHANBO_MINI_PROGRAM;
        }
        return ChannelEnum.APP;
    }


}
