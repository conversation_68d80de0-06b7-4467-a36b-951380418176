package com.sankuai.dzshoppingguide.product.detail.application.fetcher.timescard;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import com.sankuai.merchantcard.timescard.exposure.dto.CardSummaryBarDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * <AUTHOR>
 * @create 2025/3/10 15:35
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TimesCardResult extends FetcherReturnValueDTO {
    private CardSummaryBarDTO cardSummaryBarDTO;
}
