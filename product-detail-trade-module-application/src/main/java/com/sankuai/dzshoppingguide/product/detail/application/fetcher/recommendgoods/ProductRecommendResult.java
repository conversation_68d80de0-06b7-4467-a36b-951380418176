package com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommendgoods;

import com.google.common.collect.Maps;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommendgoods.dtos.ProductM;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2025/5/21 14:34
 */
@Data
public class ProductRecommendResult extends FetcherReturnValueDTO {
    /**
     * 推荐的商品id
     */
    private List<ProductM> products;

    /**
     * 推荐的商品Id对应的门店Id的映射
     */
    Map<Long, Long> dealId2ShopId = Maps.newHashMap();

    /**
     * 该组商品总数
     */
    private int total;

    /**
     * 是否还有商品
     */
    private boolean hasNext;

}
