package com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import lombok.EqualsAndHashCode;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * @Author: guangyujie
 * @Date: 2025/3/12 14:43
 */
@EqualsAndHashCode(callSuper = true)
@Getter
public class OrderPageUrlResult extends FetcherReturnValueDTO {

    private final String url;

    private final Map<String, String> extParam;

    public OrderPageUrlResult(final String url) {
        this.url = url;
        this.extParam = new HashMap<>();
    }

    public OrderPageUrlResult(final String url, final Map<String, String> extParam) {
        this.url = url;
        this.extParam = Optional.ofNullable(extParam).orElse(new HashMap<>());
    }

}
