package com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.component.trade.block.banner;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dzshoppingguide.product.detail.application.model.MemberFreeConfig;
import com.sankuai.dzshoppingguide.product.detail.application.utils.LionConfigUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.banner.BottomBarTopBannerVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.banner.enums.BottomBarBannerTypeEnums;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.rich.content.TextRichContentVO;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.RequestSourceEnum;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import com.google.common.collect.Lists;
import com.sankuai.dzshoppingguide.product.detail.application.constants.Constants;
import com.sankuai.dzshoppingguide.product.detail.application.model.CountrySubsidiesConfig;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.backgroud.BottomBarBackgroundVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.rich.content.PicRichContentVO;
import java.util.Arrays;
import java.util.Collections;
import org.apache.commons.lang3.StringUtils;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@ExtendWith(MockitoExtension.class)
class ButtonBannerComponentTest {

    /**
     * 测试当pageSource不是ZDXHYMD时返回null
     */
    @Test
    void testBuildZdxhymdButtonBanner_WhenPageSourceNotMatch_ShouldReturnNull() throws Throwable {
        // arrange
        String invalidPageSource = "invalid_source";
        // act
        BottomBarTopBannerVO result = ButtonBannerComponent.buildZdxhymdButtonBanner(invalidPageSource);
        // assert
        assertNull(result);
    }

    /**
     * 测试当pageSource为null时返回null
     */
    @Test
    void testBuildZdxhymdButtonBanner_WhenPageSourceIsNull_ShouldReturnNull() throws Throwable {
        // arrange & act
        BottomBarTopBannerVO result = ButtonBannerComponent.buildZdxhymdButtonBanner(null);
        // assert
        assertNull(result);
    }

    /**
     * 测试当pageSource为空字符串时返回null
     */
    @Test
    void testBuildZdxhymdButtonBanner_WhenPageSourceIsEmpty_ShouldReturnNull() throws Throwable {
        // arrange & act
        BottomBarTopBannerVO result = ButtonBannerComponent.buildZdxhymdButtonBanner("");
        // assert
        assertNull(result);
    }

    /**
     * 测试当pageSource是ZDXHYMD时的基本行为
     * 由于无法mock静态方法，只测试基本属性
     */
    @Test
    void testBuildZdxhymdButtonBanner_WhenPageSourceMatch_ShouldReturnBasicBanner() throws Throwable {
        // arrange
        String validPageSource = RequestSourceEnum.ZDXHYMD.getSource();
        // act
        BottomBarTopBannerVO result = ButtonBannerComponent.buildZdxhymdButtonBanner(validPageSource);
        // assert
        assertNotNull(result, "Banner should not be null for valid page source");
        assertEquals(BottomBarBannerTypeEnums.COMMON_TYPE.getCode(), result.getBannerType(), "Banner type should match COMMON_TYPE");
        assertNotNull(result.getBackground(), "Background should not be null");
    }

    /**
     * 测试当pageSource是ZDXHYMD时的集成行为
     */
    @Test
    void testBuildZdxhymdButtonBanner_WhenPageSourceMatch_ShouldReturnIntegratedBanner() throws Throwable {
        // arrange
        String validPageSource = RequestSourceEnum.ZDXHYMD.getSource();
        // act
        BottomBarTopBannerVO result = ButtonBannerComponent.buildZdxhymdButtonBanner(validPageSource);
        // assert
        assertNotNull(result, "Banner should not be null");
        assertEquals(BottomBarBannerTypeEnums.COMMON_TYPE.getCode(), result.getBannerType(), "Banner type should be COMMON_TYPE");
        assertNotNull(result.getBackground(), "Background should not be null");
        assertNotNull(result.getBannerData(), "Banner data should not be null");
    }

    /**
     * 测试banner数据的完整性
     */
    @Test
    void testBuildZdxhymdButtonBanner_WhenPageSourceMatch_ShouldHaveValidBannerData() throws Throwable {
        // arrange
        String validPageSource = RequestSourceEnum.ZDXHYMD.getSource();
        // act
        BottomBarTopBannerVO result = ButtonBannerComponent.buildZdxhymdButtonBanner(validPageSource);
        // assert
        assertNotNull(result, "Banner should not be null");
        assertNotNull(result.getBannerData(), "Banner data should not be null");
        assertTrue(result.getBannerData().size() <= 1, "Banner data should have at most one item");
        if (!result.getBannerData().isEmpty()) {
            assertTrue(result.getBannerData().get(0) instanceof TextRichContentVO, "Banner data should contain TextRichContentVO");
        }
    }

    /**
     * 测试banner背景的完整性
     */
    @Test
    void testBuildZdxhymdButtonBanner_WhenPageSourceMatch_ShouldHaveValidBackground() throws Throwable {
        // arrange
        String validPageSource = RequestSourceEnum.ZDXHYMD.getSource();
        // act
        BottomBarTopBannerVO result = ButtonBannerComponent.buildZdxhymdButtonBanner(validPageSource);
        // assert
        assertNotNull(result, "Banner should not be null");
        assertNotNull(result.getBackground(), "Background should not be null");
        assertNotNull(result.getBackground().getColors(), "Background colors should not be null");
        assertEquals(2, result.getBackground().getColors().size(), "Background should have exactly two colors");
    }

    @Test
    void testBuildCountrySubsidiesButtonBanner_InvalidFlowFlag() throws Throwable {
        // arrange
        String invalidFlowFlag = "INVALID_FLAG";
        int gpsCityId = 123;
        boolean isMt = true;
        // act
        BottomBarTopBannerVO result = ButtonBannerComponent.buildCountrySubsidiesButtonBanner(invalidFlowFlag, gpsCityId, isMt);
        // assert
        assertNull(result);
    }

    @Test
    void testBuildCountrySubsidiesButtonBanner_NullFlowFlag() throws Throwable {
        // arrange
        int gpsCityId = 123;
        boolean isMt = true;
        // act
        BottomBarTopBannerVO result = ButtonBannerComponent.buildCountrySubsidiesButtonBanner(null, gpsCityId, isMt);
        // assert
        assertNull(result);
    }

    @Test
    void testBuildCountrySubsidiesButtonBanner_CityNotInSubsidiesList_MtCase() throws Throwable {
        // arrange
        String flowFlag = Constants.COUNTRY_SUBSIDIES;
        int gpsCityId = 123;
        boolean isMt = true;
        CountrySubsidiesConfig config = new CountrySubsidiesConfig();
        config.setMtCityIds(Arrays.asList(456));
        config.setDpCityIds(Arrays.asList(789));
        // act
        BottomBarTopBannerVO result = ButtonBannerComponent.buildCountrySubsidiesButtonBanner(flowFlag, gpsCityId, isMt);
        // assert
        assertNotNull(result);
        assertEquals(BottomBarBannerTypeEnums.COMMON_TYPE.getCode(), result.getBannerType());
        assertNotNull(result.getBackground());
        assertEquals(5, result.getBannerData().size());
    }

    @Test
    void testBuildCountrySubsidiesButtonBanner_CityNotInSubsidiesList_DpCase() throws Throwable {
        // arrange
        String flowFlag = Constants.COUNTRY_SUBSIDIES;
        int gpsCityId = 123;
        boolean isMt = false;
        CountrySubsidiesConfig config = new CountrySubsidiesConfig();
        config.setMtCityIds(Arrays.asList(456));
        config.setDpCityIds(Arrays.asList(789));
        // act
        BottomBarTopBannerVO result = ButtonBannerComponent.buildCountrySubsidiesButtonBanner(flowFlag, gpsCityId, isMt);
        // assert
        assertNotNull(result);
        assertEquals(BottomBarBannerTypeEnums.COMMON_TYPE.getCode(), result.getBannerType());
        assertNotNull(result.getBackground());
        assertEquals(5, result.getBannerData().size());
    }

    @Test
    void testBuildCountrySubsidiesButtonBanner_NullConfig() throws Throwable {
        // arrange
        String flowFlag = Constants.COUNTRY_SUBSIDIES;
        int gpsCityId = 123;
        boolean isMt = true;
        // act
        BottomBarTopBannerVO result = ButtonBannerComponent.buildCountrySubsidiesButtonBanner(flowFlag, gpsCityId, isMt);
        // assert
        assertNotNull(result);
        assertEquals(BottomBarBannerTypeEnums.COMMON_TYPE.getCode(), result.getBannerType());
    }

    @Test
    void testBuildCountrySubsidiesButtonBanner_NullCityLists() throws Throwable {
        // arrange
        String flowFlag = Constants.COUNTRY_SUBSIDIES;
        int gpsCityId = 123;
        boolean isMt = true;
        CountrySubsidiesConfig config = new CountrySubsidiesConfig();
        config.setMtCityIds(null);
        config.setDpCityIds(null);
        // act
        BottomBarTopBannerVO result = ButtonBannerComponent.buildCountrySubsidiesButtonBanner(flowFlag, gpsCityId, isMt);
        // assert
        assertNotNull(result);
        assertEquals(BottomBarBannerTypeEnums.COMMON_TYPE.getCode(), result.getBannerType());
    }

    @Test
    void testBuildCountrySubsidiesButtonBanner_EmptyCityLists() throws Throwable {
        // arrange
        String flowFlag = Constants.COUNTRY_SUBSIDIES;
        int gpsCityId = 123;
        boolean isMt = true;
        CountrySubsidiesConfig config = new CountrySubsidiesConfig();
        config.setMtCityIds(Collections.emptyList());
        config.setDpCityIds(Collections.emptyList());
        // act
        BottomBarTopBannerVO result = ButtonBannerComponent.buildCountrySubsidiesButtonBanner(flowFlag, gpsCityId, isMt);
        // assert
        assertNotNull(result);
        assertEquals(BottomBarBannerTypeEnums.COMMON_TYPE.getCode(), result.getBannerType());
    }
}
