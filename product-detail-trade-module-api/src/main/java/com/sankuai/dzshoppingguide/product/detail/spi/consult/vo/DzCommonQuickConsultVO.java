package com.sankuai.dzshoppingguide.product.detail.spi.consult.vo;

import com.dianping.mobile.framework.annotation.MobileDo;

import java.io.Serializable;
import java.util.List;

@MobileDo(id = 0x38df)
public class DzCommonQuickConsultVO implements Serializable {
    /**
    * 是否命中实验组
    */
    @MobileDo.MobileField(key = 0x93b8)
    private boolean hitBExp;

    /**
    * 斗斛ab实验数据
    */
    @MobileDo.MobileField(key = 0xbda)
    private String abConfig;

    /**
    * 是否展示
    */
    @MobileDo.MobileField(key = 0xdac8)
    private boolean show;

    /**
    * 子标题
    */
    @MobileDo.MobileField(key = 0xd894)
    private String subTitle;

    /**
    * 标题
    */
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    /**
    * 问题入口数组
    */
    @MobileDo.MobileField(key = 0x4ba3)
    private List<DzCommomQuickConsultQuestionVO> questionEntryVOList;

    /**
    * 延迟展示时间
    */
    @MobileDo.MobileField(key = 0xe2fa)
    private int delayTime;

    /**
    * 快捷咨询展示类型
    */
    @MobileDo.MobileField(key = 0xe2a1)
    private int consultType;

    /**
    * 展示持续时间
    */
    @MobileDo.MobileField(key = 0xc881)
    private int displayTime;

    /**
    * 是否展示底bar-IM咨询按钮（仅团详页使用）
    */
    @MobileDo.MobileField(key = 0xc064)
    private boolean showIMBarButton;

    /**
    * 是否展示浮球-IM咨询按钮（仅团详页使用）
    */
    @MobileDo.MobileField(key = 0xebb9)
    private boolean showIMFloatButton;

    /**
    * 是否展示种植牙快捷咨询浮窗（仅团详页使用）
    */
    @MobileDo.MobileField(key = 0x8ec6)
    private boolean showDentalImplantConsult;

    /**
    * 是否展示医疗专科快捷咨询浮窗（仅团详页使用）
    */
    @MobileDo.MobileField(key = 0xb1c8)
    private boolean showMedicalSpecialistConsult;

    /**
    * 是否展示小美问问入口按钮
    */
    @MobileDo.MobileField(key = 0x3153)
    private boolean showAssistantButton;

    /**
    * ab实验数据详情，map类型，key为不同功能，value为功能对应实验数据信息。key=assistant表示小美问问对应ab实验数据
    */
    @MobileDo.MobileField(key = 0x1a8c)
    private String abConfigDetail;

    /**
    * 是否展示LE热门问题浮层
    */
    @MobileDo.MobileField(key = 0x3737)
    private boolean showLEHotQuestionFloat;

    public boolean getHitBExp() {
        return hitBExp;
    }

    public void setHitBExp(boolean hitBExp) {
        this.hitBExp = hitBExp;
    }

    public String getAbConfig() {
        return abConfig;
    }

    public void setAbConfig(String abConfig) {
        this.abConfig = abConfig;
    }

    public boolean getShow() {
        return show;
    }

    public void setShow(boolean show) {
        this.show = show;
    }

    public String getSubTitle() {
        return subTitle;
    }

    public void setSubTitle(String subTitle) {
        this.subTitle = subTitle;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public List<DzCommomQuickConsultQuestionVO> getQuestionEntryVOList() {
        return questionEntryVOList;
    }

    public void setQuestionEntryVOList(
        List<DzCommomQuickConsultQuestionVO> questionEntryVOList) {
        this.questionEntryVOList = questionEntryVOList;
    }

    public int getDelayTime() {
        return delayTime;
    }

    public void setDelayTime(int delayTime) {
        this.delayTime = delayTime;
    }

    public int getConsultType() {
        return consultType;
    }

    public void setConsultType(int consultType) {
        this.consultType = consultType;
    }

    public int getDisplayTime() {
        return displayTime;
    }

    public void setDisplayTime(int displayTime) {
        this.displayTime = displayTime;
    }

    public boolean getShowIMBarButton() {
        return showIMBarButton;
    }

    public void setShowIMBarButton(boolean showIMBarButton) {
        this.showIMBarButton = showIMBarButton;
    }

    public boolean getShowIMFloatButton() {
        return showIMFloatButton;
    }

    public void setShowIMFloatButton(boolean showIMFloatButton) {
        this.showIMFloatButton = showIMFloatButton;
    }

    public boolean getShowDentalImplantConsult() {
        return showDentalImplantConsult;
    }

    public void setShowDentalImplantConsult(boolean showDentalImplantConsult) {
        this.showDentalImplantConsult = showDentalImplantConsult;
    }

    public boolean getShowMedicalSpecialistConsult() {
        return showMedicalSpecialistConsult;
    }

    public void setShowMedicalSpecialistConsult(
        boolean showMedicalSpecialistConsult) {
        this.showMedicalSpecialistConsult = showMedicalSpecialistConsult;
    }

    public boolean getShowAssistantButton() {
        return showAssistantButton;
    }

    public void setShowAssistantButton(boolean showAssistantButton) {
        this.showAssistantButton = showAssistantButton;
    }

    public String getAbConfigDetail() {
        return abConfigDetail;
    }

    public void setAbConfigDetail(String abConfigDetail) {
        this.abConfigDetail = abConfigDetail;
    }

    public boolean getShowLEHotQuestionFloat() {
        return showLEHotQuestionFloat;
    }

    public void setShowLEHotQuestionFloat(boolean showLEHotQuestionFloat) {
        this.showLEHotQuestionFloat = showLEHotQuestionFloat;
    }
}