package com.sankuai.dzshoppingguide.product.detail.application.utils;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.sankuai.dzshoppingguide.product.detail.application.utils.model.PlayActivityModel;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class PlayCenterWrapperConvertPlayActivityModelMapTest {

    /**
     * Test convertPlayActivityModelMap with empty input
     */
    @Test
    public void testConvertPlayActivityModelMapEmptyInput() throws Throwable {
        // arrange
        String emptyInput = "";
        // act
        Map<Long, List<PlayActivityModel>> result = PlayCenterWrapper.convertPlayActivityModelMap(emptyInput);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test convertPlayActivityModelMap with null input
     */
    @Test
    public void testConvertPlayActivityModelMapNullInput() throws Throwable {
        // arrange
        String nullInput = null;
        // act
        Map<Long, List<PlayActivityModel>> result = PlayCenterWrapper.convertPlayActivityModelMap(nullInput);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test convertPlayActivityModelMap with valid input containing a single key
     */
    @Test
    public void testConvertPlayActivityModelMapValidSingleKey() throws Throwable {
        // arrange
        String input = "{\"123\":[{\"playInfo\":{" + "\"title\":\"Test Title\"," + "\"subTitle\":\"Test Subtitle\"," + "\"startTime\":1625097600000," + "\"endTime\":1625184000000," + "\"activityId\":456," + "\"materialInfoList\":[]," + "\"taskInfoList\":[]" + "}}]}";
        // act
        Map<Long, List<PlayActivityModel>> result = PlayCenterWrapper.convertPlayActivityModelMap(input);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertTrue(result.containsKey(123L));
        List<PlayActivityModel> models = result.get(123L);
        assertNotNull(models);
        assertEquals(1, models.size());
        PlayActivityModel model = models.get(0);
        assertNotNull(model.getPlayInfo());
        assertEquals("Test Title", model.getPlayInfo().getTitle());
        assertEquals("Test Subtitle", model.getPlayInfo().getSubTitle());
        assertEquals(456L, model.getPlayInfo().getActivityId());
    }

    /**
     * Test convertPlayActivityModelMap with valid input containing multiple keys
     */
    @Test
    public void testConvertPlayActivityModelMapValidMultipleKeys() throws Throwable {
        // arrange
        String input = "{" + "\"123\":[{\"playInfo\":{" + "\"title\":\"Title 1\"," + "\"subTitle\":\"Subtitle 1\"," + "\"startTime\":1625097600000," + "\"endTime\":1625184000000," + "\"activityId\":456," + "\"materialInfoList\":[]," + "\"taskInfoList\":[]" + "}}]," + "\"789\":[{\"playInfo\":{" + "\"title\":\"Title 2\"," + "\"subTitle\":\"Subtitle 2\"," + "\"startTime\":1625097600000," + "\"endTime\":1625184000000," + "\"activityId\":101112," + "\"materialInfoList\":[]," + "\"taskInfoList\":[]" + "}}]" + "}";
        // act
        Map<Long, List<PlayActivityModel>> result = PlayCenterWrapper.convertPlayActivityModelMap(input);
        // assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.containsKey(123L));
        assertTrue(result.containsKey(789L));
        List<PlayActivityModel> models1 = result.get(123L);
        assertNotNull(models1);
        assertEquals(1, models1.size());
        assertEquals("Title 1", models1.get(0).getPlayInfo().getTitle());
        assertEquals(456L, models1.get(0).getPlayInfo().getActivityId());
        List<PlayActivityModel> models2 = result.get(789L);
        assertNotNull(models2);
        assertEquals(1, models2.size());
        assertEquals("Title 2", models2.get(0).getPlayInfo().getTitle());
        assertEquals(101112L, models2.get(0).getPlayInfo().getActivityId());
    }

    /**
     * Test convertPlayActivityModelMap with invalid JSON input
     */
    @Test
    public void testConvertPlayActivityModelMapInvalidJson() throws Throwable {
        // arrange
        String invalidInput = "{invalid json}";
        // act
        Map<Long, List<PlayActivityModel>> result = PlayCenterWrapper.convertPlayActivityModelMap(invalidInput);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }
}
