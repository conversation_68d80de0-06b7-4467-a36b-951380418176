package com.sankuai.dzshoppingguide.product.detail.application.builder.pricebar.builder;

import static org.junit.Assert.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import static org.powermock.api.mockito.PowerMockito.when;
import com.dianping.lion.client.Lion;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import com.sankuai.dealuser.price.display.api.model.PromoIdentity;
import com.sankuai.dzshoppingguide.product.detail.application.builder.pricebar.builder.enums.CouponGuideTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.pricebar.component.InflateCouponTipsComponent;
import com.sankuai.dzshoppingguide.product.detail.application.builder.pricebar.config.IconConfig;
import com.sankuai.dzshoppingguide.product.detail.domain.utils.LionConfigUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.Icon;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.ProductBestPromoFormula;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.PromoTagEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.pricebar.vo.ProductPriceBarModuleVO;
import com.sankuai.nibmkt.promotion.api.common.enums.PromotionPropertyEnum;
import com.sankuai.nibmkt.promotion.api.query.model.magicalmember.response.MMCTooltipTextDTO;
import java.math.BigDecimal;
import java.util.*;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.modules.junit4.PowerMockRunnerDelegate;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(PowerMockRunner.class)
@PowerMockRunnerDelegate(SpringRunner.class)
@PrepareForTest({ Lion.class, InflateCouponTipsComponent.class })
@PowerMockIgnore({ "javax.management.*", "javax.script.*" })
public class AbstractProductBarModuleBuilderBuildBestPromoFormulaTest {

    private TestProductBarModuleBuilder builder;

    private IconConfig iconConfig;

    private static final String TRADE_MODULE_APPKEY = "com.sankuai.dzshoppingguide.detail.trademodule";

    private static final String MAGICAL_MEMBER_COUPON_ICON_CONFIG = "magical.member.coupon.icon.config";

    @Before
    public void setUp() {
        builder = new TestProductBarModuleBuilder();
        iconConfig = new IconConfig();
        iconConfig.setMarketPriceIconText("门市价");
        iconConfig.setDoubleRedPacket("double_red_packet");
        iconConfig.setDoubleRedPacketWidth(10);
        iconConfig.setDoubleRedPacketHeight(10);
        iconConfig.setAfterInflate("after_inflate");
        iconConfig.setAfterInflateWidth(20);
        iconConfig.setAfterInflateHeight(20);
        PowerMockito.mockStatic(Lion.class);
        PowerMockito.mockStatic(InflateCouponTipsComponent.class);
        when(Lion.getBean(eq(TRADE_MODULE_APPKEY), eq(MAGICAL_MEMBER_COUPON_ICON_CONFIG), eq(IconConfig.class))).thenReturn(iconConfig);
    }

    @Test
    public void testBuildBestPromoFormula_EmptyPriceDisplayDTO() throws Throwable {
        // Arrange
        PriceDisplayDTO priceDisplayDTO = new PriceDisplayDTO();
        priceDisplayDTO.setMarketPrice(new BigDecimal("200"));
        priceDisplayDTO.setUsedPromos(new ArrayList<>());
        when(InflateCouponTipsComponent.getMMCTooltipTextDTO(any())).thenReturn(null);
        // Act
        List<ProductBestPromoFormula> result = builder.buildBestPromoFormula(priceDisplayDTO, "100");
        // Assert
        org.junit.Assert.assertEquals(1, result.size());
        org.junit.Assert.assertEquals("200", result.get(0).getPrice());
        org.junit.Assert.assertEquals("门市价", result.get(0).getPriceDesc());
        org.junit.Assert.assertEquals("门市价", result.get(0).getPromoDesc());
    }

    @Test
    public void testBuildBestPromoFormula_NullMarketPrice() throws Throwable {
        // Arrange
        PriceDisplayDTO priceDisplayDTO = new PriceDisplayDTO();
        priceDisplayDTO.setMarketPrice(null);
        priceDisplayDTO.setUsedPromos(new ArrayList<>());
        when(InflateCouponTipsComponent.getMMCTooltipTextDTO(any())).thenReturn(null);
        // Act
        List<ProductBestPromoFormula> result = builder.buildBestPromoFormula(priceDisplayDTO, "100");
        // Assert
        org.junit.Assert.assertEquals(1, result.size());
        org.junit.Assert.assertNull(result.get(0).getPrice());
    }

    @Test
    public void testBuildBestPromoFormula_NormalPromo() throws Throwable {
        // Arrange
        PriceDisplayDTO priceDisplayDTO = new PriceDisplayDTO();
        priceDisplayDTO.setMarketPrice(new BigDecimal("200"));
        PromoDTO promoDTO = new PromoDTO();
        PromoIdentity identity = new PromoIdentity(123L, 1);
        identity.setPromoShowType("DEAL_PROMO");
        identity.setPromoTypeDesc("团购优惠");
        promoDTO.setIdentity(identity);
        promoDTO.setAmount(new BigDecimal("50"));
        priceDisplayDTO.setUsedPromos(Collections.singletonList(promoDTO));
        when(InflateCouponTipsComponent.getMMCTooltipTextDTO(any())).thenReturn(null);
        // Act
        List<ProductBestPromoFormula> result = builder.buildBestPromoFormula(priceDisplayDTO, "150");
        // Assert
        org.junit.Assert.assertEquals(2, result.size());
        org.junit.Assert.assertEquals("50", result.get(1).getPrice());
        org.junit.Assert.assertEquals("团购优惠", result.get(1).getPromoDesc());
    }

    @Test
    public void testBuildBestPromoFormula_MagicalMemberCoupon() throws Throwable {
        // Arrange
        PriceDisplayDTO priceDisplayDTO = new PriceDisplayDTO();
        priceDisplayDTO.setMarketPrice(new BigDecimal("200"));
        PromoDTO promoDTO = new PromoDTO();
        PromoIdentity identity = new PromoIdentity(123L, 1);
        identity.setPromoShowType(PromoTagEnum.MAGICAL_MEMBER_PLATFORM_COUPON.getType());
        identity.setPromoTypeDesc("神券优惠");
        promoDTO.setIdentity(identity);
        promoDTO.setAmount(new BigDecimal("50"));
        Map<String, String> otherInfoMap = new HashMap<>();
        otherInfoMap.put(PromotionPropertyEnum.AFTER_INFLATE.getValue(), "true");
        promoDTO.setPromotionOtherInfoMap(otherInfoMap);
        List<PromoDTO> usedPromos = Collections.singletonList(promoDTO);
        priceDisplayDTO.setUsedPromos(usedPromos);
        // Mock InflateCouponTipsComponent
        MMCTooltipTextDTO mmcTooltipTextDTO = new MMCTooltipTextDTO();
        mmcTooltipTextDTO.setPreInflateDiscountAmount("30");
        mmcTooltipTextDTO.setGuideType(1);
        when(InflateCouponTipsComponent.getMMCTooltipTextDTO(any())).thenReturn(mmcTooltipTextDTO);
        when(InflateCouponTipsComponent.getCouponGuideTypeEnum(eq(usedPromos), any(BigDecimal.class), eq(mmcTooltipTextDTO))).thenReturn(CouponGuideTypeEnum.GUIDE_INFLATE);
        // Act
        List<ProductBestPromoFormula> result = builder.buildBestPromoFormula(priceDisplayDTO, "150");
        // Assert
        org.junit.Assert.assertEquals(2, result.size());
        org.junit.Assert.assertTrue(result.get(1).isAfterInflate());
        org.junit.Assert.assertEquals("#FF6633", result.get(1).getPriceColor());
    }

    @Test
    public void testBuildBestPromoFormula_MultiplePromos() throws Throwable {
        // Arrange
        PriceDisplayDTO priceDisplayDTO = new PriceDisplayDTO();
        priceDisplayDTO.setMarketPrice(new BigDecimal("200"));
        List<PromoDTO> promos = new ArrayList<>();
        PromoDTO dealPromo = new PromoDTO();
        PromoIdentity dealIdentity = new PromoIdentity(123L, 1);
        dealIdentity.setPromoShowType("DEAL_PROMO");
        dealIdentity.setPromoTypeDesc("团购优惠");
        dealPromo.setIdentity(dealIdentity);
        dealPromo.setAmount(new BigDecimal("30"));
        promos.add(dealPromo);
        PromoDTO platformCoupon = new PromoDTO();
        PromoIdentity platformIdentity = new PromoIdentity(456L, 2);
        platformIdentity.setPromoShowType("PLATFORM_COUPON");
        platformIdentity.setPromoTypeDesc("美团券");
        platformCoupon.setIdentity(platformIdentity);
        platformCoupon.setAmount(new BigDecimal("20"));
        promos.add(platformCoupon);
        priceDisplayDTO.setUsedPromos(promos);
        when(InflateCouponTipsComponent.getMMCTooltipTextDTO(any())).thenReturn(null);
        // Act
        List<ProductBestPromoFormula> result = builder.buildBestPromoFormula(priceDisplayDTO, "150");
        // Assert
        org.junit.Assert.assertEquals(3, result.size());
        org.junit.Assert.assertEquals("团购优惠", result.get(1).getPromoDesc());
        org.junit.Assert.assertEquals("美团券", result.get(2).getPromoDesc());
    }

    @Test
    public void testBuildBestPromoFormula_InvalidPromoData() throws Throwable {
        // Arrange
        PriceDisplayDTO priceDisplayDTO = new PriceDisplayDTO();
        priceDisplayDTO.setMarketPrice(new BigDecimal("200"));
        PromoDTO promoDTO = new PromoDTO();
        promoDTO.setAmount(new BigDecimal("50"));
        priceDisplayDTO.setUsedPromos(Collections.singletonList(promoDTO));
        when(InflateCouponTipsComponent.getMMCTooltipTextDTO(any())).thenReturn(null);
        // Act
        List<ProductBestPromoFormula> result = builder.buildBestPromoFormula(priceDisplayDTO, "150");
        // Assert
        org.junit.Assert.assertEquals(1, result.size());
    }

    private static class TestProductBarModuleBuilder extends AbstractProductBarModuleBuilder<ProductPriceBarModuleVO> {

        @Override
        public ProductPriceBarModuleVO doBuild() {
            return null;
        }
    }
}
