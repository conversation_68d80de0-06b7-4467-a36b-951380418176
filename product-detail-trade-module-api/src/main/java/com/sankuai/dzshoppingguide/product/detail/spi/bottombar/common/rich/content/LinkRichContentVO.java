package com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.rich.content;

import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.rich.content.enums.RichContentTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author: g<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025/3/16 15:16
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class LinkRichContentVO extends RichContentVO {

    private String url;

    @Override
    public int getType() {
        return RichContentTypeEnum.LINK.getCode();
    }
}
