package com.sankuai.dzshoppingguide.product.detail.application.fetcher.price;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dealuser.price.display.api.model.ClientEnv;
import com.sankuai.dealuser.price.display.api.model.ProductIdentity;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.membercard.card.integration.IntegrationMemberCard;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.dto.ProductPriceReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.service.dto.ShopMemberDetailV;
import com.sankuai.mpmctmember.query.common.enums.MemberChargeTypeEnum;
import com.sankuai.mpmctmember.query.common.enums.MemberInterestCodeEnum;
import com.sankuai.mpmctmember.query.common.enums.UserMemberTypeEnum;
import com.sankuai.mpmctmember.query.thrift.dto.MemberInterestDTO;
import com.sankuai.mpmctmember.query.thrift.dto.MemberInterestDetailDTO;
import com.sankuai.mpmctmember.query.thrift.dto.MemberInterestUseRuleDTO;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class AbstractProductPriceFetcherNeedCompareTest {

    @InjectMocks
    private TestableAbstractProductPriceFetcher fetcher;

    /**
     * Helper method to create MemberInterestDetailDTO
     */
    private MemberInterestDetailDTO createMemberInterestDetail(boolean isRegularMember, boolean isNewMember) {
        MemberInterestDetailDTO interestDetail = new MemberInterestDetailDTO();
        MemberInterestDTO interest = new MemberInterestDTO();
        interest.setInterestCode(MemberInterestCodeEnum.PLATFORM_PRODUCT_PROMO.getCode());
        MemberInterestUseRuleDTO useRule = new MemberInterestUseRuleDTO();
        if (isRegularMember && !isNewMember) {
            useRule.setTargetMemberTypes(Arrays.asList(UserMemberTypeEnum.OLD_MEMBER.getCode(), UserMemberTypeEnum.NEW_MEMBER.getCode()));
        } else if (isNewMember && !isRegularMember) {
            useRule.setTargetMemberTypes(Collections.singletonList(UserMemberTypeEnum.NEW_MEMBER.getCode()));
        }
        interest.setInterestUseRule(useRule);
        interestDetail.setInterests(Collections.singletonList(interest));
        return interestDetail;
    }

    /**
     * Test case when integrationMemberCard is null
     */
    @Test
    public void testNeedCompare_WhenIntegrationMemberCardIsNull() throws Throwable {
        // act
        boolean result = fetcher.needCompare(null);
        // assert
        assertTrue(result);
    }

    /**
     * Test case when memberCardInterest is null
     */
    @Test
    public void testNeedCompare_WhenMemberCardInterestIsNull() throws Throwable {
        // arrange
        IntegrationMemberCard card = new IntegrationMemberCard();
        // act
        boolean result = fetcher.needCompare(card);
        // assert
        assertTrue(result);
    }

    /**
     * Test case when product has no member price
     */
    @Test
    public void testNeedCompare_WhenProductHasNoMemberPrice() throws Throwable {
        // arrange
        IntegrationMemberCard card = new IntegrationMemberCard();
        ShopMemberDetailV memberDetail = new ShopMemberDetailV();
        MemberInterestDetailDTO interestDetail = new MemberInterestDetailDTO();
        interestDetail.setInterests(new ArrayList<>());
        memberDetail.setOriginalResult(interestDetail);
        card.setMemberCardInterest(memberDetail);
        // act
        boolean result = fetcher.needCompare(card);
        // assert
        assertFalse(result);
    }

    /**
     * Test case for charge type member card but user is not a member
     */
    @Test
    public void testNeedCompare_WhenChargeTypeAndNotMember() throws Throwable {
        // arrange
        IntegrationMemberCard card = new IntegrationMemberCard();
        card.setMemberCardType(MemberChargeTypeEnum.CHARGE.getCode());
        ShopMemberDetailV memberDetail = new ShopMemberDetailV();
        memberDetail.setMember(false);
        MemberInterestDetailDTO interestDetail = createMemberInterestDetail(true, false);
        memberDetail.setOriginalResult(interestDetail);
        card.setMemberCardInterest(memberDetail);
        // act
        boolean result = fetcher.needCompare(card);
        // assert
        assertFalse(result);
    }

    /**
     * Test case for regular member price product with valid member
     */
    @Test
    public void testNeedCompare_WhenRegularMemberPriceWithValidMember() throws Throwable {
        // arrange
        IntegrationMemberCard card = new IntegrationMemberCard();
        card.setMemberCardType(MemberChargeTypeEnum.FREE.getCode());
        ShopMemberDetailV memberDetail = new ShopMemberDetailV();
        memberDetail.setMember(true);
        MemberInterestDetailDTO interestDetail = createMemberInterestDetail(true, false);
        memberDetail.setOriginalResult(interestDetail);
        card.setMemberCardInterest(memberDetail);
        // act
        boolean result = fetcher.needCompare(card);
        // assert
        assertTrue(result);
    }

    /**
     * Test case for new member price product when user is already a member
     */
    @Test
    public void testNeedCompare_WhenNewMemberPriceAndUserIsMember() throws Throwable {
        // arrange
        IntegrationMemberCard card = new IntegrationMemberCard();
        ShopMemberDetailV memberDetail = new ShopMemberDetailV();
        memberDetail.setMember(true);
        MemberInterestDetailDTO interestDetail = createMemberInterestDetail(false, true);
        memberDetail.setOriginalResult(interestDetail);
        card.setMemberCardInterest(memberDetail);
        // act
        boolean result = fetcher.needCompare(card);
        // assert
        assertFalse(result);
    }

    /**
     * Test case for new member price product when user is not a member
     */
    @Test
    public void testNeedCompare_WhenNewMemberPriceAndUserIsNotMember() throws Throwable {
        // arrange
        IntegrationMemberCard card = new IntegrationMemberCard();
        ShopMemberDetailV memberDetail = new ShopMemberDetailV();
        memberDetail.setMember(false);
        MemberInterestDetailDTO interestDetail = createMemberInterestDetail(false, true);
        memberDetail.setOriginalResult(interestDetail);
        card.setMemberCardInterest(memberDetail);
        // act
        boolean result = fetcher.needCompare(card);
        // assert
        assertTrue(result);
    }

    /**
     * Test case for both regular and new member price product
     */
    @Test
    public void testNeedCompare_WhenBothRegularAndNewMemberPrice() throws Throwable {
        // arrange
        IntegrationMemberCard card = new IntegrationMemberCard();
        ShopMemberDetailV memberDetail = new ShopMemberDetailV();
        memberDetail.setMember(false);
        // Create two interests: one for regular members and one for new members
        MemberInterestDTO regularInterest = new MemberInterestDTO();
        regularInterest.setInterestCode(MemberInterestCodeEnum.PLATFORM_PRODUCT_PROMO.getCode());
        MemberInterestUseRuleDTO regularRule = new MemberInterestUseRuleDTO();
        regularRule.setTargetMemberTypes(Arrays.asList(UserMemberTypeEnum.OLD_MEMBER.getCode(), UserMemberTypeEnum.NEW_MEMBER.getCode()));
        regularInterest.setInterestUseRule(regularRule);
        MemberInterestDTO newMemberInterest = new MemberInterestDTO();
        newMemberInterest.setInterestCode(MemberInterestCodeEnum.PLATFORM_PRODUCT_PROMO.getCode());
        MemberInterestUseRuleDTO newMemberRule = new MemberInterestUseRuleDTO();
        newMemberRule.setTargetMemberTypes(Collections.singletonList(UserMemberTypeEnum.NEW_MEMBER.getCode()));
        newMemberInterest.setInterestUseRule(newMemberRule);
        MemberInterestDetailDTO interestDetail = new MemberInterestDetailDTO();
        interestDetail.setInterests(Arrays.asList(regularInterest, newMemberInterest));
        memberDetail.setOriginalResult(interestDetail);
        card.setMemberCardInterest(memberDetail);
        // act
        boolean result = fetcher.needCompare(card);
        // assert
        assertTrue(result);
    }

    /**
     * Test case when originalResult is null
     */
    @Test
    public void testNeedCompare_WhenOriginalResultIsNull() throws Throwable {
        // arrange
        IntegrationMemberCard card = new IntegrationMemberCard();
        ShopMemberDetailV memberDetail = new ShopMemberDetailV();
        memberDetail.setOriginalResult(null);
        card.setMemberCardInterest(memberDetail);
        // act
        boolean result = fetcher.needCompare(card);
        // assert
        assertFalse(result);
    }

    /**
     * Test case when interests list is null
     */
    @Test
    public void testNeedCompare_WhenInterestsListIsNull() throws Throwable {
        // arrange
        IntegrationMemberCard card = new IntegrationMemberCard();
        ShopMemberDetailV memberDetail = new ShopMemberDetailV();
        MemberInterestDetailDTO interestDetail = new MemberInterestDetailDTO();
        interestDetail.setInterests(null);
        memberDetail.setOriginalResult(interestDetail);
        card.setMemberCardInterest(memberDetail);
        // act
        boolean result = fetcher.needCompare(card);
        // assert
        assertFalse(result);
    }

    // Testable implementation of abstract class for testing
    private static class TestableAbstractProductPriceFetcher extends AbstractProductPriceFetcher<ProductPriceReturnValue> {

        @Override
        protected void postProcessPriceRequest(ClientEnv clientEnv, java.util.List<ProductIdentity> productIdentities, java.util.Map<String, String> extension) {
            // Implementation not needed for testing needCompare
        }

        @Override
        protected CompletableFuture<ProductPriceReturnValue> doFetch() {
            // Implementation not needed for testing needCompare
            return null;
        }
    }
}
