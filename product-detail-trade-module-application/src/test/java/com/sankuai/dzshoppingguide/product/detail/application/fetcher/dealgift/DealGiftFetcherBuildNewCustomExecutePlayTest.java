package com.sankuai.dzshoppingguide.product.detail.application.fetcher.dealgift;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import com.sankuai.dz.product.detail.RequestCustomParamEnum;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.enums.MobileOSTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.cityId.CityIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.dealgroup.DealGroupIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapper;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.RequestSourceEnum;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class DealGiftFetcherBuildNewCustomExecutePlayTest {

    @Mock
    private ProductDetailPageRequest request;

    @InjectMocks
    private DealGiftFetcher dealGiftFetcher;

    private DealGroupIdMapper dealGroupIdMapper;

    private ShopIdMapper shopIdMapper;

    private CityIdMapper cityIdMapper;

    @BeforeEach
    void setUp() {
        dealGroupIdMapper = new DealGroupIdMapper(123L, 456L);
        shopIdMapper = new ShopIdMapper(789L, 101L);
        cityIdMapper = new CityIdMapper(1, 2);
    }

    /**
     * Test normal case with iOS and MT mini app platform
     */
    @Test
    public void testBuildNewCustomExecutePlay_NormalCase_IOS_MTWeapp() throws Throwable {
        // arrange
        when(request.getMobileOSType()).thenReturn(MobileOSTypeEnum.IOS);
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.MT_XCX);
        // act
        Map<String, String> result = dealGiftFetcher.buildNewCustomExecutePlay(dealGroupIdMapper, shopIdMapper, cityIdMapper);
        // assert
        assertNotNull(result);
        assertEquals("2", result.get("cityId"));
        // MT_WEAPP id
        assertEquals("5", result.get("platformType"));
        // IOS
        assertEquals("0", result.get("osType"));
        assertTrue(result.containsKey("activeRequestUnit"));
    }

    /**
     * Test normal case with Android and DP mini app platform
     */
    @Test
    public void testBuildNewCustomExecutePlay_NormalCase_Android_DPWeapp() throws Throwable {
        // arrange
        when(request.getMobileOSType()).thenReturn(MobileOSTypeEnum.ANDROID);
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.DP_XCX);
        // act
        Map<String, String> result = dealGiftFetcher.buildNewCustomExecutePlay(dealGroupIdMapper, shopIdMapper, cityIdMapper);
        // assert
        assertNotNull(result);
        assertEquals("2", result.get("cityId"));
        // DP_WEAPP id
        assertEquals("6", result.get("platformType"));
        // Android
        assertEquals("1", result.get("osType"));
        assertTrue(result.containsKey("activeRequestUnit"));
    }

    /**
     * Test case with activity token when page source is NEW_CUSTOMER_ACTIVITY
     */
    @Test
    public void testBuildNewCustomExecutePlay_WithActivityToken() throws Throwable {
        // arrange
        when(request.getMobileOSType()).thenReturn(MobileOSTypeEnum.IOS);
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.MT_APP);
        when(request.getPageSource()).thenReturn(RequestSourceEnum.NEW_CUSTOMER_ACTIVITY.getSource());
        when(request.getCustomParam(RequestCustomParamEnum.extparam)).thenReturn("{\"activityToken\":\"testToken\"}");
        // act
        Map<String, String> result = dealGiftFetcher.buildNewCustomExecutePlay(dealGroupIdMapper, shopIdMapper, cityIdMapper);
        // assert
        assertNotNull(result);
        // MEITUAN_APP id
        assertEquals("1", result.get("platformType"));
        assertEquals("testToken", result.get("activityToken"));
    }

    /**
     * Test case with undefined extParam
     */
    @Test
    public void testBuildNewCustomExecutePlay_UndefinedExtParam() throws Throwable {
        // arrange
        when(request.getMobileOSType()).thenReturn(MobileOSTypeEnum.IOS);
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.DP_APP);
        when(request.getCustomParam(any())).thenReturn("undefined");
        // act
        Map<String, String> result = dealGiftFetcher.buildNewCustomExecutePlay(dealGroupIdMapper, shopIdMapper, cityIdMapper);
        // assert
        assertNotNull(result);
        // DIANPING_APP id
        assertEquals("2", result.get("platformType"));
        assertFalse(result.containsKey("activityToken"));
    }

    /**
     * Test case with empty extParam
     */
    @Test
    public void testBuildNewCustomExecutePlay_EmptyExtParam() throws Throwable {
        // arrange
        when(request.getMobileOSType()).thenReturn(MobileOSTypeEnum.IOS);
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.UNKNOWN);
        when(request.getCustomParam(any())).thenReturn("");
        // act
        Map<String, String> result = dealGiftFetcher.buildNewCustomExecutePlay(dealGroupIdMapper, shopIdMapper, cityIdMapper);
        // assert
        assertNotNull(result);
        // UNKNOWN id
        assertEquals("0", result.get("platformType"));
        assertFalse(result.containsKey("activityToken"));
    }

    /**
     * Test case with unknown platform type
     */
    @Test
    public void testBuildNewCustomExecutePlay_UnknownPlatform() throws Throwable {
        // arrange
        when(request.getMobileOSType()).thenReturn(MobileOSTypeEnum.ANDROID);
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.UNKNOWN);
        // act
        Map<String, String> result = dealGiftFetcher.buildNewCustomExecutePlay(dealGroupIdMapper, shopIdMapper, cityIdMapper);
        // assert
        assertNotNull(result);
        // UNKNOWN id
        assertEquals("0", result.get("platformType"));
        // Android
        assertEquals("1", result.get("osType"));
    }

    /**
     * Test case with activity token but wrong page source (should not include token)
     */
    @Test
    public void testBuildNewCustomExecutePlay_ActivityTokenWrongSource() throws Throwable {
        // arrange
        when(request.getMobileOSType()).thenReturn(MobileOSTypeEnum.IOS);
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.MT_APP);
        when(request.getPageSource()).thenReturn("other_source");
        when(request.getCustomParam(any())).thenReturn("{\"activityToken\":\"testToken\"}");
        // act
        Map<String, String> result = dealGiftFetcher.buildNewCustomExecutePlay(dealGroupIdMapper, shopIdMapper, cityIdMapper);
        // assert
        assertNotNull(result);
        // MEITUAN_APP id
        assertEquals("1", result.get("platformType"));
        assertFalse(result.containsKey("activityToken"));
    }
}
