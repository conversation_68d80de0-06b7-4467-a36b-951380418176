package com.sankuai.dzshoppingguide.product.detail.application.fetcher.membercard.card.integration;

import com.google.common.collect.Lists;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dz.product.detail.gateway.spi.dto.ShepherdGatewayParam;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.enums.ProductTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.dealgroup.DealGroupIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.dealgroup.DealGroupIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.shopid.ShopIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.shopid.ShopIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.membercard.card.entrance.MemberEntranceResult;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.membercard.card.entrance.ShopMemberEntranceFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
import com.sankuai.dzshoppingguide.product.detail.application.service.dto.ShopMemberDetailV;
import com.sankuai.dzshoppingguide.product.detail.application.utils.ClientTypeUtils;
import com.sankuai.mpmctmember.query.common.enums.MemberInterestSubjectTypeEnum;
import com.sankuai.mpmctmember.query.common.enums.PlanAndMemberQueryFieldEnum;
import com.sankuai.mpmctmember.query.common.enums.PlatformClientApplicationEnum;
import com.sankuai.mpmctmember.query.common.enums.PlatformClientOSEnum;
import com.sankuai.mpmctmember.query.thrift.dto.MemberInterestDetailDTO;
import com.sankuai.mpmctmember.query.thrift.dto.QueryPlanAndUserIdentityDetailBySubjectIdReq;
import com.sankuai.mpmctmember.query.thrift.dto.QueryPlanAndUserIdentityDetailResp;
import com.sankuai.mpmctmember.query.thrift.dto.UserClientEnvDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @date 2025-04-12
 * @desc 会员卡基础信息
 */
@Fetcher(previousLayerDependencies = {DealGroupIdMapperFetcher.class, ShopMemberEntranceFetcher.class, ShopIdMapperFetcher.class})
@Slf4j
public abstract class BaseMemberCardFetcher extends NormalFetcherContext<IntegrationMemberCard> {
    @Autowired
    private CompositeAtomService compositeAtomService;

    private static long mtShopId;
    private static long dpShopId;
    private static long dpDealGroupId;

    // 判断是否有商家会员入口
    protected abstract boolean needHasMemberEntrance();
    
    @Override
    protected CompletableFuture<IntegrationMemberCard> doFetch() throws Exception {

        if (request.getProductTypeEnum() != ProductTypeEnum.DEAL) {
            return CompletableFuture.completedFuture(null);
        }

        MemberEntranceResult memberEntranceResult = getDependencyResult(ShopMemberEntranceFetcher.class);
        // 没有商家会员入口,不返回会员卡数据
        if (needHasMemberEntrance() && (memberEntranceResult == null || !memberEntranceResult.isHasEntrance())) {
            return CompletableFuture.completedFuture(null);
        }
    
        DealGroupIdMapper dealGroupIdMapper = getDependencyResult(DealGroupIdMapperFetcher.class);
        ShopIdMapper shopIdMapper = getDependencyResult(ShopIdMapperFetcher.class);
        mtShopId = Optional.ofNullable(shopIdMapper).map(ShopIdMapper::getMtBestShopId).orElse(0L);
        dpShopId = Optional.ofNullable(shopIdMapper).map(ShopIdMapper::getDpBestShopId).orElse(0L);
        dpDealGroupId = Optional.ofNullable(dealGroupIdMapper).map(DealGroupIdMapper::getDpDealGroupId).orElse(0L);

        CompletableFuture<ShopMemberDetailV> memberCardInterestCf = queryMemberCardInterest();
        CompletableFuture<QueryPlanAndUserIdentityDetailResp> memberCardDetailCf = queryMemberCardDetail();

        return CompletableFuture.allOf(memberCardInterestCf, memberCardDetailCf).thenApply(aVoid -> {
            IntegrationMemberCard integrationMemberCard = new IntegrationMemberCard();
            ShopMemberDetailV memberCardInterest = memberCardInterestCf.join();
            int cardType = Optional.ofNullable(memberCardInterest).map(ShopMemberDetailV::getOriginalResult).map(MemberInterestDetailDTO::getChargeType).orElse(0);
            QueryPlanAndUserIdentityDetailResp memberCardDetail = memberCardDetailCf.join();
            integrationMemberCard.setMemberCardType(cardType);
            integrationMemberCard.setMemberCardInterest(memberCardInterest);
            integrationMemberCard.setMemberCardDetail(memberCardDetail);
            return integrationMemberCard;
        });
    }

    private CompletableFuture<ShopMemberDetailV> queryMemberCardInterest() {
        ShepherdGatewayParam shepherdParam = request.getShepherdGatewayParam();
        long userId = request.getClientTypeEnum().isMtClientType() ? request.getMtUserId() : shepherdParam.getMtVirtualUserId();
        return compositeAtomService.getMemberDiscountInfoList(userId, ClientTypeUtils.genPlatform(request.getClientTypeEnum()), Lists.newArrayList(dpDealGroupId), true).thenApply(res -> {
            if (res == null || MapUtils.isEmpty(res.getDpDealId_shopMemberDetail_map())) {
                return null;
            }
            Map<Long, ShopMemberDetailV> resultMap = res.getDpDealId_shopMemberDetail_map();
            ShopMemberDetailV shopMemberDetailV = resultMap.get(dpDealGroupId);
            if (shopMemberDetailV == null || shopMemberDetailV.getOriginalResult() == null) {
                return null;
            }
            return shopMemberDetailV;
        });
    }

    private CompletableFuture<QueryPlanAndUserIdentityDetailResp> queryMemberCardDetail() {
        return compositeAtomService.queryFreeMemberCardDetail(buildRequest());
    }

    private QueryPlanAndUserIdentityDetailBySubjectIdReq buildRequest() {
        QueryPlanAndUserIdentityDetailBySubjectIdReq req = new QueryPlanAndUserIdentityDetailBySubjectIdReq();
        ClientTypeEnum clientTypeEnum = request.getClientTypeEnum();
        boolean isMt = clientTypeEnum.isMtClientType();
        req.setSubjectId(dpDealGroupId);
        req.setSubjectType(MemberInterestSubjectTypeEnum.DP_DEAL_GROUP.getType());
        req.setDpShopId(dpShopId);
        req.setMtShopId(mtShopId);
        req.setMtUserId(isMt ? request.getMtUserId() : request.getShepherdGatewayParam().getMtVirtualUserId());
        req.setQueryChannel("DEAL_GROUP_DETAIL");
        req.setPlanAndMemberQueryFieldEnumList(Lists.newArrayList(PlanAndMemberQueryFieldEnum.PLAN_BASIC_INFO,
                PlanAndMemberQueryFieldEnum.MEMBER_COUNT, PlanAndMemberQueryFieldEnum.MEMBER_BASIC_INFO));

        UserClientEnvDTO userClientEnvDTO = new UserClientEnvDTO();
        userClientEnvDTO.setPlatformClient(ClientTypeUtils.genPlatform(clientTypeEnum).getPlatform());
        userClientEnvDTO.setPlatformClientApplication(PlatformClientApplicationEnum.NATIVE.getCode());
        userClientEnvDTO
                .setPlatformClientOS(ClientTypeUtils.isAndroid(request.getMobileOSType().getCode())
                        ? PlatformClientOSEnum.ANDROID.getCode() : PlatformClientOSEnum.IOS.getCode());
        req.setUserClientEnvDTO(userClientEnvDTO);
        return req;
    }
}
