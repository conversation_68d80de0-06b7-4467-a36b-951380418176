package com.sankuai.dzshoppingguide.product.detail.application.fetcher.producttheme;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import org.apache.commons.collections.MapUtils;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import static org.junit.jupiter.api.Assertions.assertEquals;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommendgoods.dtos.ProductM;
import com.sankuai.dztheme.deal.req.DealProductRequest;

import java.util.*;

import org.junit.jupiter.api.BeforeEach;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

/**
 * Unit tests for AbstractProductThemeFetcher's encode method
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("AbstractProductThemeFetcher Encode Method Tests")
public class AbstractProductThemeFetcherTest {

    private TestProductThemeFetcher abstractProductThemeFetcher;

    @Mock
    private ProductDetailPageRequest request;

    @Mock
    private ShopIdMapper shopIdMapper;

    /**
     * Test case for encoding null input
     * Expected: Returns null when input is null
     */
    @Test
    @DisplayName("Should return null when input is null")
    public void testEncodeWithNull() {
        // arrange
        String input = null;
        // act
        String result = AbstractProductThemeFetcher.encode(input);
        // assert
        assertNull(result, "Encoding null should return null");
    }

    /**
     * Test case for encoding empty string
     * Expected: Returns null when input is empty string
     */
    @Test
    @DisplayName("Should return null when input is empty string")
    public void testEncodeWithEmptyString() {
        // arrange
        String input = "";
        // act
        String result = AbstractProductThemeFetcher.encode(input);
        // assert
        assertNull(result, "Encoding empty string should return null");
    }

    /**
     * Test case for encoding blank string
     * Expected: Returns null when input contains only whitespace
     */
    @Test
    @DisplayName("Should return null when input is blank string")
    public void testEncodeWithBlankString() {
        // arrange
        String input = "   ";
        // act
        String result = AbstractProductThemeFetcher.encode(input);
        // assert
        assertNull(result, "Encoding blank string should return null");
    }

    /**
     * Test case for encoding normal ASCII string
     * Expected: Returns properly encoded string with spaces converted to plus signs
     */
    @Test
    @DisplayName("Should encode normal ASCII string correctly")
    public void testEncodeWithNormalString() {
        // arrange
        String input = "hello world";
        // act
        String result = AbstractProductThemeFetcher.encode(input);
        // assert
        assertEquals("hello+world", result, "Normal string should be properly encoded");
    }

    /**
     * Test case for encoding string with special characters
     * Expected: Returns properly encoded string with special characters converted to percent encoding
     */
    @Test
    @DisplayName("Should encode special characters correctly")
    public void testEncodeWithSpecialCharacters() {
        // arrange
        String input = "hello?world&test=1";
        // act
        String result = AbstractProductThemeFetcher.encode(input);
        // assert
        assertEquals("hello%3Fworld%26test%3D1", result, "Special characters should be properly encoded");
    }

    /**
     * Test case for encoding string with Chinese characters
     * Expected: Returns properly encoded string with Chinese characters converted to UTF-8 percent encoding
     */
    @Test
    @DisplayName("Should encode Chinese characters correctly")
    public void testEncodeWithChineseCharacters() {
        // arrange
        String input = "你好世界";
        // act
        String result = AbstractProductThemeFetcher.encode(input);
        // assert
        assertEquals("%E4%BD%A0%E5%A5%BD%E4%B8%96%E7%95%8C", result, "Chinese characters should be properly encoded");
    }

    /**
     * Test case for encoding string with mixed content
     * Expected: Returns properly encoded string with mixed content converted appropriately
     */
    @Test
    @DisplayName("Should encode mixed content correctly")
    public void testEncodeWithMixedContent() {
        // arrange
        String input = "Hello世界?test=1";
        // act
        String result = AbstractProductThemeFetcher.encode(input);
        // assert
        assertEquals("Hello%E4%B8%96%E7%95%8C%3Ftest%3D1", result, "Mixed content should be properly encoded");
    }

    @Test
    @DisplayName("When both poiIdL and poiId are null, should return 0")
    public void testGetPoiId_WhenBothNull() {
        // arrange
        Long poiIdL = null;
        Integer poiId = null;
        // act
        long result = AbstractProductThemeFetcher.getPoiId(poiIdL, poiId);
        // assert
        assertEquals(0, result, "When both inputs are null, should return 0");
    }

    @Test
    @DisplayName("When poiIdL is null and poiId has value, should return poiId")
    public void testGetPoiId_WhenPoiIdLNullAndPoiIdHasValue() {
        // arrange
        Long poiIdL = null;
        Integer poiId = 123;
        // act
        long result = AbstractProductThemeFetcher.getPoiId(poiIdL, poiId);
        // assert
        assertEquals(123, result, "When poiIdL is null, should return poiId value");
    }

    @Test
    @DisplayName("When poiIdL is positive and poiId is null, should return poiIdL")
    public void testGetPoiId_WhenPoiIdLPositiveAndPoiIdNull() {
        // arrange
        Long poiIdL = 456L;
        Integer poiId = null;
        // act
        long result = AbstractProductThemeFetcher.getPoiId(poiIdL, poiId);
        // assert
        assertEquals(456L, result, "When poiIdL is positive and poiId is null, should return poiIdL");
    }

    @Test
    @DisplayName("When poiIdL is positive, should return poiIdL regardless of poiId")
    public void testGetPoiId_WhenPoiIdLPositive() {
        // arrange
        Long poiIdL = 789L;
        Integer poiId = 123;
        // act
        long result = AbstractProductThemeFetcher.getPoiId(poiIdL, poiId);
        // assert
        assertEquals(789L, result, "When poiIdL is positive, should return poiIdL value");
    }

    @Test
    @DisplayName("When poiIdL is zero, should return poiId")
    public void testGetPoiId_WhenPoiIdLZero() {
        // arrange
        Long poiIdL = 0L;
        Integer poiId = 123;
        // act
        long result = AbstractProductThemeFetcher.getPoiId(poiIdL, poiId);
        // assert
        assertEquals(123, result, "When poiIdL is zero, should return poiId value");
    }

    @Test
    @DisplayName("When poiIdL is negative, should return poiId")
    public void testGetPoiId_WhenPoiIdLNegative() {
        // arrange
        Long poiIdL = -1L;
        Integer poiId = 123;
        // act
        long result = AbstractProductThemeFetcher.getPoiId(poiIdL, poiId);
        // assert
        assertEquals(123, result, "When poiIdL is negative, should return poiId value");
    }

    @Test
    @DisplayName("When both poiIdL and poiId are zero, should return 0")
    public void testGetPoiId_WhenBothZero() {
        // arrange
        Long poiIdL = 0L;
        Integer poiId = 0;
        // act
        long result = AbstractProductThemeFetcher.getPoiId(poiIdL, poiId);
        // assert
        assertEquals(0, result, "When both inputs are zero, should return 0");
    }

    @BeforeEach
    public void setUp() {
        abstractProductThemeFetcher = new TestProductThemeFetcher();
        abstractProductThemeFetcher.setTestRequest(request);
    }

    @Test
    public void testDeal2ShopForLongWithEmptyList() throws Throwable {
        // arrange
        List<Integer> emptyList = Collections.emptyList();
        // act
        Map<Integer, Long> result = abstractProductThemeFetcher.deal2ShopForLong(emptyList, MapUtils.EMPTY_MAP);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testDeal2ShopForLongWithMTClientType() throws Throwable {
        // arrange
        List<Integer> dealIds = Arrays.asList(1, 2);
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.MT_APP);
        when(shopIdMapper.getMtBestShopId()).thenReturn(100L);
        abstractProductThemeFetcher.shopIdMapper = shopIdMapper;
        // act
        Map<Long, Long> dealId2ShopId = new HashMap<>();
        dealId2ShopId.put(1L, 2L);
        dealId2ShopId.put(2L, 3L);
        Map<Integer, Long> result = abstractProductThemeFetcher.deal2ShopForLong(dealIds, dealId2ShopId);
        // assert
        assertNotNull(result);
        assertEquals(3, result.size());
        assertEquals(2, result.get(1));
    }

    @Test
    public void testDeal2ShopForLongWithDPClientType() throws Throwable {
        // arrange
        List<Integer> dealIds = Arrays.asList(1, 2);
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.DP_APP);
        when(shopIdMapper.getDpBestShopId()).thenReturn(200L);
        abstractProductThemeFetcher.shopIdMapper = shopIdMapper;
        // act
        Map<Long, Long> dealId2ShopId = new HashMap<>();
        dealId2ShopId.put(1L, 2L);
        dealId2ShopId.put(2L, 3L);
        Map<Integer, Long> result = abstractProductThemeFetcher.deal2ShopForLong(dealIds, dealId2ShopId);
        // assert
        assertNotNull(result);
        assertEquals(3, result.size());
        assertEquals(2, result.get(1));
        assertEquals(3, result.get(2));
    }

    @Test
    public void testDeal2ShopForLongWithNullList() throws Throwable {
        // act
        Map<Integer, Long> result = abstractProductThemeFetcher.deal2ShopForLong(null, MapUtils.EMPTY_MAP);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    private static class TestProductThemeFetcher extends AbstractProductThemeFetcher {

        @Override
        boolean enableNewService() {
            return false;
        }

        @Override
        DealProductRequest buildDealProductRequest(List<ProductM> products, Map<Long, Long> dealId2ShopId) {
            return null;
        }


        // Method to set request for testing
        void setTestRequest(ProductDetailPageRequest req) {
            this.request = req;
        }
    }
}
