package com.sankuai.dzshoppingguide.product.detail.spi.promo.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-04-02
 * @desc
 */
@TypeDoc(description = "PromotionCoupon")
@Data
@MobileDo(id = 0xa9823289)
public class PromotionCoupon implements Serializable {
    
    @FieldDoc(description = "优惠券列表")
    @MobileField(key = 0x5db6)
    private List<CouponModule> couponList;

    @FieldDoc(description = "模块名")
    @MobileField(key = 0xb308)
    private String moduleName;
}
