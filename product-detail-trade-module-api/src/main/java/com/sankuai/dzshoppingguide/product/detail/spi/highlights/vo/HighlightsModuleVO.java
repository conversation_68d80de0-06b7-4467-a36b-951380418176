package com.sankuai.dzshoppingguide.product.detail.spi.highlights.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.HighlightsModuleAttrVO;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dz.product.detail.gateway.spi.vo.AbstractModuleVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/2/5 10:42
 * 移动之家: https://mobile.sankuai.com/studio/model/info/41256
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TypeDoc(description = "预定页亮点模块")
@MobileDo(id = 0x1b0b)
public class HighlightsModuleVO extends AbstractModuleVO {
    @Override
    public String getModuleKey() {
        return ModuleKeyConstants.HIGHLIGHTS;
    }

    @FieldDoc(description = "跳链")
    @MobileDo.MobileField(key = 0x774e)
    private String jumpUrl;

    @FieldDoc(description = "样式，simple:样式一-简单样式, struct:样式二-结构化样式, both:样式三-均展示")
    @MobileDo.MobileField(key = 0x1b3a)
    private String style;

    @FieldDoc(description = "亮点属性")
    @MobileDo.MobileField(key = 0x612f)
    private List<HighlightsModuleAttrVO> attrs;

    @FieldDoc(description = "分隔符")
    @MobileDo.MobileField(key = 0x9716)
    private String delimiter;
}
