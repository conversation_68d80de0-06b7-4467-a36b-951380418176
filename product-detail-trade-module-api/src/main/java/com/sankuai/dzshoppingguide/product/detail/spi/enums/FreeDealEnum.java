package com.sankuai.dzshoppingguide.product.detail.spi.enums;

import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import lombok.Getter;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;

@Getter
public enum FreeDealEnum {

    HOME_DESIGN_BOOKING("home_design", "家居-装修设计预约单"),

    LIFE_HOUSEKEEPING_BOOKING("life_housekeeping", "生活服务-家政预约单"),

    EDU_TRIAL_BOOKING("edu_trial", "教育-试听课预约单"),

    VACCINES("vaccines", "疫苗"),
    RECYCLE("life_recycle", "回收"),

    ;

    private final String type;

    private final String desc;

    FreeDealEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static FreeDealEnum fromType(String type) {
        for (FreeDealEnum dealEnum : FreeDealEnum.values()) {
            if (dealEnum.type.equals(type)) {
                return dealEnum;
            }
        }
        return null;
    }

    public static FreeDealEnum fromDealCategory(String categoryId) {
        if (StringUtils.isBlank(categoryId)) {
            return null;
        }
        Map<String, List> configMap = Lion.getMap(Environment.getAppName(), "free.deal.type.category.mapping", List.class);
        if (MapUtils.isEmpty(configMap)) {
            return null;
        }
        for (Map.Entry<String, List> entry : configMap.entrySet()) {
            if (entry.getValue().contains(categoryId)) {
                return fromType(entry.getKey());
            }
        }
        return null;
    }
}
