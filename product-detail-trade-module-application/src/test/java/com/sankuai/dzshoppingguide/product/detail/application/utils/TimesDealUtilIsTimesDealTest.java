// package com.sankuai.dzshoppingguide.product.detail.application.utils;
//
// import static org.junit.Assert.assertFalse;
// import static org.junit.Assert.assertTrue;
// import com.sankuai.general.product.query.center.client.dto.DealGroupBasicDTO;
// import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
// import org.junit.Test;
//
// public class TimesDealUtilIsTimesDealTest {
//
//     /**
//      * 测试 isTimesDeal 方法，当 dealGroupDTO 为 null 时
//      */
//     @Test
//     public void testIsTimesDealWhenDealGroupDTOIsNull() {
//         // arrange
//         DealGroupDTO dealGroupDTO = null;
//         // act
//         boolean result = TimesDealUtil.isTimesDeal(dealGroupDTO);
//         // assert
//         assertFalse(result);
//     }
//
//     /**
//      * 测试 isTimesDeal 方法，当 dealGroupDTO.getBasic() 为 null 时
//      */
//     @Test
//     public void testIsTimesDealWhenBasicIsNull() {
//         // arrange
//         DealGroupDTO dealGroupDTO = new DealGroupDTO();
//         dealGroupDTO.setBasic(null);
//         // act
//         boolean result = TimesDealUtil.isTimesDeal(dealGroupDTO);
//         // assert
//         assertFalse(result);
//     }
//
//     /**
//      * 测试 isTimesDeal 方法，当 dealGroupDTO.getBasic().getTradeType() 不等于 TIMES_CARD 时
//      */
//     @Test
//     public void testIsTimesDealWhenTradeTypeNotEqualTimesCard() {
//         // arrange
//         DealGroupDTO dealGroupDTO = new DealGroupDTO();
//         DealGroupBasicDTO basicDTO = new DealGroupBasicDTO();
//         basicDTO.setTradeType(999);
//         dealGroupDTO.setBasic(basicDTO);
//         // act
//         boolean result = TimesDealUtil.isTimesDeal(dealGroupDTO);
//         // assert
//         assertFalse(result);
//     }
//
//     /**
//      * 测试 isTimesDeal 方法，当 dealGroupDTO.getBasic().getTradeType() 等于 TIMES_CARD 时
//      */
//     @Test
//     public void testIsTimesDealWhenTradeTypeEqualTimesCard() {
//         // arrange
//         DealGroupDTO dealGroupDTO = new DealGroupDTO();
//         DealGroupBasicDTO basicDTO = new DealGroupBasicDTO();
//         basicDTO.setTradeType(19);
//         dealGroupDTO.setBasic(basicDTO);
//         // act
//         boolean result = TimesDealUtil.isTimesDeal(dealGroupDTO);
//         // assert
//         assertTrue(result);
//     }
// }
