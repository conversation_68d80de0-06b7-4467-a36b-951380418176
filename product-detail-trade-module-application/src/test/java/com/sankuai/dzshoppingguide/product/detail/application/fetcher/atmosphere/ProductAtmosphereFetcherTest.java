package com.sankuai.dzshoppingguide.product.detail.application.fetcher.atmosphere;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.google.common.collect.Lists;
import com.sankuai.sinai.data.api.dto.DpPoiBackCategoryDTO;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.sinai.data.api.dto.MtPoiDTO;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class ProductAtmosphereFetcherTest {

    @InjectMocks
    private ProductAtmosphereFetcher productAtmosphereFetcher;

    @Mock
    private MtPoiDTO mtPoiDTO;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    private DpPoiBackCategoryDTO createDpPoiBackCategoryDTO(int categoryId, int categoryLevel) {
        DpPoiBackCategoryDTO dto = new DpPoiBackCategoryDTO();
        dto.setCategoryId(categoryId);
        dto.setCategoryLevel(categoryLevel);
        return dto;
    }

    public List<Integer> getMtPoiCategoryIds(MtPoiDTO mtPoiDTO, int level) {
        if (Objects.isNull(mtPoiDTO) || CollectionUtils.isEmpty(mtPoiDTO.getDpBackCategoryList())) {
            return Lists.newArrayList();
        }
        return mtPoiDTO.getDpBackCategoryList().stream().filter(cate -> Objects.equals(cate.getCategoryLevel(), level)).map(DpPoiBackCategoryDTO::getCategoryId).collect(Collectors.toList());
    }

    /**
     * Test case for null MtPoiDTO
     */
    @Test
    public void testGetMtPoiCategoryIdsWithNullMtPoiDTO() {
        // arrange
        int level = 1;
        // act
        List<Integer> result = productAtmosphereFetcher.getMtPoiCategoryIds(null, level);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test case for empty DpBackCategoryList
     */
    @Test
    public void testGetMtPoiCategoryIdsWithEmptyDpBackCategoryList() {
        // arrange
        int level = 1;
        when(mtPoiDTO.getDpBackCategoryList()).thenReturn(new ArrayList<>());
        // act
        List<Integer> result = productAtmosphereFetcher.getMtPoiCategoryIds(mtPoiDTO, level);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test case for categories matching the given level
     */
    @Test
    public void testGetMtPoiCategoryIdsWithMatchingCategories() {
        // arrange
        int level = 1;
        List<DpPoiBackCategoryDTO> categories = Lists.newArrayList(createDpPoiBackCategoryDTO(1, 1), createDpPoiBackCategoryDTO(2, 1), createDpPoiBackCategoryDTO(3, 2));
        when(mtPoiDTO.getDpBackCategoryList()).thenReturn(categories);
        // act
        List<Integer> result = productAtmosphereFetcher.getMtPoiCategoryIds(mtPoiDTO, level);
        // assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.contains(1));
        assertTrue(result.contains(2));
    }

    /**
     * Test case for categories not matching the given level
     */
    @Test
    public void testGetMtPoiCategoryIdsWithNoMatchingCategories() {
        // arrange
        int level = 3;
        List<DpPoiBackCategoryDTO> categories = Lists.newArrayList(createDpPoiBackCategoryDTO(1, 1), createDpPoiBackCategoryDTO(2, 1), createDpPoiBackCategoryDTO(3, 2));
        when(mtPoiDTO.getDpBackCategoryList()).thenReturn(categories);
        // act
        List<Integer> result = productAtmosphereFetcher.getMtPoiCategoryIds(mtPoiDTO, level);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test case for categories with mixed levels
     */
    @Test
    public void testGetMtPoiCategoryIdsWithMixedLevels() {
        // arrange
        int level = 2;
        List<DpPoiBackCategoryDTO> categories = Lists.newArrayList(createDpPoiBackCategoryDTO(1, 1), createDpPoiBackCategoryDTO(2, 2), createDpPoiBackCategoryDTO(3, 2), createDpPoiBackCategoryDTO(4, 3));
        when(mtPoiDTO.getDpBackCategoryList()).thenReturn(categories);
        // act
        List<Integer> result = productAtmosphereFetcher.getMtPoiCategoryIds(mtPoiDTO, level);
        // assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.contains(2));
        assertTrue(result.contains(3));
    }

    /**
     * Test case: when dpPoiDTO is null
     * Expected: should return empty list
     */
    @Test
    public void testGetDpPoiCategoryIds_WhenDpPoiDTOIsNull() {
        // arrange
        DpPoiDTO dpPoiDTO = null;
        int level = 1;
        // act
        List<Integer> result = productAtmosphereFetcher.getDpPoiCategoryIds(dpPoiDTO, level);
        // assert
        assertTrue(result.isEmpty());
        assertEquals(Lists.newArrayList(), result);
    }

    /**
     * Test case: when backMainCategoryPath is null
     * Expected: should return empty list
     */
    @Test
    public void testGetDpPoiCategoryIds_WhenBackMainCategoryPathIsNull() {
        // arrange
        DpPoiDTO dpPoiDTO = mock(DpPoiDTO.class);
        when(dpPoiDTO.getBackMainCategoryPath()).thenReturn(null);
        int level = 1;
        // act
        List<Integer> result = productAtmosphereFetcher.getDpPoiCategoryIds(dpPoiDTO, level);
        // assert
        assertTrue(result.isEmpty());
        assertEquals(Lists.newArrayList(), result);
    }

    /**
     * Test case: when backMainCategoryPath is empty
     * Expected: should return empty list
     */
    @Test
    public void testGetDpPoiCategoryIds_WhenBackMainCategoryPathIsEmpty() {
        // arrange
        DpPoiDTO dpPoiDTO = mock(DpPoiDTO.class);
        when(dpPoiDTO.getBackMainCategoryPath()).thenReturn(new ArrayList<>());
        int level = 1;
        // act
        List<Integer> result = productAtmosphereFetcher.getDpPoiCategoryIds(dpPoiDTO, level);
        // assert
        assertTrue(result.isEmpty());
        assertEquals(Lists.newArrayList(), result);
    }

    /**
     * Test case: when there are categories matching the specified level
     * Expected: should return list of matching category IDs
     */
    @Test
    public void testGetDpPoiCategoryIds_WhenHasMatchingCategories() {
        // arrange
        DpPoiDTO dpPoiDTO = mock(DpPoiDTO.class);
        List<DpPoiBackCategoryDTO> categories = new ArrayList<>();
        DpPoiBackCategoryDTO category1 = mock(DpPoiBackCategoryDTO.class);
        when(category1.getCategoryLevel()).thenReturn(1);
        when(category1.getCategoryId()).thenReturn(100);
        DpPoiBackCategoryDTO category2 = mock(DpPoiBackCategoryDTO.class);
        when(category2.getCategoryLevel()).thenReturn(1);
        when(category2.getCategoryId()).thenReturn(200);
        categories.add(category1);
        categories.add(category2);
        when(dpPoiDTO.getBackMainCategoryPath()).thenReturn(categories);
        int level = 1;
        // act
        List<Integer> result = productAtmosphereFetcher.getDpPoiCategoryIds(dpPoiDTO, level);
        // assert
        assertEquals(2, result.size());
        assertEquals(Lists.newArrayList(100, 200), result);
    }

    /**
     * Test case: when no categories match the specified level
     * Expected: should return empty list
     */
    @Test
    public void testGetDpPoiCategoryIds_WhenNoMatchingCategories() {
        // arrange
        DpPoiDTO dpPoiDTO = mock(DpPoiDTO.class);
        List<DpPoiBackCategoryDTO> categories = new ArrayList<>();
        DpPoiBackCategoryDTO category1 = mock(DpPoiBackCategoryDTO.class);
        when(category1.getCategoryLevel()).thenReturn(1);
        DpPoiBackCategoryDTO category2 = mock(DpPoiBackCategoryDTO.class);
        when(category2.getCategoryLevel()).thenReturn(1);
        categories.add(category1);
        categories.add(category2);
        when(dpPoiDTO.getBackMainCategoryPath()).thenReturn(categories);
        // Different level than what categories have
        int level = 2;
        // act
        List<Integer> result = productAtmosphereFetcher.getDpPoiCategoryIds(dpPoiDTO, level);
        // assert
        assertTrue(result.isEmpty());
        assertEquals(Lists.newArrayList(), result);
    }
}
