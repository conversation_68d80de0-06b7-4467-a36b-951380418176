package com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.ext.param.handler;

import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.enums.OrderPageExtParamEnums;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.ext.param.BaseSingleExtParamBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.ext.param.request.ExtParamBuilderRequest;
import org.springframework.stereotype.Component;

/**
 * @Author: guangyujie
 * @Date: 2025/3/12 17:56
 */
@Component
public class PageSourceExtParamBuilder extends BaseSingleExtParamBuilder {

    @Override
    protected OrderPageExtParamEnums buildKey() {
        return OrderPageExtParamEnums.source;
    }

    @Override
    protected String doBuildExtParam(final ProductDetailPageRequest request,
                                     final ExtParamBuilderRequest builderRequest) throws Exception {
        return request.getPageSource();
    }

}
