package com.sankuai.dzshoppingguide.product.detail.application.builder.recommend;

import com.dianping.cat.Cat;
import com.dianping.deal.sales.common.datatype.ProductParam;
import com.dianping.deal.sales.common.datatype.SalesDisplayDTO;
import com.dianping.deal.shop.dto.DealGroupShop;
import com.dianping.frog.sdk.util.CollectionUtils;
import com.google.common.collect.Lists;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.BaseBuilder;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommend.*;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommend.dto.*;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.FetcherDAGStarter;
import com.sankuai.dzshoppingguide.product.detail.application.utils.PriceHelper;
import com.sankuai.dzshoppingguide.product.detail.application.utils.UrlHelper;
import com.sankuai.dzshoppingguide.product.detail.domain.utils.LionConfigUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.promo.vo.PromoDetailModule;
import com.sankuai.dzshoppingguide.product.detail.spi.recommend.vo.ContentPBO;
import com.sankuai.dzshoppingguide.product.detail.spi.recommend.vo.RelatedDealPBO;
import com.sankuai.dzshoppingguide.product.detail.spi.recommend.vo.ShopPBO;
import com.sankuai.dzshoppingguide.product.detail.spi.recommend.vo.ShopRelatedDealRecommendVO;
import com.sankuai.dztheme.deal.dto.DealProductDTO;
import com.sankuai.dztheme.deal.res.DealProductResult;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.ExtendImageItemDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants.MODULE_DETAIL_DEAL_IN_SHOP_RECOMMEND;

/**
 * @Author: zhangyuan103
 * @Date: 2025/5/19
 * @Description: 同店推荐模块，涉及到APP和小程序端
 */
@Builder(
        moduleKey = MODULE_DETAIL_DEAL_IN_SHOP_RECOMMEND, startFetcher = FetcherDAGStarter.class,
        dependentFetchers = {RecommendInfoInShopFetcher.class, DealBaseInfoFetcher.class, PriceFetcher.class,
            DealStockFetcher.class, Dp2MtShopIdMapperFetcher.class, DealProductInMtWxMiniFetcher.class,
            ShopIdMapperFetcher.class, DpDealsInShopFetcher.class, DealMapperFetcher.class}
)
@Slf4j
public class InShopRecommendBuilder extends BaseBuilder<ShopRelatedDealRecommendVO> {
    private Map<Long, DealGroupDTO> dealGroupDTOMap;
    private Map<Long, DealGroupShop> dealGroupShopMap;
    private Map<Long, List<Long>> dpMtShopIds;
    private Map<Long, PriceDisplayDTO> priceDisplayMap;
    private Map<Long, SalesDisplayDTO> productId2SaleMap;
    private List<Long> dealGroupIds;
    private List<DealGroupDTO> dealGroupDTOs;
    private Map<Long, List<PriceDisplayDTO>> priceData;
    private Map<ProductParam, SalesDisplayDTO> salesDisplayDTOMap;
    private List<Long> allDpDealIds;
    long mtShopId;
    long dpShopId;
    private boolean isMt;

    private static final DecimalFormat df = new DecimalFormat("0.0");
    private static final String MT_DETAIL_URL_TEMPLATE = "imeituan://www.meituan.com/gc/deal/detail?source=deal_detail&did=%d&poiid=%d&fromPage=%s";
    private static final String DP_DETAIL_URL_TEMPLATE = "dianping://tuandeal?source=deal_detail&shopid=%d&id=%d&fromPage=%s";
    private static final String DEAL_IMAGE_SHOW_RATIO = "1:1";
    public static final String IN_SHOP_RECOMMEND_MODULE_KEY = "inShopRecommendModuleButtonText";

    @Override
    public ShopRelatedDealRecommendVO doBuild() {
        isMt = request.getClientTypeEnum().isMtClientType();
        ShopRelatedDealRecommendVO result = new ShopRelatedDealRecommendVO();
        // 1.获取fetcher数据
        if (!initFetcher()) {
            return result;
        }

        // 2.获取团单信息
        dealGroupDTOMap = dealGroupDTOs.stream().collect(Collectors.toMap(
                deal -> isMt ? deal.getMtDealGroupId() : deal.getDpDealGroupId(), Function.identity(), (o1, o2) -> o1));
        priceDisplayMap = priceData.values().stream().flatMap(Collection::stream).distinct()
                .collect(Collectors.toMap(product -> Long.valueOf(product.getIdentity().getProductId()),
                        productIdentity -> productIdentity, (x, y) -> x));
        productId2SaleMap = new HashMap<>();
        if (MapUtils.isNotEmpty(salesDisplayDTOMap)) {
            for (Map.Entry<ProductParam, SalesDisplayDTO> entry : salesDisplayDTOMap.entrySet()) {
                productId2SaleMap.put(entry.getKey().getProductGroupId(), entry.getValue());
            }
        }

        // 3.构造推荐的团单数据
        List<RelatedDealPBO> deals = getInShopDeals(dealGroupIds);
        Map<Long, RelatedDealPBO> relatedDealPBOMap = deals.stream().collect(Collectors
                .toMap(pbo -> Long.valueOf(isMt ? pbo.getMtId() : pbo.getDpId()), Function.identity(), (o1, o2) -> o1));
        List<RelatedDealPBO> dealPBOList = Lists.newArrayList();
        dealGroupIds.forEach(dealGroupId -> {
            if (relatedDealPBOMap.get(dealGroupId) == null) {
                return;
            }
            dealPBOList.add(relatedDealPBOMap.get(dealGroupId));
        });
        result.setDealPBOList(dealPBOList);

        // 4.设置跳链和按钮文案
        // 如果是微信小程序场景，使用mdWxminiAppDetailUrl字段
        if (request.getClientTypeEnum().equals(ClientTypeEnum.MT_XCX)) {
            long bestShopId = isMt ? mtShopId : dpShopId;
            result.setShopJumpUrl((UrlHelper.getMtWxMainShopUrl(request.getPoiId(), bestShopId)));
        } else {
            result.setShopJumpUrl(anchorDealShelf(UrlHelper.getShopUrl(request, dpShopId, mtShopId)));
        }
        Map<String, String> relatedRecommendConfigMap = LionConfigUtils.getRelatedRecommendConfigMap();
        result.setBtnText(
                String.format(relatedRecommendConfigMap.getOrDefault(IN_SHOP_RECOMMEND_MODULE_KEY, "查看全部（%s个）>"),
                        CollectionUtils.isEmpty(allDpDealIds) ? 0 : allDpDealIds.size()));
        return result;
    }

    private boolean initFetcher() {
        RecommendInfoReturnValue recommendInfoReturnValue = getDependencyResult(RecommendInfoInShopFetcher.class);
        DealGroupDTOReturnValue dealGroup = getDependencyResult(DealBaseInfoFetcher.class);
        PriceReturnValue priceReturnValue = getDependencyResult(PriceFetcher.class);
        Dp2MtShopIdMapperReturnValue dp2MtShopIdMapperReturnValue = getDependencyResult(Dp2MtShopIdMapperFetcher.class);
        DealMapperReturnValue dealMapperReturnValue = getDependencyResult(DealMapperFetcher.class);
        SalesDisplayReturnValue salesDisplayReturnValue = getDependencyResult(DealStockFetcher.class);
        DpDealsInShopReturnValue dpDealsInShopReturnValue = getDependencyResult(DpDealsInShopFetcher.class);
        ShopIdMapper shopIdMapper = getDependencyResult(ShopIdMapperFetcher.class);

        dealGroupIds = Optional.ofNullable(recommendInfoReturnValue)
                .map(RecommendInfoReturnValue::getSortedDealGroupIds).orElse(null);
        dealGroupDTOs = Optional.ofNullable(dealGroup).map(DealGroupDTOReturnValue::getDealGroupDTOs).orElse(null);
        priceData = Optional.ofNullable(priceReturnValue).map(PriceReturnValue::getPriceDisplayMap).orElse(null);
        dpMtShopIds = Optional.ofNullable(dp2MtShopIdMapperReturnValue)
                .map(Dp2MtShopIdMapperReturnValue::getDpMtShopIds).orElse(null);
        dealGroupShopMap = Optional.ofNullable(dealMapperReturnValue).map(DealMapperReturnValue::getDpDealGroupShopMap)
                .orElse(null);
        salesDisplayDTOMap = Optional.ofNullable(salesDisplayReturnValue)
                .map(SalesDisplayReturnValue::getSalesDisplayDTOMap).orElse(null);
        allDpDealIds = Optional.ofNullable(dpDealsInShopReturnValue).map(DpDealsInShopReturnValue::getDpAllDealIds)
                .orElse(null);
        mtShopId = shopIdMapper.getMtBestShopId();
        dpShopId = shopIdMapper.getDpBestShopId();
        if (CollectionUtils.isEmpty(dealGroupDTOs) || CollectionUtils.isEmpty(dealGroupIds)
                || CollectionUtils.isEmpty(priceData) || CollectionUtils.isEmpty(dpMtShopIds)
                || CollectionUtils.isEmpty(dealGroupShopMap)
                || CollectionUtils.isEmpty(allDpDealIds)) {
            return false;
        }
        return true;
    }

    private String anchorDealShelf(String shopUrl) {
        if (!shopUrl.contains("anchor=deal_shelf")) {
            if (!shopUrl.contains("?")) {
                shopUrl += "?anchor=deal_shelf";
            } else {
                shopUrl += "&anchor=deal_shelf";
            }
        }
        return shopUrl;
    }

    public List<RelatedDealPBO> getInShopDeals(List<Long> dealGroupIds) {
        List<RelatedDealPBO> deals = new ArrayList<>();
        dealGroupIds.forEach(dealGroupId -> {
            RelatedDealPBO relatedDealPBO = buildDealGroupPBO(dealGroupId);
            if (relatedDealPBO == null)
                return;
            deals.add(relatedDealPBO);
        });
        return deals;
    }

    public RelatedDealPBO buildDealGroupPBO(Long dealGroupId) {
        if (MapUtils.isEmpty(dealGroupDTOMap) || !dealGroupDTOMap.containsKey(dealGroupId)
                || dealGroupDTOMap.get(dealGroupId) == null) {
            log.info("团单信息不存在, dealGroupId:{}", dealGroupId);
            return null;
        }
        DealGroupDTO dealGroupDTO = dealGroupDTOMap.get(dealGroupId);
        RelatedDealPBO relatedDealPBO = new RelatedDealPBO();
        Long curDealGroupId;
        if (isMt) {
            relatedDealPBO.setMtId(dealGroupDTO.getMtDealGroupIdInt());
            curDealGroupId = dealGroupDTO.getMtDealGroupId();
        } else {
            relatedDealPBO.setDpId(dealGroupDTO.getDpDealGroupIdInt());
            curDealGroupId = dealGroupDTO.getDpDealGroupId();
        }
        // 头图
        fillDealImage(dealGroupDTO, relatedDealPBO);
        // 标题
        relatedDealPBO.setTitle(dealGroupDTO.getBasic().getTitle());
        // 门店
        DealGroupShop dealGroupShop = dealGroupShopMap.get(dealGroupDTO.getDpDealGroupId());
        if (dealGroupShop == null) {
            return null;
        }
        Long curShopId;
        if (isMt) {
            List<Long> mtShopIds = dpMtShopIds.get(dealGroupShop.getLongShopId());
            if (CollectionUtils.isEmpty(mtShopIds)) {
                return null;
            }
            curShopId = mtShopIds.get(0);
        } else {
            curShopId = dealGroupShop.getLongShopId();
        }
        String fromPage = StringUtils.isBlank(request.getFromPage()) ? StringUtils.EMPTY : request.getFromPage();
        // 团详链接
        if (isMt) {
            relatedDealPBO.setDetailUrl(String.format(MT_DETAIL_URL_TEMPLATE, curDealGroupId, curShopId, fromPage));
            if (request.getClientTypeEnum().equals(ClientTypeEnum.MT_XCX)) {
                DealProductInMtWxMiniReturnValue dealProductInMtWXMiniReturnValue = getDependencyResult(
                        DealProductInMtWxMiniFetcher.class);
                DealProductResult dealProductResult = Optional.ofNullable(dealProductInMtWXMiniReturnValue)
                        .map(DealProductInMtWxMiniReturnValue::getDealProductResult).orElse(null);
                if (dealProductResult != null && CollectionUtils.isNotEmpty(dealProductResult.getDeals())) {
                    String detailUrl = dealProductResult.getDeals().stream().map(DealProductDTO::getDetailUr)
                            .filter(url -> url != null && url.contains(String.valueOf(curDealGroupId))).findFirst()
                            .orElse(null);
                    relatedDealPBO.setDetailUrl(String.format("%s&fromPage=%s", detailUrl, fromPage));
                }
            }
        } else {
            relatedDealPBO.setDetailUrl(String.format(DP_DETAIL_URL_TEMPLATE, curShopId, curDealGroupId, fromPage));
        }

        ShopPBO shop = new ShopPBO();
        shop.setShopId(curShopId);
        shop.setShopName(dealGroupShop.getShopName());
        shop.setDistanceDesc(getDistanceDesc(dealGroupShop.getDistance()));
        relatedDealPBO.setShop(shop);
        // 价格
        PromoDetailModule promoDetailModule = new PromoDetailModule();
        PriceDisplayDTO priceDisplayDTO = null;
        if (MapUtils.isNotEmpty(priceDisplayMap)) {
            priceDisplayDTO = priceDisplayMap.get(curDealGroupId);
        }
        if (priceDisplayDTO != null) {
            promoDetailModule.setPromoPrice(PriceHelper.formatPrice(priceDisplayDTO.getPrice()));
            promoDetailModule.setMarketPrice(PriceHelper.formatPrice(priceDisplayDTO.getMarketPrice()));
            setMarketPromoDiscount(promoDetailModule, priceDisplayDTO);
        } else {
            promoDetailModule.setPromoPrice(
                    Objects.nonNull(dealGroupDTO.getPrice()) ? dealGroupDTO.getPrice().getSalePrice() : "0.01");
            promoDetailModule.setMarketPrice(
                    Objects.nonNull(dealGroupDTO.getPrice()) ? dealGroupDTO.getPrice().getMarketPrice() : "0.01");
        }
        relatedDealPBO.setPromoDetailModule(promoDetailModule);
        // 销量
        SalesDisplayDTO salesDisplayDTO = productId2SaleMap.get(curDealGroupId);
        if (salesDisplayDTO != null) {
            relatedDealPBO.setSaleDesc(salesDisplayDTO.getSalesTag());
        }

        return relatedDealPBO;
    }

    private void fillDealImage(DealGroupDTO dealGroupDTO, RelatedDealPBO relatedDealPBO) {
        String picUrl = StringUtils.EMPTY;
        if (Objects.nonNull(dealGroupDTO.getExtendImage())
                && org.apache.commons.collections4.CollectionUtils
                        .isNotEmpty(dealGroupDTO.getExtendImage().getExtendImages())
                && org.apache.commons.collections4.CollectionUtils
                        .isNotEmpty(dealGroupDTO.getExtendImage().getExtendImages().get(0).getExtendImageItems())) {
            Optional<ExtendImageItemDTO> extendImageItemDTO = dealGroupDTO.getExtendImage().getExtendImages().get(0)
                    .getExtendImageItems().stream().filter(item -> StringUtils.isNotEmpty(item.getRatio())
                            && item.getRatio().equals(DEAL_IMAGE_SHOW_RATIO))
                    .findFirst();
            if (extendImageItemDTO.isPresent()) {
                picUrl = extendImageItemDTO.get().getUrl();
            }
        }
        relatedDealPBO.setDealContents(Lists.newArrayList(
                new ContentPBO(1, StringUtils.isNotBlank(picUrl) ? picUrl : Objects.nonNull(dealGroupDTO.getImage())
                        ? dealGroupDTO.getImage().getDefaultPicPath() : StringUtils.EMPTY)));
    }

    private static String getDistanceDesc(String distance) {
        if (StringUtils.isEmpty(distance)) {
            return null;
        }
        int y = Integer.parseInt(distance);
        if (y <= 1000) {
            return "距您" + y + "m";
        } else if (y <= 100000) {
            return "距您" + df.format(y / 1000.0) + "km";
        } else if (y <= 200000) {
            return "距您" + y / 1000.0 + "km";
        } else {
            return "距您>20km";
        }
    }

    private void setMarketPromoDiscount(PromoDetailModule promoDetailModule, PriceDisplayDTO priceDisplayDTO) {
        BigDecimal marketPrice = priceDisplayDTO.getMarketPrice();
        BigDecimal finalPrice = priceDisplayDTO.getPrice();
        if (marketPrice == null || finalPrice == null) {
            return;
        }
        BigDecimal discountRate = calcDiscountRate(marketPrice, finalPrice);

        String discountRateStr;
        if (Objects.isNull(discountRate)) {
            promoDetailModule.setMarketPromoDiscount(StringUtils.EMPTY);
            return;
        }
        if (discountRate.compareTo(new BigDecimal("7")) > 0) {
            discountRateStr = StringUtils.isNotBlank(marketPrice.toPlainString())
                    ? "￥" + marketPrice.setScale(0, RoundingMode.CEILING).toPlainString() : StringUtils.EMPTY;
        } else if (discountRate.compareTo(new BigDecimal("0.1")) <= 0) {
            discountRateStr = "0.1折";
        } else {
            discountRateStr = discountRate + "折";
        }
        promoDetailModule.setMarketPromoDiscount(discountRateStr);
    }

    /**
     * 计算折扣率（保留一位小数 + 向上取整）
     */
    private static BigDecimal calcDiscountRate(BigDecimal marketPrice, BigDecimal finalPrice) {
        Cat.logEvent("INVALID_METHOD_2",
                "com.dianping.mobile.mapi.dztgdetail.biz.relatedmodule.NearbyDiscountRelatedDealIdProcessor.calcDiscountRate(java.math.BigDecimal,java.math.BigDecimal)");
        if (marketPrice == null || finalPrice == null || marketPrice.compareTo(BigDecimal.ZERO) == 0
                || finalPrice.compareTo(BigDecimal.ZERO) == 0 || finalPrice.compareTo(marketPrice) == 0
                || marketPrice.compareTo(finalPrice) < 0) {
            return null;
        }
        return finalPrice.divide(marketPrice, 2, RoundingMode.CEILING).multiply(new BigDecimal(10)).setScale(1,
                RoundingMode.CEILING);
    }
}
