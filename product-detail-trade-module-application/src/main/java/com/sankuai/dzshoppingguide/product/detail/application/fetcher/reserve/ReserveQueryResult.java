package com.sankuai.dzshoppingguide.product.detail.application.fetcher.reserve;

import com.sankuai.dztheme.massagebook.theme.res.ReserveProductDTO;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/2/7 14:58
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ReserveQueryResult extends FetcherReturnValueDTO {
    private List<ReserveProductDTO> products;
}
