package com.sankuai.dzshoppingguide.product.detail.application.builder.similargoods;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.utils.ImageUtil;
import com.sankuai.dzshoppingguide.product.detail.spi.similargoods.vo.dtos.DzPictureComponent;
import com.sankuai.dzshoppingguide.product.detail.spi.similargoods.vo.dtos.DzProductVO;
import com.sankuai.dzshoppingguide.product.detail.spi.similargoods.vo.dtos.DzPromoVO;
import com.sankuai.dzshoppingguide.product.detail.spi.similargoods.vo.dtos.ShopVO;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommendgoods.dtos.ProductM;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommendgoods.dtos.ProductSaleM;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommendgoods.dtos.ShopM;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

@Component
@Slf4j
public class ListingProductsBuilder  {

    public List<DzProductVO> buildProductList(List<ProductM> productList, ProductDetailPageRequest request) {
        List<DzProductVO> dzProducts = Lists.newArrayList();
        if (CollectionUtils.isEmpty(productList)) {
            return dzProducts;
        }
        int index = 0;
        for (ProductM productM : productList) {
            index++;
            dzProducts.add(buildDzProductVO(productM, request));
        }
        return dzProducts;
    }

    public DzProductVO buildDzProductVO(ProductM productM, ProductDetailPageRequest request) {
        if (productM == null) {
            return null;
        }
        DzProductVO dzProductVO = new DzProductVO();
        dzProductVO.setLongProductId(productM.getProductId());
        dzProductVO.setProductItemId(buildProductItemId(productM));
        dzProductVO.setTitle(buildTitle(productM));
        dzProductVO.setPic(buildDzPic(productM, request.getClientTypeEnum().isMtClientType()));
        dzProductVO.setShop(loadShop(productM));
        dzProductVO.setSalePrice(buildSalePrice(productM, request));
        dzProductVO.setProductTags(buildProductTags(productM, request));
        dzProductVO.setMarketPrice(buildMarketPrice(productM));
        dzProductVO.setMarketPriceCent(buildMarketPriceCent(dzProductVO.getMarketPrice()));
        dzProductVO.setSale(buildSale(productM));
        dzProductVO.setDetailJumpUrl(buildDetailJumpUrl(productM, request));
        dzProductVO.setPromoVOList(buildDzPromoList(productM));
        dzProductVO.setExtAttrMap(buildExtAttrs(productM));
        dzProductVO.setButtonName(buildButtonName(productM));
        dzProductVO.setButtonJumpUrl(buildButtonJumpUrl(productM, request));
        dzProductVO.setButtonStatus(buildButtonStatus(productM));
        dzProductVO.setProductDesc(buildProductDesc(productM));
        dzProductVO.setAvailable(buildAvailable(productM));
        dzProductVO.setPicScale(buildPicScale(productM));
        //依赖dzProductVO.shop
        dzProductVO.setShopName(loadShopName(dzProductVO.getShop()));
        //有依赖
        dzProductVO.setDiscountTag(buildDiscountTag(request, productM));
        return dzProductVO;
    }

    private int buildProductItemId(ProductM productM) {
        if(productM == null || productM.getAttr("dealDefaultSkuId") == null) {
            return 0;
        }

        Integer itemId = Integer.parseInt(productM.getAttr("dealDefaultSkuId"));
        return Objects.isNull(itemId) ? 0 : itemId;
    }

    private String loadShopName(ShopVO shop) {
        if (Objects.isNull(shop)) {
            return StringUtils.EMPTY;
        }

        return shop.getShopName();
    }

    private int buildButtonStatus(ProductM productM) {
        return 0;
    }

    private double buildPicScale(ProductM productM) {
       return 0;
    }


    private String buildTitle(ProductM productM) {
       return productM.getTitle();
    }

    private String buildDetailJumpUrl(ProductM productM, ProductDetailPageRequest request) {
        String jumpUrl = productM.getJumpUrl();
        String fromPage = StringUtils.isBlank(request.getFromPage()) ? StringUtils.EMPTY : request.getFromPage();
        return String.format("%s&source=deal_detail&fromPage=%s", jumpUrl, fromPage);
    }

    private String buildProductDesc(ProductM productM) {
        if(productM == null ) {
            return null;
        }

        String dealSubTitleAttr = productM.getAttr("dealSubTitle");
        if(org.apache.commons.lang3.StringUtils.isBlank(dealSubTitleAttr)) {
            return null;
        }

        List<String> list = JSONObject.parseArray(dealSubTitleAttr, String.class);
        return String.join("・", list);
    }

    private String buildButtonJumpUrl(ProductM productM, ProductDetailPageRequest request) {
        if (productM == null) {
            return org.apache.commons.lang3.StringUtils.EMPTY;
        }
        return productM.getOrderUrl();
    }

    private String buildButtonName(ProductM productM) {
        return "抢购";
    }

    private String buildSale(ProductM productM) {
        if (productM == null) {
            return org.apache.commons.lang3.StringUtils.EMPTY;
        }
        return Optional.ofNullable(productM.getSale()).map(ProductSaleM::getSaleTag).orElse(org.apache.commons.lang3.StringUtils.EMPTY);

    }

    private List<String> buildProductTags(ProductM productM, ProductDetailPageRequest request) {
        if(productM == null || productM.getBasePrice() == null || productM.getMarketPrice() == null) {
            return Lists.newArrayList();
        }

        List<String> productTags = Lists.newArrayList();
        BigDecimal salePrice;
        String pageSource = request.getPageSource();
        if (Objects.isNull(pageSource)) {
            pageSource = StringUtils.EMPTY;
        }
        if(pageSource.equals("deal")) {
            salePrice = productM.getShopCarPrice();
        } else if(pageSource.equals("shopcarnoselect") || pageSource.equals("shopcarselect")) {
            salePrice = productM.getShopCarPrice();
        } else {
            salePrice = productM.getBasePrice();
        }


        String discountTag = buildDiscountTag(salePrice, new BigDecimal(productM.getMarketPrice()));
        if(org.apache.commons.lang3.StringUtils.isNotBlank(discountTag)) {
            productTags.add(discountTag);
        }

        String pricePowerTag = productM.getAttr("highestPriorityPricePowerTagAttr");
        if(org.apache.commons.lang3.StringUtils.isNotBlank(pricePowerTag)) {
            productTags.add(pricePowerTag);
        }

        return productTags;
    }

    private String buildDiscountTag(BigDecimal salePrice, BigDecimal marketPrice) {
        if(salePrice == null || marketPrice == null) {
            return null;
        }

        if(marketPrice.compareTo(salePrice) == 0) {
            return null;
        }
        return salePrice.divide(marketPrice, 2).multiply(new BigDecimal(10)).stripTrailingZeros().toPlainString() + "折";
    }

    private boolean buildAvailable(ProductM productM) {
       return Objects.nonNull(productM.getAvailable()) ? productM.getAvailable() : false;
    }

    private String buildMarketPrice(ProductM productM) {
        if (productM != null) {
            return productM.getMarketPrice();
        }
        return StringUtils.EMPTY;
    }

    public static String buildMarketPriceCent(String marketPriceStr){
        if(StringUtils.isBlank(marketPriceStr)){
            return "";
        }
        try {
            return new BigDecimal(marketPriceStr).multiply(new BigDecimal(100)).toPlainString();
        } catch (Exception e){
            log.error("getMarketPriceCent error, marketPriceStr:{}", marketPriceStr, e);
            return "";
        }
    }

    private Map<String, Object> buildExtAttrs(ProductM productM) {
        if(productM == null) {
            return null;
        }

        String dealApplyShopNumberAttr = productM.getAttr("dealApplyShopNumberAttr");
        if(StringUtils.isNotBlank(dealApplyShopNumberAttr)) {
            Map<String, Object> map = Maps.newHashMap();
            map.put("dealApplyShopNumberAttr", dealApplyShopNumberAttr);
            return map;
        }

        return null;
    }

    private ShopVO loadShop(ProductM productM) {
        if (productM == null || CollectionUtils.isEmpty(productM.getShopMs())) {
            return null;
        }
        ShopM shopM = productM.getShopMs().get(0);
        ShopVO shopVO = new ShopVO();
        shopVO.setShopId(shopM.getLongShopId());
        shopVO.setShopName(shopM.getShopName());
        shopVO.setDistance(shopM.getDistance());
        shopVO.setJumpUrl(shopM.getDetailUrl());
        shopVO.setRegionName(shopM.getMainRegionName());
        shopVO.setScoreTag(shopM.getScoreTag());
        return shopVO;
    }

    private String buildSalePrice(ProductM productM, ProductDetailPageRequest request) {
        if (Objects.isNull(productM)) {
            return StringUtils.EMPTY;
        }

        String pageSource = request.getPageSource();
        if (Objects.isNull(pageSource)) {
            pageSource = StringUtils.EMPTY;
        }
        if(pageSource.equals("deal")) {
            if (productM.getShopCarPrice() == null){
                return StringUtils.EMPTY;
            }
            return productM.getShopCarPrice().stripTrailingZeros().toPlainString();
        } else if(pageSource.equals("shopcarnoselect") || pageSource.equals("shopcarselect")) {
            if (productM.getShopCarPrice() == null){
                return StringUtils.EMPTY;
            }
            return productM.getShopCarPrice().stripTrailingZeros().toPlainString();
        } else {
            return getPriceStr(productM.getPayPrice());
        }
    }

    private String getPriceStr(BigDecimal price) {
        return Optional.ofNullable(price)
                .map(item -> item.setScale(2, RoundingMode.UP).stripTrailingZeros().toPlainString())
                .orElse(org.apache.commons.lang.StringUtils.EMPTY);
    }

    private String buildDzPic(ProductM productM, boolean isMt) {
        if (productM != null) {
            return ImageUtil.format(productM.getPicUrl(), 100, 100, isMt);
        }
        return StringUtils.EMPTY;
    }

    private List<DzPromoVO> buildDzPromoList(ProductM productM) {
        if(productM == null) {
            return Lists.newArrayList();
        }
        DzPromoVO dzPromoVO = new DzPromoVO();
        dzPromoVO.setTextColor("#FF4B10");
        DzPictureComponent dzPictureComponentVO = new DzPictureComponent();
        dzPictureComponentVO.setPicUrl("https://p0.meituan.net/travelcube/c0274edcf2dc5a3a4e0b276d28f150831022.png");
        dzPictureComponentVO.setPicHeight(12);
        dzPictureComponentVO.setAspectRadio(1);
        dzPromoVO.setPrePic(dzPictureComponentVO);
        return Lists.newArrayList(dzPromoVO);
    }

    private String buildDiscountTag(ProductDetailPageRequest request, ProductM productM) {
        if(productM == null || productM.getBasePrice() == null || productM.getMarketPrice() == null) {
            return null;
        }

        BigDecimal salePrice;
        String pageSource = request.getPageSource();
        if (Objects.isNull(pageSource)) {
            pageSource = StringUtils.EMPTY;
        }
        if(pageSource.equals("deal")) {
            salePrice = productM.getShopCarPrice();
        } else if(pageSource.equals("shopcarnoselect") || pageSource.equals("shopcarselect")) {
            salePrice = productM.getShopCarPrice();
        } else {
            salePrice = productM.getPayPrice();
        }

        String discountTag = buildDiscountTag(salePrice, new BigDecimal(productM.getMarketPrice()));
        if(org.apache.commons.lang3.StringUtils.isNotBlank(discountTag)) {
            return discountTag;
        }
        return null;
    }


}