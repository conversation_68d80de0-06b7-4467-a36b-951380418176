package com.sankuai.dzshoppingguide.product.detail.application.service.promo;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSONObject;
import com.dianping.lion.client.Lion;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.sankuai.dealuser.price.display.api.enums.ExtendDisplayInfoKeyEnum;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.promo.CouponItemPackageDTO;
import com.sankuai.dzshoppingguide.product.detail.spi.promo.vo.InflateCounponDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.nibmkt.promotion.api.common.enums.MagicalMemberTagShowTypeEnum;
import com.sankuai.nibmkt.promotion.api.query.model.magicalmember.response.MagicalMemberTagTextDTO;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class PromoDetailServiceBuildInflateCouponTest {

    @InjectMocks
    private PromoDetailService service;

    @Mock
    private ProductDetailPageRequest request;

    @Mock
    private ProductBaseInfo dealGroupBase;

    @Mock
    private DealGroupCategoryDTO category;

    private static final String VALID_MAGICAL_MEMBER_LABEL_JSON = "{\"magicalMemberCouponTag\":\"VIP\",\"inflateShowText\":\"Inflate Text\",\"reduceMoney\":\"10\",\"status\":\"Active\",\"showType\":\"1\"}";

    private static final String VALID_COUPON_WALLET_INFO_JSON = "{\"couponPackageList\":[{\"couponPackageId\":\"123\"}]}";

    @Mock
    private PriceDisplayDTO normalPrice;

    @BeforeEach
    void setUp() {
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.MT_APP);
        when(request.getPageSource()).thenReturn("test");
        when(request.getGpsCityId()).thenReturn(1);
        when(dealGroupBase.getCategory()).thenReturn(category);
        when(category.getCategoryId()).thenReturn(100L);
    }

    /**
     * Test case for null deal price
     */
    @Test
    void testBuildInflateCoupon_WithNullDealPrice() throws Throwable {
        // act
        InflateCounponDTO result = service.buildInflateCoupon(request, dealGroupBase, null);
        // assert
        assertNull(result, "Result should be null when dealPrice is null");
    }

    /**
     * Test case for empty extend display info
     */
    @Test
    void testBuildInflateCoupon_WithEmptyExtendDisplayInfo() throws Throwable {
        // arrange
        PriceDisplayDTO dealPromoPrice = mock(PriceDisplayDTO.class);
        when(dealPromoPrice.getExtendDisplayInfo()).thenReturn(Collections.emptyMap());
        // act
        InflateCounponDTO result = service.buildInflateCoupon(request, dealGroupBase, dealPromoPrice);
        // assert
        assertNull(result, "Result should be null when extendDisplayInfo is empty");
    }

    /**
     * Test case for null extend display info
     */
    @Test
    void testBuildInflateCoupon_WithNullExtendDisplayInfo() throws Throwable {
        // arrange
        PriceDisplayDTO dealPromoPrice = mock(PriceDisplayDTO.class);
        when(dealPromoPrice.getExtendDisplayInfo()).thenReturn(null);
        // act
        InflateCounponDTO result = service.buildInflateCoupon(request, dealGroupBase, dealPromoPrice);
        // assert
        assertNull(result, "Result should be null when extendDisplayInfo is null");
    }

    /**
     * Test case for missing magical member coupon label
     */
    @Test
    void testBuildInflateCoupon_WithMissingMagicalMemberCouponLabel() throws Throwable {
        // arrange
        PriceDisplayDTO dealPromoPrice = mock(PriceDisplayDTO.class);
        Map<String, String> extendDisplayInfo = new HashMap<>();
        when(dealPromoPrice.getExtendDisplayInfo()).thenReturn(extendDisplayInfo);
        // act
        InflateCounponDTO result = service.buildInflateCoupon(request, dealGroupBase, dealPromoPrice);
        // assert
        assertNull(result, "Result should be null when magical member coupon label is missing");
    }

    /**
     * Test case for empty magical member coupon label
     */
    @Test
    void testBuildInflateCoupon_WithEmptyMagicalMemberCouponLabel() throws Throwable {
        // arrange
        PriceDisplayDTO dealPromoPrice = mock(PriceDisplayDTO.class);
        Map<String, String> extendDisplayInfo = new HashMap<>();
        extendDisplayInfo.put(ExtendDisplayInfoKeyEnum.MAGICAL_MEMBER_COUPON_LABEL.getKey(), "");
        when(dealPromoPrice.getExtendDisplayInfo()).thenReturn(extendDisplayInfo);
        // act
        InflateCounponDTO result = service.buildInflateCoupon(request, dealGroupBase, dealPromoPrice);
        // assert
        assertNull(result, "Result should be null when magical member coupon label is empty");
    }

    /**
     * Test case for invalid magical member coupon label JSON
     */
    @Test
    void testBuildInflateCoupon_WithInvalidMagicalMemberCouponLabelJson() throws Throwable {
        // arrange
        PriceDisplayDTO dealPromoPrice = mock(PriceDisplayDTO.class);
        Map<String, String> extendDisplayInfo = new HashMap<>();
        extendDisplayInfo.put(ExtendDisplayInfoKeyEnum.MAGICAL_MEMBER_COUPON_LABEL.getKey(), "invalid json");
        when(dealPromoPrice.getExtendDisplayInfo()).thenReturn(extendDisplayInfo);
        // act
        InflateCounponDTO result = service.buildInflateCoupon(request, dealGroupBase, dealPromoPrice);
        // assert
        assertNull(result, "Result should be null when magical member coupon label JSON is invalid");
    }

    /**
     * Test case for valid magical member coupon label with invalid coupon wallet info
     */
    @Test
    void testBuildInflateCoupon_WithValidLabelAndInvalidWalletInfo() throws Throwable {
        // arrange
        PriceDisplayDTO dealPromoPrice = mock(PriceDisplayDTO.class);
        Map<String, String> extendDisplayInfo = new HashMap<>();
        extendDisplayInfo.put(ExtendDisplayInfoKeyEnum.MAGICAL_MEMBER_COUPON_LABEL.getKey(), VALID_MAGICAL_MEMBER_LABEL_JSON);
        extendDisplayInfo.put(ExtendDisplayInfoKeyEnum.COUPON_WALLET_INFO.getKey(), "invalid json");
        when(dealPromoPrice.getExtendDisplayInfo()).thenReturn(extendDisplayInfo);
        // act
        InflateCounponDTO result = service.buildInflateCoupon(request, dealGroupBase, dealPromoPrice);
        // assert
        assertNotNull(result, "Result should not be null when magical member coupon label is valid");
        assertTrue(result.getCouponGroupIdList().isEmpty(), "Coupon group ID list should be empty for invalid wallet info");
    }

    /**
     * Test case for null client type
     */
    @Test
    void testBuildInflateCoupon_WithNullClientType() throws Throwable {
        // arrange
        when(request.getClientTypeEnum()).thenReturn(null);
        PriceDisplayDTO dealPromoPrice = mock(PriceDisplayDTO.class);
        Map<String, String> extendDisplayInfo = new HashMap<>();
        extendDisplayInfo.put(ExtendDisplayInfoKeyEnum.MAGICAL_MEMBER_COUPON_LABEL.getKey(), VALID_MAGICAL_MEMBER_LABEL_JSON);
        // act & assert
        assertThrows(NullPointerException.class, () -> service.buildInflateCoupon(request, dealGroupBase, dealPromoPrice), "Should throw NullPointerException when client type is null");
    }

    /**
     * Test case for null dealPromoPrice
     */
    @Test
    public void testBuildInflateCouponWithNullDealPrice() throws Throwable {
        // arrange
        when(dealGroupBase.getCategory()).thenReturn(category);
        when(category.getCategoryId()).thenReturn(123L);
        when(request.getPageSource()).thenReturn("test");
        when(request.getGpsCityId()).thenReturn(1);
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.MT_APP);
        // act
        InflateCounponDTO result = service.buildInflateCoupon(request, dealGroupBase, null);
        // assert
        assertNull(result);
    }

    /**
     * Test case for empty extendDisplayInfo
     */
    @Test
    public void testBuildInflateCouponWithEmptyExtendDisplayInfo() throws Throwable {
        // arrange
        when(dealGroupBase.getCategory()).thenReturn(category);
        when(category.getCategoryId()).thenReturn(123L);
        when(request.getPageSource()).thenReturn("test");
        when(request.getGpsCityId()).thenReturn(1);
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.MT_APP);
        when(normalPrice.getExtendDisplayInfo()).thenReturn(new HashMap<>());
        // act
        InflateCounponDTO result = service.buildInflateCoupon(request, dealGroupBase, normalPrice);
        // assert
        assertNull(result);
    }

    /**
     * Test case for null magicalMemberCouponLabel
     */
    @Test
    public void testBuildInflateCouponWithNullMagicalMemberLabel() throws Throwable {
        // arrange
        when(dealGroupBase.getCategory()).thenReturn(category);
        when(category.getCategoryId()).thenReturn(123L);
        when(request.getPageSource()).thenReturn("test");
        when(request.getGpsCityId()).thenReturn(1);
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.MT_APP);
        Map<String, String> extendDisplayInfo = new HashMap<>();
        extendDisplayInfo.put(ExtendDisplayInfoKeyEnum.MAGICAL_MEMBER_COUPON_LABEL.getKey(), null);
        when(normalPrice.getExtendDisplayInfo()).thenReturn(extendDisplayInfo);
        // act
        InflateCounponDTO result = service.buildInflateCoupon(request, dealGroupBase, normalPrice);
        // assert
        assertNull(result);
    }

    /**
     * Test case for valid magicalMemberCouponLabel but invalid JSON
     */
    @Test
    public void testBuildInflateCouponWithInvalidJsonLabel() throws Throwable {
        // arrange
        when(dealGroupBase.getCategory()).thenReturn(category);
        when(category.getCategoryId()).thenReturn(123L);
        when(request.getPageSource()).thenReturn("test");
        when(request.getGpsCityId()).thenReturn(1);
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.MT_APP);
        Map<String, String> extendDisplayInfo = new HashMap<>();
        extendDisplayInfo.put(ExtendDisplayInfoKeyEnum.MAGICAL_MEMBER_COUPON_LABEL.getKey(), "invalid json");
        when(normalPrice.getExtendDisplayInfo()).thenReturn(extendDisplayInfo);
        // act
        InflateCounponDTO result = service.buildInflateCoupon(request, dealGroupBase, normalPrice);
        // assert
        assertNull(result);
    }

    /**
     * Test case for successful inflation with normal theme
     */
    @Test
    public void testBuildInflateCouponWithNormalTheme() throws Throwable {
        // arrange
        when(dealGroupBase.getCategory()).thenReturn(category);
        when(category.getCategoryId()).thenReturn(123L);
        when(request.getPageSource()).thenReturn("test");
        when(request.getGpsCityId()).thenReturn(1);
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.MT_APP);
        Map<String, String> extendDisplayInfo = new HashMap<>();
        MagicalMemberTagTextDTO tagTextDTO = new MagicalMemberTagTextDTO();
        tagTextDTO.setShowType(String.valueOf(MagicalMemberTagShowTypeEnum.INFLATED_POI_HAS_PRE_INFLATED_MMC.getCode()));
        tagTextDTO.setMagicalMemberCouponTag("test tag");
        tagTextDTO.setInflateShowText("test inflate");
        tagTextDTO.setReduceMoney("100");
        tagTextDTO.setStatus("1");
        extendDisplayInfo.put(ExtendDisplayInfoKeyEnum.MAGICAL_MEMBER_COUPON_LABEL.getKey(), JSONObject.toJSONString(tagTextDTO));
        when(normalPrice.getExtendDisplayInfo()).thenReturn(extendDisplayInfo);
        // act
        InflateCounponDTO result = service.buildInflateCoupon(request, dealGroupBase, normalPrice);
        // assert
        assertNotNull(result);
        assertEquals("test tag", result.getLogoIcon());
        assertEquals("test inflate", result.getCouponDesc());
        assertEquals("100", result.getMaxInflateMoney());
    }

    /**
     * Test case for successful inflation with VIP theme
     */
    @Test
    public void testBuildInflateCouponWithVipTheme() throws Throwable {
        // arrange
        when(dealGroupBase.getCategory()).thenReturn(category);
        when(category.getCategoryId()).thenReturn(123L);
        when(request.getPageSource()).thenReturn("test");
        when(request.getGpsCityId()).thenReturn(1);
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.MT_APP);
        Map<String, String> extendDisplayInfo = new HashMap<>();
        MagicalMemberTagTextDTO tagTextDTO = new MagicalMemberTagTextDTO();
        tagTextDTO.setShowType(String.valueOf(MagicalMemberTagShowTypeEnum.BASIC_POI_HAS_PRE_INFLATED_MMC.getCode()));
        tagTextDTO.setMagicalMemberCouponTag("test tag");
        tagTextDTO.setInflateShowText("test inflate");
        tagTextDTO.setReduceMoney("100");
        tagTextDTO.setStatus("1");
        extendDisplayInfo.put(ExtendDisplayInfoKeyEnum.MAGICAL_MEMBER_COUPON_LABEL.getKey(), JSONObject.toJSONString(tagTextDTO));
        when(normalPrice.getExtendDisplayInfo()).thenReturn(extendDisplayInfo);
        // act
        InflateCounponDTO result = service.buildInflateCoupon(request, dealGroupBase, normalPrice);
        // assert
        assertNotNull(result);
        assertEquals("test tag", result.getLogoIcon());
        assertEquals("test inflate", result.getCouponDesc());
        assertEquals("100", result.getMaxInflateMoney());
    }

    /**
     * Test case for successful inflation with coupon items
     */
    @Test
    public void testBuildInflateCouponWithCouponItems() throws Throwable {
        // arrange
        when(dealGroupBase.getCategory()).thenReturn(category);
        when(category.getCategoryId()).thenReturn(123L);
        when(request.getPageSource()).thenReturn("test");
        when(request.getGpsCityId()).thenReturn(1);
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.MT_APP);
        Map<String, String> extendDisplayInfo = new HashMap<>();
        MagicalMemberTagTextDTO tagTextDTO = new MagicalMemberTagTextDTO();
        tagTextDTO.setShowType(String.valueOf(MagicalMemberTagShowTypeEnum.BASIC_POI_HAS_PRE_INFLATED_MMC.getCode()));
        tagTextDTO.setMagicalMemberCouponTag("test tag");
        extendDisplayInfo.put(ExtendDisplayInfoKeyEnum.MAGICAL_MEMBER_COUPON_LABEL.getKey(), JSONObject.toJSONString(tagTextDTO));
        when(normalPrice.getExtendDisplayInfo()).thenReturn(extendDisplayInfo);
        when(request.getUserId()).thenReturn(123L);
        // act
        InflateCounponDTO result = service.buildInflateCoupon(request, dealGroupBase, normalPrice);
        // assert
        assertNotNull(result);
    }

    /**
     * Test case for successful inflation with nib biz
     */
    @Test
    public void testBuildInflateCouponWithNibBiz() throws Throwable {
        // arrange
        when(dealGroupBase.getCategory()).thenReturn(category);
        when(category.getCategoryId()).thenReturn(123L);
        when(request.getPageSource()).thenReturn("test");
        when(request.getGpsCityId()).thenReturn(1);
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.MT_APP);
        Map<String, String> extendDisplayInfo = new HashMap<>();
        MagicalMemberTagTextDTO tagTextDTO = new MagicalMemberTagTextDTO();
        tagTextDTO.setShowType(String.valueOf(MagicalMemberTagShowTypeEnum.BASIC_POI_HAS_PRE_INFLATED_MMC.getCode()));
        tagTextDTO.setMagicalMemberCouponTag("test tag");
        extendDisplayInfo.put(ExtendDisplayInfoKeyEnum.MAGICAL_MEMBER_COUPON_LABEL.getKey(), JSONObject.toJSONString(tagTextDTO));
        when(normalPrice.getExtendDisplayInfo()).thenReturn(extendDisplayInfo);
        // act
        InflateCounponDTO result = service.buildInflateCoupon(request, dealGroupBase, normalPrice);
        // assert
        assertNotNull(result);
    }

    /**
     * Test case for successful inflation with client type metrics
     */
    @Test
    public void testBuildInflateCouponWithClientTypeMetrics() throws Throwable {
        // arrange
        when(dealGroupBase.getCategory()).thenReturn(category);
        when(category.getCategoryId()).thenReturn(123L);
        when(request.getPageSource()).thenReturn("test");
        when(request.getGpsCityId()).thenReturn(1);
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.UNKNOWN);
        Map<String, String> extendDisplayInfo = new HashMap<>();
        MagicalMemberTagTextDTO tagTextDTO = new MagicalMemberTagTextDTO();
        tagTextDTO.setShowType(String.valueOf(MagicalMemberTagShowTypeEnum.BASIC_POI_HAS_PRE_INFLATED_MMC.getCode()));
        tagTextDTO.setMagicalMemberCouponTag("test tag");
        extendDisplayInfo.put(ExtendDisplayInfoKeyEnum.MAGICAL_MEMBER_COUPON_LABEL.getKey(), JSONObject.toJSONString(tagTextDTO));
        when(normalPrice.getExtendDisplayInfo()).thenReturn(extendDisplayInfo);
        // act
        InflateCounponDTO result = service.buildInflateCoupon(request, dealGroupBase, normalPrice);
        // assert
        assertNotNull(result);
        assertEquals(ClientTypeEnum.UNKNOWN.getCode(), PromoDetailService.getCatBusinessClientType(request.getClientTypeEnum()));
    }
}
