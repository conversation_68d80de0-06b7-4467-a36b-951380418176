package com.sankuai.dzshoppingguide.product.detail.application.fetcher.sku;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import com.sankuai.dztheme.deal.res.DealSkuSummaryDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @create 2025/5/28 10:41
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class DealSkuSummaryReturnValue extends FetcherReturnValueDTO {
    private DealSkuSummaryDTO dealSkuSummaryDTO;
}
