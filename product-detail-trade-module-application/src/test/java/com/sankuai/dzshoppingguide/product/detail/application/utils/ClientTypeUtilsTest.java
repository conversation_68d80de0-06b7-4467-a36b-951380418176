package com.sankuai.dzshoppingguide.product.detail.application.utils;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.mpmctmember.query.common.enums.PlatformEnum;
import java.util.Locale;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * Test cases for ClientTypeUtils.isAndroid method
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("ClientTypeUtils isIos Method Tests")
public class ClientTypeUtilsTest {

    /**
     * Test isPureHarmony with userAgent containing "openharmony" in different cases
     */
    @ParameterizedTest(name = "Test isPureHarmony with userAgent: {0}")
    @ValueSource(strings = { "openharmony", "OpenHarmony", "OPENHARMONY", "Mozilla/5.0 (OpenHarmony; Android 10)", "Some prefix openharmony some suffix" })
    public void testIsPureHarmonyWithOpenHarmonyUserAgent(String userAgent) {
        // act
        boolean result = ClientTypeUtils.isPureHarmony(userAgent);
        // assert
        assertTrue(result, "Should return true when userAgent contains 'openharmony' in any case");
    }

    /**
     * Test isAndroid method with null input
     */
    @Test
    public void testIsAndroid_WithNullInput() {
        // arrange
        String osType = null;
        // act
        boolean result = ClientTypeUtils.isAndroid(osType);
        // assert
        assertFalse(result, "Should return false for null input");
    }

    /**
     * Test isAndroid method with empty string input
     */
    @Test
    public void testIsAndroid_WithEmptyString() {
        // arrange
        String osType = "";
        // act
        boolean result = ClientTypeUtils.isAndroid(osType);
        // assert
        assertFalse(result, "Should return false for empty string");
    }

    /**
     * Test isAndroid method with whitespace input
     */
    @Test
    public void testIsAndroid_WithWhitespace() {
        // arrange
        String osType = "   ";
        // act
        boolean result = ClientTypeUtils.isAndroid(osType);
        // assert
        assertFalse(result, "Should return false for whitespace string");
    }

    /**
     * Test isAndroid method with lowercase android input
     */
    @Test
    public void testIsAndroid_WithLowercaseAndroid() {
        // arrange
        String osType = "android";
        // act
        boolean result = ClientTypeUtils.isAndroid(osType);
        // assert
        assertTrue(result, "Should return true for lowercase 'android'");
    }

    /**
     * Test isAndroid method with uppercase android input
     */
    @Test
    public void testIsAndroid_WithUppercaseAndroid() {
        // arrange
        String osType = "ANDROID";
        // act
        boolean result = ClientTypeUtils.isAndroid(osType);
        // assert
        assertTrue(result, "Should return true for uppercase 'ANDROID'");
    }

    /**
     * Test isAndroid method with mixed case android input
     */
    @Test
    public void testIsAndroid_WithMixedCaseAndroid() {
        // arrange
        String osType = "AnDrOiD";
        // act
        boolean result = ClientTypeUtils.isAndroid(osType);
        // assert
        assertTrue(result, "Should return true for mixed case 'AnDrOiD'");
    }

    /**
     * Test isAndroid method with non-android input
     */
    @Test
    public void testIsAndroid_WithNonAndroidInput() {
        // arrange
        String osType = "ios";
        // act
        boolean result = ClientTypeUtils.isAndroid(osType);
        // assert
        assertFalse(result, "Should return false for non-android input");
    }

    /**
     * Test isAndroid method with android substring input
     */
    @Test
    public void testIsAndroid_WithAndroidSubstring() {
        // arrange
        String osType = "android_12";
        // act
        boolean result = ClientTypeUtils.isAndroid(osType);
        // assert
        assertFalse(result, "Should return false for string containing 'android' as substring");
    }

    /**
     * Test scenario: Input is null
     * Expected: Should return false
     */
    @Test
    @DisplayName("Should return false when osType is null")
    public void testIsIos_WhenOsTypeIsNull() {
        // arrange
        String osType = null;
        // act
        boolean result = ClientTypeUtils.isIos(osType);
        // assert
        assertFalse(result, "Should return false for null osType");
    }

    /**
     * Test scenario: Input is empty string
     * Expected: Should return false
     */
    @Test
    @DisplayName("Should return false when osType is empty")
    public void testIsIos_WhenOsTypeIsEmpty() {
        // arrange
        String osType = "";
        // act
        boolean result = ClientTypeUtils.isIos(osType);
        // assert
        assertFalse(result, "Should return false for empty osType");
    }

    /**
     * Test scenario: Input contains only whitespace
     * Expected: Should return false
     */
    @Test
    @DisplayName("Should return false when osType is whitespace")
    public void testIsIos_WhenOsTypeIsWhitespace() {
        // arrange
        String osType = "   ";
        // act
        boolean result = ClientTypeUtils.isIos(osType);
        // assert
        assertFalse(result, "Should return false for whitespace osType");
    }

    /**
     * Test scenario: Input is "ios" in lowercase
     * Expected: Should return true
     */
    @Test
    @DisplayName("Should return true when osType is lowercase ios")
    public void testIsIos_WhenOsTypeIsLowerCase() {
        // arrange
        String osType = "ios";
        // act
        boolean result = ClientTypeUtils.isIos(osType);
        // assert
        assertTrue(result, "Should return true for lowercase ios");
    }

    /**
     * Test scenario: Input is "IOS" in uppercase
     * Expected: Should return true
     */
    @Test
    @DisplayName("Should return true when osType is uppercase IOS")
    public void testIsIos_WhenOsTypeIsUpperCase() {
        // arrange
        String osType = "IOS";
        // act
        boolean result = ClientTypeUtils.isIos(osType);
        // assert
        assertTrue(result, "Should return true for uppercase IOS");
    }

    /**
     * Test scenario: Input is "iOs" in mixed case
     * Expected: Should return true
     */
    @Test
    @DisplayName("Should return true when osType is mixed case")
    public void testIsIos_WhenOsTypeIsMixedCase() {
        // arrange
        String osType = "iOs";
        // act
        boolean result = ClientTypeUtils.isIos(osType);
        // assert
        assertTrue(result, "Should return true for mixed case iOs");
    }

    /**
     * Test scenario: Input is non-iOS value
     * Expected: Should return false
     */
    @Test
    @DisplayName("Should return false when osType is not ios")
    public void testIsIos_WhenOsTypeIsNotIos() {
        // arrange
        String osType = "android";
        // act
        boolean result = ClientTypeUtils.isIos(osType);
        // assert
        assertFalse(result, "Should return false for non-ios osType");
    }

    /**
     * Test scenario: Input has surrounding spaces
     * Expected: Should return false
     */
    @Test
    @DisplayName("Should return false when osType has surrounding spaces")
    public void testIsIos_WhenOsTypeHasSurroundingSpaces() {
        // arrange
        String osType = " ios ";
        // act
        boolean result = ClientTypeUtils.isIos(osType);
        // assert
        assertFalse(result, "Should return false for ios with surrounding spaces");
    }

    /**
     * Test isPureHarmony with null userAgent
     */
    @Test
    @DisplayName("Test isPureHarmony with null userAgent")
    public void testIsPureHarmonyWithNullUserAgent() throws Throwable {
        // arrange
        String userAgent = null;
        // act
        boolean result = ClientTypeUtils.isPureHarmony(userAgent);
        // assert
        assertFalse(result, "Should return false for null userAgent");
    }

    /**
     * Test isPureHarmony with empty userAgent
     */
    @Test
    @DisplayName("Test isPureHarmony with empty userAgent")
    public void testIsPureHarmonyWithEmptyUserAgent() throws Throwable {
        // arrange
        String userAgent = "";
        // act
        boolean result = ClientTypeUtils.isPureHarmony(userAgent);
        // assert
        assertFalse(result, "Should return false for empty userAgent");
    }

    /**
     * Test isPureHarmony with blank userAgent
     */
    @Test
    @DisplayName("Test isPureHarmony with blank userAgent")
    public void testIsPureHarmonyWithBlankUserAgent() throws Throwable {
        // arrange
        String userAgent = "    ";
        // act
        boolean result = ClientTypeUtils.isPureHarmony(userAgent);
        // assert
        assertFalse(result, "Should return false for blank userAgent");
    }

    /**
     * Test isPureHarmony with userAgent not containing "openharmony"
     */
    @Test
    @DisplayName("Test isPureHarmony with non-OpenHarmony userAgent")
    public void testIsPureHarmonyWithNonOpenHarmonyUserAgent() throws Throwable {
        // arrange
        String userAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64)";
        // act
        boolean result = ClientTypeUtils.isPureHarmony(userAgent);
        // assert
        assertFalse(result, "Should return false when userAgent doesn't contain 'openharmony'");
    }

    /**
     * Test isPureHarmony with special characters in userAgent
     */
    @Test
    @DisplayName("Test isPureHarmony with special characters")
    public void testIsPureHarmonyWithSpecialCharacters() throws Throwable {
        // arrange
        String userAgent = "!@#$%^&*()_+openharmony!@#$%^&*()_+";
        // act
        boolean result = ClientTypeUtils.isPureHarmony(userAgent);
        // assert
        assertTrue(result, "Should return true when userAgent contains 'openharmony' with special characters");
    }

    /**
     * Test isPureHarmony with very long userAgent
     */
    @Test
    @DisplayName("Test isPureHarmony with long userAgent")
    public void testIsPureHarmonyWithLongUserAgent() throws Throwable {
        // arrange
        StringBuilder userAgent = new StringBuilder();
        for (int i = 0; i < 1000; i++) {
            userAgent.append("a");
        }
        userAgent.append("openharmony");
        // act
        boolean result = ClientTypeUtils.isPureHarmony(userAgent.toString());
        // assert
        assertTrue(result, "Should return true when userAgent contains 'openharmony' in a long string");
    }

    /**
     * 测试点评小程序场景
     * 当输入DP_XCX时，应返回DP_APPLET
     */
    @Test
    @DisplayName("When input is DP_XCX then return DP_APPLET")
    public void testGenPlatform_DpXcx() throws Throwable {
        // arrange
        ClientTypeEnum clientType = ClientTypeEnum.DP_XCX;
        // act
        PlatformEnum result = ClientTypeUtils.genPlatform(clientType);
        // assert
        assertNotNull(result);
        assertEquals(PlatformEnum.DP_APPLET, result);
    }

    /**
     * 测试美团小程序场景
     * 当输入MT_XCX时，应返回MT_APPLET
     */
    @Test
    @DisplayName("When input is MT_XCX then return MT_APPLET")
    public void testGenPlatform_MtXcx() throws Throwable {
        // arrange
        ClientTypeEnum clientType = ClientTypeEnum.MT_XCX;
        // act
        PlatformEnum result = ClientTypeUtils.genPlatform(clientType);
        // assert
        assertNotNull(result);
        assertEquals(PlatformEnum.MT_APPLET, result);
    }

    /**
     * 测试美团主APP场景
     * 当输入MT_APP时，应返回MT_APP
     */
    @Test
    @DisplayName("When input is MT_APP then return MT_APP")
    public void testGenPlatform_MtApp() throws Throwable {
        // arrange
        ClientTypeEnum clientType = ClientTypeEnum.MT_APP;
        // act
        PlatformEnum result = ClientTypeUtils.genPlatform(clientType);
        // assert
        assertNotNull(result);
        assertEquals(PlatformEnum.MT_APP, result);
    }

    /**
     * 测试点评主APP场景
     * 当输入DP_APP时，应返回DP_APP
     */
    @Test
    @DisplayName("When input is DP_APP then return DP_APP")
    public void testGenPlatform_DpApp() throws Throwable {
        // arrange
        ClientTypeEnum clientType = ClientTypeEnum.DP_APP;
        // act
        PlatformEnum result = ClientTypeUtils.genPlatform(clientType);
        // assert
        assertNotNull(result);
        assertEquals(PlatformEnum.DP_APP, result);
    }

    /**
     * 测试美团其他端场景
     * 当输入MT_PC时，应返回MT_APP
     */
    @Test
    @DisplayName("When input is MT_PC then return MT_APP")
    public void testGenPlatform_MtPc() throws Throwable {
        // arrange
        ClientTypeEnum clientType = ClientTypeEnum.MT_PC;
        // act
        PlatformEnum result = ClientTypeUtils.genPlatform(clientType);
        // assert
        assertNotNull(result);
        assertEquals(PlatformEnum.MT_APP, result);
    }

    /**
     * 测试点评其他端场景
     * 当输入DP_PC时，应返回DP_APP
     */
    @Test
    @DisplayName("When input is DP_PC then return DP_APP")
    public void testGenPlatform_DpPc() throws Throwable {
        // arrange
        ClientTypeEnum clientType = ClientTypeEnum.DP_PC;
        // act
        PlatformEnum result = ClientTypeUtils.genPlatform(clientType);
        // assert
        assertNotNull(result);
        assertEquals(PlatformEnum.DP_APP, result);
    }

    /**
     * 测试点评百度地图小程序场景
     * 当输入DP_BAIDU_MAP_XCX时，应返回DP_APP
     */
    @Test
    @DisplayName("When input is DP_BAIDU_MAP_XCX then return DP_APP")
    public void testGenPlatform_DpBaiduMapXcx() throws Throwable {
        // arrange
        ClientTypeEnum clientType = ClientTypeEnum.DP_BAIDU_MAP_XCX;
        // act
        PlatformEnum result = ClientTypeUtils.genPlatform(clientType);
        // assert
        assertNotNull(result);
        assertEquals(PlatformEnum.DP_APP, result);
    }

    /**
     * 测试美团外卖APP场景
     * 当输入MT_WAI_MAI_APP时，应返回MT_APP
     */
    @Test
    @DisplayName("When input is MT_WAI_MAI_APP then return MT_APP")
    public void testGenPlatform_MtWaiMaiApp() throws Throwable {
        // arrange
        ClientTypeEnum clientType = ClientTypeEnum.MT_WAI_MAI_APP;
        // act
        PlatformEnum result = ClientTypeUtils.genPlatform(clientType);
        // assert
        assertNotNull(result);
        assertEquals(PlatformEnum.MT_APP, result);
    }

    /**
     * 测试未知类型场景
     * 当输入UNKNOWN时，应返回DP_APP
     */
    @Test
    @DisplayName("When input is UNKNOWN then return DP_APP")
    public void testGenPlatform_Unknown() throws Throwable {
        // arrange
        ClientTypeEnum clientType = ClientTypeEnum.UNKNOWN;
        // act
        PlatformEnum result = ClientTypeUtils.genPlatform(clientType);
        // assert
        assertNotNull(result);
        assertEquals(PlatformEnum.DP_APP, result);
    }

    /**
     * 测试 genPlatform 方法，当 ClientTypeEnum 在 mtClientEnums 集合中时
     */
    @Test
    public void testGenPlatformMtClientEnums() throws Throwable {
        // arrange
        ClientTypeEnum clientTypeEnum = ClientTypeEnum.MT_APP;
        // act
        PlatformEnum result = ClientTypeUtils.genPlatform(clientTypeEnum);
        // assert
        assertEquals(PlatformEnum.MT_APP, result);
    }

    /**
     * 测试 genPlatform 方法，当 ClientTypeEnum 在 dpClientEnums 集合中时
     */
    @Test
    public void testGenPlatformDpClientEnums() throws Throwable {
        // arrange
        ClientTypeEnum clientTypeEnum = ClientTypeEnum.DP_APP;
        // act
        PlatformEnum result = ClientTypeUtils.genPlatform(clientTypeEnum);
        // assert
        assertEquals(PlatformEnum.DP_APP, result);
    }

    /**
     * 测试 genPlatform 方法，当 ClientTypeEnum 不在 mtClientEnums 和 dpClientEnums 集合中，且 isMtClientType 返回 true 时
     */
    @Test
    public void testGenPlatformNotInMtClientEnumsAndDpClientEnumsAndIsMtClientTypeTrue() throws Throwable {
        // arrange
        ClientTypeEnum clientTypeEnum = ClientTypeEnum.MT_WX;
        // act
        PlatformEnum result = ClientTypeUtils.genPlatform(clientTypeEnum);
        // assert
        assertEquals(PlatformEnum.MT_APP, result);
    }

    /**
     * 测试 genPlatform 方法，当 ClientTypeEnum 不在 mtClientEnums 和 dpClientEnums 集合中，且 isMtClientType 返回 false 时
     */
    @Test
    public void testGenPlatformNotInMtClientEnumsAndDpClientEnumsAndIsMtClientTypeFalse() throws Throwable {
        // arrange
        ClientTypeEnum clientTypeEnum = ClientTypeEnum.DP_WX;
        // act
        PlatformEnum result = ClientTypeUtils.genPlatform(clientTypeEnum);
        // assert
        assertEquals(PlatformEnum.DP_APP, result);
    }
}
