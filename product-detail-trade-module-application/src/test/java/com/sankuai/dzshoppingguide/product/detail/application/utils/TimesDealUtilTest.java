package com.sankuai.dzshoppingguide.product.detail.application.utils;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dzshoppingguide.product.detail.application.constants.DealAttrKeys;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.deal.DealBasicDTO;
import com.sankuai.general.product.query.center.client.dto.deal.DealGroupDealDTO;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class TimesDealUtilTest {

    @Mock
    private ProductBaseInfo mockProductBaseInfo;

    @Mock
    private DealGroupDealDTO mockDealGroupDealDTO;

    private static final String VALID_VERIFICATION_VALUE = "单次到店仅可核销一次，仅能一人使用";

    /**
     * Helper method to create a DealGroupDealDTO with specified status
     */
    private DealGroupDealDTO createDealDTO(int status) {
        DealGroupDealDTO dealDTO = new DealGroupDealDTO();
        DealBasicDTO basicDTO = new DealBasicDTO();
        basicDTO.setStatus(status);
        dealDTO.setBasic(basicDTO);
        return dealDTO;
    }

    /**
     * Helper method to create a DealGroupDealDTO with null status
     */
    private DealGroupDealDTO createDealWithNullStatus() {
        DealGroupDealDTO dealDTO = new DealGroupDealDTO();
        DealBasicDTO basicDTO = new DealBasicDTO();
        dealDTO.setBasic(basicDTO);
        return dealDTO;
    }

    /**
     * Test parseTimes method with empty deals list
     */
    @Test
    public void testParseTimesWithEmptyList() throws Throwable {
        // arrange
        List<DealGroupDealDTO> emptyList = Collections.emptyList();
        // act
        String result = TimesDealUtil.parseTimes(emptyList);
        // assert
        assertNull(result);
    }

    /**
     * Test parseTimes method with only invalid deals
     */
    @Test
    public void testParseTimesWithOnlyInvalidDeals() throws Throwable {
        // arrange
        List<DealGroupDealDTO> deals = new ArrayList<>();
        DealGroupDealDTO invalidDeal = new DealGroupDealDTO();
        DealBasicDTO basicDTO = new DealBasicDTO();
        // Invalid status
        basicDTO.setStatus(0);
        invalidDeal.setBasic(basicDTO);
        deals.add(invalidDeal);
        // act
        String result = TimesDealUtil.parseTimes(deals);
        // assert
        assertNull(result);
    }

    /**
     * Test parseTimes method with first deal being null
     */
    @Test
    public void testParseTimesWithFirstDealNull() throws Throwable {
        // arrange
        List<DealGroupDealDTO> deals = new ArrayList<>();
        deals.add(null);
        // act
        String result = TimesDealUtil.parseTimes(deals);
        // assert
        assertNull(result);
    }

    /**
     * Test parseTimes method with first deal having null attributes
     */
    @Test
    public void testParseTimesWithFirstDealHavingNullAttributes() throws Throwable {
        // arrange
        List<DealGroupDealDTO> deals = new ArrayList<>();
        DealGroupDealDTO deal = new DealGroupDealDTO();
        DealBasicDTO basicDTO = new DealBasicDTO();
        // Valid status
        basicDTO.setStatus(1);
        deal.setBasic(basicDTO);
        deal.setAttrs(null);
        deals.add(deal);
        // act
        String result = TimesDealUtil.parseTimes(deals);
        // assert
        assertNull(result);
    }

    /**
     * Test parseTimes method with first deal having empty attributes
     */
    @Test
    public void testParseTimesWithFirstDealHavingEmptyAttributes() throws Throwable {
        // arrange
        List<DealGroupDealDTO> deals = new ArrayList<>();
        DealGroupDealDTO deal = new DealGroupDealDTO();
        DealBasicDTO basicDTO = new DealBasicDTO();
        // Valid status
        basicDTO.setStatus(1);
        deal.setBasic(basicDTO);
        deal.setAttrs(Collections.emptyList());
        deals.add(deal);
        // act
        String result = TimesDealUtil.parseTimes(deals);
        // assert
        assertNull(result);
    }

    /**
     * Test parseTimes method with no matching time attribute
     */
    @Test
    public void testParseTimesWithNoMatchingTimeAttribute() throws Throwable {
        // arrange
        List<DealGroupDealDTO> deals = new ArrayList<>();
        DealGroupDealDTO deal = new DealGroupDealDTO();
        DealBasicDTO basicDTO = new DealBasicDTO();
        // Valid status
        basicDTO.setStatus(1);
        deal.setBasic(basicDTO);
        AttrDTO attr = new AttrDTO();
        attr.setName("SomeOtherAttribute");
        attr.setValue(Collections.singletonList("SomeValue"));
        deal.setAttrs(Collections.singletonList(attr));
        deals.add(deal);
        // act
        String result = TimesDealUtil.parseTimes(deals);
        // assert
        assertEquals("", result);
    }

    /**
     * Test parseTimes method with matching time attribute
     */
    @Test
    public void testParseTimesWithMatchingTimeAttribute() throws Throwable {
        // arrange
        List<DealGroupDealDTO> deals = new ArrayList<>();
        DealGroupDealDTO deal = new DealGroupDealDTO();
        DealBasicDTO basicDTO = new DealBasicDTO();
        // Valid status
        basicDTO.setStatus(1);
        deal.setBasic(basicDTO);
        List<AttrDTO> attrs = new ArrayList<>();
        AttrDTO attr = new AttrDTO();
        attr.setName("SYS_MULTI_SALE_NUMBER");
        attr.setValue(Collections.singletonList("3"));
        attrs.add(attr);
        deal.setAttrs(attrs);
        deals.add(deal);
        // act
        String result = TimesDealUtil.parseTimes(deals);
        // assert
        assertEquals("", result);
    }

    /**
     * Test parseTimes method with deals filter switch off
     */
    @Test
    public void testParseTimesWithDealsFilterSwitchOff() throws Throwable {
        // arrange
        List<DealGroupDealDTO> deals = new ArrayList<>();
        DealGroupDealDTO deal = new DealGroupDealDTO();
        DealBasicDTO basicDTO = new DealBasicDTO();
        // Invalid status, but should still be processed
        basicDTO.setStatus(0);
        deal.setBasic(basicDTO);
        List<AttrDTO> attrs = new ArrayList<>();
        AttrDTO attr = new AttrDTO();
        attr.setName("SYS_MULTI_SALE_NUMBER");
        attr.setValue(Collections.singletonList("3"));
        attrs.add(attr);
        deal.setAttrs(attrs);
        deals.add(deal);
        // act
        String result = TimesDealUtil.parseTimes(deals);
        // assert
        assertEquals("", result);
    }

    /**
     * Test when deals list is null
     */
    @Test
    public void testFilterInvalidDeals_NullDeals() throws Throwable {
        // arrange
        List<DealGroupDealDTO> deals = null;
        // act
        List<DealGroupDealDTO> result = TimesDealUtil.filterInvalidDeals(deals);
        // assert
        assertNull(result);
    }

    /**
     * Test when deals list is empty
     */
    @Test
    public void testFilterInvalidDeals_EmptyDeals() throws Throwable {
        // arrange
        List<DealGroupDealDTO> deals = Collections.emptyList();
        // act
        List<DealGroupDealDTO> result = TimesDealUtil.filterInvalidDeals(deals);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * Test filtering with all invalid deals
     */
    @Test
    public void testFilterInvalidDeals_AllInvalidDeals() throws Throwable {
        // arrange
        DealGroupDealDTO invalidDeal1 = createDealDTO(0);
        DealGroupDealDTO invalidDeal2 = createDealWithNullStatus();
        DealGroupDealDTO invalidDeal3 = new DealGroupDealDTO();
        List<DealGroupDealDTO> deals = Arrays.asList(invalidDeal1, invalidDeal2, invalidDeal3);
        // act
        List<DealGroupDealDTO> result = TimesDealUtil.filterInvalidDeals(deals);
        // assert
        // Even if all deals are invalid, the method should return a list (empty or with filtered deals)
        // depending on the actual dealsFilterSwitch value
        assertNotNull(result);
    }

    /**
     * Test filtering with mix of valid and invalid deals
     */
    @Test
    public void testFilterInvalidDeals_MixedDeals() throws Throwable {
        // arrange
        DealGroupDealDTO validDeal1 = createDealDTO(1);
        DealGroupDealDTO validDeal2 = createDealDTO(1);
        DealGroupDealDTO invalidDeal = createDealDTO(0);
        DealGroupDealDTO nullStatusDeal = createDealWithNullStatus();
        List<DealGroupDealDTO> deals = Arrays.asList(validDeal1, validDeal2, invalidDeal, nullStatusDeal);
        // act
        List<DealGroupDealDTO> result = TimesDealUtil.filterInvalidDeals(deals);
        // assert
        assertNotNull(result);
        // If dealsFilterSwitch is true, only valid deals should remain
        if (!result.equals(deals)) {
            assertEquals(2, result.size());
            assertTrue(result.contains(validDeal1));
            assertTrue(result.contains(validDeal2));
        }
    }

    /**
     * Test with deals having only basic info but no status
     */
    @Test
    public void testFilterInvalidDeals_OnlyBasicInfoDeals() throws Throwable {
        // arrange
        DealGroupDealDTO deal1 = new DealGroupDealDTO();
        deal1.setBasic(new DealBasicDTO());
        DealGroupDealDTO deal2 = new DealGroupDealDTO();
        deal2.setBasic(new DealBasicDTO());
        List<DealGroupDealDTO> deals = Arrays.asList(deal1, deal2);
        // act
        List<DealGroupDealDTO> result = TimesDealUtil.filterInvalidDeals(deals);
        // assert
        assertNotNull(result);
        // Deals with null status should be filtered out if dealsFilterSwitch is true
        if (!result.equals(deals)) {
            assertTrue(result.isEmpty());
        }
    }

    /**
     * Test when DealGroupDTO is null
     */
    @Test
    @DisplayName("Should return false when DealGroupDTO is null")
    public void testOnlyVerificationOne_NullDealGroup() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = null;
        // act
        boolean result = TimesDealUtil.onlyVerificationOne(dealGroupDTO);
        // assert
        assertFalse(result, "Should return false when DealGroupDTO is null");
    }

    /**
     * Test when attrs list is null
     */
    @Test
    @DisplayName("Should return false when attrs list is null")
    public void testOnlyVerificationOne_NullAttrsList() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setAttrs(null);
        // act
        boolean result = TimesDealUtil.onlyVerificationOne(dealGroupDTO);
        // assert
        assertFalse(result, "Should return false when attrs list is null");
    }

    /**
     * Test when attrs list is empty
     */
    @Test
    @DisplayName("Should return false when attrs list is empty")
    public void testOnlyVerificationOne_EmptyAttrsList() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setAttrs(Collections.emptyList());
        // act
        boolean result = TimesDealUtil.onlyVerificationOne(dealGroupDTO);
        // assert
        assertFalse(result, "Should return false when attrs list is empty");
    }

    /**
     * Test when attrs contain valid verification description
     */
    @Test
    @DisplayName("Should return true when attrs contain valid verification description")
    public void testOnlyVerificationOne_ValidAttrs_ReturnsTrue() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName(DealAttrKeys.SINGLE_VERIFICATION_QUANTITY_DESC);
        attrDTO.setValue(Collections.singletonList(VALID_VERIFICATION_VALUE));
        dealGroupDTO.setAttrs(Collections.singletonList(attrDTO));
        // act
        boolean result = TimesDealUtil.onlyVerificationOne(dealGroupDTO);
        // assert
        assertTrue(result, "Should return true when attrs contain valid verification description");
    }

    /**
     * Test when attrs contain invalid verification description
     */
    @Test
    @DisplayName("Should return false when attrs contain invalid verification description")
    public void testOnlyVerificationOne_InvalidAttrs_ReturnsFalse() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName(DealAttrKeys.SINGLE_VERIFICATION_QUANTITY_DESC);
        attrDTO.setValue(Collections.singletonList("其他值"));
        dealGroupDTO.setAttrs(Collections.singletonList(attrDTO));
        // act
        boolean result = TimesDealUtil.onlyVerificationOne(dealGroupDTO);
        // assert
        assertFalse(result, "Should return false when attrs contain invalid verification description");
    }

    /**
     * Test when attrs contain multiple attributes including valid verification description
     */
    @Test
    @DisplayName("Should return true when attrs contain multiple attributes including valid verification")
    public void testOnlyVerificationOne_MultipleAttrs() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        List<AttrDTO> attrs = new ArrayList<>();
        AttrDTO attr1 = new AttrDTO();
        attr1.setName("OTHER_ATTR");
        attr1.setValue(Collections.singletonList("其他值"));
        AttrDTO attr2 = new AttrDTO();
        attr2.setName(DealAttrKeys.SINGLE_VERIFICATION_QUANTITY_DESC);
        attr2.setValue(Collections.singletonList(VALID_VERIFICATION_VALUE));
        attrs.add(attr1);
        attrs.add(attr2);
        dealGroupDTO.setAttrs(attrs);
        // act
        boolean result = TimesDealUtil.onlyVerificationOne(dealGroupDTO);
        // assert
        assertTrue(result, "Should return true when attrs contain valid verification among multiple attributes");
    }

    /**
     * Test when attr has empty value list
     */
    @Test
    @DisplayName("Should return false when attr has empty value list")
    public void testOnlyVerificationOne_EmptyValueList() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName(DealAttrKeys.SINGLE_VERIFICATION_QUANTITY_DESC);
        attrDTO.setValue(Collections.emptyList());
        dealGroupDTO.setAttrs(Collections.singletonList(attrDTO));
        // act
        boolean result = TimesDealUtil.onlyVerificationOne(dealGroupDTO);
        // assert
        assertFalse(result, "Should return false when attr has empty value list");
    }

    /**
     * Test when attr value is null
     */
    @Test
    @DisplayName("Should return false when attr value is null")
    public void testOnlyVerificationOne_NullValue() throws Throwable {
        // arrange
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName(DealAttrKeys.SINGLE_VERIFICATION_QUANTITY_DESC);
        attrDTO.setValue(null);
        dealGroupDTO.setAttrs(Collections.singletonList(attrDTO));
        // act
        boolean result = TimesDealUtil.onlyVerificationOne(dealGroupDTO);
        // assert
        assertFalse(result, "Should return false when attr value is null");
    }
}
