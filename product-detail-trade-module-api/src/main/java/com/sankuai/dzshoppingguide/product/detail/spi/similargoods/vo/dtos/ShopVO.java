package com.sankuai.dzshoppingguide.product.detail.spi.similargoods.vo.dtos;

import com.dianping.mobile.framework.annotation.MobileDo;

import java.io.Serializable;

/**
 * https://mobile.sankuai.com/studio/model/info/27691
 */
@MobileDo(id = 0x759d)
public class ShopVO implements Serializable {
    /**
    * shopId对应的加密字段
    */
    @MobileDo.MobileField(key = 0x8143)
    private String shopIdEncrypt;

    /**
    * 商品id
    */
    @MobileDo.MobileField(key = 0x349b)
    private long shopId;

    /**
    * 详情页跳转链接
    */
    @MobileDo.MobileField(key = 0x774e)
    private String jumpUrl;

    /**
    * 评分
    */
    @MobileDo.MobileField(key = 0x44a7)
    private String scoreTag;

    /**
    * 商圈名称
    */
    @MobileDo.MobileField(key = 0xf2c7)
    private String regionName;

    /**
    * 商户得分
    */
    @MobileDo.MobileField(key = 0x3811)
    private double score;

    /**
    * 分店名称
    */
    @MobileDo.MobileField(key = 0x23e9)
    private String branchName;

    /**
    * 商户名称
    */
    @MobileDo.MobileField(key = 0xb5c9)
    private String shopName;

    /**
    * 商户与用户的距离
    */
    @MobileDo.MobileField(key = 0x9ac4)
    private String distance;

    public String getShopIdEncrypt() {
        return shopIdEncrypt;
    }

    public void setShopIdEncrypt(String shopIdEncrypt) {
        this.shopIdEncrypt = shopIdEncrypt;
    }

    public long getShopId() {
        return shopId;
    }

    public void setShopId(long shopId) {
        this.shopId = shopId;
    }

    public String getJumpUrl() {
        return jumpUrl;
    }

    public void setJumpUrl(String jumpUrl) {
        this.jumpUrl = jumpUrl;
    }

    public String getScoreTag() {
        return scoreTag;
    }

    public void setScoreTag(String scoreTag) {
        this.scoreTag = scoreTag;
    }

    public String getRegionName() {
        return regionName;
    }

    public void setRegionName(String regionName) {
        this.regionName = regionName;
    }

    public double getScore() {
        return score;
    }

    public void setScore(double score) {
        this.score = score;
    }

    public String getBranchName() {
        return branchName;
    }

    public void setBranchName(String branchName) {
        this.branchName = branchName;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public String getDistance() {
        return distance;
    }

    public void setDistance(String distance) {
        this.distance = distance;
    }
}