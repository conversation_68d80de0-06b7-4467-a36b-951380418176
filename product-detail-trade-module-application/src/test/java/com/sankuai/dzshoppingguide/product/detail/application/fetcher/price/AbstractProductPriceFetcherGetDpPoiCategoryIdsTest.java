package com.sankuai.dzshoppingguide.product.detail.application.fetcher.price;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sankuai.dealuser.price.display.api.model.ClientEnv;
import com.sankuai.dealuser.price.display.api.model.ProductIdentity;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.dto.ProductPriceReturnValue;
import com.sankuai.sinai.data.api.dto.DpPoiBackCategoryDTO;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class AbstractProductPriceFetcherGetDpPoiCategoryIdsTest {

    @InjectMocks
    private AbstractProductPriceFetcher<ProductPriceReturnValue> fetcher = new AbstractProductPriceFetcher<ProductPriceReturnValue>() {

        @Override
        protected void postProcessPriceRequest(ClientEnv clientEnv, List<ProductIdentity> productIdentities, java.util.Map<String, String> extension) {
            // Implementation not needed for testing getDpPoiCategoryIds
        }

        @Override
        protected CompletableFuture<ProductPriceReturnValue> doFetch() {
            return null;
        }
    };

    private DpPoiBackCategoryDTO createBackCategory(int categoryId) {
        DpPoiBackCategoryDTO category = mock(DpPoiBackCategoryDTO.class);
        when(category.getCategoryId()).thenReturn(categoryId);
        return category;
    }

    /**
     * Test case: when input DpPoiDTO is null
     * Expected: should return empty set
     */
    @Test
    public void testGetDpPoiCategoryIdsWithNullInput() throws Throwable {
        // arrange
        DpPoiDTO dpPoiDTO = null;
        // act
        Set<Integer> result = fetcher.getDpPoiCategoryIds(dpPoiDTO);
        // assert
        assertNotNull(result, "Result should not be null");
        assertTrue(result.isEmpty(), "Result should be empty");
    }

    /**
     * Test case: when DpPoiDTO has null BackMainCategoryPath
     * Expected: should return empty set
     */
    @Test
    public void testGetDpPoiCategoryIdsWithNullBackMainCategoryPath() throws Throwable {
        // arrange
        DpPoiDTO dpPoiDTO = mock(DpPoiDTO.class);
        when(dpPoiDTO.getBackMainCategoryPath()).thenReturn(null);
        // act
        Set<Integer> result = fetcher.getDpPoiCategoryIds(dpPoiDTO);
        // assert
        assertNotNull(result, "Result should not be null");
        assertTrue(result.isEmpty(), "Result should be empty");
    }

    /**
     * Test case: when DpPoiDTO has empty BackMainCategoryPath
     * Expected: should return empty set
     */
    @Test
    public void testGetDpPoiCategoryIdsWithEmptyBackMainCategoryPath() throws Throwable {
        // arrange
        DpPoiDTO dpPoiDTO = mock(DpPoiDTO.class);
        when(dpPoiDTO.getBackMainCategoryPath()).thenReturn(Lists.newArrayList());
        // act
        Set<Integer> result = fetcher.getDpPoiCategoryIds(dpPoiDTO);
        // assert
        assertNotNull(result, "Result should not be null");
        assertTrue(result.isEmpty(), "Result should be empty");
    }

    /**
     * Test case: when DpPoiDTO has valid BackMainCategoryPath with multiple categories
     * Expected: should return set containing all category IDs
     */
    @Test
    public void testGetDpPoiCategoryIdsWithValidBackMainCategoryPath() throws Throwable {
        // arrange
        DpPoiDTO dpPoiDTO = mock(DpPoiDTO.class);
        List<DpPoiBackCategoryDTO> categoryPath = Lists.newArrayList(createBackCategory(1), createBackCategory(2), createBackCategory(3));
        when(dpPoiDTO.getBackMainCategoryPath()).thenReturn(categoryPath);
        // act
        Set<Integer> result = fetcher.getDpPoiCategoryIds(dpPoiDTO);
        // assert
        assertEquals(Sets.newHashSet(1, 2, 3), result, "Result should contain all category IDs");
    }
}
