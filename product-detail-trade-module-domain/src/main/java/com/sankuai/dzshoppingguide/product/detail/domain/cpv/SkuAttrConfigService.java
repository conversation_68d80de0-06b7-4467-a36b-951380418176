package com.sankuai.dzshoppingguide.product.detail.domain.cpv;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.sankuai.dzshoppingguide.product.detail.domain.exception.ProductDetailFatalError;
import com.sankuai.dz.product.detail.gateway.spi.enums.ProductTypeEnum;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @Author: guangyujie
 * @Date: 2025/2/11 19:40
 */
@Service
public class SkuAttrConfigService implements InitializingBean {

    private final String SKU_ATTR_CONFIG = "com.sankuai.dzshoppingguide.detail.trademodule.sku.attr.config";    // todo 待创建lion

    private Map<String, Set<String>> attrConfigMap = new ConcurrentHashMap<>();

    private void parse(String json) {
        Map<String, Set<String>> map = JSONObject.parseObject(json, new TypeReference<Map<String, Set<String>>>() {
        });
        if (map == null) {
            throw new ProductDetailFatalError("FATAL ERROR!!!查询中心attr配置为空!!!");
        }
        this.attrConfigMap = map;
    }

    private String buildLionKey(ProductTypeEnum productType, int secondProductCategory) {
        return String.format("%s-%s", productType.getCode(), secondProductCategory);
    }

    public Set<String> getSkuAttrConfig(ProductTypeEnum productType, int secondProductCategory) {
        return attrConfigMap.getOrDefault(buildLionKey(productType, secondProductCategory), new HashSet<>());
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        parse(Lion.getString(Environment.getAppName(), SKU_ATTR_CONFIG));
        Lion.addConfigListener(SKU_ATTR_CONFIG, configEvent -> parse(configEvent.getValue()));
    }

}