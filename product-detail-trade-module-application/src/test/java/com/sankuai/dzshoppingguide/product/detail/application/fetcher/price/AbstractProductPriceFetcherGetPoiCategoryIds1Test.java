package com.sankuai.dzshoppingguide.product.detail.application.fetcher.price;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dealuser.price.display.api.model.ClientEnv;
import com.sankuai.dealuser.price.display.api.model.ProductIdentity;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.best.shop.ShopInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.dto.ProductPriceReturnValue;
import com.sankuai.sinai.data.api.dto.DpPoiBackCategoryDTO;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.sinai.data.api.dto.MtPoiDTO;
import groovyjarjarantlr.collections.List;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import org.apache.calcite.adapter.java.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class AbstractProductPriceFetcherGetPoiCategoryIds1Test {

    private TestableAbstractProductPriceFetcher abstractProductPriceFetcher;

    @Mock
    private ShopInfo shopInfo;

    @Mock
    private MtPoiDTO mtPoiDTO;

    @Mock
    private DpPoiDTO dpPoiDTO;

    // Create a testable concrete class that implements the abstract class
    private static class TestableAbstractProductPriceFetcher extends AbstractProductPriceFetcher<ProductPriceReturnValue> {

        @Override
        protected void postProcessPriceRequest(ClientEnv clientEnv, java.util.List<ProductIdentity> productIdentities, java.util.Map<String, String> extension) {
            // Implementation not needed for tests
        }

        @Override
        protected CompletableFuture<ProductPriceReturnValue> doFetch() {
            // Implementation not needed for tests
            return null;
        }

        // Make the protected methods public for testing
        @Override
        public Set<Integer> getMtPoiCategoryIds(MtPoiDTO mtPoiDTO) {
            return super.getMtPoiCategoryIds(mtPoiDTO);
        }

        @Override
        public Set<Integer> getDpPoiCategoryIds(DpPoiDTO dpPoiDTO) {
            return super.getDpPoiCategoryIds(dpPoiDTO);
        }
    }

    @BeforeEach
    void setUp() {
        abstractProductPriceFetcher = new TestableAbstractProductPriceFetcher();
    }

    /**
     * Test case for MT client type with null MtPoiDTO
     */
    @Test
    public void testGetPoiCategoryIdsWithMtClientTypeAndNullMtPoiDTO() throws Throwable {
        // arrange
        when(shopInfo.getMtPoiDTO()).thenReturn(null);
        // act
        Set<Integer> result = abstractProductPriceFetcher.getPoiCategoryIds(ClientTypeEnum.MT_APP.fromCode(200), shopInfo);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test case for MT client type with empty category list
     */
    @Test
    public void testGetPoiCategoryIdsWithMtClientTypeAndEmptyCategoryList() throws Throwable {
        // arrange
        when(shopInfo.getMtPoiDTO()).thenReturn(mtPoiDTO);
        when(mtPoiDTO.getDpBackCategoryList()).thenReturn(Collections.emptyList());
        // act
        Set<Integer> result = abstractProductPriceFetcher.getPoiCategoryIds(ClientTypeEnum.MT_APP.fromCode(200), shopInfo);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test case for MT client type with valid category list
     */
    @Test
    public void testGetPoiCategoryIdsWithMtClientTypeAndValidCategories() throws Throwable {
        // arrange
        when(shopInfo.getMtPoiDTO()).thenReturn(mtPoiDTO);
        DpPoiBackCategoryDTO category1 = new DpPoiBackCategoryDTO();
        category1.setCategoryId(1);
        DpPoiBackCategoryDTO category2 = new DpPoiBackCategoryDTO();
        category2.setCategoryId(2);
        when(mtPoiDTO.getDpBackCategoryList()).thenReturn(Arrays.asList(category1, category2));
        // act
        Set<Integer> result = abstractProductPriceFetcher.getPoiCategoryIds(ClientTypeEnum.MT_APP.fromCode(200), shopInfo);
        // assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.contains(1));
        assertTrue(result.contains(2));
    }

    /**
     * Test case for DP client type with null DpPoiDTO
     */
    @Test
    public void testGetPoiCategoryIdsWithDpClientTypeAndNullDpPoiDTO() throws Throwable {
        // arrange
        when(shopInfo.getDpPoiDTO()).thenReturn(null);
        // act
        Set<Integer> result = abstractProductPriceFetcher.getPoiCategoryIds(ClientTypeEnum.DP_APP.fromCode(100), shopInfo);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test case for DP client type with empty category list
     */
    @Test
    public void testGetPoiCategoryIdsWithDpClientTypeAndEmptyCategoryList() throws Throwable {
        // arrange
        when(shopInfo.getDpPoiDTO()).thenReturn(dpPoiDTO);
        when(dpPoiDTO.getBackMainCategoryPath()).thenReturn(Collections.emptyList());
        // act
        Set<Integer> result = abstractProductPriceFetcher.getPoiCategoryIds(ClientTypeEnum.DP_APP.fromCode(100), shopInfo);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test case for DP client type with valid category list
     */
    @Test
    public void testGetPoiCategoryIdsWithDpClientTypeAndValidCategories() throws Throwable {
        // arrange
        when(shopInfo.getDpPoiDTO()).thenReturn(dpPoiDTO);
        DpPoiBackCategoryDTO category1 = new DpPoiBackCategoryDTO();
        category1.setCategoryId(1);
        DpPoiBackCategoryDTO category2 = new DpPoiBackCategoryDTO();
        category2.setCategoryId(2);
        when(dpPoiDTO.getBackMainCategoryPath()).thenReturn(Arrays.asList(category1, category2));
        // act
        Set<Integer> result = abstractProductPriceFetcher.getPoiCategoryIds(ClientTypeEnum.DP_APP.fromCode(100), shopInfo);
        // assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.contains(1));
        assertTrue(result.contains(2));
    }
}
