package com.sankuai.dzshoppingguide.product.detail.application.fetcher.membercard.card.premium;

import com.google.common.collect.Lists;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dz.product.detail.gateway.spi.dto.ShepherdGatewayParam;
import com.sankuai.dz.product.detail.gateway.spi.enums.ProductTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.dealgroup.DealGroupIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.dealgroup.DealGroupIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.membercard.card.entrance.MemberEntranceResult;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.membercard.card.entrance.ShopMemberEntranceFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
import com.sankuai.dzshoppingguide.product.detail.application.service.dto.ShopMemberDetailV;
import com.sankuai.dzshoppingguide.product.detail.application.utils.ClientTypeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

/**
 * 商家会员权益信息
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/2/27 15:53
 */
@Fetcher(previousLayerDependencies = {DealGroupIdMapperFetcher.class, ShopMemberEntranceFetcher.class})
@Slf4j
public class MemberCardInterestFetcher extends NormalFetcherContext<PremiumMemberCardResult> {
    @Autowired
    private CompositeAtomService compositeAtomService;

    @Override
    protected CompletableFuture<PremiumMemberCardResult> doFetch() {
        if (request.getProductTypeEnum() != ProductTypeEnum.DEAL) {
            return CompletableFuture.completedFuture(null);
        }

        MemberEntranceResult memberEntranceResult = getDependencyResult(ShopMemberEntranceFetcher.class);

        // 没有商家会员的入口,直接返回
        if (memberEntranceResult == null || !memberEntranceResult.isHasEntrance()) {
            return CompletableFuture.completedFuture(null);
        }

        DealGroupIdMapper dealGroupIdResult = getDependencyResult(DealGroupIdMapperFetcher.class);
        Long dpDealGroupId = Optional.ofNullable(dealGroupIdResult).map(DealGroupIdMapper::getDpDealGroupId).orElse(0L);
        ShepherdGatewayParam shepherdParam = request.getShepherdGatewayParam();
        long userId = request.getClientTypeEnum().isMtClientType() ? request.getMtUserId() : shepherdParam.getMtVirtualUserId();
        return compositeAtomService.getMemberDiscountInfoList(userId, ClientTypeUtils.genPlatform(request.getClientTypeEnum()), Lists.newArrayList(dpDealGroupId), true).thenApply(res -> {
            if (res == null || MapUtils.isEmpty(res.getDpDealId_shopMemberDetail_map())) {
                return null;
            }
            Map<Long, ShopMemberDetailV> resultMap = res.getDpDealId_shopMemberDetail_map();
            ShopMemberDetailV shopMemberDetailV = resultMap.get(dpDealGroupId);
            if (shopMemberDetailV == null || shopMemberDetailV.getOriginalResult() == null) {
                return null;
            }
            return new PremiumMemberCardResult(shopMemberDetailV);
        });
    }
}
