package com.sankuai.dzshoppingguide.product.detail.spi.instructions.dealstruct;

import  com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dz.product.detail.gateway.spi.vo.AbstractModuleVO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2024/1/11
 */
@Data
@TypeDoc(description = "结构化购买须知")
@MobileDo(id = 0xac61)
public class PnPurchaseNoteDTO extends AbstractModuleVO implements Serializable {
    @FieldDoc(description = "标题")
    @MobileDo.MobileField(key = 0xd7ba)
    private String pnTitle;

    @FieldDoc(description = "展示模块")
    @MobileDo.MobileField(key = 0xbaba)
    private List<PurchaseNoteModuleDTO> pnModules;

    @FieldDoc(description = "概述")
    @MobileDo.MobileField(key = 0xd894)
    private String subTitle;

    @Override
    public String getModuleKey() {
        return ModuleKeyConstants.REMINDER_INFO_INSTRUCTIONS;
    }
}
