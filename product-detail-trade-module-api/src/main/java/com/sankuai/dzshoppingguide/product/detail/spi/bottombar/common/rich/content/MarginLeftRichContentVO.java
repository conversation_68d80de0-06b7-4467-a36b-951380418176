package com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.rich.content;

import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.rich.content.enums.RichContentTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author: g<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025/4/4 19:30
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MarginLeftRichContentVO extends RichContentVO {

    private int marginLeft;

    public MarginLeftRichContentVO(int marginLeft) {
        this.marginLeft = marginLeft;
    }

    @Override
    public int getType() {
        return RichContentTypeEnum.MARGIN_LEFT.getCode();
    }

}
