package com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.extend.redirect;

import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.BottomBarActionDataVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.enums.BottomBarActionTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.enums.OpenTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * @Author: guangyujie
 * @Date: 2025/3/16 15:39
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class SimpleRedirectActionVO extends BottomBarActionDataVO {

    private final int actionType = BottomBarActionTypeEnum.REDIRECT.getCode();

    /**
     * 打开类型
     * redirect=直接跳转
     * modal=打开浮层
     *
     * @see OpenTypeEnum
     */
    private String openType;

    /**
     * 跳转链接
     */
    private String url;

    public SimpleRedirectActionVO(OpenTypeEnum openType, String url) {
        this.openType = openType.name();
        this.url = url;
    }

}
