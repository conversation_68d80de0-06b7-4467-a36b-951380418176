package com.sankuai.dzshoppingguide.product.detail.application.utils;

import static org.junit.Assert.*;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.RequestSourceEnum;
import org.junit.Test;

public class DealCtxHelperIsOdpSourceTest {

    /**
     * 测试 isOdpSource 方法，当 requestSource 等于 ODP 的 source 时，应返回 true
     */
    @Test
    public void testIsOdpSourceEqual() {
        // arrange
        String requestSource = RequestSourceEnum.ODP.getSource();
        // act
        boolean result = DealCtxHelper.isOdpSource(requestSource);
        // assert
        assertTrue(result);
    }

    /**
     * 测试 isOdpSource 方法，当 requestSource 不等于 ODP 的 source 时，应返回 false
     */
    @Test
    public void testIsOdpSourceNotEqual() {
        // arrange
        String requestSource = "not_odp";
        // act
        boolean result = DealCtxHelper.isOdpSource(requestSource);
        // assert
        assertFalse(result);
    }

    /**
     * 测试 isOdpSource 方法，当 requestSource 为 null 时，应返回 false
     */
    @Test
    public void testIsOdpSourceNull() {
        // arrange
        String requestSource = null;
        // act
        boolean result = DealCtxHelper.isOdpSource(requestSource);
        // assert
        assertFalse(result);
    }
}
