package com.sankuai.dzshoppingguide.product.detail.spi.timecard.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import lombok.Data;

import java.io.Serializable;

@Data
public class TimeCardItemVO implements Serializable {
    /**
     * 当前选中
     */
    @MobileDo.MobileField
    private boolean isSelect;

    /**
     * 跳链
     */
    @MobileField
    private String jumpUrl;

    /**
     * 商品ID
     */
    @MobileField
    private long productId;

    /**
     * 单位标识，如¥
     */
    @MobileField
    private String symbol;

    /**
     * 单次价格，如170.2
     */
    @MobileField
    private String perPrice;

    /**
     * 单位，如/次
     */
    @MobileField
    private String unit;

    /**
     * 标签，如性价比高
     */
    @MobileField
    private String priceTag;

    /**
     * 价格描述文案，如到手
     */
    @MobileField
    private String text;

    /**
     * 标题，如3次卡
     */
    @MobileField
    private String title;

    /**
     * 到手价
     */
    @MobileField
    private String finalPrice;

}
