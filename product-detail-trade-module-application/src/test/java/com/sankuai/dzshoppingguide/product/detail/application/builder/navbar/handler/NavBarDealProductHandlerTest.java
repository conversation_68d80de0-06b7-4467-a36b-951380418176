package com.sankuai.dzshoppingguide.product.detail.application.builder.navbar.handler;

import static com.sankuai.dz.product.detail.RequestCustomParamEnum.uuid;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.product.detail.gateway.spi.dto.ShepherdGatewayParam;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.enums.MobileOSTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.best.shop.ShopInfo;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class NavBarDealProductHandlerTest {

    private NavBarDealProductHandler handler = new NavBarDealProductHandler();

    @Mock
    private ProductDetailPageRequest request;

    @Mock
    private ShopIdMapper idMapper;

    @Mock
    private ShopInfo shopInfo;

    @Mock
    private ShepherdGatewayParam shepherdGatewayParam;

    @Before
    public void setUp() {
        // Common setup for all tests
        when(request.getShepherdGatewayParam()).thenReturn(shepherdGatewayParam);
        when(request.getPageSource()).thenReturn(null);
        when(request.getProductId()).thenReturn(0L);
    }

    private void mockRequestForClientType(ClientTypeEnum clientTypeEnum) {
        when(request.getClientTypeEnum()).thenReturn(clientTypeEnum);
    }

    private void mockRequestForMobileOSType(MobileOSTypeEnum mobileOSTypeEnum) {
        when(request.getMobileOSType()).thenReturn(mobileOSTypeEnum);
    }

    private void mockRequestForAppVersion(String appVersion) {
        when(shepherdGatewayParam.getAppVersion()).thenReturn(appVersion);
    }

    /**
     * Test case for when MobileOSType is UNKNOWN
     * Expected behavior: Should return null when client type is not MT_APP/MT_WX/DP_APP/DP_WX
     */
    @Test
    public void testBuildShareUrlMobileOSTypeIsOther() throws Throwable {
        // Use UNKNOWN client type to trigger default case
        mockRequestForClientType(ClientTypeEnum.UNKNOWN);
        mockRequestForMobileOSType(MobileOSTypeEnum.UNKNOWN);
        String result = handler.buildShareUrl(request, idMapper, shopInfo);
        assertNull("Expected null result for UNKNOWN client type", result);
    }

    /**
     * Test case for MT_APP client type with IOS mobile OS
     */
    @Test
    public void testBuildShareUrlClientTypeEnumIsMT_APPAndMobileOSTypeIsIOS() throws Throwable {
        mockRequestForClientType(ClientTypeEnum.MT_APP);
        mockRequestForMobileOSType(MobileOSTypeEnum.IOS);
        mockRequestForAppVersion("1.0");
        when(request.getCustomParam(uuid)).thenReturn(null);
        String result = handler.buildShareUrl(request, idMapper, shopInfo);
        assertNotNull(result);
        assertTrue(result.contains("ios"));
        assertTrue(result.contains("iosweb"));
    }

    /**
     * Test case for MT_APP client type with Android mobile OS
     */
    @Test
    public void testBuildShareUrlClientTypeEnumIsMT_APPAndMobileOSTypeIsAndroid() throws Throwable {
        mockRequestForClientType(ClientTypeEnum.MT_APP);
        mockRequestForMobileOSType(MobileOSTypeEnum.ANDROID);
        mockRequestForAppVersion("2.0");
        when(request.getCustomParam(uuid)).thenReturn(null);
        String result = handler.buildShareUrl(request, idMapper, shopInfo);
        assertNotNull(result);
        assertTrue(result.contains("android"));
        assertTrue(result.contains("androidweb"));
    }

    /**
     * Test case for DP_APP client type with IOS mobile OS
     */
    @Test
    public void testBuildShareUrlClientTypeEnumIsDP_APPAndMobileOSTypeIsIOS() throws Throwable {
        mockRequestForClientType(ClientTypeEnum.DP_APP);
        mockRequestForMobileOSType(MobileOSTypeEnum.IOS);
        when(shopInfo.getDpPoiDTO()).thenReturn(null);
        String result = handler.buildShareUrl(request, idMapper, shopInfo);
        assertNotNull(result);
        assertTrue(result.contains("m.dianping.com"));
    }

    /**
     * Test case for DP_APP client type with Android mobile OS
     */
    @Test
    public void testBuildShareUrlClientTypeEnumIsDP_APPAndMobileOSTypeIsANDROID() throws Throwable {
        mockRequestForClientType(ClientTypeEnum.DP_APP);
        mockRequestForMobileOSType(MobileOSTypeEnum.ANDROID);
        when(shopInfo.getDpPoiDTO()).thenReturn(null);
        String result = handler.buildShareUrl(request, idMapper, shopInfo);
        assertNotNull(result);
        assertTrue(result.contains("m.dianping.com"));
    }

    /**
     * Test case for DP_WX client type with IOS mobile OS
     */
    @Test
    public void testBuildShareUrlClientTypeEnumIsDP_WXAndMobileOSTypeIsIOS() throws Throwable {
        mockRequestForClientType(ClientTypeEnum.DP_WX);
        mockRequestForMobileOSType(MobileOSTypeEnum.IOS);
        when(shopInfo.getDpPoiDTO()).thenReturn(null);
        String result = handler.buildShareUrl(request, idMapper, shopInfo);
        assertNotNull(result);
        assertTrue(result.contains("m.dianping.com"));
    }

    /**
     * Test case for DP_WX client type with Android mobile OS
     */
    @Test
    public void testBuildShareUrlClientTypeEnumIsDP_WXAndMobileOSTypeIsANDROID() throws Throwable {
        mockRequestForClientType(ClientTypeEnum.DP_WX);
        mockRequestForMobileOSType(MobileOSTypeEnum.ANDROID);
        when(shopInfo.getDpPoiDTO()).thenReturn(null);
        String result = handler.buildShareUrl(request, idMapper, shopInfo);
        assertNotNull(result);
        assertTrue(result.contains("m.dianping.com"));
    }

    /**
     * Test case for unknown client type
     */
    @Test
    public void testBuildShareUrlClientTypeEnumIsOther() throws Throwable {
        mockRequestForClientType(ClientTypeEnum.UNKNOWN);
        String result = handler.buildShareUrl(request, idMapper, shopInfo);
        assertNull("Expected null result for UNKNOWN client type", result);
    }
}
