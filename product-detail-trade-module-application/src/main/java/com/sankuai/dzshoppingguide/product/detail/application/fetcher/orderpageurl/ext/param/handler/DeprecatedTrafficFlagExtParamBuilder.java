package com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.ext.param.handler;

import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.enums.OrderPageExtParamEnums;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.ext.param.BaseSingleExtParamBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.ext.param.request.ExtParamBuilderRequest;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * @Author: guangyujie
 * @Date: 2025/3/29 23:28
 */
@Deprecated
@Component
public class DeprecatedTrafficFlagExtParamBuilder extends BaseSingleExtParamBuilder {

    //设置流量标识参数（该标识已废弃，仅用于LE预约场景，提单实际使用trafficflag作为流量标识传递）
    @Override
    protected OrderPageExtParamEnums buildKey() {
        return OrderPageExtParamEnums.trafficFlag;
    }

    @Override
    protected String doBuildExtParam(ProductDetailPageRequest request, ExtParamBuilderRequest builderRequest) throws Exception {
        if (StringUtils.isBlank(request.getPageSource())) {
            return null;
        }
        return request.getPageSource();
    }

}
