package com.sankuai.dzshoppingguide.product.detail.spi.atmospherebar.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.dz.product.detail.gateway.spi.vo.AbstractModuleVO;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.AtmosphereInfoDetailVO;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DetailPriceVO;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DetailSaleVO;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.pricebar.vo.ProductPriceBarModuleVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025-02-26
 * @desc
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TypeDoc(description = "商品详情页氛围条")
@MobileDo(id = 0x63f6)
public class ProductAtmosphereModuleVO extends ProductPriceBarModuleVO {

    @Override
    public String getModuleKey() {
        return ModuleKeyConstants.ATMOSPHERE_PRICE_SALE_BAR_MODULE;
    }

    @FieldDoc(description = "氛围信息")
    @MobileField(key = 0xe900)
    private AtmosphereInfoDetailVO atmosphere;
}
