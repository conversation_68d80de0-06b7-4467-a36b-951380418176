package com.sankuai.dzshoppingguide.product.detail.application.builder.navbar.handler;

import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.best.shop.ShopInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.dto.ProductPriceReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.ShareModuleMiniProgramConfig;

/**
 * @Author: <EMAIL>
 * @Date: 2025/3/5
 */
public interface NavBarProductTypeHandler {
    String buildImage(String image);

    ShareModuleMiniProgramConfig buildMiniProgramConfig(ProductDetailPageRequest request, ShopIdMapper idMapper);

    String buildShareUrl(ProductDetailPageRequest request, ShopIdMapper idMapper, ShopInfo shopInfo);

    String buildTitle(ProductDetailPageRequest request, String title, ProductPriceReturnValue costEffectivePrice);

    String buildDesc(ProductDetailPageRequest request, ProductBaseInfo baseInfo);
}
