package com.sankuai.dzshoppingguide.product.detail.application.fetcher.producttheme;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommendgoods.dtos.ProductM;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommendgoods.SimilarProductRecommendFetcher;
import com.sankuai.dztheme.deal.req.DealProductRequest;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @create 2025/5/22 09:54
 */
@Fetcher(
        previousLayerDependencies = {
                SimilarProductRecommendFetcher.class
        }
)
public class OriginProductThemeFetcher extends AbstractProductThemeFetcher {

    String mtShopThemePlanId = "10500360";
    String dpShopThemePlanId = "10500361";
    int directPromoScene = 400200;
    int shopCarNoSelectScene = 400800;

    @Override
    protected boolean enableNewService(){
        return false;
    }

    @Override
    protected DealProductRequest buildDealProductRequest(List<ProductM> products, Map<Long, Long> dealId2ShopId){
        List<Integer> dealIds = Lists.newArrayList();
        dealIds.add((int) request.getProductId());
        DealProductRequest dealProductRequest = new DealProductRequest();
        dealProductRequest.setPlanId("10002450");
        dealProductRequest.setProductIds(dealIds);
        Map<String, Object> extParams = buildQueryExtParams( dealIds, dealId2ShopId );
        extParams.put("dealId2NearestShopIdL", deal2NearestShopForLong(dealIds));
        buildPriceCompareParam(extParams, request.getClientTypeEnum().isMtClientType());
        dealProductRequest.setExtParams(extParams);
        return dealProductRequest;
    }

    private void buildPriceCompareParam( Map<String, Object> extParams, boolean isMt) {
        extParams.put("shopThemePlanId", isMt ? mtShopThemePlanId : dpShopThemePlanId);
        Map<Integer, Long> deal2shop = Maps.newHashMap();
        if(request.getClientTypeEnum().isMtClientType()) {
            deal2shop.put((int) request.getProductId(), shopIdMapper.getMtBestShopId());
        } else {
            deal2shop.put((int) request.getProductId(), shopIdMapper.getDpBestShopId());
        }
        extParams.put("dealId2ShopIdForLong", deal2shop);


        String pageSource = request.getPageSource();
        extParams.put("themeReqSource", 0);
        List<Integer> bpDealGroupTypes = Lists.newArrayList();
        bpDealGroupTypes.add(1);
        extParams.put("bpDealGroupTypes", bpDealGroupTypes);

        if(StringUtils.isNotBlank(pageSource)) {
            extParams.put("pageSource","dealGroupDetail");
            if(pageSource.equals("deal")) {
                extParams.put("directPromoSceneCode", directPromoScene);
            } else if(pageSource.equals("shopcarnoselect") || pageSource.equals("shopcarselect")) {
                extParams.put("directPromoSceneCode", shopCarNoSelectScene);
            }
        }
    }

    public Map<Integer, Integer> deal2NearestShop(List<Integer> dealIds) {
        if (CollectionUtils.isEmpty(dealIds)) {
            return Collections.emptyMap();
        }
        Map<Integer, Integer> deal2ShopMap = new HashMap<>();
        for (int i = 0; i < dealIds.size(); i++) {
            deal2ShopMap.put(dealIds.get(i), (int) (request.getClientTypeEnum().isMtClientType() ? shopIdMapper.getMtBestShopId() : shopIdMapper.getDpBestShopId()));
        }
        return deal2ShopMap;
    }

    public Map<Integer, Long> deal2NearestShopForLong(List<Integer> dealIds) {
        if (CollectionUtils.isEmpty(dealIds)) {
            return Collections.emptyMap();
        }
        Map<Integer, Long> deal2ShopMap = new HashMap<>();
        for (int i = 0; i < dealIds.size(); i++) {
            deal2ShopMap.put(dealIds.get(i),  request.getClientTypeEnum().isMtClientType() ? shopIdMapper.getMtBestShopId() : shopIdMapper.getDpBestShopId());
        }
        return deal2ShopMap;
    }
}
