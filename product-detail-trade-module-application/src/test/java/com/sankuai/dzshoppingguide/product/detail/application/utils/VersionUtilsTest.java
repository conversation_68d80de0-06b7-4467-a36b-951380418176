package com.sankuai.dzshoppingguide.product.detail.application.utils;

import static org.junit.jupiter.api.Assertions.*;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * Unit tests for VersionUtils.isLessThanOrEqual method
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("VersionUtils isLessThanOrEqual Tests")
public class VersionUtilsTest {

    /**
     * Test case: current version equals target version
     * Expected: should return true
     */
    @Test
    @DisplayName("When versions are equal should return true")
    public void testIsLessThanOrEqual_WhenVersionsEqual() throws Throwable {
        // arrange
        String currentVersion = "1.0.0";
        String targetVersion = "1.0.0";
        // act
        boolean result = VersionUtils.isLessThanOrEqual(currentVersion, targetVersion);
        // assert
        assertTrue(result, "Equal versions should return true");
    }

    /**
     * Test case: current version is less than target version
     * Expected: should return true
     */
    @Test
    @DisplayName("When current version is less than target should return true")
    public void testIsLessThanOrEqual_WhenCurrentVersionLess() throws Throwable {
        // arrange
        String currentVersion = "1.0.0";
        String targetVersion = "2.0.0";
        // act
        boolean result = VersionUtils.isLessThanOrEqual(currentVersion, targetVersion);
        // assert
        assertTrue(result, "Lower version should return true");
    }

    /**
     * Test case: current version is greater than target version
     * Expected: should return false
     */
    @Test
    @DisplayName("When current version is greater than target should return false")
    public void testIsLessThanOrEqual_WhenCurrentVersionGreater() throws Throwable {
        // arrange
        String currentVersion = "2.0.0";
        String targetVersion = "1.0.0";
        // act
        boolean result = VersionUtils.isLessThanOrEqual(currentVersion, targetVersion);
        // assert
        assertFalse(result, "Higher version should return false");
    }

    /**
     * Test case: versions have different number of segments
     * Expected: should handle comparison correctly
     */
    @Test
    @DisplayName("When versions have different lengths should compare correctly")
    public void testIsLessThanOrEqual_WhenDifferentLengths() throws Throwable {
        // arrange
        String currentVersion = "1.0";
        String targetVersion = "1.0.1";
        // act
        boolean result = VersionUtils.isLessThanOrEqual(currentVersion, targetVersion);
        // assert
        assertTrue(result, "Version with fewer segments should be handled correctly");
    }

    /**
     * Test case: versions with leading zeros
     * Expected: should handle comparison correctly
     */
    @Test
    @DisplayName("When versions contain leading zeros should compare correctly")
    public void testIsLessThanOrEqual_WithLeadingZeros() throws Throwable {
        // arrange
        String currentVersion = "1.02.0";
        String targetVersion = "1.2.0";
        // act
        boolean result = VersionUtils.isLessThanOrEqual(currentVersion, targetVersion);
        // assert
        assertTrue(result, "Versions with leading zeros should be handled correctly");
    }

    /**
     * Test case: complex version numbers with multiple segments
     * Expected: should handle comparison correctly
     */
    @Test
    @DisplayName("When comparing complex version numbers should work correctly")
    public void testIsLessThanOrEqual_ComplexVersions() throws Throwable {
        // arrange
        String currentVersion = "********";
        String targetVersion = "********";
        // act
        boolean result = VersionUtils.isLessThanOrEqual(currentVersion, targetVersion);
        // assert
        assertTrue(result, "Complex version numbers should be compared correctly");
    }

    /**
     * Test case: versions with all zeros
     * Expected: should handle comparison correctly
     */
    @Test
    @DisplayName("When comparing versions with all zeros should work correctly")
    public void testIsLessThanOrEqual_AllZeros() throws Throwable {
        // arrange
        String currentVersion = "0.0.0";
        String targetVersion = "0.0.0";
        // act
        boolean result = VersionUtils.isLessThanOrEqual(currentVersion, targetVersion);
        // assert
        assertTrue(result, "Versions with all zeros should be compared correctly");
    }

    /**
     * Test case: current version is greater than target version
     * Expected: should return true
     */
    @Test
    @DisplayName("Should return true when current version is greater")
    public void testIsGreaterThanOrEqual_CurrentVersionGreater() {
        // arrange
        String currentVersion = "2.0.0";
        String targetVersion = "1.9.9";
        // act
        boolean result = VersionUtils.isGreaterThanOrEqual(currentVersion, targetVersion);
        // assert
        assertTrue(result, "2.0.0 should be greater than 1.9.9");
    }

    /**
     * Test case: versions are exactly equal
     * Expected: should return true
     */
    @Test
    @DisplayName("Should return true when versions are equal")
    public void testIsGreaterThanOrEqual_VersionsEqual() {
        // arrange
        String currentVersion = "1.0.0";
        String targetVersion = "1.0.0";
        // act
        boolean result = VersionUtils.isGreaterThanOrEqual(currentVersion, targetVersion);
        // assert
        assertTrue(result, "Equal versions should return true");
    }

    /**
     * Test case: current version is less than target version
     * Expected: should return false
     */
    @Test
    @DisplayName("Should return false when current version is less")
    public void testIsGreaterThanOrEqual_CurrentVersionLess() {
        // arrange
        String currentVersion = "1.9.9";
        String targetVersion = "2.0.0";
        // act
        boolean result = VersionUtils.isGreaterThanOrEqual(currentVersion, targetVersion);
        // assert
        assertFalse(result, "1.9.9 should be less than 2.0.0");
    }

    /**
     * Test case: current version has more segments than target
     * Expected: should compare correctly and return true
     */
    @Test
    @DisplayName("Should handle different segment counts correctly - more segments in current")
    public void testIsGreaterThanOrEqual_CurrentMoreSegments() {
        // arrange
        String currentVersion = "2.0.0.1";
        String targetVersion = "2.0.0";
        // act
        boolean result = VersionUtils.isGreaterThanOrEqual(currentVersion, targetVersion);
        // assert
        assertTrue(result, "2.0.0.1 should be greater than 2.0.0");
    }

    /**
     * Test case: target version has more segments than current
     * Expected: should compare correctly and return true when equal
     */
    @Test
    @DisplayName("Should handle different segment counts correctly - more segments in target")
    public void testIsGreaterThanOrEqual_TargetMoreSegments() {
        // arrange
        String currentVersion = "2.0";
        String targetVersion = "2.0.0";
        // act
        boolean result = VersionUtils.isGreaterThanOrEqual(currentVersion, targetVersion);
        // assert
        assertTrue(result, "2.0 should be equal to 2.0.0");
    }

    /**
     * Test case: single digit version comparison
     * Expected: should compare correctly
     */
    @Test
    @DisplayName("Should handle single digit versions")
    public void testIsGreaterThanOrEqual_SingleDigit() {
        // arrange
        String currentVersion = "2";
        String targetVersion = "1";
        // act
        boolean result = VersionUtils.isGreaterThanOrEqual(currentVersion, targetVersion);
        // assert
        assertTrue(result, "Version 2 should be greater than 1");
    }

    /**
     * Test case: versions with large numbers
     * Expected: should compare correctly
     */
    @Test
    @DisplayName("Should handle large version numbers")
    public void testIsGreaterThanOrEqual_LargeNumbers() {
        // arrange
        String currentVersion = "999.999.999";
        String targetVersion = "999.999.998";
        // act
        boolean result = VersionUtils.isGreaterThanOrEqual(currentVersion, targetVersion);
        // assert
        assertTrue(result, "999.999.999 should be greater than 999.999.998");
    }

    /**
     * Test case: versions with leading zeros
     * Expected: should compare correctly ignoring leading zeros
     */
    @Test
    @DisplayName("Should handle versions with leading zeros")
    public void testIsGreaterThanOrEqual_LeadingZeros() {
        // arrange
        String currentVersion = "02.00.00";
        String targetVersion = "2.0.0";
        // act
        boolean result = VersionUtils.isGreaterThanOrEqual(currentVersion, targetVersion);
        // assert
        assertTrue(result, "02.00.00 should be equal to 2.0.0");
    }

    /**
     * Test when current version is less than target version
     */
    @Test
    public void testIsLessThanWhenCurrentVersionIsLess() throws Throwable {
        // arrange
        String currentVersion = "1.2.3";
        String targetVersion = "1.2.4";
        // act
        boolean result = VersionUtils.isLessThan(currentVersion, targetVersion);
        // assert
        assertTrue(result, "Expected 1.2.3 to be less than 1.2.4");
    }

    /**
     * Test when current version is equal to target version
     */
    @Test
    public void testIsLessThanWhenVersionsAreEqual() throws Throwable {
        // arrange
        String currentVersion = "1.2.3";
        String targetVersion = "1.2.3";
        // act
        boolean result = VersionUtils.isLessThan(currentVersion, targetVersion);
        // assert
        assertFalse(result, "Expected 1.2.3 to not be less than 1.2.3");
    }

    /**
     * Test when current version is greater than target version
     */
    @Test
    public void testIsLessThanWhenCurrentVersionIsGreater() throws Throwable {
        // arrange
        String currentVersion = "1.2.4";
        String targetVersion = "1.2.3";
        // act
        boolean result = VersionUtils.isLessThan(currentVersion, targetVersion);
        // assert
        assertFalse(result, "Expected 1.2.4 to not be less than 1.2.3");
    }

    /**
     * Test when versions have different number of parts (shorter current version)
     */
    @Test
    public void testIsLessThanWithShorterCurrentVersion() throws Throwable {
        // arrange
        String currentVersion = "1.2";
        String targetVersion = "1.2.1";
        // act
        boolean result = VersionUtils.isLessThan(currentVersion, targetVersion);
        // assert
        assertTrue(result, "Expected 1.2 to be less than 1.2.1");
    }

    /**
     * Test when versions have different number of parts (longer current version)
     */
    @Test
    public void testIsLessThanWithLongerCurrentVersion() throws Throwable {
        // arrange
        String currentVersion = "1.2.0";
        String targetVersion = "1.2";
        // act
        boolean result = VersionUtils.isLessThan(currentVersion, targetVersion);
        // assert
        assertFalse(result, "Expected 1.2.0 to not be less than 1.2");
    }

    /**
     * Test with versions containing leading zeros
     */
    @Test
    public void testIsLessThanWithLeadingZeros() throws Throwable {
        // arrange
        String currentVersion = "1.02.3";
        String targetVersion = "1.2.3";
        // act
        boolean result = VersionUtils.isLessThan(currentVersion, targetVersion);
        // assert
        assertFalse(result, "Expected 1.02.3 to not be less than 1.2.3");
    }

    /**
     * Test with empty strings
     */
    @Test
    public void testIsLessThanWithEmptyStrings() throws Throwable {
        // arrange
        String currentVersion = "";
        String targetVersion = "";
        // act
        boolean result = VersionUtils.isLessThan(currentVersion, targetVersion);
        // assert
        assertFalse(result, "Expected empty string to not be less than empty string");
    }

    /**
     * Test with null current version
     */
    @Test
    public void testIsLessThanWithNullCurrentVersion() throws Throwable {
        // arrange
        String currentVersion = null;
        String targetVersion = "1.0.0";
        // act
        boolean result = VersionUtils.isLessThan(currentVersion, targetVersion);
        // assert
        assertTrue(result, "Expected null current version to be less than non-null target version");
    }

    /**
     * Test with null target version
     */
    @Test
    public void testIsLessThanWithNullTargetVersion() throws Throwable {
        // arrange
        String currentVersion = "1.0.0";
        String targetVersion = null;
        // act
        boolean result = VersionUtils.isLessThan(currentVersion, targetVersion);
        // assert
        assertFalse(result, "Expected non-null current version to not be less than null target version");
    }

    /**
     * Test with both versions null
     */
    @Test
    public void testIsLessThanWithBothVersionsNull() throws Throwable {
        // arrange
        String currentVersion = null;
        String targetVersion = null;
        // act
        boolean result = VersionUtils.isLessThan(currentVersion, targetVersion);
        // assert
        assertFalse(result, "Expected null versions to not be less than each other");
    }
}
