package com.sankuai.dzshoppingguide.product.detail.application.builder.consult;

import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.extend.BaseBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.consult.DetailIMFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.consult.DetailIMResult;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.FetcherDAGStarter;
import com.sankuai.dzshoppingguide.product.detail.spi.consult.vo.DealDetailIMVO;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import lombok.extern.slf4j.Slf4j;

import java.net.URLDecoder;

/**
 * <AUTHOR>
 * @create 2025/3/18 20:05
 */
@Builder(
        moduleKey = ModuleKeyConstants.MODULE_DETAIL_DEAL_CONSULT_MODULE,
        startFetcher = FetcherDAGStarter.class,
        dependentFetchers = {
                DetailIMFetcher.class
        }
)
@Slf4j
public class DealDetailIMBuilder extends BaseBuilder<DealDetailIMVO> {
    @Override
    public DealDetailIMVO doBuild() {
        DetailIMResult consultResult = getDependencyResult(DetailIMFetcher.class);

        DealDetailIMVO result = new DealDetailIMVO();
        String imUrl = consultResult.getImUrl();
        result.setOnlineConsultUrl(imUrl);
        result.setOriginalOnlineConsultUrl(buildOriginalOnlineConsult(imUrl));
        return result;
    }

    public String buildOriginalOnlineConsult(String imUrl){
        try {
            String fullURL = imUrl;
            if (fullURL == null) {
                return null;
            }
            int start = fullURL.indexOf("?url=");
            if (start == -1) {
                return URLDecoder.decode(fullURL, "UTF-8");
            } else {
                String urlWithoutPrefix = fullURL.substring(start + "?url=".length());
                return URLDecoder.decode(urlWithoutPrefix, "UTF-8");
            }
        } catch (Exception e) {
            log.error("buildOriginalOnlineConsult error, e = ", e);
            return null;
        }
    }
}
