package com.sankuai.dzshoppingguide.product.detail.application.builder.pricebar.builder.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 优惠标签枚举
 * <AUTHOR>
 * @date 2025/04/15
 */
@Getter
@AllArgsConstructor
public enum PromoBarTagEnum {

    MAGIC_COUPON(1, "神券", 1),
    MEMBER_CARD(2, "会员卡", 2),
    NORMAL_COUPON(3, "普通优惠券", 3),
    PLATFORM_COUPON(4, "平台券", 3),
    MERCHANT_COUPON(5, "商家券", 3),
    GOVERNMENT_COUPON(6, "政府券", 3),
    SECKILL(7, "限时秒杀", 4),
    MT_SUBSIDY(8, "美团补贴", 5),
    NEW_USER(9, "新客特惠", 6),
    SPECIAL_OFFER(10, "特惠促销", 7),
    DEAL_PROMO(11, "团购优惠", 8),
    RETURN_COUPON(12, "消费返券", 9),
    RETURN_GIFT(13, "下单返礼", 10),
    BUY_GIFT(14, "买赠活动", 11),
    PIN_TUAN(15, "拼团优惠", 12),
    IDLE_TIME(16, "闲时特惠", 13),
    BUY_MORE_DISCOUNT(17, "多买多减", 14),
    FINANCIAL_COUPON(18, "金融券", 15);

    /**
     * 优惠标签索引，用于唯一标识
     */
    private final int index;

    /**
     * 优惠标签主题
     */
    private final String labelTheme;

    /**
     * 排序序号
     */
    private final int sort;

    /**
     * 根据index获取枚举
     */
    public static PromoBarTagEnum getByIndex(int index) {
        return Arrays.stream(values())
                .filter(tag -> tag.getIndex() == index)
                .findFirst()
                .orElse(null);
    }

    /**
     * 获取未使用优惠标签列表
     * 未用优惠：神券＞会员卡＞买赠活动＞普通优惠券（平台券>商家券>政府券）＞拼团优惠＞闲时特惠＞多买多减＞金融券
     */
    public static List<PromoBarTagEnum> getMorePromoBarTags() {
        return Arrays.asList(
                MAGIC_COUPON,
                MEMBER_CARD,
                BUY_GIFT,
                PLATFORM_COUPON,
                MERCHANT_COUPON,
                GOVERNMENT_COUPON,
                PIN_TUAN,
                IDLE_TIME,
                BUY_MORE_DISCOUNT,
                FINANCIAL_COUPON
        );
    }

    /**
     * 获取已享优惠标签列表
     * 已享优惠：神券＞会员卡＞普通优惠券（平台券>商家券>政府券）＞限时秒杀＞美团补贴＞新客特惠＞特惠促销＞团购优惠＞消费返券＞下单返礼＞金融券
     */
    public static List<PromoBarTagEnum> getUsedPromoTags() {
        return Arrays.asList(
                MAGIC_COUPON,
                MEMBER_CARD,
                PLATFORM_COUPON,
                MERCHANT_COUPON,
                GOVERNMENT_COUPON,
                SECKILL,
                MT_SUBSIDY,
                NEW_USER,
                SPECIAL_OFFER,
                DEAL_PROMO,
                RETURN_COUPON,
                RETURN_GIFT,
                FINANCIAL_COUPON
        );
    }

    /**
     * 获取普通优惠券标签列表
     * 普通优惠券：平台券>商家券>政府券
     */
    public static List<PromoBarTagEnum> getNormalCouponSubTypes() {
        return Arrays.asList(
                PLATFORM_COUPON,
                MERCHANT_COUPON,
                GOVERNMENT_COUPON
        );
    }

    /**
     * 获取立减优惠标签列表
     * 立减优惠：限时秒杀＞美团补贴＞新客特惠＞特惠促销
     */
    public static List<PromoBarTagEnum> getInstantDiscountTags() {
        return Arrays.asList(
                SECKILL,
                MT_SUBSIDY,
                NEW_USER,
                SPECIAL_OFFER
        );
    }

    /**
     * 获取其他活动标签列表
     * 其他活动：闲时特惠＞多买多减＞消费返券＞下单返礼＞金融券
     */
    public static List<PromoBarTagEnum> getOtherActivityTags() {
        return Arrays.asList(
                IDLE_TIME,
                BUY_MORE_DISCOUNT,
                RETURN_COUPON,
                RETURN_GIFT,
                FINANCIAL_COUPON
        );
    }

    /**
     * 判断是否为普通优惠券子类型
     */
    public boolean isNormalCouponSubType() {
        return getNormalCouponSubTypes().contains(this);
    }

    /**
     * 判断是否为立减优惠类型
     */
    public boolean isInstantDiscount() {
        return getInstantDiscountTags().contains(this);
    }

    /**
     * 判断是否为其他活动类型
     */
    public boolean isOtherActivity() {
        return getOtherActivityTags().contains(this);
    }

    /**
     * 根据标签主题获取枚举
     */
    public static PromoBarTagEnum getByLabelTheme(String labelTheme) {
        return Arrays.stream(values())
                .filter(tag -> tag.getLabelTheme().equals(labelTheme))
                .findFirst()
                .orElse(null);
    }

    /**
     * 获取所有标签（按sort排序）
     */
    public static List<PromoBarTagEnum> getAllTags() {
        return Arrays.stream(values())
                .sorted((a, b) -> Integer.compare(a.getSort(), b.getSort()))
                .collect(Collectors.toList());
    }

    /**
     * 获取所有标签主题（按sort排序）
     */
    public static List<String> getAllLabelThemes() {
        return getAllTags().stream()
                .map(PromoBarTagEnum::getLabelTheme)
                .collect(Collectors.toList());
    }
}