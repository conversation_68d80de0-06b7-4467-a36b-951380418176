package com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.component.trade.block.buy.button.shopping.cart;

import com.alibaba.fastjson.JSON;
import com.dianping.lion.facade.LionFacade;
import com.google.common.collect.Sets;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.component.trade.block.utils.TradeButtonBuildUtils;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.exception.TradeButtonBuildFatalException;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.OrderPageUrlResult;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sale.info.enums.ProductSaleStatusEnum;
import com.sankuai.dzshoppingguide.product.detail.application.utils.VersionUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.extend.shopping.cart.AddShoppingCardActionVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.component.trade.button.BaseTradeButtonVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.rich.content.PicRichContentVO;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;

import java.util.Collections;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;

import static com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.constant.ButtonStyleConstant.ADD_SHOPPING_CART_BUTTON_STYLE;

/**
 * @Author: guangyujie
 * @Date: 2025/3/17 11:53
 */
@Slf4j
public class AddShoppingCardButtonComponent {

    public static BaseTradeButtonVO build(final ProductDetailPageRequest request,
                                          final ProductCategory productCategory,
                                          final long skuId,
                                          final OrderPageUrlResult orderPageUrlResult,
                                          final boolean isOnlyIconStyle,
                                          final ProductSaleStatusEnum saleStatus) {
        try {
            if (hasSourcePromoParam(orderPageUrlResult)) {
                return null;
            }
            if (!isShoppingCart(request, productCategory)) {
                return null;
            }
            if (isOnlyIconStyle) {
                return TradeButtonBuildUtils.buildOnlyIconTradeButtonVO(
                        saleStatus,
                        new PicRichContentVO(48, 48, "https://p0.meituan.net/ingee/82e379408c5c1bc46cf55bbd48be4ba5806.png"),
                        ADD_SHOPPING_CART_BUTTON_STYLE,
                        new AddShoppingCardActionVO(request.getProductType(), request.getProductId(), skuId)
                );
            } else {
                return TradeButtonBuildUtils.buildTradeButtonVO(
                        saleStatus,
                        "加入购物车",
                        ADD_SHOPPING_CART_BUTTON_STYLE,
                        new AddShoppingCardActionVO(request.getProductType(), request.getProductId(), skuId)
                );
            }
        } catch (Throwable throwable) {
            log.error("AddShoppingCardButtonComponent.build,request:{}",
                    JSON.toJSONString(request), new TradeButtonBuildFatalException(throwable));
            return null;
        }
    }

    private final static Set<String> ACCESS_BENEFIT_LIST = Sets.newHashSet(
            "pass_param",
            "eventpromochannel",
            "lyyuserid",
            "promotionchannel"
    );

    /**
     * 只要链接上携带了渠道优惠相关的参数则不展示购物车
     */
    private static boolean hasSourcePromoParam(final OrderPageUrlResult orderPageUrlResult) {
        if (orderPageUrlResult == null || orderPageUrlResult.getExtParam() == null) {
            return false;
        }
        for (String s : ACCESS_BENEFIT_LIST) {
            if (orderPageUrlResult.getExtParam().containsKey(s)) {
                return true;
            }
        }
        return false;
    }

    public static boolean isShoppingCart(final ProductDetailPageRequest request,
                                         final ProductCategory productCategory) {
        return request.getClientTypeEnum().isMtClientType()
                && request.getClientTypeEnum().isInApp()
                && VersionUtils.isGreaterThanOrEqual(request.getShepherdGatewayParam().getMrnVersion(), "0.5.1")
                && VersionUtils.isGreaterThanOrEqual(request.getShepherdGatewayParam().getAppVersion(), "12.8.200")
                && isValidCategory(productCategory);
    }

    /**
     * 不知道为啥配了两个lion
     */
    private static boolean isValidCategory(final ProductCategory productCategory) {
        Set<Integer> newShoppingCartCategoryIds = LionFacade
                .getSet("com.sankuai.dzu.tpbase.dztgdetailweb.shopping.cart.new.category.ids", Integer.class, Collections.emptySet());
        Set<Integer> shoppingCartCategoryIds = LionFacade
                .getSet("com.sankuai.dzu.tpbase.dztgdetailweb.shopping.cart.category.ids", Integer.class, Collections.emptySet());
        int categoryId = Optional.ofNullable(productCategory)
                .map(ProductCategory::getProductSecondCategoryId)
                .orElse(0);
        return newShoppingCartCategoryIds.contains(categoryId) || shoppingCartCategoryIds.contains(categoryId);
    }

    /**
     * 多sku商品加购浮层，暂时用不到
     */
    private static @NotNull String getAddShoppingCardUrl(final ProductDetailPageRequest request,
                                                         final ProductBaseInfo productBaseInfo,
                                                         final ProductAttr productAttr,
                                                         long skuId) {
        boolean isMT = request.getClientTypeEnum().isMtClientType();
        String schema = "%s?mrn_biz=gc&mrn_entry=gcsubmitordermrnmodules-unify&mrn_component=addtocartpage-popup&mrn_min_version=0.0.26&isTransparent=true&hideLoading=true&mrn_transparent=true&mrn_hideloading=true&mrn_hideNextNavBar=true&pagesource=dealGroupDetail&dealid=%s&shopid=%s";
        // 如果是穿戴甲，则不用拼上skuid
        String redirectUrl;
        if (isNewWearableNailDeal(productBaseInfo, productAttr)) {
            redirectUrl = String.format(
                    schema + "&need_exhibit_skuid=1",
                    isMT ? "imeituan://www.meituan.com/mrn" : "dianping://mrn",
                    request.getProductId(),
                    request.getPoiId()
            );
        } else {
            // 优先使用前端传入的用户选择sku，兜底使用第一个sku
            redirectUrl = String.format(
                    schema + "&skuid=%s",
                    isMT ? "imeituan://www.meituan.com/mrn" : "dianping://mrn",
                    request.getProductId(),
                    request.getPoiId(),
                    skuId
            );
        }
        return redirectUrl;
    }

    public static boolean isNewWearableNailDeal(final ProductBaseInfo productBaseInfo,
                                                final ProductAttr productAttr) {
        if (productBaseInfo == null || productAttr == null) {
            return false;
        }
        DealGroupCategoryDTO category = productBaseInfo.getCategory();
        return Objects.equals(category.getCategoryId(), 502L)
                && Objects.equals(category.getServiceType(), "穿戴甲")
                && productAttr.getProductAttr("tag_unifyProduct").isPresent();
    }

}
