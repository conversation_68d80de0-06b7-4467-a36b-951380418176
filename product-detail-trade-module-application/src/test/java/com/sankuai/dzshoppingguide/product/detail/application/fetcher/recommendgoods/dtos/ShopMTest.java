package com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommendgoods.dtos;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import com.google.common.collect.Lists;
import java.util.ArrayList;
import org.junit.jupiter.api.DisplayName;
import java.util.Arrays;
import java.util.Collections;
import org.mockito.InjectMocks;

/**
 * Test cases for ShopM.setLongShopId method
 */
@ExtendWith(MockitoExtension.class)
public class ShopMTest {

    @InjectMocks
    private ShopM shopM;

    /**
     * Test when longShopId > 0 and shopId == 0
     * Expected: Should set both longShopId and shopId
     */
    @Test
    public void testSetLongShopId_WhenLongShopIdPositiveAndShopIdZero() throws Throwable {
        // arrange
        ShopM shopM = new ShopM();
        long testLongShopId = 123L;
        // act
        shopM.setLongShopId(testLongShopId);
        // assert
        assertAll(() -> assertEquals(testLongShopId, shopM.getLongShopId(), "longShopId should be set to the input value"), () -> assertEquals((int) testLongShopId, shopM.getShopId(), "shopId should be set to the cast value of longShopId"));
    }

    /**
     * Test when longShopId > 0 but shopId is already set
     * Expected: Should only set longShopId without modifying existing shopId
     */
    @Test
    public void testSetLongShopId_WhenLongShopIdPositiveAndShopIdNonZero() throws Throwable {
        // arrange
        ShopM shopM = new ShopM();
        int originalShopId = 456;
        shopM.setShopId(originalShopId);
        long testLongShopId = 123L;
        // act
        shopM.setLongShopId(testLongShopId);
        // assert
        assertAll(() -> assertEquals(testLongShopId, shopM.getLongShopId(), "longShopId should be set to the input value"), () -> assertEquals(originalShopId, shopM.getShopId(), "shopId should remain unchanged"));
    }

    /**
     * Test when longShopId = 0
     * Expected: Should only set longShopId to 0, shopId should remain unchanged
     */
    @Test
    public void testSetLongShopId_WhenLongShopIdZero() throws Throwable {
        // arrange
        ShopM shopM = new ShopM();
        long testLongShopId = 0L;
        // act
        shopM.setLongShopId(testLongShopId);
        // assert
        assertAll(() -> assertEquals(testLongShopId, shopM.getLongShopId(), "longShopId should be set to 0"), () -> assertEquals(0, shopM.getShopId(), "shopId should remain 0"));
    }

    /**
     * Test when longShopId < 0
     * Expected: Should set both longShopId and shopId to negative value when shopId is 0
     */
    @Test
    public void testSetLongShopId_WhenLongShopIdNegative() throws Throwable {
        // arrange
        ShopM shopM = new ShopM();
        long testLongShopId = -123L;
        // act
        shopM.setLongShopId(testLongShopId);
        // assert
        assertAll(() -> assertEquals(testLongShopId, shopM.getLongShopId(), "longShopId should be set to negative value"), () -> assertEquals((int) testLongShopId, shopM.getShopId(), "shopId should be set to negative value"));
    }

    /**
     * Test with maximum long value
     * Expected: Should set longShopId to max value and shopId to its int conversion
     */
    @Test
    public void testSetLongShopId_WhenLongShopIdMaxValue() throws Throwable {
        // arrange
        ShopM shopM = new ShopM();
        long testLongShopId = Long.MAX_VALUE;
        // act
        shopM.setLongShopId(testLongShopId);
        // assert
        assertAll(() -> assertEquals(testLongShopId, shopM.getLongShopId(), "longShopId should be set to max value"), () -> assertEquals((int) testLongShopId, shopM.getShopId(), "shopId should be set to int cast of max value"));
    }

    /**
     * Test with minimum long value
     * Expected: Should set longShopId to min value and shopId to its int conversion
     */
    @Test
    public void testSetLongShopId_WhenLongShopIdMinValue() throws Throwable {
        // arrange
        ShopM shopM = new ShopM();
        long testLongShopId = Long.MIN_VALUE;
        // act
        shopM.setLongShopId(testLongShopId);
        // assert
        assertAll(() -> assertEquals(testLongShopId, shopM.getLongShopId(), "longShopId should be set to min value"), () -> assertEquals((int) testLongShopId, shopM.getShopId(), "shopId should be set to int cast of min value"));
    }

    @Test
    @DisplayName("Should create new extAttrs list when it is null")
    public void testSetAttr_WhenExtAttrsIsNull() throws Throwable {
        // arrange
        ShopM shopM = new ShopM();
        String attrName = "testName";
        String attrValue = "testValue";
        // act
        shopM.setAttr(attrName, attrValue);
        // assert
        assertNotNull(shopM.getExtAttrs());
        assertEquals(1, shopM.getExtAttrs().size());
        AttrM addedAttr = shopM.getExtAttrs().get(0);
        assertEquals(attrName, addedAttr.getName());
        assertEquals(attrValue, addedAttr.getValue());
    }

    @Test
    @DisplayName("Should add attribute when extAttrs is empty")
    public void testSetAttr_WhenExtAttrsIsEmpty() throws Throwable {
        // arrange
        ShopM shopM = new ShopM();
        shopM.setExtAttrs(new ArrayList<>());
        String attrName = "testName";
        String attrValue = "testValue";
        // act
        shopM.setAttr(attrName, attrValue);
        // assert
        assertNotNull(shopM.getExtAttrs());
        assertEquals(1, shopM.getExtAttrs().size());
        AttrM addedAttr = shopM.getExtAttrs().get(0);
        assertEquals(attrName, addedAttr.getName());
        assertEquals(attrValue, addedAttr.getValue());
    }

    @Test
    @DisplayName("Should append attribute when extAttrs has existing elements")
    public void testSetAttr_WhenExtAttrsHasExistingElements() throws Throwable {
        // arrange
        ShopM shopM = new ShopM();
        AttrM existingAttr = new AttrM("existingName", "existingValue");
        shopM.setExtAttrs(Lists.newArrayList(existingAttr));
        String attrName = "testName";
        String attrValue = "testValue";
        // act
        shopM.setAttr(attrName, attrValue);
        // assert
        assertNotNull(shopM.getExtAttrs());
        assertEquals(2, shopM.getExtAttrs().size());
        AttrM firstAttr = shopM.getExtAttrs().get(0);
        AttrM secondAttr = shopM.getExtAttrs().get(1);
        assertEquals("existingName", firstAttr.getName());
        assertEquals("existingValue", firstAttr.getValue());
        assertEquals(attrName, secondAttr.getName());
        assertEquals(attrValue, secondAttr.getValue());
    }

    @Test
    @DisplayName("Should handle empty string parameters")
    public void testSetAttr_WithEmptyStrings() throws Throwable {
        // arrange
        ShopM shopM = new ShopM();
        String attrName = "";
        String attrValue = "";
        // act
        shopM.setAttr(attrName, attrValue);
        // assert
        assertNotNull(shopM.getExtAttrs());
        assertEquals(1, shopM.getExtAttrs().size());
        AttrM addedAttr = shopM.getExtAttrs().get(0);
        assertEquals(attrName, addedAttr.getName());
        assertEquals(attrValue, addedAttr.getValue());
    }

    @Test
    @DisplayName("Should handle multiple attribute additions")
    public void testSetAttr_MultipleAttributes() throws Throwable {
        // arrange
        ShopM shopM = new ShopM();
        // act
        shopM.setAttr("attr1", "value1");
        shopM.setAttr("attr2", "value2");
        shopM.setAttr("attr3", "value3");
        // assert
        assertNotNull(shopM.getExtAttrs());
        assertEquals(3, shopM.getExtAttrs().size());
        assertEquals("attr1", shopM.getExtAttrs().get(0).getName());
        assertEquals("value1", shopM.getExtAttrs().get(0).getValue());
        assertEquals("attr2", shopM.getExtAttrs().get(1).getName());
        assertEquals("value2", shopM.getExtAttrs().get(1).getValue());
        assertEquals("attr3", shopM.getExtAttrs().get(2).getName());
        assertEquals("value3", shopM.getExtAttrs().get(2).getValue());
    }

    @Test
    @DisplayName("Should handle null parameter values")
    public void testSetAttr_WithNullValues() throws Throwable {
        // arrange
        ShopM shopM = new ShopM();
        String attrName = null;
        String attrValue = null;
        // act
        shopM.setAttr(attrName, attrValue);
        // assert
        assertNotNull(shopM.getExtAttrs());
        assertEquals(1, shopM.getExtAttrs().size());
        AttrM addedAttr = shopM.getExtAttrs().get(0);
        assertNull(addedAttr.getName());
        assertNull(addedAttr.getValue());
    }

    @Test
    public void testGetAttr_WhenExtAttrsIsNull_ReturnsNull() throws Throwable {
        // arrange
        shopM.setExtAttrs(null);
        // act
        String result = shopM.getAttr("anyAttr");
        // assert
        assertNull(result);
    }

    @Test
    public void testGetAttr_WhenExtAttrsIsEmpty_ReturnsNull() throws Throwable {
        // arrange
        shopM.setExtAttrs(Collections.emptyList());
        // act
        String result = shopM.getAttr("anyAttr");
        // assert
        assertNull(result);
    }

    @Test
    public void testGetAttr_WhenNoMatchingAttr_ReturnsNull() throws Throwable {
        // arrange
        AttrM attr1 = new AttrM("attr1", "value1");
        AttrM attr2 = new AttrM("attr2", "value2");
        shopM.setExtAttrs(Arrays.asList(attr1, attr2));
        // act
        String result = shopM.getAttr("nonExistingAttr");
        // assert
        assertNull(result);
    }

    @Test
    public void testGetAttr_WhenMatchingAttrExists_ReturnsCorrectValue() throws Throwable {
        // arrange
        AttrM attr1 = new AttrM("attr1", "value1");
        AttrM attr2 = new AttrM("attr2", "value2");
        shopM.setExtAttrs(Arrays.asList(attr1, attr2));
        // act
        String result = shopM.getAttr("attr2");
        // assert
        assertEquals("value2", result);
    }

    @Test
    public void testGetAttr_WhenAttrNameIsNull_ReturnsNull() throws Throwable {
        // arrange
        AttrM attr = new AttrM("attr1", "value1");
        shopM.setExtAttrs(Collections.singletonList(attr));
        // act
        String result = shopM.getAttr(null);
        // assert
        assertNull(result);
    }

    @Test
    public void testGetAttr_WhenExtAttrsContainsNull_ReturnsCorrectValue() throws Throwable {
        // arrange
        AttrM attr = new AttrM("attr1", "value1");
        shopM.setExtAttrs(Arrays.asList(null, attr));
        // act
        String result = shopM.getAttr("attr1");
        // assert
        assertEquals("value1", result);
    }

    @Test
    public void testGetAttr_WhenMultipleMatchesExist_ReturnsFirstMatch() throws Throwable {
        // arrange
        AttrM attr1 = new AttrM("sameName", "firstValue");
        AttrM attr2 = new AttrM("sameName", "secondValue");
        shopM.setExtAttrs(Arrays.asList(attr1, attr2));
        // act
        String result = shopM.getAttr("sameName");
        // assert
        assertEquals("firstValue", result);
    }
}
