package com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.component.cepintuan;

import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.ProductBottomBarComponentVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.enums.BottomBarComponentTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @Author: guang<PERSON><PERSON>e
 * @Date: 2025/3/16 14:46
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CEPinTuanInfoVO extends ProductBottomBarComponentVO {

    /**
     * 还需要的参团人数
     */
    private int needMemberCount;

    /**
     * 头像列表
     */
    private List<String> avatars;

    @Override
    public int getComponentType() {
        return BottomBarComponentTypeEnum.COST_EFFECTIVE_PIN_TUAN_INFO.getCode();
    }

}
