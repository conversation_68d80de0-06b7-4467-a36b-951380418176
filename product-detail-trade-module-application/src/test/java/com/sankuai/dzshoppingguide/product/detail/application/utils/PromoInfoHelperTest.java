package com.sankuai.dzshoppingguide.product.detail.application.utils;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import com.dianping.lion.common.util.JsonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import com.sankuai.nibmkt.promotion.api.common.enums.PromotionPropertyEnum;
import java.util.HashMap;
import java.util.Map;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * 测试 getFinanceExtJson 方法
 */
@ExtendWith(MockitoExtension.class)
public class PromoInfoHelperTest {

    /**
     * 测试当 coupon 为 null 时，返回空字符串
     */
    @Test
    public void testGetFinanceExtJsonWhenCouponIsNull() throws Throwable {
        // arrange
        PromoDTO coupon = null;
        // act
        String result = PromoInfoHelper.getFinanceExtJson(coupon);
        // assert
        assertEquals("", result);
    }

    /**
     * 测试当 coupon 不为 null，但 promotionOtherInfoMap 为 null 时，返回空字符串
     */
    @Test
    public void testGetFinanceExtJsonWhenPromotionOtherInfoMapIsNull() throws Throwable {
        // arrange
        PromoDTO coupon = Mockito.mock(PromoDTO.class);
        when(coupon.getPromotionOtherInfoMap()).thenReturn(null);
        // act
        String result = PromoInfoHelper.getFinanceExtJson(coupon);
        // assert
        assertEquals("", result);
    }

    /**
     * 测试当 coupon 不为 null，promotionOtherInfoMap 不为空但不包含 FINANCE_EXT 时，返回空字符串
     */
    @Test
    public void testGetFinanceExtJsonWhenPromotionOtherInfoMapDoesNotContainFinanceExt() throws Throwable {
        // arrange
        PromoDTO coupon = Mockito.mock(PromoDTO.class);
        Map<String, String> promotionOtherInfoMap = new HashMap<>();
        promotionOtherInfoMap.put("OTHER_KEY", "OTHER_VALUE");
        when(coupon.getPromotionOtherInfoMap()).thenReturn(promotionOtherInfoMap);
        // act
        String result = PromoInfoHelper.getFinanceExtJson(coupon);
        // assert
        assertEquals("", result);
    }

    /**
     * 测试当 coupon 不为 null，promotionOtherInfoMap 不为空且包含 FINANCE_EXT 时，返回 FINANCE_EXT 对应的值
     */
    @Test
    public void testGetFinanceExtJsonWhenPromotionOtherInfoMapContainsFinanceExt() throws Throwable {
        // arrange
        PromoDTO coupon = Mockito.mock(PromoDTO.class);
        Map<String, String> promotionOtherInfoMap = new HashMap<>();
        promotionOtherInfoMap.put(PromotionPropertyEnum.FINANCE_EXT.getValue(), "FINANCE_EXT_VALUE");
        when(coupon.getPromotionOtherInfoMap()).thenReturn(promotionOtherInfoMap);
        // act
        String result = PromoInfoHelper.getFinanceExtJson(coupon);
        // assert
        assertEquals("FINANCE_EXT_VALUE", result);
    }

    /**
     * Test case for null coupon input
     */
    @Test
    public void testGetFinanceExtPackageSecretKey_NullCoupon() throws Throwable {
        // arrange
        PromoDTO coupon = null;
        // act
        String result = PromoInfoHelper.getFinanceExtPackageSecretKey(coupon);
        // assert
        assertEquals("", result);
    }

    /**
     * Test case for empty finance ext JSON
     */
    @Test
    public void testGetFinanceExtPackageSecretKey_EmptyFinanceExt() throws Throwable {
        // arrange
        PromoDTO coupon = new PromoDTO();
        Map<String, String> promotionOtherInfoMap = new HashMap<>();
        promotionOtherInfoMap.put("FINANCE_EXT", "");
        coupon.setPromotionOtherInfoMap(promotionOtherInfoMap);
        // act
        String result = PromoInfoHelper.getFinanceExtPackageSecretKey(coupon);
        // assert
        assertEquals("", result);
    }

    /**
     * Test case for valid finance ext JSON with packageSecretKey
     */
    @Test
    public void testGetFinanceExtPackageSecretKey_ValidJsonWithKey() throws Throwable {
        // arrange
        PromoDTO coupon = new PromoDTO();
        Map<String, String> promotionOtherInfoMap = new HashMap<>();
        promotionOtherInfoMap.put("FINANCE_EXT", "{\"packageSecretKey\":\"test123\"}");
        coupon.setPromotionOtherInfoMap(promotionOtherInfoMap);
        // act
        String result = PromoInfoHelper.getFinanceExtPackageSecretKey(coupon);
        // assert
        assertEquals("test123", result);
    }

    /**
     * Test case for valid finance ext JSON without packageSecretKey
     */
    @Test
    public void testGetFinanceExtPackageSecretKey_ValidJsonWithoutKey() throws Throwable {
        // arrange
        PromoDTO coupon = new PromoDTO();
        Map<String, String> promotionOtherInfoMap = new HashMap<>();
        promotionOtherInfoMap.put("FINANCE_EXT", "{\"otherKey\":\"value\"}");
        coupon.setPromotionOtherInfoMap(promotionOtherInfoMap);
        // act
        String result = PromoInfoHelper.getFinanceExtPackageSecretKey(coupon);
        // assert
        assertEquals("", result);
    }

    /**
     * Test case for invalid JSON format
     */
    @Test
    public void testGetFinanceExtPackageSecretKey_InvalidJson() throws Throwable {
        // arrange
        PromoDTO coupon = new PromoDTO();
        Map<String, String> promotionOtherInfoMap = new HashMap<>();
        // Using empty object as JSON instead of invalid JSON string
        promotionOtherInfoMap.put("FINANCE_EXT", "{}");
        coupon.setPromotionOtherInfoMap(promotionOtherInfoMap);
        // act
        String result = PromoInfoHelper.getFinanceExtPackageSecretKey(coupon);
        // assert
        assertEquals("", result);
    }
}
