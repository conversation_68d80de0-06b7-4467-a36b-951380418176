package com.sankuai.dzshoppingguide.product.detail.spi.enums;

@SuppressWarnings("all")
public enum PlayCenterPlatformEnum {
    MEITUAN_APP(1, "美团APP"),
    DIANPING_APP(2, "点评APP"),
    IBAN(3, "美团I版"),
    DIANPING_IBAN(4, "点评I版"),
    MT_WEAPP(5, "美团微信小程序"),
    DP_WEAPP(6, "点评微信小程序"),
    MT_HOTEL_WEAPP(7, "美团酒店小程序"),
    H5(8, "H5"),
    PC(9, "PC"),
    UNKNOWN(0, "未知来源");


    private final int id;
    private final String name;

    PlayCenterPlatformEnum(int id, String name) {
        this.id = id;
        this.name = name;
    }

    public int getId() {
        return id;
    }

    public String getName() {
        return name;
    }
}
