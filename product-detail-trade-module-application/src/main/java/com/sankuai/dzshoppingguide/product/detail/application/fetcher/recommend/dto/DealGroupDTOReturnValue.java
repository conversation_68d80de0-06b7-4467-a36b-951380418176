package com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommend.dto;


import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @Author: zhangyuan103
 * @Date: 2025/5/23
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
public class DealGroupDTOReturnValue extends FetcherReturnValueDTO {
    private List<DealGroupDTO> dealGroupDTOs;
}
