package com.sankuai.dzshoppingguide.product.detail.application.fetcher.membercard.card.discount;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collections;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

import javax.annotation.Resource;

import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.dto.ProductPriceReturnValue;
import com.sankuai.mpmctmember.query.thrift.dto.QueryPlanAndUserIdentityDetailResp;
import com.sankuai.newdzcard.supply.dto.ext.PremiumMemberCardExtDTO;
import com.sankuai.newdzcard.supply.dto.rights.PremiumMemberRightDTO;
import org.apache.commons.collections4.CollectionUtils;

import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dzcard.navigation.api.dto.DzCardQueryRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.membercard.card.integration.IntegrationMemberCard;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.membercard.card.integration.IntegrationMemberCardNoEntranceFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.ProductPromoPriceFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
import com.sankuai.dzshoppingguide.product.detail.application.service.dto.ShopMemberDetailV;
import com.sankuai.mpmctmember.query.common.enums.MemberChargeTypeEnum;
import com.sankuai.newdzcard.supply.dto.DzCardDTO;

@Fetcher(
    previousLayerDependencies = {IntegrationMemberCardNoEntranceFetcher.class, ProductPromoPriceFetcher.class}
)
public class MemberCardDiscountFetcher extends NormalFetcherContext<MemberCardDiscountReturnValue>{

    @Resource
    private CompositeAtomService compositeAtomService;

    @Override
    protected CompletableFuture<MemberCardDiscountReturnValue> doFetch() throws Exception {
        ProductPriceReturnValue productPriceReturnValue = getDependencyResult(ProductPromoPriceFetcher.class);
        IntegrationMemberCard integrationMemberCard = getDependencyResult(IntegrationMemberCardNoEntranceFetcher.class);
        if (Objects.isNull(integrationMemberCard) || Objects.isNull(integrationMemberCard.getMemberCardInterest())) {
            return CompletableFuture.completedFuture(null);
        }
        ShopMemberDetailV memberCardInterest = integrationMemberCard.getMemberCardInterest();
        MemberCardDiscountReturnValue memberCardDiscountReturnValue = new MemberCardDiscountReturnValue();
        // 会员卡类型
        MemberChargeTypeEnum memberCardTypeEnum = MemberChargeTypeEnum.getByCode(integrationMemberCard.getMemberCardType());
        memberCardDiscountReturnValue.setMemberCardTypeEnum(memberCardTypeEnum);
        // 是否是会员
        memberCardDiscountReturnValue.setMember(memberCardInterest.isMember());

        // 会员卡落地页
        QueryPlanAndUserIdentityDetailResp memberCardDetail = integrationMemberCard.getMemberCardDetail();
        if (Objects.isNull(memberCardDetail) || Objects.nonNull(memberCardDetail.getMemberPlanDetailDTO())) {
            memberCardDiscountReturnValue.setMemberCardUrl(memberCardDetail.getMemberPlanDetailDTO().getMemberPageLink());
        }
        // 付费会员卡
        if (integrationMemberCard.isPremiumMemberCard()) {
            DzCardQueryRequest dzCardQueryRequest = buildDzCardQueryRequest(memberCardInterest.getPlanId());
            return compositeAtomService.queryDzCard(dzCardQueryRequest).thenApply(result -> {
                if (CollectionUtils.isEmpty(result)) {
                    return null;
                }
                DzCardDTO dzCardDTO = result.get(0);
                if (Objects.isNull(dzCardDTO) || CollectionUtils.isEmpty(dzCardDTO.getRights()) || Objects.isNull(dzCardDTO.getExtInfo())) {
                    return null;
                }
                // 付费会员卡折扣
                PremiumMemberRightDTO dzCardRightDTO = (PremiumMemberRightDTO) dzCardDTO.getRights().get(0);
                memberCardDiscountReturnValue.setMemberCardDiscount(dzCardRightDTO.getDiscountValue().toPlainString());
                // 开卡费
                PremiumMemberCardExtDTO extInfo = (PremiumMemberCardExtDTO) dzCardDTO.getExtInfo();
                memberCardDiscountReturnValue.setOpenCardFee(extInfo.getPrice().toPlainString());
                // 有效期
                memberCardDiscountReturnValue.setValidPeriod(String.format("开通后%s月内有效", extInfo.getEffectiveMonths()));
                return memberCardDiscountReturnValue;
            });
        } else if (integrationMemberCard.isFreeMemberCard()) {
            PriceDisplayDTO promoPriceDisplayDTO = productPriceReturnValue.getFinalPriceDisplayDTO();
            PromoDTO memberCardPromo = getMemberCardPromo(promoPriceDisplayDTO);
            if (Objects.isNull(memberCardPromo) || Objects.isNull(memberCardPromo.getAmount())) {
                return CompletableFuture.completedFuture(null);
            }
            // 免费会员卡优惠
            BigDecimal memberCardAmount = memberCardPromo.getAmount();
            memberCardDiscountReturnValue.setMemberCardAmount(memberCardAmount.toPlainString());
            return CompletableFuture.completedFuture(memberCardDiscountReturnValue);
        }
        return CompletableFuture.completedFuture(null);
    }

    private PromoDTO getMemberCardPromo(PriceDisplayDTO promoPriceDisplayDTO) {
        if (Objects.isNull(promoPriceDisplayDTO) || CollectionUtils.isEmpty(promoPriceDisplayDTO.getUsedPromos())) {
            return null;
        }
        return promoPriceDisplayDTO.getUsedPromos().stream()
                .filter(Objects::nonNull)
                .filter(promoDTO -> Objects.nonNull(promoDTO.getIdentity()) && (Objects.equals(promoDTO.getIdentity().getPromoShowType(), "MEMBER_BENEFITS") || Objects.equals(promoDTO.getIdentity().getPromoShowType(), "NEW_MEMBER_BENEFITS")))
                .findFirst()
                .orElse(null);
    }

    private DzCardQueryRequest buildDzCardQueryRequest(long planId) {
        DzCardQueryRequest dzCardQueryRequest = new DzCardQueryRequest();
        dzCardQueryRequest.setCardIdList(Collections.singletonList((int) planId));
        return dzCardQueryRequest;
    }
}
