package com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommendgoods.dtos;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 商品融合优惠之后价格
 * @auther: liweilong06
 * @date: 2020/9/7 9:57 下午
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProductPriceM implements Serializable {

    /**
     *
     * 商品融合优惠之后价格, 不同融合逻辑体现为多个文案, 多个文案为单选
     */
    private String priceTag;

    /**
     * 商品融合优惠价格描述
     */
    private String priceDesc;

}
