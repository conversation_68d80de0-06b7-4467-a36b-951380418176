package com.sankuai.dzshoppingguide.product.detail.spi.bottombar.banner.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: guang<PERSON><PERSON>e
 * @Date: 2025/3/5 18:48
 */
@Getter
public enum BottomBarBannerTypeEnums {

    COMMON_TYPE(1, "通用样式（目前只有1，以后也可能只有1）"),
    SUCK_BOTTOM_TYPE(8, "神券提示吸底条");

    private final int code;

    private final String desc;

    BottomBarBannerTypeEnums(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static BottomBarBannerTypeEnums fromCode(int code) {
        for (BottomBarBannerTypeEnums value : BottomBarBannerTypeEnums.values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        throw new IllegalArgumentException("无效code:" + code);
    }

    public static boolean containsCode(int code) {
        for (BottomBarBannerTypeEnums value : BottomBarBannerTypeEnums.values()) {
            if (value.getCode() == code) {
                return true;
            }
        }
        return false;
    }

    public static Map<Integer, String> buildCodeDescMap() {
        return Arrays.stream(BottomBarBannerTypeEnums.values()).collect(Collectors.toMap(
                BottomBarBannerTypeEnums::getCode,
                BottomBarBannerTypeEnums::getDesc
        ));
    }

    public static Map<String, String> buildNameDescMap() {
        return Arrays.stream(BottomBarBannerTypeEnums.values()).collect(Collectors.toMap(
                BottomBarBannerTypeEnums::name,
                BottomBarBannerTypeEnums::getDesc
        ));
    }

}