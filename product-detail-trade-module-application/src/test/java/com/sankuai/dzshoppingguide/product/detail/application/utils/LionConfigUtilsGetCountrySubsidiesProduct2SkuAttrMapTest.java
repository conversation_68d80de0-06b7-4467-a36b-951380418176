package com.sankuai.dzshoppingguide.product.detail.application.utils;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import static org.powermock.api.mockito.PowerMockito.when;
import com.dianping.lion.client.Lion;
import com.google.common.collect.Lists;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.modules.junit4.PowerMockRunnerDelegate;
import static org.junit.Assert.*;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;

@RunWith(PowerMockRunner.class)
@PrepareForTest({ Lion.class })
@Slf4j
public class LionConfigUtilsGetCountrySubsidiesProduct2SkuAttrMapTest {

    private static final String TRADE_MODULE_APPKEY = "com.sankuai.travelcommon.trade";

    private static final String COUNTRY_SUBSIDIES_PRODUCT_2_SKU_ATTR_MAP = "country.subsidies.product.2.sku.attr.map";

    private static final String APP_KEY = "dztrade-mapi-web";

    private static final String CONFIG_KEY = "dztrade-mapi-web.cleaning.self.own.csUrl.switch";

    private static final boolean DEFAULT_VALUE = false;

    @Before
    public void setUp() {
        PowerMockito.mockStatic(Lion.class);
    }

    /**
     * 测试 serviceTypeId 为 null 时返回空列表
     */
    @Test
    public void testGetCountrySubsidiesProduct2SkuAttrMapWhenServiceTypeIdIsNull() throws Throwable {
        // act
        List<String> result = LionConfigUtils.getCountrySubsidiesProduct2SkuAttrMap(null);
        // assert
        assertEquals(Collections.emptyList(), result);
    }

    /**
     * 测试 serviceTypeId <=0 时返回空列表
     */
    @Test
    public void testGetCountrySubsidiesProduct2SkuAttrMapWhenServiceTypeIdIsZero() throws Throwable {
        // act
        List<String> result = LionConfigUtils.getCountrySubsidiesProduct2SkuAttrMap(0);
        // assert
        assertEquals(Collections.emptyList(), result);
    }

    /**
     * 测试 serviceTypeId 有效但配置中不存在对应值时返回 null
     */
    @Test
    public void testGetCountrySubsidiesProduct2SkuAttrMapWhenConfigNotContainsKey() throws Throwable {
        // arrange
        Map<String, List> mockMap = new HashMap<>();
        PowerMockito.when(Lion.getMap(eq(TRADE_MODULE_APPKEY), eq(COUNTRY_SUBSIDIES_PRODUCT_2_SKU_ATTR_MAP), eq(List.class), eq(Collections.<String, List>emptyMap()))).thenReturn(mockMap);
        // act
        List<String> result = LionConfigUtils.getCountrySubsidiesProduct2SkuAttrMap(123);
        // assert
        assertNull(result);
    }

    /**
     * 测试 Lion.getMap() 返回 null 时方法行为
     */
    @Test
    public void testGetCountrySubsidiesProduct2SkuAttrMapWhenLionReturnsNull() throws Throwable {
        // arrange
        PowerMockito.when(Lion.getMap(eq(TRADE_MODULE_APPKEY), eq(COUNTRY_SUBSIDIES_PRODUCT_2_SKU_ATTR_MAP), eq(List.class), eq(Collections.<String, List>emptyMap()))).thenReturn(null);
        // act
        List<String> result = LionConfigUtils.getCountrySubsidiesProduct2SkuAttrMap(123);
        // assert
        assertNull(result);
    }

    /**
     * 测试 Lion.getMap() 返回空 Map 时方法行为
     */
    @Test
    public void testGetCountrySubsidiesProduct2SkuAttrMapWhenLionReturnsEmptyMap() throws Throwable {
        // arrange
        PowerMockito.when(Lion.getMap(eq(TRADE_MODULE_APPKEY), eq(COUNTRY_SUBSIDIES_PRODUCT_2_SKU_ATTR_MAP), eq(List.class), eq(Collections.<String, List>emptyMap()))).thenReturn(Collections.<String, List>emptyMap());
        // act
        List<String> result = LionConfigUtils.getCountrySubsidiesProduct2SkuAttrMap(123);
        // assert
        assertNull(result);
    }

    @Test
    public void testNeedReplaceImToCsForCleaningWhenLionReturnsTrue() throws Throwable {
        // arrange
        PowerMockito.mockStatic(Lion.class);
        PowerMockito.when(Lion.getBoolean(eq(APP_KEY), eq(CONFIG_KEY), eq(DEFAULT_VALUE))).thenReturn(true);
        // act
        boolean result = LionConfigUtils.needReplaceImToCsForCleaning();
        // assert
        assertTrue("Method should return true when Lion config is true", result);
    }

    @Test
    public void testNeedReplaceImToCsForCleaningWhenLionReturnsFalse() throws Throwable {
        // arrange
        PowerMockito.mockStatic(Lion.class);
        PowerMockito.when(Lion.getBoolean(eq(APP_KEY), eq(CONFIG_KEY), eq(DEFAULT_VALUE))).thenReturn(false);
        // act
        boolean result = LionConfigUtils.needReplaceImToCsForCleaning();
        // assert
        assertFalse("Method should return false when Lion config is false", result);
    }

    @Test
    public void testNeedReplaceImToCsForCleaningWhenLionThrowsException() throws Throwable {
        // arrange
        PowerMockito.mockStatic(Lion.class);
        PowerMockito.when(Lion.getBoolean(eq(APP_KEY), eq(CONFIG_KEY), eq(DEFAULT_VALUE))).thenThrow(new RuntimeException("Lion configuration error"));
        // act
        boolean result = LionConfigUtils.needReplaceImToCsForCleaning();
        // assert
        assertFalse("Method should return false when Lion throws exception", result);
    }
}
