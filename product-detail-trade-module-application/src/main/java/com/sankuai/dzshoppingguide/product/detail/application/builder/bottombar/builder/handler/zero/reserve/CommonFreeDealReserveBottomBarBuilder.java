package com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.builder.handler.zero.reserve;

import com.alibaba.fastjson.JSON;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.BuilderTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.builder.BaseBottomBarBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.component.trade.block.utils.TradeButtonBuildUtils;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.exception.BottomBarFatalException;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.shopid.ShopIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.shopid.ShopIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sale.info.enums.ProductSaleStatusEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.FetcherDAGStarter;
import com.sankuai.dzshoppingguide.product.detail.application.utils.FreeDealUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.ProductBuyBarModule;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.enums.OpenTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.extend.redirect.SimpleRedirectActionVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.block.trade.StandardTradeBlockVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.scene.StandardTradeBottomBarVO;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.promo.FreeDealConfig;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.FreeDealEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;

import java.util.Optional;

import static com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.constant.ButtonStyleConstant.STANDARD_TRADE_BUTTON_STYLE;

/**
 * 0元预约底部按钮Builder，不可交易
 *
 * @Author: guangyujie
 * @Date: 2025/3/19
 */
@Slf4j
@Builder(
        builderType = BuilderTypeEnum.VARIABLE_BUILDER,
        moduleKey = ModuleKeyConstants.BOTTOM_BAR,
        startFetcher = FetcherDAGStarter.class,
        dependentFetchers = {
                ProductBaseInfoFetcher.class,
                ShopIdMapperFetcher.class
        }
)
public class CommonFreeDealReserveBottomBarBuilder extends BaseBottomBarBuilder {

    private ProductBaseInfo productBaseInfo;
    private long dpShopId;
    private long mtShopId;

    @Override
    public ProductBuyBarModule buildBuyBarModule() {
        ShopIdMapper shopIdMapper = getDependencyResult(ShopIdMapperFetcher.class);
        dpShopId = Optional.ofNullable(shopIdMapper).map(ShopIdMapper::getDpBestShopId).orElse(0L);
        mtShopId = Optional.ofNullable(shopIdMapper).map(ShopIdMapper::getMtBestShopId).orElse(0L);
        productBaseInfo = getDependencyResult(ProductBaseInfoFetcher.class);
        long categoryId = Optional.ofNullable(productBaseInfo)
                .map(ProductBaseInfo::getCategory)
                .map(DealGroupCategoryDTO::getCategoryId)
                .orElse(0L);
        FreeDealEnum freeDealEnum = FreeDealEnum.fromDealCategory(String.valueOf(categoryId));
        FreeDealConfig freeDealConfig = FreeDealUtils.getFreeDealConfig(freeDealEnum);
        if (freeDealConfig == null) {
            log.error("CommonFreeDealReserveBottomBarBuilder.getFreeDealConfig,request:{}", JSON.toJSONString(request), new BottomBarFatalException("查不到零元预约的配置，默认取教育配置"));
            freeDealConfig = FreeDealUtils.getFreeDealConfig(FreeDealEnum.EDU_TRIAL_BOOKING);
            if (freeDealConfig == null) {
                throw new BottomBarFatalException("FATAL ERROR!!!0元预约兜底配置失效，降级到静态兜底数据!!!");
            }
        }
        StandardTradeBottomBarVO standardTradeBottomBarVO = new StandardTradeBottomBarVO();
        standardTradeBottomBarVO.setRightBottomBar(StandardTradeBlockVO.buildSingleButtonStyle(
                TradeButtonBuildUtils.buildTradeButtonVO(
                        ProductSaleStatusEnum.ONLINE,
                        freeDealConfig.getButtonText(),
                        STANDARD_TRADE_BUTTON_STYLE,
                        new SimpleRedirectActionVO(OpenTypeEnum.redirect, getFreeDealUrl(freeDealConfig))
                )
        ));
        return ProductBuyBarModule.buildWithoutBanner(standardTradeBottomBarVO);
    }

    private String getFreeDealUrl(@NotNull final FreeDealConfig freeDealConfig) {
        boolean isMT = request.getClientTypeEnum().isMtClientType();
        boolean isInApp = request.getClientTypeEnum().isInApp();
        long shopId = isMT ? mtShopId : dpShopId;
        long dealGroupId = productBaseInfo.getProductIdDTO().getProductId(isMT);
        String schema = isMT ?
                (isInApp ? freeDealConfig.getMtAppSchema() : freeDealConfig.getMtH5Schema()) :
                (isInApp ? freeDealConfig.getDpAppSchema() : freeDealConfig.getDpH5Schema());
        String schemaAfter = schema.replace("{shopId}", String.valueOf(shopId));
        return schemaAfter.replace("{dealId}", String.valueOf(dealGroupId));
    }

    protected ProductBuyBarModule buildDefaultBuyBarModule() {
        StandardTradeBottomBarVO standardTradeBottomBarVO = new StandardTradeBottomBarVO();
        standardTradeBottomBarVO.setRightBottomBar(StandardTradeBlockVO.buildSingleButtonStyle(
                TradeButtonBuildUtils.buildTradeButtonVO(
                        ProductSaleStatusEnum.ONLINE,
                        "立即预约",
                        STANDARD_TRADE_BUTTON_STYLE,
                        new SimpleRedirectActionVO(OpenTypeEnum.redirect, getDefaultFreeDealUrl())
                )
        ));
        return ProductBuyBarModule.buildWithoutBanner(standardTradeBottomBarVO);
    }

    private String getDefaultFreeDealUrl() {
        boolean isMT = request.getClientTypeEnum().isMtClientType();
        boolean isInApp = request.getClientTypeEnum().isInApp();
        long shopId = isMT ? mtShopId : dpShopId;
        long dealGroupId = request.getProductId();
        String schema = isMT ?
                (isInApp ? "imeituan://www.meituan.com/mrn?mrn_biz=gc&mrn_entry=vg-mrn-reserve&mrn_component=customersubmit&mtShopId={shopId}&mtDealGroupId={dealId}" :
                        "/index/pages/h5/h5?f_token=1&noshare=1&weburl=https%3A%2F%2Fg.meituan.com%2Fvg-mrn-reserve%2Frn_gc_vg-mrn-reserve%2Findex.html%3Fmrn_component%3Dcustomersubmit%26mtShopId%3D{shopId}%26mtDealGroupId%3D{dealId}%26mc_app_source%3Dmeituan%26product%3Dmtwxapp"
                ) :
                (isInApp ? "dianping://mrn?mrn_biz=gc&mrn_entry=vg-mrn-reserve&mrn_component=customersubmit&dpShopId={shopId}&dpDealGroupId={dealId}" :
                        "/pages/webview/webview?url=https%3A%2F%2Fg.dianping.com%2Fvg-mrn-reserve%2Frn_gc_vg-mrn-reserve%2Findex.html%3Fmrn_component%3Dcustomersubmit%26dpShopId%3D{shopId}%26dpDealGroupId%3D{dealId}%26mc_app_source%3Ddianping%26product%3Ddianping-wxapp%26token%3D!"
                );
        String schemaAfter = schema.replace("{shopId}", String.valueOf(shopId));
        return schemaAfter.replace("{dealId}", String.valueOf(dealGroupId));
    }

}
