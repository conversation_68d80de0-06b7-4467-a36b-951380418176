package com.sankuai.dzshoppingguide.product.detail.application.builder.pricebar.component;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.sankuai.dealuser.price.display.api.enums.ExtendDisplayInfoKeyEnum;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import com.sankuai.dzshoppingguide.product.detail.application.builder.atmospherebar.builder.enums.PromoSceneEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.pricebar.builder.enums.CouponGuideTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.pricebar.config.ButtonTextConfig;
import com.sankuai.dzshoppingguide.product.detail.application.builder.pricebar.config.IconConfig;
import com.sankuai.dzshoppingguide.product.detail.application.builder.pricebar.config.ProductPriceConfig;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.abtest.AbTestReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.service.promo.PromoDetailService;
import com.sankuai.dzshoppingguide.product.detail.application.utils.LionConfigUtils;
import com.sankuai.dzshoppingguide.product.detail.application.utils.PriceHelper;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.InflateCouponTips;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.SuckBottomBanner;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.TextDisplayInfo;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.PromoTagEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.promo.vo.CouponItemInfoDTO;
import com.sankuai.nib.mkt.common.base.util.JsonUtils;
import com.sankuai.nibmkt.promotion.api.common.enums.PromotionPropertyEnum;
import com.sankuai.nibmkt.promotion.api.common.enums.magicalmember.MMCTooltipGuideTypeEnum;
import com.sankuai.nibmkt.promotion.api.common.enums.magicalmember.MMCTooltipsBreakEvenTypeEnum;
import com.sankuai.nibmkt.promotion.api.common.utils.MagicalMemberTagTextUtils;
import com.sankuai.nibmkt.promotion.api.query.model.magicalmember.response.MMCTooltipTextDTO;
import com.sankuai.nibmkt.promotion.api.query.model.magicalmember.response.MagicalMemberTagTextDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.sankuai.dzshoppingguide.product.detail.application.builder.atmospherebar.builder.enums.PromoSceneEnum.ONLY_DEAL_PROMO;

/**
 * <AUTHOR>
 * @date 2025/04/09
 */
@Slf4j
public class InflateCouponTipsComponent {

    /**
     * 引导膨胀、引导购买的实验结果
     */
    private static final List<String> GUIDE_INFLATE_AND_PURCHASE_EXP_RESULT = Lists.newArrayList("c", "d");

    /**
     * 引导膨胀、购买、使用实验结果
     */
    private static final List<String> GUIDE_INFLATE_PURCHASE_AND_USE_EXP_RESULT = Lists.newArrayList("c", "d");

    private static final List<String> MAGIC_COUPON_ENHANCE_EXP_RESULT = Lists.newArrayList( "d");

    /**
     * 构建神券提示条
     * @param dealPromoPriceDisplayDTO 促销价格展示DTO
     * @param finalPrice 最终价格
     * @return InflateCouponTips 神券提示条信息
     */
     public static InflateCouponTips buildInflateCouponTips(PriceDisplayDTO dealPromoPriceDisplayDTO, String finalPrice, String orderPageUrl, AbTestReturnValue abTestReturnValue) {
        InflateCouponTips inflateCouponTips = new InflateCouponTips();
        if (dealPromoPriceDisplayDTO == null) {
            return null;
        }
        Map<String, String> extendDisplayInfoMap = dealPromoPriceDisplayDTO.getExtendDisplayInfo();
        if(MapUtils.isEmpty(extendDisplayInfoMap)) {
            return null;
        }
        MagicalMemberTagTextDTO magicalMemberTagTextDTO = getMagicalMemberTagTextDTO(extendDisplayInfoMap);
        List<PromoDTO> usedPromos = dealPromoPriceDisplayDTO.getUsedPromos();
        PromoSceneEnum promoSceneEnum = getPromoScene(dealPromoPriceDisplayDTO);

        MMCTooltipTextDTO mmcTooltipTextDTO = getMMCTooltipTextDTO(extendDisplayInfoMap);
        CouponGuideTypeEnum guideTypeEnum = getCouponGuideTypeEnum(usedPromos, new BigDecimal(finalPrice), mmcTooltipTextDTO);

        //仅团购优惠 场景 且未命中 引导购买（神券强化二期逻辑）
        if (promoSceneEnum.getCode().equals(PromoSceneEnum.ONLY_DEAL_PROMO.getCode()) && ! Objects.equals(CouponGuideTypeEnum.GUIDE_PURCHASE,guideTypeEnum)) {
            return null;
        }
        // 实验卡控 撤出一期实验，用二期实验代替
        String expResult = abTestReturnValue.getAbTestExpResult("MtMagicalCouponStyleEnhancementV2");
        if (!isHitInflateCouponTipsExp(guideTypeEnum, expResult)) {
            return null;
        }
        IconConfig iconConfig = LionConfigUtils.getIconConfig();
        inflateCouponTips.setCouponGuideType(guideTypeEnum.getCode());
        inflateCouponTips.setButtonClickUrl(orderPageUrl);
        inflateCouponTips.setBackgroundImageUrl(iconConfig.getTipsBackgroundImageUrl());
        inflateCouponTips.setMagicCoupon(getMagicCoupon4Tips(usedPromos));
        Map<String, List<TextDisplayInfo>> textMap = getTextDisplayInfoConfig();
        switch(guideTypeEnum) {
            case GUIDE_INFLATE:
                // 设置引导膨胀券提示条的按钮点击URL为订单页面URL null
                inflateCouponTips.setButtonClickUrl("");
                handleGuideInflate(inflateCouponTips, mmcTooltipTextDTO, textMap, iconConfig, magicalMemberTagTextDTO, expResult);
                break;
            case GUIDE_TO_USE_INFLATE:
                String discountAmount = getDiscountAmount(usedPromos);
                handleGuideToUseInflate(inflateCouponTips, discountAmount, textMap);
                break;
            case GUIDE_PURCHASE:
                handleGuidePurchase(inflateCouponTips, mmcTooltipTextDTO, textMap, expResult);
                break;
            case NO_GUIDE:
                handleNoGuide(inflateCouponTips, mmcTooltipTextDTO, textMap, usedPromos, expResult);
                break;
            default:
                break;
        }
        return inflateCouponTips;
    }

    public static MagicalMemberTagTextDTO getMagicalMemberTagTextDTO(Map<String, String> extendDisplayInfoMap){
        //神会员标签文案
        String magicalMemberCouponLabel = extendDisplayInfoMap.get(ExtendDisplayInfoKeyEnum.MAGICAL_MEMBER_COUPON_LABEL.getKey());
        MagicalMemberTagTextDTO memberTagText = MagicalMemberTagTextUtils.bestMagicalMemberTagTextDTO(Lists.newArrayList(magicalMemberCouponLabel));
        if(memberTagText == null) {
            return null;
        }
        return memberTagText;
    }

    public static CouponGuideTypeEnum getCouponGuideTypeEnum(List<PromoDTO> usedPromos, BigDecimal price, MMCTooltipTextDTO mmcTooltipTextDTO) {
        if (hasMagicMemberCoupon(usedPromos)) {
            PromoDTO magicalMemberCoupon = usedPromos.stream()
                    .filter(promoDTO -> promoDTO.getPromotionExplanatoryTags() != null
                            && PromoTagEnum.MAGICAL_MEMBER_PLATFORM_COUPON.getType().equals(promoDTO.getIdentity().getPromoShowType()))
                    .findFirst()
                    .orElse(null);
            Map<String, String> promotionOtherInfoMap = magicalMemberCoupon.getPromotionOtherInfoMap();
            boolean afterInflate = Boolean.parseBoolean(promotionOtherInfoMap.get(PromotionPropertyEnum.AFTER_INFLATE.getValue()));
            if (afterInflate) {
                return CouponGuideTypeEnum.GUIDE_TO_USE_INFLATE;
            }
            if(Objects.nonNull(mmcTooltipTextDTO) && canInflate(magicalMemberCoupon)){
                return CouponGuideTypeEnum.GUIDE_INFLATE;
            }
            //判断是否满足引导使用
            List<ProductPriceConfig> priceConfigList = com.sankuai.dzshoppingguide.product.detail.application.utils.LionConfigUtils.getProductPriceConfig();
            for(ProductPriceConfig config : priceConfigList){
                if(isPriceInRange(price, new BigDecimal(config.getLowerLimit()), new BigDecimal(config.getUpperLimit()))
                        && magicalMemberCoupon.getAmount().compareTo(new BigDecimal(config.getCouponAmount())) >= 0){
                    return CouponGuideTypeEnum.GUIDE_TO_USE_INFLATE;
                }
            }
            return CouponGuideTypeEnum.NO_GUIDE;
        }
        if (Objects.isNull(mmcTooltipTextDTO)) {
            return CouponGuideTypeEnum.NO_GUIDE;
        }
        return mmcTooltipTextDTO.getGuideType() == MMCTooltipGuideTypeEnum.GUIDING_BUY.getCode()
                ? CouponGuideTypeEnum.GUIDE_PURCHASE : CouponGuideTypeEnum.NO_GUIDE;
    }

    /**
     * 获取优惠场景
     * @param promoPriceDisplayDTO
     * @return
     */
     public static PromoSceneEnum getPromoScene(PriceDisplayDTO promoPriceDisplayDTO) {
        List<PromoDTO> usedPromos = promoPriceDisplayDTO.getUsedPromos();

        if (org.apache.commons.collections4.CollectionUtils.isEmpty(usedPromos)) {
            return PromoSceneEnum.NO_PROMO;
        }

        // 是否仅存在团购优惠
        boolean isOnlyDealPromo = (usedPromos.size() == 1) && usedPromos.stream()
                .allMatch(promo -> promo != null
                        && promo.getIdentity() != null
                        && PromoTagEnum.DEAL_PROMO.getType().equals(promo.getIdentity().getPromoShowType()));
        if (isOnlyDealPromo) {
            return ONLY_DEAL_PROMO;
        }

        // 是否存在团购优惠
        boolean hasDealPromo = usedPromos.stream()
                .anyMatch(promo -> promo != null
                        && promo.getIdentity() != null
                        && PromoTagEnum.DEAL_PROMO.getType().equals(promo.getIdentity().getPromoShowType()));

        // 是否包含神券
        boolean hasMagicalCoupon = InflateCouponTipsComponent.hasMagicMemberCoupon(usedPromos);
        //除团购优惠以外的优惠类型，且不含神券时 || 没有团购优惠、没有神券优惠、有其他优惠时
        if ((hasDealPromo && !hasMagicalCoupon) || (!hasDealPromo && !hasMagicalCoupon && usedPromos.size() > 0)) {
            return PromoSceneEnum.NON_DEAL_PROMO_NO_MAGICAL_COUPON;
        }

        //有神券、领券或购券后能形成更优算价组合时
        if (hasMagicalCoupon || canFormBetterPriceCombo(promoPriceDisplayDTO)) {
            return PromoSceneEnum.HAS_MAGICAL_COUPON;
        }
        return ONLY_DEAL_PROMO;
    }

    /**
     * 购券后能形成更优算价组合
     * @param normalPriceDTO
     * @return
     */
    private static boolean canFormBetterPriceCombo(PriceDisplayDTO normalPriceDTO) {
        Map<String, String> extendDisplayInfoMap = normalPriceDTO.getExtendDisplayInfo();
        if(MapUtils.isEmpty(extendDisplayInfoMap)) {
            return false;
        }

        String mmcTooltipTextDTOStr = extendDisplayInfoMap.get(ExtendDisplayInfoKeyEnum.MMC_TOOLTIP_TEXT_DTO.getKey());
        if (org.apache.commons.lang3.StringUtils.isBlank(mmcTooltipTextDTOStr)) {
            return false;
        }
        MMCTooltipTextDTO mmcTooltipTextDTO = JsonUtils.parseObject(mmcTooltipTextDTOStr,MMCTooltipTextDTO.class);
        if(mmcTooltipTextDTO == null) {
            return false;
        }
        //购券形成更优，根据引导购买判断
        return MMCTooltipGuideTypeEnum.GUIDING_BUY.getCode() == mmcTooltipTextDTO.getGuideType();
    }

    private static boolean isHitInflateCouponTipsExp(CouponGuideTypeEnum guideTypeEnum, String expResult) {
        if (GUIDE_INFLATE_PURCHASE_AND_USE_EXP_RESULT.contains(expResult)) {
            return true;
        }
        return (guideTypeEnum == CouponGuideTypeEnum.GUIDE_INFLATE || guideTypeEnum == CouponGuideTypeEnum.GUIDE_PURCHASE)
                && GUIDE_INFLATE_AND_PURCHASE_EXP_RESULT.contains(expResult);
    }

    private static CouponItemInfoDTO getMagicCoupon4Tips(List<PromoDTO> usedPromos) {
        if (CollectionUtils.isEmpty(usedPromos)) {
            return null;
        }
        PromoDTO magicCouponPromoDTO = usedPromos.stream()
                .filter(promoDTO -> PromoTagEnum.MAGICAL_MEMBER_PLATFORM_COUPON.getType().equals(promoDTO.getIdentity().getPromoShowType()))
                .findFirst()
                .orElse(null);
        if (magicCouponPromoDTO == null) {
            return null;
        }
        return PromoDetailService.buildCouponItem(magicCouponPromoDTO, 0L);
    }

    /**
     * 判断最优算价组合内是否有神券
     * @param usedPromos
     * @return
     */
    public static boolean hasMagicMemberCoupon(List<PromoDTO> usedPromos) {
        if (CollectionUtils.isEmpty(usedPromos)) {
            return false;
        }
        return usedPromos.stream()
                .anyMatch(promo -> promo != null
                        && promo.getIdentity() != null
                        && PromoTagEnum.MAGICAL_MEMBER_PLATFORM_COUPON.getType().equals(promo.getIdentity().getPromoShowType()));
    }


    /**
     * 判断神券是否可膨胀
     * @param magicalMemberCoupon
     * @return
     */
    private static boolean canInflate(PromoDTO magicalMemberCoupon) {

        if (magicalMemberCoupon == null) {
            return false;
        }

        Map<String, String> promotionOtherInfoMap = magicalMemberCoupon.getPromotionOtherInfoMap();
        if (promotionOtherInfoMap == null) {
            return false;
        }

        return Boolean.parseBoolean(promotionOtherInfoMap.get(PromotionPropertyEnum.CAN_INFLATE.getValue()));
    }

    /**
     * 获取神券优惠金额
     * @param usedPromos 使用的优惠列表
     * @return 神券优惠金额,如果没有神券返回null
     */
    public static String getDiscountAmount(List<PromoDTO> usedPromos) {
        if (CollectionUtils.isEmpty(usedPromos)) {
            return null;
        }

        // 从优惠列表中找到神券
        return usedPromos.stream()
                .filter(promoDTO -> promoDTO != null
                        && promoDTO.getIdentity() != null
                        && PromoTagEnum.MAGICAL_MEMBER_PLATFORM_COUPON.getType()
                        .equals(promoDTO.getIdentity().getPromoShowType()))
                .findFirst()
                .map(PromoDTO::getAmount)
                .map(amount -> amount.toPlainString())
                .orElse(null);
    }

    /**
     * 判断最优算价组合内是否有神券是否可膨胀
     * @param usedPromos
     * @return
     */
    private static boolean canInflate(List<PromoDTO> usedPromos) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(usedPromos)) {
            return false;
        }
        PromoDTO magicalMemberCoupon = usedPromos.stream()
                .filter(promoDTO -> promoDTO.getPromotionExplanatoryTags() != null
                        && PromoTagEnum.MAGICAL_MEMBER_PLATFORM_COUPON.getType().equals(promoDTO.getIdentity().getPromoShowType()))
                .findFirst()
                .orElse(null);

        if (magicalMemberCoupon == null) {
            return false;
        }

        Map<String, String> promotionOtherInfoMap = magicalMemberCoupon.getPromotionOtherInfoMap();
        if (promotionOtherInfoMap == null) {
            return false;
        }

        return Boolean.parseBoolean(promotionOtherInfoMap.get(PromotionPropertyEnum.CAN_INFLATE.getValue()));
    }

    /**
     * 判断价格是否在指定区间内
     * @param price 待判断的价格
     * @param minPrice 最小价格（包含）
     * @param maxPrice 最大价格（不包含）
     * @return true-在区间内，false-不在区间内
     */
    private static boolean isPriceInRange(BigDecimal price, BigDecimal minPrice, BigDecimal maxPrice) {
        return Optional.ofNullable(price)
                .map(p -> (minPrice == null || p.compareTo(minPrice) >= 0)
                        && (maxPrice == null || p.compareTo(maxPrice) < 0))
                .orElse(false);
    }

    /**
     * 获取神会员提示条信息
     * @param extendDisplayInfoMap 扩展信息Map
     * @return MMCTooltipTextDTO 神会员提示条DTO
     */
    public static MMCTooltipTextDTO getMMCTooltipTextDTO(Map<String, String> extendDisplayInfoMap) {
        if (MapUtils.isEmpty(extendDisplayInfoMap)) {
            return null;
        }
        String mmcTooltipTextDTOStr = extendDisplayInfoMap.get(ExtendDisplayInfoKeyEnum.MMC_TOOLTIP_TEXT_DTO.getKey());
        return JsonUtils.parseObject(mmcTooltipTextDTOStr, MMCTooltipTextDTO.class);
    }

    /**
     * 处理引导膨胀场景
     * @param inflateCouponTips 神券提示条信息
     * @param mmcTooltipTextDTO 神会员提示条DTO
     * @param textMap 文案配置Map
     * @param iconConfig 图标配置
     */
    private static void handleGuideInflate(InflateCouponTips inflateCouponTips, MMCTooltipTextDTO mmcTooltipTextDTO,
                                    Map<String, List<TextDisplayInfo>> textMap, IconConfig iconConfig, MagicalMemberTagTextDTO magicalMemberTagTextDTO, String expResult) {
        if (mmcTooltipTextDTO == null || MMCTooltipGuideTypeEnum.GUIDING_INFLATE.getCode() != mmcTooltipTextDTO.getGuideType()) {
            return;
        }

        inflateCouponTips.setButtonText(ButtonTextConfig.FREE_INFLATE);
        inflateCouponTips.setButtonTextList(Lists.newArrayList(ButtonTextConfig.FREE_INFLATE));
        if (MAGIC_COUPON_ENHANCE_EXP_RESULT.contains(expResult)) {
            // 构造 引导轮播 文案
            inflateCouponTips.setButtonTextList(buildButtonTextList(magicalMemberTagTextDTO));
        }
        inflateCouponTips.setButtonIcon(iconConfig.getFreeInflateArrow());


        List<TextDisplayInfo> tempTextDisplayInfos = textMap.get(CouponGuideTypeEnum.GUIDE_INFLATE.getCode());
        Map<String, String> textReplacementMap = new HashMap<String, String>() {{
            put("x", mmcTooltipTextDTO.getPreInflateDiscountAmount());
            put("y", mmcTooltipTextDTO.getMaxInflateDiscountAmount());
        }};

        List<TextDisplayInfo> newTextDisplayInfos = updateTextDisplayInfos(tempTextDisplayInfos, textReplacementMap);
        inflateCouponTips.setText(newTextDisplayInfos);
        inflateCouponTips.setSuckBottomBanner(builderSuckBottomBanner(ButtonTextConfig.GO_TO_INFLATE, newTextDisplayInfos));
    }

    /**
     * 构造引导膨胀按钮 轮播文案
     * @param magicalMemberTagTextDTO
     * @return
     */
    public static List<String> buildButtonTextList(MagicalMemberTagTextDTO magicalMemberTagTextDTO) {
        if (Objects.nonNull(magicalMemberTagTextDTO)) {
            List<String> buttonTextList = new ArrayList<>();
            buttonTextList.add(String.format(ButtonTextConfig.MAX_REDUCE_MONEY, magicalMemberTagTextDTO.getReduceMoney()));
            buttonTextList.add(ButtonTextConfig.GO_TO_INFLATE);
            return buttonTextList;
        }
        return null;
    }

    /**
     * 处理引导使用场景
     * @param inflateCouponTips 神券提示条信息
     * @param discountAmount 折扣金额
     * @param textMap 文案配置Map
     */
    private static void handleGuideToUseInflate(InflateCouponTips inflateCouponTips, String discountAmount,
                                         Map<String, List<TextDisplayInfo>> textMap) {
        inflateCouponTips.setButtonText(ButtonTextConfig.USE_NOW);
        inflateCouponTips.setButtonTextList(Lists.newArrayList(ButtonTextConfig.USE_NOW));
        List<TextDisplayInfo> tempTextDisplayInfos = textMap.get(CouponGuideTypeEnum.GUIDE_TO_USE_INFLATE.getCode());
        Map<String, String> textReplacementMap = new HashMap<String, String>() {{
            put("x", discountAmount);
        }};

        List<TextDisplayInfo> newTextDisplayInfos = updateTextDisplayInfos(tempTextDisplayInfos, textReplacementMap);
        inflateCouponTips.setText(newTextDisplayInfos);
        inflateCouponTips.setSuckBottomBanner(builderSuckBottomBanner("", newTextDisplayInfos));
    }

    /**
     * 构造按钮轮播文案
     * @param canInflate 是否能膨胀
     * @param mmcTooltipTextDTO 用于 是否一单回本
     * @return
     */
    private static List<String> buildButtonTextList (MMCTooltipTextDTO mmcTooltipTextDTO, boolean canInflate) {
        if (MMCTooltipsBreakEvenTypeEnum.ONE_RETURN.getCode() == mmcTooltipTextDTO.getBreakEvenType()) {
            // 一单回本
            // 可膨胀
            if (canInflate) {
                List<String> buttonTextList = new ArrayList<>();
                buttonTextList.add(ButtonTextConfig.STILL_INFLATABLE);
                buttonTextList.add(ButtonTextConfig.PURCHASE_MAGIC_COUPON_PACKAGE);
                return buttonTextList;
            } else {
                // 不可膨胀
                List<String> buttonTextList = new ArrayList<>();
                String preInflateDiscountAmount = mmcTooltipTextDTO.getPreInflateDiscountAmount();
                //  元神券提示条的立减价格
                buttonTextList.add(String.format(ButtonTextConfig.INSTANCE_REDUCE, preInflateDiscountAmount));
                buttonTextList.add(ButtonTextConfig.PURCHASE_MAGIC_COUPON_PACKAGE);
                return buttonTextList;
            }

        }else {
            // 不可一单回本
            // 可膨胀
            if (canInflate) {
                List<String> buttonTextList = new ArrayList<>();
                buttonTextList.add(ButtonTextConfig.ONE_RETURN);
                buttonTextList.add(ButtonTextConfig.PURCHASE_MAGIC_COUPON_PACKAGE);
                return buttonTextList;
            } else {
                // 不可膨胀
                List<String> buttonTextList = new ArrayList<>();
                String preInflateDiscountAmount = mmcTooltipTextDTO.getPreInflateDiscountAmount();

                // 元神券提示条的立减价格
                buttonTextList.add(String.format(ButtonTextConfig.INSTANCE_REDUCE, preInflateDiscountAmount));
                buttonTextList.add(ButtonTextConfig.ONE_RETURN);
                buttonTextList.add(ButtonTextConfig.PURCHASE_MAGIC_COUPON_PACKAGE);
                return buttonTextList;
            }
        }

    }

    /**
     * 处理引导购买场景
     * @param inflateCouponTips 神券提示条信息
     * @param mmcTooltipTextDTO 神会员提示条DTO
     * @param textMap 文案配置Map
     */
    private static void handleGuidePurchase(InflateCouponTips inflateCouponTips, MMCTooltipTextDTO mmcTooltipTextDTO,
                                     Map<String, List<TextDisplayInfo>> textMap, String expResult) {
        if (mmcTooltipTextDTO == null || MMCTooltipGuideTypeEnum.GUIDING_BUY.getCode() != mmcTooltipTextDTO.getGuideType()) {
            return;
        }

        inflateCouponTips.setButtonText(ButtonTextConfig.BUY_NOW);
        inflateCouponTips.setButtonTextList(Lists.newArrayList(ButtonTextConfig.BUY_NOW));
        if ( mmcTooltipTextDTO.isGuideBuyCanInflate()) {
            handleBuyAfterCanInflate(inflateCouponTips, mmcTooltipTextDTO, textMap, expResult);
        } else if (!mmcTooltipTextDTO.isGuideBuyCanInflate()) {
            handleBuyAfterNotInflate(inflateCouponTips, mmcTooltipTextDTO, textMap, expResult);
        }
    }

    /**
     * 处理购买后可膨胀场景
     * @param inflateCouponTips 神券提示条信息
     * @param mmcTooltipTextDTO 神会员提示条DTO
     * @param textMap 文案配置Map
     */
    private static void handleBuyAfterCanInflate(InflateCouponTips inflateCouponTips, MMCTooltipTextDTO mmcTooltipTextDTO,
                                          Map<String, List<TextDisplayInfo>> textMap, String expResult) {
        List<TextDisplayInfo> tempTextDisplayInfos = textMap.get(CouponGuideTypeEnum.BUY_AFTER_CAN_INFLATE.getCode());
        addOneReturnTextIfNeeded(tempTextDisplayInfos, mmcTooltipTextDTO);
        inflateCouponTips.setText(tempTextDisplayInfos);
        List<TextDisplayInfo> suckBottomTextDisplayInfos = textMap.get(CouponGuideTypeEnum.BUY_AFTER_CAN_INFLATE_SUCK_BOTTOM.getCode());
        addOneReturnTextIfNeeded(suckBottomTextDisplayInfos, mmcTooltipTextDTO);
        inflateCouponTips.setSuckBottomBanner(builderSuckBottomBanner("", suckBottomTextDisplayInfos));

        // 可膨胀 按钮轮播文案
        if (MAGIC_COUPON_ENHANCE_EXP_RESULT.contains(expResult)) {
            inflateCouponTips.setButtonTextList(buildButtonTextList(mmcTooltipTextDTO,true));
        }
    }

    /**
     * 处理购买后不可膨胀场景
     * @param inflateCouponTips 神券提示条信息
     * @param mmcTooltipTextDTO 神会员提示条DTO
     * @param textMap 文案配置Map
     */
    private static void handleBuyAfterNotInflate(InflateCouponTips inflateCouponTips, MMCTooltipTextDTO mmcTooltipTextDTO,
                                          Map<String, List<TextDisplayInfo>> textMap, String expResult) {
        String textMapKey = MMCTooltipsBreakEvenTypeEnum.ONE_RETURN.getCode() == mmcTooltipTextDTO.getBreakEvenType() ?
                CouponGuideTypeEnum.BUY_AFTER_NOT_INFLATE_ADD_ONE_RETURN.getCode() : CouponGuideTypeEnum.BUY_AFTER_NOT_INFLATE.getCode();
        List<TextDisplayInfo> tempTextDisplayInfos = textMap.get(textMapKey);
        Map<String, String> textReplacementMap = new HashMap<String, String>() {{
            put("x", mmcTooltipTextDTO.getPreInflateDiscountAmount());
        }};

        List<TextDisplayInfo> newTextDisplayInfos = updateTextDisplayInfos(tempTextDisplayInfos, textReplacementMap);
        addOneReturnTextIfNeeded(newTextDisplayInfos, mmcTooltipTextDTO);
        inflateCouponTips.setText(newTextDisplayInfos);
        List<TextDisplayInfo> suckBottomTextDisplayInfos = textMap.get(CouponGuideTypeEnum.BUY_AFTER_NOT_INFLATE_SUCK_BOTTOM.getCode());
        List<TextDisplayInfo> newSuckBottomTextDisplayInfos = updateTextDisplayInfos(suckBottomTextDisplayInfos, textReplacementMap);
        addOneReturnTextIfNeeded(newSuckBottomTextDisplayInfos, mmcTooltipTextDTO);
        inflateCouponTips.setSuckBottomBanner(builderSuckBottomBanner("", newSuckBottomTextDisplayInfos));

        // 不可膨胀，按钮轮播文案
        if (MAGIC_COUPON_ENHANCE_EXP_RESULT.contains(expResult)) {
            inflateCouponTips.setButtonTextList(buildButtonTextList(mmcTooltipTextDTO, false));
        }
    }

    private static void handleNoGuide(InflateCouponTips inflateCouponTips, MMCTooltipTextDTO mmcTooltipTextDTO,
                                            Map<String, List<TextDisplayInfo>> textMap,List<PromoDTO> usedPromos, String expResult) {
        // 神券强化感知二期
        if (!GUIDE_INFLATE_PURCHASE_AND_USE_EXP_RESULT.contains(expResult)) {
            return;
        }
        if (CollectionUtils.isEmpty(usedPromos)) {
            return;
        }
        PromoDTO magicPromDTO = usedPromos.stream().filter(promoDTO->{  return PromoTagEnum.MAGICAL_MEMBER_PLATFORM_COUPON.getType().equals(promoDTO.getIdentity().getPromoShowType());}).findFirst().orElse(null);
        if (magicPromDTO == null) {
            return;
        }
        List<TextDisplayInfo> tempTextDisplayInfos = textMap.get(CouponGuideTypeEnum.GUIDE_TO_USE_INFLATE.getCode());
        Map<String, String> textReplacementMap = new HashMap<String, String>() {{
            put("x", PriceHelper.dropLastZero(magicPromDTO.getAmount()));
        }};
        List<TextDisplayInfo> newTextDisplayInfos = updateTextDisplayInfos(tempTextDisplayInfos, textReplacementMap);
        // 神券吸底条
        inflateCouponTips.setSuckBottomBanner(builderSuckBottomBanner("", newTextDisplayInfos));
    }

    /**
     * 添加一返文案（如果需要）
     * @param textDisplayInfos 文案列表
     * @param mmcTooltipTextDTO 神会员提示条DTO
     */
    private static void addOneReturnTextIfNeeded(List<TextDisplayInfo> textDisplayInfos, MMCTooltipTextDTO mmcTooltipTextDTO) {
        if (MMCTooltipsBreakEvenTypeEnum.ONE_RETURN.getCode() == mmcTooltipTextDTO.getBreakEvenType()) {
            TextDisplayInfo oneReturnText = new TextDisplayInfo();
            oneReturnText.setText(CouponGuideTypeEnum.ONE_RETURN.getDesc());
            oneReturnText.setTextColor("#FF6633");
            textDisplayInfos.add(oneReturnText);
        }
    }


    /**
     * 设置吸底banner
     * @param buttonText 按钮文案
     * @param textDisplayInfos 文案列表
     */
    private static SuckBottomBanner builderSuckBottomBanner(String buttonText, List<TextDisplayInfo> textDisplayInfos) {
        SuckBottomBanner suckBottomBanner = new SuckBottomBanner();
        IconConfig iconConfig = LionConfigUtils.getIconConfig();
        suckBottomBanner.setPaybackIcon(iconConfig.getSuckBottomBannerIcon());
        suckBottomBanner.setButtonText(buttonText);
        suckBottomBanner.setText(textDisplayInfos);
        return suckBottomBanner;
    }


    /**
     * 更新配置文案模版
     * @param textDisplayInfos
     * @param textReplacementMap
     * @return
     */
    private static List<TextDisplayInfo> updateTextDisplayInfos(List<TextDisplayInfo> textDisplayInfos,Map<String, String> textReplacementMap) {
        if (CollectionUtils.isEmpty(textDisplayInfos)) {
            return Collections.emptyList();
        }
        return textDisplayInfos.stream()
                .map(info -> {
                    if (info == null || StringUtils.isBlank(info.getText())) {
                        return info;
                    }

                    TextDisplayInfo newInfo = new TextDisplayInfo();
                    BeanUtils.copyProperties(info, newInfo);

                    String replacement = textReplacementMap.get(info.getText());
                    if (StringUtils.isNotBlank(replacement)) {
                        newInfo.setText(replacement);
                    }

                    return newInfo;
                })
                .collect(Collectors.toList());
    }

    /**
     * 获取配置文案
     * @return
     */
    private static Map<String, List<TextDisplayInfo>> getTextDisplayInfoConfig() {
        try {
            Map<String, List> rawMap = LionConfigUtils.getTextDisplayInfoConfig();
            if (MapUtils.isEmpty(rawMap)) {
                return Collections.emptyMap();
            }

            return rawMap.entrySet()
                    .stream()
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            entry -> convertToTextDisplayInfoList(entry.getValue()),
                            (v1, v2) -> v1,
                            HashMap::new
                    ));
        } catch (Exception e) {
            log.error("getTextDisplayInfoConfig error", e);
            return Collections.emptyMap();
        }
    }

    /**
     * 将 List 转换为 List<TextDisplayInfo>
     */
    @SuppressWarnings("unchecked")
    private static List<TextDisplayInfo> convertToTextDisplayInfoList(List rawList) {
        if (CollectionUtils.isEmpty(rawList)) {
            return Collections.emptyList();
        }
        return (List<TextDisplayInfo>) rawList.stream()
                .map(item -> {
                    try {
                        String jsonString = JSONObject.toJSONString(item);
                        return JSONObject.parseObject(jsonString, TextDisplayInfo.class);
                    } catch (Exception e) {
                        log.error("Convert to TextDisplayInfo failed, raw data: {}", item, e);
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
