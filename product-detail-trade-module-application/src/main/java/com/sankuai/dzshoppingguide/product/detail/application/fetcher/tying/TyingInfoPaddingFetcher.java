package com.sankuai.dzshoppingguide.product.detail.application.fetcher.tying;

import com.dianping.deal.sales.common.datatype.ProductParam;
import com.dianping.deal.sales.common.datatype.SalesDisplayDTO;
import com.dianping.deal.sales.common.datatype.SalesDisplayRequest;
import com.dianping.deal.sales.common.enums.SalesPlatform;
import com.dianping.vc.enums.VCClientTypeEnum;
import com.dianping.vc.enums.VCPlatformEnum;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.dealuser.price.display.api.enums.*;
import com.sankuai.dealuser.price.display.api.model.*;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dzshoppingguide.product.detail.application.constants.SaleConstants;
import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
import com.sankuai.dzshoppingguide.product.detail.application.utils.ClientMappingUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.BindingSourceEnum;
import com.sankuai.dztheme.deal.dto.DealProductDTO;
import com.sankuai.dztheme.deal.req.DealProductRequest;
import com.sankuai.dztheme.deal.res.DealProductResult;
import com.sankuai.general.product.query.center.client.builder.model.*;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 搭售信息填充（基础信息、销量、价格、副标题）
 */
@Fetcher(previousLayerDependencies = {
        BlackListFilterFetcher.class,
})
@Slf4j
public class TyingInfoPaddingFetcher extends NormalFetcherContext<TyingPaddingResult> {

    @Autowired
    private CompositeAtomService compositeAtomService;

    private TyingIdResult tyingIdResult;

    private static final String DZ_DEAL_SUB_TITLE_PLAN_ID = "10002434";

    @Override
    protected CompletableFuture<TyingPaddingResult> doFetch() throws Exception {
        tyingIdResult = getDependencyResult(BlackListFilterFetcher.class);
        if (tyingIdResult == null || CollectionUtils.isEmpty(tyingIdResult.getCombinationDealInfoList())) {
            return CompletableFuture.completedFuture(null);
        }

        CompletableFuture<Map<ProductParam, SalesDisplayDTO>> salesCF = compositeAtomService.multiGetSales(buildSalesDisplayRequest());

        CompletableFuture<QueryDealGroupListResponse> dealResultCF = compositeAtomService.queryByDealGroupIds(buildQueryByDealGroupIdRequest());

        CompletableFuture<DealProductResult> subtitleCf = compositeAtomService.queryDealSubtitle(buildDealProductRequest());

        CompletableFuture<PriceResponse<Map<Long, List<PriceDisplayDTO>>>> priceCf = compositeAtomService.batchQueryPriceWithResponse(buildBatchPriceRequest());

        return CompletableFuture.allOf(salesCF, dealResultCF, subtitleCf, priceCf).thenApply(v -> toTyingPaddingResult(salesCF.join(), dealResultCF.join(), subtitleCf.join(), priceCf.join()));
    }

    private TyingPaddingResult toTyingPaddingResult(Map<ProductParam, SalesDisplayDTO> salesDisplayDTOMap, QueryDealGroupListResponse queryDealGroupListResponse, DealProductResult subtitleResult, PriceResponse<Map<Long, List<PriceDisplayDTO>>> priceResponse) {
        TyingPaddingResult tyingPaddingResult = new TyingPaddingResult();
        tyingPaddingResult.setMainDealGroupId(tyingIdResult.getMainDealGroupId());
        tyingPaddingResult.setCombinationDealInfoList(tyingIdResult.getCombinationDealInfoList());
        tyingPaddingResult.setDealIdPriceDisplayMap(getDealIdPriceDisplayMap(priceResponse));
        tyingPaddingResult.setPriceSecretInfo(Optional.ofNullable(priceResponse).map(PriceResponse::getPriceSecretInfo).orElse(""));
        tyingPaddingResult.setProductId2SubTitleMap(getProductId2SubTitleMap(subtitleResult));
        tyingPaddingResult.setDealIdDealGroupDTOMap(getDealIdDealGroupDTOMap(queryDealGroupListResponse));
        tyingPaddingResult.setProductId2SaleMap(getProductId2SaleMap(salesDisplayDTOMap));
        PriceDisplayDTO mainPriceDisplayDTO = Optional.ofNullable(tyingPaddingResult.getDealIdPriceDisplayMap()).map(m -> m.get(tyingIdResult.getMainDealGroupId())).orElse(null);
        tyingPaddingResult.setBindingDealCombinationInfoMap(buildBindingDealCombinationInfoMap(mainPriceDisplayDTO));
        return tyingPaddingResult;
    }

    private Map<Integer, PriceDisplayDTO> getDealIdPriceDisplayMap(PriceResponse<Map<Long, List<PriceDisplayDTO>>> priceResponse) {
        if (priceResponse == null || !priceResponse.isSuccess()) {
            return Maps.newHashMap();
        }
        return priceResponse.getData().values().stream().flatMap(Collection::stream).distinct()
                .collect(Collectors.toMap(product -> product.getIdentity().getProductId(), productIdentity -> productIdentity, (x, y) -> x));
    }

    private Map<Integer, List<String>> getProductId2SubTitleMap(DealProductResult dealProductResult) {
        if (dealProductResult == null || CollectionUtils.isEmpty(dealProductResult.getDeals())) {
            return Maps.newHashMap();
        }
        return dealProductResult.getDeals().stream()
                .collect(Collectors.toMap(DealProductDTO::getProductId, DealProductDTO::getProductTags));
    }

    private Map<Integer, DealGroupDTO> getDealIdDealGroupDTOMap(QueryDealGroupListResponse queryDealGroupListResponse) {
        if (queryDealGroupListResponse == null ||  queryDealGroupListResponse.getData() == null || CollectionUtils.isEmpty(queryDealGroupListResponse.getData().getList())) {
            return Maps.newHashMap();
        }
        return queryDealGroupListResponse.getData().getList().stream().collect(Collectors.toMap(dealGroupDTO -> request.getClientTypeEnum().isMtClientType() ? dealGroupDTO.getMtDealGroupId().intValue() : dealGroupDTO.getDpDealGroupId().intValue(), Function.identity(), (o1, o2) -> o1));
    }

    private Map<Integer, SalesDisplayDTO> getProductId2SaleMap(Map<ProductParam, SalesDisplayDTO> salesDisplayDTOMap) {
        if (MapUtils.isEmpty(salesDisplayDTOMap)) {
            return Maps.newHashMap();
        }
        Map<Integer, SalesDisplayDTO> productId2SaleMap = new HashMap<>();
        for (Map.Entry<ProductParam, SalesDisplayDTO> entry : salesDisplayDTOMap.entrySet()) {
            productId2SaleMap.put((int) entry.getKey().getProductGroupId(), entry.getValue());
        }
        return productId2SaleMap;
    }
    private Map<Integer, CombinationDealInfo> buildBindingDealCombinationInfoMap(PriceDisplayDTO priceDisplayDTO) {
        Map<Integer, CombinationDealInfo> bindingDealCombinationInfoMap = tyingIdResult.getCombinationDealInfoList().stream().collect(Collectors.toMap(CombinationDealInfo::getBindingDealId, Function.identity(),(e1,e2)->e1));
        buildDealCombinationInfoPrice(priceDisplayDTO, bindingDealCombinationInfoMap);
        return bindingDealCombinationInfoMap;
    }

    public void buildDealCombinationInfoPrice(PriceDisplayDTO priceDisplayDTO, Map<Integer, CombinationDealInfo> bindingDealCombinationInfoMap){
        if (Objects.nonNull(priceDisplayDTO)) {
            org.apache.commons.collections4.CollectionUtils.emptyIfNull(priceDisplayDTO.getExtPrices()).forEach(extPriceDisplayDTO -> {
                if (extPriceDisplayDTO.getExtPriceType() != ExtPriceTypeEnum.Tying_Sale.getType()) {
                    return;
                }
                TyingSaleDTO tyingSaleDTO = org.apache.commons.collections4.CollectionUtils.emptyIfNull(extPriceDisplayDTO.getTyingSaleDTOS()).stream().filter(t -> TyingSaleTypeEnum.DEAL_GROUP.getType() == t.getTyingSaleType()).collect(Collectors.toList()).stream().findFirst().orElse(new TyingSaleDTO());
                int bindingDealId = (int) tyingSaleDTO.getTyingSaleProductId();
                if (bindingDealId == 0 || !bindingDealCombinationInfoMap.containsKey(bindingDealId)) {
                    return;
                }
                bindingDealCombinationInfoMap.get(bindingDealId).setExtPrice(extPriceDisplayDTO.getExtPrice());
                bindingDealCombinationInfoMap.get(bindingDealId).setExtPricePromoAmount(extPriceDisplayDTO.getExtPricePromoAmount());
                bindingDealCombinationInfoMap.get(bindingDealId).setTyingSaleSkuId(tyingSaleDTO.getTyingSaleSkuId());
            });
        }
    }


    private BatchPriceRequest buildBatchPriceRequest() {
        List<CombinationDealInfo> combinationDealInfos = tyingIdResult.getCombinationDealInfoList();
        BatchPriceRequest priceRequest = new BatchPriceRequest();
        ClientEnv clientEnv = new ClientEnv();
        clientEnv.setClientType(ClientMappingUtils.getBpClientType(request).getType());
        clientEnv.setUnionId(request.getShepherdGatewayParam().getUnionid());
        clientEnv.setVersion(request.getShepherdGatewayParam().getAppVersion());
        clientEnv.setCityId(request.getCityId());
        clientEnv.setUuid(request.getShepherdGatewayParam().getDeviceId());
        clientEnv.setLatitude(request.getUserLat());
        clientEnv.setLongitude(request.getUserLng());
        priceRequest.setClientEnv(clientEnv);
        priceRequest.setScene(RequestSceneEnum.TYING_SALE_WithDealPromo.getScene());
        priceRequest.setUserId(request.getUserId());//已确认判断平台后再使用
        Map<Long, List<ProductIdentity>> shopId2ProductIds = new HashMap<>();
        priceRequest.setLongShopId2ProductIds(shopId2ProductIds);

        ProductIdentity identity = new ProductIdentity(combinationDealInfos.get(0).getMainDealId(), ProductTypeEnum.DEAL.getType());
        List<TyingSaleDTO> tyingSaleDTOs = Lists.newArrayList();
        combinationDealInfos.forEach(combinationDealInfo -> {
            TyingSaleDTO tyingSaleDTO = new TyingSaleDTO();
            tyingSaleDTO.setTyingSaleProductId(combinationDealInfo.getBindingDealId());
            tyingSaleDTO.setTyingSaleShopId(request.getPoiId());
            tyingSaleDTO.setTyingSaleType(TyingSaleTypeEnum.DEAL_GROUP.getType());
            // 人工组品才需要传入，tyingId要求long型
            if (combinationDealInfo.getBindingSource() == BindingSourceEnum.MANUAL.getValue()) {
                tyingSaleDTO.setTyingId(String.valueOf(combinationDealInfo.getItemId()));
            } else {
                tyingSaleDTO.setTyingId("0");
            }
            tyingSaleDTOs.add(tyingSaleDTO);
        });
        identity.setTyingSaleDTOs(tyingSaleDTOs);
        shopId2ProductIds.put(request.getPoiId(), Lists.newArrayList(identity));
        Map<String, String> extMap = Maps.newHashMap();
        extMap.put(ExtensionKeyEnum.PageName.getDesc(), PageNameEnum.DealGroupNormalDetailCombTyingSale.getCode());
        extMap.put(ExtensionKeyEnum.PageSource.getDesc(), String.valueOf(PageSourceEnum.dealDetailPage.getType()));
        priceRequest.setExtension(extMap);

        priceRequest.setUserId(request.getUserId());//已确认判断平台后再使用
        return priceRequest;
    }

    private SalesDisplayRequest buildSalesDisplayRequest() {
        List<ProductParam> productParamList = tyingIdResult.getAllDealGroupIds().stream()
                .map(dealGroupId -> ProductParam.productScene(dealGroupId, request.getCityId()))
                .collect(Collectors.toList());
        SalesDisplayRequest multiRequest = SalesDisplayRequest.multiQuery(productParamList);
        multiRequest.setPlatform(request.getClientTypeEnum().isMtClientType() ? SalesPlatform.MT.getValue() : SalesPlatform.DP.getValue());
        Map<String, String> extra = Maps.newHashMap();
        //透传销量区间差异化，反爬标识
        extra.put(SaleConstants.MTSI_FLAG, request.getShepherdGatewayParam().getMtsiflag());
        extra.put(SaleConstants.CONFUSION_FLAG, SaleConstants.GENERAL_SECTION);
        multiRequest.setExtra(extra);
        return multiRequest;
    }

    private QueryByDealGroupIdRequest buildQueryByDealGroupIdRequest() {
        Set<Long> dealGroupIdSet = tyingIdResult.getAllDealGroupIds().stream().map(Integer::longValue).collect(Collectors.toSet());
        return QueryByDealGroupIdRequestBuilder.builder()
                .dealGroupIds(dealGroupIdSet, request.getClientTypeEnum().isMtClientType() ? IdTypeEnum.MT : IdTypeEnum.DP)
                .basicInfo(DealGroupBasicInfoBuilder.builder().all())
                .image(DealGroupImageBuilder.builder().all())
                .category(DealGroupCategoryBuilder.builder().all())
                .dealGroupStock(DealGroupStockBuilder.builder().all())
                .region(DealGroupRegionBuilder.builder().all())
                .build();
    }

    private DealProductRequest buildDealProductRequest() {
        DealProductRequest dealProductRequest = new DealProductRequest();
        dealProductRequest.setProductIds(tyingIdResult.getBindingDealGroupIds());
        dealProductRequest.setPlanId(DZ_DEAL_SUB_TITLE_PLAN_ID);
        // 扩展参数
        Map<String, Object> extParams = Maps.newHashMap();
        extParams.put("dealIds", tyingIdResult.getBindingDealGroupIds());
        extParams.put("clientType", request.getClientTypeEnum().isMtClientType() ? VCClientTypeEnum.MT_APP.getCode() : VCClientTypeEnum.DP_APP.getCode());
        extParams.put("platform", request.getClientTypeEnum().isMtClientType() ? VCPlatformEnum.MT.getType() : VCPlatformEnum.DP.getType());
        extParams.put("deviceId", request.getShepherdGatewayParam().getDeviceId());
        extParams.put("unionId", request.getShepherdGatewayParam().getUnionid());
        extParams.put("shopIdForLong", request.getPoiId());
        dealProductRequest.setExtParams(extParams);
        return dealProductRequest;
    }
}
