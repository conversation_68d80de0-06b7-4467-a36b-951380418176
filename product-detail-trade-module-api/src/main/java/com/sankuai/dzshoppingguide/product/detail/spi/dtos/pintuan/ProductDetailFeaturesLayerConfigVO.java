package com.sankuai.dzshoppingguide.product.detail.spi.dtos.pintuan;

import com.dianping.mobile.framework.annotation.MobileDo;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2025/3/13
 */
@MobileDo(id = 0x8885)
public class ProductDetailFeaturesLayerConfigVO implements Serializable {
    /**
     * 商品详情页浮层模块
     */
    @MobileDo.MobileField(key = 0x479f)
    private List<ProductDetailLayerItemConfig> layerConfigs;

    /**
     * 标题
     */
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    public List<ProductDetailLayerItemConfig> getLayerConfigs() {
        return layerConfigs;
    }

    public void setLayerConfigs(List<ProductDetailLayerItemConfig> layerConfigs) {
        this.layerConfigs = layerConfigs;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }
}
