package com.sankuai.dzshoppingguide.product.detail.application.fetcher.coupon.magic;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import com.sankuai.dzshoppingguide.product.detail.application.builder.pricebar.builder.enums.CouponGuideTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2025-04-24
 * @desc
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MagicCouponInfo extends FetcherReturnValueDTO {
    /**
     * 神券引导状态
     */
    private CouponGuideTypeEnum couponGuideTypeEnum;
}
