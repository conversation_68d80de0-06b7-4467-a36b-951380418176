package com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommendgoods;

import com.dianping.martgeneral.recommend.api.dto.RecommendDTO;
import com.dianping.martgeneral.recommend.api.entity.RecommendParameters;
import com.dianping.martgeneral.recommend.api.entity.RecommendResult;
import com.dianping.martgeneral.recommend.api.enums.PlatformEnum;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dz.product.detail.RequestCustomParamEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommendgoods.dtos.ProductM;
import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.math.NumberUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @create 2025/5/21 14:33
 */
@Fetcher(
        previousLayerDependencies = {
                ProductBaseInfoFetcher.class
        }
)
@Slf4j
public abstract class AbstractProductRecommendFetcher extends NormalFetcherContext<ProductRecommendResult> {

    private static final int DEFAULT_PAGE_NUM = 1;
    private static final int PAGE_SIZE = 10;

    private static final int ROLE_PLAY_SPU_RECOMMEND_BIZ = 114;

    private static final List<Integer> PIN_POOL_SHOP_RECOMMEND_BIZ = Lists.newArrayList(117);

    private static final List<Integer> POOL_GROUP_BIZ_IDS  = Lists.newArrayList(116, 162, 163, 166, 201);

    private static final int SPU_DEAL_SHOP_RECOMMEND_BIZ_ID = 406;
    private static final int CAR_SPU_DEAL_SHOP_RECOMMEND_BIZ_ID = 491;
    private static final int ACTIVE_ROLE_PLAY_CITY_PIN_ID = 144;

    private static final int MEDICAL_BRAND_PRODUCT_RECOMMEND = 108;

    private static final int BACKROOM_PRODUCT_RECOMMEND = 201;

    public static final int BIJIA_PRODUCT_RECOMMEND = 514;

    abstract Map<String, Object> buildBizParams();
    abstract int getBizId();
    abstract int getMinQueryNum();

    @Resource
    private CompositeAtomService atomService;

    @Override
    protected CompletableFuture<ProductRecommendResult> doFetch() throws Exception {
        RecommendParameters recommendParameters = buildRecommendParameters();
        CompletableFuture<RecommendResult<RecommendDTO>> recommendResultCompletableFuture = atomService.getRecommendResult(recommendParameters);
        return recommendResultCompletableFuture.thenApply(result -> {
            if (Objects.nonNull(result)) {
                return buildProducts(result);
            }
            return null;
        });
    }

    public RecommendParameters buildRecommendParameters(){
        RecommendParameters parameters = new RecommendParameters();
        parameters.setBizId(getBizId());
        parameters.setCityId(request.getCityId());
        parameters.setPlatformEnum(request.getClientTypeEnum().isMtClientType() ? PlatformEnum.MT : PlatformEnum.DP);
        parameters.setLat(request.getUserLat());
        parameters.setLng(request.getUserLng());
        parameters.setPageSize(getPageSize() <= 0 ? PAGE_SIZE : getPageSize());
        parameters.setPageNumber(getPageNum());
        parameters.setBizParams(buildBizParams());
        parameters.setOriginUserId(request.getUserId() > 0 ? String.valueOf(request.getUserId()) : null);
        if (request.getClientTypeEnum().isMtClientType()) {
            parameters.setUuid(request.getShepherdGatewayParam().getDeviceId());
        } else {
            parameters.setDpid(request.getShepherdGatewayParam().getDeviceId());
        }
        return parameters;
    }

    private ProductRecommendResult buildProducts(RecommendResult<RecommendDTO> recommendResult) {
        List<RecommendDTO> sortedResult = recommendResult.getSortedResult();

        ProductRecommendResult result = new ProductRecommendResult();
        if (CollectionUtils.isEmpty(sortedResult)) {
            return null;
        }
        int pageSize = getPageSize();
        result.setHasNext(sortedResult.size() > pageSize);

        int recommendBizId = getBizId();
        if (recommendBizId == ROLE_PLAY_SPU_RECOMMEND_BIZ) {
            int minQueryNum = getMinQueryNum();
            if (minQueryNum > 0 && sortedResult.size() < minQueryNum) {
                //热门剧本小于5，则不展示模块
                return result;
            }
        }

        if(BIJIA_PRODUCT_RECOMMEND == recommendBizId) {
            buildBiJiaShopProductM(sortedResult, result);
            // 统计比价助手的总数
            buildBiJiaTotalCount(recommendResult, result);
            return result;
        }

        List<ProductM> productMS = sortedResult.stream().map(e -> buildProduct(recommendBizId, e)).collect(Collectors.toList());
        result.setProducts(productMS);

        if (MapUtils.isNotEmpty(recommendResult.getBizData()) && recommendResult.getBizData().containsKey("recallSize")) {
            //召回数量，200以上不保证值的准确性；200以下，为真实数量
            result.setTotal((Integer) recommendResult.getBizData().get("recallSize"));
        }
        return result;
    }

    private void buildBiJiaTotalCount(RecommendResult<RecommendDTO> recommendResult, ProductRecommendResult result) {
        int recommendBizId = getBizId();
        if(recommendBizId == BIJIA_PRODUCT_RECOMMEND) {
            Map<String, Object> bizDataMap = recommendResult.getSortedResult().get(0).getBizData();
            if(MapUtils.isNotEmpty(bizDataMap) && bizDataMap.containsKey("totalHits")) {
                result.setTotal((Integer) bizDataMap.get("totalHits"));
            }
        }
    }

    private ProductM buildProduct(int recommendBizId, RecommendDTO recommendDTO) {
        if (Objects.isNull(recommendDTO)) {
            return null;
        }
        ProductM productM = new ProductM();
        productM.setProductId(NumberUtils.toInt(recommendDTO.getItem()));
        if (MapUtils.isNotEmpty(recommendDTO.getBizData())) {
            productM.setShopNum(NumberUtils.toInt((String) recommendDTO.getBizData().getOrDefault("shopcnt", "0")));
            fillBizInfoByRecommendBizId(recommendBizId, recommendDTO, productM);
        }
        return productM;
    }

    private void fillBizInfoByRecommendBizId(int recommendBizId, RecommendDTO recommendDTO, ProductM productM) {
        if (recommendBizId == ACTIVE_ROLE_PLAY_CITY_PIN_ID) {
            long shopId = NumberUtils.toLong((String) recommendDTO.getBizData().getOrDefault("shopid", "0"));
            productM.setShopIds(shopId > 0 ? Lists.newArrayList(shopId) : Lists.newArrayList());
        }
    }


    private void buildBiJiaShopProductM(List<RecommendDTO> sortedResult, ProductRecommendResult result) {
        if(Objects.isNull(sortedResult)) {
            return ;
        }

        List<ProductM> products = Lists.newArrayList();
        Map<Long, Long> dealId2ShopId = Maps.newHashMap();

        for(RecommendDTO recommendDTO : sortedResult) {
            ProductM productModule = new ProductM();
            long productId = NumberUtils.toLong(recommendDTO.getItem());
            productModule.setProductId(productId);
            if (MapUtils.isNotEmpty(recommendDTO.getBizData())) {
                long shopId = NumberUtils.toLong((String) recommendDTO.getBizData().get("relShopId"));
                dealId2ShopId.put(productId, shopId);
            }
            products.add(productModule);
        }
        result.setProducts(products);
        result.setDealId2ShopId(dealId2ShopId);
    }


    private int getPageNum() {
        if (request.getCustomParam() != null
                && request.getCustomParam(RequestCustomParamEnum.pagenum) != null) {
            return Integer.parseInt(request.getCustomParam(RequestCustomParamEnum.pagenum));
        }
        return DEFAULT_PAGE_NUM;
    }

    private int getPageSize() {
        if (request.getCustomParam() != null
                && request.getCustomParam(RequestCustomParamEnum.pagenum) != null) {
            return Integer.parseInt(request.getCustomParam(RequestCustomParamEnum.pagenum));
        }
        return 0;
    }
}
