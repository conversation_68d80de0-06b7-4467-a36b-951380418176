package com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.ext.param.handler;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.product.detail.RequestCustomParamEnum;
import com.sankuai.dz.product.detail.gateway.spi.dto.CustomParam;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.enums.OrderPageExtParamEnums;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.ext.param.request.ExtParamBuilderRequest;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * Unit tests for {@link EventPromoChannelExtParamBuilder#buildKey()}
 */
@ExtendWith(MockitoExtension.class)
class EventPromoChannelExtParamBuilderTest {

    private final EventPromoChannelExtParamBuilder builder = new EventPromoChannelExtParamBuilder();

    @Mock
    private ProductDetailPageRequest request;

    @Mock
    private ExtParamBuilderRequest builderRequest;

    /**
     * Test that buildKey() returns the correct enum value eventpromochannel
     */
    @Test
    @DisplayName("Should return eventpromochannel enum value")
    public void testBuildKeyReturnsExpectedEnumValue() throws Throwable {
        // arrange
        EventPromoChannelExtParamBuilder builder = new EventPromoChannelExtParamBuilder();
        // act
        OrderPageExtParamEnums result = builder.buildKey();
        // assert
        assertNotNull(result, "buildKey() should not return null");
        assertEquals(OrderPageExtParamEnums.eventpromochannel, result, "buildKey() should return OrderPageExtParamEnums.eventpromochannel");
        assertEquals("优惠渠道，营销定义", result.getDesc(), "The description of returned enum should match");
    }

    /**
     * 测试当 customParam 为 null 时返回 null
     */
    @Test
    public void testDoBuildExtParamWhenCustomParamIsNull() throws Exception {
        // arrange
        when(request.getCustomParam(RequestCustomParamEnum.eventpromochannel)).thenReturn(null);
        // act
        String result = builder.doBuildExtParam(request, builderRequest);
        // assert
        assertNull(result);
        verify(request).getCustomParam(RequestCustomParamEnum.eventpromochannel);
    }

    /**
     * 测试当 eventpromochannel 参数为 null 时返回 null
     */
    @Test
    public void testDoBuildExtParamWhenEventPromoChannelIsNull() throws Exception {
        // arrange
        CustomParam customParam = mock(CustomParam.class);
        when(request.getCustomParam(RequestCustomParamEnum.eventpromochannel)).thenReturn(null);
        // act
        String result = builder.doBuildExtParam(request, builderRequest);
        // assert
        assertNull(result);
        verify(request).getCustomParam(RequestCustomParamEnum.eventpromochannel);
    }

    /**
     * 测试当 eventpromochannel 参数为空字符串时返回 null
     */
    @Test
    public void testDoBuildExtParamWhenEventPromoChannelIsEmpty() throws Exception {
        // arrange
        CustomParam customParam = mock(CustomParam.class);
        when(request.getCustomParam(RequestCustomParamEnum.eventpromochannel)).thenReturn("");
        // act
        String result = builder.doBuildExtParam(request, builderRequest);
        // assert
        assertNull(result);
        verify(request).getCustomParam(RequestCustomParamEnum.eventpromochannel);
    }

    /**
     * 测试当 eventpromochannel 参数有正常值时返回该值
     */
    @Test
    public void testDoBuildExtParamWhenEventPromoChannelHasValue() throws Exception {
        // arrange
        String expectedValue = "test_channel";
        CustomParam customParam = mock(CustomParam.class);
        when(request.getCustomParam(RequestCustomParamEnum.eventpromochannel)).thenReturn(expectedValue);
        // act
        String result = builder.doBuildExtParam(request, builderRequest);
        // assert
        assertEquals(expectedValue, result);
        verify(request).getCustomParam(RequestCustomParamEnum.eventpromochannel);
    }

    /**
     * 测试当 request 为 null 时抛出 NullPointerException
     */
    @Test
    public void testDoBuildExtParamWhenRequestIsNull() {
        // arrange
        ProductDetailPageRequest nullRequest = null;
        // act & assert
        assertThrows(NullPointerException.class, () -> {
            builder.doBuildExtParam(nullRequest, builderRequest);
        });
    }
}
