package com.sankuai.dzshoppingguide.product.detail.application.fetcher.tag.guarantee;

import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dz.product.detail.RequestSourceEnum;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategoryFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.dealgroup.PlatformProductIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.dealgroup.PlatformProductIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.ProductPromoPriceFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.dto.ProductPriceReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
import com.sankuai.nib.price.operation.api.common.dto.SessionContextDTO;
import com.sankuai.nib.price.operation.api.guarantee.common.dto.ChannelDTO;
import com.sankuai.nib.price.operation.api.guarantee.common.dto.GuaranteeObjectQueryDTO;
import com.sankuai.nib.price.operation.api.guarantee.common.dto.TagDTO;
import com.sankuai.nib.price.operation.api.guarantee.common.dto.UserInfoDTO;
import com.sankuai.nib.price.operation.api.guarantee.guide.standard.dto.ObjectGuaranteeTagDTO;
import com.sankuai.nib.price.operation.api.guarantee.guide.standard.dto.QueryTagOptionDTO;
import com.sankuai.nib.price.operation.api.guarantee.guide.standard.request.BatchQueryGuaranteeTagRequest;
import com.sankuai.nib.price.operation.common.guarantee.enums.ChannelNoEnum;
import com.sankuai.nib.price.operation.common.guarantee.enums.GuaranteeTagNameEnum;
import com.sankuai.nib.price.operation.common.guarantee.enums.PlatformEnum;
import com.sankuai.nib.price.operation.common.guarantee.enums.QueryExtKeyEnum;
import com.sankuai.nib.price.operation.common.guarantee.enums.TerminalTypeEnum;
import com.sankuai.nib.sp.common.enums.Owner;
import com.sankuai.nib.sp.common.enums.TradeType;
import org.apache.commons.collections4.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletableFuture;

import static com.sankuai.nib.price.operation.common.guarantee.enums.GuaranteeTypeEnum.ANXIN_LEARNING_GUARANTEE;
import static com.sankuai.nib.price.operation.common.guarantee.enums.GuaranteeTypeEnum.ANXIN_MEDICAL_GUARANTEE;
import static com.sankuai.nib.price.operation.common.guarantee.enums.GuaranteeTypeEnum.BEST_PRICE_GUARANTEE;
import static com.sankuai.nib.price.operation.common.guarantee.enums.GuaranteeTypeEnum.PRICE_PROTECTION;
import static com.sankuai.nib.price.operation.common.guarantee.enums.GuaranteeTypeEnum.STATE_SUBSIDIES;
import static com.sankuai.nib.price.operation.common.guarantee.enums.ObjectTypeEnum.PRODUCT;
import static com.sankuai.nib.price.operation.common.guarantee.enums.ReturnModeEnum.PRIORITY_SORT_ONE_GROUP_BY_GUARANTEE_TYPE;

/**
 * <AUTHOR>
 * @date 2025-03-12
 * @desc 商品保障标签
 */
@Fetcher(
        previousLayerDependencies = {
                ProductCategoryFetcher.class,
                PlatformProductIdMapperFetcher.class,
                ShopIdMapperFetcher.class,
                ProductPromoPriceFetcher.class
        }
)
public class GuaranteeTagFetcher extends NormalFetcherContext<ProductGuaranteeTagInfo> {

    private static final Set<ClientTypeEnum> MAIN_WEB_CLIENT_TYPE = Sets.newHashSet(ClientTypeEnum.MT_I, ClientTypeEnum.DP_M, ClientTypeEnum.MT_PC, ClientTypeEnum.DP_PC);

    @Resource
    private CompositeAtomService compositeAtomService;

    @Override
    protected CompletableFuture<ProductGuaranteeTagInfo> doFetch() {
        ProductPriceReturnValue promoPriceReturnValue = getDependencyResult(ProductPromoPriceFetcher.class);
        ProductCategory productCategory = getDependencyResult(ProductCategoryFetcher.class);
        PlatformProductIdMapper platformProductIdMapper = getDependencyResult(PlatformProductIdMapperFetcher.class);
        ShopIdMapper shopIdMapper = getDependencyResult(ShopIdMapperFetcher.class);
        SessionContextDTO sessionContextDTO = buildSessionContextDTO(productCategory);
        BatchQueryGuaranteeTagRequest guaranteeTagRequest = buildQueryGuaranteeTagRequest(productCategory, platformProductIdMapper, shopIdMapper);
        return compositeAtomService.batchQueryGuaranteeTag(sessionContextDTO, guaranteeTagRequest).thenApply(result -> {
            if (CollectionUtils.isEmpty(result)) {
                return new ProductGuaranteeTagInfo();
            }
            ProductGuaranteeTagInfo productGuaranteeTagInfo = new ProductGuaranteeTagInfo();
            // 价保
            TagDTO stateSubsidiesTag = getStateSubsidies(result);
            productGuaranteeTagInfo.setStateSubsidies(stateSubsidiesTag);
            return productGuaranteeTagInfo;
        });
    }

    private TagDTO getStateSubsidies(List<ObjectGuaranteeTagDTO> guaranteeTags) {
        return guaranteeTags.stream()
                .filter(Objects::nonNull)
                .map(ObjectGuaranteeTagDTO::getTags)
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .filter(tag -> Objects.equals(GuaranteeTagNameEnum.STATE_SUBSIDIES_GUARANTEE.getCode(), tag.getCode()))
                .findFirst()
                .orElse(null);
    }

    private BatchQueryGuaranteeTagRequest buildQueryGuaranteeTagRequest(ProductCategory productCategory, PlatformProductIdMapper platformProductIdMapper, ShopIdMapper shopIdMapper) {
        BatchQueryGuaranteeTagRequest guaranteeTagRequest = new BatchQueryGuaranteeTagRequest();
        guaranteeTagRequest.setObjects(getObjects(productCategory.getProductSecondCategoryId(), platformProductIdMapper.getPlatformProductId(), shopIdMapper.getMtBestShopId()));
        guaranteeTagRequest.setGuaranteeTypes(getGuaranteeTypes());
        guaranteeTagRequest.setUserInfo(getUserInfoDTO(request.getClientTypeEnum(), request.getPageSource()));
        guaranteeTagRequest.setQueryTagOption(getQueryTagOption());
        return guaranteeTagRequest;
    }

    public QueryTagOptionDTO getQueryTagOption(){
        QueryTagOptionDTO queryTagOptionDTO=new QueryTagOptionDTO();
        queryTagOptionDTO.setReturnMode(PRIORITY_SORT_ONE_GROUP_BY_GUARANTEE_TYPE.getCode());
        return queryTagOptionDTO;
    }

    public UserInfoDTO getUserInfoDTO(ClientTypeEnum clientTypeEnum, String pageSource) {
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        ChannelDTO channelDTO = new ChannelDTO();

        PlatformEnum platformEnum;
        if (clientTypeEnum.isMtClientType()) {
            platformEnum = PlatformEnum.MT_PLATFORM;
        } else {
            platformEnum = PlatformEnum.DP_PLATFORM;
        }
        channelDTO.setPlatform(platformEnum.getCode());

        TerminalTypeEnum terminalTypeEnum;
        if (clientTypeEnum.isInApp()) {
            terminalTypeEnum = TerminalTypeEnum.APP;
        } else if (clientTypeEnum.isInWxXCX()) {
            terminalTypeEnum = TerminalTypeEnum.APPLETS;
        } else if (MAIN_WEB_CLIENT_TYPE.contains(clientTypeEnum)) {
            terminalTypeEnum = TerminalTypeEnum.PC;
        } else {
            terminalTypeEnum = TerminalTypeEnum.MOBILE;
        }
        channelDTO.setTerminalType(terminalTypeEnum.getCode());

        ChannelNoEnum channelNoEnum;
        if (RequestSourceEnum.fromLive(pageSource)) {
            channelNoEnum = ChannelNoEnum.LIVE_STREAMING;
        } else {
            channelNoEnum = ChannelNoEnum.UNKNOWN;
        }
        channelDTO.setChannelNo(channelNoEnum.getCode());

        userInfoDTO.setChannel(channelDTO);
        return userInfoDTO;
    }

    private Set<Integer> getGuaranteeTypes() {
        Set<Integer> guaranteeTypes = new HashSet<>();
        // 国家补贴
        guaranteeTypes.add(STATE_SUBSIDIES.getCode());
        return guaranteeTypes;
    }

    public Set<GuaranteeObjectQueryDTO> getObjects(int secondCategoryId, long platformProductId, long mtShopId) {
        if (platformProductId <= 0L) {
            return new HashSet<>();
        }
        Set<GuaranteeObjectQueryDTO> objects = new HashSet<>();
        GuaranteeObjectQueryDTO object = new GuaranteeObjectQueryDTO();
        object.setObjectId(String.valueOf(platformProductId));
        object.setObjectType(PRODUCT.getCode());
        objects.add(object);
        return objects;
    }

    private SessionContextDTO buildSessionContextDTO(ProductCategory productCategory) {
        SessionContextDTO sessionContext = new SessionContextDTO();
        sessionContext.setOwner(Owner.NIB_GENERAL.getValue());
        sessionContext.setTradeType(TradeType.GROUPBUY_PAY.getCode());
        return sessionContext;
    }
}
