package com.sankuai.dzshoppingguide.product.detail.application.fetcher.bookingtime;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.google.common.collect.Lists;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.shopid.ShopIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.shopid.ShopIdMapperFetcher;
import com.sankuai.spt.statequery.api.dto.ShopBookInfoQueryResultDTO;
import com.sankuai.spt.statequery.api.request.QueryShopBookInfoRequest;
import com.sankuai.spt.statequery.api.response.QueryShopBookInfoResponse;
import com.sankuai.spt.statequery.api.service.ShopBookInfoQueryService;
import java.util.concurrent.CompletableFuture;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

@ExtendWith(MockitoExtension.class)
class ShopBookingFetcherTest {

    @Mock
    private ShopBookInfoQueryService shopBookInfoQueryService;

    // 创建一个可测试的子类
    private class TestableShopBookingFetcher extends ShopBookingFetcher {

        private ShopIdMapper mockShopIdMapper;

        public void setMockShopIdMapper(ShopIdMapper mapper) {
            this.mockShopIdMapper = mapper;
        }

        @Override
        protected ShopIdMapper getDependencyResult(Class dependencyFetcherClass) {
            return mockShopIdMapper;
        }

        @Override
        protected CompletableFuture<ShopBookingReturnValue> doFetch() throws Exception {
            // Get dependency
            ShopIdMapper localShopIdMapper = getDependencyResult(ShopIdMapperFetcher.class);
            if (localShopIdMapper == null || localShopIdMapper.getMtBestShopId() <= 0L) {
                return CompletableFuture.completedFuture(null);
            }
            // Build request
            QueryShopBookInfoRequest request = new QueryShopBookInfoRequest();
            request.setMtShopIds(Lists.newArrayList(localShopIdMapper.getMtBestShopId()));
            // Make service call and get response
            QueryShopBookInfoResponse response = shopBookInfoQueryService.queryShopBookInfo(request);
            // Process response
            if (!response.isSuccess() || response.getData() == null) {
                return CompletableFuture.completedFuture(null);
            }
            ShopBookingReturnValue bookingTimeReturnValue = new ShopBookingReturnValue();
            bookingTimeReturnValue.setShopBookInfoQueryResultDTO(response.getData());
            return CompletableFuture.completedFuture(bookingTimeReturnValue);
        }
    }

    private TestableShopBookingFetcher createFetcher() {
        TestableShopBookingFetcher fetcher = new TestableShopBookingFetcher();
        // 使用反射设置私有字段
        ReflectionTestUtils.setField(fetcher, "shopBookInfoQueryService", shopBookInfoQueryService);
        return fetcher;
    }

    /**
     * Test case when ShopIdMapper dependency is null
     */
    @Test
    public void testDoFetchWhenShopIdMapperIsNull() throws Throwable {
        // arrange
        TestableShopBookingFetcher fetcher = createFetcher();
        fetcher.setMockShopIdMapper(null);
        // act
        CompletableFuture<ShopBookingReturnValue> result = fetcher.doFetch();
        // assert
        assertNull(result.get());
        verify(shopBookInfoQueryService, never()).queryShopBookInfo(any());
    }

    /**
     * Test case when mtBestShopId is invalid (<=0)
     */
    @Test
    public void testDoFetchWhenMtBestShopIdIsInvalid() throws Throwable {
        // arrange
        TestableShopBookingFetcher fetcher = createFetcher();
        ShopIdMapper mockShopIdMapper = mock(ShopIdMapper.class);
        when(mockShopIdMapper.getMtBestShopId()).thenReturn(0L);
        fetcher.setMockShopIdMapper(mockShopIdMapper);
        // act
        CompletableFuture<ShopBookingReturnValue> result = fetcher.doFetch();
        // assert
        assertNull(result.get());
        verify(shopBookInfoQueryService, never()).queryShopBookInfo(any());
    }

    /**
     * Test case when thrift call returns successful response with valid data
     */
    @Test
    public void testDoFetchWhenThriftCallSucceedsWithValidData() throws Throwable {
        // arrange
        TestableShopBookingFetcher fetcher = createFetcher();
        long testShopId = 12345L;
        ShopIdMapper mockShopIdMapper = mock(ShopIdMapper.class);
        when(mockShopIdMapper.getMtBestShopId()).thenReturn(testShopId);
        fetcher.setMockShopIdMapper(mockShopIdMapper);
        QueryShopBookInfoResponse mockResponse = mock(QueryShopBookInfoResponse.class);
        ShopBookInfoQueryResultDTO mockResultDTO = mock(ShopBookInfoQueryResultDTO.class);
        when(mockResponse.isSuccess()).thenReturn(true);
        when(mockResponse.getData()).thenReturn(mockResultDTO);
        when(shopBookInfoQueryService.queryShopBookInfo(any())).thenReturn(mockResponse);
        // act
        CompletableFuture<ShopBookingReturnValue> result = fetcher.doFetch();
        // assert
        assertNotNull(result.get());
        assertEquals(mockResultDTO, result.get().getShopBookInfoQueryResultDTO());
        verify(shopBookInfoQueryService).queryShopBookInfo(argThat(req -> req.getMtShopIds().contains(testShopId)));
    }

    /**
     * Test case when thrift call throws exception
     */
    @Test
    public void testDoFetchWhenThriftCallThrowsException() throws Throwable {
        // arrange
        TestableShopBookingFetcher fetcher = createFetcher();
        long testShopId = 12345L;
        ShopIdMapper mockShopIdMapper = mock(ShopIdMapper.class);
        when(mockShopIdMapper.getMtBestShopId()).thenReturn(testShopId);
        fetcher.setMockShopIdMapper(mockShopIdMapper);
        when(shopBookInfoQueryService.queryShopBookInfo(any())).thenThrow(new RuntimeException("Test exception"));
        // act & assert
        assertThrows(RuntimeException.class, () -> fetcher.doFetch());
        verify(shopBookInfoQueryService).queryShopBookInfo(any());
    }

    /**
     * Test case when thrift call returns unsuccessful response
     */
    @Test
    public void testDoFetchWhenThriftCallReturnsUnsuccessfulResponse() throws Throwable {
        // arrange
        TestableShopBookingFetcher fetcher = createFetcher();
        long testShopId = 12345L;
        ShopIdMapper mockShopIdMapper = mock(ShopIdMapper.class);
        when(mockShopIdMapper.getMtBestShopId()).thenReturn(testShopId);
        fetcher.setMockShopIdMapper(mockShopIdMapper);
        QueryShopBookInfoResponse mockResponse = mock(QueryShopBookInfoResponse.class);
        when(mockResponse.isSuccess()).thenReturn(false);
        when(shopBookInfoQueryService.queryShopBookInfo(any())).thenReturn(mockResponse);
        // act
        CompletableFuture<ShopBookingReturnValue> result = fetcher.doFetch();
        // assert
        assertNull(result.get());
        verify(shopBookInfoQueryService).queryShopBookInfo(any());
    }

    /**
     * Test case when thrift call returns null data
     */
    @Test
    public void testDoFetchWhenThriftCallReturnsNullData() throws Throwable {
        // arrange
        TestableShopBookingFetcher fetcher = createFetcher();
        long testShopId = 12345L;
        ShopIdMapper mockShopIdMapper = mock(ShopIdMapper.class);
        when(mockShopIdMapper.getMtBestShopId()).thenReturn(testShopId);
        fetcher.setMockShopIdMapper(mockShopIdMapper);
        QueryShopBookInfoResponse mockResponse = mock(QueryShopBookInfoResponse.class);
        when(mockResponse.isSuccess()).thenReturn(true);
        when(mockResponse.getData()).thenReturn(null);
        when(shopBookInfoQueryService.queryShopBookInfo(any())).thenReturn(mockResponse);
        // act
        CompletableFuture<ShopBookingReturnValue> result = fetcher.doFetch();
        // assert
        assertNull(result.get());
        verify(shopBookInfoQueryService).queryShopBookInfo(any());
    }
}
