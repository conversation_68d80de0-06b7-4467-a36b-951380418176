package com.sankuai.dzshoppingguide.product.detail.application.utils;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import java.math.BigDecimal;
import org.junit.Test;

public class BeautyBianMeiCouponHelperTest {

    /**
     * 测试 getBeautyBianMeiCouponPromoName 方法，当 PromoDTO 对象为 null 时，应抛出 NullPointerException
     */
    @Test(expected = NullPointerException.class)
    public void testGetBeautyBianMeiCouponPromoNameWhenPromoDTOIsNull() {
        BeautyBianMeiCouponHelper.getBeautyBianMeiCouponPromoName(null);
    }

    /**
     * 测试 getBeautyBianMeiCouponPromoName 方法，当 PromoDTO 对象非 null，但 amount 属性为 null 时，应抛出 NullPointerException
     */
    @Test(expected = NullPointerException.class)
    public void testGetBeautyBianMeiCouponPromoNameWhenAmountIsNull() {
        PromoDTO promoDTO = new PromoDTO();
        BeautyBianMeiCouponHelper.getBeautyBianMeiCouponPromoName(promoDTO);
    }

    /**
     * 测试 getBeautyBianMeiCouponPromoName 方法，当 PromoDTO 对象非 null，且 amount 属性非 null 时，应返回预期的字符串
     */
    @Test
    public void testGetBeautyBianMeiCouponPromoNameWhenAmountIsNotNull() {
        PromoDTO promoDTO = new PromoDTO();
        promoDTO.setAmount(new BigDecimal("100"));
        String result = BeautyBianMeiCouponHelper.getBeautyBianMeiCouponPromoName(promoDTO);
        assertEquals("100元变美神券", result);
    }
}
