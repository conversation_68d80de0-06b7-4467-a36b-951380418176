package com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSON;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.response.FetcherResponse;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.QueryCenterAggregateReturnValue;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class ProductAttrFetcherTest {

    @Mock
    private Map<String, AttrDTO> mockSkuAttr;

    @Mock
    private AttrDTO attrDTO;

    /**
     * 测试正常场景：aggregateResult 不为空，且所有嵌套对象都不为空
     */
    @Test
    public void testMapResultNormalCase() throws Throwable {
        // arrange
        ProductAttrFetcher fetcher = new ProductAttrFetcher();
        FetcherResponse<QueryCenterAggregateReturnValue> aggregateResult = mock(FetcherResponse.class);
        QueryCenterAggregateReturnValue returnValue = mock(QueryCenterAggregateReturnValue.class);
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        List<AttrDTO> attrs = new ArrayList<>();
        AttrDTO attr1 = new AttrDTO();
        attr1.setName("attr1");
        attrs.add(attr1);
        when(aggregateResult.getReturnValue()).thenReturn(returnValue);
        when(returnValue.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getAttrs()).thenReturn(attrs);
        // act
        FetcherResponse<ProductAttr> result = fetcher.mapResult(aggregateResult);
        // assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getReturnValue());
        Optional<AttrDTO> attrDTO = result.getReturnValue().getProductAttr("attr1");
        assertTrue(attrDTO.isPresent());
        assertEquals("attr1", attrDTO.get().getName());
    }

    /**
     * 测试边界场景：aggregateResult 为空
     */
    @Test
    public void testMapResultAggregateResultIsNull() throws Throwable {
        // arrange
        ProductAttrFetcher fetcher = new ProductAttrFetcher();
        // act
        FetcherResponse<ProductAttr> result = fetcher.mapResult(null);
        // assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getReturnValue());
        Optional<AttrDTO> attrDTO = result.getReturnValue().getProductAttr("anyKey");
        assertFalse(attrDTO.isPresent());
    }

    /**
     * 测试边界场景：aggregateResult.getReturnValue() 为空
     */
    @Test
    public void testMapResultReturnValueIsNull() throws Throwable {
        // arrange
        ProductAttrFetcher fetcher = new ProductAttrFetcher();
        FetcherResponse<QueryCenterAggregateReturnValue> aggregateResult = mock(FetcherResponse.class);
        when(aggregateResult.getReturnValue()).thenReturn(null);
        // act
        FetcherResponse<ProductAttr> result = fetcher.mapResult(aggregateResult);
        // assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getReturnValue());
        Optional<AttrDTO> attrDTO = result.getReturnValue().getProductAttr("anyKey");
        assertFalse(attrDTO.isPresent());
    }

    /**
     * 测试边界场景：QueryCenterAggregateReturnValue.getDealGroupDTO() 为空
     */
    @Test
    public void testMapResultDealGroupDTOIsNull() throws Throwable {
        // arrange
        ProductAttrFetcher fetcher = new ProductAttrFetcher();
        FetcherResponse<QueryCenterAggregateReturnValue> aggregateResult = mock(FetcherResponse.class);
        QueryCenterAggregateReturnValue returnValue = mock(QueryCenterAggregateReturnValue.class);
        when(aggregateResult.getReturnValue()).thenReturn(returnValue);
        when(returnValue.getDealGroupDTO()).thenReturn(null);
        // act
        FetcherResponse<ProductAttr> result = fetcher.mapResult(aggregateResult);
        // assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getReturnValue());
        Optional<AttrDTO> attrDTO = result.getReturnValue().getProductAttr("anyKey");
        assertFalse(attrDTO.isPresent());
    }

    /**
     * 测试边界场景：DealGroupDTO.getAttrs() 返回空列表
     */
    @Test
    public void testMapResultAttrsIsEmpty() throws Throwable {
        // arrange
        ProductAttrFetcher fetcher = new ProductAttrFetcher();
        FetcherResponse<QueryCenterAggregateReturnValue> aggregateResult = mock(FetcherResponse.class);
        QueryCenterAggregateReturnValue returnValue = mock(QueryCenterAggregateReturnValue.class);
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        when(aggregateResult.getReturnValue()).thenReturn(returnValue);
        when(returnValue.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getAttrs()).thenReturn(new ArrayList<>());
        // act
        FetcherResponse<ProductAttr> result = fetcher.mapResult(aggregateResult);
        // assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getReturnValue());
        Optional<AttrDTO> attrDTO = result.getReturnValue().getProductAttr("anyKey");
        assertFalse(attrDTO.isPresent());
    }

    /**
     * 测试边界场景：attrs 列表中的 AttrDTO 对象为空
     */
    @Test
    public void testMapResultAttrDTOIsNull() throws Throwable {
        // arrange
        ProductAttrFetcher fetcher = new ProductAttrFetcher();
        FetcherResponse<QueryCenterAggregateReturnValue> aggregateResult = mock(FetcherResponse.class);
        QueryCenterAggregateReturnValue returnValue = mock(QueryCenterAggregateReturnValue.class);
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        List<AttrDTO> attrs = new ArrayList<>();
        attrs.add(null);
        when(aggregateResult.getReturnValue()).thenReturn(returnValue);
        when(returnValue.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getAttrs()).thenReturn(attrs);
        // act
        FetcherResponse<ProductAttr> result = fetcher.mapResult(aggregateResult);
        // assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getReturnValue());
        Optional<AttrDTO> attrDTO = result.getReturnValue().getProductAttr("anyKey");
        assertFalse(attrDTO.isPresent());
    }

    /**
     * 测试异常场景：DealGroupDTO.getAttrs() 返回 null
     */
    @Test
    public void testMapResultAttrsIsNull() throws Throwable {
        // arrange
        ProductAttrFetcher fetcher = new ProductAttrFetcher();
        FetcherResponse<QueryCenterAggregateReturnValue> aggregateResult = mock(FetcherResponse.class);
        QueryCenterAggregateReturnValue returnValue = mock(QueryCenterAggregateReturnValue.class);
        DealGroupDTO dealGroupDTO = mock(DealGroupDTO.class);
        when(aggregateResult.getReturnValue()).thenReturn(returnValue);
        when(returnValue.getDealGroupDTO()).thenReturn(dealGroupDTO);
        when(dealGroupDTO.getAttrs()).thenReturn(null);
        // act
        FetcherResponse<ProductAttr> result = fetcher.mapResult(aggregateResult);
        // assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNotNull(result.getReturnValue());
        Optional<AttrDTO> attrDTO = result.getReturnValue().getProductAttr("anyKey");
        assertFalse(attrDTO.isPresent());
    }

    /**
     * Test getProductAttrValue when attrName exists and has non-null value list
     */
    @Test
    public void testGetProductAttrValueWithExistingAttrAndNonNullValue() {
        // arrange
        String attrName = "color";
        List<String> expectedValues = Arrays.asList("red", "blue");
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setValue(expectedValues);
        when(mockSkuAttr.get(attrName)).thenReturn(attrDTO);
        ProductAttr productAttr = new ProductAttr(mockSkuAttr);
        // act
        List<String> result = productAttr.getProductAttrValue(attrName);
        // assert
        assertEquals(expectedValues, result);
        verify(mockSkuAttr).get(attrName);
    }

    /**
     * Test getProductAttrValue when attrName exists but AttrDTO value is null
     */
    @Test
    public void testGetProductAttrValueWithExistingAttrAndNullValue() {
        // arrange
        String attrName = "size";
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setValue(null);
        when(mockSkuAttr.get(attrName)).thenReturn(attrDTO);
        ProductAttr productAttr = new ProductAttr(mockSkuAttr);
        // act
        List<String> result = productAttr.getProductAttrValue(attrName);
        // assert
        assertNull(result);
        verify(mockSkuAttr).get(attrName);
    }

    /**
     * Test getProductAttrValue when attrName does not exist in skuAttr map
     */
    @Test
    public void testGetProductAttrValueWithNonExistingAttr() {
        // arrange
        String attrName = "nonexistent";
        when(mockSkuAttr.get(attrName)).thenReturn(null);
        ProductAttr productAttr = new ProductAttr(mockSkuAttr);
        // act
        List<String> result = productAttr.getProductAttrValue(attrName);
        // assert
        assertNull(result);
        verify(mockSkuAttr).get(attrName);
    }

    /**
     * Test getProductAttrValue when skuAttr map is null
     */
    @Test
    public void testGetProductAttrValueWithNullSkuAttr() {
        // arrange
        String attrName = "anyAttr";
        ProductAttr productAttr = new ProductAttr(null);
        // act
        List<String> result = productAttr.getProductAttrValue(attrName);
        // assert
        assertNull(result);
    }

    /**
     * Test getProductAttrValue with real Map instead of mock
     */
    @Test
    public void testGetProductAttrValueWithRealMap() {
        // arrange
        String attrName = "material";
        List<String> expectedValues = Arrays.asList("cotton", "polyester");
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setValue(expectedValues);
        Map<String, AttrDTO> realSkuAttr = new HashMap<>();
        realSkuAttr.put(attrName, attrDTO);
        ProductAttr productAttr = new ProductAttr(realSkuAttr);
        // act
        List<String> result = productAttr.getProductAttrValue(attrName);
        // assert
        assertEquals(expectedValues, result);
    }

    /**
     * Test case for null skuAttr map
     */
    @Test
    public void testGetProductAttrFirstValueWithNullSkuAttr() {
        // arrange
        ProductAttr productAttr = new ProductAttr(null);
        // act
        String result = productAttr.getProductAttrFirstValue("testAttr");
        // assert
        assertNull(result);
    }

    /**
     * Test case for empty skuAttr map
     */
    @Test
    public void testGetProductAttrFirstValueWithEmptySkuAttr() {
        // arrange
        ProductAttr productAttr = new ProductAttr(Collections.emptyMap());
        // act
        String result = productAttr.getProductAttrFirstValue("testAttr");
        // assert
        assertNull(result);
    }

    /**
     * Test case for skuAttr map not containing the given attrName
     */
    @Test
    public void testGetProductAttrFirstValueWithNonExistentAttr() {
        // arrange
        Map<String, AttrDTO> skuAttr = new HashMap<>();
        skuAttr.put("existingAttr", attrDTO);
        ProductAttr productAttr = new ProductAttr(skuAttr);
        // act
        String result = productAttr.getProductAttrFirstValue("nonExistentAttr");
        // assert
        assertNull(result);
    }

    /**
     * Test case for AttrDTO with null value list
     */
    @Test
    public void testGetProductAttrFirstValueWithNullValueList() {
        // arrange
        when(attrDTO.getValue()).thenReturn(null);
        Map<String, AttrDTO> skuAttr = Collections.singletonMap("testAttr", attrDTO);
        ProductAttr productAttr = new ProductAttr(skuAttr);
        // act
        String result = productAttr.getProductAttrFirstValue("testAttr");
        // assert
        assertNull(result);
    }

    /**
     * Test case for AttrDTO with empty value list
     */
    @Test
    public void testGetProductAttrFirstValueWithEmptyValueList() {
        // arrange
        when(attrDTO.getValue()).thenReturn(Collections.emptyList());
        Map<String, AttrDTO> skuAttr = Collections.singletonMap("testAttr", attrDTO);
        ProductAttr productAttr = new ProductAttr(skuAttr);
        // act
        String result = productAttr.getProductAttrFirstValue("testAttr");
        // assert
        assertNull(result);
    }

    /**
     * Test case for AttrDTO with non-empty value list
     */
    @Test
    public void testGetProductAttrFirstValueWithNonEmptyValueList() {
        // arrange
        when(attrDTO.getValue()).thenReturn(Arrays.asList("firstValue", "secondValue"));
        Map<String, AttrDTO> skuAttr = Collections.singletonMap("testAttr", attrDTO);
        ProductAttr productAttr = new ProductAttr(skuAttr);
        // act
        String result = productAttr.getProductAttrFirstValue("testAttr");
        // assert
        assertEquals("firstValue", result);
    }

    /**
     * Test when skuAttr map is null
     */
    @Test
    public void testGetProductAttrValueJson_NullMap() throws Throwable {
        // arrange
        ProductAttr productAttr = new ProductAttr(null);
        // act
        String result = productAttr.getProductAttrValueJson("anyAttr");
        // assert
        assertNull(result);
    }

    /**
     * Test when attribute name doesn't exist in map
     */
    @Test
    public void testGetProductAttrValueJson_NonExistentAttr() throws Throwable {
        // arrange
        Map<String, AttrDTO> skuAttr = new HashMap<>();
        ProductAttr productAttr = new ProductAttr(skuAttr);
        // act
        String result = productAttr.getProductAttrValueJson("nonExistent");
        // assert
        assertNull(result);
    }

    /**
     * Test when AttrDTO exists but has null value list
     */
    @Test
    public void testGetProductAttrValueJson_NullValueList() throws Throwable {
        // arrange
        Map<String, AttrDTO> skuAttr = new HashMap<>();
        lenient().when(attrDTO.getValue()).thenReturn(null);
        skuAttr.put("testAttr", attrDTO);
        ProductAttr productAttr = new ProductAttr(skuAttr);
        // act
        String result = productAttr.getProductAttrValueJson("testAttr");
        // assert
        assertNull(result);
    }

    /**
     * Test when AttrDTO exists but has empty value list
     */
    @Test
    public void testGetProductAttrValueJson_EmptyValueList() throws Throwable {
        // arrange
        Map<String, AttrDTO> skuAttr = new HashMap<>();
        when(attrDTO.getValue()).thenReturn(Collections.emptyList());
        skuAttr.put("testAttr", attrDTO);
        ProductAttr productAttr = new ProductAttr(skuAttr);
        // act
        String result = productAttr.getProductAttrValueJson("testAttr");
        // assert
        assertNull(result);
    }

    /**
     * Test happy path with valid attribute having non-empty value list
     */
    @Test
    public void testGetProductAttrValueJson_ValidAttr() throws Throwable {
        // arrange
        Map<String, AttrDTO> skuAttr = new HashMap<>();
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setValue(Arrays.asList("value1", "value2"));
        skuAttr.put("testAttr", attrDTO);
        ProductAttr productAttr = new ProductAttr(skuAttr);
        String expectedJson = JSON.toJSONString(Arrays.asList("value1", "value2"));
        // act
        String result = productAttr.getProductAttrValueJson("testAttr");
        // assert
        assertEquals(expectedJson, result);
    }

    /**
     * Test when AttrDTO is null in the map
     */
    @Test
    public void testGetProductAttrValueJson_NullAttrDTO() throws Throwable {
        // arrange
        Map<String, AttrDTO> skuAttr = new HashMap<>();
        skuAttr.put("testAttr", null);
        ProductAttr productAttr = new ProductAttr(skuAttr);
        // act
        String result = productAttr.getProductAttrValueJson("testAttr");
        // assert
        assertNull(result);
    }
}
