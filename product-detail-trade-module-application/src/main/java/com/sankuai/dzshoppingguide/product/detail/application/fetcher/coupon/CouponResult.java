package com.sankuai.dzshoppingguide.product.detail.application.fetcher.coupon;

import com.dianping.tgc.open.entity.BatchExProxyCouponResponseDTO;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import com.sankuai.dzshoppingguide.product.detail.application.service.dto.ShopMemberDetailV;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CouponResult extends FetcherReturnValueDTO {
    private BatchExProxyCouponResponseDTO exProxyCouponResponseDTO;

}
