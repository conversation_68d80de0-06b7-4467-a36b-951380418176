package com.sankuai.dzshoppingguide.product.detail.application.fetcher.LeadsInfoFetcher;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.dianping.deal.detail.enums.PlatformEnum;
import com.dianping.product.shelf.common.request.activities.ActivityProductQueryRequest;
import com.google.common.collect.Lists;
import com.sankuai.clr.content.process.gateway.thrift.dto.leads.LoadLeadsInfoReqDTO;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import java.lang.reflect.Field;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class LeadsInfoFetcherTest {

    @InjectMocks
    private LeadsInfoFetcher leadsInfoFetcher;

    @Mock
    private ProductDetailPageRequest request;

    private void setRequest(ProductDetailPageRequest request) throws Exception {
        Field requestField = leadsInfoFetcher.getClass().getSuperclass().getSuperclass().getDeclaredField("request");
        requestField.setAccessible(true);
        requestField.set(leadsInfoFetcher, request);
    }



    /**
     * Test buildLoadLeadsInfoReq for MT platform
     */
    @Test
    public void testBuildLoadLeadsInfoReq_MTPlatform() throws Throwable {
        // arrange
        ProductDetailPageRequest request = new ProductDetailPageRequest();
        request.setPoiId(123L);
        request.setProductId(456L);
        setRequest(request);
        // act
        LoadLeadsInfoReqDTO result = leadsInfoFetcher.buildLoadLeadsInfoReq();
        // assert
        assertNotNull(result);
        assertEquals(PlatformEnum.DIAN_PING.getType(), result.getPlatform());
        assertEquals(123L, result.getPoiId());
        assertEquals(null, result.getMtDealGroupId());
        assertEquals(456L, result.getDpDealGroupId());
        assertEquals(1, result.getMerchantType());
        assertEquals(20, result.getEntranceCode());
    }

    /**
     * Test buildLoadLeadsInfoReq for DP platform
     */
    @Test
    public void testBuildLoadLeadsInfoReq_DPPlatform() throws Throwable {
        // arrange
        ProductDetailPageRequest request = new ProductDetailPageRequest();
        request.setClientType(ClientTypeEnum.DP_APP.getCode());
        request.setPoiId(123L);
        request.setProductId(456L);
        setRequest(request);
        // act
        LoadLeadsInfoReqDTO result = leadsInfoFetcher.buildLoadLeadsInfoReq();
        // assert
        assertNotNull(result);
        assertEquals(PlatformEnum.DIAN_PING.getType(), result.getPlatform());
        assertEquals(123L, result.getPoiId());
        assertEquals(456L, result.getDpDealGroupId());
        assertNull(result.getMtDealGroupId());
        assertEquals(1, result.getMerchantType());
        assertEquals(20, result.getEntranceCode());
    }

    /**
     * Test buildLoadLeadsInfoReq with zero values
     */
    @Test
    public void testBuildLoadLeadsInfoReq_ZeroValues() throws Throwable {
        // arrange
        ProductDetailPageRequest request = new ProductDetailPageRequest();
        request.setClientType(ClientTypeEnum.MT_APP.getCode());
        request.setPoiId(0L);
        request.setProductId(0L);
        setRequest(request);
        // act
        LoadLeadsInfoReqDTO result = leadsInfoFetcher.buildLoadLeadsInfoReq();
        // assert
        assertNotNull(result);
        assertEquals(0L, result.getPoiId());
        assertEquals(0L, result.getMtDealGroupId());
        assertEquals(1, result.getMerchantType());
        assertEquals(20, result.getEntranceCode());
    }

    /**
     * Test buildLoadLeadsInfoReq with null request
     */
    @Test
    public void testBuildLoadLeadsInfoReq_NullRequest() throws Throwable {
        // arrange
        setRequest(null);
        // act & assert
        assertThrows(NullPointerException.class, () -> leadsInfoFetcher.buildLoadLeadsInfoReq());
    }

    /**
     * Test building request for Meituan platform
     */
    @Test
    public void testBuildActivityProductQueryReq_MeituanPlatform() throws Throwable {
        // arrange
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.MT_APP);
        when(request.getPoiId()).thenReturn(123L);
        when(request.getProductId()).thenReturn(456L);
        // act
        ActivityProductQueryRequest result = leadsInfoFetcher.buildActivityProductQueryReq();
        // assert
        assertTrue(result.isMt());
        assertEquals(123L, result.getShopId());
        assertEquals(Lists.newArrayList(456), result.getProductIds());
        assertEquals(1, result.getProductType());
    }

    /**
     * Test building request for Dianping platform
     */
    @Test
    public void testBuildActivityProductQueryReq_DianpingPlatform() throws Throwable {
        // arrange
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.DP_APP);
        when(request.getPoiId()).thenReturn(123L);
        when(request.getProductId()).thenReturn(456L);
        // act
        ActivityProductQueryRequest result = leadsInfoFetcher.buildActivityProductQueryReq();
        // assert
        assertFalse(result.isMt());
        assertEquals(123L, result.getShopId());
        assertEquals(Lists.newArrayList(456), result.getProductIds());
        assertEquals(1, result.getProductType());
    }

    /**
     * Test handling of large productId that exceeds Integer range
     */
    @Test
    public void testBuildActivityProductQueryReq_LargeProductId() throws Throwable {
        // arrange
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.DP_APP);
        when(request.getPoiId()).thenReturn(123L);
        when(request.getProductId()).thenReturn(Integer.MAX_VALUE + 1L);
        // act & assert
        assertThrows(ArithmeticException.class, () -> {
            leadsInfoFetcher.buildActivityProductQueryReq();
        });
    }

    /**
     * Test with null poiId
     */
    @Test
    public void testBuildActivityProductQueryReq_NullPoiId() throws Throwable {
        // arrange
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.MT_APP);
        // Using 0L as an edge case value
        when(request.getPoiId()).thenReturn(0L);
        when(request.getProductId()).thenReturn(456L);
        // act
        ActivityProductQueryRequest result = leadsInfoFetcher.buildActivityProductQueryReq();
        // assert
        assertEquals(0L, result.getShopId());
        assertEquals(Lists.newArrayList(456), result.getProductIds());
        assertEquals(1, result.getProductType());
    }

    /**
     * Test with zero productId
     */
    @Test
    public void testBuildActivityProductQueryReq_ZeroProductId() throws Throwable {
        // arrange
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.MT_APP);
        when(request.getPoiId()).thenReturn(123L);
        when(request.getProductId()).thenReturn(0L);
        // act
        ActivityProductQueryRequest result = leadsInfoFetcher.buildActivityProductQueryReq();
        // assert
        assertEquals(123L, result.getShopId());
        assertEquals(Lists.newArrayList(0), result.getProductIds());
        assertEquals(1, result.getProductType());
    }
}
