package com.sankuai.dzshoppingguide.product.detail.spi.dtos;

import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-03-05
 * @desc 价格查询策略规则
 */
@Data
public class PriceQueryStrategyRule {

    /**
     * 页面来源
     */
    private List<String> pageSources;

    /**
     * 商品二级类目
     */
    private List<Integer> secondCategoryIds;

    /**
     * 客户端类型
     */
    private List<ClientTypeEnum> clientTypes;

    /**
     * 门店类目
     */
    private List<Integer> poiCategoryIds;

    /**
     * 从小到大排序
     */
    private int priority;

    private String priceQueryScene;
}
