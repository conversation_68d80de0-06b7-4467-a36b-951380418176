package com.sankuai.dzshoppingguide.product.detail.application.utils;


import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.RequestSourceEnum;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;

import java.util.Objects;

import static com.sankuai.dzshoppingguide.product.detail.application.constants.LionConstants.PRE_ORDER_CATEGORY_IDS;

/**
 * <AUTHOR>
 * @date 2022/11/25
 */
public class DealCtxHelper {

    public static boolean isOdpSource(String requestSource) {
        return RequestSourceEnum.ODP.getSource().equals(requestSource);
    }

    public static boolean isPreOrderDeal(DealGroupCategoryDTO categoryDTO, ClientTypeEnum clientTypeEnum, String pagesource) {

        // 当前仅作用于美团APP、点评APP
        // 标识和二级类目决定是否为强预订团单
        return judgeMainApp(clientTypeEnum)
                && RequestSourceEnum.PRE_ORDER_DEAL.getSource().equals(pagesource) && LionConfigUtils.hitLeadsDeal(categoryDTO, PRE_ORDER_CATEGORY_IDS);
    }

    //美团、点评APP客户端
    public static boolean judgeMainApp(ClientTypeEnum clientTypeEnum) {
        return ClientTypeEnum.MT_APP == clientTypeEnum || ClientTypeEnum.DP_APP == clientTypeEnum;
    }

    public static boolean isMtLiveMinApp(ClientTypeEnum clientTypeEnum) {
        return Objects.equals(clientTypeEnum, ClientTypeEnum.MT_LIVE_XCX)
                || Objects.equals(clientTypeEnum, ClientTypeEnum.MT_LIVE_ORDER_XCX);
    }


}
