package com.sankuai.dzshoppingguide.product.detail.application.fetcher.coupon;

import com.dianping.tgc.open.entity.BatchExProxyCouponRequest;
import com.dianping.tgc.open.entity.BizIdType;
import com.dianping.tgc.open.entity.ExProxyCouponContext;
import com.dianping.tgc.open.entity.PlatformEnum;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.dealgroup.DealGroupIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.dealgroup.DealGroupIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Optional;
import java.util.concurrent.CompletableFuture;

import static com.sankuai.dz.product.detail.gateway.spi.enums.MobileOSTypeEnum.IOS;

/**
 * <AUTHOR>
 */
@Fetcher(previousLayerDependencies = {DealGroupIdMapperFetcher.class})
@Slf4j
public class CouponFetcher extends NormalFetcherContext<CouponResult> {
    @Autowired
    private CompositeAtomService compositeAtomService;

    @Override
    protected CompletableFuture<CouponResult> doFetch() {
        try{
            DealGroupIdMapper dealGroupIdResult = getDependencyResult(DealGroupIdMapperFetcher.class);

            BatchExProxyCouponRequest batchExProxyCouponRequest = buildBatchExProxyCouponRequest(request, dealGroupIdResult);
            return compositeAtomService.queryExcludeProxyCouponList(batchExProxyCouponRequest).thenApply(res->{
                CouponResult result = new CouponResult();
                result.setExProxyCouponResponseDTO(res);
                return result;
            });
        }catch (Exception e){
            log.error("CouponFetcher.doFetch error", e);
            return CompletableFuture.completedFuture(new CouponResult());
        }
    }

    private BatchExProxyCouponRequest buildBatchExProxyCouponRequest(ProductDetailPageRequest pageRequest, DealGroupIdMapper dealGroupIdResult) {
        BatchExProxyCouponRequest request = new BatchExProxyCouponRequest();
        pageRequest.getClientTypeEnum();
        if(pageRequest.getClientTypeEnum().isMtClientType()) {
            request.setPlatform(PlatformEnum.MT.getCode());
            request.setBizType(BizIdType.MT_DEAL_GROUP.getCode());
            request.setBizId( pageRequest.getProductId());
            request.setMtUserId(pageRequest.getMtUserId());//已确认判断平台后再使用
            request.setMtShopId(pageRequest.getPoiId());
        } else {
            request.setPlatform(PlatformEnum.DP.getCode());
            request.setBizType(BizIdType.DEAL_GROUP_ID.getCode());
            request.setDpBizId( pageRequest.getProductId());
            request.setDpUserId(pageRequest.getDpUserId());
            request.setDpShopId(pageRequest.getPoiId());
        }

        request.setClientType(getClientTypeEnum(pageRequest.getClientTypeEnum()).getCode());
        request.setCityId(pageRequest.getCityId());
        request.setUserLatitude(pageRequest.getUserLat());
        request.setUserLongitude(pageRequest.getUserLng());
        request.setPageSource(pageRequest.getPageSource());
        request.setNeedDiscountCoupon(true);
        request.setExProxyCouponContext(buildExProxyCouponContext(pageRequest, dealGroupIdResult));

        return request;
    }

    private com.sankuai.nib.mkt.common.base.enums.propertyValueType.ClientTypeEnum getClientTypeEnum(ClientTypeEnum  clientType) {
        boolean isIos = IOS.equals(request.getMobileOSType());

        switch (clientType) {
            case MT_APP:
                return isIos ? com.sankuai.nib.mkt.common.base.enums.propertyValueType.ClientTypeEnum.IPHONE : com.sankuai.nib.mkt.common.base.enums.propertyValueType.ClientTypeEnum.ANDROID;
            case DP_APP:
                return isIos ? com.sankuai.nib.mkt.common.base.enums.propertyValueType.ClientTypeEnum.DP_IPHONE : com.sankuai.nib.mkt.common.base.enums.propertyValueType.ClientTypeEnum.DP_ANDROID;
            case MT_WX:
                return com.sankuai.nib.mkt.common.base.enums.propertyValueType.ClientTypeEnum.WE_CHAT_APPLET;
            case DP_WX:
                return com.sankuai.nib.mkt.common.base.enums.propertyValueType.ClientTypeEnum.DP_WE_CHAT_APPLET;
            case UNKNOWN:
            default:
                return com.sankuai.nib.mkt.common.base.enums.propertyValueType.ClientTypeEnum.PC;
        }
    }

    private ExProxyCouponContext buildExProxyCouponContext(ProductDetailPageRequest request, DealGroupIdMapper dealGroupIdResult) {
        Long dpDealGroupId = Optional.ofNullable(dealGroupIdResult).map(DealGroupIdMapper::getDpDealGroupId).orElse(0L);

        ExProxyCouponContext exProxyCouponContext = new ExProxyCouponContext();
        exProxyCouponContext.setDpId(String.valueOf(dpDealGroupId));
        exProxyCouponContext.setVersion(request.getShepherdGatewayParam().getAppVersion());
        exProxyCouponContext.setRequestURI(request.getShepherdGatewayParam().getRequestURI());
        exProxyCouponContext.setUserAgent(request.getShepherdGatewayParam().getUserAgent());
        exProxyCouponContext.setUserIp(request.getShepherdGatewayParam().getUserIp());
        exProxyCouponContext.setCx(request.getCx());

        return exProxyCouponContext;
    }
}
