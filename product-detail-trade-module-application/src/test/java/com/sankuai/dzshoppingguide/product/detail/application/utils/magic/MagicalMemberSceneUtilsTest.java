package com.sankuai.dzshoppingguide.product.detail.application.utils.magic;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.cityId.CityIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.pinpool.SimilarDealCacheResult;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.general.product.query.center.client.dto.DealGroupBasicDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class MagicalMemberSceneUtilsTest {

    @Mock
    private ProductDetailPageRequest request;

    @Mock
    private ProductBaseInfo productBaseInfo;

    @Mock
    private SimilarDealCacheResult similarDealCacheResult;

    @Mock
    private DealGroupCategoryDTO categoryDTO;

    @Mock
    private DealGroupBasicDTO basicDTO;

    @Mock
    private CityIdMapper cityIdMapper;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        // Setup default behavior for productBaseInfo
        when(productBaseInfo.getCategory()).thenReturn(categoryDTO);
        when(productBaseInfo.getBasic()).thenReturn(basicDTO);
        // Setup default behavior for categoryDTO
        when(categoryDTO.getCategoryId()).thenReturn(1L);
        when(categoryDTO.getServiceTypeId()).thenReturn(1L);
        // Setup default behavior for basicDTO
        // Not a times deal by default
        when(basicDTO.getTradeType()).thenReturn(0);
        // Setup default behavior for similarDealCacheResult
    }
}
