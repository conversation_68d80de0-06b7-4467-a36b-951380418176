package com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.ext.param.handler;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.product.detail.RequestCustomParamEnum;
import com.sankuai.dz.product.detail.gateway.spi.dto.CustomParam;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.enums.OrderPageExtParamEnums;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.ext.param.request.ExtParamBuilderRequest;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * Unit tests for {@link PassParamExtParamBuilder#buildKey()} method.
 *
 * <p>This test class verifies all possible scenarios for the buildKey() method,
 * although the method is simple with no branches or complex logic.</p>
 */
@DisplayName("PassParamExtParamBuilder buildKey Tests")
@ExtendWith(MockitoExtension.class)
class PassParamExtParamBuilderTest {

    private final PassParamExtParamBuilder builder = new PassParamExtParamBuilder();

    @Mock
    private ProductDetailPageRequest request;

    @Mock
    private ExtParamBuilderRequest builderRequest;

    /**
     * Test that buildKey() returns the expected enum value 'pass_param'.
     */
    @Test
    @DisplayName("Should return pass_param enum when buildKey is called")
    public void testBuildKeyReturnsPassParamEnum() {
        // arrange
        PassParamExtParamBuilder builder = new PassParamExtParamBuilder();
        // act
        OrderPageExtParamEnums result = builder.buildKey();
        // assert
        assertEquals(OrderPageExtParamEnums.pass_param, result, "buildKey() should return OrderPageExtParamEnums.pass_param");
    }

    /**
     * Test that buildKey() returns a non-null value.
     */
    @Test
    @DisplayName("Should return non-null enum when buildKey is called")
    public void testBuildKeyReturnsNonNull() {
        // arrange
        PassParamExtParamBuilder builder = new PassParamExtParamBuilder();
        // act
        OrderPageExtParamEnums result = builder.buildKey();
        // assert
        assertNotNull(result, "buildKey() should not return null");
    }

    /**
     * Test that buildKey() returns an enum with the correct description.
     */
    @Test
    @DisplayName("Should return enum with correct description when buildKey is called")
    public void testBuildKeyReturnsEnumWithCorrectDescription() {
        // arrange
        PassParamExtParamBuilder builder = new PassParamExtParamBuilder();
        // act
        OrderPageExtParamEnums result = builder.buildKey();
        // assert
        assertEquals("订单归因，交易侧定义", result.getDesc(), "Returned enum should have correct description");
    }

    /**
     * Test that buildKey() consistently returns the same enum value.
     */
    @Test
    @DisplayName("Should consistently return same enum value on multiple calls")
    public void testBuildKeyReturnsConsistentValue() {
        // arrange
        PassParamExtParamBuilder builder = new PassParamExtParamBuilder();
        // act
        OrderPageExtParamEnums firstCall = builder.buildKey();
        OrderPageExtParamEnums secondCall = builder.buildKey();
        // assert
        assertSame(firstCall, secondCall, "buildKey() should consistently return the same enum instance");
    }

    /**
     * Test when customParam is null in ProductDetailPageRequest
     */
    @Test
    public void testDoBuildExtParamWhenCustomParamIsNull() throws Exception {
        // arrange
        when(request.getCustomParam(RequestCustomParamEnum.pass_param)).thenReturn(null);
        // act
        String result = builder.doBuildExtParam(request, builderRequest);
        // assert
        assertNull(result);
        verify(request).getCustomParam(RequestCustomParamEnum.pass_param);
    }

    /**
     * Test when pass_param is null
     */
    @Test
    public void testDoBuildExtParamWhenPassParamIsNull() throws Exception {
        // arrange
        CustomParam customParam = mock(CustomParam.class);
        when(request.getCustomParam(RequestCustomParamEnum.pass_param)).thenReturn(null);
        // act
        String result = builder.doBuildExtParam(request, builderRequest);
        // assert
        assertNull(result);
        verify(request).getCustomParam(RequestCustomParamEnum.pass_param);
    }

    /**
     * Test when pass_param is empty string
     */
    @Test
    public void testDoBuildExtParamWhenPassParamIsEmpty() throws Exception {
        // arrange
        when(request.getCustomParam(RequestCustomParamEnum.pass_param)).thenReturn("");
        // act
        String result = builder.doBuildExtParam(request, builderRequest);
        // assert
        assertNull(result);
        verify(request).getCustomParam(RequestCustomParamEnum.pass_param);
    }

    /**
     * Test when pass_param is blank string
     */
    @Test
    public void testDoBuildExtParamWhenPassParamIsBlank() throws Exception {
        // arrange
        when(request.getCustomParam(RequestCustomParamEnum.pass_param)).thenReturn("   ");
        // act
        String result = builder.doBuildExtParam(request, builderRequest);
        // assert
        assertNull(result);
        verify(request).getCustomParam(RequestCustomParamEnum.pass_param);
    }

    /**
     * Test when pass_param is "undefined"
     */
    @Test
    public void testDoBuildExtParamWhenPassParamIsUndefined() throws Exception {
        // arrange
        when(request.getCustomParam(RequestCustomParamEnum.pass_param)).thenReturn("undefined");
        // act
        String result = builder.doBuildExtParam(request, builderRequest);
        // assert
        assertNull(result);
        verify(request).getCustomParam(RequestCustomParamEnum.pass_param);
    }

    /**
     * Test when pass_param has a valid value
     */
    @Test
    public void testDoBuildExtParamWhenPassParamHasValue() throws Exception {
        // arrange
        String expectedValue = "testPassParam";
        when(request.getCustomParam(RequestCustomParamEnum.pass_param)).thenReturn(expectedValue);
        // act
        String result = builder.doBuildExtParam(request, builderRequest);
        // assert
        assertEquals(expectedValue, result);
        verify(request).getCustomParam(RequestCustomParamEnum.pass_param);
    }

    /**
     * Test when pass_param has a valid value with spaces
     */
    @Test
    public void testDoBuildExtParamWhenPassParamHasValueWithSpaces() throws Exception {
        // arrange
        String expectedValue = "  testPassParam  ";
        when(request.getCustomParam(RequestCustomParamEnum.pass_param)).thenReturn(expectedValue);
        // act
        String result = builder.doBuildExtParam(request, builderRequest);
        // assert
        assertEquals(expectedValue, result);
        verify(request).getCustomParam(RequestCustomParamEnum.pass_param);
    }
}
