package com.sankuai.dzshoppingguide.product.detail.application.fetcher.tying;

import com.dianping.deal.sales.common.datatype.SalesDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TyingPaddingResult extends FetcherReturnValueDTO {
    private Integer mainDealGroupId;
    private List<CombinationDealInfo> combinationDealInfoList;
    private Map<Integer, PriceDisplayDTO> dealIdPriceDisplayMap;
    private Map<Integer, List<String>> productId2SubTitleMap;
    private Map<Integer, DealGroupDTO> dealIdDealGroupDTOMap;
    private Map<Integer, SalesDisplayDTO> productId2SaleMap;
    private Map<Integer, String> dealOrderPageUrlMap;
    private Map<Integer, CombinationDealInfo> bindingDealCombinationInfoMap;
    private String priceSecretInfo;
}
