package com.sankuai.dzshoppingguide.product.detail.application.service.promo;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;
import com.dianping.tgc.open.entity.PromoCouponButton;
import com.dianping.tgc.open.entity.PromoCouponInfo;
import com.dianping.tpfun.product.api.sku.pintuan.dto.PinProductBrief;
import com.google.common.collect.Lists;
import com.sankuai.dealuser.price.display.api.enums.PromotionExplanatoryTagEnum;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import com.sankuai.dealuser.price.display.api.model.PromoIdentity;
import com.sankuai.dz.product.detail.gateway.spi.dto.ShepherdGatewayParam;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.builder.promodetail.dto.PromotionDetailParams;
import com.sankuai.dzshoppingguide.product.detail.application.builder.promodetail.enums.CouponStatusEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.promodetail.enums.CouponStyleEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.promodetail.enums.CouponTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.promodetail.enums.PromotionTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.best.shop.ShopInfo;
import com.sankuai.dzshoppingguide.product.detail.application.utils.ButtonStyleHelper;
import com.sankuai.dzshoppingguide.product.detail.application.utils.UrlHelper;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.Icon;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.CanInflateEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.CouponValidStatusEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.InflateStatusEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.MagicCouponTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.promo.vo.CouponItemInfoDTO;
import com.sankuai.dzshoppingguide.product.detail.spi.promo.vo.CouponModule;
import com.sankuai.dzshoppingguide.product.detail.spi.promo.vo.CouponTitle;
import com.sankuai.dzshoppingguide.product.detail.spi.promo.vo.PopUpContentModule;
import com.sankuai.dzshoppingguide.product.detail.spi.promo.vo.PromotionBarModule;
import com.sankuai.nibmkt.promotion.api.common.enums.PromotionPropertyEnum;
import com.sankuai.nibmkt.promotion.api.common.enums.PromotionTextEnum;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.sinai.data.api.dto.MtPoiDTO;
import java.math.BigDecimal;
import java.util.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;
import com.dianping.product.shelf.common.dto.activity.ActivityDetailDTO;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.LeadsInfoFetcher.LeadsInfoResult;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.cepinpool.dto.CostEffectivePinTuan;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.dealgift.DealGiftResult;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.utils.PriceHelper;
import com.sankuai.dzshoppingguide.product.detail.spi.promo.vo.DealGift;
import com.sankuai.dzshoppingguide.product.detail.spi.promo.vo.PromoDetailModule;
import com.sankuai.general.product.query.center.client.dto.DealGroupBasicDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.statesubsidies.c.thrift.constant.QualificationStatusEnum;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@ExtendWith(MockitoExtension.class)
class PromoDetailServiceSubtractPromoDescTest {

    @InjectMocks
    private PromoDetailService promoDetailService;

    @Mock
    private PromoDetailModuleBuilderService promoDetailModuleBuilderService;

    @Mock
    private PromotionDetailBuilderService promotionDetailBuilderService;

    @Mock
    private ButtonStyleHelper buttonStyleHelper;

    @Mock
    private LeafRepository leafRepository;

    private PromoDTO basePromoDTO;

    @BeforeEach
    void setUp() {
        basePromoDTO = new PromoDTO();
        basePromoDTO.setStartTime(new Date(System.currentTimeMillis() - 1000));
        basePromoDTO.setEndTime(new Date(System.currentTimeMillis() + 10000));
        basePromoDTO.setAmount(new BigDecimal("10.00"));
        basePromoDTO.setCouponId("testCouponId");
        basePromoDTO.setCouponGroupId("testGroupId");
        basePromoDTO.setCouponTitle("Test Coupon");
        basePromoDTO.setMinConsumptionAmount(new BigDecimal("100.00"));
        basePromoDTO.setIdentity(new PromoIdentity(1L, 1, "Test Promo"));
        // Initialize default maps
        Map<String, String> otherInfo = new HashMap<>();
        otherInfo.put(PromotionPropertyEnum.ASSET_TYPE.getValue(), "0");
        basePromoDTO.setPromotionOtherInfoMap(otherInfo);
        Map<String, String> displayText = new HashMap<>();
        basePromoDTO.setPromotionDisplayTextMap(displayText);
        List<Integer> tags = new ArrayList<>();
        basePromoDTO.setPromotionExplanatoryTags(tags);
    }

    private PromoDTO createPayCouponPromoDTO() {
        PromoDTO promoDTO = cloneBasePromoDTO();
        Map<String, String> otherInfo = new HashMap<>();
        otherInfo.put(PromotionPropertyEnum.CAN_INFLATE.getValue(), "true");
        otherInfo.put(PromotionPropertyEnum.AFTER_INFLATE.getValue(), "false");
        otherInfo.put(PromotionPropertyEnum.ASSET_TYPE.getValue(), "0");
        otherInfo.put(PromotionPropertyEnum.MAX_INFLATE_MONEY.getValue(), "2000");
        promoDTO.setPromotionOtherInfoMap(otherInfo);
        Map<String, String> displayText = new HashMap<>();
        displayText.put(PromotionTextEnum.topLeftIcon.getValue(), "icon_url");
        displayText.put(PromotionTextEnum.promotionButtonText.getValue(), "Inflate");
        promoDTO.setPromotionDisplayTextMap(displayText);
        List<Integer> tags = new ArrayList<>();
        tags.add(PromotionExplanatoryTagEnum.MAGICAL_MEMBER_COUPON.getCode());
        promoDTO.setPromotionExplanatoryTags(tags);
        return promoDTO;
    }

    private PromoDTO createFreeCouponPromoDTO() {
        PromoDTO promoDTO = cloneBasePromoDTO();
        Map<String, String> otherInfo = new HashMap<>();
        otherInfo.put(PromotionPropertyEnum.CAN_INFLATE.getValue(), "true");
        otherInfo.put(PromotionPropertyEnum.AFTER_INFLATE.getValue(), "true");
        otherInfo.put(PromotionPropertyEnum.ASSET_TYPE.getValue(), "0");
        otherInfo.put(PromotionPropertyEnum.QUERY_INFLATE_FLAG.getValue(), "true");
        promoDTO.setPromotionOtherInfoMap(otherInfo);
        List<Integer> tags = new ArrayList<>();
        tags.add(PromotionExplanatoryTagEnum.MAGICAL_MEMBER_COUPON_FREE.getCode());
        promoDTO.setPromotionExplanatoryTags(tags);
        return promoDTO;
    }

    private PromoDTO createTspCouponPromoDTO() {
        PromoDTO promoDTO = cloneBasePromoDTO();
        Map<String, String> otherInfo = new HashMap<>();
        otherInfo.put(PromotionPropertyEnum.ASSET_TYPE.getValue(), "1");
        otherInfo.put(PromotionPropertyEnum.TSP_COUPON_ID.getValue(), "tsp123");
        otherInfo.put(PromotionPropertyEnum.TSP_COUPON_GROUP_ID.getValue(), "tspGroup123");
        promoDTO.setPromotionOtherInfoMap(otherInfo);
        List<Integer> tags = new ArrayList<>();
        promoDTO.setPromotionExplanatoryTags(tags);
        return promoDTO;
    }

    private PromoDTO createExpiredCouponPromoDTO() {
        PromoDTO promoDTO = cloneBasePromoDTO();
        promoDTO.setStartTime(new Date(System.currentTimeMillis() - 20000));
        promoDTO.setEndTime(new Date(System.currentTimeMillis() - 10000));
        Map<String, String> otherInfo = new HashMap<>();
        otherInfo.put(PromotionPropertyEnum.ASSET_TYPE.getValue(), "0");
        promoDTO.setPromotionOtherInfoMap(otherInfo);
        List<Integer> tags = new ArrayList<>();
        promoDTO.setPromotionExplanatoryTags(tags);
        return promoDTO;
    }

    private PromoDTO createUnknownTypeCouponPromoDTO() {
        PromoDTO promoDTO = cloneBasePromoDTO();
        Map<String, String> otherInfo = new HashMap<>();
        otherInfo.put(PromotionPropertyEnum.ASSET_TYPE.getValue(), "0");
        promoDTO.setPromotionOtherInfoMap(otherInfo);
        promoDTO.setPromotionExplanatoryTags(new ArrayList<>());
        return promoDTO;
    }

    private PromoDTO cloneBasePromoDTO() {
        PromoDTO promoDTO = new PromoDTO();
        promoDTO.setStartTime(basePromoDTO.getStartTime());
        promoDTO.setEndTime(basePromoDTO.getEndTime());
        promoDTO.setAmount(basePromoDTO.getAmount());
        promoDTO.setCouponId(basePromoDTO.getCouponId());
        promoDTO.setCouponGroupId(basePromoDTO.getCouponGroupId());
        promoDTO.setCouponTitle(basePromoDTO.getCouponTitle());
        promoDTO.setMinConsumptionAmount(basePromoDTO.getMinConsumptionAmount());
        promoDTO.setIdentity(basePromoDTO.getIdentity());
        // Initialize default maps
        Map<String, String> otherInfo = new HashMap<>();
        otherInfo.put(PromotionPropertyEnum.ASSET_TYPE.getValue(), "0");
        promoDTO.setPromotionOtherInfoMap(otherInfo);
        Map<String, String> displayText = new HashMap<>();
        promoDTO.setPromotionDisplayTextMap(displayText);
        List<Integer> tags = new ArrayList<>();
        promoDTO.setPromotionExplanatoryTags(tags);
        return promoDTO;
    }

    private ShopInfo createShopInfo() {
        ShopInfo shopInfo = new ShopInfo();
        DpPoiDTO dpPoiDTO = new DpPoiDTO();
        dpPoiDTO.setUuid("test-uuid");
        dpPoiDTO.setShopId(1234L);
        shopInfo.setDpPoiDTO(dpPoiDTO);
        MtPoiDTO mtPoiDTO = new MtPoiDTO();
        mtPoiDTO.setMtPoiId(5678L);
        shopInfo.setMtPoiDTO(mtPoiDTO);
        return shopInfo;
    }

    private ProductDetailPageRequest createPageRequest(int clientType) {
        ProductDetailPageRequest pageRequest = new ProductDetailPageRequest();
        pageRequest.setClientType(clientType);
        // Initialize ShepherdGatewayParam
        ShepherdGatewayParam gatewayParam = new ShepherdGatewayParam();
        gatewayParam.setDpUserId(1001L);
        gatewayParam.setMtUserId(2001L);
        gatewayParam.setDeviceId("test-device-id");
        gatewayParam.setUnionid("test-union-id");
        gatewayParam.setMobileOSType("ios");
        gatewayParam.setAppVersion("1.0.0");
        gatewayParam.setMtsiflag("test-flag");
        pageRequest.setShepherdGatewayParam(gatewayParam);
        return pageRequest;
    }

    /**
     * Test case where both promoDesc and promoTag are blank
     */
    @Test
    public void testSubtractPromoDescBothBlank() {
        // arrange
        String promoDesc = "";
        String promoTag = "";
        // act
        String result = promoDetailService.subtractPromoDesc(promoDesc, promoTag);
        // assert
        assertEquals("", result);
    }

    /**
     * Test case where promoTag is blank but promoDesc is not
     */
    @Test
    public void testSubtractPromoDescBlankTag() {
        // arrange
        String promoDesc = "Some promo description";
        String promoTag = "";
        // act
        String result = promoDetailService.subtractPromoDesc(promoDesc, promoTag);
        // assert
        assertEquals("Some promo description", result);
    }

    /**
     * Test case where promoDesc is blank but promoTag is not
     */
    @Test
    public void testSubtractPromoDescBlankDesc() {
        // arrange
        String promoDesc = "";
        String promoTag = "Tag";
        // act
        String result = promoDetailService.subtractPromoDesc(promoDesc, promoTag);
        // assert
        assertEquals("", result);
    }

    /**
     * Test case where promoDesc starts with promoTag + "，"
     */
    @Test
    public void testSubtractPromoDescStartsWithTag() {
        // arrange
        String promoTag = "Tag";
        String promoDesc = "Tag，Some description";
        // act
        String result = promoDetailService.subtractPromoDesc(promoDesc, promoTag);
        // assert
        assertEquals("Some description", result);
    }

    /**
     * Test case where promoDesc does not start with promoTag + "，"
     */
    @Test
    public void testSubtractPromoDescNotStartWithTag() {
        // arrange
        String promoTag = "Tag";
        String promoDesc = "Some description";
        // act
        String result = promoDetailService.subtractPromoDesc(promoDesc, promoTag);
        // assert
        assertEquals("Some description", result);
    }

    /**
     * Test case where promoDesc contains promoTag but not at the start
     */
    @Test
    public void testSubtractPromoDescContainsTagNotAtStart() {
        // arrange
        String promoTag = "Tag";
        String promoDesc = "Description with Tag in the middle";
        // act
        String result = promoDetailService.subtractPromoDesc(promoDesc, promoTag);
        // assert
        assertEquals("Description with Tag in the middle", result);
    }

    /**
     * Test case where promoDesc and promoTag are identical
     */
    @Test
    public void testSubtractPromoDescIdentical() {
        // arrange
        String promoTag = "Tag";
        String promoDesc = "Tag";
        // act
        String result = promoDetailService.subtractPromoDesc(promoDesc, promoTag);
        // assert
        assertEquals("Tag", result);
    }

    /**
     * Test when input map is null
     * Expected: returns 0 due to NullPointerException being caught
     */
    @Test
    public void testGetCouponNum_WhenMapIsNull() throws Throwable {
        // arrange
        Map<String, String> promotionOtherInfoMap = null;
        // act
        int result = promoDetailService.getCouponNum(promotionOtherInfoMap);
        // assert
        assertEquals(0, result);
    }

    /**
     * Test when input map is empty
     * Expected: returns 1 as couponNum will be null
     */
    @Test
    public void testGetCouponNum_WhenMapIsEmpty() throws Throwable {
        // arrange
        Map<String, String> promotionOtherInfoMap = new HashMap<>();
        // act
        int result = promoDetailService.getCouponNum(promotionOtherInfoMap);
        // assert
        assertEquals(1, result);
    }

    /**
     * Test when map contains valid coupon number
     */
    @Test
    public void testGetCouponNum_WhenValidCouponNumber() throws Throwable {
        // arrange
        Map<String, String> promotionOtherInfoMap = new HashMap<>();
        promotionOtherInfoMap.put(PromotionPropertyEnum.COUPON_AGGREGATE_NUM.getValue(), "5");
        // act
        int result = promoDetailService.getCouponNum(promotionOtherInfoMap);
        // assert
        assertEquals(5, result);
    }

    /**
     * Test when coupon number value is null
     * Expected: returns 1 as per the method logic
     */
    @Test
    public void testGetCouponNum_WhenCouponNumberIsNull() throws Throwable {
        // arrange
        Map<String, String> promotionOtherInfoMap = new HashMap<>();
        promotionOtherInfoMap.put(PromotionPropertyEnum.COUPON_AGGREGATE_NUM.getValue(), null);
        // act
        int result = promoDetailService.getCouponNum(promotionOtherInfoMap);
        // assert
        assertEquals(1, result);
    }

    /**
     * Test when coupon number is invalid (non-numeric string)
     * Expected: returns 0 due to NumberFormatException being caught
     */
    @Test
    public void testGetCouponNum_WhenInvalidCouponNumber() throws Throwable {
        // arrange
        Map<String, String> promotionOtherInfoMap = new HashMap<>();
        promotionOtherInfoMap.put(PromotionPropertyEnum.COUPON_AGGREGATE_NUM.getValue(), "abc");
        // act
        int result = promoDetailService.getCouponNum(promotionOtherInfoMap);
        // assert
        assertEquals(0, result);
    }

    /**
     * Test when coupon number is negative
     * Expected: returns the negative number as the method doesn't validate the sign
     */
    @Test
    public void testGetCouponNum_WhenNegativeCouponNumber() throws Throwable {
        // arrange
        Map<String, String> promotionOtherInfoMap = new HashMap<>();
        promotionOtherInfoMap.put(PromotionPropertyEnum.COUPON_AGGREGATE_NUM.getValue(), "-5");
        // act
        int result = promoDetailService.getCouponNum(promotionOtherInfoMap);
        // assert
        assertEquals(-5, result);
    }

    /**
     * 测试空输入参数
     * 当优惠券列表为空时，应返回空列表
     */
    @Test
    public void testBuildNormalCouponWithNullInput() throws Throwable {
        // arrange
        List<PromoCouponInfo> optAndFinancialCoupons = Collections.emptyList();
        PriceDisplayDTO dealPromDisplayDTO = new PriceDisplayDTO();
        // act
        List<CouponModule> result = promoDetailService.buildNormalCoupon(optAndFinancialCoupons, dealPromDisplayDTO);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试包含已使用优惠券的情况
     */
    @Test
    public void testBuildNormalCouponWithUsedPromos() throws Throwable {
        // arrange
        PriceDisplayDTO priceDisplay = new PriceDisplayDTO();
        PromoDTO usedPromo = new PromoDTO();
        usedPromo.setCouponGroupId("1");
        priceDisplay.setUsedPromos(Collections.singletonList(usedPromo));
        PromoCouponInfo coupon = new PromoCouponInfo();
        coupon.setStatus(0);
        coupon.setCouponGroupId(1L);
        coupon.setCouponType(0);
        coupon.setTitle("Test Coupon");
        List<PromoCouponInfo> coupons = Collections.singletonList(coupon);
        // act
        List<CouponModule> result = promoDetailService.buildNormalCoupon(coupons, priceDisplay);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试跳转链接类型的优惠券
     */
    @Test
    public void testBuildNormalCouponWithJumpUrlAction() throws Throwable {
        // arrange
        PriceDisplayDTO priceDisplay = new PriceDisplayDTO();
        priceDisplay.setUsedPromos(null);
        PromoCouponButton button = new PromoCouponButton();
        button.setActionType(1);
        button.setClickUrl("http://test.com");
        button.setTitle("Click Me");
        PromoCouponInfo coupon = new PromoCouponInfo();
        coupon.setStatus(0);
        coupon.setCouponGroupId(1L);
        coupon.setCouponType(0);
        coupon.setTitle("Test Coupon");
        coupon.setPromoCouponButton(button);
        List<PromoCouponInfo> coupons = Collections.singletonList(coupon);
        // act
        List<CouponModule> result = promoDetailService.buildNormalCoupon(coupons, priceDisplay);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(CouponStatusEnum.JUMP_URL.getCode(), result.get(0).getCouponStatus());
        assertEquals("Click Me", result.get(0).getActionButton().getButtonText());
        assertEquals("http://test.com", result.get(0).getActionButton().getJumpUrl());
    }

    @Test
    void testBuildCouponItemWithNullPromoDTO() throws Throwable {
        assertThrows(NullPointerException.class, () -> promoDetailService.buildCouponItem(null, 123L));
    }

    @Test
    void testBuildCouponItemValidPayCoupon() throws Throwable {
        // arrange
        PromoDTO promoDTO = createPayCouponPromoDTO();
        // act
        CouponItemInfoDTO result = promoDetailService.buildCouponItem(promoDTO, 123L);
        // assert
        assertNotNull(result);
        assertEquals("testCouponId", result.getCouponCode());
        assertEquals("testGroupId", result.getApplyId());
        assertEquals("10.00", result.getCouponAmount());
        assertEquals("Test Coupon", result.getCouponName());
        assertEquals(MagicCouponTypeEnum.PAY_COUPON.getCode(), result.getCouponType());
        assertEquals(CanInflateEnum.CAN_INFLATE.getCode(), result.getCanInflate());
        assertEquals(InflateStatusEnum.NO_INFLATE.getCode(), result.getInflatedStatus());
        assertEquals(1, result.getValidStatus());
    }

    @Test
    void testBuildCouponItemFreeCouponAfterInflate() throws Throwable {
        // arrange
        PromoDTO promoDTO = createFreeCouponPromoDTO();
        // act
        CouponItemInfoDTO result = promoDetailService.buildCouponItem(promoDTO, 123L);
        // assert
        assertNotNull(result);
        assertEquals(MagicCouponTypeEnum.FREE_COUPON.getCode(), result.getCouponType());
        assertEquals(InflateStatusEnum.AFTER_INFLATE.getCode(), result.getInflatedStatus());
        assertEquals("满100.00可用", result.getThresholdDesc());
        assertEquals(new BigDecimal("100.00"), result.getThresholdAmount());
    }

    @Test
    void testBuildCouponItemTspCoupon() throws Throwable {
        // arrange
        PromoDTO promoDTO = createTspCouponPromoDTO();
        // act
        CouponItemInfoDTO result = promoDetailService.buildCouponItem(promoDTO, 123L);
        // assert
        assertNotNull(result);
        assertEquals(1, result.getAssetType());
        assertEquals("tsp123", result.getCouponCode());
        assertEquals("tspGroup123", result.getApplyId());
    }

    @Test
    void testBuildCouponItemExpired() throws Throwable {
        // arrange
        PromoDTO promoDTO = createExpiredCouponPromoDTO();
        // act
        CouponItemInfoDTO result = promoDetailService.buildCouponItem(promoDTO, 123L);
        // assert
        assertNotNull(result);
        assertEquals(CouponValidStatusEnum.UN_VALID.getCode(), result.getValidStatus());
    }

    @Test
    void testBuildCouponItemUnknownType() throws Throwable {
        // arrange
        PromoDTO promoDTO = createUnknownTypeCouponPromoDTO();
        // act
        CouponItemInfoDTO result = promoDetailService.buildCouponItem(promoDTO, 123L);
        // assert
        assertNotNull(result);
        assertEquals(MagicCouponTypeEnum.UN_KNOWN.getCode(), result.getCouponType());
    }

    /**
     * Test when normalPriceDisplay is null
     */
    @Test
    void testBuildBuyMoreReduction_NullNormalPriceDisplay() throws Throwable {
        // arrange
        PromotionDetailParams params = new PromotionDetailParams();
        PriceDisplayDTO normalPriceDisplay = new PriceDisplayDTO();
        params.setNormalPriceDisplayDTO(normalPriceDisplay);
        // act & assert
        assertNull(promoDetailService.buildBuyMoreReduction(params), "Should return null when morePromos is null");
    }

    /**
     * Test when morePromos list is empty
     */
    @Test
    void testBuildBuyMoreReduction_EmptyMorePromos() throws Throwable {
        // arrange
        PromotionDetailParams params = new PromotionDetailParams();
        PriceDisplayDTO normalPriceDisplay = new PriceDisplayDTO();
        normalPriceDisplay.setMorePromos(Collections.emptyList());
        params.setNormalPriceDisplayDTO(normalPriceDisplay);
        // act & assert
        assertNull(promoDetailService.buildBuyMoreReduction(params), "Should return null when morePromos is empty");
    }

    /**
     * Test when no matching PromoDTO found in morePromos
     */
    @Test
    void testBuildBuyMoreReduction_NoMatchingPromo() throws Throwable {
        // arrange
        PromotionDetailParams params = new PromotionDetailParams();
        PriceDisplayDTO normalPriceDisplay = new PriceDisplayDTO();
        List<PromoDTO> promos = new ArrayList<>();
        PromoDTO promoDTO = mock(PromoDTO.class);
        PromoIdentity promoIdentity = mock(PromoIdentity.class);
        when(promoIdentity.getPromoShowType()).thenReturn("DIFFERENT_TYPE");
        when(promoDTO.getIdentity()).thenReturn(promoIdentity);
        promos.add(promoDTO);
        normalPriceDisplay.setMorePromos(promos);
        params.setNormalPriceDisplayDTO(normalPriceDisplay);
        // act & assert
        assertNull(promoDetailService.buildBuyMoreReduction(params), "Should return null when no matching promo found");
    }

    /**
     * Test successful case with valid matching PromoDTO
     */
    @Test
    void testBuildBuyMoreReduction_Success() throws Throwable {
        // arrange
        PromotionDetailParams params = new PromotionDetailParams();
        PriceDisplayDTO normalPriceDisplay = new PriceDisplayDTO();
        List<PromoDTO> promos = new ArrayList<>();
        PromoDTO promoDTO = mock(PromoDTO.class);
        PromoIdentity promoIdentity = mock(PromoIdentity.class);
        when(promoIdentity.getPromoShowType()).thenReturn(CouponTypeEnum.BUY_MORE_REDUCTION.getShowType());
        when(promoDTO.getIdentity()).thenReturn(promoIdentity);
        when(promoDTO.getExtendDesc()).thenReturn("Test Description");
        when(promoDTO.getTag()).thenReturn("Test Tag");
        promos.add(promoDTO);
        normalPriceDisplay.setMorePromos(promos);
        params.setNormalPriceDisplayDTO(normalPriceDisplay);
        // act
        CouponModule result = promoDetailService.buildBuyMoreReduction(params);
        // assert
        assertNotNull(result, "Should return non-null CouponModule");
        assertEquals(CouponTypeEnum.BUY_MORE_REDUCTION.getCode(), result.getCouponType());
        assertEquals(CouponStyleEnum.SIMPLE_STYLE.getCode(), result.getCouponStyle());
        assertEquals(PromotionTypeEnum.PROMOTION_ACTIVITY.getCode(), result.getPromotionType());
        List<CouponTitle> couponTitles = result.getCouponTitle();
        assertNotNull(couponTitles, "CouponTitle list should not be null");
        assertFalse(couponTitles.isEmpty(), "CouponTitle list should not be empty");
        assertEquals("Test Description", couponTitles.get(0).getContent());
        PromotionBarModule barModule = result.getPromotionBarModule();
        assertNotNull(barModule, "PromotionBarModule should not be null");
        assertEquals("Test Tag", barModule.getLabelDesc());
    }

    /**
     * Test with multiple PromoDTO where only one matches
     */
    @Test
    void testBuildBuyMoreReduction_MultiplePromosOneMatch() throws Throwable {
        // arrange
        PromotionDetailParams params = new PromotionDetailParams();
        PriceDisplayDTO normalPriceDisplay = new PriceDisplayDTO();
        List<PromoDTO> promos = new ArrayList<>();
        // Add non-matching promo
        PromoDTO nonMatchingPromo = mock(PromoDTO.class);
        PromoIdentity nonMatchingIdentity = mock(PromoIdentity.class);
        when(nonMatchingIdentity.getPromoShowType()).thenReturn("OTHER_TYPE");
        when(nonMatchingPromo.getIdentity()).thenReturn(nonMatchingIdentity);
        promos.add(nonMatchingPromo);
        // Add matching promo
        PromoDTO matchingPromo = mock(PromoDTO.class);
        PromoIdentity matchingIdentity = mock(PromoIdentity.class);
        when(matchingIdentity.getPromoShowType()).thenReturn(CouponTypeEnum.BUY_MORE_REDUCTION.getShowType());
        when(matchingPromo.getIdentity()).thenReturn(matchingIdentity);
        when(matchingPromo.getExtendDesc()).thenReturn("Test Description");
        when(matchingPromo.getTag()).thenReturn("Test Tag");
        promos.add(matchingPromo);
        normalPriceDisplay.setMorePromos(promos);
        params.setNormalPriceDisplayDTO(normalPriceDisplay);
        // act
        CouponModule result = promoDetailService.buildBuyMoreReduction(params);
        // assert
        assertNotNull(result, "Should return non-null CouponModule");
        assertEquals(CouponTypeEnum.BUY_MORE_REDUCTION.getCode(), result.getCouponType());
        assertEquals("Test Description", result.getCouponTitle().get(0).getContent());
    }

    /**
     * Test normal case with valid pin product brief
     */
    @Test
    public void testBuildMerchantGroupBuyCouponModuleNormalCase() throws Throwable {
        // arrange
        PromotionDetailParams params = new PromotionDetailParams();
        params.setPageRequest(createPageRequest(ClientTypeEnum.MT_APP.getCode()));
        PinProductBrief pinProductBrief = new PinProductBrief();
        pinProductBrief.setPinPersonNum(3);
        pinProductBrief.setPrice(new BigDecimal("50.00"));
        pinProductBrief.setUrl("https://test.com");
        params.setPinProductBrief(pinProductBrief);
        params.setShopInfo(createShopInfo());
        // act
        CouponModule result = promoDetailService.buildMerchantGroupBuyCouponModule(params);
        // assert
        assertNotNull(result);
        assertEquals(CouponTypeEnum.MERCHANT_PIN_TUAN.getCode(), result.getCouponType());
        assertEquals(CouponStyleEnum.MERCHANT_PINTUAN.getCode(), result.getCouponStyle());
        assertEquals(PromotionTypeEnum.MERCHANT_PIN_TUAN.getCode(), result.getPromotionType());
        // Verify title
        List<CouponTitle> titles = result.getCouponTitle();
        assertNotNull(titles);
        assertEquals(3, titles.size());
        assertEquals("3人团，拼成到手", titles.get(0).getContent());
        assertEquals("50", titles.get(1).getContent());
        assertEquals("元", titles.get(2).getContent());
        // Verify subtitle
        assertNotNull(result.getCouponSubTitle());
        assertEquals(1, result.getCouponSubTitle().size());
        assertEquals("未成团自动退款", result.getCouponSubTitle().get(0).getContent());
        // Verify button
        assertNotNull(result.getActionButton());
        assertEquals("发起拼团", result.getActionButton().getButtonText());
        // Verify promotion bar
        assertNotNull(result.getPromotionBarModule());
        assertEquals("拼团", result.getPromotionBarModule().getLabelTheme());
        assertEquals("特惠3人团", result.getPromotionBarModule().getLabelDesc());
    }

    /**
     * Test case when pinProductBrief is null
     */
    @Test
    public void testBuildMerchantGroupBuyCouponModuleNullPinProductBrief() throws Throwable {
        // arrange
        PromotionDetailParams params = new PromotionDetailParams();
        params.setPinProductBrief(null);
        // act & assert
        assertThrows(NullPointerException.class, () -> promoDetailService.buildMerchantGroupBuyCouponModule(params));
    }

    /**
     * Test case with different group sizes
     */
    @Test
    public void testBuildMerchantGroupBuyCouponModuleDifferentGroupSizes() throws Throwable {
        // arrange
        PromotionDetailParams params = new PromotionDetailParams();
        params.setPageRequest(createPageRequest(ClientTypeEnum.MT_APP.getCode()));
        PinProductBrief pinProductBrief = new PinProductBrief();
        // Different group size
        pinProductBrief.setPinPersonNum(2);
        pinProductBrief.setPrice(new BigDecimal("100.00"));
        pinProductBrief.setUrl("https://test.com");
        params.setPinProductBrief(pinProductBrief);
        params.setShopInfo(createShopInfo());
        // act
        CouponModule result = promoDetailService.buildMerchantGroupBuyCouponModule(params);
        // assert
        assertNotNull(result);
        assertNotNull(result.getCouponTitle());
        assertEquals("2人团，拼成到手", result.getCouponTitle().get(0).getContent());
        assertEquals("特惠2人团", result.getPromotionBarModule().getLabelDesc());
    }

    /**
     * Test case with different price formats
     */
    @Test
    public void testBuildMerchantGroupBuyCouponModuleDifferentPriceFormats() throws Throwable {
        // arrange
        PromotionDetailParams params = new PromotionDetailParams();
        params.setPageRequest(createPageRequest(ClientTypeEnum.MT_APP.getCode()));
        PinProductBrief pinProductBrief = new PinProductBrief();
        pinProductBrief.setPinPersonNum(3);
        // Decimal price
        pinProductBrief.setPrice(new BigDecimal("99.99"));
        pinProductBrief.setUrl("https://test.com");
        params.setPinProductBrief(pinProductBrief);
        params.setShopInfo(createShopInfo());
        // act
        CouponModule result = promoDetailService.buildMerchantGroupBuyCouponModule(params);
        // assert
        assertNotNull(result);
        assertNotNull(result.getCouponTitle());
        assertEquals("99.99", result.getCouponTitle().get(1).getContent());
    }

    /**
     * Test case for popup content structure
     */
    @Test
    public void testBuildMerchantGroupBuyCouponModulePopupContent() throws Throwable {
        // arrange
        PromotionDetailParams params = new PromotionDetailParams();
        params.setPageRequest(createPageRequest(ClientTypeEnum.MT_APP.getCode()));
        PinProductBrief pinProductBrief = new PinProductBrief();
        pinProductBrief.setPinPersonNum(3);
        pinProductBrief.setPrice(new BigDecimal("50.00"));
        pinProductBrief.setUrl("https://test.com");
        params.setPinProductBrief(pinProductBrief);
        params.setShopInfo(createShopInfo());
        // act
        CouponModule result = promoDetailService.buildMerchantGroupBuyCouponModule(params);
        // assert
        assertNotNull(result);
        assertNotNull(result.getCouponSubTitleIcon());
        assertNotNull(result.getCouponSubTitleIcon().getPopUpContent());
        PopUpContentModule popUpContent = result.getCouponSubTitleIcon().getPopUpContent();
        assertEquals("拼团规则", popUpContent.getTitle());
        assertNotNull(popUpContent.getContent());
        assertEquals(7, popUpContent.getContent().size());
        // Verify icon
        Icon icon = result.getCouponSubTitleIcon().getIcon();
        assertNotNull(icon);
        assertEquals("https://p0.meituan.net/dztgdetailimages/1be58df81580af07199e9587856fe9321396.png", icon.getIcon());
        assertEquals(12, icon.getIconWidth());
        assertEquals(12, icon.getIconHeight());
    }

    @Test
    public void testBuildDealPromoDetailInfo_PrivateLiveMiniapp() throws Throwable {
        // arrange
        ProductBaseInfo dealGroupBase = mock(ProductBaseInfo.class);
        CostEffectivePinTuan costEffectivePinTuan = mock(CostEffectivePinTuan.class);
        ClientTypeEnum clientType = ClientTypeEnum.MT_LIVE_XCX;
        PriceDisplayDTO dealPromoPrice = mock(PriceDisplayDTO.class);
        PromoDetailModule promoDetailModule = new PromoDetailModule();
        PriceDisplayDTO costEffectivePrice = mock(PriceDisplayDTO.class);
        LeadsInfoResult leadsInfoResult = mock(LeadsInfoResult.class);
        DealGiftResult dealGiftResult = mock(DealGiftResult.class);
        List<DealGift> dealGifts = new ArrayList<>();
        QualificationStatusEnum qualificationStatus = QualificationStatusEnum.BIND;
        // Verify initial state
        assertNull(promoDetailModule.getFinalPrice());
        assertNull(promoDetailModule.getBestPromoDetails());
        // act
        promoDetailService.buildDealPromoDetailInfo(dealGroupBase, costEffectivePinTuan, clientType, dealPromoPrice, promoDetailModule, costEffectivePrice, leadsInfoResult, dealGiftResult, dealGifts, qualificationStatus);
        // assert
        // For private live miniapp, the method should return early without modifying the promoDetailModule
        assertNull(promoDetailModule.getFinalPrice());
        assertNull(promoDetailModule.getBestPromoDetails());
        // Verify no interactions with dependencies
        verifyNoInteractions(dealGroupBase);
        verifyNoInteractions(costEffectivePinTuan);
        verifyNoInteractions(dealPromoPrice);
        verifyNoInteractions(costEffectivePrice);
        verifyNoInteractions(leadsInfoResult);
        verifyNoInteractions(dealGiftResult);
        verifyNoInteractions(buttonStyleHelper);
    }

    @Test
    public void testBuildDealPromoDetailInfo_NullDealPromoPrice() throws Throwable {
        // arrange
        ProductBaseInfo dealGroupBase = mock(ProductBaseInfo.class);
        DealGroupCategoryDTO categoryDTO = mock(DealGroupCategoryDTO.class);
        when(dealGroupBase.getCategory()).thenReturn(categoryDTO);
        when(categoryDTO.getCategoryId()).thenReturn(123L);
        CostEffectivePinTuan costEffectivePinTuan = mock(CostEffectivePinTuan.class);
        ClientTypeEnum clientType = ClientTypeEnum.MT_APP;
        PriceDisplayDTO dealPromoPrice = null;
        PromoDetailModule promoDetailModule = new PromoDetailModule();
        PriceDisplayDTO costEffectivePrice = mock(PriceDisplayDTO.class);
        LeadsInfoResult leadsInfoResult = mock(LeadsInfoResult.class);
        DealGiftResult dealGiftResult = mock(DealGiftResult.class);
        List<DealGift> dealGifts = new ArrayList<>();
        QualificationStatusEnum qualificationStatus = QualificationStatusEnum.BIND;
        // act
        promoDetailService.buildDealPromoDetailInfo(dealGroupBase, costEffectivePinTuan, clientType, dealPromoPrice, promoDetailModule, costEffectivePrice, leadsInfoResult, dealGiftResult, dealGifts, qualificationStatus);
        // assert
        assertNull(promoDetailModule.getMarketPrice());
    }

    @Test
    public void testBuildDealPromoDetailInfo_FreeDeal() throws Throwable {
        // arrange
        ProductBaseInfo dealGroupBase = mock(ProductBaseInfo.class);
        DealGroupCategoryDTO categoryDTO = mock(DealGroupCategoryDTO.class);
        when(dealGroupBase.getCategory()).thenReturn(categoryDTO);
        when(categoryDTO.getCategoryId()).thenReturn(505L);
        DealGroupBasicDTO basicDTO = mock(DealGroupBasicDTO.class);
        CostEffectivePinTuan costEffectivePinTuan = mock(CostEffectivePinTuan.class);
        ClientTypeEnum clientType = ClientTypeEnum.MT_APP;
        PriceDisplayDTO dealPromoPrice = mock(PriceDisplayDTO.class);
        PromoDetailModule promoDetailModule = new PromoDetailModule();
        PriceDisplayDTO costEffectivePrice = mock(PriceDisplayDTO.class);
        LeadsInfoResult leadsInfoResult = mock(LeadsInfoResult.class);
        DealGiftResult dealGiftResult = mock(DealGiftResult.class);
        List<DealGift> dealGifts = new ArrayList<>();
        QualificationStatusEnum qualificationStatus = QualificationStatusEnum.BIND;
        // act
        promoDetailService.buildDealPromoDetailInfo(dealGroupBase, costEffectivePinTuan, clientType, dealPromoPrice, promoDetailModule, costEffectivePrice, leadsInfoResult, dealGiftResult, dealGifts, qualificationStatus);
        // assert
        assertNull(promoDetailModule.getFinalPrice());
    }

    @Test
    public void testBuildDealPromoDetailInfo_LeadsDealWithSpecialValue() throws Throwable {
        // arrange
        ProductBaseInfo dealGroupBase = mock(ProductBaseInfo.class);
        DealGroupCategoryDTO categoryDTO = mock(DealGroupCategoryDTO.class);
        when(dealGroupBase.getCategory()).thenReturn(categoryDTO);
        CostEffectivePinTuan costEffectivePinTuan = mock(CostEffectivePinTuan.class);
        ClientTypeEnum clientType = ClientTypeEnum.MT_XCX;
        PriceDisplayDTO dealPromoPrice = mock(PriceDisplayDTO.class);
        PromoDetailModule promoDetailModule = new PromoDetailModule();
        PriceDisplayDTO costEffectivePrice = mock(PriceDisplayDTO.class);
        LeadsInfoResult leadsInfoResult = mock(LeadsInfoResult.class);
        ActivityDetailDTO activityDetailDTO = mock(ActivityDetailDTO.class);
        DealGiftResult dealGiftResult = mock(DealGiftResult.class);
        List<DealGift> dealGifts = new ArrayList<>();
        QualificationStatusEnum qualificationStatus = QualificationStatusEnum.BIND;
        // act
        promoDetailService.buildDealPromoDetailInfo(dealGroupBase, costEffectivePinTuan, clientType, dealPromoPrice, promoDetailModule, costEffectivePrice, leadsInfoResult, dealGiftResult, dealGifts, qualificationStatus);
        // assert
        assertNull(promoDetailModule.getFinalPrice());
    }

    @Test
    public void testBuildDealPromoDetailInfo_NormalCaseWithPromos() throws Throwable {
        // arrange
        ProductBaseInfo dealGroupBase = mock(ProductBaseInfo.class);
        DealGroupCategoryDTO categoryDTO = mock(DealGroupCategoryDTO.class);
        when(dealGroupBase.getCategory()).thenReturn(categoryDTO);
        CostEffectivePinTuan costEffectivePinTuan = mock(CostEffectivePinTuan.class);
        ClientTypeEnum clientType = ClientTypeEnum.MT_APP;
        PriceDisplayDTO dealPromoPrice = mock(PriceDisplayDTO.class);
        when(dealPromoPrice.getPrice()).thenReturn(new BigDecimal("100.00"));
        List<PromoDTO> usedPromos = new ArrayList<>();
        PromoDTO promoDTO = mock(PromoDTO.class);
        PromoIdentity identity = mock(PromoIdentity.class);
        when(promoDTO.getIdentity()).thenReturn(identity);
        when(identity.getPromoShowType()).thenReturn("DISCOUNT_SELL");
        when(promoDTO.getAmount()).thenReturn(new BigDecimal("10.00"));
        when(identity.getPromoTypeDesc()).thenReturn("特惠促销");
        when(promoDTO.getExtendDesc()).thenReturn("满100减10");
        usedPromos.add(promoDTO);
        when(dealPromoPrice.getUsedPromos()).thenReturn(usedPromos);
        PromoDetailModule promoDetailModule = new PromoDetailModule();
        PriceDisplayDTO costEffectivePrice = mock(PriceDisplayDTO.class);
        LeadsInfoResult leadsInfoResult = mock(LeadsInfoResult.class);
        DealGiftResult dealGiftResult = mock(DealGiftResult.class);
        List<DealGift> dealGifts = new ArrayList<>();
        QualificationStatusEnum qualificationStatus = QualificationStatusEnum.BIND;
        // act
        promoDetailService.buildDealPromoDetailInfo(dealGroupBase, costEffectivePinTuan, clientType, dealPromoPrice, promoDetailModule, costEffectivePrice, leadsInfoResult, dealGiftResult, dealGifts, qualificationStatus);
        // assert
        assertEquals("100", promoDetailModule.getFinalPrice());
        assertNotNull(promoDetailModule.getBestPromoDetails());
        assertEquals(1, promoDetailModule.getBestPromoDetails().size());
        assertEquals("特惠促销", promoDetailModule.getBestPromoDetails().get(0).getPromoTag());
        assertEquals("10", promoDetailModule.getBestPromoDetails().get(0).getPromoAmount());
    }

    @Test
    public void testBuildDealPromoDetailInfo_ExceptionCase() throws Throwable {
        // arrange
        ProductBaseInfo dealGroupBase = mock(ProductBaseInfo.class);
        when(dealGroupBase.getCategory()).thenThrow(new RuntimeException("Test exception"));
        CostEffectivePinTuan costEffectivePinTuan = mock(CostEffectivePinTuan.class);
        ClientTypeEnum clientType = ClientTypeEnum.MT_APP;
        PriceDisplayDTO dealPromoPrice = mock(PriceDisplayDTO.class);
        PromoDetailModule promoDetailModule = new PromoDetailModule();
        PriceDisplayDTO costEffectivePrice = mock(PriceDisplayDTO.class);
        LeadsInfoResult leadsInfoResult = mock(LeadsInfoResult.class);
        DealGiftResult dealGiftResult = mock(DealGiftResult.class);
        List<DealGift> dealGifts = new ArrayList<>();
        QualificationStatusEnum qualificationStatus = QualificationStatusEnum.BIND;
        // act & assert
        assertDoesNotThrow(() -> {
            promoDetailService.buildDealPromoDetailInfo(dealGroupBase, costEffectivePinTuan, clientType, dealPromoPrice, promoDetailModule, costEffectivePrice, leadsInfoResult, dealGiftResult, dealGifts, qualificationStatus);
        });
    }
}
