package com.sankuai.dzshoppingguide.product.detail.application.utils;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.mockStatic;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

/**
 * Test class for ShareIdUtils.getShareId method
 */
public class ShareIdUtilsTest {

    /**
     * Test getShareId with different input lengths
     * Verifies the method handles various input lengths correctly
     */
    @ParameterizedTest
    @ValueSource(ints = { 1, 5, 10, 20 })
    public void testGetShareId_VariousLengths(int length) {
        // act
        String shareId = ShareIdUtils.getShareId(length);
        // assert
        assertNotNull(shareId, "Generated shareId should not be null");
        assertEquals(8 + length, shareId.length(), "ShareId length should be timestamp(8) + input length");
        assertTrue(shareId.matches("[0-9a-z]+"), "ShareId should only contain base36 characters");
    }

    /**
     * Test getShareId with normal input length
     * Verifies the basic functionality and format of generated share ID
     */
    @Test
    public void testGetShareId_NormalLength() throws Throwable {
        // arrange
        int length = 5;
        // act
        String shareId = ShareIdUtils.getShareId(length);
        // assert
        assertNotNull(shareId, "Generated shareId should not be null");
        assertEquals(13, shareId.length(), "ShareId length should be timestamp(8) + random(5)");
        assertTrue(shareId.matches("[0-9a-z]+"), "ShareId should only contain base36 characters");
    }

    /**
     * Test getShareId with zero length
     * Verifies the method handles zero length input correctly
     */
    @Test
    public void testGetShareId_ZeroLength() throws Throwable {
        // arrange
        int length = 0;
        // act
        String shareId = ShareIdUtils.getShareId(length);
        // assert
        assertNotNull(shareId, "Generated shareId should not be null");
        assertEquals(8, shareId.length(), "ShareId should only contain timestamp part (8 characters)");
        assertTrue(shareId.matches("[0-9a-z]+"), "ShareId should only contain base36 characters");
    }

    /**
     * Test getShareId with negative length
     * Verifies the method handles negative length input correctly
     */
    @Test
    public void testGetShareId_NegativeLength() throws Throwable {
        // arrange
        int length = -1;
        // act
        String shareId = ShareIdUtils.getShareId(length);
        // assert
        assertNotNull(shareId, "Generated shareId should not be null");
        assertEquals(8, shareId.length(), "ShareId should only contain timestamp part (8 characters)");
        assertTrue(shareId.matches("[0-9a-z]+"), "ShareId should only contain base36 characters");
    }

    /**
     * Test share ID format consistency
     * Verifies the format of timestamp and random parts
     */
    @Test
    public void testGetShareId_FormatConsistency() throws Throwable {
        // arrange
        int length = 5;
        // act
        String shareId = ShareIdUtils.getShareId(length);
        // assert
        String timestampPart = shareId.substring(0, 8);
        String randomPart = shareId.substring(8);
        assertTrue(timestampPart.matches("[0-9a-z]{8}"), "Timestamp part should be 8 base36 characters");
        assertTrue(randomPart.matches("[0-9a-z]{" + length + "}"), "Random part should match expected length with base36 characters");
    }

    /**
     * Test uniqueness of consecutive share IDs
     * Verifies that consecutive calls generate different IDs
     */
    @Test
    public void testGetShareId_ConsecutiveUniqueness() throws Throwable {
        // arrange
        int length = 5;
        // act
        String shareId1 = ShareIdUtils.getShareId(length);
        String shareId2 = ShareIdUtils.getShareId(length);
        // assert
        assertNotNull(shareId1, "First shareId should not be null");
        assertNotNull(shareId2, "Second shareId should not be null");
        assertNotEquals(shareId1, shareId2, "Consecutive calls should generate different shareIds");
    }

    /**
     * Test random part distribution
     * Verifies that random parts have good distribution
     */
    @Test
    public void testGetShareId_RandomDistribution() throws Throwable {
        // arrange
        int length = 5;
        int sampleSize = 100;
        // act
        String[] randomParts = new String[sampleSize];
        for (int i = 0; i < sampleSize; i++) {
            String shareId = ShareIdUtils.getShareId(length);
            randomParts[i] = shareId.substring(8);
        }
        // assert
        long uniqueCount = java.util.Arrays.stream(randomParts).distinct().count();
        assertTrue(uniqueCount > sampleSize * 0.9, "At least 90% of random parts should be unique");
    }
}
