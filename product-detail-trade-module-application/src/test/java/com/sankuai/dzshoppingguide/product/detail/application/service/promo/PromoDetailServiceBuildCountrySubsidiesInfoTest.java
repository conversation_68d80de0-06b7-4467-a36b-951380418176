package com.sankuai.dzshoppingguide.product.detail.application.service.promo;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dealuser.price.display.api.enums.PricePromoInfoTypeEnum;
import com.sankuai.dealuser.price.display.api.enums.PromoTypeEnum;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import com.sankuai.dealuser.price.display.api.model.PromoIdentity;
import com.sankuai.dealuser.price.display.api.model.PromoTextDTO;
import com.sankuai.dzshoppingguide.product.detail.application.model.CountrySubsidiesConfig;
import com.sankuai.dzshoppingguide.product.detail.application.utils.LionConfigUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.promo.vo.NationalSubsidyInfo;
import com.sankuai.statesubsidies.c.thrift.constant.QualificationStatusEnum;
import groovyjarjarantlr.collections.List;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class PromoDetailServiceBuildCountrySubsidiesInfoTest {

    @Mock
    private CountrySubsidiesConfigProvider configProvider;

    @InjectMocks
    private PromoDetailService promoDetailService;

    private CountrySubsidiesConfig config;

    @BeforeEach
    void setUp() {
        config = new CountrySubsidiesConfig();
        config.setIcon("configIcon");
        config.setTitle("configTitle");
        config.setSubtitle("configSubtitle");
        config.setMtJumpUrl("mtUrl");
        config.setDpJumpUrl("dpUrl");
        config.setReceivedButtonPic("receivedPic");
        config.setUnReceivedButtonPic("unreceivedPic");
    }

    // Add this interface to your production code
    public interface CountrySubsidiesConfigProvider {

        CountrySubsidiesConfig getCountrySubsidiesConfig();
    }

    /**
     * Test when priceDisplayDTO is null
     */
    @Test
    public void testBuildCountrySubsidiesInfoWhenPriceDisplayIsNull() throws Throwable {
        // act
        NationalSubsidyInfo result = promoDetailService.buildCountrySubsidiesInfo(null, QualificationStatusEnum.BIND, true);
        // assert
        assertNull(result);
    }

    /**
     * Test when pricePromoInfoMap is null
     */
    @Test
    public void testBuildCountrySubsidiesInfoWhenPricePromoInfoMapIsNull() throws Throwable {
        // arrange
        PriceDisplayDTO priceDisplayDTO = new PriceDisplayDTO();
        priceDisplayDTO.setPricePromoInfoMap(null);
        // act
        NationalSubsidyInfo result = promoDetailService.buildCountrySubsidiesInfo(priceDisplayDTO, QualificationStatusEnum.BIND, true);
        // assert
        assertNull(result);
    }

    /**
     * Test when no STATE_SUBSIDIES_PROMO in pricePromoInfoMap
     */
    @Test
    public void testBuildCountrySubsidiesInfoWhenNoStateSubsidiesPromo() throws Throwable {
        // arrange
        PriceDisplayDTO priceDisplayDTO = new PriceDisplayDTO();
        Map pricePromoInfoMap = new HashMap();
        pricePromoInfoMap.put(PricePromoInfoTypeEnum.MAGICAL_MEMBER.getType(), Collections.emptyList());
        priceDisplayDTO.setPricePromoInfoMap(pricePromoInfoMap);
        // act
        NationalSubsidyInfo result = promoDetailService.buildCountrySubsidiesInfo(priceDisplayDTO, QualificationStatusEnum.BIND, true);
        // assert
        assertNull(result);
    }

    /**
     * Test when STATE_SUBSIDIES_PROMO exists but no matching promo type
     */
    @Test
    public void testBuildCountrySubsidiesInfoWhenNoMatchingPromoType() throws Throwable {
        // arrange
        PriceDisplayDTO priceDisplayDTO = new PriceDisplayDTO();
        Map pricePromoInfoMap = new HashMap();
        PromoDTO promoDTO = new PromoDTO();
        PromoIdentity identity = new PromoIdentity(PromoTypeEnum.NORMAL_PROMO.getType());
        promoDTO.setIdentity(identity);
        pricePromoInfoMap.put(PricePromoInfoTypeEnum.STATE_SUBSIDIES_PROMO.getType(), Collections.singletonList(promoDTO));
        priceDisplayDTO.setPricePromoInfoMap(pricePromoInfoMap);
        // act
        NationalSubsidyInfo result = promoDetailService.buildCountrySubsidiesInfo(priceDisplayDTO, QualificationStatusEnum.BIND, true);
        // assert
        assertNull(result);
    }

    /**
     * Test when promo amount is null
     */
    @Test
    public void testBuildCountrySubsidiesInfoWhenPromoAmountIsNull() throws Throwable {
        // arrange
        PriceDisplayDTO priceDisplayDTO = new PriceDisplayDTO();
        Map pricePromoInfoMap = new HashMap();
        PromoDTO promoDTO = new PromoDTO();
        PromoIdentity identity = new PromoIdentity(PromoTypeEnum.STATE_SUBSIDIES_PROMO.getType());
        promoDTO.setIdentity(identity);
        promoDTO.setAmount(null);
        pricePromoInfoMap.put(PricePromoInfoTypeEnum.STATE_SUBSIDIES_PROMO.getType(), Collections.singletonList(promoDTO));
        priceDisplayDTO.setPricePromoInfoMap(pricePromoInfoMap);
        // act
        NationalSubsidyInfo result = promoDetailService.buildCountrySubsidiesInfo(priceDisplayDTO, QualificationStatusEnum.BIND, true);
        // assert
        assertNotNull(result);
        assertEquals("", result.getPromo());
    }
}
