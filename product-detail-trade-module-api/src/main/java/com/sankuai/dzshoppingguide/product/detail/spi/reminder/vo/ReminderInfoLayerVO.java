package com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;

@Data
@MobileDo(id = 0xc32f)
public class ReminderInfoLayerVO implements Serializable {
    /**
     * 浮层icon
     */
    @MobileDo.MobileField(key = 0x3c48)
    private Icon icon;

    /**
     * 浮层标题
     */
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    /**
     * 类型区分（1跳链 2浮层key）
     */
    @MobileDo.MobileField(key = 0x8f0c)
    private int type;

    /**
     * 跳链
     */
    @MobileDo.MobileField(key = 0x774e)
    private String jumpUrl;

    /**
     * 对应前端浮层key
     */
    @MobileDo.MobileField(key = 0xf30e)
    private String modulekey;
}