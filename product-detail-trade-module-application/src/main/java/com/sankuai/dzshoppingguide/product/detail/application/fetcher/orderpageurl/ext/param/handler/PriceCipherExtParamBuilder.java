package com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.ext.param.handler;

import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.enums.OrderPageExtParamEnums;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.ext.param.BaseSingleExtParamBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.ext.param.request.ExtParamBuilderRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.dto.ProductPriceReturnValue;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * @Author: guangyujie
 * @Date: 2025/4/7 15:49
 */
@Component
public class PriceCipherExtParamBuilder extends BaseSingleExtParamBuilder {

    @Override
    protected OrderPageExtParamEnums buildKey() {
        return OrderPageExtParamEnums.pricecipher;
    }

    @Override
    protected String doBuildExtParam(ProductDetailPageRequest request, ExtParamBuilderRequest builderRequest) throws Exception {
        // 先出门市价的价惠密文，如果门市价的为空，初始化成普通的价格
        String priceCipher = Optional.ofNullable(builderRequest.getPromoPrice())
                .map(ProductPriceReturnValue::getPriceSecretInfo).orElse(null);
        if (StringUtils.isBlank(priceCipher)) {
            priceCipher = Optional.ofNullable(builderRequest.getNormalPrice())
                    .map(ProductPriceReturnValue::getPriceSecretInfo).orElse(null);
        }
        return priceCipher;
    }

}
