package com.sankuai.dzshoppingguide.product.detail.spi.promo.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025-05-21
 * @desc
 */
@Data
@MobileDo(id = 0xae1d)
public class NationalSubsidyPromotionBar implements Serializable {
    /**
     * 优惠金额
     */
    @MobileField(key = 0x4f4c)
    private String promotionDesc;

    /**
     * 优惠信息后缀
     */
    @MobileField(key = 0x8aa1)
    private String promotionDescSuffix;

    /**
     * 优惠信息前缀
     */
    @MobileField(key = 0x5e1d)
    private String promotionDescPrefix;

    /**
     * 0-未领取；1-已领取
     */
    @MobileField(key = 0xc851)
    private int issueStatus;

    /**
     * 按钮文案
     */
    @MobileField(key = 0xe221)
    private String buttonText;

    /**
     * 图标
     */
    @MobileField(key = 0x3c48)
    private String icon;
}
