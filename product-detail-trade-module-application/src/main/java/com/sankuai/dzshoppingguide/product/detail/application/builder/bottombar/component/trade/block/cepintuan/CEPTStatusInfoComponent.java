package com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.component.trade.block.cepintuan;

import com.alibaba.fastjson.JSON;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.exception.TradeButtonBuildFatalException;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.cepinpool.dto.CostEffectivePinTuan;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.component.cepintuan.CEPinTuanInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;

/**
 * @Author: guangyujie
 * @Date: 2025/3/20 22:02
 */
@Slf4j
public class CEPTStatusInfoComponent {

    public static CEPinTuanInfoVO build(final ProductDetailPageRequest request,
                                        @NotNull final CostEffectivePinTuan costEffectivePinTuan) {
        try {
            CEPinTuanInfoVO pinTuanInfo = new CEPinTuanInfoVO();
            pinTuanInfo.setNeedMemberCount(costEffectivePinTuan.getNeedMemberCount());
            pinTuanInfo.setAvatars(costEffectivePinTuan.getAvatars());
            return pinTuanInfo;
        } catch (Throwable throwable) {
            log.error("CEPTStatusInfoComponent.build,request:{}",
                    JSON.toJSONString(request), new TradeButtonBuildFatalException(throwable));
            return null;
        }
    }

}
