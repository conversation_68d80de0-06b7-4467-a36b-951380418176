package com.sankuai.dzshoppingguide.product.detail.application.fetcher.membercard.card.integration;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.FetcherDAGStarter;

import lombok.extern.slf4j.Slf4j;

/**
 * 商家会员聚合fetcher-不需要有商家会员入口
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/2/27 15:53
 */
@Fetcher(previousLayerDependencies = {FetcherDAGStarter.class})
@Slf4j
public class IntegrationMemberCardNoEntranceFetcher extends BaseMemberCardFetcher {@Override
    protected boolean needHasMemberEntrance() {
        return false;
    }
}
