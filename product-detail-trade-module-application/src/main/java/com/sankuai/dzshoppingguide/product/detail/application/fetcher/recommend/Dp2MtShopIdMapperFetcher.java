package com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommend;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.shopid.ShopIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.shopid.ShopIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommend.dto.Dp2MtShopIdMapperReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.concurrent.CompletableFuture;

/**
 * @Author: zhangyuan103
 * @Date: 2025/5/26
 * @Description: 点评shopId到美团shopId的映射
 */
@Fetcher(previousLayerDependencies = {ShopIdMapperFetcher.class})
@Slf4j
public class Dp2MtShopIdMapperFetcher extends NormalFetcherContext<Dp2MtShopIdMapperReturnValue> {
    @Autowired
    CompositeAtomService compositeAtomService;

    @Override
    protected CompletableFuture<Dp2MtShopIdMapperReturnValue> doFetch() throws Exception {
        ShopIdMapper shopIdMapper = getDependencyResult(ShopIdMapperFetcher.class);
        long dpShopId = shopIdMapper.getDpBestShopId();
        return compositeAtomService.queryMtByDpIdsL(Collections.singletonList(dpShopId)).thenApply(Dp2MtShopIdMapperReturnValue::new);
    }
}
