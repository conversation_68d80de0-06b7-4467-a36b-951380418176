package com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.builder.handler.standard.impl;

import com.dianping.tpfun.product.api.sku.pintuan.dto.PinProductBrief;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.BuilderTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.builder.handler.standard.StandardBottomBarBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.component.trade.block.buy.button.DefaultTradeButtonComponent;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.component.trade.block.buy.button.StandardTradeButtonComponent;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.component.trade.block.buy.button.pintuan.NormalPinTuanButtonComponent;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.component.trade.block.buy.button.shop.member.ShopMemberTradeButtonComponent;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.membercard.card.premium.MemberCardInterestFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.membercard.card.premium.PremiumMemberCardResult;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.pinpool.PinTuanPromoDisplayFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.pinpool.PinTuanPromoDisplayResult;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.pinpool.PricePinPoolFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.pinpool.PricePinPoolResult;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sale.info.enums.ProductSaleStatusEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sale.info.enums.ProductSaleTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.FetcherDAGStarter;
import com.sankuai.dzshoppingguide.product.detail.application.service.dto.ShopMemberDetailV;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.ProductBottomBarVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.block.trade.StandardTradeBlockVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.component.trade.button.BaseTradeButtonVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.component.trade.button.StandardTradeButtonVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.scene.StandardTradeBottomBarVO;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;

import java.util.Optional;

/**
 * @Author: guangyujie
 * @Date: 2025/3/10 15:57
 */
@Builder(
        builderType = BuilderTypeEnum.VARIABLE_BUILDER,
        moduleKey = ModuleKeyConstants.BOTTOM_BAR,
        startFetcher = FetcherDAGStarter.class,
        dependentFetchers = {
                PricePinPoolFetcher.class,
                PinTuanPromoDisplayFetcher.class,
                MemberCardInterestFetcher.class
        }
)
@Slf4j
//标准样式：https://km.sankuai.com/collabpage/2705691071
public class DefaultBottomBarBuilder extends StandardBottomBarBuilder {

    private PinProductBrief pinProductBrief;
    private PinTuanPromoDisplayResult pinTuanPromoDisplayResult;
    private ShopMemberDetailV shopMemberDetailV;
    private ProductSaleStatusEnum saleStatus;

    @Override
    protected void prepareData() {
        saleStatus = Optional.ofNullable(productSaleInfo)
                .map(saleInfo -> saleInfo.getSaleStatus(ProductSaleTypeEnum.COMMON_DEAL))
                .orElse(ProductSaleStatusEnum.ONLINE);
        pinProductBrief = getDependencyResult(PricePinPoolFetcher.class, PricePinPoolResult.class)
                .map(PricePinPoolResult::getPinProductBrief).orElse(null);
        pinTuanPromoDisplayResult = getDependencyResult(PinTuanPromoDisplayFetcher.class);
        shopMemberDetailV = getDependencyResult(MemberCardInterestFetcher.class, PremiumMemberCardResult.class)
                .map(PremiumMemberCardResult::getShopMemberDetailV).orElse(null);
    }

    private boolean showShoppingCart = false;

    @Override
    protected ProductBottomBarVO buildProductBottomBarVO() {
        StandardTradeBottomBarVO standardTradeBottomBarVO = new StandardTradeBottomBarVO();
        standardTradeBottomBarVO.setLeftBottomBar(buildQuickEntranceBlock());
        standardTradeBottomBarVO.setRightBottomBar(buildStandardTradeBlock());
        if (showShoppingCart) {
            addShoppingCartButton(standardTradeBottomBarVO, saleStatus);
        }
        return standardTradeBottomBarVO;
    }

    /**
     * 交易按钮区域
     */
    private @NotNull StandardTradeBlockVO buildStandardTradeBlock() {
        //构建右侧交易按钮
        //逻辑：普通到手价、商家会员价比价，最低的出
        StandardTradeButtonVO rightButton = ShopMemberTradeButtonComponent.build(
                request, productCategory, normalPriceReturnValue, promoPriceReturnValue, shopMemberDetailV, orderPageUrl, purchaseCouponReturnValue, saleStatus
        );
        if (rightButton == null) {
            rightButton = StandardTradeButtonComponent.build(
                    request, productCategory, normalPriceReturnValue, orderPageUrl, purchaseCouponReturnValue, saleStatus
            );
        }
        if (rightButton == null) {  //最终兜底
            rightButton = DefaultTradeButtonComponent.build(request, orderPageUrl);
        }
        //构建左侧交易辅助按钮
        //逻辑：普通拼团 > 加入购物车（仅美团APP内有，如果有渠道优惠则隐藏）
        BaseTradeButtonVO leftButton = NormalPinTuanButtonComponent.build(
                request, pinProductBrief, normalPrice, pinTuanPromoDisplayResult, saleStatus
        );
        if (leftButton == null) {
            showShoppingCart = true;
        }
        return StandardTradeBlockVO.buildTwoButtonStyle(leftButton, rightButton);
    }

}
