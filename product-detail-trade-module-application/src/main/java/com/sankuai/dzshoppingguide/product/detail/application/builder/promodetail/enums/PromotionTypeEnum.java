package com.sankuai.dzshoppingguide.product.detail.application.builder.promodetail.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025-04-17
 * @desc 优惠类型枚举
 */
@Getter
public enum PromotionTypeEnum {
    // 优惠券
    COUPON(1, "优惠券"),
    // 买赠
    BUY_GIVE(2, "买赠"),
    // 优惠活动
    PROMOTION_ACTIVITY(3, "优惠活动"),
    // 商家拼团
    MERCHANT_PIN_TUAN(4, "商家拼团"),
    // 免费商家会员优惠
    FREE_MERCHANT_MEMBER_CARD(5, "免费商家会员优惠"),
    // 付费商家会员优惠
    PAY_MERCHANT_MEMBER_CARD(6, "付费商家会员优惠"),
    ;

    final int code;
    final String desc;

    PromotionTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}
