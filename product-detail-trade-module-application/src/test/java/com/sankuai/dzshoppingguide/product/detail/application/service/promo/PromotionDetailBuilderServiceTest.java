package com.sankuai.dzshoppingguide.product.detail.application.service.promo;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dealuser.price.display.api.enums.CouponAssignStatusEnum;
import com.sankuai.dealuser.price.display.api.enums.PromoTypeEnum;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import com.sankuai.dealuser.price.display.api.model.PromoIdentity;
import com.sankuai.dzshoppingguide.product.detail.spi.promo.vo.DztgCouponInfo;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class PromotionDetailBuilderServiceTest {

    @Mock
    private LeafRepository leafRepository;

    @InjectMocks
    private PromotionDetailBuilderService promotionDetailBuilderService;

    public PromotionDetailBuilderServiceTest() {
        MockitoAnnotations.openMocks(this);
    }

    /**
     * Test when coupon is null
     */
    @Test
    public void testConvertPromoDTO2DztgCouponInfo_CouponIsNull() throws Throwable {
        // arrange
        PromoDTO coupon = null;
        String mrnVersion = "1.0.0";
        // act
        DztgCouponInfo result = promotionDetailBuilderService.convertPromoDTO2DztgCouponInfo(coupon, mrnVersion);
        // assert
        assertNull(result, "Expected result to be null when coupon is null");
    }

    /**
     * Test when coupon identity is null but has other required fields
     */
    @Test
    public void testConvertPromoDTO2DztgCouponInfo_CouponIdentityIsNull() throws Throwable {
        // arrange
        PromoDTO coupon = new PromoDTO();
        coupon.setIdentity(null);
        coupon.setExtendDesc("Test Description");
        coupon.setCouponValueText("100");
        coupon.setCouponValueType(1);
        coupon.setUseTimeDesc("Valid for 30 days");
        coupon.setPriceLimitDesc("No limit");
        coupon.setCouponId("123");
        coupon.setAmount(new BigDecimal("100"));
        String mrnVersion = "1.0.0";
        // act
        DztgCouponInfo result = promotionDetailBuilderService.convertPromoDTO2DztgCouponInfo(coupon, mrnVersion);
        // assert
        assertNotNull(result, "Expected result to be not null when coupon identity is null");
        assertEquals("Test Description", result.getTitle());
        assertEquals("100", result.getAmount());
        assertEquals("元", result.getAmountCornerMark());
        assertEquals("优惠券", result.getSourceTag());
        assertEquals("123", result.getCouponId());
    }

    /**
     * Test when coupon type is COUPON with MERCHANT_COUPON show type
     */
    @Test
    public void testConvertPromoDTO2DztgCouponInfo_MerchantCouponType() throws Throwable {
        // arrange
        PromoDTO coupon = new PromoDTO();
        PromoIdentity identity = new PromoIdentity(123L, PromoTypeEnum.COUPON.getType());
        identity.setPromoShowType("MERCHANT_COUPON");
        coupon.setIdentity(identity);
        coupon.setCanAssign(true);
        coupon.setExtendDesc("Test Merchant Coupon");
        coupon.setCouponValueText("10");
        coupon.setCouponValueType(1);
        coupon.setUseTimeDesc("Valid for 30 days");
        coupon.setPriceLimitDesc("No limit");
        coupon.setCouponId("123");
        coupon.setAmount(new BigDecimal("10"));
        String mrnVersion = "1.0.0";
        // act
        DztgCouponInfo result = promotionDetailBuilderService.convertPromoDTO2DztgCouponInfo(coupon, mrnVersion);
        // assert
        assertNotNull(result, "Expected result to be not null when coupon type is MERCHANT_COUPON");
        assertEquals(0, result.getStatus(), "Expected status to be 0 when coupon can be assigned");
        assertEquals("Test Merchant Coupon", result.getTitle());
        assertEquals("10", result.getAmount());
        assertEquals("元", result.getAmountCornerMark());
        assertEquals(0, result.getCouponType());
        assertEquals("商家券", result.getSourceTag());
        assertEquals("123", result.getCouponId());
        assertEquals(123L, result.getCouponGroupId());
    }

    /**
     * Test when coupon type is GOVERNMENT_CONSUME_COUPON with UN_ASSIGNED status
     */
    @Test
    public void testConvertPromoDTO2DztgCouponInfo_UnassignedGovernmentCoupon() throws Throwable {
        // arrange
        PromoDTO coupon = new PromoDTO();
        PromoIdentity identity = new PromoIdentity(12345L, PromoTypeEnum.GOVERNMENT_CONSUME_COUPON.getType());
        identity.setPromoShowType("GOVERNMENT_CONSUME_COUPON");
        coupon.setIdentity(identity);
        coupon.setCouponGroupId("12345");
        coupon.setExtendDesc("Government Coupon");
        coupon.setCouponValueText("20");
        coupon.setCouponValueType(1);
        coupon.setUseTimeDesc("Valid for 60 days");
        coupon.setPriceLimitDesc("Minimum spend 100元");
        coupon.setCouponAssignStatus(CouponAssignStatusEnum.UN_ASSIGNED.getCode());
        coupon.setCouponId("123");
        coupon.setAmount(new BigDecimal("20"));
        Map promotionOtherInfoMap = new HashMap();
        promotionOtherInfoMap.put("FINANCE_EXT", "{\"packageSecretKey\":\"test-key\"}");
        coupon.setPromotionOtherInfoMap(promotionOtherInfoMap);
        String mrnVersion = "1.0.0";
        when(leafRepository.batchGenFinancialConsumeSerialId(1)).thenReturn(Collections.singletonList(1L));
        // act
        DztgCouponInfo result = promotionDetailBuilderService.convertPromoDTO2DztgCouponInfo(coupon, mrnVersion);
        // assert
        assertNotNull(result, "Expected result to be not null when coupon type is GOVERNMENT_CONSUME_COUPON");
        assertEquals(0, result.getStatus(), "Expected status to be 0 when coupon is unassigned");
        assertEquals("Government Coupon", result.getTitle());
        assertEquals("20", result.getAmount());
        assertEquals("元", result.getAmountCornerMark());
        assertEquals(2, result.getCouponType());
        assertEquals("政府消费券", result.getSourceTag());
        assertEquals("1", result.getSerialno());
        assertEquals("test-key", result.getPackageSecretKey());
        assertEquals(12345L, result.getCouponGroupId());
        assertEquals("123", result.getCouponId());
    }

    /**
     * Test when coupon has invalid amount or title
     */
    @Test
    public void testConvertPromoDTO2DztgCouponInfo_InvalidAmountOrTitle() throws Throwable {
        // arrange
        PromoDTO coupon = new PromoDTO();
        PromoIdentity identity = new PromoIdentity(1L, PromoTypeEnum.COUPON.getType());
        coupon.setIdentity(identity);
        // Invalid title
        coupon.setExtendDesc(null);
        // Invalid amount
        coupon.setCouponValueText(null);
        String mrnVersion = "1.0.0";
        // act
        DztgCouponInfo result = promotionDetailBuilderService.convertPromoDTO2DztgCouponInfo(coupon, mrnVersion);
        // assert
        assertNull(result, "Expected result to be null when coupon has invalid amount or title");
    }

    /**
     * Test when mrnVersion is less than configured version for government coupon
     */
    @Test
    public void testConvertPromoDTO2DztgCouponInfo_MrnVersionLessThanConfigured() throws Throwable {
        // arrange
        PromoDTO coupon = new PromoDTO();
        PromoIdentity identity = new PromoIdentity(12345L, PromoTypeEnum.GOVERNMENT_CONSUME_COUPON.getType());
        identity.setPromoShowType("GOVERNMENT_CONSUME_COUPON");
        coupon.setIdentity(identity);
        coupon.setCouponGroupId("12345");
        coupon.setExtendDesc("Government Coupon");
        coupon.setCouponValueText("20");
        coupon.setAmount(new BigDecimal("20"));
        Map promotionOtherInfoMap = new HashMap();
        promotionOtherInfoMap.put("FINANCE_EXT", "{\"packageSecretKey\":\"test-key\"}");
        coupon.setPromotionOtherInfoMap(promotionOtherInfoMap);
        // Less than the configured version 0.5.8
        String mrnVersion = "0.5.7";
        // act
        DztgCouponInfo result = promotionDetailBuilderService.convertPromoDTO2DztgCouponInfo(coupon, mrnVersion);
        // assert
        assertNull(result, "Expected result to be null when mrnVersion is less than configured version");
    }

    /**
     * 测试promoDTO为null的情况
     */
    @Test
    @DisplayName("When promoDTO is null, should return null")
    public void testConvertSourceTag_WhenPromoDTO_IsNull() throws Throwable {
        assertNull(promotionDetailBuilderService.convertSourceTag(null));
    }

    /**
     * 测试identity为null的情况
     */
    @Test
    @DisplayName("When identity is null, should return default value")
    public void testConvertSourceTag_WhenIdentity_IsNull() throws Throwable {
        PromoDTO promoDTO = new PromoDTO();
        promoDTO.setIdentity(null);
        assertEquals("团购优惠", promotionDetailBuilderService.convertSourceTag(promoDTO));
    }

    /**
     * 测试promoShowType为null的情况
     */
    @Test
    @DisplayName("When promoShowType is null, should return default value")
    public void testConvertSourceTag_WhenPromoShowType_IsNull() throws Throwable {
        PromoDTO promoDTO = new PromoDTO();
        PromoIdentity identity = new PromoIdentity(1L, 1);
        identity.setPromoShowType(null);
        promoDTO.setIdentity(identity);
        assertEquals("团购优惠", promotionDetailBuilderService.convertSourceTag(promoDTO));
    }

    /**
     * 测试promoShowType为DISCOUNT_SELL的情况
     */
    @Test
    @DisplayName("When promoShowType is DISCOUNT_SELL")
    public void testConvertSourceTag_WhenPromoShowType_IsDiscountSell() throws Throwable {
        PromoDTO promoDTO = new PromoDTO();
        PromoIdentity identity = new PromoIdentity(1L, 1);
        identity.setPromoShowType("DISCOUNT_SELL");
        promoDTO.setIdentity(identity);
        assertEquals("特惠促销", promotionDetailBuilderService.convertSourceTag(promoDTO));
    }

    /**
     * 测试promoShowType为MT_SUBSIDY的情况
     */
    @Test
    @DisplayName("When promoShowType is MT_SUBSIDY")
    public void testConvertSourceTag_WhenPromoShowType_IsMtSubsidy() throws Throwable {
        PromoDTO promoDTO = new PromoDTO();
        PromoIdentity identity = new PromoIdentity(1L, 1);
        identity.setPromoShowType("MT_SUBSIDY");
        promoDTO.setIdentity(identity);
        assertEquals("美团补贴", promotionDetailBuilderService.convertSourceTag(promoDTO));
    }

    /**
     * 测试promoShowType为NEW_CUSTOMER_DISCOUNT的情况
     */
    @Test
    @DisplayName("When promoShowType is NEW_CUSTOMER_DISCOUNT")
    public void testConvertSourceTag_WhenPromoShowType_IsNewCustomerDiscount() throws Throwable {
        PromoDTO promoDTO = new PromoDTO();
        PromoIdentity identity = new PromoIdentity(1L, 1);
        identity.setPromoShowType("NEW_CUSTOMER_DISCOUNT");
        promoDTO.setIdentity(identity);
        assertEquals("新客特惠", promotionDetailBuilderService.convertSourceTag(promoDTO));
    }

    /**
     * 测试promoShowType为MEMBER_BENEFITS的情况
     */
    @Test
    @DisplayName("When promoShowType is MEMBER_BENEFITS")
    public void testConvertSourceTag_WhenPromoShowType_IsMemberBenefits() throws Throwable {
        PromoDTO promoDTO = new PromoDTO();
        PromoIdentity identity = new PromoIdentity(1L, 1);
        identity.setPromoShowType("MEMBER_BENEFITS");
        promoDTO.setIdentity(identity);
        assertEquals("会员优惠", promotionDetailBuilderService.convertSourceTag(promoDTO));
    }

    /**
     * 测试promoShowType为PLATFORM_COUPON的情况
     */
    @Test
    @DisplayName("When promoShowType is PLATFORM_COUPON")
    public void testConvertSourceTag_WhenPromoShowType_IsPlatformCoupon() throws Throwable {
        PromoDTO promoDTO = new PromoDTO();
        PromoIdentity identity = new PromoIdentity(1L, 1);
        identity.setPromoShowType("PLATFORM_COUPON");
        promoDTO.setIdentity(identity);
        assertEquals("美团券", promotionDetailBuilderService.convertSourceTag(promoDTO));
    }

    /**
     * 测试promoShowType为MERCHANT_COUPON的情况
     */
    @Test
    @DisplayName("When promoShowType is MERCHANT_COUPON")
    public void testConvertSourceTag_WhenPromoShowType_IsMerchantCoupon() throws Throwable {
        PromoDTO promoDTO = new PromoDTO();
        PromoIdentity identity = new PromoIdentity(1L, 1);
        identity.setPromoShowType("MERCHANT_COUPON");
        promoDTO.setIdentity(identity);
        assertEquals("商家券", promotionDetailBuilderService.convertSourceTag(promoDTO));
    }

    /**
     * 测试promoShowType为GOVERNMENT_CONSUME_COUPON的情况
     */
    @Test
    @DisplayName("When promoShowType is GOVERNMENT_CONSUME_COUPON")
    public void testConvertSourceTag_WhenPromoShowType_IsGovernmentConsumeCoupon() throws Throwable {
        PromoDTO promoDTO = new PromoDTO();
        PromoIdentity identity = new PromoIdentity(1L, 1);
        identity.setPromoShowType("GOVERNMENT_CONSUME_COUPON");
        promoDTO.setIdentity(identity);
        assertEquals("政府消费券", promotionDetailBuilderService.convertSourceTag(promoDTO));
    }

    /**
     * 测试promoShowType为DEAL_PROMO的情况
     */
    @Test
    @DisplayName("When promoShowType is DEAL_PROMO")
    public void testConvertSourceTag_WhenPromoShowType_IsDealPromo() throws Throwable {
        PromoDTO promoDTO = new PromoDTO();
        PromoIdentity identity = new PromoIdentity(1L, 1);
        identity.setPromoShowType("DEAL_PROMO");
        promoDTO.setIdentity(identity);
        assertEquals("团购优惠", promotionDetailBuilderService.convertSourceTag(promoDTO));
    }

    /**
     * 测试promoShowType为PRESALE_PROMO的情况
     */
    @Test
    @DisplayName("When promoShowType is PRESALE_PROMO")
    public void testConvertSourceTag_WhenPromoShowType_IsPresalePromo() throws Throwable {
        PromoDTO promoDTO = new PromoDTO();
        PromoIdentity identity = new PromoIdentity(1L, 1);
        identity.setPromoShowType("PRESALE_PROMO");
        promoDTO.setIdentity(identity);
        assertEquals("预售优惠", promotionDetailBuilderService.convertSourceTag(promoDTO));
    }

    /**
     * 测试promoShowType为NEW_MEMBER_BENEFITS的情况
     */
    @Test
    @DisplayName("When promoShowType is NEW_MEMBER_BENEFITS")
    public void testConvertSourceTag_WhenPromoShowType_IsNewMemberBenefits() throws Throwable {
        PromoDTO promoDTO = new PromoDTO();
        PromoIdentity identity = new PromoIdentity(1L, 1);
        identity.setPromoShowType("NEW_MEMBER_BENEFITS");
        promoDTO.setIdentity(identity);
        assertEquals("新会员优惠", promotionDetailBuilderService.convertSourceTag(promoDTO));
    }

    /**
     * 测试promoShowType为未知类型的情况
     */
    @Test
    @DisplayName("When promoShowType is unknown")
    public void testConvertSourceTag_WhenPromoShowType_IsUnknown() throws Throwable {
        PromoDTO promoDTO = new PromoDTO();
        PromoIdentity identity = new PromoIdentity(1L, 1);
        identity.setPromoShowType("UNKNOWN_TYPE");
        promoDTO.setIdentity(identity);
        assertEquals("优惠", promotionDetailBuilderService.convertSourceTag(promoDTO));
    }
}
