package com.sankuai.dzshoppingguide.product.detail.application.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.google.common.util.concurrent.SettableFuture;
import com.meituan.service.mobile.mtthrift.callback.OctoThriftCallback;
import com.meituan.service.mobile.mtthrift.netty.ContextStore;
import com.sankuai.mktplay.center.mkt.play.center.client.PlayCenterService;
import com.sankuai.mktplay.center.mkt.play.center.client.PlayExecuteRequest;
import com.sankuai.mktplay.center.mkt.play.center.client.PlayExecuteResponse;
import com.sankuai.mktplay.center.mkt.play.center.client.UserInfo;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import org.apache.thrift.TException;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class CompositeAtomServiceImplQueryNewCustomExecutePlayTest {

    @Mock
    private PlayCenterService.AsyncIface playCenterServiceFuture;

    @InjectMocks
    private CompositeAtomServiceImpl compositeAtomService;

    private PlayExecuteRequest playExecuteRequest;

    private PlayExecuteResponse playExecuteResponse;

    private SettableFuture<Object> settableFuture;

    @BeforeEach
    void setUp() {
        UserInfo userInfo = new UserInfo(123L, 1);
        playExecuteRequest = new PlayExecuteRequest(1L, userInfo);
        playExecuteResponse = new PlayExecuteResponse(200);
        playExecuteResponse.setResult("success");
        settableFuture = SettableFuture.create();
        ContextStore.setFuture(settableFuture);
    }

    @AfterEach
    void tearDown() {
        ContextStore.removeFuture();
    }

    /**
     * Test successful execution of queryNewCustomExecutePlay
     */
    @Test
    public void testQueryNewCustomExecutePlaySuccess() throws Throwable {
        // arrange
        doAnswer(invocation -> {
            OctoThriftCallback callback = invocation.getArgument(1);
            callback.onComplete(playExecuteResponse);
            settableFuture.set(playExecuteResponse);
            return null;
        }).when(playCenterServiceFuture).executePlay(any(), any());
        // act
        CompletableFuture<PlayExecuteResponse> future = compositeAtomService.queryNewCustomExecutePlay(playExecuteRequest);
        PlayExecuteResponse result = future.get();
        // assert
        assertNotNull(result);
        assertEquals(200, result.getStatus());
        assertEquals("success", result.getResult());
        verify(playCenterServiceFuture).executePlay(eq(playExecuteRequest), any());
    }

    /**
     * Test queryNewCustomExecutePlay with exception during execution
     */
    @Test
    public void testQueryNewCustomExecutePlayWithException() throws Throwable {
        // arrange
        TException exception = new TException("Test exception");
        doAnswer(invocation -> {
            OctoThriftCallback callback = invocation.getArgument(1);
            callback.onError(exception);
            // The method returns null on error
            settableFuture.set(null);
            return null;
        }).when(playCenterServiceFuture).executePlay(any(), any());
        // act
        CompletableFuture<PlayExecuteResponse> future = compositeAtomService.queryNewCustomExecutePlay(playExecuteRequest);
        PlayExecuteResponse result = future.get();
        // assert
        // Expect null result as per the implementation
        assertNull(result);
        verify(playCenterServiceFuture).executePlay(eq(playExecuteRequest), any());
    }

    /**
     * Test queryNewCustomExecutePlay with null request
     */
    @Test
    public void testQueryNewCustomExecutePlayWithNullRequest() throws Throwable {
        // arrange
        playExecuteRequest = null;
        doAnswer(invocation -> {
            settableFuture.set(null);
            return null;
        }).when(playCenterServiceFuture).executePlay(any(), any());
        // act
        CompletableFuture<PlayExecuteResponse> future = compositeAtomService.queryNewCustomExecutePlay(playExecuteRequest);
        PlayExecuteResponse result = future.get();
        // assert
        assertNull(result);
        verify(playCenterServiceFuture).executePlay(eq(playExecuteRequest), any());
    }

    /**
     * Test queryNewCustomExecutePlay when service throws TException
     */
    @Test
    public void testQueryNewCustomExecutePlayServiceThrowsTException() throws Throwable {
        // arrange
        TException expectedException = new TException("Service error");
        doThrow(expectedException).when(playCenterServiceFuture).executePlay(any(), any());
        // act & assert
        TException actualException = assertThrows(TException.class, () -> {
            compositeAtomService.queryNewCustomExecutePlay(playExecuteRequest);
        });
        assertEquals("Service error", actualException.getMessage());
        verify(playCenterServiceFuture).executePlay(eq(playExecuteRequest), any());
    }
}
