package com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommendgoods.dtos;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;


@MobileDo(id = 0xc2b9)
@Data
public class CouponPromoItem implements Serializable {
    /**
     * 券码
     */
    @MobileField(key = 0x537d)
    private String couponCode;

    /**
     * 券批次id
     */
    @MobileField(key = 0xb991)
    private String applyId;

    /**
     * 券描述
     */
    @MobileField(key = 0xe399)
    private String couponDesc;

    /**
     * 券类型，1-神会员券，2-免费神券
     */
    @MobileField(key = 0x7cd6)
    private int couponType;

    /**
     * 券logo图标
     */
    @MobileField(key = 0xd010)
    private String logoIcon;

    /**
     * 是否已膨胀，1：已膨胀，2：未膨胀
     */
    @MobileField(key = 0x73d9)
    private int inflatedStatus;

    /**
     * 是否可膨胀，1：可膨胀，2：不可膨胀
     */
    @MobileField(key = 0x2055)
    private int canInflate;

    /**
     * 券button链接
     */
    @MobileField(key = 0x9e2e)
    private String couponButtonUrl;

    /**
     * 券button文案
     */
    @MobileField(key = 0x95fd)
    private String couponButtonText;

    /**
     * 券标签
     */
    @MobileField(key = 0xe654)
    private String couponTag;

    /**
     * 膨胀前门槛（分）
     */
    @MobileField(key = 0x8735)
    private int requiredAmount;

    /**
     * 膨胀前金额（分）
     */
    @MobileField(key = 0xd608)
    private int reduceAmount;

    /**
     * 页面来源
     */
    @MobileField(key = 0x3f9)
    private int pageSource;

    /**
     * 膨胀参数透传
     */
    @MobileField(key = 0xcb02)
    private String bizToken;

    /**
     * 业务线
     */
    @MobileField(key = 0x8826)
    private String nibBiz;

    @MobileField(key = 0xb5bb)
    private String position;

    /**
     * 资产类型
     */
    @MobileField(key = 0xb82f)
    private int assetType;

    /**
     * 膨后金额（分）
     */
    @MobileField(key = 0x974e)
    private int inflatedAmount;

    @MobileField(key = 0x1ef7)
    private String couponChannel;

    /**
     * 线下码id
     */
    @MobileField(key = 0xb8f2)
    private String offlineCode;

    /**
     * 时间文案
     */
    private String couponValidTimeText;


    /**
     * 神券icon
     */
    private String logoIconUrl;

    /**
     * 是否有效
     */
    private Integer validStatus;

    /**
     * 最大膨胀金额（分）
     */
    private String maxInflatedMoney;

    /**
     * 有效时间戳
     */
    private String validTime;

    /**
     * 券批次id
     */
    private String couponGroupId;

    private int queryInflateFlag;

    private BigDecimal minConsumptionAmount;
}