package com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.dto;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/6/14 11:59
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class BatchPriceReturnValue extends FetcherReturnValueDTO {
}
