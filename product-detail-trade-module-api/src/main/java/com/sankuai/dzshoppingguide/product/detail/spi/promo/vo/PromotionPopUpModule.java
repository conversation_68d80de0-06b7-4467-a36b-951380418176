package com.sankuai.dzshoppingguide.product.detail.spi.promo.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import lombok.Data;

import java.util.List;
import java.io.Serializable;

@TypeDoc(description = "优惠浮层模块")
@Data
@MobileDo(id = 0xec3de1d5)
public class PromotionPopUpModule implements Serializable {
    
    @FieldDoc(description = "其他神券优惠券")
    @MobileField(key = 0x60cd)
    private InflateCounponDTO otherInflateCoupon;

    @FieldDoc(description = "最佳神券优惠券")
    @MobileField(key = 0xe16d)
    private InflateCounponDTO bestInflateCoupon;

    @FieldDoc(description = "优惠券列表")
    @MobileField(key = 0xb4f3)
    private List<PromotionCoupon> promotionCouponList;

    @FieldDoc(description = "模块名")
    @MobileField(key = 0xb308)
    private String moduleName;
}