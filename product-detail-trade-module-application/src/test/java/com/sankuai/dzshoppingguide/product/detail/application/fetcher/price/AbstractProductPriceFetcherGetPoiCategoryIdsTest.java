package com.sankuai.dzshoppingguide.product.detail.application.fetcher.price;

import static com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.google.common.collect.Lists;
import com.sankuai.dealuser.price.display.api.model.ClientEnv;
import com.sankuai.dealuser.price.display.api.model.ProductIdentity;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.best.shop.ShopInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.dto.ProductPriceReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
import com.sankuai.sinai.data.api.dto.DpPoiBackCategoryDTO;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.sinai.data.api.dto.MtPoiDTO;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
@DisplayName("AbstractProductPriceFetcher isFootMassageScene Test")
class AbstractProductPriceFetcherGetPoiCategoryIdsTest {

    @InjectMocks
    private AbstractProductPriceFetcher<ProductPriceReturnValue> abstractProductPrice = new AbstractProductPriceFetcher<ProductPriceReturnValue>() {

        @Override
        protected CompletableFuture<ProductPriceReturnValue> doFetch() {
            return null;
        }

        @Override
        protected void postProcessPriceRequest(ClientEnv clientEnv, List<ProductIdentity> productIdentities, Map<String, String> extension) {
        }

        @Override
        public Set<Integer> getPoiCategoryIds(ClientTypeEnum clientTypeEnum, ShopInfo shopInfo) {
            if (clientTypeEnum == null || shopInfo == null) {
                return Collections.emptySet();
            }
            if (clientTypeEnum.isMtClientType()) {
                return getMtPoiCategoryIds(shopInfo.getMtPoiDTO());
            }
            return getDpPoiCategoryIds(shopInfo.getDpPoiDTO());
        }

        @Override
        public Set<Integer> getMtPoiCategoryIds(MtPoiDTO mtPoiDTO) {
            return super.getMtPoiCategoryIds(mtPoiDTO);
        }

        @Override
        public Set<Integer> getDpPoiCategoryIds(DpPoiDTO dpPoiDTO) {
            return super.getDpPoiCategoryIds(dpPoiDTO);
        }
    };

    @Mock
    private ShopInfo shopInfo;

    @Mock
    private MtPoiDTO mtPoiDTO;

    @Mock
    private DpPoiDTO dpPoiDTO;

    @Mock
    private CompositeAtomService compositeAtomService;

    @InjectMocks
    private TestProductPriceFetcher fetcher;

    /**
     * Test case for MT client type with valid MtPoiDTO and category list
     */
    @Test
    public void testGetPoiCategoryIdsWithMtClientTypeAndValidData() throws Throwable {
        // arrange
        DpPoiBackCategoryDTO category = new DpPoiBackCategoryDTO();
        category.setCategoryId(123);
        when(shopInfo.getMtPoiDTO()).thenReturn(mtPoiDTO);
        when(mtPoiDTO.getDpBackCategoryList()).thenReturn(Lists.newArrayList(category));
        // act
        Set<Integer> result = abstractProductPrice.getPoiCategoryIds(ClientTypeEnum.MT_APP, shopInfo);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertTrue(result.contains(123));
    }

    /**
     * Test case for DP client type with valid DpPoiDTO and category list
     */
    @Test
    public void testGetPoiCategoryIdsWithDpClientTypeAndValidData() throws Throwable {
        // arrange
        DpPoiBackCategoryDTO category = new DpPoiBackCategoryDTO();
        category.setCategoryId(456);
        when(shopInfo.getDpPoiDTO()).thenReturn(dpPoiDTO);
        when(dpPoiDTO.getBackMainCategoryPath()).thenReturn(Lists.newArrayList(category));
        // act
        Set<Integer> result = abstractProductPrice.getPoiCategoryIds(ClientTypeEnum.DP_APP, shopInfo);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertTrue(result.contains(456));
    }

    /**
     * Test case for MT client type with null MtPoiDTO
     */
    @Test
    public void testGetPoiCategoryIdsWithMtClientTypeAndNullMtPoiDTO() throws Throwable {
        // arrange
        when(shopInfo.getMtPoiDTO()).thenReturn(null);
        // act
        Set<Integer> result = abstractProductPrice.getPoiCategoryIds(ClientTypeEnum.MT_APP, shopInfo);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test case for DP client type with null DpPoiDTO
     */
    @Test
    public void testGetPoiCategoryIdsWithDpClientTypeAndNullDpPoiDTO() throws Throwable {
        // arrange
        when(shopInfo.getDpPoiDTO()).thenReturn(null);
        // act
        Set<Integer> result = abstractProductPrice.getPoiCategoryIds(ClientTypeEnum.DP_APP, shopInfo);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test case for MT client type with empty category list
     */
    @Test
    public void testGetPoiCategoryIdsWithMtClientTypeAndEmptyCategoryList() throws Throwable {
        // arrange
        when(shopInfo.getMtPoiDTO()).thenReturn(mtPoiDTO);
        when(mtPoiDTO.getDpBackCategoryList()).thenReturn(Collections.emptyList());
        // act
        Set<Integer> result = abstractProductPrice.getPoiCategoryIds(ClientTypeEnum.MT_APP, shopInfo);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test case for DP client type with empty category list
     */
    @Test
    public void testGetPoiCategoryIdsWithDpClientTypeAndEmptyCategoryList() throws Throwable {
        // arrange
        when(shopInfo.getDpPoiDTO()).thenReturn(dpPoiDTO);
        when(dpPoiDTO.getBackMainCategoryPath()).thenReturn(Collections.emptyList());
        // act
        Set<Integer> result = abstractProductPrice.getPoiCategoryIds(ClientTypeEnum.DP_APP, shopInfo);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test case for null ShopInfo
     */
    @Test
    public void testGetPoiCategoryIdsWithNullShopInfo() throws Throwable {
        // act
        Set<Integer> result = abstractProductPrice.getPoiCategoryIds(ClientTypeEnum.MT_APP, null);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test case for null ClientTypeEnum
     */
    @Test
    public void testGetPoiCategoryIdsWithNullClientType() throws Throwable {
        // act
        Set<Integer> result = abstractProductPrice.getPoiCategoryIds(null, shopInfo);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test case: Valid MT_APP client type with valid category ID
     * Expected: Should return true
     */
    @Test
    public void testIsFootMassageScene_MtAppValidCategory_ReturnsTrue() throws Throwable {
        // arrange
        Set<Integer> poiCategoryId = new HashSet<>();
        // act
        boolean result = abstractProductPrice.isFootMassageScene(MT_APP, poiCategoryId, 303);
        // assert
        assertTrue(result);
    }

    /**
     * Test case: Valid DP_APP client type with valid category ID
     * Expected: Should return true
     */
    @Test
    public void testIsFootMassageScene_DpAppValidCategory_ReturnsTrue() throws Throwable {
        // arrange
        Set<Integer> poiCategoryId = new HashSet<>();
        // act
        boolean result = abstractProductPrice.isFootMassageScene(DP_APP, poiCategoryId, 304);
        // assert
        assertTrue(result);
    }

    /**
     * Test case: Valid MT_LIVE_XCX client type with valid category ID
     * Expected: Should return true
     */
    @Test
    public void testIsFootMassageScene_MtLiveXcxValidCategory_ReturnsTrue() throws Throwable {
        // arrange
        Set<Integer> poiCategoryId = new HashSet<>();
        // act
        boolean result = abstractProductPrice.isFootMassageScene(MT_LIVE_XCX, poiCategoryId, 1610);
        // assert
        assertTrue(result);
    }

    /**
     * Test case: Invalid client type with valid category ID
     * Expected: Should return false
     */
    @Test
    public void testIsFootMassageScene_InvalidClientType_ReturnsFalse() throws Throwable {
        // arrange
        Set<Integer> poiCategoryId = new HashSet<>();
        // act
        boolean result = abstractProductPrice.isFootMassageScene(MT_XCX, poiCategoryId, 303);
        // assert
        assertFalse(result);
    }

    /**
     * Test case: Valid client type with invalid category ID
     * Expected: Should return false
     */
    @Test
    public void testIsFootMassageScene_ValidClientTypeInvalidCategory_ReturnsFalse() throws Throwable {
        // arrange
        Set<Integer> poiCategoryId = new HashSet<>();
        // act
        boolean result = abstractProductPrice.isFootMassageScene(MT_APP, poiCategoryId, 999);
        // assert
        assertFalse(result);
    }

    /**
     * Test case: Valid client type with null poiCategoryId
     * Expected: Should return true if other conditions are met
     */
    @Test
    public void testIsFootMassageScene_NullPoiCategoryId_ReturnsTrue() throws Throwable {
        // act
        boolean result = abstractProductPrice.isFootMassageScene(MT_APP, null, 303);
        // assert
        assertTrue(result);
    }

    /**
     * Test case: MT_LIVE_ORDER_XCX client type with valid category ID
     * Expected: Should return true
     */
    @Test
    public void testIsFootMassageScene_MtLiveOrderXcxValidCategory_ReturnsTrue() throws Throwable {
        // arrange
        Set<Integer> poiCategoryId = new HashSet<>();
        // act
        boolean result = abstractProductPrice.isFootMassageScene(MT_LIVE_ORDER_XCX, poiCategoryId, 304);
        // assert
        assertTrue(result);
    }

    /**
     * Test case: Valid client type with empty poiCategoryId set
     * Expected: Should return true if other conditions are met
     */
    @Test
    public void testIsFootMassageScene_EmptyPoiCategoryId_ReturnsTrue() throws Throwable {
        // arrange
        Set<Integer> poiCategoryId = new HashSet<>();
        // act
        boolean result = abstractProductPrice.isFootMassageScene(MT_APP, poiCategoryId, 303);
        // assert
        assertTrue(result);
    }

    /**
     * Concrete implementation of AbstractProductPriceFetcher for testing
     */
    private static class TestProductPriceFetcher extends AbstractProductPriceFetcher<ProductPriceReturnValue> {

        @Override
        protected void postProcessPriceRequest(ClientEnv clientEnv, List<ProductIdentity> productIdentities, Map<String, String> extension) {
            // Implementation not needed for testing isMtLiveXcx
        }

        @Override
        protected CompletableFuture<ProductPriceReturnValue> doFetch() {
            // Implementation not needed for testing isMtLiveXcx
            return null;
        }
    }

    /**
     * Test case for MT_LIVE_XCX client type
     */
    @Test
    @DisplayName("Should return true when client type is MT_LIVE_XCX")
    public void testIsMtLiveXcx_WithMtLiveXcx_ShouldReturnTrue() {
        // arrange
        ClientTypeEnum clientType = ClientTypeEnum.MT_LIVE_XCX;
        // act
        boolean result = fetcher.isMtLiveXcx(clientType);
        // assert
        assertTrue(result, "Should return true for MT_LIVE_XCX client type");
    }

    /**
     * Test case for MT_LIVE_ORDER_XCX client type
     */
    @Test
    @DisplayName("Should return true when client type is MT_LIVE_ORDER_XCX")
    public void testIsMtLiveXcx_WithMtLiveOrderXcx_ShouldReturnTrue() {
        // arrange
        ClientTypeEnum clientType = ClientTypeEnum.MT_LIVE_ORDER_XCX;
        // act
        boolean result = fetcher.isMtLiveXcx(clientType);
        // assert
        assertTrue(result, "Should return true for MT_LIVE_ORDER_XCX client type");
    }

    /**
     * Test case for MT_APP client type
     */
    @Test
    @DisplayName("Should return false when client type is MT_APP")
    public void testIsMtLiveXcx_WithMtApp_ShouldReturnFalse() {
        // arrange
        ClientTypeEnum clientType = ClientTypeEnum.MT_APP;
        // act
        boolean result = fetcher.isMtLiveXcx(clientType);
        // assert
        assertFalse(result, "Should return false for MT_APP client type");
    }

    /**
     * Test case for DP_APP client type
     */
    @Test
    @DisplayName("Should return false when client type is DP_APP")
    public void testIsMtLiveXcx_WithDpApp_ShouldReturnFalse() {
        // arrange
        ClientTypeEnum clientType = ClientTypeEnum.DP_APP;
        // act
        boolean result = fetcher.isMtLiveXcx(clientType);
        // assert
        assertFalse(result, "Should return false for DP_APP client type");
    }

    /**
     * Test case for MT_XCX client type
     */
    @Test
    @DisplayName("Should return false when client type is MT_XCX")
    public void testIsMtLiveXcx_WithMtXcx_ShouldReturnFalse() {
        // arrange
        ClientTypeEnum clientType = ClientTypeEnum.MT_XCX;
        // act
        boolean result = fetcher.isMtLiveXcx(clientType);
        // assert
        assertFalse(result, "Should return false for MT_XCX client type");
    }

    /**
     * Test case for UNKNOWN client type
     */
    @Test
    @DisplayName("Should return false when client type is UNKNOWN")
    public void testIsMtLiveXcx_WithUnknown_ShouldReturnFalse() {
        // arrange
        ClientTypeEnum clientType = ClientTypeEnum.UNKNOWN;
        // act
        boolean result = fetcher.isMtLiveXcx(clientType);
        // assert
        assertFalse(result, "Should return false for UNKNOWN client type");
    }

    /**
     * Test case for null input
     */
    @Test
    @DisplayName("Should return false when client type is null")
    public void testIsMtLiveXcx_WithNullClientType_ShouldReturnFalse() {
        // arrange
        ClientTypeEnum clientType = null;
        // act
        boolean result = fetcher.isMtLiveXcx(clientType);
        // assert
        assertFalse(result, "Should return false for null client type");
    }
}
