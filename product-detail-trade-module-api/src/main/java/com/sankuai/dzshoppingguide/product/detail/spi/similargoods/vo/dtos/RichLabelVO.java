package com.sankuai.dzshoppingguide.product.detail.spi.similargoods.vo.dtos;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;

/**
 * 富文本的文字信息
 */
@Data
@MobileDo(id = 0x2b7c)
public class RichLabelVO implements Serializable {

    /**
     * 0代表默认居左，1代表居中，2代表居右
     */
    @MobileDo.MobileField(key = 0xb5bb)
    private int position = RichLabelPositionEnums.MIDDLE.getType();

    /**
     * 加粗：bold  普通：normal
     * com.sankuai.dzviewscene.productshelf.vo.enums.RichLabelFrontWeightEnums
     */
    @MobileDo.MobileField(key = 0x579e)
    private String fontWeight = "normal";

    /**
     * 字体大小
     */
    @MobileDo.MobileField(key = 0xfee3)
    private int textSize = 14;

    /**
     * 16进制，颜色代码+透明度，示例：#FFCC7788
     */
    @MobileDo.MobileField(key = 0xeead)
    private String textColor = "#FFCC7788";

    /**
     * 文本类型 加粗
     * B端场景需要使用
     */
    @MobileDo.MobileField(key = 0xddfb)
    private String textStyle;

    /**
     * 显示内容，不支持换行
     */
    @MobileDo.MobileField(key = 0x451b)
    private String text;

    /**
     * 背景色
     */
    @MobileDo.MobileField(key = 0xba62)
    private String backgroundColor;

    /**
     * 边框颜色
     */
    @MobileDo.MobileField(key = 0xad82)
    private String borderColor;

    /**
     * 是否有边框
     */
    @MobileDo.MobileField(key = 0xc51b)
    private boolean hasBorder;

    /**
     * 边框圆角样式，格式为'x,y,z,w'，表示左上、右上、右下、左下圆角半径
     */
    @MobileDo.MobileField(key = 0x500c)
    private String cssBorderRadius;

    /**
     * 内边距样式，格式为'x,y,z,w'，表示上、右、下、左内边距大小
     */
    @MobileDo.MobileField(key = 0x68fa)
    private String cssPadding;

    /**
     * 跳转链接
     */
    @MobileDo.MobileField(key = 0x774e)
    private String jumpUrl;

    /**
     * 承接时间戳
     */
    @MobileDo.MobileField(key = 0xffd1)
    private long numerical;

    /**
     * 文本前图片标签
     */
    @MobileDo.MobileField(key = 0xb548)
    private String preIcon;

    /**
     * 文本前图片标签比例
     */
    @MobileDo.MobileField(key = 0x6725)
    private double preIconAspectRadio;

    public RichLabelVO() {

    }

    public RichLabelVO(String text) {
        this.text = text;
    }

    public RichLabelVO(String textColor, boolean hasBorder, String borderColor, String text) {
        this.textColor = textColor;
        this.hasBorder = hasBorder;
        this.borderColor = borderColor;
        this.text = text;
    }

    public RichLabelVO(int textSize, String text, String textColor, String backgroundColor) {
        this.textSize = textSize;
        this.text = text;
        this.textColor = textColor;
        this.backgroundColor = backgroundColor;
    }

    public RichLabelVO(int position, String fontWeight, int textSize, String textColor, String text) {
        this.position = position;
        this.fontWeight = fontWeight;
        this.textSize = textSize;
        this.textColor = textColor;
        this.text = text;
    }

    public RichLabelVO(int textSize, String textColor, String text) {
        this.textSize = textSize;
        this.textColor = textColor;
        this.text = text;
    }

    public RichLabelVO(String textStyle, String textColor, int textSize, String text) {
        this.textStyle = textStyle;
        this.textColor = textColor;
        this.textSize = textSize;
        this.text = text;
    }

    public RichLabelVO(String fontWeight, int textSize, String textColor, String text) {
        this.fontWeight = fontWeight;
        this.textSize = textSize;
        this.textColor = textColor;
        this.text = text;
    }

    public boolean isHasBorder() {
        return hasBorder;
    }

    public void setHasBorder(boolean hasBorder) {
        this.hasBorder = hasBorder;
    }

    public String getBorderColor() {
        return borderColor;
    }

    public void setBorderColor(String borderColor) {
        this.borderColor = borderColor;
    }

    public String getFontWeight() {
        return fontWeight;
    }

    public void setFontWeight(String fontWeight) {
        this.fontWeight = fontWeight;
    }

    public int getTextSize() {
        return textSize;
    }

    public void setTextSize(int textSize) {
        this.textSize = textSize;
    }

    public String getTextColor() {
        return textColor;
    }

    public void setTextColor(String textColor) {
        this.textColor = textColor;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public int getPosition() {
        return position;
    }

    public void setPosition(int position) {
        this.position = position;
    }

    public String getBackgroundColor() {
        return backgroundColor;
    }

    public void setBackgroundColor(String backgroundColor) {
        this.backgroundColor = backgroundColor;
    }

    public String getTextStyle() {
        return textStyle;
    }

    public void setTextStyle(String textStyle) {
        this.textStyle = textStyle;
    }

    public String getCssBorderRadius() {
        return cssBorderRadius;
    }

    public void setCssBorderRadius(String cssBorderRadius) {
        this.cssBorderRadius = cssBorderRadius;
    }

    public String getCssPadding() {
        return cssPadding;
    }

    public void setCssPadding(String cssPadding) {
        this.cssPadding = cssPadding;
    }

    public String getJumpUrl() {
        return jumpUrl;
    }
    public void setJumpUrl(String jumpUrl) {
        this.jumpUrl = jumpUrl;
    }

    public long getNumerical() {
        return numerical;
    }

    public void setNumerical(long numerical) {
        this.numerical = numerical;
    }

    public String getPreIcon() {
        return preIcon;
    }

    public void setPreIcon(String preIcon) {
        this.preIcon = preIcon;
    }

    public double getPreIconAspectRadio() {
        return preIconAspectRadio;
    }

    public void setPreIconAspectRadio(double preIconAspectRadio) {
        this.preIconAspectRadio = preIconAspectRadio;
    }
}
