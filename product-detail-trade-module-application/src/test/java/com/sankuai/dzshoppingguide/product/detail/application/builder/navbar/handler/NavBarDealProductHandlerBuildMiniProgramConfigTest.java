package com.sankuai.dzshoppingguide.product.detail.application.builder.navbar.handler;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.utils.AppImageSize;
import com.sankuai.dzshoppingguide.product.detail.application.utils.ImageHelper;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.ShareModuleMiniProgramConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class NavBarDealProductHandlerBuildMiniProgramConfigTest {

    @InjectMocks
    private NavBarDealProductHandler navBarDealProductHandler;

    @Mock
    private ProductDetailPageRequest request;

    @Mock
    private ShopIdMapper idMapper;

    private final NavBarDealProductHandler handler = new NavBarDealProductHandler();

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    /**
     * Test case for unknown client type
     */
    @Test
    public void testBuildMiniProgramConfigUnknownClientType() throws Throwable {
        // arrange
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.UNKNOWN);
        // act
        ShareModuleMiniProgramConfig config = navBarDealProductHandler.buildMiniProgramConfig(request, idMapper);
        // assert
        assertNotNull(config);
        assertNull(config.getMiniProgramId());
        assertNull(config.getPath());
    }

    /**
     * Test case for MT_WX client type
     */
    @Test
    public void testBuildMiniProgramConfigMtWx() throws Throwable {
        // arrange
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.MT_WX);
        when(request.getProductId()).thenReturn(12345L);
        when(idMapper.getMtBestShopId()).thenReturn(67890L);
        // act
        ShareModuleMiniProgramConfig config = navBarDealProductHandler.buildMiniProgramConfig(request, idMapper);
        // assert
        assertEquals("gh_870576f3c6f9", config.getMiniProgramId());
        String expectedPath = String.format("/gc/pages/deal/dealdetail/dealdetail?dealid=%s&shopid=%s&nsrc=2", request.getProductId(), idMapper.getMtBestShopId());
        assertEquals(expectedPath, config.getPath());
    }

    @Test
    public void testBuildMiniProgramConfigOther() throws Throwable {
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.UNKNOWN);
        ShareModuleMiniProgramConfig config = navBarDealProductHandler.buildMiniProgramConfig(request, idMapper);
        assertNull(config.getMiniProgramId());
        assertNull(config.getPath());
    }

    /**
     * Test case for when the input image is null
     */
    @Test
    void testBuildImageWhenImageIsNull() throws Throwable {
        // arrange
        String image = null;
        // act
        String result = navBarDealProductHandler.buildImage(image);
        // assert
        assertNull(result);
    }

    /**
     * Test case for when the input image is empty string
     */
    @Test
    void testBuildImageWhenImageIsEmpty() throws Throwable {
        // arrange
        String image = "";
        // act
        String result = navBarDealProductHandler.buildImage(image);
        // assert
        assertEquals("", result);
    }

    /**
     * Test case for when the input image is valid and mini program share size is valid
     */
    @Test
    void testBuildImageWhenImageIsValidAndMiniProgramShareSizeIsValid() throws Throwable {
        // arrange
        String image = "test_image.jpg";
        // act
        String result = navBarDealProductHandler.buildImage(image);
        // assert
        assertNotNull(result);
        // Since we can't mock static method, we can verify the result is not empty
        assertFalse(result.isEmpty());
    }

    /**
     * Test case for when the input image is valid but uses medium size
     */
    @Test
    void testBuildImageWhenImageIsValidAndUsesMediumSize() throws Throwable {
        // arrange
        String image = "test_image.jpg";
        NavBarDealProductHandler handler = new NavBarDealProductHandler() {

            int[] convertWidthHeight(int width, int height) {
                // Force using medium size
                return null;
            }
        };
        // act
        String result = handler.buildImage(image);
        // assert
        assertNotNull(result);
        assertFalse(result.isEmpty());
    }

    /**
     * Test case for when the input image is valid and uses mini program share size
     */
    @Test
    void testBuildImageWhenImageIsValidAndUsesMiniProgramShareSize() throws Throwable {
        // arrange
        String image = "test_image.jpg";
        NavBarDealProductHandler handler = new NavBarDealProductHandler() {

            int[] convertWidthHeight(int width, int height) {
                // Return valid dimensions
                return new int[] { width, height };
            }
        };
        // act
        String result = handler.buildImage(image);
        // assert
        assertNotNull(result);
        assertFalse(result.isEmpty());
    }

    /**
     * Test case for when the input image is valid but dimensions are invalid
     */
    @Test
    void testBuildImageWhenImageIsValidButDimensionsInvalid() throws Throwable {
        // arrange
        String image = "test_image.jpg";
        NavBarDealProductHandler handler = new NavBarDealProductHandler() {

            int[] convertWidthHeight(int width, int height) {
                // Return invalid dimensions
                return new int[] { 0, 0 };
            }
        };
        // act
        String result = handler.buildImage(image);
        // assert
        assertNotNull(result);
        assertFalse(result.isEmpty());
    }

    /**
     * Test case for when the input image is null.
     */
    @Test
    void testBuildImageImageIsNull() throws Throwable {
        // arrange
        String image = null;
        // act
        String result = navBarDealProductHandler.buildImage(image);
        // assert
        assertNull(result);
    }

    /**
     * Test MT_APP client type with valid idMapper
     */
    @Test
    public void testBuildMiniProgramConfig_MT_APP_WithIdMapper() throws Throwable {
        // arrange
        ProductDetailPageRequest request = mock(ProductDetailPageRequest.class);
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.MT_APP);
        when(request.getProductId()).thenReturn(12345L);
        ShopIdMapper idMapper = mock(ShopIdMapper.class);
        when(idMapper.getMtBestShopId()).thenReturn(67890L);
        // act
        ShareModuleMiniProgramConfig result = handler.buildMiniProgramConfig(request, idMapper);
        // assert
        assertEquals("gh_870576f3c6f9", result.getMiniProgramId());
        assertEquals("/gc/pages/deal/dealdetail/dealdetail?dealid=12345&shopid=67890&nsrc=2", result.getPath());
    }

    /**
     * Test MT_WX client type with null idMapper
     */
    @Test
    public void testBuildMiniProgramConfig_MT_WX_WithNullIdMapper() throws Throwable {
        // arrange
        ProductDetailPageRequest request = mock(ProductDetailPageRequest.class);
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.MT_WX);
        when(request.getProductId()).thenReturn(12345L);
        // act
        ShareModuleMiniProgramConfig result = handler.buildMiniProgramConfig(request, null);
        // assert
        assertEquals("gh_870576f3c6f9", result.getMiniProgramId());
        assertEquals("/gc/pages/deal/dealdetail/dealdetail?dealid=12345&shopid=0&nsrc=2", result.getPath());
    }

    /**
     * Test unknown client type
     */
    @Test
    public void testBuildMiniProgramConfig_UnknownClientType() throws Throwable {
        // arrange
        ProductDetailPageRequest request = mock(ProductDetailPageRequest.class);
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.UNKNOWN);
        // act
        ShareModuleMiniProgramConfig result = handler.buildMiniProgramConfig(request, null);
        // assert
        assertNotNull(result);
        assertNull(result.getMiniProgramId());
        assertNull(result.getPath());
    }

    /**
     * Test null request
     */
    @Test
    public void testBuildMiniProgramConfig_NullRequest() throws Throwable {
        // arrange
        ShopIdMapper idMapper = mock(ShopIdMapper.class);
        // act & assert
        assertThrows(NullPointerException.class, () -> {
            handler.buildMiniProgramConfig(null, idMapper);
        });
    }

    /**
     * Test MT_APP client type with null productId
     */
    @Test
    public void testBuildMiniProgramConfig_MT_APP_WithNullProductId() throws Throwable {
        // arrange
        ProductDetailPageRequest request = mock(ProductDetailPageRequest.class);
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.MT_APP);
        when(request.getProductId()).thenReturn(0L);
        ShopIdMapper idMapper = mock(ShopIdMapper.class);
        when(idMapper.getMtBestShopId()).thenReturn(67890L);
        // act
        ShareModuleMiniProgramConfig result = handler.buildMiniProgramConfig(request, idMapper);
        // assert
        assertEquals("gh_870576f3c6f9", result.getMiniProgramId());
        assertEquals("/gc/pages/deal/dealdetail/dealdetail?dealid=0&shopid=67890&nsrc=2", result.getPath());
    }
}
