// package com.sankuai.dzshoppingguide.product.detail.application.builder.headpic;
//
// import org.junit.Before;
// import org.junit.Test;
// import org.junit.runner.RunWith;
// import org.mockito.InjectMocks;
// import org.mockito.MockitoAnnotations;
// import org.powermock.core.classloader.annotations.PrepareForTest;
// import org.powermock.modules.junit4.PowerMockRunner;
//
// /**
//  * @Author: wb_wangxiaoguang02
//  * @Date: 2025/2/13 20:02
//  */
// @RunWith(PowerMockRunner.class)
// @PrepareForTest
// public class HeadPicModuleBuilderTest {
//     @InjectMocks
//     private HeadPicModuleBuilder headPicModuleBuilder;
//
//     @Before
//     public void setUp() {
//         MockitoAnnotations.initMocks(this);
//     }
//
//     @Test
//     public void testBuild() {
//
//
//     }
//
//     private static final String baseInfoJson = "{\"@class\":\"com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo\",\"productIdDTO\":{\"@class\":\"com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.dto.ProductIdDTO\",\"productType\":\"RESERVE\",\"productModelType\":\"FUN_PRODUCT\",\"dpProductId\":1037179209,\"mtProductId\":1037179209},\"basic\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupBasicDTO\",\"categoryId\":521,\"title\":\"足浴测试｜1月15\",\"brandName\":null,\"titleDesc\":null,\"beginSaleDate\":\"2025-01-15 16:37:26\",\"endSaleDate\":\"2104-12-27 16:37:26\",\"status\":1,\"saleChannel\":null,\"salePlatform\":null,\"sourceId\":null,\"tradeType\":4,\"platformCategoryId\":81521,\"addTime\":\"2025-01-15 17:34:45\",\"updateTime\":\"2025-01-15 17:34:45\"},\"image\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupImageDTO\",\"defaultPicPath\":\"https://p1.meituan.net[\\\"http://p0.meituan.net/dpmerchantpic/8c254e227b28381b0141dde5d4e9d9e9104960.png\\\",\\\"http://p0.meituan.net/dpmerchantpic/860fd58ca4afb0189a76dc411d120e3d295498.png\\\"]\",\"allPicPaths\":\"https://p1.meituan.net[\\\"http://p0.meituan.net/dpmerchantpic/8c254e227b28381b0141dde5d4e9d9e9104960.png\\\",\\\"http://p0.meituan.net/dpmerchantpic/860fd58ca4afb0189a76dc411d120e3d295498.png\\\"]\",\"videoPath\":null,\"videoCoverPath\":null,\"videoSize\":null,\"extendVideos\":null,\"allVideos\":null},\"bgBu\":null,\"channel\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupChannelDTO\",\"channelId\":-1,\"channelEn\":\"other\",\"channelCn\":\"其他\",\"channelGroupId\":2,\"channelGroupEn\":\"general\",\"channelGroupCn\":\"到店综合\"},\"rule\":null,\"displayShopInfo\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupDisplayShopDTO\",\"dpDisplayShopIds\":[\"java.util.ArrayList\",[1055891017273590]],\"mtDisplayShopIds\":[\"java.util.ArrayList\",[1055891017273590]]},\"tags\":null,\"regions\":[\"java.util.ArrayList\",[{\"@class\":\"com.sankuai.general.product.query.center.client.dto.DealGroupRegionDTO\",\"dpCityId\":1,\"mtCityId\":10}]],\"price\":null,\"saleChannelAggregation\":{\"@class\":\"com.sankuai.general.product.query.center.client.dto.SaleChannelAggregationDTO\",\"allSupport\":true,\"supportChannels\":[\"java.util.ArrayList\",[]],\"notSupportChannels\":[\"java.util.ArrayList\",[]]}}";
// }