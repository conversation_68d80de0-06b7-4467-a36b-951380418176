package com.sankuai.dzshoppingguide.product.detail.application.fetcher.haima;

import com.dianping.haima.client.HaimaClient;
import com.dianping.haima.client.request.HaimaRequest;
import com.dianping.haima.client.response.HaimaResponse;
import com.dianping.haima.entity.haima.HaimaConfig;
import com.dianping.haima.entity.haima.HaimaContent;
import com.dianping.lion.common.util.JsonUtils;
import com.google.common.collect.Lists;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.dealgroup.DealGroupIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.shopid.ShopIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.haima.dtos.BlackListShopConfig;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.best.shop.ShopInfoFetcher;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @create 2025/5/28 17:05
 */
@Fetcher(
        previousLayerDependencies = {
                ShopIdMapperFetcher.class,
                ShopInfoFetcher.class,
                DealGroupIdMapperFetcher.class})
@Slf4j
public class HaiMaFetcher extends NormalFetcherContext<HaiMaReturnValue> {

    @Autowired
    private HaimaClient haimaClient;

    @Override
    protected CompletableFuture<HaiMaReturnValue> doFetch() throws Exception {
        try{
            HaiMaReturnValue returnValue = new HaiMaReturnValue();
            boolean isBlackListShop = isBlackListShop(String.valueOf(request.getPoiId()), request.getClientTypeEnum().isMtClientType());
            returnValue.setBlackListShop(isBlackListShop);
            return CompletableFuture.completedFuture(returnValue);
        }catch (Exception e){
            log.error("HaiMaFetcher.doFetch error:", e);
        }
        return CompletableFuture.completedFuture(new HaiMaReturnValue());
    }

    private boolean isBlackListShop(String shopId, boolean isMt) {
        if (StringUtils.isBlank(shopId)) {
            return false;
        }
        BlackListShopConfig config = getBlackListShopConfig();
        if (Objects.isNull(config)) {
            return false;
        }
        List<String> shopIds = isMt ? config.getMtShopIds() : config.getDpShopIds();
        if (CollectionUtils.isEmpty(shopIds)) {
            return false;
        }
        return shopIds.stream().anyMatch(id -> id.equals(shopId));
    }
    
    private BlackListShopConfig getBlackListShopConfig() {
        HaimaRequest request = new HaimaRequest();
        // 业务方申请的运营位key
        request.setSceneKey("dz_compare_price_blacklist_shop");
        HaimaResponse response = queryHaimaConfig(request);
        List<HaimaConfig> haimaData = Optional.ofNullable(response)
                .orElse(new HaimaResponse())
                .getData();
        List<HaimaContent> haimaContents = Optional.ofNullable(haimaData)
                .orElse(Lists.newArrayList())
                .stream()
                .findFirst()
                .orElse(new HaimaConfig())
                .getContents();
        String extJson = Optional.ofNullable(haimaContents)
                .orElse(Lists.newArrayList())
                .stream()
                .findFirst()
                .orElse(new HaimaContent())
                .getExtJson();
        try {
            return JsonUtils.fromJson(extJson, BlackListShopConfig.class);
        } catch (Exception e) {
            log.error("[HaimaWrapper] getBlackListShopConfig  error, blacklist json={}", extJson, e);
            return null;
        }
    }

    public HaimaResponse queryHaimaConfig(HaimaRequest request) {
        HaimaResponse response = null;
        try {
            response = haimaClient.query(request);
            if (response.isSuccess()) {
                return response;
            }
        } catch (Exception e) {
            log.error("queryHaimaConfig error", e);
        }
        return response;
    }
}
