package com.sankuai.dzshoppingguide.product.detail.application.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.dianping.pigeon.remoting.invoker.concurrent.InvocationCallback;
import com.dianping.pigeon.remoting.invoker.util.InvokerHelper;
import com.dianping.product.shelf.common.dto.Response;
import com.dianping.product.shelf.common.dto.activity.ActivityDetailDTO;
import com.dianping.product.shelf.common.request.activities.ActivityProductQueryRequest;
import com.dianping.product.shelf.query.api.ActivityShelfQueryService;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;
import com.google.common.util.concurrent.SettableFuture;
import com.meituan.service.mobile.mtthrift.netty.ContextStore;
import com.sankuai.dzshoppingguide.product.detail.domain.utils.ThriftAsyncUtils;
import com.sankuai.mpproduct.idservice.api.model.GroupIdDTO;
import com.sankuai.mpproduct.idservice.api.request.DpGroupIdConvertRequest;
import com.sankuai.mpproduct.idservice.api.response.GroupIdConvertResponse;
import com.sankuai.mpproduct.idservice.api.service.IdService;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import org.apache.thrift.TException;

@ExtendWith(MockitoExtension.class)
class CompositeAtomServiceImplQuerySpecialValueDeal2Test {

    @Mock
    private ActivityShelfQueryService activityShelfQueryServiceFuture;

    @InjectMocks
    private CompositeAtomServiceImpl compositeAtomService;

    private ActivityProductQueryRequest request;

    @Mock
    private IdService idService;

    @BeforeEach
    void setUp() {
        request = new ActivityProductQueryRequest();
        // 使用Arrays.asList替代List.of
        request.setProductIds(Arrays.asList(1, 2, 3));
        request.setShopId(123L);
        request.setProductType(1);
    }

    /**
     * 测试当activityShelfQueryService返回null时，方法应返回null
     */
    @Test
    void testQuerySpecialValueDeal_ServiceReturnsNull() throws Throwable {
        // arrange
        doAnswer((InvocationOnMock invocation) -> {
            InvocationCallback callback = (InvocationCallback) InvokerHelper.getCallback();
            callback.onSuccess(null);
            return null;
        }).when(activityShelfQueryServiceFuture).queryOnlineActivity(any());
        // act
        CompletableFuture<ActivityDetailDTO> resultFuture = compositeAtomService.querySpecialValueDeal(request);
        // assert
        assertNull(resultFuture.join(), "Result should be null when service returns null");
    }

    /**
     * 测试当activityShelfQueryService抛出异常时，方法应返回null并记录错误日志
     */
    @Test
    void testQuerySpecialValueDeal_ServiceThrowsException() throws Throwable {
        // arrange
        doAnswer((InvocationOnMock invocation) -> {
            InvocationCallback callback = (InvocationCallback) InvokerHelper.getCallback();
            callback.onFailure(new RuntimeException("Service error"));
            return null;
        }).when(activityShelfQueryServiceFuture).queryOnlineActivity(any());
        // act
        CompletableFuture<ActivityDetailDTO> resultFuture = compositeAtomService.querySpecialValueDeal(request);
        // assert
        assertNull(resultFuture.join(), "Result should be null when service throws exception");
    }

    /**
     * 测试当activityShelfQueryService返回不成功的响应时，方法应返回null
     */
    @Test
    void testQuerySpecialValueDeal_ServiceReturnsUnsuccessfulResponse() throws Throwable {
        // arrange
        Response<ActivityDetailDTO> response = new Response<>();
        response.setSuccess(false);
        doAnswer((InvocationOnMock invocation) -> {
            InvocationCallback callback = (InvocationCallback) InvokerHelper.getCallback();
            callback.onSuccess(response);
            return null;
        }).when(activityShelfQueryServiceFuture).queryOnlineActivity(any());
        // act
        CompletableFuture<ActivityDetailDTO> resultFuture = compositeAtomService.querySpecialValueDeal(request);
        // assert
        assertNull(resultFuture.join(), "Result should be null when service returns unsuccessful response");
    }

    /**
     * 测试当activityShelfQueryService返回成功的响应但内容为null时，方法应返回null
     */
    @Test
    void testQuerySpecialValueDeal_ServiceReturnsSuccessButContentIsNull() throws Throwable {
        // arrange
        Response<ActivityDetailDTO> response = new Response<>();
        response.setSuccess(true);
        response.setContent(null);
        doAnswer((InvocationOnMock invocation) -> {
            InvocationCallback callback = (InvocationCallback) InvokerHelper.getCallback();
            callback.onSuccess(response);
            return null;
        }).when(activityShelfQueryServiceFuture).queryOnlineActivity(any());
        // act
        CompletableFuture<ActivityDetailDTO> resultFuture = compositeAtomService.querySpecialValueDeal(request);
        // assert
        assertNull(resultFuture.join(), "Result should be null when service returns success but content is null");
    }

    /**
     * 测试当activityShelfQueryService返回成功的响应且有内容时，方法应返回正确的内容
     */
    @Test
    void testQuerySpecialValueDeal_ServiceReturnsSuccessWithContent() throws Throwable {
        // arrange
        ActivityDetailDTO expected = new ActivityDetailDTO();
        expected.setActivityId(1001);
        expected.setName("Test Activity");
        Response<ActivityDetailDTO> response = new Response<>();
        response.setSuccess(true);
        response.setContent(expected);
        doAnswer((InvocationOnMock invocation) -> {
            InvocationCallback callback = (InvocationCallback) InvokerHelper.getCallback();
            callback.onSuccess(response);
            return null;
        }).when(activityShelfQueryServiceFuture).queryOnlineActivity(any());
        // act
        CompletableFuture<ActivityDetailDTO> resultFuture = compositeAtomService.querySpecialValueDeal(request);
        // assert
        ActivityDetailDTO result = resultFuture.join();
        assertNotNull(result, "Result should not be null when service returns success with content");
        assertEquals(expected.getActivityId(), result.getActivityId(), "Activity ID should match");
        assertEquals(expected.getName(), result.getName(), "Activity name should match");
    }

    @Test
    public void testConvertDpGroupIdsToMtGroupIds_ServiceException() throws Throwable {
        // arrange
        List<Long> dpGroupIds = new ArrayList<>();
        dpGroupIds.add(1001L);
        when(idService.convertDpGroupIdsToMtGroupIds(any(DpGroupIdConvertRequest.class))).thenThrow(new TException("Service error"));
        // act
        CompletableFuture<Map<Long, Long>> resultFuture = compositeAtomService.convertDpGroupIdsToMtGroupIds(dpGroupIds);
        // assert
        Map<Long, Long> result = resultFuture.get();
        assertTrue(result.isEmpty());
    }
}
