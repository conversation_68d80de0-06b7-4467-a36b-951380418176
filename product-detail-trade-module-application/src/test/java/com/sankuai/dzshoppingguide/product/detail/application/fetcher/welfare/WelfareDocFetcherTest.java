package com.sankuai.dzshoppingguide.product.detail.application.fetcher.welfare;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.response.FetcherResponse;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.BaseFetcherContext;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.shopid.ShopIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.shopid.ShopIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.customer.ProductCustomer;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.customer.ProductCustomerFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
import com.sankuai.meituan.charity.merchant.main.sdk.dto.request.DzVpoiReq;
import com.sankuai.meituan.charity.merchant.main.sdk.dto.response.DzProductDocResp;
import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class WelfareDocFetcherTest {

    private TestableWelfareDocFetcher welfareDocFetcher;

    @Mock
    private CompositeAtomService compositeAtomService;

    @Mock
    private ProductCustomer productCustomer;

    @Mock
    private ShopIdMapper shopIdMapper;

    // 创建一个可测试的子类
    private static class TestableWelfareDocFetcher extends WelfareDocFetcher {

        private final Map<String, BaseFetcherContext> fetcherContextMap = new HashMap<>();

        public TestableWelfareDocFetcher() {
            super();
        }

        public void initializeContext(ProductCustomer productCustomer, ShopIdMapper shopIdMapper) {
            // 初始化 ProductCustomerFetcher 的响应
            FetcherResponse<ProductCustomer> productCustomerResponse = FetcherResponse.succeed(productCustomer);
            BaseFetcherContext productCustomerContext = mock(BaseFetcherContext.class);
            when(productCustomerContext.getFetchResponse()).thenReturn(productCustomerResponse);
            fetcherContextMap.put(ProductCustomerFetcher.class.getName(), productCustomerContext);
            // 初始化 ShopIdMapperFetcher 的响应
            FetcherResponse<ShopIdMapper> shopIdMapperResponse = FetcherResponse.succeed(shopIdMapper);
            BaseFetcherContext shopIdMapperContext = mock(BaseFetcherContext.class);
            when(shopIdMapperContext.getFetchResponse()).thenReturn(shopIdMapperResponse);
            fetcherContextMap.put(ShopIdMapperFetcher.class.getName(), shopIdMapperContext);
        }

        @Override
        protected <T extends FetcherReturnValueDTO> FetcherResponse<T> getDependencyResponse(Class<? extends BaseFetcherContext> dependencyFetcherClass) {
            BaseFetcherContext context = fetcherContextMap.get(dependencyFetcherClass.getName());
            return context != null ? context.getFetchResponse() : null;
        }
    }

    @BeforeEach
    void setUp() throws Exception {
        // 创建并初始化 TestableWelfareDocFetcher
        welfareDocFetcher = new TestableWelfareDocFetcher();
        welfareDocFetcher.initializeContext(productCustomer, shopIdMapper);
        // 使用反射设置私有字段
        Field compositeAtomServiceField = WelfareDocFetcher.class.getDeclaredField("compositeAtomService");
        compositeAtomServiceField.setAccessible(true);
        compositeAtomServiceField.set(welfareDocFetcher, compositeAtomService);
        // 设置基础行为
        when(shopIdMapper.getMtBestShopId()).thenReturn(1L);
        when(productCustomer.getOriginCustomerId()).thenReturn(2L);
    }

    /**
     * Test case: When signCharity is true, should return WelfareDocInfo
     */
    @Test
    void testDoFetch_WhenSignCharityTrue_ShouldReturnWelfareDocInfo() throws Throwable {
        // arrange
        DzProductDocResp dzProductDocResp = new DzProductDocResp();
        dzProductDocResp.setSignCharity(true);
        when(compositeAtomService.queryDzProductDoc(any(DzVpoiReq.class))).thenReturn(CompletableFuture.completedFuture(dzProductDocResp));
        // act
        CompletableFuture<WelfareDocInfo> result = welfareDocFetcher.doFetch();
        // assert
        assertNotNull(result);
        WelfareDocInfo welfareDocInfo = result.get();
        assertNotNull(welfareDocInfo);
        assertEquals(dzProductDocResp, welfareDocInfo.getDzProductDocResp());
    }

    /**
     * Test case: When signCharity is false, should return null WelfareDocInfo
     */
    @Test
    void testDoFetch_WhenSignCharityFalse_ShouldReturnNull() throws Throwable {
        // arrange
        DzProductDocResp dzProductDocResp = new DzProductDocResp();
        dzProductDocResp.setSignCharity(false);
        when(compositeAtomService.queryDzProductDoc(any(DzVpoiReq.class))).thenReturn(CompletableFuture.completedFuture(dzProductDocResp));
        // act
        CompletableFuture<WelfareDocInfo> result = welfareDocFetcher.doFetch();
        // assert
        assertNotNull(result);
        assertNull(result.get());
    }

    /**
     * Test case: When queryDzProductDoc throws exception, should complete exceptionally
     */
    @Test
    void testDoFetch_WhenQueryFails_ShouldCompleteExceptionally() throws Throwable {
        // arrange
        CompletableFuture<DzProductDocResp> failedFuture = new CompletableFuture<>();
        failedFuture.completeExceptionally(new RuntimeException("Query failed"));
        when(compositeAtomService.queryDzProductDoc(any(DzVpoiReq.class))).thenReturn(failedFuture);
        // act
        CompletableFuture<WelfareDocInfo> result = welfareDocFetcher.doFetch();
        // assert
        assertNotNull(result);
        assertTrue(result.isCompletedExceptionally());
    }

    /**
     * Test case: Verify DzVpoiReq parameters are set correctly
     */
    @Test
    void testDoFetch_ShouldSetCorrectDzVpoiReqParameters() throws Throwable {
        // arrange
        when(compositeAtomService.queryDzProductDoc(any(DzVpoiReq.class))).thenReturn(CompletableFuture.completedFuture(new DzProductDocResp()));
        // act
        welfareDocFetcher.doFetch();
        // assert
        verify(compositeAtomService).queryDzProductDoc(argThat(req -> {
            assertEquals("1", req.getPoiId());
            assertEquals("2", req.getPartnerId());
            return true;
        }));
    }
}
