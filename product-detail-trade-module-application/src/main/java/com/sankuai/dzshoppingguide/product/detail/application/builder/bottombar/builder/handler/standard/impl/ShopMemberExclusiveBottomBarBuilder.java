package com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.builder.handler.standard.impl;

import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.BuilderTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.builder.handler.standard.StandardBottomBarBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.component.trade.block.banner.CouponBannerComponent;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.component.trade.block.buy.button.DefaultTradeButtonComponent;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.component.trade.block.buy.button.StandardTradeButtonComponent;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.component.trade.block.member.exclusive.MemberExclusiveButtonComponent;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.membercard.card.premium.MemberCardInterestFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.membercard.card.premium.PremiumMemberCardResult;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.handler.normal.NormalOrderPageUrlFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sale.info.enums.ProductSaleStatusEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sale.info.enums.ProductSaleTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.FetcherDAGStarter;
import com.sankuai.dzshoppingguide.product.detail.application.service.dto.ShopMemberDetailV;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.banner.BottomBarTopBannerVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.ProductBottomBarVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.block.trade.StandardTradeBlockVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.component.trade.button.StandardTradeButtonVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.scene.StandardTradeBottomBarVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.backgroud.BottomBarBackgroundVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.rich.content.TextRichContentVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.rich.content.enums.TextStyleEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;

import java.util.Optional;

/**
 * @Author: guangyujie
 * @Date: 2025/4/2 09:49
 */
@Builder(
        builderType = BuilderTypeEnum.VARIABLE_BUILDER,
        moduleKey = ModuleKeyConstants.BOTTOM_BAR,
        startFetcher = FetcherDAGStarter.class,
        dependentFetchers = {
                NormalOrderPageUrlFetcher.class,
                MemberCardInterestFetcher.class,
        }
)
@Slf4j
public class ShopMemberExclusiveBottomBarBuilder extends StandardBottomBarBuilder {

    private ShopMemberDetailV shopMemberDetailV;
    private ProductSaleStatusEnum saleStatus;
    private boolean isMember;

    @Override
    protected void prepareData() {
        shopMemberDetailV = getDependencyResult(MemberCardInterestFetcher.class, PremiumMemberCardResult.class)
                .map(PremiumMemberCardResult::getShopMemberDetailV).orElse(null);
        isMember = Optional.ofNullable(shopMemberDetailV).map(ShopMemberDetailV::isMember).orElse(false);
        saleStatus = Optional.ofNullable(productSaleInfo)
                .map(saleInfo -> saleInfo.getSaleStatus(ProductSaleTypeEnum.MEMBER_EXCLUSIVE_DEAL))
                .orElse(ProductSaleStatusEnum.ONLINE);


    }

    @Override
    protected ProductBottomBarVO buildProductBottomBarVO() {
        StandardTradeBottomBarVO standardTradeBottomBarVO = new StandardTradeBottomBarVO();
        standardTradeBottomBarVO.setLeftBottomBar(buildQuickEntranceBlock());
        standardTradeBottomBarVO.setRightBottomBar(buildStandardTradeBlock());
        if (saleStatus == ProductSaleStatusEnum.ONLINE
                || saleStatus == ProductSaleStatusEnum.MEMBER_EXCLUSIVE) {
            //会员专属卡控不能直接交易但是可以加入购物车
            addShoppingCartButton(standardTradeBottomBarVO, ProductSaleStatusEnum.ONLINE);
        } else {
            addShoppingCartButton(standardTradeBottomBarVO, ProductSaleStatusEnum.OFFLINE);
        }
        return standardTradeBottomBarVO;
    }

    /**
     * 交易按钮区域
     */
    private @NotNull StandardTradeBlockVO buildStandardTradeBlock() {
        //构建右侧交易按钮
        StandardTradeButtonVO rightButton = null;
        if (saleStatus == ProductSaleStatusEnum.MEMBER_EXCLUSIVE) {
            rightButton = MemberExclusiveButtonComponent.build(
                    request, normalPrice, purchaseCouponReturnValue, shopMemberDetailV
            );
        }
        if (rightButton == null) {
            rightButton = StandardTradeButtonComponent.build(
                    request, productCategory, normalPriceReturnValue, orderPageUrl, purchaseCouponReturnValue, saleStatus
            );
        }
        if (rightButton == null) {  //最终兜底
            rightButton = DefaultTradeButtonComponent.build(request, orderPageUrl);
        }
        return StandardTradeBlockVO.buildSingleButtonStyle(rightButton);
    }

    @Override
    protected BottomBarTopBannerVO buildBanner() {
        if (isMember) {
            // 会员优先级高于神券吸底条,所以不传神券吸底条参数
            return CouponBannerComponent.build(request, productCategory, dealPromoPrice);
        }
        // 非会员时展示
        BottomBarTopBannerVO banner = new BottomBarTopBannerVO();
        banner.add(new TextRichContentVO("会员专属商品，仅限商家会员购买", TextStyleEnum.Default, 24, "#8E3C12"));
        banner.setBackground(BottomBarBackgroundVO.buildSingleColor("#FFEDDE"));
        return banner;
    }

}
