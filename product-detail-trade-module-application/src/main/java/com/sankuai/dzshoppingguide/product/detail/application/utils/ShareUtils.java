package com.sankuai.dzshoppingguide.product.detail.application.utils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Random;

/**
 * @Author: zheng<PERSON><EMAIL>
 * @Date: 2025/3/7
 */
public class ShareUtils {
    public static String shareIdGenerator(String uuid) {
        // 获取当前时间并格式化为指定格式
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String timeStr = sdf.format(new Date());

        // 构造数据部分
        StringBuilder data = new StringBuilder();
        if (uuid != null && !uuid.isEmpty()) {
            data.append(uuid);
        } else {
            for (int i = 0; i < 64; i++) {
                data.append("0");
            }
        }

        // 添加随机数部分
        Random random = new Random();
        for (int i = 0; i < 3; i++) {
            data.append(random.nextInt(10)); // 生成0-9的随机整数
        }

        // 拼接时间和数据部分
        return timeStr + data.toString();
    }
}
