package com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.component.trade.block.cepintuan.utils;

import com.sankuai.dzshoppingguide.product.detail.application.fetcher.cepinpool.dto.CostEffectivePinTuan;
import com.sankuai.inf.octo.mns.model.HostEnv;
import com.sankuai.inf.octo.mns.util.ProcessInfoUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;

import java.net.URLEncoder;

/**
 * @Author: guangyujie
 * @Date: 2025/3/20 23:29
 */
@Slf4j
public class CEPTUrlUtils {

    public static String getPinTuanResultUrl(@NotNull final CostEffectivePinTuan costEffectivePinTuan) {
        return String.format(
                "imeituan://www.meituan.com/mrn?mrn_biz=meishi&mrn_entry=group-pintuan-result&mrn_component=main&orderGroupId=%s&scene=share_page",
                costEffectivePinTuan.getShareToken()
        );
    }

    private static final String PIN_TUAN_SCHEMA = ProcessInfoUtil.getHostEnv() == HostEnv.DEV || ProcessInfoUtil.getHostEnv() == HostEnv.TEST
            ? "http://awp.hfe.st.meituan.com/dfe/duo-page/group-pintuan-result/web/index.html"
            : "https://awp.meituan.com/dfe/duo-page/group-pintuan-result/web/index.html";

    public static String getWxShareJumpUrl(@NotNull final CostEffectivePinTuan costEffectivePinTuan) {
        String result = StringUtils.EMPTY;
        String pinTuanUrl = String.format("%s?orderGroupId=%s&scene=share_page", PIN_TUAN_SCHEMA, costEffectivePinTuan.getShareToken());
        try {
            result = String.format("/index/pages/h5/h5?weburl=%s&f_openId=1&f_userId=1&f_token=1&f_userId=1&f_ci=1&f_pos=1&barcol=FF112E&barfcol=FFFFFF&title=特价团购拼团",
                    URLEncoder.encode(pinTuanUrl, "UTF-8"));
        } catch (Exception e) {
            log.error("getShareJumpUrl error", e);
        }
        return result;
    }

}
