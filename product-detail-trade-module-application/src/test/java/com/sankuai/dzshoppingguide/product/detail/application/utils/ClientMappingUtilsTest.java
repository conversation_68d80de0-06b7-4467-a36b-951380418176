package com.sankuai.dzshoppingguide.product.detail.application.utils;

import static com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.dianping.deal.common.enums.ClientTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.enums.MobileOSTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class ClientMappingUtilsTest {

    @Mock
    private ProductDetailPageRequest pageRequest;

    /**
     * Test DP_APP client type with iOS mobile OS
     */
    @Test
    public void testGetBpClientType_DpAppIos() throws Throwable {
        // arrange
        when(pageRequest.getClientTypeEnum()).thenReturn(DP_APP);
        when(pageRequest.getMobileOSType()).thenReturn(MobileOSTypeEnum.IOS);
        // act
        com.dianping.deal.common.enums.ClientTypeEnum result = ClientMappingUtils.getBpClientType(pageRequest);
        // assert
        assertEquals(com.dianping.deal.common.enums.ClientTypeEnum.dp_mainApp_ios, result);
    }

    /**
     * Test DP_APP client type with Android mobile OS
     */
    @Test
    public void testGetBpClientType_DpAppAndroid() throws Throwable {
        // arrange
        when(pageRequest.getClientTypeEnum()).thenReturn(DP_APP);
        when(pageRequest.getMobileOSType()).thenReturn(MobileOSTypeEnum.ANDROID);
        // act
        com.dianping.deal.common.enums.ClientTypeEnum result = ClientMappingUtils.getBpClientType(pageRequest);
        // assert
        assertEquals(com.dianping.deal.common.enums.ClientTypeEnum.dp_mainApp_android, result);
    }

    /**
     * Test MT_APP client type with iOS mobile OS
     */
    @Test
    public void testGetBpClientType_MtAppIos() throws Throwable {
        // arrange
        when(pageRequest.getClientTypeEnum()).thenReturn(MT_APP);
        when(pageRequest.getMobileOSType()).thenReturn(MobileOSTypeEnum.IOS);
        // act
        com.dianping.deal.common.enums.ClientTypeEnum result = ClientMappingUtils.getBpClientType(pageRequest);
        // assert
        assertEquals(com.dianping.deal.common.enums.ClientTypeEnum.mt_mainApp_ios, result);
    }

    /**
     * Test MT_APP client type with Android mobile OS
     */
    @Test
    public void testGetBpClientType_MtAppAndroid() throws Throwable {
        // arrange
        when(pageRequest.getClientTypeEnum()).thenReturn(MT_APP);
        when(pageRequest.getMobileOSType()).thenReturn(MobileOSTypeEnum.ANDROID);
        // act
        com.dianping.deal.common.enums.ClientTypeEnum result = ClientMappingUtils.getBpClientType(pageRequest);
        // assert
        assertEquals(com.dianping.deal.common.enums.ClientTypeEnum.mt_mainApp_android, result);
    }

    /**
     * Test MT_WX client type (WeChat mini program)
     */
    @Test
    public void testGetBpClientType_MtWx() throws Throwable {
        // arrange
        when(pageRequest.getClientTypeEnum()).thenReturn(MT_WX);
        // act
        com.dianping.deal.common.enums.ClientTypeEnum result = ClientMappingUtils.getBpClientType(pageRequest);
        // assert
        assertEquals(com.dianping.deal.common.enums.ClientTypeEnum.mt_weApp, result);
    }

    /**
     * Test DP_WX client type (WeChat mini program)
     */
    @Test
    public void testGetBpClientType_DpWx() throws Throwable {
        // arrange
        when(pageRequest.getClientTypeEnum()).thenReturn(DP_WX);
        // act
        com.dianping.deal.common.enums.ClientTypeEnum result = ClientMappingUtils.getBpClientType(pageRequest);
        // assert
        assertEquals(com.dianping.deal.common.enums.ClientTypeEnum.dp_weApp, result);
    }

    /**
     * Test DP_BAIDU_MAP_XCX client type (Baidu Map mini program)
     */
    @Test
    public void testGetBpClientType_DpBaiduMapXcx() throws Throwable {
        // arrange
        when(pageRequest.getClientTypeEnum()).thenReturn(DP_BAIDU_MAP_XCX);
        // act
        com.dianping.deal.common.enums.ClientTypeEnum result = ClientMappingUtils.getBpClientType(pageRequest);
        // assert
        assertEquals(com.dianping.deal.common.enums.ClientTypeEnum.dp_baiduMap_weApp, result);
    }

    /**
     * Test MT_KUAI_SHOU_XCX client type (Kuaishou mini program)
     */
    @Test
    public void testGetBpClientType_MtKuaishouXcx() throws Throwable {
        // arrange
        when(pageRequest.getClientTypeEnum()).thenReturn(MT_KUAI_SHOU_XCX);
        // act
        com.dianping.deal.common.enums.ClientTypeEnum result = ClientMappingUtils.getBpClientType(pageRequest);
        // assert
        assertEquals(com.dianping.deal.common.enums.ClientTypeEnum.mt_mainApp_ios, result);
    }

    /**
     * Test default case with unknown client type
     */
    @Test
    public void testGetBpClientType_DefaultCase() throws Throwable {
        // arrange
        when(pageRequest.getClientTypeEnum()).thenReturn(UNKNOWN);
        // act
        com.dianping.deal.common.enums.ClientTypeEnum result = ClientMappingUtils.getBpClientType(pageRequest);
        // assert
        assertEquals(com.dianping.deal.common.enums.ClientTypeEnum.mt_mainApp_ios, result);
    }
}
