package com.sankuai.dzshoppingguide.product.detail.spi.makeup.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.dz.product.detail.gateway.spi.vo.AbstractModuleVO;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.MarkupDetailModuleConfig;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.MarkupModuleItem;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2025/3/3
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TypeDoc(description = "加价详情模块")
@MobileDo(id = 0xcc9c)
public class MarkupDetailVO extends AbstractModuleVO {
    @Override
    public String getModuleKey() {
        return ModuleKeyConstants.MODULE_DETAIL_DEAL_MARKUP_DETAIL;
    }

    @FieldDoc(description = "加价项详情信息")
    @MobileDo.MobileField(key = 0xc326)
    private List<MarkupModuleItem> moduleItems;

    @FieldDoc(description = "加价模块信息")
    @MobileDo.MobileField(key = 0xa4f2)
    private MarkupDetailModuleConfig moduleConfig;
}
