package com.sankuai.dzshoppingguide.product.detail.application.builder.multisku.builder;

import com.google.common.collect.Maps;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.BuilderTypeEnum;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.extend.BaseVariableBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.abtest.AbTestFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategoryFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.sku.SkuPriceFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.sku.SkuPriceReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.attr.SkuAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.defaultselect.SkuDefaultSelectFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.saleattr.ProductSaleAttrFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.FetcherDAGStarter;
import com.sankuai.dzshoppingguide.product.detail.application.utils.DealSkuUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.MultiSkuSelectItemVO;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.MultiSkuSelectRichTextVO;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.multisku.vo.MultiSkuSelectVO;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupBasicDTO;
import com.sankuai.general.product.query.center.client.dto.deal.DealGroupDealDTO;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-04-22
 * @desc
 */
@Builder(
        builderType = BuilderTypeEnum.VARIABLE_BUILDER,
        moduleKey = ModuleKeyConstants.MODULE_DETAIL_DEAL_MULTI_SKU_SELECT,
        startFetcher = FetcherDAGStarter.class,
        dependentFetchers = {
                SkuPriceFetcher.class,
                SkuDefaultSelectFetcher.class,
                ProductBaseInfoFetcher.class,
                AbTestFetcher.class,
                ProductCategoryFetcher.class,
                ProductSaleAttrFetcher.class
        }
)
public class DigitalApplianceMultiSkuSelectModuleBuilder extends BaseVariableBuilder<MultiSkuSelectVO> {

    private SkuAttr skuSaleAttr;

    @Override
    public MultiSkuSelectVO doBuild() {
        ProductCategory productCategory = getDependencyResult(ProductCategoryFetcher.class);
        ProductBaseInfo baseInfo = getDependencyResult(ProductBaseInfoFetcher.class);
        SkuPriceReturnValue priceReturnValue = getDependencyResult(SkuPriceFetcher.class);
        skuSaleAttr = getDependencyResult(ProductSaleAttrFetcher.class, SkuAttr.class)
                .orElse(new SkuAttr(Maps.newHashMap()));
        if (Objects.isNull(priceReturnValue) || CollectionUtils.isEmpty(priceReturnValue.getPriceDisplayDTOList())) {
            return null;
        }
        List<PriceDisplayDTO> priceDisplayDTOS = priceReturnValue.getPriceDisplayDTOList();
        Map<Long, PriceDisplayDTO> skuIdPriceMap = priceDisplayDTOS.stream()
                .collect(Collectors.toMap(p -> (long)p.getIdentity().getSkuId(), Function.identity(), (x, y) -> x));
        List<MultiSkuSelectItemVO> skuSelectItems = buildMultiSkuSelectItems(baseInfo, skuIdPriceMap, productCategory);
        MultiSkuSelectVO multiSkuSelectVO = new MultiSkuSelectVO();
        multiSkuSelectVO.setSkuSelectItems(skuSelectItems);
        return multiSkuSelectVO;
    }

    private List<MultiSkuSelectItemVO> buildMultiSkuSelectItems(ProductBaseInfo productBaseInfo, Map<Long, PriceDisplayDTO> skuIdPriceMap, ProductCategory productCategory) {
        List<DealGroupDealDTO> dealGroupDealDTOS = Optional.ofNullable(productBaseInfo)
                .map(ProductBaseInfo::getDeals)
                .filter(CollectionUtils::isNotEmpty)
                .orElse(null);
        if (CollectionUtils.isEmpty(dealGroupDealDTOS)) {
            return null;
        }
        List<DealGroupDealDTO> validDealGroupDeals = DealSkuUtils.getDigitalApplianceDealSkuList(dealGroupDealDTOS);
        if (CollectionUtils.isEmpty(validDealGroupDeals)) {
            return null;
        }
        // sku在名称上展示的属性
        Integer thirdCategoryId = Optional.ofNullable(productCategory)
                .map(ProductCategory::getProductThirdCategoryId)
                .orElse(0);
        // 商品标题
        String productTitle = Optional.ofNullable(productBaseInfo)
                .map(ProductBaseInfo::getBasic)
                .map(DealGroupBasicDTO::getTitle)
                .orElse(null);
        // 按CountrySubsidiesPrice升序排序，如果为空，则排在后面，如果CountrySubsidiesPrice相同，则按skuid升序排序
        List<MultiSkuInfo> skuInfos = validDealGroupDeals.stream()
                .map(deal -> convertToSku(productTitle, deal, skuIdPriceMap))
                .filter(Objects::nonNull)
                .sorted(Comparator.comparing(MultiSkuInfo::getCountrySubsidiesPrice, Comparator.nullsLast(BigDecimal::compareTo))
                        .thenComparing(MultiSkuInfo::getFinalPrice)).collect(Collectors.toList());

        List<MultiSkuSelectItemVO> skuSelectItemVOS = skuInfos.stream()
                .map(MultiSkuInfo::getItem)
                .collect(Collectors.toList());
        // 当传入skuId为空时，选择国补价格最低的sku
        if (request.getSkuId() <= 0L) {
            skuSelectItemVOS.get(0).setSelected(true);
        }
        return skuSelectItemVOS;
    }

    private MultiSkuInfo convertToSku(String dealGroupTitle, DealGroupDealDTO deal, Map<Long, PriceDisplayDTO> skuIdPriceMap) {
        if (Objects.isNull(deal) || Objects.isNull(deal.getBasic())) {
            return null;
        }
        Long skuId = deal.getDealId();
        Map<String, AttrDTO> skuSaleAttrMap = skuSaleAttr.getSkuAttrs(skuId);
        List<AttrDTO> saleAttrDTOS = MapUtils.isEmpty(skuSaleAttrMap) ? null : new ArrayList<>(skuSaleAttrMap.values());
        MultiSkuSelectItemVO item = new MultiSkuSelectItemVO();
        item.setProductId(request.getProductId());
        item.setSkuId(skuId);
        String skuTitle = getSkuTitle(dealGroupTitle, saleAttrDTOS);
        item.setTitle(skuTitle);
        item.setSelected(Objects.equals(skuId, request.getSkuId()));
        MultiSkuSelectRichTextVO richTextVO = new MultiSkuSelectRichTextVO();
        richTextVO.setSymbol("￥");
        PriceDisplayDTO priceDisplayDTO = skuIdPriceMap.get(skuId);
        richTextVO.setSalePrice(priceDisplayDTO.getPrice().toPlainString());
        richTextVO.setMarketPrice(priceDisplayDTO.getMarketPrice().toPlainString());
        item.setMultiSkuSelectRichTextVO(richTextVO);
        MultiSkuInfo skuInfo = new MultiSkuInfo();
        skuInfo.setItem(item);
        skuInfo.setSkuId(skuId);
        skuInfo.setCountrySubsidiesPrice(priceDisplayDTO.getStateSubsidiesPrice());
        skuInfo.setFinalPrice(priceDisplayDTO.getPrice());
        return skuInfo;
    }

    private String getSkuTitle(String title, List<AttrDTO> skuAttrs) {
        if (CollectionUtils.isEmpty(skuAttrs)) {
            return title;
        }
        return skuAttrs.stream()
                .map(AttrDTO::getValue)
                .filter(Objects::nonNull)
                .map(val -> val.get(0))
                .collect(Collectors.joining("|"));
    }

    @Data
    private class MultiSkuInfo {
        private MultiSkuSelectItemVO item;
        private BigDecimal countrySubsidiesPrice;
        private Long skuId;
        private BigDecimal finalPrice;
    }
}
