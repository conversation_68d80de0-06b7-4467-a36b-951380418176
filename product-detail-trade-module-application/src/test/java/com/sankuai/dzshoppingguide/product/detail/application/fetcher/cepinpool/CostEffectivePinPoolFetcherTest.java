package com.sankuai.dzshoppingguide.product.detail.application.fetcher.cepinpool;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.cepinpool.dto.CostEffectivePinTuan;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.dto.ProductPriceReturnValue;
import java.util.ArrayList;
import java.util.concurrent.CompletableFuture;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.modules.junit4.PowerMockRunnerDelegate;

@ExtendWith(MockitoExtension.class)
@PrepareForTest(CostEffectivePinPoolFetcher.class)
@PowerMockRunnerDelegate(PowerMockRunner.class)
class CostEffectivePinPoolFetcherTest {

    @Spy
    @InjectMocks
    private CostEffectivePinPoolFetcher fetcher;

    @Mock
    private ProductPriceReturnValue costEffectivePrice;

    @Mock
    private PriceDisplayDTO priceDisplayDTO;

    /**
     * Test case when price return value is null
     */
    @Test
    public void testDoFetch_WhenPriceIsNull_ReturnsNull() throws Throwable {
        // arrange
        CostEffectivePinPoolFetcher fetcher = PowerMockito.spy(new CostEffectivePinPoolFetcher());
        PowerMockito.doReturn(null).when(fetcher, "getDependencyResult", any());
        // act
        CompletableFuture<CostEffectivePinTuan> result = fetcher.doFetch();
        // assert
        assertNotNull(result);
        assertNull(result.get());
    }

    /**
     * Test case when price display DTO is null
     */
    @Test
    public void testDoFetch_WhenPriceDisplayDTOIsNull_ReturnsNull() throws Throwable {
        // arrange
        CostEffectivePinPoolFetcher fetcher = PowerMockito.spy(new CostEffectivePinPoolFetcher());
        ProductPriceReturnValue returnValue = mock(ProductPriceReturnValue.class);
        when(returnValue.getPriceDisplayDTO()).thenReturn(null);
        PowerMockito.doReturn(returnValue).when(fetcher, "getDependencyResult", any());
        // act
        CompletableFuture<CostEffectivePinTuan> result = fetcher.doFetch();
        // assert
        assertNotNull(result);
        assertNull(result.get());
    }

    /**
     * Test case when used promos is empty
     */
    @Test
    public void testDoFetch_WhenUsedPromosEmpty_ReturnsNull() throws Throwable {
        // arrange
        CostEffectivePinPoolFetcher fetcher = PowerMockito.spy(new CostEffectivePinPoolFetcher());
        ProductPriceReturnValue returnValue = mock(ProductPriceReturnValue.class);
        PriceDisplayDTO priceDisplayDTO = mock(PriceDisplayDTO.class);
        when(returnValue.getPriceDisplayDTO()).thenReturn(priceDisplayDTO);
        when(priceDisplayDTO.getUsedPromos()).thenReturn(new ArrayList<>());
        PowerMockito.doReturn(returnValue).when(fetcher, "getDependencyResult", any());
        // act
        CompletableFuture<CostEffectivePinTuan> result = fetcher.doFetch();
        // assert
        assertNotNull(result);
        assertNull(result.get());
    }
}
