package com.sankuai.dzshoppingguide.product.detail.application.fetcher.atmosphere;

import com.dianping.gmkt.scene.api.delivery.dto.res.ResourceExposureResponseDTO;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025-03-10
 * @desc 商品氛围返回值
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProductAtmosphereReturnValue extends FetcherReturnValueDTO {

    private ResourceExposureResponseDTO resourceExposureResponse;
}
