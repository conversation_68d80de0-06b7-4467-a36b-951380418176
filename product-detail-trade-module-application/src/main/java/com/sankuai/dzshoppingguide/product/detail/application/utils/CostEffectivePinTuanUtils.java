package com.sankuai.dzshoppingguide.product.detail.application.utils;

import com.sankuai.dzshoppingguide.product.detail.application.fetcher.cepinpool.dto.CostEffectivePinTuan;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

/**
 * @Author: zheng<PERSON><EMAIL>
 * @Date: 2024/4/23
 */
@Slf4j
public class CostEffectivePinTuanUtils {

    /**
     * 1. 是否展示优惠条信息
     * 2. 不同底bar
     */
    public static boolean isCePinTuaScene(CostEffectivePinTuan costEffectivePinTuan) {
        if (costEffectivePinTuan != null){
            return costEffectivePinTuan.isCePinTuanScene();
        }
        return false;
    }


    /**
     * 是否为强化样式 前提是有拼团优惠
     * @param costEffectivePinTuan
     * @return
     */
    public static boolean enhancedStyle(CostEffectivePinTuan costEffectivePinTuan) {
        return Objects.nonNull(costEffectivePinTuan)
                && Objects.nonNull(costEffectivePinTuan.getSceneType())
                && costEffectivePinTuan.getSceneType() == 56;
    }

    /**
     * 是否主态
     * @return
     */
    public static boolean activePinTuan(CostEffectivePinTuan costEffectivePinTuan) {
        return pinTuanOpened(costEffectivePinTuan)
                && costEffectivePinTuan.isActivePinTuan();
    }

    /**
     * 是否已开团，是则展示拼团样式，否则不展示
     * @return
     */
    public static boolean pinTuanOpened(CostEffectivePinTuan costEffectivePinTuan) {
        return costEffectivePinTuan.isCePinTuanScene()
                && costEffectivePinTuan.isPinTuanOpened();
    }

}
