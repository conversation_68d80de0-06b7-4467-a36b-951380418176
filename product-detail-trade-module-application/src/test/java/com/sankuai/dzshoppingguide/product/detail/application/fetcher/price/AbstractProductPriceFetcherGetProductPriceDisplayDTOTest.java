package com.sankuai.dzshoppingguide.product.detail.application.fetcher.price;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.sankuai.dealuser.price.display.api.model.*;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.dto.LocalPriceDisplayDTO;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.dto.ProductPriceReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;
import org.springframework.test.util.ReflectionTestUtils;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.best.shop.ShopInfo;
import com.sankuai.sinai.data.api.dto.DpPoiBackCategoryDTO;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.sinai.data.api.dto.MtPoiDTO;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.Set;

class AbstractProductPriceFetcherGetProductPriceDisplayDTOTest {

    @Mock
    private CompositeAtomService compositeAtomService;

    @InjectMocks
    private AbstractProductPriceFetcher<ProductPriceReturnValue> fetcher = new AbstractProductPriceFetcher<ProductPriceReturnValue>() {

        @Override
        protected void postProcessPriceRequest(ClientEnv clientEnv, List<ProductIdentity> productIdentities, Map<String, String> extension) {
        }

        @Override
        protected CompletableFuture<ProductPriceReturnValue> doFetch() {
            return null;
        }
    };

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        ProductDetailPageRequest request = new ProductDetailPageRequest();
        request.setPoiId(123L);
        request.setProductId(456L);
        ReflectionTestUtils.setField(fetcher, "request", request);
    }

    /**
     * Test successful case with valid response data
     */
    @Test
    public void testGetProductPriceDisplayDTO_Success() throws Throwable {
        // arrange
        BatchPriceRequest priceRequest = new BatchPriceRequest();
        PriceDisplayDTO priceDisplayDTO = new PriceDisplayDTO();
        ProductIdentity identity = new ProductIdentity();
        identity.setProductId(456);
        priceDisplayDTO.setIdentity(identity);
        Map<Long, List<PriceDisplayDTO>> dataMap = new HashMap<>();
        List<PriceDisplayDTO> priceList = new ArrayList<>();
        priceList.add(priceDisplayDTO);
        dataMap.put(123L, priceList);
        PriceResponse<Map<Long, List<PriceDisplayDTO>>> response = mock(PriceResponse.class);
        when(response.isSuccess()).thenReturn(true);
        when(response.getData()).thenReturn(dataMap);
        when(response.getPriceSecretInfo()).thenReturn("secretInfo");
        when(compositeAtomService.batchQueryPriceWithResponse(any())).thenReturn(CompletableFuture.completedFuture(response));
        // act
        CompletableFuture<LocalPriceDisplayDTO> future = fetcher.getProductPriceDisplayDTO(priceRequest);
        LocalPriceDisplayDTO result = future.get();
        // assert
        assertNotNull(result);
        assertEquals(priceDisplayDTO, result.getPriceDisplayDTO());
        assertEquals("secretInfo", result.getPriceSecretInfo());
    }

    /**
     * Test case when response is null
     */
    @Test
    public void testGetProductPriceDisplayDTO_NullResponse() throws Throwable {
        // arrange
        BatchPriceRequest priceRequest = new BatchPriceRequest();
        when(compositeAtomService.batchQueryPriceWithResponse(any())).thenReturn(CompletableFuture.completedFuture(null));
        // act
        CompletableFuture<LocalPriceDisplayDTO> future = fetcher.getProductPriceDisplayDTO(priceRequest);
        LocalPriceDisplayDTO result = future.get();
        // assert
        assertNull(result);
    }

    /**
     * Test case when response is not success
     */
    @Test
    public void testGetProductPriceDisplayDTO_NotSuccess() throws Throwable {
        // arrange
        BatchPriceRequest priceRequest = new BatchPriceRequest();
        PriceResponse<Map<Long, List<PriceDisplayDTO>>> response = mock(PriceResponse.class);
        when(response.isSuccess()).thenReturn(false);
        when(compositeAtomService.batchQueryPriceWithResponse(any())).thenReturn(CompletableFuture.completedFuture(response));
        // act
        CompletableFuture<LocalPriceDisplayDTO> future = fetcher.getProductPriceDisplayDTO(priceRequest);
        LocalPriceDisplayDTO result = future.get();
        // assert
        assertNull(result);
    }

    /**
     * Test case when data map is empty
     */
    @Test
    public void testGetProductPriceDisplayDTO_EmptyDataMap() throws Throwable {
        // arrange
        BatchPriceRequest priceRequest = new BatchPriceRequest();
        PriceResponse<Map<Long, List<PriceDisplayDTO>>> response = mock(PriceResponse.class);
        when(response.isSuccess()).thenReturn(true);
        when(response.getData()).thenReturn(new HashMap<>());
        when(compositeAtomService.batchQueryPriceWithResponse(any())).thenReturn(CompletableFuture.completedFuture(response));
        // act
        CompletableFuture<LocalPriceDisplayDTO> future = fetcher.getProductPriceDisplayDTO(priceRequest);
        LocalPriceDisplayDTO result = future.get();
        // assert
        assertNull(result);
    }

    /**
     * Test case when no matching product found in price list
     */
    @Test
    public void testGetProductPriceDisplayDTO_NoMatchingProduct() throws Throwable {
        // arrange
        BatchPriceRequest priceRequest = new BatchPriceRequest();
        PriceDisplayDTO priceDisplayDTO = new PriceDisplayDTO();
        ProductIdentity identity = new ProductIdentity();
        // Different product ID
        identity.setProductId(789);
        priceDisplayDTO.setIdentity(identity);
        Map<Long, List<PriceDisplayDTO>> dataMap = new HashMap<>();
        List<PriceDisplayDTO> priceList = new ArrayList<>();
        priceList.add(priceDisplayDTO);
        dataMap.put(123L, priceList);
        PriceResponse<Map<Long, List<PriceDisplayDTO>>> response = mock(PriceResponse.class);
        when(response.isSuccess()).thenReturn(true);
        when(response.getData()).thenReturn(dataMap);
        when(compositeAtomService.batchQueryPriceWithResponse(any())).thenReturn(CompletableFuture.completedFuture(response));
        // act
        CompletableFuture<LocalPriceDisplayDTO> future = fetcher.getProductPriceDisplayDTO(priceRequest);
        LocalPriceDisplayDTO result = future.get();
        // assert
        assertNull(result);
    }

    /**
     * Test case when poi ID not found in data map
     */
    @Test
    public void testGetProductPriceDisplayDTO_PoiIdNotFound() throws Throwable {
        // arrange
        BatchPriceRequest priceRequest = new BatchPriceRequest();
        Map<Long, List<PriceDisplayDTO>> dataMap = new HashMap<>();
        // Different poi ID
        dataMap.put(456L, new ArrayList<>());
        PriceResponse<Map<Long, List<PriceDisplayDTO>>> response = mock(PriceResponse.class);
        when(response.isSuccess()).thenReturn(true);
        when(response.getData()).thenReturn(dataMap);
        when(compositeAtomService.batchQueryPriceWithResponse(any())).thenReturn(CompletableFuture.completedFuture(response));
        // act
        CompletableFuture<LocalPriceDisplayDTO> future = fetcher.getProductPriceDisplayDTO(priceRequest);
        LocalPriceDisplayDTO result = future.get();
        // assert
        assertNull(result);
    }

    @Test
    public void testGetPoiCategoryIdsWhenShopInfoIsNull() throws Throwable {
        // arrange
        AbstractProductPriceFetcher<?> fetcher = new TestProductPriceFetcher();
        // act
        Set<Integer> result = fetcher.getPoiCategoryIds(ClientTypeEnum.MT_APP, null);
        // assert
        assertNotNull(result, "Returned set should not be null when shopInfo is null");
        assertTrue(result.isEmpty(), "Returned set should be empty when shopInfo is null");
    }

    @Test
    public void testGetPoiCategoryIdsWhenShopInfoIsMockAndNull() throws Throwable {
        // arrange
        AbstractProductPriceFetcher<?> fetcher = new TestProductPriceFetcher();
        ShopInfo shopInfo = null;
        // Mockito is used here to demonstrate the required mocking framework, even though shopInfo is null.
        // act
        Set<Integer> result = fetcher.getPoiCategoryIds(ClientTypeEnum.DP_APP, shopInfo);
        // assert
        assertNotNull(result, "Returned set should not be null when shopInfo is null (mock context)");
        assertTrue(result.isEmpty(), "Returned set should be empty when shopInfo is null (mock context)");
    }

    @Test
    public void testGetPoiCategoryIdsWithMtClientTypeAndNonEmptyCategoryList() throws Throwable {
        // arrange
        AbstractProductPriceFetcher<?> fetcher = new TestProductPriceFetcher();
        ShopInfo shopInfo = mock(ShopInfo.class);
        MtPoiDTO mtPoiDTO = mock(MtPoiDTO.class);
        DpPoiBackCategoryDTO cat1 = mock(DpPoiBackCategoryDTO.class);
        DpPoiBackCategoryDTO cat2 = mock(DpPoiBackCategoryDTO.class);
        when(cat1.getCategoryId()).thenReturn(101);
        when(cat2.getCategoryId()).thenReturn(202);
        when(shopInfo.getMtPoiDTO()).thenReturn(mtPoiDTO);
        when(mtPoiDTO.getDpBackCategoryList()).thenReturn(Arrays.asList(cat1, cat2));
        // act
        Set<Integer> result = fetcher.getPoiCategoryIds(ClientTypeEnum.MT_APP, shopInfo);
        // assert
        assertNotNull(result);
        assertEquals(new HashSet<>(Arrays.asList(101, 202)), result);
    }

    @Test
    public void testGetPoiCategoryIdsWithDpClientTypeAndEmptyCategoryList() throws Throwable {
        // arrange
        AbstractProductPriceFetcher<?> fetcher = new TestProductPriceFetcher();
        ShopInfo shopInfo = mock(ShopInfo.class);
        DpPoiDTO dpPoiDTO = mock(DpPoiDTO.class);
        when(shopInfo.getDpPoiDTO()).thenReturn(dpPoiDTO);
        when(dpPoiDTO.getBackMainCategoryPath()).thenReturn(Collections.emptyList());
        // act
        Set<Integer> result = fetcher.getPoiCategoryIds(ClientTypeEnum.DP_APP, shopInfo);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testGetPoiCategoryIdsWithDpClientTypeAndNonEmptyCategoryList() throws Throwable {
        // arrange
        AbstractProductPriceFetcher<?> fetcher = new TestProductPriceFetcher();
        ShopInfo shopInfo = mock(ShopInfo.class);
        DpPoiDTO dpPoiDTO = mock(DpPoiDTO.class);
        DpPoiBackCategoryDTO cat1 = mock(DpPoiBackCategoryDTO.class);
        DpPoiBackCategoryDTO cat2 = mock(DpPoiBackCategoryDTO.class);
        when(cat1.getCategoryId()).thenReturn(303);
        when(cat2.getCategoryId()).thenReturn(404);
        when(shopInfo.getDpPoiDTO()).thenReturn(dpPoiDTO);
        when(dpPoiDTO.getBackMainCategoryPath()).thenReturn(Arrays.asList(cat1, cat2));
        // act
        Set<Integer> result = fetcher.getPoiCategoryIds(ClientTypeEnum.DP_APP, shopInfo);
        // assert
        assertNotNull(result);
        assertEquals(new HashSet<>(Arrays.asList(303, 404)), result);
    }

    static class TestProductPriceFetcher extends AbstractProductPriceFetcher<ProductPriceReturnValue> {

        @Override
        protected java.util.concurrent.CompletableFuture<ProductPriceReturnValue> doFetch() {
            return null;
        }

        @Override
        protected void postProcessPriceRequest(com.sankuai.dealuser.price.display.api.model.ClientEnv clientEnv, java.util.List<com.sankuai.dealuser.price.display.api.model.ProductIdentity> productIdentities, java.util.Map<String, String> extension) {
        }
    }
}
