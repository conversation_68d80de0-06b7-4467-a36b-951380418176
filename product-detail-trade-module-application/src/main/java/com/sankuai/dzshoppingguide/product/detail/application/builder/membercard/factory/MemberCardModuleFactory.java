package com.sankuai.dzshoppingguide.product.detail.application.builder.membercard.factory;

import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.BuilderTypeEnum;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.extend.BaseBuilderFactory;
import com.sankuai.dz.product.detail.gateway.spi.enums.ProductTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.membercard.basebuilder.BaseAbstractMemberCardBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.membercard.impl.DealMemberDiscountCardBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.membercard.impl.PremiumMemberCardBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.membercard.impl.ReserveMemberCardBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.membercard.card.entrance.MemberEntranceResult;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.membercard.card.entrance.ShopMemberEntranceFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.FetcherDAGStarter;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.membercard.vo.BasicCardModuleVO;

import java.util.Objects;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/3/16 14:00
 */
@Builder(
        builderType = BuilderTypeEnum.BUILDER_FACTORY,
        moduleKey = ModuleKeyConstants.MEMBER_CARD,
        startFetcher = FetcherDAGStarter.class,
        dependentFetchers = {
            ShopMemberEntranceFetcher.class
        }
)
public class MemberCardModuleFactory extends BaseBuilderFactory<BasicCardModuleVO> {

    @Override
    protected Class<? extends BaseAbstractMemberCardBuilder> selectVariableBuilder() {
        MemberEntranceResult shopHasMemberEntrance = getDependencyResult(ShopMemberEntranceFetcher.class);
        if (shopHasMemberEntrance != null && shopHasMemberEntrance.isHasEntrance()) {
            return PremiumMemberCardBuilder.class;
        }

        if (Objects.equals(ProductTypeEnum.RESERVE, request.getProductTypeEnum())) {
            return ReserveMemberCardBuilder.class;
        }

        return DealMemberDiscountCardBuilder.class;

    }
}
