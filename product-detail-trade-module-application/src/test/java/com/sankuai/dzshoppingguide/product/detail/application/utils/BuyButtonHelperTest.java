package com.sankuai.dzshoppingguide.product.detail.application.utils;

import static org.junit.jupiter.api.Assertions.*;
import com.dianping.tpfun.product.api.sku.pintuan.dto.PinProductBrief;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupChannelDTO;
import com.sankuai.merchantcard.timescard.exposure.dto.CardSummaryBarDTO;
import java.math.BigDecimal;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class BuyButtonHelperTest {

    /**
     * 测试 isDaoGua 方法，当 price 和 normalPrice 都为null 时，应返回false
     */
    @Test
    public void testIsDaoGuaBothNull() {
        // arrange
        BigDecimal price = null;
        PriceDisplayDTO normalPrice = null;
        // act
        boolean result = BuyButtonHelper.isDaoGua(price, normalPrice);
        // assert
        assertFalse(result);
    }

    /**
     * 测试 isDaoGua 方法，当 price 为null，normalPrice 不为null 时，应返回false
     */
    @Test
    public void testIsDaoGuaPriceNull() {
        // arrange
        BigDecimal price = null;
        PriceDisplayDTO normalPrice = new PriceDisplayDTO();
        normalPrice.setPrice(new BigDecimal("100"));
        // act
        boolean result = BuyButtonHelper.isDaoGua(price, normalPrice);
        // assert
        assertFalse(result);
    }

    /**
     * 测试 isDaoGua 方法，当 price 不为null，normalPrice 为null 时，应返回false
     */
    @Test
    public void testIsDaoGuaNormalPriceNull() {
        // arrange
        BigDecimal price = new BigDecimal("100");
        PriceDisplayDTO normalPrice = null;
        // act
        boolean result = BuyButtonHelper.isDaoGua(price, normalPrice);
        // assert
        assertFalse(result);
    }

    /**
     * 测试 isDaoGua 方法，当 price 和 normalPrice 都不为null，但 price 小于 normalPrice 时，应返回false
     */
    @Test
    public void testIsDaoGuaPriceLessThanNormalPrice() {
        // arrange
        BigDecimal price = new BigDecimal("50");
        PriceDisplayDTO normalPrice = new PriceDisplayDTO();
        normalPrice.setPrice(new BigDecimal("100"));
        // act
        boolean result = BuyButtonHelper.isDaoGua(price, normalPrice);
        // assert
        assertFalse(result);
    }

    /**
     * 测试 isDaoGua 方法，当 price 和 normalPrice 都不为null，且 price 大于等于 normalPrice 时，应返回true
     */
    @Test
    public void testIsDaoGuaPriceGreaterThanOrEqualToNormalPrice() {
        // arrange
        BigDecimal price = new BigDecimal("100");
        PriceDisplayDTO normalPrice = new PriceDisplayDTO();
        normalPrice.setPrice(new BigDecimal("100"));
        // act
        boolean result = BuyButtonHelper.isDaoGua(price, normalPrice);
        // assert
        assertTrue(result);
    }

    /**
     * Test case for null times card
     */
    @Test
    public void testIsValidTimesCard_NullTimesCard() throws Throwable {
        // arrange
        int dealGroupId = 123;
        DealGroupCategoryDTO categoryDTO = new DealGroupCategoryDTO();
        PriceDisplayDTO normalPrice = new PriceDisplayDTO();
        // act
        boolean result = BuyButtonHelper.isValidTimesCard(dealGroupId, null, categoryDTO, normalPrice);
        // assert
        assertFalse(result);
    }

    /**
     * Test case for null price in times card
     */
    @Test
    public void testIsValidTimesCard_NullPrice() throws Throwable {
        // arrange
        int dealGroupId = 123;
        CardSummaryBarDTO timesCard = new CardSummaryBarDTO();
        timesCard.setTimes(1);
        timesCard.setPrice(null);
        DealGroupCategoryDTO categoryDTO = new DealGroupCategoryDTO();
        PriceDisplayDTO normalPrice = new PriceDisplayDTO();
        // act
        boolean result = BuyButtonHelper.isValidTimesCard(dealGroupId, timesCard, categoryDTO, normalPrice);
        // assert
        assertFalse(result);
    }

    /**
     * Test case for invalid times (<=0)
     */
    @Test
    public void testIsValidTimesCard_InvalidTimes() throws Throwable {
        // arrange
        int dealGroupId = 123;
        CardSummaryBarDTO timesCard = new CardSummaryBarDTO();
        timesCard.setTimes(0);
        timesCard.setPrice(BigDecimal.TEN);
        DealGroupCategoryDTO categoryDTO = new DealGroupCategoryDTO();
        PriceDisplayDTO normalPrice = new PriceDisplayDTO();
        // act
        boolean result = BuyButtonHelper.isValidTimesCard(dealGroupId, timesCard, categoryDTO, normalPrice);
        // assert
        assertFalse(result);
    }

    /**
     * Test case for dao gua price scenario
     */
    @Test
    public void testIsValidTimesCard_DaoGuaPrice() throws Throwable {
        // arrange
        int dealGroupId = 123;
        CardSummaryBarDTO timesCard = new CardSummaryBarDTO();
        timesCard.setTimes(1);
        timesCard.setPrice(BigDecimal.valueOf(100));
        DealGroupCategoryDTO categoryDTO = new DealGroupCategoryDTO();
        PriceDisplayDTO normalPrice = new PriceDisplayDTO();
        normalPrice.setPrice(BigDecimal.valueOf(50));
        // act
        boolean result = BuyButtonHelper.isValidTimesCard(dealGroupId, timesCard, categoryDTO, normalPrice);
        // assert
        assertFalse(result);
    }

    /**
     * Test when pinProductBrief is null
     */
    @Test
    public void testIsValidPinPool_WhenPinProductBriefIsNull() throws Throwable {
        // arrange
        long dealGroupId = 123L;
        PriceDisplayDTO normalPrice = new PriceDisplayDTO();
        BigDecimal pinPoolPromoAmount = BigDecimal.TEN;
        // act
        boolean result = BuyButtonHelper.isValidPinPool(dealGroupId, null, pinPoolPromoAmount, normalPrice);
        // assert
        assertFalse(result);
    }

    /**
     * Test when product id is invalid (<=0)
     */
    @Test
    public void testIsValidPinPool_WhenProductIdIsInvalid() throws Throwable {
        // arrange
        long dealGroupId = 123L;
        PinProductBrief pinProductBrief = new PinProductBrief();
        pinProductBrief.setProductId(0);
        PriceDisplayDTO normalPrice = new PriceDisplayDTO();
        BigDecimal pinPoolPromoAmount = BigDecimal.TEN;
        // act
        boolean result = BuyButtonHelper.isValidPinPool(dealGroupId, pinProductBrief, pinPoolPromoAmount, normalPrice);
        // assert
        assertFalse(result);
    }

    /**
     * Test when product is sold out
     */
    @Test
    public void testIsValidPinPool_WhenProductIsSoldOut() throws Throwable {
        // arrange
        long dealGroupId = 123L;
        PinProductBrief pinProductBrief = new PinProductBrief();
        pinProductBrief.setProductId(1);
        pinProductBrief.setSoldOut(true);
        PriceDisplayDTO normalPrice = new PriceDisplayDTO();
        BigDecimal pinPoolPromoAmount = BigDecimal.TEN;
        // act
        boolean result = BuyButtonHelper.isValidPinPool(dealGroupId, pinProductBrief, pinPoolPromoAmount, normalPrice);
        // assert
        assertFalse(result);
    }

    /**
     * Test when price is null
     */
    @Test
    public void testIsValidPinPool_WhenPriceIsNull() throws Throwable {
        // arrange
        long dealGroupId = 123L;
        PinProductBrief pinProductBrief = new PinProductBrief();
        pinProductBrief.setProductId(1);
        pinProductBrief.setSoldOut(false);
        pinProductBrief.setPrice(null);
        PriceDisplayDTO normalPrice = new PriceDisplayDTO();
        BigDecimal pinPoolPromoAmount = BigDecimal.TEN;
        // act
        boolean result = BuyButtonHelper.isValidPinPool(dealGroupId, pinProductBrief, pinPoolPromoAmount, normalPrice);
        // assert
        assertFalse(result);
    }

    /**
     * Test when price is zero
     */
    @Test
    public void testIsValidPinPool_WhenPriceIsZero() throws Throwable {
        // arrange
        long dealGroupId = 123L;
        PinProductBrief pinProductBrief = new PinProductBrief();
        pinProductBrief.setProductId(1);
        pinProductBrief.setSoldOut(false);
        pinProductBrief.setPrice(BigDecimal.ZERO);
        PriceDisplayDTO normalPrice = new PriceDisplayDTO();
        BigDecimal pinPoolPromoAmount = BigDecimal.TEN;
        // act
        boolean result = BuyButtonHelper.isValidPinPool(dealGroupId, pinProductBrief, pinPoolPromoAmount, normalPrice);
        // assert
        assertFalse(result);
    }

    /**
     * Test when URL is empty
     */
    @Test
    public void testIsValidPinPool_WhenUrlIsEmpty() throws Throwable {
        // arrange
        long dealGroupId = 123L;
        PinProductBrief pinProductBrief = new PinProductBrief();
        pinProductBrief.setProductId(1);
        pinProductBrief.setSoldOut(false);
        pinProductBrief.setPrice(BigDecimal.TEN);
        pinProductBrief.setUrl("");
        PriceDisplayDTO normalPrice = new PriceDisplayDTO();
        BigDecimal pinPoolPromoAmount = BigDecimal.ONE;
        // act
        boolean result = BuyButtonHelper.isValidPinPool(dealGroupId, pinProductBrief, pinPoolPromoAmount, normalPrice);
        // assert
        assertFalse(result);
    }

    /**
     * Test successful case without promo amount
     */
    @Test
    public void testIsValidPinPool_SuccessWithoutPromoAmount() throws Throwable {
        // arrange
        long dealGroupId = 123L;
        PinProductBrief pinProductBrief = new PinProductBrief();
        pinProductBrief.setProductId(1);
        pinProductBrief.setSoldOut(false);
        pinProductBrief.setPrice(new BigDecimal("100"));
        pinProductBrief.setUrl("http://test.com");
        PriceDisplayDTO normalPrice = new PriceDisplayDTO();
        normalPrice.setPrice(new BigDecimal("200"));
        // act
        boolean result = BuyButtonHelper.isValidPinPool(dealGroupId, pinProductBrief, null, normalPrice);
        // assert
        assertTrue(result);
    }

    /**
     * Test successful case with promo amount
     */
    @Test
    public void testIsValidPinPool_SuccessWithPromoAmount() throws Throwable {
        // arrange
        long dealGroupId = 123L;
        PinProductBrief pinProductBrief = new PinProductBrief();
        pinProductBrief.setProductId(1);
        pinProductBrief.setSoldOut(false);
        pinProductBrief.setPrice(new BigDecimal("100"));
        pinProductBrief.setUrl("http://test.com");
        PriceDisplayDTO normalPrice = new PriceDisplayDTO();
        normalPrice.setPrice(new BigDecimal("90"));
        BigDecimal pinPoolPromoAmount = new BigDecimal("20");
        // act
        boolean result = BuyButtonHelper.isValidPinPool(dealGroupId, pinProductBrief, pinPoolPromoAmount, normalPrice);
        // assert
        assertTrue(result);
    }

    /**
     * Test price inversion case (isDaoGua)
     */
    @Test
    public void testIsValidPinPool_WhenPriceInversion() throws Throwable {
        // arrange
        long dealGroupId = 123L;
        PinProductBrief pinProductBrief = new PinProductBrief();
        pinProductBrief.setProductId(1);
        pinProductBrief.setSoldOut(false);
        pinProductBrief.setPrice(new BigDecimal("100"));
        pinProductBrief.setUrl("http://test.com");
        PriceDisplayDTO normalPrice = new PriceDisplayDTO();
        normalPrice.setPrice(new BigDecimal("80"));
        BigDecimal pinPoolPromoAmount = new BigDecimal("10");
        // act
        boolean result = BuyButtonHelper.isValidPinPool(dealGroupId, pinProductBrief, pinPoolPromoAmount, normalPrice);
        // assert
        assertFalse(result);
    }
}
