package com.sankuai.dzshoppingguide.product.detail.application.utils;

import com.google.common.collect.Sets;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import org.apache.commons.lang3.StringUtils;

import java.util.Locale;
import java.util.Set;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/2/27 20:36
 */
public class ClientTypeUtils {
    private static final Set<ClientTypeEnum> miniClientEnums = Sets.newHashSet(ClientTypeEnum.DP_XCX,
            ClientTypeEnum.DP_BAIDU_MAP_XCX, ClientTypeEnum.MT_XCX, ClientTypeEnum.MT_ZJ_XCX,
            ClientTypeEnum.MT_WWJKZ_XCX, ClientTypeEnum.MT_KUAI_SHOU_XCX, ClientTypeEnum.MT_MAO_YAN_XCX);

    private static final Set<ClientTypeEnum> wxMiniProgramEnums = Sets.newHashSet(ClientTypeEnum.MT_WX, ClientTypeEnum.DP_WX);

    private static final Set<ClientTypeEnum> mainApp = Sets.newHashSet(ClientTypeEnum.MT_APP, ClientTypeEnum.DP_APP,
            ClientTypeEnum.MT_WAI_MAI_APP);

    public static boolean isMainApp(ClientTypeEnum clientTypeEnum) {
        return mainApp.contains(clientTypeEnum);
    }

    public static boolean isMiniProgram(ClientTypeEnum clientTypeEnum) {
        return miniClientEnums.contains(clientTypeEnum);
    }

    public static boolean isWxMiniProgram(ClientTypeEnum clientTypeEnum) {
        return wxMiniProgramEnums.contains(clientTypeEnum);
    }

    private static final Set<ClientTypeEnum> mtClientEnums = Sets.newHashSet(ClientTypeEnum.MT_APP, ClientTypeEnum.MT_XCX);
    public static boolean isMtMainOrMiniClient(ClientTypeEnum clientTypeEnum) {
        return mtClientEnums.contains(clientTypeEnum);
    }

    private static final Set<ClientTypeEnum> dpClientEnums = Sets.newHashSet(ClientTypeEnum.DP_APP, ClientTypeEnum.DP_XCX);
    public static boolean isDpMainOrMiniClient(ClientTypeEnum clientTypeEnum) {
        return dpClientEnums.contains(clientTypeEnum);
    }

    public static com.sankuai.mpmctmember.query.common.enums.PlatformEnum genPlatform(ClientTypeEnum clientTypeEnum) {
        boolean miniProgram = ClientTypeUtils.isMiniProgram(clientTypeEnum);
        if (ClientTypeUtils.isMtMainOrMiniClient(clientTypeEnum)) {
            return miniProgram ? com.sankuai.mpmctmember.query.common.enums.PlatformEnum.MT_APPLET
                    : com.sankuai.mpmctmember.query.common.enums.PlatformEnum.MT_APP;
        } else if (ClientTypeUtils.isDpMainOrMiniClient(clientTypeEnum)) {
            return miniProgram ? com.sankuai.mpmctmember.query.common.enums.PlatformEnum.DP_APPLET
                    : com.sankuai.mpmctmember.query.common.enums.PlatformEnum.DP_APP;
        } else {
            return clientTypeEnum.isMtClientType() ? com.sankuai.mpmctmember.query.common.enums.PlatformEnum.MT_APP
                    : com.sankuai.mpmctmember.query.common.enums.PlatformEnum.DP_APP;
        }
    }

    public static boolean isAndroid(String osType) {
        if (StringUtils.isBlank(osType)) {
            return false;
        }
        return osType.equalsIgnoreCase("android");
    }

    public static boolean isIos(String osType) {
        if (StringUtils.isBlank(osType)) {
            return false;
        }
        return osType.equalsIgnoreCase("ios");
    }

    public static boolean isPureHarmony(String userAgent) {
        if (org.apache.commons.lang.StringUtils.isBlank(userAgent)) {
            return false;
        }
        return userAgent.toLowerCase(Locale.ROOT).contains("openharmony");
    }

}
