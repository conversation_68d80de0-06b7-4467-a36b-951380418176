package com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.ext.param.handler;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.mock;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.enums.OrderPageExtParamEnums;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * 测试 PromotionChannelExtParamBuilder 的 buildKey 方法
 */
@ExtendWith(MockitoExtension.class)
class PromotionChannelExtParamBuilderBuildKeyTest {

    /**
     * 测试 buildKey 方法返回正确的 promotionchannel 枚举值
     */
    @Test
    void testBuildKey_ShouldReturnPromotionChannelEnum() {
        // arrange
        PromotionChannelExtParamBuilder builder = new PromotionChannelExtParamBuilder();
        // act
        OrderPageExtParamEnums result = builder.buildKey();
        // assert
        assertEquals(OrderPageExtParamEnums.promotionchannel, result, "buildKey() should return OrderPageExtParamEnums.promotionchannel");
    }

    /**
     * 演示 Mockito 的使用方式（虽然此方法不需要 mock）
     */
    @Test
    void testBuildKey_WithMockitoDemonstration() {
        // arrange - 演示如何创建 mock 对象
        @SuppressWarnings("unused")
        PromotionChannelExtParamBuilder mockBuilder = mock(PromotionChannelExtParamBuilder.class);
        // 实际测试逻辑
        PromotionChannelExtParamBuilder builder = new PromotionChannelExtParamBuilder();
        // act
        OrderPageExtParamEnums result = builder.buildKey();
        // assert
        assertEquals(OrderPageExtParamEnums.promotionchannel, result, "buildKey() should return OrderPageExtParamEnums.promotionchannel");
    }
}
