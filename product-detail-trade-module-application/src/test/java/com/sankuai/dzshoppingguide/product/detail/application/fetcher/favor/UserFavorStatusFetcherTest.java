package com.sankuai.dzshoppingguide.product.detail.application.fetcher.favor;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import com.dianping.userremote.service.collection.FavorService;
import com.google.common.collect.Lists;
import com.sankuai.athena.inf.AthenaInf;
import com.sankuai.athena.inf.InfFinder;
import com.sankuai.athena.inf.rpc.RpcService;
import com.sankuai.coll.idl.CollectionService;
import com.sankuai.dz.product.detail.gateway.spi.dto.ShepherdGatewayParam;
import com.sankuai.dz.product.detail.gateway.spi.enums.PlatformEnum;
import com.sankuai.dz.product.detail.gateway.spi.enums.ProductTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.user.collection.client.CollTypeEnum;
import com.sankuai.user.collection.client.UserCollectionClient;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import org.apache.thrift.TException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

@ExtendWith(MockitoExtension.class)
public class UserFavorStatusFetcherTest {

    @InjectMocks
    private UserFavorStatusFetcher userFavorStatusFetcher;

    @Mock
    private FavorService favorService;

    @Mock
    private CollectionService.Iface iface;

    @Spy
    private UserCollectionClient userCollectionClient;

    @BeforeEach
    public void setup() {
        ReflectionTestUtils.setField(userFavorStatusFetcher, "userCollectionClient", userCollectionClient);
        ReflectionTestUtils.setField(userCollectionClient, "iface", iface);
    }

    @Override
    protected void finalize() throws Throwable {
        // Reset static field after tests
        ReflectionTestUtils.setField(AthenaInf.class, "infFinder", null);
        super.finalize();
    }

    /**
     * Test MT platform with TException
     */
    @Test
    public void testDoFetch_MtPlatform_TException() throws Throwable {
        // arrange
        ProductDetailPageRequest request = new ProductDetailPageRequest();
        request.setProductId(123L);
        request.setProductType(ProductTypeEnum.DEAL.getCode());
        // MT_APP maps to MT platform
        request.setClientType(200);
        ShepherdGatewayParam gatewayParam = new ShepherdGatewayParam();
        gatewayParam.setMtUserId(456L);
        request.setShepherdGatewayParam(gatewayParam);
        ReflectionTestUtils.setField(userFavorStatusFetcher, "request", request);
        when(iface.collectedStatusCheckV2(any())).thenThrow(new TException("Test exception"));
        // act
        CompletableFuture<UserFavorStatusReturnValue> resultFuture = userFavorStatusFetcher.doFetch();
        // assert
        UserFavorStatusReturnValue result = resultFuture.join();
        assertNull(result);
    }
}
