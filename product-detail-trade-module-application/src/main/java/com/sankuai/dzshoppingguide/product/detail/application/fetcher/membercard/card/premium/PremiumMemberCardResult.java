package com.sankuai.dzshoppingguide.product.detail.application.fetcher.membercard.card.premium;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import com.sankuai.dzshoppingguide.product.detail.application.service.dto.ShopMemberDetailV;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/2/27 15:54
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PremiumMemberCardResult extends FetcherReturnValueDTO {
    private ShopMemberDetailV shopMemberDetailV;

}
