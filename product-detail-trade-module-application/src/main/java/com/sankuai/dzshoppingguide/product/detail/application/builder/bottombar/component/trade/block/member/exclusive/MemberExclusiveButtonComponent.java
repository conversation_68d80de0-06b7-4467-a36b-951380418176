package com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.component.trade.block.member.exclusive;

import com.alibaba.fastjson.JSON;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.component.trade.block.buy.button.StandardTradeButtonComponent;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.component.trade.block.utils.PriceFormatUtils;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.component.trade.block.utils.TradeButtonBuildUtils;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.purchase.coupon.PurchaseCouponReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sale.info.enums.ProductSaleStatusEnum;
import com.sankuai.dzshoppingguide.product.detail.application.service.dto.ShopMemberDetailV;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.enums.OpenTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.extend.redirect.SimpleRedirectActionVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.component.trade.button.StandardTradeButtonVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Nullable;

import static com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.constant.ButtonStyleConstant.SHOP_MEMBER_BUTTON_STYLE;

/**
 * @Author: guangyujie
 * @Date: 2025/4/2 22:46
 */
@Slf4j
public class MemberExclusiveButtonComponent {

    public static StandardTradeButtonVO build(final ProductDetailPageRequest request,
                                              @Nullable final PriceDisplayDTO normalPrice,
                                              @Nullable final PurchaseCouponReturnValue purchaseCouponReturnValue,
                                              @Nullable final ShopMemberDetailV shopMemberDetailV) {
        try {
            if (shopMemberDetailV == null
                    || shopMemberDetailV.getOriginalResult() == null
                    || StringUtils.isBlank(shopMemberDetailV.getOriginalResult().getMemberPageUrl())) {
                throw new IllegalArgumentException("命中了会员专属但是无法获取商家会员落地页");
            }
            // 如果是小程序的话,这个地方的逻辑有调整,会员专属的也需要跳转到提单页里面
            if (request.getClientTypeEnum().isInWxXCX()) {
                return null;
            }
            return TradeButtonBuildUtils.buildTradeButtonVO(
                    ProductSaleStatusEnum.ONLINE,
                    PriceFormatUtils.formatPrice("会员价", normalPrice),
                    StandardTradeButtonComponent.buildSubTitle(purchaseCouponReturnValue),
                    SHOP_MEMBER_BUTTON_STYLE,
                    new SimpleRedirectActionVO(
                            OpenTypeEnum.redirect,
                            shopMemberDetailV.getOriginalResult().getMemberPageUrl()
                    )
            );
        } catch (Throwable throwable) {
            log.error("MemberExclusiveButtonComponent.build,request:{}", JSON.toJSONString(request), throwable);
            return null;
        }
    }

}
