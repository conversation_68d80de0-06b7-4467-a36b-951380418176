package com.sankuai.dzshoppingguide.product.detail.spi.membercard.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/2/26 22:24
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TypeDoc(description = "免费商家会员卡")
@MobileDo(id = 0xda37)
public class FreeMemberCardModuleVO extends BasicCardModuleVO {
    @Override
    public String getModuleKey() {
        return ModuleKeyConstants.MEMBER_CARD;
    }
    @FieldDoc(description = "会员卡logo")
    @MobileDo.MobileField(key = 0x9789)
    private String memberCardLogo;

    @FieldDoc(description = "会员卡名称")
    @MobileDo.MobileField(key = 0xe068)
    private String memberCardName;

    @FieldDoc(description = "品牌slogan")
    @MobileDo.MobileField(key = 0xc193)
    private String registerMemberText;

    @FieldDoc(description = "尊享会员")
    @MobileDo.MobileField(key = 0xb56a)
    private String levelName;

    @FieldDoc(description = "是否会员")
    @MobileDo.MobileField(key = 0x3558)
    private boolean member;

    @FieldDoc(description = "是否新会员")
    @MobileDo.MobileField(key = 0xbdf8)
    private boolean isNewMember;

    @FieldDoc(description = "会员数量描述")
    @MobileDo.MobileField(key = 0xab65)
    private String memberCountDesc;

    @FieldDoc(description = "卡数量")
    @MobileDo.MobileField(key = 0xa83e)
    private String cardNumber;

    @FieldDoc(description = "会员名称")
    @MobileDo.MobileField(key = 0x2c49)
    private String memberName;
}
