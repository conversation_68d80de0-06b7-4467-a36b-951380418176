package com.sankuai.dzshoppingguide.product.detail.application.fetcher.price;

import com.sankuai.dealuser.price.display.api.enums.DzPriceSceneEnum;
import com.sankuai.dealuser.price.display.api.enums.ExtensionKeyEnum;
import com.sankuai.dealuser.price.display.api.enums.RequestSceneEnum;
import com.sankuai.dealuser.price.display.api.model.BatchPriceRequest;
import com.sankuai.dealuser.price.display.api.model.ClientEnv;
import com.sankuai.dealuser.price.display.api.model.ProductIdentity;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.product.detail.RequestCustomParamEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.dto.ProductPriceReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.defaultselect.SkuDefaultSelect;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.defaultselect.SkuDefaultSelectFetcher;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @date 2025-03-06
 * @desc 商品特价团购价格查询
 */
@Fetcher(
        previousLayerDependencies = {SkuDefaultSelectFetcher.class}
)
public class ProductCostEffectivePriceFetcher extends AbstractProductPriceFetcher<ProductPriceReturnValue> {

    @Override
    protected CompletableFuture<ProductPriceReturnValue> doFetch() {
        SkuDefaultSelect skuDefaultSelect = getDependencyResult(SkuDefaultSelectFetcher.class);
        BatchPriceRequest priceRequest = buildBatchPriceRequest(request, skuDefaultSelect);
        priceRequest.setScene(RequestSceneEnum.PROMO_DETAIL_DESC_WithDealPromo.getScene());
        return getProductPriceDisplayDTO(priceRequest).thenApply(ProductPriceReturnValue::new);
    }

    @Override
    protected void postProcessPriceRequest(ClientEnv clientEnv, List<ProductIdentity> productIdentityList, Map<String, String> extension) {
        extension.put(ExtensionKeyEnum.DzPriceScene.getDesc(), String.valueOf(DzPriceSceneEnum.COST_EFFECTIVE_PIN_TUAN_DEDUCTION.getCode()));
        // 拼团参数，拼团活动id
        String pintuanActivityId = request.getCustomParam(RequestCustomParamEnum.pintuanActivityId);
        if (StringUtils.isNotBlank(pintuanActivityId)) {
            productIdentityList.forEach(productIdentity -> productIdentity.getExtParams().put(ExtensionKeyEnum.PinTuanActivityId.getDesc(), pintuanActivityId));
        }
        // 拼团参数，拼团id
        String shareToken = request.getCustomParam(RequestCustomParamEnum.orderGroupId);
        if (StringUtils.isNotBlank(shareToken)) {
            productIdentityList.forEach(productIdentity -> productIdentity.getExtParams().put(ExtensionKeyEnum.ShareToken.getDesc(), shareToken));
        }
    }

}
