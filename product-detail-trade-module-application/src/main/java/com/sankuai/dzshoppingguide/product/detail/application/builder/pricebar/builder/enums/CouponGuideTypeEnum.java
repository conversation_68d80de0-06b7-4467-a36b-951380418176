package com.sankuai.dzshoppingguide.product.detail.application.builder.pricebar.builder.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/04/01
 * 神券引导状态枚举
 */
@Getter
public enum CouponGuideTypeEnum {

    // 引导相关状态组
    GUIDE_INFLATE("GUIDE_INFLATE", "引导膨胀", CouponGuideGroup.GUIDE),
    GUIDE_TO_USE_INFLATE("GUIDE_TO_USE_INFLATE", "引导使用", CouponGuideGroup.GUIDE),
    GUIDE_PURCHASE("GUIDE_PURCHASE", "引导购买", CouponGuideGroup.GUIDE),
    NO_GUIDE("NO_GUIDE", "不做引导", CouponGuideGroup.GUIDE),
    // 状态相关组
    AFTER_INFLATE("AFTER_INFLATE", "已膨胀", CouponGuideGroup.STATUS),
    ONE_RETURN("ONE_RETURN", "一单回本", CouponGuideGroup.STATUS),
    BUY_AFTER_CAN_INFLATE("BUY_AFTER_CAN_INFLATE", "购后首单可膨", CouponGuideGroup.STATUS),
    BUY_AFTER_CAN_INFLATE_SUCK_BOTTOM("BUY_AFTER_CAN_INFLATE_SUCK_BOTTOM", "购后首单可膨吸底条", CouponGuideGroup.STATUS),
    BUY_AFTER_NOT_INFLATE("BUY_AFTER_NOT_INFLATE", "购后首单不可膨", CouponGuideGroup.STATUS),
    BUY_AFTER_NOT_INFLATE_ADD_ONE_RETURN("BUY_AFTER_NOT_INFLATE_ADD_ONE_RETURN", "购后首单不可膨一单回本", CouponGuideGroup.STATUS),
    BUY_AFTER_NOT_INFLATE_SUCK_BOTTOM("BUY_AFTER_NOT_INFLATE_SUCK_BOTTOM", "购后首单不可膨吸底条", CouponGuideGroup.STATUS),
    ;

    private final String code;
    private final String desc;
    private final String group;

    CouponGuideTypeEnum(String code, String desc, String group) {
        this.code = code;
        this.desc = desc;
        this.group = group;
    }

    /**
     *  膨前、购前状态
     */
    public static final List<String> beforeInflatePurchaseTypes = Collections.unmodifiableList(Arrays.asList(
            GUIDE_INFLATE.code,
            GUIDE_PURCHASE.code
    ));
    
    
    /**
     * 神券引导分组
     */
    public interface CouponGuideGroup {
        /** 引导相关 */
        String GUIDE = "GUIDE";
        /** 状态相关 */
        String STATUS = "STATUS";
    }



    public static CouponGuideTypeEnum getByCode(String code) {
        if (code == null) {
            return NO_GUIDE;
        }
        return Arrays.stream(values())
                .filter(type -> type.getCode().equals(code))
                .findFirst()
                .orElse(NO_GUIDE);
    }
}