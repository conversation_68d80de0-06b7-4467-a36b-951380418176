package com.sankuai.dzshoppingguide.product.detail.application.builder.pintuan.rule;

import com.dianping.lion.client.Lion;
import com.dianping.lion.common.util.JsonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Maps;
import com.sankuai.dealuser.price.display.api.enums.PromoTypeEnum;
import com.sankuai.dealuser.price.display.api.enums.PromotionExplanatoryTagEnum;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.BaseBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.cepinpool.CostEffectivePinPoolFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.cepinpool.dto.CostEffectivePinTuan;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.ProductCostEffectivePriceFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.dto.ProductPriceReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.FetcherDAGStarter;
import com.sankuai.dzshoppingguide.product.detail.application.model.MaterialInfoDO;
import com.sankuai.dzshoppingguide.product.detail.domain.utils.LionConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.pintuan.ProductDetailFeaturesLayerConfigVO;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.pintuan.ProductDetailGuaranteeItem;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.pintuan.ProductDetailLayerItemConfig;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.pintuan.RuleInfoPO;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.pintuan.vo.ProductDetailPinTuanRuleInfoVO;
import com.sankuai.nibmkt.promotion.api.common.enums.PromotionPropertyEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: <EMAIL>
 * @Date: 2025/3/13
 */
@Builder(
        moduleKey = ModuleKeyConstants.MODULE_DETAIL_DEAL_PINTUAN_RULE,
        startFetcher = FetcherDAGStarter.class,
        dependentFetchers = {
                ProductCostEffectivePriceFetcher.class,
                CostEffectivePinPoolFetcher.class
        }
)
@Slf4j
public class CostEffectivePinTuanRuleModuleBuilder extends BaseBuilder<ProductDetailPinTuanRuleInfoVO> {
    private static final String PIN_TUAN_RULE_PAY = "pinTuanRule_pay";
    private static final String PIN_TUAN_RULE_INVITE = "pinTuanRule_invite";
    private static final String PIN_TUAN_RULE_INVITE_NEW = "pinTuanRule_invite_new";
    private static final String PIN_TUAN_RULE_COMPLETE = "pinTuanRule_complete";
    private static final String TEXT = "text";
    private static final String ICON = "icon";
    public static final String RULE_INFO_KEY = "shareHelpRuleDetail";

    private PriceDisplayDTO priceDisplayDTO;
    private CostEffectivePinTuan costEffectivePinTuan;

    @Override
    public ProductDetailPinTuanRuleInfoVO doBuild() {
        ProductPriceReturnValue costEffectivePrice = getDependencyResult(ProductCostEffectivePriceFetcher.class);
        priceDisplayDTO = costEffectivePrice.getPriceDisplayDTO();
        if (priceDisplayDTO == null
                || CollectionUtils.isEmpty(priceDisplayDTO.getUsedPromos())) {
            return null;
        }
        List<PromoDTO> promoDTOS = priceDisplayDTO.getUsedPromos();
        // 是否有拼团优惠
        PromoDTO promoDTO = promoDTOS.stream().filter(usedPromo -> matchPinTuan(usedPromo)).findFirst().orElse(null);
        if (promoDTO == null) {
            return null;
        }

        // 获取拼团规则
        String materialInfoStr = promoDTOS.stream().filter(pro -> MapUtils.isNotEmpty(pro.getPromotionOtherInfoMap()) && pro.getPromotionOtherInfoMap().containsKey(PromotionPropertyEnum.MATERIAL_INFO_LIST.getValue()))
                .map(promo -> promo.getPromotionOtherInfoMap().get(PromotionPropertyEnum.MATERIAL_INFO_LIST.getValue()))
                .findFirst().orElse(null);
        List<MaterialInfoDO> materialInfoDOList = null;
        if (StringUtils.isNotEmpty(materialInfoStr)) {
            materialInfoDOList = JsonUtils.fromJson(materialInfoStr, new TypeReference<List<MaterialInfoDO>>() {});
        }
        if (Objects.isNull(materialInfoDOList)) {
            return null;
        }

        ProductDetailPinTuanRuleInfoVO pinTuanRuleInfo = new ProductDetailPinTuanRuleInfoVO();
        pinTuanRuleInfo.setTitle("拼团规则");
        pinTuanRuleInfo.setItems(buildPinTuanRuleItems());
        pinTuanRuleInfo.setFeaturesLayer(buildPinTuanRuleFeaturesLayer(getRuleInfoPOS(materialInfoDOList)));
        return pinTuanRuleInfo;
    }

    private List<RuleInfoPO> getRuleInfoPOS(List<MaterialInfoDO> materialInfoDO) {
        return materialInfoDO
                .stream()
                .filter(m -> RULE_INFO_KEY.equals(m.getFieldKey()) && StringUtils.isNotEmpty(m.getFieldName()) && StringUtils.isNotEmpty(m.getFieldValue()))
                .sorted(new Comparator<MaterialInfoDO>() {
                    @Override
                    public int compare(MaterialInfoDO o1, MaterialInfoDO o2) {
                        try {
                            long s1 = NumberUtils.isParsable(o1.getFieldDesc()) ? Long.parseLong(o1.getFieldDesc()) : Long.MAX_VALUE;
                            long s2 = NumberUtils.isParsable(o2.getFieldDesc()) ? Long.parseLong(o2.getFieldDesc()) : Long.MAX_VALUE;
                            if (s1 < s2) {
                                return -1;
                            } else if (s1 == s2) {
                                return 0;
                            }
                            return 1;
                        } catch (Exception e) {
                            log.error("unknown error", e);
                        }
                        return 0;
                    }
                })
                .map(m -> {
                    RuleInfoPO ruleInfo = new RuleInfoPO();
                    ruleInfo.setTitle(m.getFieldName());
                    ruleInfo.setContent(m.getFieldValue());
                    return ruleInfo;
                }).collect(Collectors.toList());

    }

    public ProductDetailFeaturesLayerConfigVO buildPinTuanRuleFeaturesLayer(List<RuleInfoPO> ruleInfoPOS) {
        ProductDetailFeaturesLayerConfigVO featuresLayer = new ProductDetailFeaturesLayerConfigVO();
        featuresLayer.setTitle("查看拼团规则");
        featuresLayer.setLayerConfigs(buildPinTuanLayerConfigs(ruleInfoPOS));
        return featuresLayer;
    }

    public List<ProductDetailLayerItemConfig> buildPinTuanLayerConfigs(List<RuleInfoPO> ruleInfoPOS) {
        final List<ProductDetailLayerItemConfig> layerConfigs = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(ruleInfoPOS)) {
            ruleInfoPOS.forEach(e -> {
                ProductDetailLayerItemConfig layerConfig = new ProductDetailLayerItemConfig();
                layerConfig.setTitle(e.getTitle());
                layerConfig.setDesc(e.getContent());
                layerConfigs.add(layerConfig);
            });
        }
        return layerConfigs;
    }

    public List<ProductDetailGuaranteeItem> buildPinTuanRuleItems() {
        Map<String, Map> pinTuanRuleConfig = Lion.getMap(LionConstants.DZTGDETAIL_APPKEY,
                LionConstants.PIN_TUAN_RULE, Map.class, new HashMap<>());
        List<ProductDetailGuaranteeItem> items = new ArrayList<>();
        Map<String, String> payConfig = pinTuanRuleConfig.getOrDefault(PIN_TUAN_RULE_PAY, Maps.newHashMap());
        Map<String, String> inviteConfig = pinTuanRuleConfig.getOrDefault(PIN_TUAN_RULE_INVITE, Maps.newHashMap());
        if (getLimitNewCustomJoin(priceDisplayDTO.getUsedPromos())) {
            inviteConfig = pinTuanRuleConfig.getOrDefault(PIN_TUAN_RULE_INVITE_NEW, Maps.newHashMap());
        }
        Map<String, String> completeConfig = pinTuanRuleConfig.getOrDefault(PIN_TUAN_RULE_COMPLETE, Maps.newHashMap());
        items.add(buildPinTuanIconText(payConfig.get(TEXT), payConfig.get(ICON)));
        items.add(buildPinTuanIconText(inviteConfig.get(TEXT), inviteConfig.get(ICON)));
        items.add(buildPinTuanIconText(completeConfig.get(TEXT), completeConfig.get(ICON)));
        return items;
    }

    private boolean getLimitNewCustomJoin(List<PromoDTO> promoDTOS) {
        String limitNewCustomJoin = promoDTOS.stream().filter(pro -> MapUtils.isNotEmpty(pro.getPromotionOtherInfoMap()) && pro.getPromotionOtherInfoMap().containsKey(PromotionPropertyEnum.LIMIT_NEW_CUSTOMER_JOIN.getValue()))
                .map(promo -> promo.getPromotionOtherInfoMap().get(PromotionPropertyEnum.LIMIT_NEW_CUSTOMER_JOIN.getValue()))
                .findFirst().orElse(null);
        return "true".equals(limitNewCustomJoin);
    }

    public ProductDetailGuaranteeItem buildPinTuanIconText(String text, String icon) {
        ProductDetailGuaranteeItem item = new ProductDetailGuaranteeItem();
        item.setText(text);
        item.setIcon(icon);
        return item;
    }

    private boolean matchPinTuan(PromoDTO usedPromo) {
        if (CollectionUtils.isEmpty(usedPromo.getPromotionExplanatoryTags())) {
            return false;
        }
        return PromoTypeEnum.NORMAL_PROMO.getType() == usedPromo.getIdentity().getPromoType() && usedPromo.getPromotionExplanatoryTags().contains(PromotionExplanatoryTagEnum.PIN_TUAN_DEDUCTION.getCode());
    }
}
