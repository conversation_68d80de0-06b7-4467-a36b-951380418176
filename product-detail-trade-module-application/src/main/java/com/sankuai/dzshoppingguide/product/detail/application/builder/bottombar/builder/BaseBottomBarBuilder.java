package com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.builder;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.google.common.collect.Maps;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.ParentBuilder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.extend.BaseVariableBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.exception.BottomBarFatalException;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.category.ProductCategoryFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.FetcherDAGStarter;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.ProductBuyBarModule;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.BottomBarActionDataVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.enums.BottomBarActionTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.ProductBottomBarVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.component.trade.button.BaseTradeButtonVO;
import lombok.extern.slf4j.Slf4j;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * @Author: guangyujie
 * @Date: 2025/4/9 11:34
 */
@ParentBuilder(
        startFetcher = FetcherDAGStarter.class,
        dependentFetchers = {
                ProductCategoryFetcher.class
        }
)
@Slf4j
public abstract class BaseBottomBarBuilder extends BaseVariableBuilder<ProductBuyBarModule> {

    private int productSecondCategoryId;
    private String className;

    @Override
    public final ProductBuyBarModule doBuild() {
        className = this.getClass().getSimpleName();
        Transaction transaction = Cat.newTransaction("BottomBarBuilder", className);
        try {
            productSecondCategoryId = getDependencyResult(ProductCategoryFetcher.class, ProductCategory.class)
                    .map(ProductCategory::getProductSecondCategoryId).orElse(0);
            ProductBuyBarModule productBuyBarModule = buildBuyBarModule();
            if (productBuyBarModule == null) {
                throw new BottomBarFatalException("底bar构建结果为null!!!");
            }
            checkBuyBar(productBuyBarModule);
            transaction.setStatus(Transaction.SUCCESS);
            return productBuyBarModule;
        } catch (Throwable throwable) {
            transaction.setStatus(throwable);
            log.error("{}.doBuild error!, request = {}", className, JSON.toJSONString(request), new BottomBarFatalException(throwable));
            return buildDefaultBuyBarModule();
        } finally {
            transaction.complete();
        }
    }

    protected abstract ProductBuyBarModule buildBuyBarModule();

    protected abstract ProductBuyBarModule buildDefaultBuyBarModule();

    private void checkBuyBar(final @NotNull ProductBuyBarModule productBuyBarModule) {
        try {
            List<BaseTradeButtonVO> tradeButtons = Optional.ofNullable(productBuyBarModule)
                    .map(ProductBuyBarModule::getBottomBar)
                    .map(ProductBottomBarVO::queryAllTradeButtons)
                    .orElse(new ArrayList<>());
            //交易按钮数量统计
            logTradeButtonCount(tradeButtons);
            //交易按钮动作统计
            logTradeButtonActionType(tradeButtons);
        } catch (Throwable throwable) {
            log.error("{}.checkBuyBar error!, request = {}", className, JSON.toJSONString(request), throwable);
        }
    }

    private void logTradeButtonCount(List<BaseTradeButtonVO> tradeButtons) {
        Map<String, String> tags = Maps.newHashMap();
        tags.put("builder", className);
        tags.put("tradeButtonCount", String.valueOf(tradeButtons.size()));
        tags.put("productSecondCategoryId", String.valueOf(productSecondCategoryId));
        Cat.logMetricForCount("TradeButtonCount", tags);
    }

    private void logTradeButtonActionType(List<BaseTradeButtonVO> tradeButtons) {
        for (BaseTradeButtonVO tradeButton : tradeButtons) {
            String actionType = Optional.ofNullable(tradeButton)
                    .map(BaseTradeButtonVO::getActionData)
                    .map(BottomBarActionDataVO::getActionType)
                    .map(BottomBarActionTypeEnum::fromCode)
                    .map(BottomBarActionTypeEnum::name)
                    .orElse(BottomBarActionTypeEnum.UNKNOWN.name());
            Map<String, String> tags = Maps.newHashMap();
            tags.put("actionType", actionType);
            tags.put("productSecondCategoryId", String.valueOf(productSecondCategoryId));
            Cat.logMetricForCount("TradeButtonActionTypeByCategory", tags);

            tags.remove("productSecondCategoryId");
            tags.put("builder", className);
            Cat.logMetricForCount("TradeButtonActionTypeByBuilder", tags);
        }
    }

}
