package com.sankuai.dzshoppingguide.product.detail.application.utils;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.enums.ProductTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.best.shop.ShopInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.defaultselect.SkuDefaultSelect;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class UrlHelperGetShareUrlTest {

    @Mock
    private ProductDetailPageRequest request;

    @Mock
    private ShopInfo shopInfo;

    @Mock
    private ShopIdMapper shopIdMapper;

    @Mock
    private DealGroupDTO dealGroupDTO;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    /**
     * Test getShareUrl method for MT_APP client type
     */
    @Test
    public void testGetShareUrlForMtApp() {
        // arrange
        when(request.getClientType()).thenReturn(ClientTypeEnum.MT_APP.getCode());
        when(request.getProductId()).thenReturn(12345L);
        when(shopIdMapper.getMtBestShopId()).thenReturn(67890L);
        // act
        String result = UrlHelper.getShareUrl(request, shopIdMapper, shopInfo);
        // assert
        assertNotNull(result);
        assertTrue(result.contains("imeituan%3A%2F%2Fwww.meituan.com%2Fmrn%3F"));
        assertTrue(result.contains("poiId%3D67890"));
        assertTrue(result.contains("productId%3D12345"));
    }

    /**
     * Test getShareUrl method for DP_APP client type
     */
    @Test
    public void testGetShareUrlForDpApp() {
        // arrange
        when(request.getClientType()).thenReturn(ClientTypeEnum.DP_APP.getCode());
        when(request.getProductId()).thenReturn(54321L);
        when(shopIdMapper.getDpBestShopId()).thenReturn(98765L);
        // act
        String result = UrlHelper.getShareUrl(request, shopIdMapper, shopInfo);
        // assert
        assertNotNull(result);
        assertTrue(result.contains("dianping%3A%2F%2Fmrn%3F"));
        assertTrue(result.contains("poiId%3D98765"));
        assertTrue(result.contains("productId%3D54321"));
    }

    /**
     * Test getShareUrl method for unknown client type
     */
    @Test
    public void testGetShareUrlForUnknownClientType() {
        // arrange
        when(request.getClientType()).thenReturn(ClientTypeEnum.UNKNOWN.getCode());
        // act
        String result = UrlHelper.getShareUrl(request, shopIdMapper, shopInfo);
        // assert
        assertNull(result);
    }

    /**
     * Test getShareUrl method with null idMapper
     */
    @Test
    public void testGetShareUrlWithNullIdMapper() {
        // arrange
        when(request.getClientType()).thenReturn(ClientTypeEnum.MT_APP.getCode());
        when(request.getProductId()).thenReturn(12345L);
        // act
        String result = UrlHelper.getShareUrl(request, null, shopInfo);
        // assert
        assertNotNull(result);
        assertTrue(result.contains("imeituan%3A%2F%2Fwww.meituan.com%2Fmrn%3F"));
        assertTrue(result.contains("poiId%3D0"));
        assertTrue(result.contains("productId%3D12345"));
    }

    /**
     * Test getShareUrl method with null request
     */
    @Test
    public void testGetShareUrlWithNullRequest() {
        // act & assert
        assertThrows(NullPointerException.class, () -> UrlHelper.getShareUrl(null, shopIdMapper, shopInfo));
    }

    /**
     * Test getHomeUrl method with MT_APP client type
     */
    @Test
    public void testGetHomeUrlWithMTAppClientType() {
        // arrange
        ProductDetailPageRequest request = mock(ProductDetailPageRequest.class);
        when(request.getClientType()).thenReturn(ClientTypeEnum.MT_APP.getCode());
        // act
        String result = UrlHelper.getHomeUrl(request);
        // assert
        assertEquals("imeituan://www.meituan.com/home", result);
    }

    /**
     * Test getHomeUrl method with DP_APP client type
     */
    @Test
    public void testGetHomeUrlWithDPAppClientType() {
        // arrange
        ProductDetailPageRequest request = mock(ProductDetailPageRequest.class);
        when(request.getClientType()).thenReturn(ClientTypeEnum.DP_APP.getCode());
        // act
        String result = UrlHelper.getHomeUrl(request);
        // assert
        assertEquals("dianping://home", result);
    }

    /**
     * Test getHomeUrl method with UNKNOWN client type
     */
    @Test
    public void testGetHomeUrlWithUnknownClientType() {
        // arrange
        ProductDetailPageRequest request = mock(ProductDetailPageRequest.class);
        when(request.getClientType()).thenReturn(ClientTypeEnum.UNKNOWN.getCode());
        // act
        String result = UrlHelper.getHomeUrl(request);
        // assert
        assertNull(result);
    }

    /**
     * Test getHomeUrl method with DP_M client type
     */
    @Test
    public void testGetHomeUrlWithDPMClientType() {
        // arrange
        ProductDetailPageRequest request = mock(ProductDetailPageRequest.class);
        when(request.getClientType()).thenReturn(ClientTypeEnum.DP_M.getCode());
        // act
        String result = UrlHelper.getHomeUrl(request);
        // assert
        assertNull(result);
    }

    /**
     * Test getHomeUrl method with MT_I client type
     */
    @Test
    public void testGetHomeUrlWithMTIClientType() {
        // arrange
        ProductDetailPageRequest request = mock(ProductDetailPageRequest.class);
        when(request.getClientType()).thenReturn(ClientTypeEnum.MT_I.getCode());
        // act
        String result = UrlHelper.getHomeUrl(request);
        // assert
        assertNull(result);
    }

    /**
     * Test getIdleHoursBuyUrl with normal URL
     */
    @Test
    void testGetIdleHoursBuyUrl_WithNormalUrl() throws Throwable {
        // arrange
        ProductDetailPageRequest request = new ProductDetailPageRequest();
        request.setClientType(ClientTypeEnum.DP_APP.getCode());
        request.setProductType(ProductTypeEnum.DEAL.getCode());
        SkuDefaultSelect skuDefaultSelect = new SkuDefaultSelect(123L);
        ShopInfo shopInfo = new ShopInfo();
        ShopIdMapper shopIdMapper = new ShopIdMapper();
        when(dealGroupDTO.getDpDealGroupId()).thenReturn(1234L);
        when(dealGroupDTO.getMtDealGroupId()).thenReturn(5678L);
        ProductBaseInfo dealGroupBase = new ProductBaseInfo(ProductTypeEnum.DEAL, dealGroupDTO);
        Integer cityId = 1;
        // act
        String result = UrlHelper.getIdleHoursBuyUrl(request, skuDefaultSelect, shopInfo, shopIdMapper, dealGroupBase, cityId);
        // assert
        assertEquals("dianping://createorder?dealid=5678&shopuuid=null&shopid=0&promosource=1", result);
    }

    /**
     * Test getIdleHoursBuyUrl when getCommonBuyUrl returns null
     */
    @Test
    void testGetIdleHoursBuyUrl_WithNullUrl() throws Throwable {
        // arrange
        ProductDetailPageRequest request = new ProductDetailPageRequest();
        request.setClientType(ClientTypeEnum.DP_APP.getCode());
        request.setProductType(ProductTypeEnum.DEAL.getCode());
        SkuDefaultSelect skuDefaultSelect = new SkuDefaultSelect(123L);
        ShopInfo shopInfo = new ShopInfo();
        ShopIdMapper shopIdMapper = new ShopIdMapper();
        when(dealGroupDTO.getDpDealGroupId()).thenReturn(1234L);
        when(dealGroupDTO.getMtDealGroupId()).thenReturn(5678L);
        ProductBaseInfo dealGroupBase = new ProductBaseInfo(ProductTypeEnum.DEAL, dealGroupDTO);
        Integer cityId = 1;
        // act
        String result = UrlHelper.getIdleHoursBuyUrl(request, skuDefaultSelect, shopInfo, shopIdMapper, dealGroupBase, cityId);
        // assert
        assertEquals("dianping://createorder?dealid=5678&shopuuid=null&shopid=0&promosource=1", result);
    }

    /**
     * Test getIdleHoursBuyUrl when getCommonBuyUrl returns empty string
     */
    @Test
    void testGetIdleHoursBuyUrl_WithEmptyUrl() throws Throwable {
        // arrange
        ProductDetailPageRequest request = new ProductDetailPageRequest();
        request.setClientType(ClientTypeEnum.DP_APP.getCode());
        request.setProductType(ProductTypeEnum.DEAL.getCode());
        SkuDefaultSelect skuDefaultSelect = new SkuDefaultSelect(123L);
        ShopInfo shopInfo = new ShopInfo();
        ShopIdMapper shopIdMapper = new ShopIdMapper();
        when(dealGroupDTO.getDpDealGroupId()).thenReturn(1234L);
        when(dealGroupDTO.getMtDealGroupId()).thenReturn(5678L);
        ProductBaseInfo dealGroupBase = new ProductBaseInfo(ProductTypeEnum.DEAL, dealGroupDTO);
        Integer cityId = 1;
        // act
        String result = UrlHelper.getIdleHoursBuyUrl(request, skuDefaultSelect, shopInfo, shopIdMapper, dealGroupBase, cityId);
        // assert
        assertEquals("dianping://createorder?dealid=5678&shopuuid=null&shopid=0&promosource=1", result);
    }
}
