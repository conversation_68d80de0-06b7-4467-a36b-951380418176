package com.sankuai.dzshoppingguide.product.detail.application.builder.pricebar.builder;

import com.dianping.deal.sales.common.datatype.SalesDisplayInfoDTO;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sankuai.dealuser.price.display.api.enums.PricePowerTagEnum;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PricePowerTagDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PricePowerTagItem;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.extend.BaseVariableBuilder;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.pricebar.builder.enums.CouponGuideTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.pricebar.component.InflateCouponTipsComponent;
import com.sankuai.dzshoppingguide.product.detail.application.builder.pricebar.config.IconConfig;
import com.sankuai.dzshoppingguide.product.detail.application.builder.pricebar.config.ProductPricePowerTagConfig;
import com.sankuai.dzshoppingguide.product.detail.application.constants.DealAttrKeys;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.dto.ProductPriceReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.attr.SkuAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.sale.ProductSaleReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.statesubsidies.CountrySubsidiesQualificationDTO;
import com.sankuai.dzshoppingguide.product.detail.application.utils.PriceHelper;
import com.sankuai.dzshoppingguide.product.detail.application.utils.PriceUtils;
import com.sankuai.dzshoppingguide.product.detail.application.utils.PromoHelper;
import com.sankuai.dzshoppingguide.product.detail.application.utils.TimesDealUtil;
import com.sankuai.dzshoppingguide.product.detail.domain.utils.LionConfigUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DetailPriceVO;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DetailSaleVO;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.Icon;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.ProductBestPromoFormula;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.PromoTagEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.pricebar.vo.ProductPriceBarModuleVO;
import com.sankuai.general.product.query.center.client.dto.PriceDTO;
import com.sankuai.general.product.query.center.client.dto.deal.DealGroupDealDTO;
import com.sankuai.nibmkt.promotion.api.common.enums.PromotionPropertyEnum;
import com.sankuai.nibmkt.promotion.api.query.model.magicalmember.response.MMCTooltipTextDTO;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

import static com.sankuai.dealuser.price.display.api.enums.PricePowerTagEnum.*;

/**
 * <AUTHOR>
 * @date 2025-03-14
 * @desc 商品价格栏模块构建器
 */
public abstract class AbstractProductBarModuleBuilder<T extends ProductPriceBarModuleVO> extends BaseVariableBuilder<T> {
    private static final String DISCOUNT_TAG_FORMAT = "%s 折";

    private static final String PRICE_SYMBOL = "¥";

    protected static final Set<ClientTypeEnum> PRIVATE_LIVE_CLIENT_TYPE = Sets.newHashSet(ClientTypeEnum.MT_LIVE_XCX, ClientTypeEnum.MT_LIVE_ORDER_XCX);

    private static final String PRICE_TREND_LAYER_URL_MT = "imeituan://www.meituan.com/mrn?mrn_biz=gc&mrn_entry=pricehelperpopup&mrn_component=TimeTrendPopup&isTransparent=true&hideLoading=true&mrn_transparent=true&mrn_hideloading=true&mrn_hideNextNavBar=true&dealGroupId=%s&shopId=%s&pageSource=deal";

    private static final String PRICE_TREND_LAYER_URL_DP = "dianping://mrn?mrn_biz=gc&mrn_entry=pricehelperpopup&mrn_component=TimeTrendPopup&isTransparent=true&hideLoading=true&mrn_transparent=true&mrn_hideloading=true&mrn_hideNextNavBar=true&dealGroupId=%s&shopId=%s&pageSource=deal";
    /**
     * 足疗价格力排序标签
     */
    private static final List<Integer> FOOT_MASSAGE_PRICE_POWER_TAG_SORT_ORDER = Lists.newArrayList(
            NETWORK_LOW_PRICE.getType(),
            LOWEST_PRICE_IN_RADIUS_3KM_RANGE.getType(),
            LOWEST_PRICE_IN_CITY.getType(),
            LOWEST_PRICE_IN_DISTRICT.getType(),
            LOWEST_PRICE_IN_REGION.getType(),
            LOWEST_PRICE_IN_RECENT_365_DAYS.getType(),
            LOWEST_PRICE_IN_RECENT_180_DAYS.getType(),
            LOWEST_PRICE_IN_RECENT_90_DAYS.getType(),
            LOWEST_PRICE_IN_RECENT_60_DAYS.getType(),
            LOWEST_PRICE_IN_RECENT_30_DAYS.getType()
    );

    /**
     * 时间价格力标签
     */
    public static final Set<String> TIME_PRICE_POWER_TAG_VALUES = Sets.newHashSet(
            LOWEST_PRICE_IN_RECENT_30_DAYS.getDesc(),
            LOWEST_PRICE_IN_RECENT_60_DAYS.getDesc(),
            LOWEST_PRICE_IN_RECENT_90_DAYS.getDesc(),
            LOWEST_PRICE_IN_RECENT_180_DAYS.getDesc(),
            LOWEST_PRICE_IN_RECENT_365_DAYS.getDesc()
    );

    @Override
    public T doBuild() {
        return null;
    }


    protected DetailPriceVO buildPriceVO(ProductPriceParam param) {
        PriceDisplayDTO normalPriceDisplayDTO = Optional.ofNullable(param.getNormalPriceReturnValue()).map(ProductPriceReturnValue::getPriceDisplayDTO).orElse(null);
        if (normalPriceDisplayDTO == null) {
            return buildDefaultPriceVO(param.getProductBaseInfo());
        }
        PriceDisplayDTO dealPromoPriceDisplayDTO = null;
        if (!PRIVATE_LIVE_CLIENT_TYPE.contains(request.getClientTypeEnum()) && Objects.nonNull(param.getDealPromoPriceReturnValue())) {
            dealPromoPriceDisplayDTO = param.getDealPromoPriceReturnValue().getPriceDisplayDTO();
        }
        return buildPriceVO(normalPriceDisplayDTO, dealPromoPriceDisplayDTO, param.getProductCategory(), param.getProductBaseInfo(), param.getSkuAttr(),param.isAtmosphere());
    }

    /**
     * 使用查询中心的价格来兜底
     * @param productBaseInfo 商品基础信息
     * @return 默认价格信息
     */
    private DetailPriceVO buildDefaultPriceVO(ProductBaseInfo productBaseInfo) {
        if (Objects.isNull(productBaseInfo) || Objects.isNull(productBaseInfo.getPrice())) {
            return null;
        }
        PriceDTO productPrice = productBaseInfo.getPrice();
        DetailPriceVO priceVO = new DetailPriceVO();
        priceVO.setMarketPrice(productPrice.getMarketPrice());
        priceVO.setFinalPrice(productPrice.getSalePrice());
        return priceVO;
    }

    public DetailSaleVO buildSaleInfo(ProductSaleReturnValue productSaleReturnValue) {
        if (Objects.isNull(productSaleReturnValue)) {
            return new DetailSaleVO();
        }
        SalesDisplayInfoDTO saleDisplayDTO = productSaleReturnValue.getSaleDisplayDTO();
        if (Objects.isNull(saleDisplayDTO)) {
            return new DetailSaleVO();
        }
        DetailSaleVO saleVO = new DetailSaleVO();
        saleVO.setSaleTag(Objects.nonNull(saleDisplayDTO.getSalesTag()) ? saleDisplayDTO.getSalesTag() : null);
        saleVO.setTextColor("#FFFFFF");
        return saleVO;
    }

    protected DetailPriceVO buildPriceVO(PriceDisplayDTO normalPriceDisplayDTO, PriceDisplayDTO dealPromoPriceDisplayDTO,
                                       ProductCategory productCategory, ProductBaseInfo productBaseInfo, SkuAttr skuAttr,boolean isAtmosphere) {
        DetailPriceVO priceVO = new DetailPriceVO();
        // 到手价
        String finalPrice = PriceUtils.buildFinalPrice(normalPriceDisplayDTO.getPrice());
        priceVO.setFinalPrice(finalPrice);
        // 门市价
        priceVO.setMarketPrice(PriceUtils.buildMarketPrice(normalPriceDisplayDTO.getMarketPrice()));
        priceVO.setPriceSymbol(PRICE_SYMBOL);
        // 折扣
        priceVO.setDiscountTagBackgroundColor("#FFF1EC");
        priceVO.setDiscountTagRadius(3);
        priceVO.setDiscountTagHasBorder(false);
        priceVO.setDiscountTagTextColor("#FF4B10");
        BigDecimal discount = calcDiscount(normalPriceDisplayDTO.getPrice(), normalPriceDisplayDTO.getMarketPrice());
        if (Objects.nonNull(discount)) {
            priceVO.setDiscountTag(String.format(DISCOUNT_TAG_FORMAT, discount.toPlainString()));
        }
        int secondCategoryId = productCategory.getProductSecondCategoryId();
        // 价格力标签
        String productTag = buildProductTag(dealPromoPriceDisplayDTO, secondCategoryId);
        priceVO.setProductTag(productTag);
        boolean clickable = buildProductTagClickable(productTag, secondCategoryId, request.getClientTypeEnum());
        if (clickable) {
            String priceTrendLayerUrl = request.getClientTypeEnum().isMtClientType()
                    ? String.format(PRICE_TREND_LAYER_URL_MT, request.getProductId(), request.getPoiId())
                    : String.format(PRICE_TREND_LAYER_URL_DP, request.getProductId(), request.getPoiId());
            priceVO.setProductTagClickUrl(priceTrendLayerUrl);
        }
        // 处理次卡单次价
        boolean isTimesDeal = TimesDealUtil.isTimesDeal(productBaseInfo);
        Long skuId = Optional.ofNullable(productBaseInfo)
                .map(ProductBaseInfo::getDeals)
                .orElse(Collections.emptyList())
                .stream()
                .findFirst()
                .map(DealGroupDealDTO::getDealId)
                .orElse(0L);
        if (Objects.isNull(skuAttr)) {
            return priceVO;
        }

        String cardTimes = skuAttr.getSkuAttrFirstValue(skuId, DealAttrKeys.SYS_MULTI_SALE_NUMBER);

        if (!isTimesDeal || StringUtils.isBlank(cardTimes) || !NumberUtils.isDigits(cardTimes)) {
            return priceVO;
        }

        String singlePrice = new BigDecimal(finalPrice)
                .divide(new BigDecimal(cardTimes), 2, RoundingMode.UP)
                .stripTrailingZeros()
                .toPlainString();

        if (StringUtils.isBlank(singlePrice)) {
            return priceVO;
        }

        priceVO.setSinglePrice(singlePrice);
        priceVO.setPricePrefix("单次");
        priceVO.setDiscountTag(isAtmosphere 
            ? String.format("%s次总价¥%s", cardTimes, finalPrice)
            : String.format("%s/次｜%s", singlePrice, priceVO.getDiscountTag()));
        return priceVO;
    }

    private boolean buildProductTagClickable(String productTag, int secondCategoryId, ClientTypeEnum clientTypeEnum) {
        String platform = clientTypeEnum.isMtClientType() ? "mt" : "dp";
        Map<String, String> categoryId2ExpIdMap = LionConfigUtils.getComparePriceAssistantExpConfig();
        String categoryIdStr = platform + secondCategoryId;
        if (!categoryId2ExpIdMap.containsKey(categoryIdStr)) {
            return false;
        }
        return TIME_PRICE_POWER_TAG_VALUES.contains(productTag);
    }

    private String buildProductTag(PriceDisplayDTO priceDisplayDTO) {
        if (priceDisplayDTO == null) {
            return null;
        }
        PricePowerTagDisplayDTO pricePowerTagDisplayDTO = Optional.of(priceDisplayDTO)
                .map(PriceDisplayDTO::getPricePowerTagDisplayDTO).orElse(null);
        if (Objects.isNull(pricePowerTagDisplayDTO) || CollectionUtils.isEmpty(pricePowerTagDisplayDTO.getAllTagList())) {
            return null;
        }
        List<PricePowerTagItem> allTagList = pricePowerTagDisplayDTO.getAllTagList();
        // allTagList 按FOOT_MASSAGE_PRICE_POWER_TAG_SORT_ORDER 顺序排序
        allTagList.sort(Comparator.comparingInt(powerTagItem -> FOOT_MASSAGE_PRICE_POWER_TAG_SORT_ORDER.indexOf(powerTagItem.getTagType())));
        PricePowerTagItem firstTag = allTagList.get(0);
        boolean showPricePowerTag = firstTag != null && FOOT_MASSAGE_PRICE_POWER_TAG_SORT_ORDER.contains(firstTag.getTagType());
        if (!showPricePowerTag) {
            return null;
        }
        return firstTag.getTagName();
    }

    private String buildProductTag(PriceDisplayDTO priceDisplayDTO, int secondCategoryId) {
        if (Objects.isNull(priceDisplayDTO)) {
            return null;
        }
        PricePowerTagDisplayDTO pricePowerTagDisplayDTO = Optional.of(priceDisplayDTO)
                .map(PriceDisplayDTO::getPricePowerTagDisplayDTO).orElse(null);
        if (Objects.isNull(pricePowerTagDisplayDTO) || CollectionUtils.isEmpty(pricePowerTagDisplayDTO.getAllTagList())) {
            return null;
        }
        List<PricePowerTagItem> allTagList = pricePowerTagDisplayDTO.getAllTagList();
        // 获取当前类目的配置
        ProductPricePowerTagConfig pricePowerTagConfig = getProductPricePowerTagConfig(secondCategoryId);
        if (CollectionUtils.isEmpty(allTagList) || Objects.isNull(pricePowerTagConfig)
                || CollectionUtils.isEmpty(pricePowerTagConfig.getSortPowerTags())) {
            return null;
        }
        // allTagList根据pricePowerTagConfig.getSortPowerTags()枚举列表顺序排序
        allTagList.sort((tag1, tag2) -> {
            PricePowerTagEnum powerTag1 = findByCode(tag1.getTagType());
            PricePowerTagEnum powerTag2 = findByCode(tag2.getTagType());
            List<PricePowerTagEnum> sortedTags = pricePowerTagConfig.getSortPowerTags();
            return Integer.compare(sortedTags.indexOf(powerTag1), sortedTags.indexOf(powerTag2));
        });
        PricePowerTagItem firstTag = allTagList.get(0);
        PricePowerTagEnum firstTagEnum = findByCode(firstTag.getTagType());
        boolean showPricePowerTag = firstTagEnum != null && pricePowerTagConfig.getSortPowerTags().contains(firstTagEnum);
        return showPricePowerTag ? firstTag.getTagName() : null;
    }

    private ProductPricePowerTagConfig getProductPricePowerTagConfig(int secondCategoryId) {
        List<ProductPricePowerTagConfig> productPricePowerTagConfig = com.sankuai.dzshoppingguide.product.detail.application.utils.LionConfigUtils.getProductPricePowerTagConfig();
        if (CollectionUtils.isEmpty(productPricePowerTagConfig)) {
            return null;
        }
        return productPricePowerTagConfig.stream()
                .filter(config -> isConfigMatchCategory(config, secondCategoryId))
                .findFirst()
                .orElseGet(() -> getDefaultConfig(productPricePowerTagConfig));
    }

    private boolean isConfigMatchCategory(ProductPricePowerTagConfig config, int categoryId) {
        return CollectionUtils.isNotEmpty(config.getCategoryIds()) 
               && config.getCategoryIds().contains(categoryId);
    }

    private ProductPricePowerTagConfig getDefaultConfig(List<ProductPricePowerTagConfig> configs) {
        return configs.stream()
                .filter(config -> CollectionUtils.isEmpty(config.getCategoryIds()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 计算折扣(保留1位)四舍五入，即 9.9 折
     * @param finalPrice 优惠后价
     * @param marketPrice  优惠前价
     * @return 折扣
     */
    protected BigDecimal calcDiscount(BigDecimal finalPrice, BigDecimal marketPrice) {
        if (finalPrice == null || marketPrice == null || marketPrice.compareTo(BigDecimal.ZERO) == 0 || finalPrice.compareTo(BigDecimal.ZERO) == 0) {
            return null;
        }
        BigDecimal discount = finalPrice.divide(marketPrice, 3, RoundingMode.DOWN)
                .multiply(new BigDecimal(10)).setScale(1, RoundingMode.UP);
        return discount.compareTo(BigDecimal.ZERO) > 0 ? discount : null;
    }

    /**
     * 构建最优价格算价公式
     * @param promoPriceDisplayDTO
     * @param finalPrice
     * @return
     */
    protected List<ProductBestPromoFormula> buildBestPromoFormula(PriceDisplayDTO promoPriceDisplayDTO, String finalPrice) {
        IconConfig iconConfig = com.sankuai.dzshoppingguide.product.detail.application.utils.LionConfigUtils.getIconConfig();
        List<PromoDTO> usedPromos = promoPriceDisplayDTO.getUsedPromos();
        List<ProductBestPromoFormula> productBestPromoFormulaList = Lists.newArrayList();
        ProductBestPromoFormula marketPriceFormula = new ProductBestPromoFormula();
        marketPriceFormula.setPriceDesc(iconConfig.getMarketPriceIconText());
        marketPriceFormula.setPrice(PriceUtils.buildMarketPrice(promoPriceDisplayDTO.getMarketPrice()));
        marketPriceFormula.setPromoDesc(PromoTagEnum.MARKET_PRICE.getDesc());
        marketPriceFormula.setPriceSymbol(PRICE_SYMBOL);
        productBestPromoFormulaList.add(marketPriceFormula);
        Map<String, String> extendDisplayInfoMap = promoPriceDisplayDTO.getExtendDisplayInfo();
        MMCTooltipTextDTO mmcTooltipTextDTO = InflateCouponTipsComponent.getMMCTooltipTextDTO(extendDisplayInfoMap);
        for (PromoDTO promoDTO : usedPromos) {
            if (promoDTO.getIdentity() == null || promoDTO.getAmount() == null || StringUtils.isEmpty(promoDTO.getIdentity().getPromoTypeDesc())) {
                continue;
            }
            ProductBestPromoFormula productBestPromoFormula = new ProductBestPromoFormula();
            productBestPromoFormula.setPrice(PriceHelper.dropLastZero(promoDTO.getAmount()));
            productBestPromoFormula.setPromoDesc(PromoHelper.convertPromoTag(promoDTO.getIdentity().getPromoShowType(), promoDTO.getIdentity().getPromoTypeDesc()));
            productBestPromoFormula.setPriceSymbol(PRICE_SYMBOL);
            if (PromoTagEnum.MAGICAL_MEMBER_PLATFORM_COUPON.getType().equals(promoDTO.getIdentity().getPromoShowType())) {
                Map<String, String> promotionOtherInfoMap = promoDTO.getPromotionOtherInfoMap();
                boolean afterInflate = Boolean.parseBoolean(promotionOtherInfoMap.get(PromotionPropertyEnum.AFTER_INFLATE.getValue()));
                productBestPromoFormula.setAfterInflate(afterInflate);
                //白盒红包icon
                Icon whiteBoxIcon = new Icon();
                whiteBoxIcon.setIcon(iconConfig.getAfterInflate());
                whiteBoxIcon.setIconWidth(iconConfig.getAfterInflateWidth());
                whiteBoxIcon.setIconHeight(iconConfig.getAfterInflateHeight());
                productBestPromoFormula.setWhiteBoxIcon(whiteBoxIcon);
                productBestPromoFormula.setPriceColor("#FF6633");
                productBestPromoFormula.setPromoDesc(PromoTagEnum.MAGICAL_MEMBER_PLATFORM_COUPON.getDesc());
                CouponGuideTypeEnum guideType = InflateCouponTipsComponent.getCouponGuideTypeEnum(usedPromos, new BigDecimal(finalPrice), mmcTooltipTextDTO);
                if (guideType != null && CouponGuideTypeEnum.GUIDE_INFLATE.getCode().equals(guideType.getCode())) {
                    if(mmcTooltipTextDTO != null){
                        productBestPromoFormula.setBlackBoxPriceColor("#FF2626");
                        productBestPromoFormula.setBlackBoxPrice(mmcTooltipTextDTO.getPreInflateDiscountAmount());
                    }
                    //黑盒红包icon
                    Icon blackBoxIcon = new Icon();
                    //引导膨胀价格公示展示红包icon
                    blackBoxIcon.setIcon(iconConfig.getDoubleRedPacket());
                    blackBoxIcon.setIconWidth(iconConfig.getDoubleRedPacketWidth());
                    blackBoxIcon.setIconHeight(iconConfig.getDoubleRedPacketHeight());
                    productBestPromoFormula.setBlackBoxIcon(blackBoxIcon);
                }

            }
            productBestPromoFormulaList.add(productBestPromoFormula);
        }
        // 按照 orderTags 列表的顺序排序
        List<String> orderedTags = PromoTagEnum.orderedTags;
        productBestPromoFormulaList.sort((o1, o2) -> compareByOrderedTags(o1.getPromoDesc(), o2.getPromoDesc(), orderedTags));
        // 排序后处理神券描述
        productBestPromoFormulaList.forEach(formula -> {
            if (Objects.equals(formula.getPromoDesc(), PromoTagEnum.MAGICAL_MEMBER_PLATFORM_COUPON.getDesc())) {
                formula.setPromoDesc("");
            }
        });
        return productBestPromoFormulaList;
    }

    /**
     * 根据有序标签列表进行排序
     * @param desc1 标签1
     * @param desc2 标签2
     * @param orderedTags 有序标签列表
     * @return 比较结果
     */
    private int compareByOrderedTags(String desc1, String desc2, List<String> orderedTags) {
        // 处理空值情况
        if (desc1 == null && desc2 == null) {
            return 0;
        }
        if (desc1 == null) {
            return 1;
        }
        if (desc2 == null) {
            return -1;
        }

        // 获取在 orderedTags 中的位置
        int index1 = orderedTags.indexOf(desc1);
        int index2 = orderedTags.indexOf(desc2);

        // 如果标签不在 orderedTags 中,则排在后面
        if (index1 == -1 && index2 == -1) {
            return 0;
        }
        if (index1 == -1) {
            return 1;
        }
        if (index2 == -1) {
            return -1;
        }

        return Integer.compare(index1, index2);
    }

    @Data
    @Builder
    public static class ProductPriceParam {
        private ProductPriceReturnValue normalPriceReturnValue;
        private ProductPriceReturnValue dealPromoPriceReturnValue;
        private ProductCategory productCategory;
        private ProductBaseInfo productBaseInfo;
        private CountrySubsidiesQualificationDTO subsidiesQualificationDTO;
        private SkuAttr skuAttr;
        private boolean isAtmosphere;
    }
}