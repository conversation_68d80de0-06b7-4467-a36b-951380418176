package com.sankuai.dzshoppingguide.product.detail.application.utils;

import com.dianping.cat.Cat;
import org.apache.commons.lang.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class HtmlUtils {

    /**
     * 去除HTML所有标签
     *
     * @param html
     * @return
     */
    public static String removeHtml(String html) {
        if (StringUtils.isBlank(html)) {
            return html;
        }
        return html.replaceAll("<.*?>", "");
    }

    public static String removeLastEnter(String html) {
        if (StringUtils.isBlank(html)) {
            return html;
        }
        if (html.endsWith("\n")){
            return html.substring(0, html.length()-1);
        }
        return html;
    }

    /**
     * 使用\n来替换div or br等有回车效果的标签
     * 反转移&nbsp;等字符
     *
     * @param html
     * @return
     */
    public static String enterReplace(String html) {
        if (StringUtils.isBlank(html)) {
            return html;
        }
        html = org.springframework.web.util.HtmlUtils.htmlUnescape(html);
        return html
                .replaceAll("<(div|br).*?>", "")
                .replaceAll("</(div|br|p|h1|h2|h3|h4|h5|h6).*?>", "\n")
                .replace("\n\n", "\n");
    }

    /**
     * 尽可能保留html样式的情况下将html转换为text
     *
     * @param html
     * @return
     */
    public static String html2text(String html) {
        if (StringUtils.isBlank(html)) {
            return html;
        }
        return removeLastEnter(removeHtml(enterReplace(html.trim())));
    }

    public static String format(String row) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.util.HtmlUtils.format(java.lang.String)");
        if (StringUtils.isBlank(row)) {
            return StringUtils.EMPTY;
        }

        for (HtmlLabel htmlLabel : HtmlLabel.values()) {
            switch (htmlLabel.getLableType()) {
                case RMALL:
                    row = rmAllByLabel(row, htmlLabel.getLableName());
                    break;
                case MIDDLE:
                    row = rmEndsByLabel(row, htmlLabel.getLableName());
                    break;
                default:
                    break;
            }
        }
        return row;
    }

    /**
     * 去掉匹配的整个文本
     */
    public static String rmAllByLabel(String rowLine, String label) {
        Cat.logEvent("INVALID_METHOD_4", "com.dianping.mobile.mapi.dztgdetail.util.HtmlUtils.rmAllByLabel(java.lang.String,java.lang.String)");
        if (StringUtils.isBlank(rowLine)) {
            return StringUtils.EMPTY;
        }

        Pattern p = Pattern.compile(genRegex(label));
        Matcher m = p.matcher(rowLine.trim());
        while (m.find()) {
            rowLine = rowLine.replace(m.group(), StringUtils.EMPTY);
        }

        return rmRedundantLabel(rowLine, label);
    }

    /**
     * 去掉标签名，留下中间的文本
     */
    public static String rmEndsByLabel(String rowLine, String lable) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.util.HtmlUtils.rmEndsByLabel(java.lang.String,java.lang.String)");
        if (StringUtils.isBlank(rowLine)) {
            return StringUtils.EMPTY;
        }

        Pattern p = Pattern.compile(genRegex(lable));
        Matcher m = p.matcher(rowLine.trim());
        while (m.find()) {
            rowLine = rowLine.replace(m.group(), m.group(2));
        }
        return rmRedundantLabel(rowLine, lable);
    }

    /**
     * 去掉多余的标签
     */
    private static String rmRedundantLabel(String rowLine, String label) {
        Cat.logEvent("INVALID_METHOD_5", "com.dianping.mobile.mapi.dztgdetail.util.HtmlUtils.rmRedundantLabel(java.lang.String,java.lang.String)");
        if (StringUtils.isBlank(rowLine)) {
            return StringUtils.EMPTY;
        }

        Pattern p = Pattern.compile("<" + label + "(.*?)>");
        Matcher m = p.matcher(rowLine.trim());
        while (m.find()) {
            rowLine = rowLine.replace(m.group(), StringUtils.EMPTY);
        }

        p = Pattern.compile("</" + label + ">");
        m = p.matcher(rowLine.trim());
        while (m.find()) {
            rowLine = rowLine.replace(m.group(), StringUtils.EMPTY);
        }

        return rowLine.trim();
    }

    private static String genRegex(String label) {
        Cat.logEvent("INVALID_METHOD_3", "com.dianping.mobile.mapi.dztgdetail.util.HtmlUtils.genRegex(java.lang.String)");
        if (StringUtils.isBlank(label)) {
            return StringUtils.EMPTY;
        }

        StringBuilder builder = new StringBuilder();
        builder.append("<").append(label);
        builder.append("(.*?)>(.*?)</");
        builder.append(label).append(">");
        return builder.toString();
    }

    public enum HtmlLabel {

        A("a", LabelType.MIDDLE),
        B("b", LabelType.MIDDLE),
        UL("ul", LabelType.MIDDLE),
        STRONG("strong", LabelType.MIDDLE),
        LI("li", LabelType.MIDDLE),
        SPAN("span", LabelType.MIDDLE),
        SUP("sup", LabelType.RMALL),
        LABEL("label", LabelType.RMALL);

        /**
         * 标签名
         */
        private String labelName;
        /**
         * 标签处理类型
         */
        private LabelType labelType;

        private HtmlLabel(String labelName, LabelType labelType) {
            this.labelName = labelName;
            this.labelType = labelType;
        }

        public enum LabelType {
            /**
             * 整个去掉
             */
            RMALL,
            /**
             * 留下中间的文本
             */
            MIDDLE
        }

        public String getLableName() {
            return labelName;
        }

        public LabelType getLableType() {
            return labelType;
        }
    }
}
