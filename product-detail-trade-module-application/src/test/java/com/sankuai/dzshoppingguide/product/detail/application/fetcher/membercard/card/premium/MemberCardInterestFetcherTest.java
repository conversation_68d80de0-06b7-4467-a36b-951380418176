package com.sankuai.dzshoppingguide.product.detail.application.fetcher.membercard.card.premium;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

import com.sankuai.dz.product.detail.gateway.spi.dto.ShepherdGatewayParam;
import com.sankuai.dz.product.detail.gateway.spi.enums.ProductTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.dealgroup.DealGroupIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.membercard.card.entrance.ShopMemberEntranceFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;

import java.util.concurrent.CompletableFuture;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class MemberCardInterestFetcherTest {

    @InjectMocks
    private MemberCardInterestFetcher memberCardInterestFetcher;

    @Mock
    private CompositeAtomService compositeAtomService;

    @Mock
    private ShopMemberEntranceFetcher shopMemberEntranceFetcher;

    @Mock
    private DealGroupIdMapperFetcher dealGroupIdMapperFetcher;

    @Mock
    private ProductDetailPageRequest request;

    private void setupRequestWithProductType(ProductTypeEnum productTypeEnum) {
        when(request.getProductTypeEnum()).thenReturn(productTypeEnum);
    }

    private void setupRequestWithShepherdGatewayParam() {
        when(request.getShepherdGatewayParam()).thenReturn(new ShepherdGatewayParam());
    }

    // Utility method to invoke protected methods using reflection
    private Object invokeProtectedMethod(Object target, String methodName, Class<?>... parameterTypes) throws Exception {
        java.lang.reflect.Method method = target.getClass().getSuperclass().getDeclaredMethod(methodName, parameterTypes);
        method.setAccessible(true);
        return method.invoke(target, ShopMemberEntranceFetcher.class);
    }

    @Test
    public void testDoFetchProductTypeNotDeal() throws Throwable {
        setupRequestWithProductType(ProductTypeEnum.RESERVE);
        CompletableFuture<PremiumMemberCardResult> result = memberCardInterestFetcher.doFetch();
        assertNotNull(result);
        assertNull(result.get());
    }
}
