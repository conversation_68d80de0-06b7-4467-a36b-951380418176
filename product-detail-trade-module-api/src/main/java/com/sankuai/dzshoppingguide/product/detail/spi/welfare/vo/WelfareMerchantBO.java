package com.sankuai.dzshoppingguide.product.detail.spi.welfare.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.dz.product.detail.gateway.spi.vo.AbstractModuleVO;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author: huqi
 * @Date: 2020/3/12 3:39 下午
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TypeDoc(description = "公益商家模型")
@MobileDo(id = 0x6597)
public class WelfareMerchantBO extends AbstractModuleVO {
    /**
     * 公益商家描述
     */
    @MobileDo.MobileField(key = 0xfebf)
    private String desc;

    /**
     * 公益商家标题
     */
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    /**
     * 公益商家icon
     */
    @MobileDo.MobileField(key = 0x3c48)
    private String icon;


    @Override
    public String getModuleKey() {
        return ModuleKeyConstants.WELFARE_INFO_MODULE;
    }
}
