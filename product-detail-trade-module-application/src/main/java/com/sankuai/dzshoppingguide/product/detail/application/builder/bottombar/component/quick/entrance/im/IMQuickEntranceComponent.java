package com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.component.quick.entrance.im;

import com.alibaba.fastjson.JSON;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.consult.DetailIMResult;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.enums.OpenTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.extend.redirect.SimpleRedirectActionVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.component.quick.entrance.QuickEntranceButtonVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

/**
 * @Author: guangyujie
 * @Date: 2025/3/20 15:30
 */
@Slf4j
public class IMQuickEntranceComponent {

    public static QuickEntranceButtonVO build(final ProductDetailPageRequest request,
                                              final DetailIMResult detailIMResult) {
        try {
            if (detailIMResult == null || StringUtils.isBlank(detailIMResult.getImUrl())) {
                return null;
            }
            QuickEntranceButtonVO button = new QuickEntranceButtonVO();
            button.setButtonText("在线咨询");
            button.setButtonPic("https://p0.meituan.net/ingee/fbfd04caeb2400ee6c909e0392b48d311652.png");
            button.setActionData(new SimpleRedirectActionVO(OpenTypeEnum.redirect, detailIMResult.getImUrl()));
            return button;
        } catch (Throwable throwable) {
            log.error("ShopQuickEntranceComponent.build,request:{}", JSON.toJSONString(request), throwable);
            return null;
        }
    }

}
