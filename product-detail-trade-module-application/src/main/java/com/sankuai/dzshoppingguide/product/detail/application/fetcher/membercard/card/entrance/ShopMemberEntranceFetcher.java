package com.sankuai.dzshoppingguide.product.detail.application.fetcher.membercard.card.entrance;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.shopid.ShopIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.shopid.ShopIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
import com.sankuai.dzshoppingguide.product.detail.application.utils.ClientTypeUtils;
import com.sankuai.mpmctmember.query.common.enums.PlatformClientApplicationEnum;
import com.sankuai.mpmctmember.query.common.enums.PlatformClientOSEnum;
import com.sankuai.mpmctmember.query.thrift.dto.GetShopMemberEntranceReqDTO;
import com.sankuai.mpmctmember.query.thrift.dto.UserClientEnvDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Optional;
import java.util.concurrent.CompletableFuture;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/3/13 16:31
 */
@Fetcher(previousLayerDependencies = {ShopIdMapperFetcher.class})
@Slf4j
public class ShopMemberEntranceFetcher extends NormalFetcherContext<MemberEntranceResult> {
    @Autowired
    private CompositeAtomService compositeAtomService;

    private static long dpShopId;
    private static long mtShopId;

    @Override
    protected CompletableFuture<MemberEntranceResult> doFetch() {
        ShopIdMapper shopIdMapper = getDependencyResult(ShopIdMapperFetcher.class);
        dpShopId = Optional.ofNullable(shopIdMapper).map(ShopIdMapper::getDpBestShopId).orElse(0L);
        mtShopId = Optional.ofNullable(shopIdMapper).map(ShopIdMapper::getMtBestShopId).orElse(0L);
        return compositeAtomService.getShopMemberEntrance(buildRequest()).thenApply(res -> new MemberEntranceResult(res != null && res.getHasEntrance()));
    }
    
    private GetShopMemberEntranceReqDTO buildRequest() {
        GetShopMemberEntranceReqDTO reqDTO = new GetShopMemberEntranceReqDTO();
        reqDTO.setDpShopId(dpShopId);
        reqDTO.setMtShopId(mtShopId);
        UserClientEnvDTO envDTO = new UserClientEnvDTO();
        envDTO.setPlatformClient(ClientTypeUtils.genPlatform(request.getClientTypeEnum()).getPlatform());
        envDTO.setPlatformClientApplication(PlatformClientApplicationEnum.NATIVE.getCode());
        envDTO.setPlatformClientOS(ClientTypeUtils.isAndroid(request.getMobileOSType().getCode())
                ? PlatformClientOSEnum.ANDROID.getCode() : PlatformClientOSEnum.IOS.getCode());
        reqDTO.setUserClientEnv(envDTO);
        return reqDTO;
    }
}
