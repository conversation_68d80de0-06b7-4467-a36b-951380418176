// package com.sankuai.dzshoppingguide.product.detail.application.fetcher.price;
//
// import com.dianping.deal.detail.enums.PlatformEnum;
// import com.dianping.product.shelf.common.dto.activity.ActivityDetailDTO;
// import com.dianping.product.shelf.common.request.activities.ActivityProductQueryRequest;
// import com.google.common.collect.Lists;
// import com.sankuai.clr.content.process.gateway.thrift.dto.leads.LoadLeadsInfoReqDTO;
// import com.sankuai.clr.content.process.gateway.thrift.dto.leads.LoadLeadsInfoRespDTO;
// import com.sankuai.dealuser.price.display.api.enums.ProductTypeEnum;
// import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
// import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
// import com.sankuai.dzshoppingguide.product.detail.application.fetcher.LeadsInfoFetcher.LeadsInfoFetcher;
// import com.sankuai.dzshoppingguide.product.detail.application.fetcher.LeadsInfoFetcher.LeadsInfoResult;
// import com.sankuai.dzshoppingguide.product.detail.application.fetcher.cepinpool.CostEffectivePinPoolFetcher;
// import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.dto.BatchPriceReturnValue;
// import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
// import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfoFetcher;
// import com.sankuai.dzshoppingguide.product.detail.application.service.promo.PromoPriceService;
// import com.sankuai.dzshoppingguide.product.detail.application.utils.DealUtils;
// import lombok.extern.slf4j.Slf4j;
// import org.apache.thrift.TException;
// import org.springframework.beans.factory.annotation.Autowired;
//
// import java.util.Objects;
// import java.util.concurrent.CompletableFuture;
//
// /**
//  * <AUTHOR>
//  * @date 2025/6/14 11:58
//  */
// @Fetcher(previousLayerDependencies = {
//         ProductBaseInfoFetcher.class,
//         LeadsInfoFetcher.class,
//         ProductNormalPriceFetcher.class,
//         ProductPromoPriceFetcher.class,
//         ProductCostEffectivePriceFetcher.class,
//         CostEffectivePinPoolFetcher.class
// })
// @Slf4j
// public class BatchFinalPriceFetcher extends NormalFetcherContext<BatchPriceReturnValue> {
//
//     @Autowired
//     private PromoPriceService promoPriceService;
//
//     @Override
//     protected CompletableFuture<BatchPriceReturnValue> doFetch() throws Exception {
//
//         leadsInfoResult = getDependencyResult(LeadsInfoFetcher.class);
//         normalPrice = getDependencyResult(ProductNormalPriceFetcher.class);
//         costEffectivePrice = getDependencyResult(ProductCostEffectivePriceFetcher.class);
//         dealPromoPrice = getDependencyResult(ProductPromoPriceFetcher.class);
//         costEffectivePinTuan = getDependencyResult(CostEffectivePinPoolFetcher.class);
//
//         return null;
//     }
//
//     // leadsInfoResult
//     protected CompletableFuture<LeadsInfoResult> doFetch() throws TException {
//         ProductBaseInfo dealGroupInfo = getDependencyResult(ProductBaseInfoFetcher.class);
//         if (Objects.nonNull(dealGroupInfo) && DealUtils.isLeadsDeal(dealGroupInfo)) {
//             CompletableFuture<LoadLeadsInfoRespDTO> loadLeadsInfoCf = compositeAtomService.loadLeadsInfo(buildLoadLeadsInfoReq());
//             CompletableFuture<ActivityDetailDTO> activityDetailCf = compositeAtomService.querySpecialValueDeal(buildActivityProductQueryReq());
//             return CompletableFuture.allOf(loadLeadsInfoCf, activityDetailCf).thenApply(a -> new LeadsInfoResult(activityDetailCf.join(), loadLeadsInfoCf.join()));
//
//         }
//         return CompletableFuture.completedFuture(new LeadsInfoResult());
//     }
//
//     public LoadLeadsInfoReqDTO buildLoadLeadsInfoReq(){
//         LoadLeadsInfoReqDTO reqDTO = new LoadLeadsInfoReqDTO();
//         reqDTO.setPoiId(request.getPoiId());
//         reqDTO.setPlatform(request.getClientTypeEnum().isMtClientType() ? PlatformEnum.MEI_TUAN.getType() : PlatformEnum.DIAN_PING.getType());
//         reqDTO.setMerchantType(1);
//         reqDTO.setEntranceCode(20);
//         long dealGroupId = request.getProductId();
//         if ( request.getClientTypeEnum().isMtClientType()) {
//             reqDTO.setMtDealGroupId(dealGroupId);
//         } else {
//             reqDTO.setDpDealGroupId(dealGroupId);
//         }
//         return reqDTO;
//     }
//
//     public ActivityProductQueryRequest buildActivityProductQueryReq(){
//         ActivityProductQueryRequest req = new ActivityProductQueryRequest();
//         req.setMt(request.getClientTypeEnum().isMtClientType());
//         req.setShopId(request.getPoiId());
//         req.setProductIds(Lists.newArrayList(Math.toIntExact(request.getProductId())));
//         req.setProductType(ProductTypeEnum.DEAL.getType()); // 团单类型为1
//         return req;
//     }
// }
