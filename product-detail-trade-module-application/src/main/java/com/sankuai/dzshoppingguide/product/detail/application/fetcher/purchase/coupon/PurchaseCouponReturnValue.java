package com.sankuai.dzshoppingguide.product.detail.application.fetcher.purchase.coupon;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @Author: guangyujie
 * @Date: 2025/3/18 15:18
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PurchaseCouponReturnValue extends FetcherReturnValueDTO {

    /**
     * 【提单页领券购】是否有政府消费券
     */
    private final boolean isGovernmentConsumeCouponPromo;

    /**
     * 【提单页领券购】到综消费券二期支持美团微信小程序
     */
    private final boolean isCouponPurchaseCouponPromo;

    /**
     * 提单页领券购，提单页跳链透传字段
     */
    private final List<Map<String, Object>> couponParamForOrderPage;

    /**
     * 团详页领券购，在跳转提单页之前调用营销接口发券
     */
    private final Set<Long> couponGroupIdsForPurchase;

    /**
     * 是否有领券购（团详+提单）
     */
    public boolean hasCouponForPurchase() {
        return isGovernmentConsumeCouponPromo
                || isCouponPurchaseCouponPromo
                || CollectionUtils.isNotEmpty(couponGroupIdsForPurchase);
    }

    public PurchaseCouponReturnValue(final boolean isGovernmentConsumeCouponPromo,
                                     final boolean isCouponPurchaseCouponPromo,
                                     final List<Map<String, Object>> couponParamForOrderPage,
                                     final Set<Long> couponGroupIdsForPurchase) {
        this.isGovernmentConsumeCouponPromo = isGovernmentConsumeCouponPromo;
        this.isCouponPurchaseCouponPromo = isCouponPurchaseCouponPromo;
        this.couponParamForOrderPage = couponParamForOrderPage;
        this.couponGroupIdsForPurchase = couponGroupIdsForPurchase;
    }
}
