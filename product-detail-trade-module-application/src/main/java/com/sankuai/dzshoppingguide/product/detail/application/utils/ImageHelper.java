package com.sankuai.dzshoppingguide.product.detail.application.utils;

import com.dianping.cat.Cat;
import com.dianping.piccentercloud.display.api.PictureUrlGenerator;
import com.dianping.piccentercloud.display.api.PictureVisitParams;
import com.dianping.piccentercloud.display.api.enums.PictureVisitPattern;
import com.dianping.piccentercloud.display.api.enums.WaterMark;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class ImageHelper {

    private static final Logger logger = LoggerFactory.getLogger(ImageHelper.class);

    private static final String MT_BIZ = "mc";
    private static final String DP_BIZ = "pc";

    public static String mediumSize(String key, boolean isMt) {
        return format(key, ImageSize.MEDIUM.width, ImageSize.MEDIUM.height, isMt);
    }

    public static String legacy320Size(String key, boolean isMt) {
        return format(key, ImageSize.LEGACY320.width, ImageSize.MEDIUM.height, isMt);
    }

    public static String originalSize(String key, boolean isMt) {
        return format(key, ImageSize.ORIGINAL.width, ImageSize.ORIGINAL.height, isMt);
    }

    public static String format(String key, int width, int height, boolean isMt) {
        if (StringUtils.isEmpty(key)) {
            return key;
        }
        WaterMark waterMark = isMt ? WaterMark.MEITUAN : WaterMark.DIANPING;
        String biz = isMt ? MT_BIZ : DP_BIZ;
        PictureVisitParams params = new PictureVisitParams(biz, key, 1, 1, width, height, waterMark);
        PictureUrlGenerator pictureUrlGenerator = new PictureUrlGenerator(params, PictureVisitPattern.COMPATIBLE);
        try {
            return pictureUrlGenerator.getPictureURLWithHTTPSProtocol();
        } catch (Exception e) {
            logger.debug("NewImageUrlFormatter.getPicUrl error, key = {}, error = ", key, e);
        }
        return null;
    }

    public static String formatWithoutWatermark(String key, int width, int height, boolean isMt) {
        if (StringUtils.isEmpty(key)) {
            return key;
        }
        String biz = isMt ? MT_BIZ : DP_BIZ;
        PictureVisitParams params = new PictureVisitParams(biz, key, 1, 1, width, height, WaterMark.EMPTY);
        PictureUrlGenerator pictureUrlGenerator = new PictureUrlGenerator(params, PictureVisitPattern.COMPATIBLE);
        try {
            return pictureUrlGenerator.getPictureURLWithHTTPSProtocol();
        } catch (Exception e) {
            logger.debug("NewImageUrlFormatter.getPicUrl error, key = {}, error = ", key, e);
        }
        return null;
    }

    public static String format(String key, int width, int height) {
        if (StringUtils.isEmpty(key)) {
            return StringUtils.EMPTY;
        }
        PictureVisitParams params = new PictureVisitParams("pc", key, 1, 1, width, height, WaterMark.DIANPING);
        PictureUrlGenerator pictureUrlGenerator = new PictureUrlGenerator(params, PictureVisitPattern.COMPATIBLE);
        try {
            return pictureUrlGenerator.getPictureURLWithHTTPSProtocol();
        } catch (Exception e1) {
            logger.error("pictureUrlGenerator.getPictureURLWithHTTPSProtocol error", e1);
        }
        try {
            return pictureUrlGenerator.getFullPictureURL();
        } catch (Exception e2) {
            logger.error("pictureUrlGenerator.getFullPictureURL error", e2);
            return key;
        }
    }
    
    public static int getOriginalImageWidth() {
        return ImageSize.ORIGINAL.width;
    }
    
    public static int getOriginalImageHeight() {
        return ImageSize.ORIGINAL.height;
    }

    private enum ImageSize {
        LARGE(220, 164),
        MEDIUM(180, 132),
        SMALL(120, 90),
        ORIGINAL(960, 540),
        LEGACY(160, 100),
        LEGACY320(320, 200);

        public int width;
        public int height;

        private ImageSize(int width, int height) {
            this.width = width;
            this.height = height;
        }
    }
}
