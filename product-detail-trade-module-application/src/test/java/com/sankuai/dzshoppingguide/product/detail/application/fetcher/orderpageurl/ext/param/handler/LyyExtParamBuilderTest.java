package com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.ext.param.handler;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;
import com.sankuai.dz.product.detail.RequestCustomParamEnum;
import com.sankuai.dz.product.detail.gateway.spi.dto.CustomParam;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.enums.OrderPageExtParamEnums;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.ext.param.request.ExtParamBuilderRequest;
import java.io.UnsupportedEncodingException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
@DisplayName("LyyExtParamBuilder Tests")
class LyyExtParamBuilderTest {

    private final LyyExtParamBuilder builder = new LyyExtParamBuilder();

    @Mock
    private ProductDetailPageRequest request;

    @Mock
    private ExtParamBuilderRequest builderRequest;

    @InjectMocks
    private LyyExtParamBuilder lyyExtParamBuilder;

    /**
     * 测试当 customParam 为 null 时返回 null
     */
    @Test
    public void testDoBuildExtParamWhenCustomParamIsNull() throws UnsupportedEncodingException {
        // arrange
        when(request.getCustomParam(RequestCustomParamEnum.lyyuserid)).thenReturn(null);
        // act
        String result = builder.doBuildExtParam(request, builderRequest);
        // assert
        assertNull(result);
    }

    /**
     * 测试当 lyyuserid 为 null 时返回 null
     */
    @Test
    public void testDoBuildExtParamWhenLyyUserIdIsNull() throws UnsupportedEncodingException {
        // arrange
        CustomParam customParam = new CustomParam();
        when(request.getCustomParam(RequestCustomParamEnum.lyyuserid)).thenReturn(null);
        // act
        String result = builder.doBuildExtParam(request, builderRequest);
        // assert
        assertNull(result);
    }

    /**
     * 测试当 lyyuserid 为空字符串时返回 null
     */
    @Test
    public void testDoBuildExtParamWhenLyyUserIdIsEmpty() throws UnsupportedEncodingException {
        // arrange
        when(request.getCustomParam(RequestCustomParamEnum.lyyuserid)).thenReturn("");
        // act
        String result = builder.doBuildExtParam(request, builderRequest);
        // assert
        assertNull(result);
    }

    /**
     * 测试当 lyyuserid 有值时返回该值
     */
    @Test
    public void testDoBuildExtParamWhenLyyUserIdHasValue() throws UnsupportedEncodingException {
        // arrange
        String expectedUserId = "test123";
        when(request.getCustomParam(RequestCustomParamEnum.lyyuserid)).thenReturn(expectedUserId);
        // act
        String result = builder.doBuildExtParam(request, builderRequest);
        // assert
        assertEquals(expectedUserId, result);
    }

    /**
     * Test case for buildKey method
     * Scenario: Verify that buildKey returns the correct OrderPageExtParamEnums value
     * Expected: Should return OrderPageExtParamEnums.lyyuserid
     */
    @Test
    @DisplayName("buildKey should return lyyuserid enum value")
    public void testBuildKey_ShouldReturnLyyUserIdEnum() {
        // arrange
        // No specific arrangement needed as method returns constant value
        // act
        OrderPageExtParamEnums result = lyyExtParamBuilder.buildKey();
        // assert
        assertNotNull(result, "Result should not be null");
        assertEquals(OrderPageExtParamEnums.lyyuserid, result, "buildKey should return OrderPageExtParamEnums.lyyuserid");
        assertEquals("", result.getDesc(), "The description of lyyuserid enum should be empty string");
    }
}
