package com.sankuai.dzshoppingguide.product.detail.application.fetcher.cepinpool.dto;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2024/4/23
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CostEffectivePinTuan extends FetcherReturnValueDTO {

    /**
     * 是否为强化样式 前提是有拼团优惠
     */
    public boolean enhancedStyle() {
        return sceneType == 56;
    }

    /**
     * 成团还需要的人数
     */
    public int getNeedMemberCount() {
        if (helpSuccCountMin < hasHelpCount) {
            return 0;
        }
        return helpSuccCountMin - hasHelpCount;
    }

    /**
     * 拼团中
     */
    public boolean inPinTuan() {
        return isPinTuanOpened() && inPinTuan;
    }

    /**
     * 是否拼团场景 = 是否含有拼团优惠
     * 如果用户是新客，但是进入一个仅新客的特团拼团，报价不会返回这个优惠，估判断为不是特团拼团场景，展示成普通团购样式
     */
    private boolean cePinTuanScene;

    /**
     * 是否拼团中，true-已开团，false-未开团
     */
    private boolean pinTuanOpened;

    /**
     * 是否主态拼团，true-主态，false-客态
     */
    private boolean activePinTuan;

    /**
     * 已拼&待拼，true-已拼，false-待拼
     */
    private boolean inPinTuan;

    /**
     * 特团来源场景
     */
    private int sceneType;

    /**
     * 拼团ID
     */
    private String pinTuanActivityId;

    /**
     * 分享token， 同orderGroupId
     */
    private String shareToken;

    /**
     * 拼团总人数
     */
    private int groupSuccCountMin;

    /**
     * 拼团成功人数
     */
    private int hasHelpCount;

    /**
     * 参团用户头像
     */
    private List<String> avatars;

    /**
     * 拼团完成时间，unix时间戳
     */
    private long expireTime;

    /**
     * 拼团规则浮层文案
     */
    private List<RuleInfoPO> ruleInfoPOS;

    /**
     * 是否限制新客
     */
    private boolean limitNewCustomJoin;

    /**
     * 促销ID
     */
    private long promotionId;

    /**
     * 拼团参数配置
     */
    private CePinTuanPassParamConfig pinTuanPassParamConfig;

    /**
     * 拼团人数（该变量+1则为总拼团人数）
     */
    private int helpSuccCountMin;
}
