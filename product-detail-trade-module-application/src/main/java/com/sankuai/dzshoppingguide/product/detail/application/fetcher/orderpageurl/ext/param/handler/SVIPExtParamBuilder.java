package com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.ext.param.handler;

import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.enums.OrderPageExtParamEnums;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.ext.param.BaseMultiExtParamBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.ext.param.request.ExtParamBuilderRequest;
import com.sankuai.dzshoppingguide.product.detail.application.utils.magic.MagicFlagUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: guangyujie
 * @Date: 2025/3/12 17:29
 */
@Component
public class SVIPExtParamBuilder extends BaseMultiExtParamBuilder {

    @Override
    protected Map<OrderPageExtParamEnums, String> doMultiBuildExtParam(final ProductDetailPageRequest request,
                                                                       final ExtParamBuilderRequest builderRequest) throws Exception {
        Map<OrderPageExtParamEnums, String> map = new HashMap<>();
        map.put(OrderPageExtParamEnums.mmcinflate, MagicFlagUtils.getMmcInflate(request));
        map.put(OrderPageExtParamEnums.mmcuse, MagicFlagUtils.getMmcUse(request));
        map.put(OrderPageExtParamEnums.mmcbuy, MagicFlagUtils.getMmcBuy(request));
        map.put(OrderPageExtParamEnums.mmcfree, MagicFlagUtils.getMmcFree(request));
        return map;
    }

}
