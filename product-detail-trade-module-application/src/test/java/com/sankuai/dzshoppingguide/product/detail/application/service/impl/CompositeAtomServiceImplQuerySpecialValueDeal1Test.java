package com.sankuai.dzshoppingguide.product.detail.application.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.dianping.pigeon.remoting.invoker.concurrent.InvocationCallback;
import com.dianping.pigeon.remoting.invoker.util.InvokerHelper;
import com.dianping.product.shelf.common.dto.Response;
import com.dianping.product.shelf.common.dto.activity.ActivityDetailDTO;
import com.dianping.product.shelf.common.request.activities.ActivityProductQueryRequest;
import com.dianping.product.shelf.query.api.ActivityShelfQueryService;
import com.sankuai.dzshoppingguide.product.detail.domain.utils.PigeonCallbackUtils;
import java.util.Arrays;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.stubbing.Answer;

@ExtendWith(MockitoExtension.class)
class CompositeAtomServiceImplQuerySpecialValueDeal1Test {

    @Mock
    private ActivityShelfQueryService activityShelfQueryServiceFuture;

    @InjectMocks
    private CompositeAtomServiceImpl compositeAtomService;

    private ActivityProductQueryRequest request;

    @BeforeEach
    void setUp() {
        request = new ActivityProductQueryRequest();
        request.setProductIds(Arrays.asList(1, 2, 3));
        request.setShopId(123L);
        request.setProductType(1);
    }

    /**
     * 测试服务调用成功且返回有效结果的情况
     */
    @Test
    void testQuerySpecialValueDeal_SuccessWithValidResult() throws Throwable {
        // arrange
        ActivityDetailDTO expectedDetail = new ActivityDetailDTO();
        expectedDetail.setActivityId(1001);
        expectedDetail.setName("Test Activity");
        Response<ActivityDetailDTO> mockResponse = new Response<>();
        mockResponse.setSuccess(true);
        mockResponse.setContent(expectedDetail);
        CompletableFuture<ActivityDetailDTO> resultFuture = new CompletableFuture<>();
        doAnswer((InvocationOnMock invocation) -> {
            InvocationCallback callback = (InvocationCallback) InvokerHelper.getCallback();
            callback.onSuccess(mockResponse);
            return null;
        }).when(activityShelfQueryServiceFuture).queryOnlineActivity(any());
        // act
        resultFuture = compositeAtomService.querySpecialValueDeal(request);
        // assert
        ActivityDetailDTO result = resultFuture.join();
        assertNotNull(result, "Result should not be null when service returns success");
        assertEquals(1001, result.getActivityId(), "Activity ID should match");
        assertEquals("Test Activity", result.getName(), "Activity name should match");
    }

    /**
     * 测试服务调用成功但返回失败结果的情况
     */
    @Test
    void testQuerySpecialValueDeal_SuccessButFailedResponse() throws Throwable {
        // arrange
        Response<ActivityDetailDTO> mockResponse = new Response<>();
        mockResponse.setSuccess(false);
        mockResponse.setMsg("Activity not found");
        CompletableFuture<ActivityDetailDTO> resultFuture = new CompletableFuture<>();
        doAnswer((InvocationOnMock invocation) -> {
            InvocationCallback callback = (InvocationCallback) InvokerHelper.getCallback();
            callback.onSuccess(mockResponse);
            return null;
        }).when(activityShelfQueryServiceFuture).queryOnlineActivity(any());
        // act
        resultFuture = compositeAtomService.querySpecialValueDeal(request);
        // assert
        ActivityDetailDTO result = resultFuture.join();
        assertNull(result, "Result should be null when service returns failed response");
    }

    /**
     * 测试服务调用抛出异常的情况
     */
    @Test
    void testQuerySpecialValueDeal_ServiceThrowsException() throws Throwable {
        // arrange
        RuntimeException mockException = new RuntimeException("Service unavailable");
        CompletableFuture<ActivityDetailDTO> resultFuture = new CompletableFuture<>();
        doAnswer((InvocationOnMock invocation) -> {
            InvocationCallback callback = (InvocationCallback) InvokerHelper.getCallback();
            callback.onFailure(mockException);
            return null;
        }).when(activityShelfQueryServiceFuture).queryOnlineActivity(any());
        // act
        resultFuture = compositeAtomService.querySpecialValueDeal(request);
        // assert
        ActivityDetailDTO result = resultFuture.join();
        assertNull(result, "Result should be null when service throws exception");
    }

    /**
     * 测试服务调用返回null结果的情况
     */
    @Test
    void testQuerySpecialValueDeal_ServiceReturnsNull() throws Throwable {
        // arrange
        CompletableFuture<ActivityDetailDTO> resultFuture = new CompletableFuture<>();
        doAnswer((InvocationOnMock invocation) -> {
            InvocationCallback callback = (InvocationCallback) InvokerHelper.getCallback();
            callback.onSuccess(null);
            return null;
        }).when(activityShelfQueryServiceFuture).queryOnlineActivity(any());
        // act
        resultFuture = compositeAtomService.querySpecialValueDeal(request);
        // assert
        ActivityDetailDTO result = resultFuture.join();
        assertNull(result, "Result should be null when service returns null");
    }

    /**
     * 测试服务调用返回成功但内容为null的情况
     */
    @Test
    void testQuerySpecialValueDeal_SuccessButNullContent() throws Throwable {
        // arrange
        Response<ActivityDetailDTO> mockResponse = new Response<>();
        mockResponse.setSuccess(true);
        mockResponse.setContent(null);
        CompletableFuture<ActivityDetailDTO> resultFuture = new CompletableFuture<>();
        doAnswer((InvocationOnMock invocation) -> {
            InvocationCallback callback = (InvocationCallback) InvokerHelper.getCallback();
            callback.onSuccess(mockResponse);
            return null;
        }).when(activityShelfQueryServiceFuture).queryOnlineActivity(any());
        // act
        resultFuture = compositeAtomService.querySpecialValueDeal(request);
        // assert
        ActivityDetailDTO result = resultFuture.join();
        assertNull(result, "Result should be null when content is null");
    }
}
