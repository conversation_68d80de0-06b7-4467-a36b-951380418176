package com.sankuai.dzshoppingguide.product.detail.application.utils;

import com.google.common.base.Charsets;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.InetAddress;
import java.net.URLEncoder;

public class NetUtils {

    protected static final Logger logger = LoggerFactory.getLogger(NetUtils.class);

    public static String getIP() {
        try {
            return InetAddress.getLocalHost().getHostAddress().toString();
        } catch (Exception e) {
            logger.error("can't not get ip!", e);
        }
        return Strings.EMPTY;
    }

    public static String encode(String input) {
        if (StringUtils.isBlank(input))
            return input;
        try {
            return URLEncoder.encode(input, Charsets.UTF_8.name());
        } catch (Exception e) {
            logger.error("netUtils encode has error", e);
        }
        return null;
    }
}
