package com.sankuai.dzshoppingguide.product.detail.application.fetcher.sku;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.response.FetcherResponse;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
import com.sankuai.dztheme.deal.req.SkuOptionBatchRequest;
import com.sankuai.dztheme.deal.res.DealSkuSummaryDTO;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class DealSkuSummaryFetcherTest {

    @Mock
    private CompositeAtomService compositeAtomService;

    @Mock
    private ProductDetailPageRequest request;

    @InjectMocks
    private DealSkuSummaryFetcher dealSkuSummaryFetcher;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    /**
     * 测试正常流程 - 返回结果包含当前productId
     */
    @Test
    void testDoFetchWithResultContainsProductId() throws Throwable {
        // arrange
        long productId = 123L;
        DealSkuSummaryDTO expectedDto = new DealSkuSummaryDTO();
        Map<Long, DealSkuSummaryDTO> resultMap = new HashMap<>();
        resultMap.put(productId, expectedDto);
        when(request.getProductId()).thenReturn(productId);
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.MT_APP);
        when(compositeAtomService.batchQuerySummary(any(SkuOptionBatchRequest.class))).thenReturn(CompletableFuture.completedFuture(resultMap));
        // act
        CompletableFuture<DealSkuSummaryReturnValue> future = dealSkuSummaryFetcher.doFetch();
        // assert
        assertNotNull(future);
        DealSkuSummaryReturnValue result = future.get();
        assertNotNull(result);
        assertEquals(expectedDto, result.getDealSkuSummaryDTO());
    }

    /**
     * 测试正常流程 - 返回结果不包含当前productId
     */
    @Test
    void testDoFetchWithResultNotContainsProductId() throws Throwable {
        // arrange
        long productId = 123L;
        Map<Long, DealSkuSummaryDTO> resultMap = new HashMap<>();
        resultMap.put(456L, new DealSkuSummaryDTO());
        when(request.getProductId()).thenReturn(productId);
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.DP_APP);
        when(compositeAtomService.batchQuerySummary(any(SkuOptionBatchRequest.class))).thenReturn(CompletableFuture.completedFuture(resultMap));
        // act
        CompletableFuture<DealSkuSummaryReturnValue> future = dealSkuSummaryFetcher.doFetch();
        // assert
        assertNotNull(future);
        DealSkuSummaryReturnValue result = future.get();
        assertNotNull(result);
        assertNull(result.getDealSkuSummaryDTO());
    }

    /**
     * 测试正常流程 - 返回结果为空
     */
    @Test
    void testDoFetchWithEmptyResult() throws Throwable {
        // arrange
        long productId = 123L;
        when(request.getProductId()).thenReturn(productId);
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.MT_APP);
        when(compositeAtomService.batchQuerySummary(any(SkuOptionBatchRequest.class))).thenReturn(CompletableFuture.completedFuture(null));
        // act
        CompletableFuture<DealSkuSummaryReturnValue> future = dealSkuSummaryFetcher.doFetch();
        // assert
        assertNotNull(future);
        DealSkuSummaryReturnValue result = future.get();
        assertNotNull(result);
        assertNull(result.getDealSkuSummaryDTO());
    }

    /**
     * 测试异常流程 - compositeAtomService抛出异常
     */
    @Test
    void testDoFetchWithException() throws Throwable {
        // arrange
        long productId = 123L;
        when(request.getProductId()).thenReturn(productId);
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.DP_APP);
        when(compositeAtomService.batchQuerySummary(any(SkuOptionBatchRequest.class))).thenThrow(new RuntimeException("Test exception"));
        // act
        CompletableFuture<DealSkuSummaryReturnValue> future = dealSkuSummaryFetcher.doFetch();
        // assert
        assertNotNull(future);
        DealSkuSummaryReturnValue result = future.get();
        assertNull(result);
    }
}
