package com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.pricerange;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import com.sankuai.tpfun.skuoperationapi.price.dto.response.BatchPriceRangeInfoResponse;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @create 2025/5/28 15:31
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PriceRangeInfoReturnValue extends FetcherReturnValueDTO {
    private BatchPriceRangeInfoResponse batchPriceRangeInfoResponse;
}
