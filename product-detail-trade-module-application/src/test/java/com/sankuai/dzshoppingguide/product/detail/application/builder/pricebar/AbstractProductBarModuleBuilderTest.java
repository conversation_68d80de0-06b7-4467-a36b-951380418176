package com.sankuai.dzshoppingguide.product.detail.application.builder.pricebar;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.dianping.deal.sales.common.datatype.SalesDisplayInfoDTO;
import com.sankuai.dzshoppingguide.product.detail.application.builder.pricebar.builder.AbstractProductBarModuleBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.sale.ProductSaleReturnValue;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DetailSaleVO;
import com.sankuai.dzshoppingguide.product.detail.spi.pricebar.vo.ProductPriceBarModuleVO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class AbstractProductBarModuleBuilderTest {

    private final AbstractProductBarModuleBuilder<?> builder = new AbstractProductBarModuleBuilder<ProductPriceBarModuleVO>() {

        @Override
        public ProductPriceBarModuleVO doBuild() {
            return null;
        }
    };

    /**
     * Test case when ProductSaleReturnValue has null saleDisplayDTO
     */
    @Test
    public void testBuildSaleInfo_WhenSaleDisplayDTONull() throws Throwable {
        // arrange
        ProductSaleReturnValue productSaleReturnValue = new ProductSaleReturnValue();
        productSaleReturnValue.setSaleDisplayDTO(null);
        // act
        DetailSaleVO result = builder.buildSaleInfo(productSaleReturnValue);
        // assert
        assertNotNull(result);
        assertNull(result.getSaleTag());
    }

    /**
     * Test case when SalesDisplayInfoDTO has null salesTag
     */
    @Test
    public void testBuildSaleInfo_WhenSalesTagNull() throws Throwable {
        // arrange
        ProductSaleReturnValue productSaleReturnValue = new ProductSaleReturnValue();
        SalesDisplayInfoDTO salesDisplayInfoDTO = new SalesDisplayInfoDTO();
        salesDisplayInfoDTO.setSalesTag(null);
        productSaleReturnValue.setSaleDisplayDTO(salesDisplayInfoDTO);
        // act
        DetailSaleVO result = builder.buildSaleInfo(productSaleReturnValue);
        // assert
        assertNotNull(result);
        assertNull(result.getSaleTag());
    }

    /**
     * Test case when SalesDisplayInfoDTO has valid salesTag
     */
    @Test
    public void testBuildSaleInfo_WhenSalesTagValid() throws Throwable {
        // arrange
        String expectedSaleTag = "Test Sale Tag";
        ProductSaleReturnValue productSaleReturnValue = new ProductSaleReturnValue();
        SalesDisplayInfoDTO salesDisplayInfoDTO = new SalesDisplayInfoDTO();
        salesDisplayInfoDTO.setSalesTag(expectedSaleTag);
        productSaleReturnValue.setSaleDisplayDTO(salesDisplayInfoDTO);
        // act
        DetailSaleVO result = builder.buildSaleInfo(productSaleReturnValue);
        // assert
        assertNotNull(result);
        assertEquals(expectedSaleTag, result.getSaleTag());
    }

    /**
     * Test case when ProductSaleReturnValue is not null but empty
     */
    @Test
    public void testBuildSaleInfo_WhenEmptyProductSaleReturnValue() throws Throwable {
        // arrange
        ProductSaleReturnValue productSaleReturnValue = new ProductSaleReturnValue();
        // act
        DetailSaleVO result = builder.buildSaleInfo(productSaleReturnValue);
        // assert
        assertNotNull(result);
        assertNull(result.getSaleTag());
    }
}
