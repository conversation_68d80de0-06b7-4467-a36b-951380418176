package com.sankuai.dzshoppingguide.product.detail.application.builder.pricebar.builder;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dzshoppingguide.product.detail.spi.pricebar.vo.ProductPriceBarModuleVO;
import java.math.BigDecimal;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * Test cases for AbstractProductBarModuleBuilder.calcDiscount method
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("AbstractProductBarModuleBuilder calcDiscount Test")
public class AbstractProductBarModuleBuilderCalcDiscountTest {

    private final TestableAbstractProductBarModuleBuilder builder = new TestableAbstractProductBarModuleBuilder();

    private static class TestableAbstractProductBarModuleBuilder extends AbstractProductBarModuleBuilder<ProductPriceBarModuleVO> {

        @Override
        public ProductPriceBarModuleVO doBuild() {
            return null;
        }
    }

    /**
     * Test scenario: Calculate discount when both inputs are null
     * Expected: Should return null
     */
    @Test
    @DisplayName("Should return null when both inputs are null")
    public void testCalcDiscount_BothInputsNull() {
        // arrange
        BigDecimal finalPrice = null;
        BigDecimal marketPrice = null;
        // act
        BigDecimal result = builder.calcDiscount(finalPrice, marketPrice);
        // assert
        assertNull(result, "Discount calculation should return null when both inputs are null");
    }

    /**
     * Test scenario: Calculate discount when market price is zero
     * Expected: Should return null
     */
    @Test
    @DisplayName("Should return null when market price is zero")
    public void testCalcDiscount_ZeroMarketPrice() {
        // arrange
        BigDecimal finalPrice = new BigDecimal("50.00");
        BigDecimal marketPrice = BigDecimal.ZERO;
        // act
        BigDecimal result = builder.calcDiscount(finalPrice, marketPrice);
        // assert
        assertNull(result, "Discount calculation should return null when market price is zero");
    }

    /**
     * Test scenario: Calculate normal discount (50% off)
     * Expected: Should return 5.0 (representing 5折)
     */
    @Test
    @DisplayName("Should calculate correct discount for 50% off")
    public void testCalcDiscount_NormalHalfPrice() {
        // arrange
        BigDecimal finalPrice = new BigDecimal("50.00");
        BigDecimal marketPrice = new BigDecimal("100.00");
        // act
        BigDecimal result = builder.calcDiscount(finalPrice, marketPrice);
        // assert
        assertEquals(0, new BigDecimal("5.0").compareTo(result), "Discount should be 5.0 (5折) for 50% off");
    }

    /**
     * Test scenario: Calculate discount with rounding
     * Expected: Should round up to one decimal place
     */
    @Test
    @DisplayName("Should round up discount to one decimal place")
    public void testCalcDiscount_WithRounding() {
        // arrange
        BigDecimal finalPrice = new BigDecimal("66.66");
        BigDecimal marketPrice = new BigDecimal("100.00");
        // act
        BigDecimal result = builder.calcDiscount(finalPrice, marketPrice);
        // assert
        assertEquals(0, new BigDecimal("6.7").compareTo(result), "Discount should be rounded up to 6.7 折");
    }

    /**
     * Test scenario: Calculate discount for very small prices
     * Expected: Should handle small numbers correctly
     */
    @Test
    @DisplayName("Should handle very small price values correctly")
    public void testCalcDiscount_SmallPrices() {
        // arrange
        BigDecimal finalPrice = new BigDecimal("0.01");
        BigDecimal marketPrice = new BigDecimal("0.02");
        // act
        BigDecimal result = builder.calcDiscount(finalPrice, marketPrice);
        // assert
        assertEquals(0, new BigDecimal("5.0").compareTo(result), "Discount should be 5.0 折 for small prices");
    }

    /**
     * Test scenario: Calculate discount when final price is greater than market price
     * Expected: Should return correct value above 10
     */
    @Test
    @DisplayName("Should handle final price greater than market price")
    public void testCalcDiscount_FinalPriceGreaterThanMarket() {
        // arrange
        BigDecimal finalPrice = new BigDecimal("120.00");
        BigDecimal marketPrice = new BigDecimal("100.00");
        // act
        BigDecimal result = builder.calcDiscount(finalPrice, marketPrice);
        // assert
        assertEquals(0, new BigDecimal("12.0").compareTo(result), "Discount should be 12.0 when final price is greater than market price");
    }
}
