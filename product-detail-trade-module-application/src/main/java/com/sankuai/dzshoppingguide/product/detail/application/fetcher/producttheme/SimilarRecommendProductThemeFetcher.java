package com.sankuai.dzshoppingguide.product.detail.application.fetcher.producttheme;

import com.google.common.collect.Lists;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommendgoods.dtos.ProductM;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommendgoods.SimilarProductRecommendFetcher;
import com.sankuai.dztheme.deal.req.DealProductRequest;
import org.apache.commons.lang.StringUtils;
import java.util.*;

/**
 * <AUTHOR>
 * @create 2025/5/22 09:54
 */
@Fetcher(
        previousLayerDependencies = {
                SimilarProductRecommendFetcher.class
        }
)
public class SimilarRecommendProductThemeFetcher extends AbstractProductThemeFetcher {

    String mtShopThemePlanId = "10500360";
    String dpShopThemePlanId = "10500361";
    int directPromoScene = 400200;
    int shopCarNoSelectScene = 400800;

    @Override
    protected boolean enableNewService(){
        return false;
    }

    @Override
    protected DealProductRequest buildDealProductRequest(List<ProductM> products, Map<Long, Long> dealId2ShopId){
        List<Integer> dealIds = flexibleGettingDealIds(products);
        DealProductRequest dealProductRequest = new DealProductRequest();
        dealProductRequest.setPlanId("10002450");
        dealProductRequest.setProductIds(dealIds);
        Map<String, Object> extParams = buildQueryExtParams(dealIds, dealId2ShopId);
        extParams.put("dealId2NearestShopIdL", deal2ShopForLong(dealIds, dealId2ShopId));
        buildPriceCompareParam(dealIds, extParams, request.getClientTypeEnum().isMtClientType(), dealId2ShopId);
        dealProductRequest.setExtParams(extParams);
        return dealProductRequest;
    }

    private void buildPriceCompareParam(List<Integer> dealIds, Map<String, Object> extParams, boolean isMt, Map<Long, Long> dealId2ShopId) {
        extParams.put("shopThemePlanId", isMt ? mtShopThemePlanId : dpShopThemePlanId);
        extParams.put("dealId2ShopIdForLong", deal2ShopForLong(dealIds, dealId2ShopId));
        String pageSource = request.getPageSource();
        extParams.put("themeReqSource", 0);
        List<Integer> bpDealGroupTypes = Lists.newArrayList();
        bpDealGroupTypes.add(1);
        extParams.put("bpDealGroupTypes", bpDealGroupTypes);

        if(StringUtils.isNotBlank(pageSource)) {
            extParams.put("pageSource","dealGroupDetail");
            if(pageSource.equals("deal")) {
                extParams.put("directPromoSceneCode", directPromoScene);
            } else if(pageSource.equals("shopcarnoselect") || pageSource.equals("shopcarselect")) {
                extParams.put("directPromoSceneCode", shopCarNoSelectScene);
            }
        }
    }

}
