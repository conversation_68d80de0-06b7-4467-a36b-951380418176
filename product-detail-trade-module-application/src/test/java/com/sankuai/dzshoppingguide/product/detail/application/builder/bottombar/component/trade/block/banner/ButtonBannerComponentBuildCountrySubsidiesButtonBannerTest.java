package com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.component.trade.block.banner;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import com.dianping.lion.client.Lion;
import com.google.common.collect.Lists;
import com.sankuai.dzshoppingguide.product.detail.application.constants.Constants;
import com.sankuai.dzshoppingguide.product.detail.application.model.CountrySubsidiesConfig;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.banner.BottomBarTopBannerVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.banner.enums.BottomBarBannerTypeEnums;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.backgroud.BottomBarBackgroundVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.rich.content.PicRichContentVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.rich.content.RichContentVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.rich.content.TextRichContentVO;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class ButtonBannerComponentBuildCountrySubsidiesButtonBannerTest {

    private static final String TRADE_MODULE_APPKEY = "com.sankuai.travelcommon.trade";

    private static final String COUNTRY_SUBSIDIES_CONFIG = "country.subsidies.config";

    /**
     * 测试flowFlag不等于COUNTRY_SUBSIDIES时返回null
     */
    @Test
    void testBuildCountrySubsidiesButtonBanner_InvalidFlowFlag() throws Throwable {
        // arrange
        String flowFlag = "invalid_flag";
        int gpsCityId = 123;
        boolean isMt = true;
        // act
        BottomBarTopBannerVO result = ButtonBannerComponent.buildCountrySubsidiesButtonBanner(flowFlag, gpsCityId, isMt);
        // assert
        assertNull(result);
    }

    /**
     * 测试flowFlag为null时返回null
     */
    @Test
    void testBuildCountrySubsidiesButtonBanner_NullFlowFlag() throws Throwable {
        // arrange
        String flowFlag = null;
        int gpsCityId = 123;
        boolean isMt = true;
        // act
        BottomBarTopBannerVO result = ButtonBannerComponent.buildCountrySubsidiesButtonBanner(flowFlag, gpsCityId, isMt);
        // assert
        assertNull(result);
    }

    /**
     * 测试flowFlag为空字符串时返回null
     */
    @Test
    void testBuildCountrySubsidiesButtonBanner_EmptyFlowFlag() throws Throwable {
        // arrange
        String flowFlag = "";
        int gpsCityId = 123;
        boolean isMt = true;
        // act
        BottomBarTopBannerVO result = ButtonBannerComponent.buildCountrySubsidiesButtonBanner(flowFlag, gpsCityId, isMt);
        // assert
        assertNull(result);
    }
}
