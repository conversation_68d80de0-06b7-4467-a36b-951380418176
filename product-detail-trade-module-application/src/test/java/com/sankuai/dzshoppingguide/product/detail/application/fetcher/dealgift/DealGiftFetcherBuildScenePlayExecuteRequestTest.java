package com.sankuai.dzshoppingguide.product.detail.application.fetcher.dealgift;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.product.detail.gateway.spi.dto.ShepherdGatewayParam;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.enums.MobileOSTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.cityId.CityIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.dealgroup.DealGroupIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.mktplay.center.mkt.play.center.client.ActiveRequestUnit;
import com.sankuai.mktplay.center.mkt.play.center.client.ScenePlayExecuteRequest;
import com.sankuai.mktplay.center.mkt.play.center.client.UserInfo;
import com.sankuai.mktplay.center.mkt.play.center.client.UserSourceEnum;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class DealGiftFetcherBuildScenePlayExecuteRequestTest {

    @InjectMocks
    private DealGiftFetcher dealGiftFetcher;

    @Mock
    private ProductDetailPageRequest request;

    @Mock
    private ProductBaseInfo productBaseInfo;

    @Mock
    private DealGroupIdMapper dealGroupIdMapper;

    @Mock
    private ShopIdMapper shopIdMapper;

    @Mock
    private CityIdMapper cityIdMapper;

    @Mock
    private ShepherdGatewayParam shepherdGatewayParam;

    private Map<String, String> requestForExecutePlay;

    /**
     * Test normal case with all valid inputs
     */
    @Test
    public void testBuildScenePlayExecuteRequest_NormalCase() throws Throwable {
        // arrange
        long playId = 123L;
        long userId = 456L;
        long mtDealGroupId = 789L;
        long mtShopId = 101L;
        int mtCityId = 1;
        when(request.getUserId()).thenReturn(userId);
        when(request.getMobileOSType()).thenReturn(MobileOSTypeEnum.IOS);
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.MT_APP);
        when(request.getMtUserId()).thenReturn(userId);
        when(request.getShepherdGatewayParam()).thenReturn(shepherdGatewayParam);
        when(shepherdGatewayParam.getUnionid()).thenReturn("test-unionid");
        when(shepherdGatewayParam.getUserIp()).thenReturn("127.0.0.1");
        when(shepherdGatewayParam.getAppVersion()).thenReturn("1.0.0");
        when(dealGroupIdMapper.getMtDealGroupId()).thenReturn(mtDealGroupId);
        when(shopIdMapper.getMtBestShopId()).thenReturn(mtShopId);
        when(cityIdMapper.getMtCityId()).thenReturn(mtCityId);
        requestForExecutePlay = new HashMap<>();
        requestForExecutePlay.put("testKey", "testValue");
        // act
        ScenePlayExecuteRequest result = dealGiftFetcher.buildScenePlayExecuteRequest(productBaseInfo, dealGroupIdMapper, shopIdMapper, cityIdMapper, playId, requestForExecutePlay);
        // assert
        assertNotNull(result);
        assertEquals(playId, result.getPlayId());
        assertNotNull(result.getUserInfo());
        assertEquals(userId, result.getUserInfo().getUserId());
        assertNotNull(result.getRequest());
        assertNotNull(result.getActiveRequestUnit());
        List<ActiveRequestUnit> units = result.getActiveRequestUnit();
        assertEquals(1, units.size());
        assertEquals(mtDealGroupId, units.get(0).getRowId());
    }

    /**
     * Test case with null requestForExecutePlay map
     */
    @Test
    public void testBuildScenePlayExecuteRequest_NullRequestMap() throws Throwable {
        // arrange
        long playId = 123L;
        long userId = 456L;
        when(request.getUserId()).thenReturn(userId);
        when(request.getMobileOSType()).thenReturn(MobileOSTypeEnum.IOS);
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.MT_APP);
        when(request.getMtUserId()).thenReturn(userId);
        when(request.getShepherdGatewayParam()).thenReturn(shepherdGatewayParam);
        when(shepherdGatewayParam.getUnionid()).thenReturn("test-unionid");
        when(shepherdGatewayParam.getUserIp()).thenReturn("127.0.0.1");
        when(shepherdGatewayParam.getAppVersion()).thenReturn("1.0.0");
        // act
        ScenePlayExecuteRequest result = dealGiftFetcher.buildScenePlayExecuteRequest(productBaseInfo, dealGroupIdMapper, shopIdMapper, cityIdMapper, playId, null);
        // assert
        assertNotNull(result);
        assertEquals(playId, result.getPlayId());
        assertNotNull(result.getUserInfo());
        assertNotNull(result.getRequest());
        assertNotNull(result.getActiveRequestUnit());
    }

    /**
     * Test case with empty requestForExecutePlay map
     */
    @Test
    public void testBuildScenePlayExecuteRequest_EmptyRequestMap() throws Throwable {
        // arrange
        long playId = 123L;
        long userId = 456L;
        when(request.getUserId()).thenReturn(userId);
        when(request.getMobileOSType()).thenReturn(MobileOSTypeEnum.IOS);
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.DP_APP);
        when(request.getDpUserId()).thenReturn(userId);
        when(request.getShepherdGatewayParam()).thenReturn(shepherdGatewayParam);
        when(shepherdGatewayParam.getUnionid()).thenReturn("test-unionid");
        when(shepherdGatewayParam.getUserIp()).thenReturn("127.0.0.1");
        when(shepherdGatewayParam.getAppVersion()).thenReturn("1.0.0");
        requestForExecutePlay = new HashMap<>();
        // act
        ScenePlayExecuteRequest result = dealGiftFetcher.buildScenePlayExecuteRequest(productBaseInfo, dealGroupIdMapper, shopIdMapper, cityIdMapper, playId, requestForExecutePlay);
        // assert
        assertNotNull(result);
        assertEquals(playId, result.getPlayId());
        assertNotNull(result.getUserInfo());
        UserInfo userInfo = result.getUserInfo();
        assertEquals(userId, userInfo.getUserId());
        assertEquals(UserSourceEnum.DP.getValue(), userInfo.getUserSource());
        assertNotNull(result.getRequest());
        assertNotNull(result.getActiveRequestUnit());
    }

    /**
     * Test boundary case with zero playId
     */
    @Test
    public void testBuildScenePlayExecuteRequest_ZeroPlayId() throws Throwable {
        // arrange
        long playId = 0L;
        long userId = 456L;
        when(request.getUserId()).thenReturn(userId);
        when(request.getMobileOSType()).thenReturn(MobileOSTypeEnum.IOS);
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.MT_APP);
        when(request.getMtUserId()).thenReturn(userId);
        when(request.getShepherdGatewayParam()).thenReturn(shepherdGatewayParam);
        when(shepherdGatewayParam.getUnionid()).thenReturn("test-unionid");
        when(shepherdGatewayParam.getUserIp()).thenReturn("127.0.0.1");
        when(shepherdGatewayParam.getAppVersion()).thenReturn("1.0.0");
        requestForExecutePlay = new HashMap<>();
        // act
        ScenePlayExecuteRequest result = dealGiftFetcher.buildScenePlayExecuteRequest(productBaseInfo, dealGroupIdMapper, shopIdMapper, cityIdMapper, playId, requestForExecutePlay);
        // assert
        assertNotNull(result);
        assertEquals(0L, result.getPlayId());
        assertNotNull(result.getUserInfo());
        assertNotNull(result.getRequest());
        assertNotNull(result.getActiveRequestUnit());
    }

    /**
     * Test case with Android mobile OS type
     */
    @Test
    public void testBuildScenePlayExecuteRequest_AndroidOSType() throws Throwable {
        // arrange
        long playId = 123L;
        long userId = 456L;
        when(request.getUserId()).thenReturn(userId);
        when(request.getMobileOSType()).thenReturn(MobileOSTypeEnum.ANDROID);
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.MT_APP);
        when(request.getMtUserId()).thenReturn(userId);
        when(request.getShepherdGatewayParam()).thenReturn(shepherdGatewayParam);
        when(shepherdGatewayParam.getUnionid()).thenReturn("test-unionid");
        when(shepherdGatewayParam.getUserIp()).thenReturn("127.0.0.1");
        when(shepherdGatewayParam.getAppVersion()).thenReturn("1.0.0");
        requestForExecutePlay = new HashMap<>();
        // act
        ScenePlayExecuteRequest result = dealGiftFetcher.buildScenePlayExecuteRequest(productBaseInfo, dealGroupIdMapper, shopIdMapper, cityIdMapper, playId, requestForExecutePlay);
        // assert
        assertNotNull(result);
        assertEquals(playId, result.getPlayId());
        UserInfo userInfo = result.getUserInfo();
        assertNotNull(userInfo);
        assertEquals(userId, userInfo.getUserId());
        Map<String, String> requestMap = result.getRequest();
        assertNotNull(requestMap);
        // Android should be "1"
        assertEquals("1", requestMap.get("osType"));
    }
}
