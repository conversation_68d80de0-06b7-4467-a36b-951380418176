package com.sankuai.dzshoppingguide.product.detail.application.fetcher.tag.guarantee;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import com.sankuai.nib.price.operation.api.guarantee.common.dto.TagDTO;
import com.sankuai.nib.price.operation.api.guarantee.guide.standard.dto.ObjectGuaranteeTagDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2025-03-12
 * @desc 商品保障标签信息
 */

@EqualsAndHashCode(callSuper = true)
@Data
public class ProductGuaranteeTagInfo extends FetcherReturnValueDTO {
    /**
     * 国家补贴
     */
    private TagDTO stateSubsidies;

    /**
     * 安心学
     */
    private TagDTO anXinLearning;
}
