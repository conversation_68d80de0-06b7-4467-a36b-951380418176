package com.sankuai.dzshoppingguide.product.detail.spi.membercard.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/2/5 10:31
 * 移动之家 https://mobile.sankuai.com/studio/model/info/41251
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TypeDoc(description = "新版预订详情页(折扣卡模块)")
@MobileDo(id = 0xdbe0)
public class DiscountCardModuleVO extends BasicCardModuleVO {
    @Override
    public String getModuleKey() {
        return ModuleKeyConstants.MEMBER_CARD;
    }

    @FieldDoc(description = "会员价")
    @MobileDo.MobileField(key = 0xb2e7)
    private String dealPriceWithPromo;

    @FieldDoc(description = "非会员价")
    @MobileDo.MobileField(key = 0xedce)
    private String dealPriceWithCard;

    @FieldDoc(description = "限时促销描述")
    @MobileDo.MobileField(key = 0x7146)
    private String limitTimePromoDesc;
}
