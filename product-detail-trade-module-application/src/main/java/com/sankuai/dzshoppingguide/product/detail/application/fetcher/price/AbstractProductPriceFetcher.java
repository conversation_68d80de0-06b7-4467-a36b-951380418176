package com.sankuai.dzshoppingguide.product.detail.application.fetcher.price;

import com.dianping.deal.common.enums.ClientTypeEnum;
import com.dianping.lion.client.Lion;
import com.dianping.lion.common.util.JsonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.sankuai.dealuser.price.display.api.enums.*;
import com.sankuai.dealuser.price.display.api.model.BatchPriceRequest;
import com.sankuai.dealuser.price.display.api.model.ClientEnv;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.ProductIdentity;
import com.sankuai.dealuser.price.display.api.model.id.UserIdDTO;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dz.product.detail.RequestCustomParamEnum;
import com.sankuai.dz.product.detail.gateway.spi.dto.ShepherdGatewayParam;
import com.sankuai.dz.product.detail.gateway.spi.enums.MobileOSTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.constants.Constants;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.abtest.AbTestFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.abtest.AbTestReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.membercard.card.integration.IntegrationMemberCard;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.best.shop.ShopInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.best.shop.ShopInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.dto.LocalPriceDisplayDTO;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.dto.ProductPriceReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.defaultselect.SkuDefaultSelect;
import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
import com.sankuai.dzshoppingguide.product.detail.application.service.dto.ShopMemberDetailV;
import com.sankuai.dzshoppingguide.product.detail.application.utils.ClientMappingUtils;
import com.sankuai.dzshoppingguide.product.detail.application.utils.LiveUtils;
import com.sankuai.dzshoppingguide.product.detail.application.utils.VersionUtils;
import com.sankuai.dzshoppingguide.product.detail.application.utils.magic.MagicFlagUtils;
import com.sankuai.dzshoppingguide.product.detail.domain.utils.LionConfigUtils;
import com.sankuai.dzshoppingguide.product.detail.domain.utils.LionConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.RequestSourceEnum;
import com.sankuai.mpmctmember.query.common.enums.MemberChargeTypeEnum;
import com.sankuai.mpmctmember.query.common.enums.MemberInterestCodeEnum;
import com.sankuai.mpmctmember.query.common.enums.UserMemberTypeEnum;
import com.sankuai.mpmctmember.query.thrift.dto.MemberInterestDetailDTO;
import com.sankuai.sinai.data.api.dto.DpPoiBackCategoryDTO;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.sinai.data.api.dto.MtPoiDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static com.sankuai.dealuser.price.display.api.enums.AggregateStrategyEnum.MAGIC_MEMBER_COUPON_AGGREGATION;
import static com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum.*;

/**
 * <AUTHOR>
 * @date 2025-03-04
 * @desc 商品价格查询器
 */
@Fetcher(
        previousLayerDependencies = {
                ShopInfoFetcher.class,
                AbTestFetcher.class
        }
)
@Slf4j
public abstract class AbstractProductPriceFetcher<T extends ProductPriceReturnValue> extends NormalFetcherContext<T> {
    /**
     * 猜喜渠道
     */
    private static final String SUBSIDY_SCENE = "subsidyScene";
    private static final String HIT_GUESS_LIKE_SUBSIDY = "hitGuessLikeSubsidy";

    /**
     * 支持的客户端类型
     */
    public static final Set<com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum> SUPPORT_CLIENT_TYPE = Sets.newHashSet(MT_APP, DP_APP, MT_XCX, DP_XCX,
            DP_BAIDU_MAP_XCX, MT_KUAI_SHOU_XCX, MT_I, DP_M, MT_LIVE_XCX, MT_LIVE_ORDER_XCX);

    // 支持的ODP客户端类型
    public static final Set<com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum> ODP_SUPPORT_CLIENT_TYPE = Sets.newHashSet(MT_APP, DP_APP, MT_XCX, DP_XCX,
            DP_BAIDU_MAP_XCX, MT_I, DP_M, MT_LIVE_XCX, MT_LIVE_ORDER_XCX);


    @Resource
    private CompositeAtomService compositeAtomService;

    @Override
    protected CompletableFuture<T> doFetch() {
        return null;
    }

    public BatchPriceRequest buildBatchPriceRequest(ProductDetailPageRequest request, SkuDefaultSelect defaultSelect) {
        BatchPriceRequest priceRequest = new BatchPriceRequest();
        // 1. 环境参数
        ClientEnv clientEnv = buildClientEnv(request);
        priceRequest.setClientEnv(clientEnv);
        // 2. 商品参数
        int productId = (int) request.getProductId();
        ProductIdentity productIdentity = new ProductIdentity(productId, ProductTypeEnum.DEAL.getType());
        productIdentity.setExtParams(buildExtParams());
        String flowFlag = request.getCustomParam(RequestCustomParamEnum.flowFlag);
        if (Objects.nonNull(defaultSelect) && (!Objects.equals(flowFlag, Constants.COUNTRY_SUBSIDIES) || request.getSkuId() > 0)) {
            productIdentity.setSkuId((int) defaultSelect.getSelectedSkuId());
        }
        List<ProductIdentity> productIdentities = Lists.newArrayList();
        productIdentities.add(productIdentity);
        priceRequest.setLongShopId2ProductIds(Collections.singletonMap(request.getPoiId(), productIdentities));
        // 3. 用户ID
        priceRequest.setUserId(request.getUserId());
        // 4. 扩展参数
        Map<String, String> extension = buildExtension();
        priceRequest.setExtension(extension);
        // 5. 价格一致率参数
        priceRequest.getExtension().put(ExtensionKeyEnum.PageName.getDesc(), PageNameEnum.DealGroupNormalDetailV2.getCode());
        String priceCipher = request.getPricecipher();
        if (StringUtils.isNotBlank(priceCipher)) {
            priceRequest.getExtension().put(ExtensionKeyEnum.PriceSecretInfo.getDesc(), priceCipher);
        }
        // 6. 直播秒杀场景查价参数
        String passParam = request.getCustomParam(RequestCustomParamEnum.pass_param);
        Map<String, String> liveSecKillParams = LiveUtils.getLiveSecKillParams(passParam, request.getPageSource());
        if (MapUtils.isNotEmpty(liveSecKillParams)) {
            extension.putAll(liveSecKillParams);
            String liveSecKillActiveId = ExtensionKeyEnum.LiveSecKillActiveId.getDesc();
            if (liveSecKillParams.containsKey(liveSecKillActiveId)) {
                productIdentities.forEach(identity -> {
                    identity.getExtParams().put(liveSecKillActiveId, liveSecKillParams.get(liveSecKillActiveId));
                });
            }
        }
        postProcessPriceRequest(clientEnv, productIdentities, extension);
        return priceRequest;
    }

    protected CompletableFuture<LocalPriceDisplayDTO> getProductPriceDisplayDTO(BatchPriceRequest priceRequest) {
        return compositeAtomService.batchQueryPriceWithResponse(priceRequest).thenApply(result -> {
            if (result == null || !result.isSuccess()
                    || MapUtils.isEmpty(result.getData())
                    || !result.getData().containsKey(request.getPoiId())) {
                return null;
            }
            PriceDisplayDTO priceDisplayDTO = result.getData().get(request.getPoiId())
                    .stream()
                    .filter(p -> p.getIdentity().getProductId() == request.getProductId())
                    .findFirst()
                    .orElse(null);
            if (priceDisplayDTO == null) {
                return null;
            }
            return new LocalPriceDisplayDTO(priceDisplayDTO, result.getPriceSecretInfo());
        });
    }

    protected CompletableFuture<List<PriceDisplayDTO>> getSkuPriceDisplayDTO(BatchPriceRequest priceRequest) {
        return compositeAtomService.batchQueryPriceWithResponse(priceRequest).thenApply(result -> {
            if (result == null || !result.isSuccess()
                    || MapUtils.isEmpty(result.getData())
                    || !result.getData().containsKey(request.getPoiId())) {
                return null;
            }
            return result.getData().get(request.getPoiId());
        });
    }

    /**
     * 报价请求参数后置处理器
     * @param clientEnv 报价请求环境参数
     * @param productIdentities 报价请求商品参数
     * @param extension 报价请求扩展参数
     */
    protected abstract void postProcessPriceRequest(ClientEnv clientEnv, List<ProductIdentity> productIdentities, Map<String, String> extension);

    protected Map<String, String> buildExtParams() {
        String extParam = request.getCustomParam(RequestCustomParamEnum.extparam);
        return parseExtParams(extParam);
    }

    private Map<String, String> buildExtension() {
        Map<String, String> ext = Maps.newHashMap();
        // 神会员点位
        String position = getPosition(request);
        ext.put(ExtensionKeyEnum.Position.getDesc(), position);
        // 神会员生效标识
        // 1. 神券膨胀标识，1-可膨 2-不可膨
        int mmcInflate = Integer.parseInt(MagicFlagUtils.getMmcInflate(request));
        if (MagicFlagUtils.isValid(mmcInflate)) {
            ext.put(ExtensionKeyEnum.QueryInflateType.getDesc(), String.valueOf(MagicFlagUtils.canInflate(mmcInflate) 
                    ? QueryInflateTypeEnum.EXIST_COUPON_INFLATE_QUERY.getCode() : QueryInflateTypeEnum.NOT_INFLATE_QUERY.getCode()));
        }
        // 2. 券包可买标识： 1-可买 2-不可买
        int mmcBuy = Integer.parseInt(MagicFlagUtils.getMmcBuy(request));
        if (MagicFlagUtils.isValid(mmcBuy)) {
            ext.put(ExtensionKeyEnum.QueryMMCPackageType.getDesc(), String.valueOf(MagicFlagUtils.canBuy(mmcBuy) 
                    ? QueryMMCPackageTypeEnum.MMC_PACKAGE_QUERY.getCode() : QueryMMCPackageTypeEnum.MMC_PACKAGE_NOT_QUERY.getCode()));
        }
        // 3. 神券可用标识： 1-可用 2-不可用
        int mmcUse = Integer.parseInt(MagicFlagUtils.getMmcUse(request));
        if (MagicFlagUtils.isValid(mmcUse)) {
            ext.put(ExtensionKeyEnum.QueryMMCType.getDesc(), String.valueOf(MagicFlagUtils.canUse(mmcUse) 
                    ? QueryMMCTypeEnum.MMC_CAN_USE.getCode() : QueryMMCTypeEnum.MMC_CAN_NOT_USE.getCode()));
        }
        // 4. 神券聚合
        ext.put(ExtensionKeyEnum.couponAggregateStrategy.getDesc(), String.valueOf(MAGIC_MEMBER_COUPON_AGGREGATION.getType()));
        // 5. 神券包组件版本
        if (StringUtils.isNotBlank(MagicFlagUtils.getMmcPakVersion(request))) {
            ext.put(ExtensionKeyEnum.magicMemberComponentVersion.getDesc(), MagicFlagUtils.getMmcPakVersion(request));
        }
        // 报价来源
        ext.put(ExtensionKeyEnum.Source.getDesc(), request.getPageSource());
        String priceSource = buildPriceSource();
        if (StringUtils.isNotBlank(priceSource)) {
            ext.put(ExtensionKeyEnum.Source.getDesc(), priceSource);
        }
        // 页面来源
        ext.put(ExtensionKeyEnum.PageSource.getDesc(), String.valueOf(PageSourceEnum.dealDetailPage.getType()));
        // 优惠描述文案模板
        ext.put(ExtensionKeyEnum.PromoDescType.getDesc(), PromoDescEnum.New_Tuan_Detail.getType());
        // 优惠减负2期限时秒杀优惠单独提出来一个优惠类型，仅针对App端生效
        if (request.getClientTypeEnum().isInApp()) {
            ext.put(ExtensionKeyEnum.PromoDetailTemplate.getDesc(), String.valueOf(PromoDetailTemplateEnum.NewVersion.getCode()));
        }
        return ext;
    }

    private String buildPriceSource() {
        String pageSource = request.getPageSource();
        if (StringUtils.isBlank(pageSource)) {
            return null;
        }
        Map<String, String> pageSource2PriceSourceMap = LionConfigUtils.getPageSource2PriceSourceMap();
        // 优惠码小程序处理
        if (request.getClientTypeEnum() == MT_XCX && RequestSourceEnum.fromYouHuiMaMiniChannel(pageSource)) {
            return pageSource2PriceSourceMap.get(pageSource);
        }
        // 版本号控制
        String appVersion = request.getShepherdGatewayParam().getAppVersion();
        if (request.getClientTypeEnum().isMtClientType() && VersionUtils.isLessThan(appVersion, "12.8.200")) {
            return null;
        }
        if (request.getClientTypeEnum().isDpClientType() && VersionUtils.isLessThan(appVersion, "10.73.10")) {
            return null;
        }
        // 特殊处理猜喜场景
        return handlePriceSource(request, pageSource2PriceSourceMap);
    }

    private String handlePriceSource(ProductDetailPageRequest pageRequest, Map<String, String> pageSource2PriceSourceMap) {
        String priceSource = pageSource2PriceSourceMap.get(pageRequest.getPageSource());
        // 猜喜渠道流量来源为source=caixi，但是猜喜侧只有hitGuessLikeSubsidy=true时调用报价才传source=caixi，为和猜喜保持一致，使用hitGuessLikeSubsidy字段判定
        if (!LionConfigUtils.useNewSourceForCaixi()) {
            return priceSource;
        }
        if (RequestSourceEnum.CAI_XI.getSource().equals(pageRequest.getPageSource())) {
            String extParam = pageRequest.getCustomParam(RequestCustomParamEnum.extparam);
            Map<String, String> requestExtParams = parseExtParams(extParam);
            return MapUtils.isNotEmpty(requestExtParams) && requestExtParams.containsKey(SUBSIDY_SCENE) 
                    && HIT_GUESS_LIKE_SUBSIDY.equals(requestExtParams.get(SUBSIDY_SCENE)) ? RequestSourceEnum.CAI_XI.getSource() : null;
        }
        return priceSource;
    }

    private Map<String, String> parseExtParams(String extParam) {
        if (StringUtils.isBlank(extParam)) {
            return new HashMap<>();
        }
        try {
            return JsonUtils.fromJson(extParam, new TypeReference<Map<String, String>>() {});
        } catch (Exception e) {
            log.error("parse ext params failed", e);
            return new HashMap<>();
        }
    }

    private ClientEnv buildClientEnv(ProductDetailPageRequest pageRequest) {
        // 1. 环境参数
        ClientEnv clientEnv = new ClientEnv();
        ShepherdGatewayParam shepherdGatewayParam = pageRequest.getShepherdGatewayParam();
        // 1.1 客户端类型
        ClientTypeEnum clientTypeEnum = ClientMappingUtils.getBpClientType(pageRequest);
        clientEnv.setClientType(clientTypeEnum.getType());
        // 1.2 union id
        String unionId = shepherdGatewayParam.getUnionid();
        clientEnv.setUnionId(unionId);
        // 1.3 版本号
        String appVersion = shepherdGatewayParam.getAppVersion();
        clientEnv.setVersion(appVersion);
        // 1.4 经纬度
        clientEnv.setLatitude(pageRequest.getUserLat());
        clientEnv.setLongitude(pageRequest.getUserLng());
        // 1.5 定位城市ID
        clientEnv.setGpsCityId(pageRequest.getGpsCityId());
        // 1.6 经纬度类型
        clientEnv.setGpsCoordinateType(pageRequest.getGpsCoordinateTypeEnum().name());
        // 1.7 用户信息
        UserIdDTO userInfo = new UserIdDTO();
        userInfo.setDpRealId(pageRequest.getDpUserId());
        userInfo.setDpVirtualId(shepherdGatewayParam.getDpVirtualUserId());
        userInfo.setMtRealId(pageRequest.getMtUserId());
        userInfo.setMtVirtualId(shepherdGatewayParam.getMtVirtualUserId());
        clientEnv.setUserIdDTO(userInfo);
        clientEnv.setCityId(pageRequest.getCityId());
        // 1.8 uuid 和 dpid
        clientEnv.setUuid(shepherdGatewayParam.getDeviceId());
        // 1.9 微信小程序神会员特定参数
        if (pageRequest.getClientTypeEnum().isInWxXCX()) {
            String openId = shepherdGatewayParam.getOpenId();
            String mpAppId = shepherdGatewayParam.getMpAppId();
            // 小程序版本
            String wxVersion = shepherdGatewayParam.getCsecversionname();
            clientEnv.setWxOpenId(openId);
            clientEnv.setWxAppId(mpAppId);
            clientEnv.setWxVersion(wxVersion);
        }
        return clientEnv;
    }

    public boolean isPreSaleProduct(ProductAttr productAttr) {
        String preSaleTag = productAttr.getProductAttrFirstValue("preSaleTag");
        return StringUtils.isNotBlank(preSaleTag) && preSaleTag.contains("true");
    }

    private String getPosition(ProductDetailPageRequest request) {
        if (request.getClientTypeEnum() == MT_APP) {
            return "2104";
        }
        // 前端MRN版本号
        ShepherdGatewayParam shepherdGatewayParam = request.getShepherdGatewayParam();
        String mrnVersion = shepherdGatewayParam.getMrnVersion();
        if (request.getClientTypeEnum() == DP_APP && isMagicMemberValid(mrnVersion)) {
            return "3104";
        }
        if (request.getClientTypeEnum() == MT_XCX) {
            return "1104";
        }
        return StringUtils.EMPTY;
    }

    // 神会员优惠生效开关
    public static boolean isMagicMemberValid(String mrnVersion) {
        if (StringUtils.isBlank(mrnVersion)) {
            return false;
        }
        return VersionUtils.isGreaterThanOrEqual(mrnVersion, Lion.getString("com.sankuai.dzu.tpbase.dztgdetailweb",
                LionConstants.MAGICAL_DP_MRN_MIN_VERSION, "0.5.11"));
    }

    public boolean isMerchantMember(com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum clientTypeEnum, int secondCategoryId) {
        Set<com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum> clientTypeEnums = Sets.newHashSet(MT_APP, DP_APP, MT_XCX, DP_XCX, MT_LIVE_XCX, MT_LIVE_ORDER_XCX);
        boolean judgeMemberPrice = LionConfigUtils.judgeMemberPriceCategory(secondCategoryId);
        return clientTypeEnums.contains(clientTypeEnum) && judgeMemberPrice;
    }

    /**
     * 普通立减价和商家会员价比价
     * 如果商家会员价比普通立减价低，则返回商家会员价
     * 否则返回普通立减价
     * @param merchantMemberNormalPriceDisplayReturnValue 商家会员价
     * @param defaultNormalPriceDisplayReturnValue 普通立减价
     * @param productId 商品ID
     * @return 是否返回商家会员价
     */
    public boolean useMerchantMemberPrice(ProductPriceReturnValue merchantMemberNormalPriceDisplayReturnValue, ProductPriceReturnValue defaultNormalPriceDisplayReturnValue, long productId) {
        // 如果普通立减价为空，则返回商家会员价
        if (Objects.isNull(defaultNormalPriceDisplayReturnValue) || Objects.isNull(defaultNormalPriceDisplayReturnValue.getPriceDisplayDTO())) {
            return true;
        }
        //  如果商家会员价为空，则返回普通立减价
        if (Objects.isNull(merchantMemberNormalPriceDisplayReturnValue) || Objects.isNull(merchantMemberNormalPriceDisplayReturnValue.getPriceDisplayDTO())) {
            return false;
        }

        PriceDisplayDTO defaultNormalPriceDisplay = defaultNormalPriceDisplayReturnValue.getPriceDisplayDTO();
        PriceDisplayDTO merchantMemberNormalPriceDisplay = merchantMemberNormalPriceDisplayReturnValue.getPriceDisplayDTO();
        return merchantMemberNormalPriceDisplay.getPrice().compareTo(defaultNormalPriceDisplay.getPrice()) < 0;
    }

    public boolean isFootMassageScene(com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum clientTypeEnum, Set<Integer> poiCategoryId, int secondCategoryId) {
        // 判断是否足疗场景
        Set<com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum> clientTypeEnums = Sets.newHashSet(MT_APP, DP_APP, MT_I, DP_M, MT_LIVE_XCX, MT_LIVE_ORDER_XCX);
        List<Integer> categoryIds = Lists.newArrayList(303, 304, 1610);
        // todo poiCategoryId为空，待确认
//        return clientTypeEnums.contains(clientTypeEnum) && poiCategoryId.contains(48) && categoryIds.contains(secondCategoryId);
        return clientTypeEnums.contains(clientTypeEnum) && categoryIds.contains(secondCategoryId);
    }

    public Set<Integer> getPoiCategoryIds(com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum clientTypeEnum, ShopInfo shopInfo) {
        if (Objects.isNull(shopInfo)) {
            return Sets.newHashSet();
        }
        if (clientTypeEnum.isMtClientType()) {
            return getMtPoiCategoryIds(shopInfo.getMtPoiDTO());
        }
        return getDpPoiCategoryIds(shopInfo.getDpPoiDTO());
    }

    public Set<Integer> getMtPoiCategoryIds(MtPoiDTO mtPoiDTO) {
        if (Objects.isNull(mtPoiDTO)) {
            return Sets.newHashSet();
        }
        return CollectionUtils.emptyIfNull(mtPoiDTO.getDpBackCategoryList())
                .stream()
                .map(DpPoiBackCategoryDTO::getCategoryId)
                .collect(Collectors.toSet());
    }

    public Set<Integer> getDpPoiCategoryIds(DpPoiDTO dpPoiDTO) {
        if (Objects.isNull(dpPoiDTO)) {
            return Sets.newHashSet();
        }
        return CollectionUtils.emptyIfNull(dpPoiDTO.getBackMainCategoryPath())
                .stream()
                .map(DpPoiBackCategoryDTO::getCategoryId)
                .collect(Collectors.toSet());
    }

    public boolean isODP(com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum clientTypeEnum, String pageSource) {
        return RequestSourceEnum.fromOdp(pageSource) && ODP_SUPPORT_CLIENT_TYPE.contains(clientTypeEnum);
    }

    public boolean isDpBaiduMapXcx(com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum clientTypeEnum) {
        return DP_BAIDU_MAP_XCX.equals(clientTypeEnum);
    }

    public boolean isMtLiveXcx(com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum clientTypeEnum) {
        // 美团美播小程序
        return clientTypeEnum == com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum.MT_LIVE_XCX || clientTypeEnum == com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum.MT_LIVE_ORDER_XCX;
    }

    protected boolean needCompare(IntegrationMemberCard integrationMemberCard) {
        // 如果会员卡信息为空，说明会员优惠中不是商家会员卡则需要比价
        if (Objects.isNull(integrationMemberCard) || Objects.isNull(integrationMemberCard.getMemberCardInterest())) {
            return true;
        }
        ShopMemberDetailV shopMemberDetailV = integrationMemberCard.getMemberCardInterest();
        // 如果商品没有会员价/新会员价，不比价
        if (!isMemberPriceProduct(shopMemberDetailV) && !isNewMemberPriceProduct(shopMemberDetailV)) {
            return false;
        }
        // 如果用户未开通付费商家会员，不比价
        if (integrationMemberCard.getMemberCardType() == MemberChargeTypeEnum.CHARGE.getCode() && !shopMemberDetailV.isMember()) {
            return false;
        }
        // 新会员商品，仅非会员或新会员能展示
        if (isNewMemberPriceProduct(shopMemberDetailV) && shopMemberDetailV.isMember()) {
            return false;
        }
        return true;
    }


    // 是否是会员价商品
    private boolean isMemberPriceProduct(ShopMemberDetailV shopMemberDetailV) {
        if (Objects.isNull(shopMemberDetailV) || Objects.isNull(shopMemberDetailV.getOriginalResult())) {
            return false;
        }
        // interests不为空 MemberInterestDTO.interestCode为MemberInterestCodeEnum.PLATFORM_PRODUCT_PROMO
        // 且 MemberInterestDTO.interestUseRule中targetMemberTypes包含UserMemberTypeEnum.OLD_MEMBER老会员
        MemberInterestDetailDTO memberInterestDetailDTO = shopMemberDetailV.getOriginalResult();
        return CollectionUtils.isNotEmpty(memberInterestDetailDTO.getInterests())
                && memberInterestDetailDTO.getInterests()
                .stream()
                .anyMatch(interestDTO -> Objects.equals(interestDTO.getInterestCode(), MemberInterestCodeEnum.PLATFORM_PRODUCT_PROMO.getCode()))
                && memberInterestDetailDTO.getInterests()
                .stream()
                .anyMatch(interestDTO -> Objects.nonNull(interestDTO.getInterestUseRule())
                        && CollectionUtils.isNotEmpty(interestDTO.getInterestUseRule().getTargetMemberTypes())
                        && interestDTO.getInterestUseRule().getTargetMemberTypes().contains(UserMemberTypeEnum.OLD_MEMBER.getCode()));
    }

    // 是否是新会员价商品
    private boolean isNewMemberPriceProduct(ShopMemberDetailV shopMemberDetailV) {
        if (Objects.isNull(shopMemberDetailV) || Objects.isNull(shopMemberDetailV.getOriginalResult())) {
            return false;
        }
        //interests不为空 MemberInterestDTO.interestCode为MemberInterestCodeEnum.PLATFORM_PRODUCT_PROMO
        //且 MemberInterestDTO.interestUseRule中targetMemberTypes的size=1 且targetMemberTypes包含UserMemberTypeEnum.NEW_MEMBER
        MemberInterestDetailDTO memberInterestDetailDTO = shopMemberDetailV.getOriginalResult();
        return CollectionUtils.isNotEmpty(memberInterestDetailDTO.getInterests())
                && memberInterestDetailDTO.getInterests()
                .stream()
                .anyMatch(interestDTO -> Objects.equals(interestDTO.getInterestCode(), MemberInterestCodeEnum.PLATFORM_PRODUCT_PROMO.getCode()))
                && memberInterestDetailDTO.getInterests()
                .stream()
                .anyMatch(interestDTO -> Objects.nonNull(interestDTO.getInterestUseRule())
                        && CollectionUtils.isNotEmpty(interestDTO.getInterestUseRule().getTargetMemberTypes())
                        && interestDTO.getInterestUseRule().getTargetMemberTypes().size() == 1
                        && interestDTO.getInterestUseRule().getTargetMemberTypes().contains(UserMemberTypeEnum.NEW_MEMBER.getCode()));
    }
}
