package com.sankuai.dzshoppingguide.product.detail.spi.headpic.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.HeadPicModuleVO;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dz.product.detail.gateway.spi.vo.AbstractModuleVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/2/5 17:38
 * 移动之家 https://mobile.sankuai.com/studio/model/info/41249
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TypeDoc(description = "新版预订详情页（头图模块）")
@MobileDo(id = 0xf8f)
public class HeadPicModulesVO extends AbstractModuleVO {
    @Override
    public String getModuleKey() {
        return ModuleKeyConstants.HEAD_PIC;
    }

    @FieldDoc(description = "预订详情头图模块")
    @MobileDo.MobileField(key = 0x1e76)
    private List<HeadPicModuleVO> headPicModules;
}
