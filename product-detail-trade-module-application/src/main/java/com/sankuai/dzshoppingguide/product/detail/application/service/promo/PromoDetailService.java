package com.sankuai.dzshoppingguide.product.detail.application.service.promo;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.dianping.tgc.open.entity.*;
import com.dianping.tpfun.product.api.sku.pintuan.dto.PinProductBrief;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.dealuser.price.display.api.enums.*;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.builder.promodetail.dto.PromotionDetailParams;
import com.sankuai.dzshoppingguide.product.detail.application.builder.promodetail.enums.CouponStatusEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.promodetail.enums.CouponStyleEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.promodetail.enums.CouponTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.promodetail.enums.PromotionTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.LeadsInfoFetcher.LeadsInfoResult;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.cepinpool.dto.CostEffectivePinTuan;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.dealgift.DealGiftResult;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.cityId.CityIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.shopid.ShopIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.membercard.card.discount.MemberCardDiscountReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.best.shop.ShopInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.dto.ProductPriceReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.defaultselect.SkuDefaultSelect;
import com.sankuai.dzshoppingguide.product.detail.application.model.CountrySubsidiesConfig;
import com.sankuai.dzshoppingguide.product.detail.application.utils.*;
import com.sankuai.dzshoppingguide.product.detail.application.utils.context.DealCtxUtils;
import com.sankuai.dzshoppingguide.product.detail.application.utils.richText.RichText;
import com.sankuai.dzshoppingguide.product.detail.application.utils.richText.RichTextUtil;
import com.sankuai.dzshoppingguide.product.detail.application.utils.richText.TextStyle;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.Icon;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.promo.CouponItemPackageDTO;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.promo.FreeDealConfig;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.CanInflateEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.CouponValidStatusEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.InflateStatusEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.PromoTagEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.promo.vo.*;
import com.sankuai.mpmctmember.query.common.enums.MemberChargeTypeEnum;
import com.sankuai.nibmkt.promotion.api.common.enums.MagicalMemberTagShowTypeEnum;
import com.sankuai.nibmkt.promotion.api.common.enums.PromotionPropertyEnum;
import com.sankuai.nibmkt.promotion.api.common.enums.PromotionTextEnum;
import com.sankuai.nibmkt.promotion.api.common.utils.MagicalMemberTagTextUtils;
import com.sankuai.nibmkt.promotion.api.query.model.magicalmember.response.MagicalMemberTagTextDTO;
import com.sankuai.nibscp.common.flow.identify.util.SptDyeUtil;
import com.sankuai.statesubsidies.c.thrift.constant.QualificationStatusEnum;
import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.sankuai.dzshoppingguide.product.detail.application.utils.PromoHelper.*;
import static com.sankuai.dzshoppingguide.product.detail.spi.enums.MagicCouponTypeEnum.*;

/**
 * <AUTHOR>
 * @create 2025/3/3 19:37
 */
@Service
@Slf4j
public class PromoDetailService {
    @Resource
    private PromoDetailModuleBuilderService promoDetailModuleBuilderService;
    @Resource
    private PromotionDetailBuilderService promotionDetailBuilderService;
    @Resource
    private ButtonStyleHelper buttonStyleHelper;
    @Resource
    private LeafRepository leafRepository;

    // 优惠详情组装信息（最长不超过10行，每行字数<=5）
    private static final int MAX_CHAR = 10;
    public static final int MAX_CHARS_PER = 5;

    public static final String DEFAULT_VIP_COUPON_DESC = "神券";


    public PromoDetailModule buildPromoDetailInfo(ProductDetailPageRequest request,
                                                  CostEffectivePinTuan costEffectivePinTuan,
                                                  ProductBaseInfo dealGroupBase,
                                                  PriceDisplayDTO normalPrice,
                                                  PriceDisplayDTO costEffectivePrice,
                                                  ProductAttr productAttr) {
        PromoDetailModule promoDetailModule = null;
        try {
            //0元预约场景专用
            if (DealCtxUtils.isFreeDeal(dealGroupBase)){
                buildFreeDealPromoDetailInfo(dealGroupBase, promoDetailModule);
            }else if (DealCtxHelper.isOdpSource(request.getPageSource())) {
                promoDetailModule = buildOdpPromoDetailInfo(dealGroupBase, normalPrice);
            } else {
                promoDetailModule = buildNormalPromoDetailInfo(request.getClientTypeEnum(),dealGroupBase, normalPrice, request.getPageSource());
            }
            return promoDetailModule;
        } catch (Exception e) {
            log.error("buildPromoDetailInfo error", e);
            return promoDetailModule;
        }
    }

    public PromoDetailModule buildNormalPromoDetailInfo(ClientTypeEnum clientTypeEnum, ProductBaseInfo dealGroupBase, PriceDisplayDTO normalPrice, String requestSource) {
        if (normalPrice == null || Objects.isNull(dealGroupBase) || Objects.isNull(dealGroupBase.getPrice())) {
            return null;
        }
        // 适配第三方平台 开店宝侧、阿波罗侧 + 特团覆盖小程序 + 小程序升级新团详
        if (DealCtxUtils.isExternal(clientTypeEnum) && dealGroupBase.getCategory().getCategoryId() != 712) {
            if (!DealCtxUtils.isThirdPArt(clientTypeEnum) && DealCtxUtils.notMiniProgramCostEffective(clientTypeEnum, requestSource)) {
                return null;
            }
        }

        PromoDetailModule promoDetailModule = new PromoDetailModule();
        BigDecimal marketPrice = new BigDecimal(dealGroupBase.getPrice().getMarketPrice());
        //没有商家优惠时全网低价就是团单价
        String marketPriceStr = PriceHelper.formatPrice(marketPrice);
        promoDetailModule.setMarketPrice(marketPriceStr);
        BigDecimal promoPrice = normalPrice.getPrice();
        promoDetailModule.setPromoPrice(PriceHelper.formatPrice(promoPrice));


        if (CollectionUtils.isNotEmpty(normalPrice.getUsedPromos())) {
            BigDecimal couponPromo = new BigDecimal(0);
            BigDecimal reductionPromo = new BigDecimal(0);
            if (normalPrice.getUsedPromos() != null) {
                List<PromoDTO> promoDTOList = normalPrice.getUsedPromos();
                for (PromoDTO promoDTO : promoDTOList) {
                    if (promoDTO == null || promoDTO.getAmount() == null) {
                        continue;
                    }
                    if (promoDTO.getIdentity().getPromoType() == PromoTypeEnum.NORMAL_PROMO.getType()) {
                        reductionPromo = reductionPromo.add(promoDTO.getAmount());
                    } else if (promoDTO.getIdentity().getPromoType() == PromoTypeEnum.IDLE_PROMO.getType()) {
                        reductionPromo = reductionPromo.add(promoDTO.getAmount());
                    } else if (promoDTO.getIdentity().getPromoType() == PromoTypeEnum.THRESHOLD.getType()) {
                        reductionPromo = reductionPromo.add(promoDTO.getAmount());
                    } else {
                        couponPromo = couponPromo.add(promoDTO.getAmount());
                    }
                }
            }
        }
        return promoDetailModule;
    }

    public void buildDealPromoDetailInfo(ProductBaseInfo dealGroupBase,CostEffectivePinTuan costEffectivePinTuan, ClientTypeEnum clientTypeEnum,
                                         PriceDisplayDTO dealPromoPrice, PromoDetailModule promoDetailModule, PriceDisplayDTO costEffectivePrice,
                                         LeadsInfoResult leadsInfoResult, DealGiftResult dealGiftResult, List<DealGift> dealGifts,
                                         QualificationStatusEnum qualificationStatusEnum) {
        try {
            if (DealCtxHelper.isMtLiveMinApp(clientTypeEnum)) {
                // 私域直播价格无优惠
                return;
            }
            buildDealPromoDetailInfo(costEffectivePinTuan,
                    clientTypeEnum,
                    dealPromoPrice,
                    promoDetailModule,
                    dealGroupBase,
                    costEffectivePrice,
                    dealGifts,
                    leadsInfoResult, qualificationStatusEnum);
        } catch (Exception e) {
            log.error("buildDealPromoDetailInfo error", e);
        }
    }

    public NationalSubsidyInfo buildCountrySubsidiesInfo(PriceDisplayDTO priceDisplayDTO, QualificationStatusEnum qualificationStatusEnum, boolean isMt) {
        if (Objects.isNull(priceDisplayDTO)) {
            return null;
        }
        PromoDTO countrySubsidiesPromoDTO = Optional.ofNullable(priceDisplayDTO.getPricePromoInfoMap())
                .orElse(Collections.emptyMap())
                .getOrDefault(PricePromoInfoTypeEnum.STATE_SUBSIDIES_PROMO.getType(), Collections.emptyList())
                .stream()
                .filter(promo -> Objects.equals(promo.getIdentity().getPromoType(), PromoTypeEnum.STATE_SUBSIDIES_PROMO.getType()))
                .findFirst()
                .orElse(null);
        if (Objects.isNull(countrySubsidiesPromoDTO)) {
            return null;
        }
        CountrySubsidiesConfig config = LionConfigUtils.getCountrySubsidiesConfig();
        // 计算国补优惠价
        BigDecimal amount = countrySubsidiesPromoDTO.getAmount();
        String finalPrice = Objects.isNull(amount) ? StringUtils.EMPTY : String.valueOf(amount.toBigInteger());
        NationalSubsidyInfo nationalSubsidyInfo = new NationalSubsidyInfo();
        nationalSubsidyInfo.setPromo(finalPrice);
        nationalSubsidyInfo.setIcon(config.getIcon());
        String title = convertPromoTag(countrySubsidiesPromoDTO);
        nationalSubsidyInfo.setTitle(StringUtils.isBlank(title) ? config.getTitle() : title);
        String subTitle = Objects.nonNull(countrySubsidiesPromoDTO.getPromoTextDTO()) ? countrySubsidiesPromoDTO.getPromoTextDTO().getSubTitle() : StringUtils.EMPTY;
        nationalSubsidyInfo.setSubTitle(StringUtils.isBlank(subTitle) ? config.getSubtitle() : subTitle);
        // 0-未领取；1-已领取
        nationalSubsidyInfo.setIssueStatus(qualificationStatusEnum == QualificationStatusEnum.UNBIND ? 0 : 1);
        nationalSubsidyInfo.setJumpUrl(isMt ? config.getMtJumpUrl() : config.getDpJumpUrl());
        nationalSubsidyInfo.setButtonPic(qualificationStatusEnum == QualificationStatusEnum.UNBIND ?
                config.getUnReceivedButtonPic() : config.getReceivedButtonPic());
        return nationalSubsidyInfo;
    }

    /**
     * 包含团购优惠（市场价-售卖价）的优惠明细
     */
    private void buildDealPromoDetailInfo(CostEffectivePinTuan costEffectivePinTuan, ClientTypeEnum clientTypeEnum,
                                          PriceDisplayDTO dealPromoPrice, PromoDetailModule promoDetailModule,
                                          ProductBaseInfo dealGroupBase, PriceDisplayDTO costEffectivePrice,
                                          List<DealGift> dealGifts, LeadsInfoResult leadsInfoResult,
                                          QualificationStatusEnum qualificationStatusEnum) {
        if ((dealPromoPrice == null || CollectionUtils.isEmpty(dealPromoPrice.getUsedPromos()))) {
            if (needHideMarketPrice(dealGroupBase.getCategory().getCategoryId()) && promoDetailModule != null) {
                promoDetailModule.setMarketPrice(null);
            }
            return;
        }
        //0元预约-不展示优惠信息
        if (DealCtxUtils.isFreeDeal(dealGroupBase)){
            return;
        }
        // 留资型团单页面且（超值特惠套餐或小程序）不展示优惠信息
        if (DealUtils.isLeadsDeal(dealGroupBase) && (DealCtxUtils.isHitSpecialValueDeal(leadsInfoResult) || !(DealCtxUtils.judgeMainApp(clientTypeEnum)))) {
            return;
        }

        if (promoDetailModule == null) {
            promoDetailModule = new PromoDetailModule();
        }


        promoDetailModule.setFinalPrice(dealPromoPrice == null || dealPromoPrice.getPrice() == null ? "0.01" : PriceHelper.dropLastZero(dealPromoPrice.getPrice()));
        List<DealBestPromoDetail> bestPromoDetails = Lists.newArrayList();
        promoDetailModule.setBestPromoDetails(bestPromoDetails);

        promoDetailModule.setDealGifts(dealGifts);
        // 国补优惠
        NationalSubsidyInfo nationalSubsidyInfo = buildCountrySubsidiesInfo(dealPromoPrice, qualificationStatusEnum, clientTypeEnum.isMtClientType());
        if (Objects.nonNull(nationalSubsidyInfo)) {
            promoDetailModule.setNationalSubsidyInfo(nationalSubsidyInfo);
            promoDetailModule.setNationalSubsidyPromotionBar(buildNationalSubsidyPromotionBar(nationalSubsidyInfo));
        }
        List<PromoDTO> usedPromos = Optional.ofNullable(dealPromoPrice).map(PriceDisplayDTO::getUsedPromos)
                .orElse(Lists.newArrayList());

        if (isPinTuanStyle(costEffectivePinTuan)) {
            promoDetailModule.setFinalPrice(Objects.isNull(costEffectivePrice.getPrice()) ? "0.01" : PriceHelper.dropLastZero(costEffectivePrice.getPrice()));
        }
        // 强化样式下需要展示拼团优惠
        if (CostEffectivePinTuanUtils.enhancedStyle(costEffectivePinTuan) && Objects.nonNull(costEffectivePrice) && CollectionUtils.isNotEmpty(costEffectivePrice.getUsedPromos())) {
            usedPromos = costEffectivePrice.getUsedPromos();
        }
        for (PromoDTO promoDTO : usedPromos) {
            if (promoDTO.getIdentity() == null || promoDTO.getAmount() == null || StringUtils.isEmpty(promoDTO.getIdentity().getPromoTypeDesc())) {
                continue;
            }
            DealBestPromoDetail bestPromoDetail = new DealBestPromoDetail();
            bestPromoDetail.setIconUrl(promoDTO.getIcon());
            bestPromoDetail.setPromoName(promoDTO.getIdentity().getPromoTypeDesc());
            if (BeautyBianMeiCouponHelper.isBeautyBianMeiCouponPromo(promoDTO)) {
                bestPromoDetail.setPromoName(BeautyBianMeiCouponHelper.getBeautyBianMeiCouponPromoName(promoDTO));
            }
            bestPromoDetail.setPromoAmount(PriceHelper.dropLastZero(promoDTO.getAmount()));
            bestPromoDetail.setPromoTag(convertPromoTag(promoDTO));
            String promoDesc = convertPromoDesc(promoDTO,promoDetailModule,bestPromoDetail.getPromoTag());
            List<String> formatted = ParallDealBuilderProcessorUtils.formatToRichText(promoDesc, MAX_CHAR, MAX_CHARS_PER);
            bestPromoDetail.setPromoDesc(promoDesc);
            bestPromoDetail.setPromoDescList(formatted);
            buttonStyleHelper.entertainmentMemberPromoDetailComplete(promoDTO, bestPromoDetail);
            bestPromoDetails.add(bestPromoDetail);
        }


        //局改团详不展示优惠标签
        if(ReassuredRepairUtil.isTagPresent(dealGroupBase)){
            //局改 团详页显示的是 门店售价
            buildReassuredRepairPromoDetailModule(dealPromoPrice, promoDetailModule);
        }
    }

    private NationalSubsidyPromotionBar buildNationalSubsidyPromotionBar(NationalSubsidyInfo nationalSubsidyInfo) {
        if (Objects.isNull(nationalSubsidyInfo)) {
            return null;
        }
        NationalSubsidyPromotionBar bar = new NationalSubsidyPromotionBar();
        bar.setIcon("https://p0.meituan.net/ingee/bf84e4f45a3c8c36f3db1affc2fce2f31282.png");
        bar.setPromotionDescPrefix("立减");
        bar.setPromotionDesc(nationalSubsidyInfo.getPromo());
        bar.setPromotionDescSuffix("元");
        bar.setIssueStatus(nationalSubsidyInfo.getIssueStatus());
        bar.setButtonText(nationalSubsidyInfo.getIssueStatus() == 0 ? "领取" : StringUtils.EMPTY);
        return bar;
    }

    public String convertPromoTag(PromoDTO promoDTO) {
        if (promoDTO == null) {
            return null;
        }
        if (promoDTO.getIdentity() == null || promoDTO.getIdentity().getPromoShowType() == null) {
            return "团购优惠";
        }
        switch (promoDTO.getIdentity().getPromoShowType()) {
            case "DISCOUNT_SELL":
            {
                Boolean control = Lion.getBoolean(Environment.getAppName(), "com.sankuai.dzshoppingguide.detail.trademodule.medical.promo.tag.control", false);
                //医疗特殊逻辑，特惠促销展示为商家立减
                if(control && !ObjectUtils.isEmpty(promoDTO.getIdentity().getPromoTypeDesc())
                        && promoDTO.getIdentity().getPromoTypeDesc().contains("商家立减")) {
                    return "商家立减";
                }
                return "特惠促销";
            }
            case "MT_SUBSIDY":
                return "美团补贴";
            case "NEW_CUSTOMER_DISCOUNT":
                return "新客特惠";
            case "MEMBER_BENEFITS":
                return "会员优惠";
            case "NEW_MEMBER_BENEFITS":
                return "新会员优惠";
            case "PLATFORM_COUPON":
                return "美团券";
            case "MERCHANT_COUPON":
                return "商家券";
            case "GOVERNMENT_CONSUME_COUPON":
                return "政府消费券";
            case "DEAL_PROMO":
                return "团购优惠";
            case "PRESALE_PROMO":
                return "预售优惠";
            case "MAGICAL_MEMBER_PLATFORM_COUPON":
                return DEFAULT_VIP_COUPON_DESC;
            case "SECOND_KILL":
                return "限时秒杀";
            case "STATE_SUBSIDIES_PROMO":
                return "国家补贴";
            default:
                return "团购优惠";
        }
    }

    public String convertPromoDesc(PromoDTO promoDTO,PromoDetailModule promoDetailModule,String promoTag) {
        try {
            // 国家补贴
            if (Objects.nonNull(promoDTO) && Objects.nonNull(promoDTO.getIdentity())
                    && StringUtils.equals(promoDTO.getIdentity().getPromoShowType(), "STATE_SUBSIDIES_PROMO")) {
                    return promoDTO.getPromoTextDTO().getSubTitle();
            }
            if (promoDTO == null || StringUtils.isBlank(promoDTO.getExtendDesc()) || promoDTO.getIdentity() == null || StringUtils.isBlank(promoDTO.getIdentity().getPromoShowType()) || promoDetailModule == null) {
                return "团购优惠";
            }
            if (!StringUtils.equals(promoDTO.getIdentity().getPromoShowType(), "MAGICAL_MEMBER_PLATFORM_COUPON")) {
                // 非神券,走去除开头的逻辑
                if (promoDTO.getPromoTextDTO() != null) {
                    List<String> couponList = Arrays.asList("GOVERNMENT_CONSUME_COUPON","MERCHANT_COUPON","PLATFORM_COUPON");
                    if (couponList.contains(promoDTO.getPromoTextDTO().getPromoDivideType())) {
                        return subtractPromoDesc(promoDTO.getPromoTextDTO().getTitle(), promoTag);
                    }
                }
                return subtractPromoDesc(promoDTO.getExtendDesc(), promoTag);
            }

            BigDecimal minConsumptionAmount = promoDTO.getMinConsumptionAmount();
            BigDecimal amount = promoDTO.getAmount();
            if (minConsumptionAmount == null || amount == null) {
                return promoDTO.getExtendDesc();
            }

            if (minConsumptionAmount.compareTo(BigDecimal.ZERO) == 0) {
                // 无门槛的神券
                return "无门槛";
            }

            // 到这个地方就是有门槛的了
            return String.format("满%s元减%s元",PriceHelper.dropLastZero(minConsumptionAmount), PriceHelper.dropLastZero(amount));
        } catch (Exception e) {
            log.error("convertPromoDesc error",e);
            return promoDTO.getExtendDesc() == null ? "团购优惠" : promoDTO.getExtendDesc();
        }
    }

    public String subtractPromoDesc(String promoDesc ,String promoTag) {
        if (StringUtils.isBlank(promoTag) || StringUtils.isBlank(promoDesc)) {
            return promoDesc;
        }
        if (promoDesc.startsWith(promoTag + "，")) {
            return promoDesc.replace(promoTag + "，", "");
        }
        return promoDesc;
    }

    private void buildReassuredRepairPromoDetailModule(PriceDisplayDTO priceDisplayDTO, PromoDetailModule promoDetailModule) {
        promoDetailModule.setFinalPrice(priceDisplayDTO.getMarketPrice() == null ? "0.01" : PriceHelper.dropLastZero(priceDisplayDTO.getMarketPrice()));

        promoDetailModule.setMarketPrice(StringUtils.EMPTY);
        promoDetailModule.setBestPromoDetails(null);
    }



    // 需要展示拼团价的场景
    private boolean isPinTuanStyle(CostEffectivePinTuan costEffectivePinTuan) {
        if (CostEffectivePinTuanUtils.isCePinTuaScene(costEffectivePinTuan)) {
            if (CostEffectivePinTuanUtils.activePinTuan(costEffectivePinTuan)){
                return false;
            }
            return true;
        }
        return false;
    }

    private boolean needHideMarketPrice(Long categoryId) {
        List<Long> list = Lion.getList("com.sankuai.dzu.tpbase.dztgdetailweb", "com.sankuai.dzu.tpbase.dztgdetailweb.hidemarketprice.categoryid.config", Long.class, Lists.newArrayList());
        return list.contains(categoryId);
    }

    /**
     * 广平分销场景优惠展示逻辑
     */
    private PromoDetailModule buildOdpPromoDetailInfo(ProductBaseInfo dealGroupBase, PriceDisplayDTO odpPrice) {
        PromoDetailModule promoDetailModule = new PromoDetailModule();
        BigDecimal dealGroupPrice = new BigDecimal(dealGroupBase.getPrice().getSalePrice());
        BigDecimal marketPrice = new BigDecimal(dealGroupBase.getPrice().getMarketPrice());
        BigDecimal promoPrice = odpPrice != null ? odpPrice.getPrice() : dealGroupPrice;

        String marketPriceStr = PriceHelper.format(marketPrice);
        String promoPriceStr = PriceHelper.format(promoPrice);

        promoDetailModule.setMarketPrice(marketPriceStr);
        promoDetailModule.setPromoPrice(promoPriceStr);
        return promoDetailModule;
    }

    public void buildFreeDealPromoDetailInfo(ProductBaseInfo dealGroupBase, PromoDetailModule promoDetailModule) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.ParallDealBuilderProcessor.buildFreeDealPromoDetailInfo(DealCtx,DealGroupPBO)");
        if (promoDetailModule == null) {
            promoDetailModule = new PromoDetailModule();
        }
        // 价格信息
        BigDecimal dealGroupPrice = new BigDecimal(dealGroupBase.getPrice().getSalePrice());
        String dealGroupPriceStr = PriceHelper.format(dealGroupPrice);
        // promoDetailModule.setDealGroupPrice(dealGroupPriceStr);
        promoDetailModule.setFinalPrice(dealGroupPriceStr);
        FreeDealConfig freeDealConfig = DealCtxUtils.getFreeDealConfig(dealGroupBase.getCategory().getCategoryId());
        if (freeDealConfig != null && freeDealConfig.isShowMarketPrice()) {
            BigDecimal marketPrice = new BigDecimal(dealGroupBase.getPrice().getMarketPrice());
            promoDetailModule.setMarketPrice(PriceHelper.formatPrice(marketPrice));
        }

    }

    public void buildCardStylePromoDetailInfo(ProductDetailPageRequest request,
                                              CityIdMapper cityIdMapper,
                                              ProductBaseInfo dealGroupBase,
                                              PromoDetailModule promoDetailModule,
                                              PriceDisplayDTO normalPrice,
                                              List<DealGift> dealGifts,
                                              BatchExProxyCouponResponseDTO batchExProxyCouponResponseDTO,
                                              LeadsInfoResult leadsInfoResult,
                                              CostEffectivePinTuan costEffectivePinTuan,
                                              PriceDisplayDTO costEffectivePrice,
                                              PriceDisplayDTO idlePromoPrice,
                                              PinProductBrief pinProductBrief,
                                              BigDecimal pinPoolPromoAmount,
                                              PriceDisplayDTO dealPromoPrice,
                                              ShopInfo shopInfo,
                                              ShopIdMapper shopIdMapper,
                                              SkuDefaultSelect skuDefaultSelect) {
        //美团放心改不需要这个信息
        if (ReassuredRepairUtil.isTagPresent(dealGroupBase)) {
            return;
        }

        if (promoDetailModule == null) {
            return;
        }
        // 0元预约单不展示任何优惠信息
        if (DealCtxUtils.isFreeDeal(dealGroupBase) && DealCtxUtils.getFreeDealType(dealGroupBase.getCategory().getCategoryId()) != null) {
            return;
        }
        // 留资型团单且（超值特惠套餐或小程序）不展示任何优惠活动
        if (DealUtils.isLeadsDeal(dealGroupBase) && (DealCtxUtils.isHitSpecialValueDeal(leadsInfoResult) || !(DealCtxUtils.judgeMainApp(request.getClientTypeEnum())))) {
            return;
        }
        promoDetailModule.setExposureList(buildPromoExposureInfo(batchExProxyCouponResponseDTO));
        promoDetailModule.setCouponList(buildCouponList(batchExProxyCouponResponseDTO, normalPrice, request.getShepherdGatewayParam().getMrnVersion()));
        promoDetailModule.setPromoActivityList(buildPromoActivityList(
                request,
                skuDefaultSelect,
                dealGroupBase,
                cityIdMapper,
                costEffectivePinTuan,
                costEffectivePrice,
                normalPrice,
                idlePromoPrice,
                batchExProxyCouponResponseDTO,
                pinProductBrief,
                pinPoolPromoAmount,
                shopInfo,
                shopIdMapper
        ));
        promoDetailModule.setInflateCounpon(buildInflateCoupon(request, dealGroupBase, dealPromoPrice));

        couponFilter(promoDetailModule);

        List<String> promoAbstractList = promoDetailModuleBuilderService.buildPromoAbstractList(promoDetailModule, request.getClientTypeEnum(), normalPrice, dealGifts);
        promoDetailModule.setPromoAbstractList(promoAbstractList);
    }

    private List<DztgPromoExposureInfoVO> buildPromoExposureInfo(BatchExProxyCouponResponseDTO batchExProxyCouponResponseDTO) {
        try {
            if (batchExProxyCouponResponseDTO == null || batchExProxyCouponResponseDTO.getPromoExposureInfoList() == null) {
                return new ArrayList<>();
            }
            List<PromoExposureInfo> promoExposureInfoList = batchExProxyCouponResponseDTO.getPromoExposureInfoList();

            return promoExposureInfoList.stream()
                    .filter(Objects::nonNull)
                    .map(exposure -> {
                        DztgPromoExposureInfoVO dztgPromoExposureInfoVO = new DztgPromoExposureInfoVO();
                        dztgPromoExposureInfoVO.setFlowId(exposure.getFlowId());
                        dztgPromoExposureInfoVO.setResourceActivityId(exposure.getResourceActivityId());
                        dztgPromoExposureInfoVO.setActivityId(exposure.getActivityId());
                        dztgPromoExposureInfoVO.setMaterialId(exposure.getMaterialId());
                        dztgPromoExposureInfoVO.setRowKey(exposure.getRowKey());
                        dztgPromoExposureInfoVO.setCouponType(exposure.getCouponType());
                        dztgPromoExposureInfoVO.setAmount(exposure.getAmount());
                        dztgPromoExposureInfoVO.setTitle(exposure.getTitle());
                        dztgPromoExposureInfoVO.setCouponValueType(exposure.getCouponValueType());
                        dztgPromoExposureInfoVO.setSubTitle(exposure.getSubTitle());
                        dztgPromoExposureInfoVO.setCanAssign(exposure.getCanAssign());
                        dztgPromoExposureInfoVO.setTimeDesc(exposure.getTimeDesc());
                        dztgPromoExposureInfoVO.setTimeSubDesc(exposure.getTimeSubDesc());
                        dztgPromoExposureInfoVO.setUseEndTime(exposure.getUseEndTime());

                        return dztgPromoExposureInfoVO;
                    })
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("buildPromoExposureInfo error,", e);
        }
        return null;
    }

    private List<DztgCouponInfo> buildCouponList(BatchExProxyCouponResponseDTO batchExProxyCouponResponseDTO, PriceDisplayDTO normalPrice, String mrnVersion) {
        try {
            // 从优惠券团队拿到的券信息
            List<DztgCouponInfo> couponList = new ArrayList<>();
            if (batchExProxyCouponResponseDTO != null) {
                List<PromoCouponInfo> promoCouponInfoList = batchExProxyCouponResponseDTO.getPromoCouponInfoList();
                List<PromoCouponInfo> financialCouponInfoList = batchExProxyCouponResponseDTO.getFinancialCouponInfoList();

                // 从优惠券团队拿到的券信息
                couponList = Stream.of(promoCouponInfoList, financialCouponInfoList)
                        .filter(Objects::nonNull)
                        .flatMap(List::stream)
                        .map(this::convertPromoCouponInfo2DztgCouponInfo)
                        .collect(Collectors.toList());
            }
            List<Long> couponGroupIdList = couponList.stream().map(c -> c.getCouponGroupId()).collect(Collectors.toList());

            // 从优惠代理服务拿到的券信息，两者取并集，如果重复，以优惠券团队为准
            List<DztgCouponInfo> extraCoupon = new ArrayList<>();

            if (normalPrice != null) {
                List<PromoDTO> usedPromos = normalPrice.getUsedPromos();
                if (usedPromos != null) {
                    extraCoupon = usedPromos.stream()
                            .filter(this::isCoupon)
                            .map(usedPromo -> promotionDetailBuilderService.convertPromoDTO2DztgCouponInfo(usedPromo, mrnVersion))
                            .filter(Objects::nonNull)
                            .filter(dztgCouponInfo -> !couponGroupIdList.contains(dztgCouponInfo.getCouponGroupId()))
                            .collect(Collectors.toList());
                }
            }


            // 合并couponList和extraCoupon，如果couponGroupId重复，只保留couponList中的
            List<DztgCouponInfo> result = Stream.concat(couponList.stream(), extraCoupon.stream())
                    .collect(Collectors.toList());
            result.sort(Comparator.comparing(DztgCouponInfo::getStatus)
                    .thenComparing((a, b) -> {
                        String aAmountCornerMark = a.getAmountCornerMark();
                        String bAmountCornerMark = b.getAmountCornerMark();
                        if ("元".equals(aAmountCornerMark) && "折".equals(bAmountCornerMark)) {
                            return -1;
                        } else if ("折".equals(aAmountCornerMark) && "元".equals(bAmountCornerMark)) {
                            return 1;
                        } else {
                            return 0;
                        }
                    })
                    .thenComparing(DztgCouponInfo::getSourceTag)
                    .thenComparing((a, b) -> {
                        double aAmount = Double.parseDouble(a.getAmount());
                        double bAmount = Double.parseDouble(b.getAmount());
                        return Double.compare(bAmount, aAmount);
                    }));

            return result;
        } catch (Exception e) {
            log.error("buildCouponList error, ", e);
        }
        return null;
    }

    private boolean isCoupon(PromoDTO promoDTO) {
        if (promoDTO == null) {
            return false;
        }
        if (promoDTO.getIdentity() == null) {
            return false;
        }
        if (Objects.equals(promoDTO.getIdentity().getPromoType(), PromoTypeEnum.COUPON.getType()) && promoDTO.getIdentity().getPromoId() != 0) {
            return true;
        }
        if (Objects.equals(promoDTO.getIdentity().getPromoType(), PromoTypeEnum.GOVERNMENT_CONSUME_COUPON.getType())
                && org.apache.commons.lang3.StringUtils.isNumeric(promoDTO.getCouponGroupId())) {
            return true;
        }
        return false;
    }

    private DztgCouponInfo convertPromoCouponInfo2DztgCouponInfo(PromoCouponInfo coupon) {
        if (coupon == null) {
            return null;
        }
        DztgCouponInfo dztgCouponInfo = new DztgCouponInfo();
        dztgCouponInfo.setCouponGroupId(coupon.getCouponGroupId());
        dztgCouponInfo.setTitle(coupon.getTitle());
        dztgCouponInfo.setAmount(coupon.getAmount());
        dztgCouponInfo.setAmountCornerMark(coupon.getAmountCornerMark());
        dztgCouponInfo.setTimeDesc(coupon.getTimeDesc());
        dztgCouponInfo.setAmountDesc(coupon.getAmountDesc());
        dztgCouponInfo.setCouponType(coupon.getCouponType());
        dztgCouponInfo.setSourceTag(coupon.getSourceTag());
        dztgCouponInfo.setSpecificTag(coupon.getSpecificTag());
        dztgCouponInfo.setTagUrl(coupon.getTagUrl());
        dztgCouponInfo.setStatus(coupon.getStatus());
        dztgCouponInfo.setIconText(coupon.getIconText());
        dztgCouponInfo.setIconUrl(coupon.getIconUrl());
        dztgCouponInfo.setPromoCouponButton(buildDztgCouponButton(coupon.getPromoCouponButton()));

        return dztgCouponInfo;
    }

    private DztgCouponButton buildDztgCouponButton(PromoCouponButton promoCouponButton) {
        if (promoCouponButton == null) {
            return null;
        }
        DztgCouponButton dztgCouponButton = new DztgCouponButton();
        dztgCouponButton.setTitle(promoCouponButton.getTitle());
        dztgCouponButton.setClickUrl(promoCouponButton.getClickUrl());
        dztgCouponButton.setActionType(promoCouponButton.getActionType());

        return dztgCouponButton;
    }

    private List<PromoActivityInfoVO> buildPromoActivityList(ProductDetailPageRequest request,
                                                             SkuDefaultSelect skuDefaultSelect,
                                                             ProductBaseInfo dealGroupBase,
                                                             CityIdMapper cityIdMapper,
                                                             CostEffectivePinTuan costEffectivePT,
                                                             PriceDisplayDTO costEffectivePrice,
                                                             PriceDisplayDTO normalPrice,
                                                             PriceDisplayDTO idlePromoPrice,
                                                             BatchExProxyCouponResponseDTO batchExProxyCouponResponseDTO,
                                                             PinProductBrief pinProductBrief,
                                                             BigDecimal pinPoolPromoAmount,
                                                             ShopInfo shopInfo,
                                                             ShopIdMapper shopIdMapper) {
        try {
            List<PromoActivityInfoVO> result = new ArrayList<>();

            // 平台拼团
            PromoActivityInfoVO costEffectivePinTuan = buildCostEffectivePintuan(costEffectivePT, costEffectivePrice);
            if (costEffectivePinTuan != null) {
                result.add(costEffectivePinTuan);
            }
            if (batchExProxyCouponResponseDTO == null || batchExProxyCouponResponseDTO.getPromoReturnInfoList() == null) {
                return result;
            }
            List<PromoReturnInfo> returnInfoList = batchExProxyCouponResponseDTO.getPromoReturnInfoList();

            // 闲时特惠
            PromoActivityInfoVO idlePromo = buildIdlePromo(
                    request, skuDefaultSelect, shopInfo, shopIdMapper, dealGroupBase, cityIdMapper, idlePromoPrice
            );
            if (idlePromo != null) {
                result.add(idlePromo);
            }

            // 商户多份立减
            PromoActivityInfoVO buyMoreReduction = buildBuyMoreReduction(normalPrice);
            if (buyMoreReduction != null) {
                result.add(buyMoreReduction);
            }

            // 平台多买多折
            PromoActivityInfoVO buyMoreDiscount = buildBuyMoreDiscount(normalPrice);
            if (buyMoreDiscount != null) {
                result.add(buyMoreDiscount);
            }

            // 商家拼团
            PromoActivityInfoVO pintuan = buildPintuanActivity(shopInfo, request, normalPrice, pinProductBrief, pinPoolPromoAmount);
            if (pintuan != null && costEffectivePinTuan == null) {
                result.add(pintuan);
            }

            // 返券、返礼、金融券活动
            List<PromoActivityInfoVO> couponActivityList = returnInfoList.stream()
                    .filter(Objects::nonNull)
                    .map(returnInfo -> {
                        PromoActivityInfoVO promoActivityInfoVO = new PromoActivityInfoVO();
                        promoActivityInfoVO.setBonusType(returnInfo.getBonusType());
                        promoActivityInfoVO.setText(PromoHelper.getTextFromJson(returnInfo.getText(),"text"));
                        promoActivityInfoVO.setStyle(returnInfo.getStyle());
                        promoActivityInfoVO.setLeadUrl(returnInfo.getLeadUrl());
                        promoActivityInfoVO.setShortText(PromoHelper.getTextFromJson(returnInfo.getText(),"text"));

                        return promoActivityInfoVO;
                    })
                    .collect(Collectors.toList());

            result.addAll(couponActivityList);
            return result;
        } catch (Exception e) {
            log.error("buildPromoActivityList error,", e);
        }

        return null;
    }

    public PromoActivityInfoVO buildCostEffectivePintuan(CostEffectivePinTuan costEffectivePinTuan, PriceDisplayDTO costEffectivePrice) {
        if (!CostEffectivePinTuanUtils.enhancedStyle(costEffectivePinTuan) || Objects.isNull(costEffectivePinTuan.getGroupSuccCountMin())) {
            return null;
        }

        try {
            PriceDisplayDTO price = costEffectivePrice;
            if (price == null) {
                return null;
            }

            PromoActivityInfoVO promoActivityInfoVO = new PromoActivityInfoVO();
            promoActivityInfoVO.setBonusType("拼团");
            promoActivityInfoVO.setText(getText(costEffectivePinTuan, price));
            promoActivityInfoVO.setStyle(0);
            promoActivityInfoVO.setShortText(getText(costEffectivePinTuan, price));
            return promoActivityInfoVO;
        } catch (Exception e) {
            log.error("buildBuyMoreReduction error, ", e);
        }
        return null;
    }

    private String getText(CostEffectivePinTuan costEffectivePinTuan, PriceDisplayDTO price) {
        int textSize = 11;
        RichText.TextItem defaultTextItem = new RichText.TextItem();
        defaultTextItem.setStrikethrough(false);
        defaultTextItem.setTextcolor("#222222");
        defaultTextItem.setTextsize(textSize);
        defaultTextItem.setTextstyle(TextStyle.DEFAULT.getStyle());
        defaultTextItem.setUnderline(false);

        RichText.TextItem highlightTextItem = new RichText.TextItem();
        highlightTextItem.setStrikethrough(false);
        highlightTextItem.setTextcolor("#222222");
        highlightTextItem.setTextsize(textSize);
        highlightTextItem.setTextstyle(TextStyle.DEFAULT.getStyle());
        highlightTextItem.setUnderline(false);

        RichText richText=new RichText();
        richText.getTextItemList().add(RichTextUtil.buildTextItem(costEffectivePinTuan.getGroupSuccCountMin() + "人团，每人到手价", highlightTextItem));
        richText.getTextItemList().add(RichTextUtil.buildTextItem("￥" + price.getPrice(), highlightTextItem));
        return richText.toString();
    }

    public InflateCounponDTO buildInflateCoupon(ProductDetailPageRequest request,
                                                ProductBaseInfo dealGroupBase,
                                                PriceDisplayDTO dealPromoPrice) {
        InflateCounponDTO inflateCounponDTO = new InflateCounponDTO();
        PriceDisplayDTO dealPrice = dealPromoPrice;
        Map<String, String> metricTags = Maps.newHashMap();
        metricTags.put("pageSource", request.getPageSource());
        metricTags.put("categoryId", String.valueOf(dealGroupBase.getCategory().getCategoryId()));
        metricTags.put("cityId", String.valueOf(request.getGpsCityId()));
        metricTags.put("isMt", String.valueOf(request.getClientTypeEnum().isMtClientType()));
        metricTags.put("clientType", String.valueOf(getCatBusinessClientType(request.getClientTypeEnum())));
        Cat.logMetricForCount("magicalCoupon_all", metricTags);
        if(dealPrice == null) {
            return null;
        }
        Map<String, String> extendDisplayInfoMap = dealPrice.getExtendDisplayInfo();
        if(MapUtils.isEmpty(extendDisplayInfoMap)) {
            return null;
        }

        //神会员标签文案
        String magicalMemberCouponLabel = extendDisplayInfoMap.get(ExtendDisplayInfoKeyEnum.MAGICAL_MEMBER_COUPON_LABEL.getKey());

        MagicalMemberTagTextDTO memberTagText = MagicalMemberTagTextUtils.bestMagicalMemberTagTextDTO(Lists.newArrayList(magicalMemberCouponLabel));
        if(memberTagText == null) {
            return null;
        }

        Cat.logMetricForCount("magicalCoupon_hasData", metricTags);
        inflateCounponDTO.setLogoIcon(memberTagText.getMagicalMemberCouponTag()); //神会员标签
        List<SptDyeUtil.DyeTraceParam> traceParams = Lists.newArrayList();
        // step1 创建染色场景参数对象 (主场景、子场景)
        SptDyeUtil.DyeTraceParam flowEntranceParam = SptDyeUtil.DyeTraceParam.ofFlowEntrance("MAGIC_MEMBER", "default");
        traceParams.add(flowEntranceParam);
        // step1.5 根据定位城市id创建染色对象
        if (request.getClientTypeEnum().isMtClientType()) {
            SptDyeUtil.DyeTraceParam magicMemberMtCityParam = SptDyeUtil.DyeTraceParam.ofMagicMemberMtCity(
                    String.valueOf(request.getGpsCityId()));
            traceParams.add(magicMemberMtCityParam);
        } else {
            SptDyeUtil.DyeTraceParam magicMemberDpCityParam = SptDyeUtil.DyeTraceParam.ofMagicMemberDpCity(
                    String.valueOf(request.getGpsCityId()));
            traceParams.add(magicMemberDpCityParam);
        }
        // step2 调用工具类方法染色
        SptDyeUtil.putDyeTraceParams(traceParams);

        inflateCounponDTO.setCouponDesc(memberTagText.getInflateShowText()); //膨胀券文案
        inflateCounponDTO.setMaxInflateMoney(memberTagText.getReduceMoney()); //膨胀金额文案
        inflateCounponDTO.setCouponStatus(memberTagText.getStatus()); //膨胀状态
        MagicalMemberTagShowTypeEnum magicMemberNumEnum = MagicalMemberTagShowTypeEnum.findByValue(memberTagText.getShowType());
        if(magicMemberNumEnum != null) {
            if(null != MagicalMemberTagShowTypeEnum.findByValue(memberTagText.getShowType())) {
                int code = Objects.requireNonNull(MagicalMemberTagShowTypeEnum.findByValue(memberTagText.getShowType())).getCode();
                inflateCounponDTO.setShowType(String.valueOf(code)); //展示标签类型
                if(themeLists.contains(code)) {
                    inflateCounponDTO.setLabelTheme(MAGIC_NORMAL_TAG_THEME);
                    if(StringUtils.isNotBlank(memberTagText.getInflateShowText())) {
                        inflateCounponDTO.setLogoIcon("https://p0.meituan.net/ingee/88acab73b52c30f7c987d11b11bc13813516.png");

                    }
                } else {
                    inflateCounponDTO.setLabelTheme(MAGIC_VIP_TAG_THEME);
                    if(StringUtils.isNotBlank(memberTagText.getInflateShowText())) {
                        inflateCounponDTO.setLogoIcon("https://p0.meituan.net/ingee/ccf230b75315f1f3bec4eeef9cd988de2508.png");
                    }
                }
            }
        }
        inflateCounponDTO.setCouponWalletInfo(extendDisplayInfoMap.get(ExtendDisplayInfoKeyEnum.COUPON_WALLET_INFO.getKey())); //券包模块信息
        inflateCounponDTO.setMmcPkgResult(toMmcPkgResult(extendDisplayInfoMap.get(ExtendDisplayInfoKeyEnum.COMMON_EXPERIMENT_ID_SET.getKey()))); // 神券组件实验结果
        inflateCounponDTO.setCouponGroupIdList(buildCouponGroupIdList(inflateCounponDTO.getCouponWalletInfo()));
        //组装已领券列表
        CouponItemPackageDTO couponItemPackage = buildCouponItems(dealPrice, request.getUserId());
        inflateCounponDTO.setCouponItems(couponItemPackage.getCouponItems());
        inflateCounponDTO.setCouponIds(couponItemPackage.getCouponIds());
        inflateCounponDTO.setNibBiz(getNibBiz(dealGroupBase.getCategory().getCategoryId()));

        return inflateCounponDTO;
    }
    private List<Integer> toMmcPkgResult(String value) {
        if (StringUtils.isBlank(value)) {
            return null;
        }
        try {
            return JSONObject.parseArray(value, Integer.class);
        } catch (Exception e) {
            log.error("toMmcPkgResult, jSON parseArray failed, value={}", value, e);
        }
        return null;
    }

    public List<String> buildCouponGroupIdList(String couponWalletInfoStr) {
        List<String> result = Lists.newArrayList();
        try {
            if(StringUtils.isNotBlank(couponWalletInfoStr)) {
                JSONObject jsonObject = JSONObject.parseObject(couponWalletInfoStr);
                if(jsonObject.get("couponPackageList") != null) {
                    JSONArray jsonArray = (JSONArray) jsonObject.get("couponPackageList");
                    for (Object element : jsonArray) {
                        // 处理每个元素
                        JSONObject elementJson = (JSONObject) element;
                        String couponPackageId = (String) elementJson.get("couponPackageId");
                        if (StringUtils.isNotBlank(couponPackageId)) {
                            result.add(couponPackageId);
                        }
                    }
                }
            }
        } catch (Exception e) {
            Cat.logError(e);
        }
        return result;
    }

    public static String getNibBiz(long categoryId) {
        Map<String, String> categoryIdMap = Lion.getMap("com.sankuai.dzu.tpbase.dztgdetailweb.svip.categoryid", String.class);
        return categoryIdMap.getOrDefault(String.valueOf(categoryId), null);
    }

    public static int getCatBusinessClientType(ClientTypeEnum clientTypeEnum) {
        if (clientTypeEnum == null) {
            return ClientTypeEnum.UNKNOWN.getCode();
        }
        if (clientTypeEnum == ClientTypeEnum.DP_APP
                || clientTypeEnum == ClientTypeEnum.DP_XCX
                || clientTypeEnum == ClientTypeEnum.MT_APP
                || clientTypeEnum == ClientTypeEnum.MT_XCX) {
            return clientTypeEnum.getCode();
        }
        return ClientTypeEnum.UNKNOWN.getCode();
    }


    public CouponItemPackageDTO buildCouponItems(PriceDisplayDTO dealPrice, long userId) {
        CouponItemPackageDTO couponItemPackage = new CouponItemPackageDTO();
        List<CouponItemInfoDTO> couponItems = Lists.newArrayList();
        List<String> couponIds = Lists.newArrayList();

        Map<Integer, List<PromoDTO>> pricePromoInfoMap = dealPrice.getPricePromoInfoMap();
        if(MapUtils.isEmpty(pricePromoInfoMap) || CollectionUtils.isEmpty(pricePromoInfoMap.get(PricePromoInfoTypeEnum.MAGICAL_MEMBER.getType()))) {
            return couponItemPackage;
        }

        List<PromoDTO> promoDTOs = pricePromoInfoMap.get(PricePromoInfoTypeEnum.MAGICAL_MEMBER.getType());

        for(PromoDTO promoDTO : promoDTOs) {
            CouponItemInfoDTO couponItem = new CouponItemInfoDTO();
            couponItem.setEndTime(promoDTO.getEndTime().getTime());
            if(StringUtils.isNotBlank(promoDTO.getCouponId())) {
                couponIds.add(promoDTO.getCouponId());
            }
            //券码
            couponItem.setCouponCode(promoDTO.getCouponId());
            //券批次id
            couponItem.setApplyId(promoDTO.getCouponGroupId());
            Pair<String, String> pair = getValidTimeText(promoDTO.getStartTime(), promoDTO.getEndTime());
            if(pair != null ) {
                //券到期时间文案
                couponItem.setCouponValidTimeText(pair.getKey());
                //到期有效时间
                couponItem.setValidTime(pair.getValue());
            }

            //券金额
            couponItem.setCouponAmount(promoDTO.getAmount().toPlainString());
            couponItem.setAmount(promoDTO.getAmount());
            //券名称
            couponItem.setCouponName(promoDTO.getCouponTitle());
            //券状态 1-有效 2-无效 （根据开始和结束时间判断）
            couponItem.setValidStatus(getCouponValidStatus(promoDTO.getStartTime(), promoDTO.getEndTime()));
            //付费神券/免费神券
            couponItem.setCouponType(getCouponType(promoDTO.getPromotionExplanatoryTags()));
            //营销扩展信息
            Map<String, String> promotionOtherInfoMap = promoDTO.getPromotionOtherInfoMap();
            //膨胀前金额
            if(promoDTO.getMinConsumptionAmount() != null ) {
                couponItem.setOriginalRequiredAmount(promoDTO.getMinConsumptionAmount().multiply(new BigDecimal("100")).longValue());
            }

            Map<String, String> promotionDisplayTextMap = promoDTO.getPromotionDisplayTextMap();


            if(MapUtils.isNotEmpty(promotionDisplayTextMap)) {
                //顶部icon链接
                couponItem.setLogoIconUrl(promotionDisplayTextMap.get(PromotionTextEnum.topLeftIcon.getValue()));
                // 新顶部icon链接
                couponItem.setTopLeftNewIconInfo(promotionDisplayTextMap.get(PromotionTextEnum.topLeftNewIconInfo.getValue()));

                //膨胀按钮文案
                couponItem.setCouponButtonText(promotionDisplayTextMap.get(PromotionTextEnum.promotionButtonText.getValue()));
            }

            //膨胀后金额
            couponItem.setOriginalReduceAmount(promoDTO.getAmount().multiply(BigDecimal.valueOf(100)).longValue());

            //资产类型
            couponItem.setAssetType(Integer.parseInt(promotionOtherInfoMap.get(PromotionPropertyEnum.ASSET_TYPE.getValue())));

            if(couponItem.getAssetType() == 1) {
                //到家券用这个券id
                couponItem.setCouponCode(promotionOtherInfoMap.get(PromotionPropertyEnum.TSP_COUPON_ID.getValue()));
                couponItem.setApplyId(promotionOtherInfoMap.get(PromotionPropertyEnum.TSP_COUPON_GROUP_ID.getValue()));
            }

            //业务线
            couponItem.setBizLine(promotionOtherInfoMap.get(PromotionPropertyEnum.NIB_BIZ_LINE.getValue()));

            //实膨参数透传
            couponItem.setBizToken(promotionOtherInfoMap.get(PromotionPropertyEnum.BIZ_TOKEN.getValue()));

            //券是否可膨胀
            boolean canInflate = Boolean.parseBoolean(promotionOtherInfoMap.get(PromotionPropertyEnum.CAN_INFLATE.getValue()));
            couponItem.setCanInflate(canInflate?CanInflateEnum.CAN_INFLATE.getCode():CanInflateEnum.CAN_NOT_INFLATE.getCode());

            //券是否已膨胀
            boolean afterInflate = Boolean.parseBoolean(promotionOtherInfoMap.get(PromotionPropertyEnum.AFTER_INFLATE.getValue()));
            couponItem.setInflatedStatus(afterInflate?InflateStatusEnum.AFTER_INFLATE.getCode():InflateStatusEnum.NO_INFLATE.getCode());

            if(afterInflate) {
                //已膨胀后的神券就是普通券，门槛取基础信息字段
                couponItem.setThresholdDesc(buildCouponConditionText(promoDTO.getMinConsumptionAmount()));
                couponItem.setThresholdAmount(promoDTO.getMinConsumptionAmount());
            } else {
                if(canInflate) {
                    //未膨胀券的神券，
                    if(null != promotionOtherInfoMap.get(PromotionPropertyEnum.AFTER_INFLATE_REQUIRE_AMOUNT.getValue())) {
                        couponItem.setThresholdDesc(promoDTO.getPriceLimitDesc());
                        couponItem.setThresholdAmount(new BigDecimal(promotionOtherInfoMap.get(PromotionPropertyEnum.AFTER_INFLATE_REQUIRE_AMOUNT.getValue())));
                    }
                    if(null != promotionOtherInfoMap.get(PromotionPropertyEnum.MAX_INFLATE_MONEY.getValue())) {
                        //最高膨胀至XX
                        couponItem.setCouponDesc(buildCouponDesc(BigDecimal.valueOf(Double.parseDouble(promotionOtherInfoMap.get(PromotionPropertyEnum.MAX_INFLATE_MONEY.getValue()))/100.0).stripTrailingZeros()));
                    }
                } else {
                    couponItem.setThresholdDesc(buildCouponConditionText(promoDTO.getMinConsumptionAmount()));
                    couponItem.setThresholdAmount(promoDTO.getMinConsumptionAmount());
                }
            }
            if (promotionOtherInfoMap.get(PromotionPropertyEnum.QUERY_INFLATE_FLAG.getValue()) != null
                    && "true".equals(promotionOtherInfoMap.get(PromotionPropertyEnum.QUERY_INFLATE_FLAG.getValue()))) {
                // 1标识券已锁价
                couponItem.setQueryInflateFlag(1);
            }
            // 券聚合数量
            if (LionConfigUtils.hitCouponAggregateSwitch(userId)){
                couponItem.setCouponNum(getCouponNum(promotionOtherInfoMap));
            }
            couponItems.add(couponItem);
        }

        //券聚合（不聚合领塞、膨后的券）
        List<CouponItemInfoDTO> result = aggregateCouponProcess(couponItems, userId);
        //券的排序
        couponItems =  result.stream()
                .sorted(Comparator.comparing(CouponItemInfoDTO::getCanInflate)
                        .reversed()
                        .thenComparing(CouponItemInfoDTO::getQueryInflateFlag, Comparator.comparingInt(c -> c))
                        .thenComparing(CouponItemInfoDTO::getAmount, (price1,price2) -> -price1.compareTo(price2))
                        .thenComparing(CouponItemInfoDTO::getThresholdAmount)
                        .thenComparing(CouponItemInfoDTO::getCouponType)
                        .thenComparing(CouponItemInfoDTO::getEndTime))
                .collect(Collectors.toList());
        couponItemPackage.setCouponItems(couponItems);
        couponItemPackage.setCouponIds(couponIds);
        return couponItemPackage;
    }

    private static String buildCouponDesc(BigDecimal price) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.facade.rcf.processor.ParallDealBuilderProcessor.buildCouponDesc(java.math.BigDecimal)");
        return "最高膨胀至" + price.toPlainString();

    }

    public static int getCouponType(List<Integer> promotionExplanatoryTags) {
        if(promotionExplanatoryTags.contains(PromotionExplanatoryTagEnum.MAGICAL_MEMBER_COUPON.getCode())) {
            return PAY_COUPON.getCode();
        } else if (promotionExplanatoryTags.contains(PromotionExplanatoryTagEnum.MAGICAL_MEMBER_COUPON_FREE.getCode())) {
            return FREE_COUPON.getCode();
        } else {
            return UN_KNOWN.getCode();
        }
    }

    /**
     * 判断券是否在可用时间内
     * @param startTime
     * @param endTime
     * @return
     */
    public static int getCouponValidStatus(Date startTime, Date endTime) {
        Date nowDate = new Date();
        if(nowDate.after(startTime) && nowDate.before(endTime)) {
            return CouponValidStatusEnum.VALID.getCode(); //有效
        }
        return CouponValidStatusEnum.UN_VALID.getCode(); //无效
    }

    private static String buildCouponConditionText(BigDecimal price) {
        if(price.compareTo(BigDecimal.valueOf(0)) == 0) {
            return "无门槛";
        }

        return "满"+price.toPlainString()+"可用";
    }

    private static Pair<String, String> getValidTimeText(Date startTime, Date endTime) {
        try {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy/MM/dd");
            String startTimeStr = dateFormat.format(startTime);
            String endTimeStr = dateFormat.format(endTime);
            long currentTime = System.currentTimeMillis();
            if(currentTime > endTime.getTime()) {
                //已过期
                return new Pair<>(startTimeStr +" 至 " + endTimeStr, null);
            } else {
                if (endTime.getTime() - currentTime > 24 * 3600 * 1000) {
                    return new Pair<>(endTimeStr + " 到期", null);
                } else {
                    return new Pair<>("今日到期，仅剩" , String.valueOf(endTime.getTime()));
                }
            }
        } catch (Exception e) {
            Cat.logError(e);
        }
        return null;
    }

    /**
     * 券聚合逻辑
     * @param couponItems
     * @return
     */
    private List<CouponItemInfoDTO> aggregateCouponProcess(List<CouponItemInfoDTO> couponItems, long userId){
        // 新逻辑不用聚合
        if (LionConfigUtils.hitCouponAggregateSwitch(userId)){
            return couponItems;
        }
        // 走老逻辑
        return buildCouponItemInfos(couponItems);
    }

    /**
     * 获取券数量
     * @param promotionOtherInfoMap
     * @return
     */
    public static int getCouponNum(Map<String, String> promotionOtherInfoMap){
        try{
            String couponNum = promotionOtherInfoMap.get(PromotionPropertyEnum.COUPON_AGGREGATE_NUM.getValue());
            if (couponNum == null){
                return 1;
            }
            return Integer.parseInt(couponNum);
        }catch (Exception e){
            log.error("getCouponNum error, e{}", e);
            return 0;
        }
    }

    private List<CouponItemInfoDTO> buildCouponItemInfos(List<CouponItemInfoDTO> couponItems) {
        if(CollectionUtils.isEmpty(couponItems)) {
            return couponItems;
        }

        Map<String, List<CouponItemInfoDTO>> couponListMap = Maps.newHashMap();
        for(CouponItemInfoDTO couponItem : couponItems) {
            if(couponItem.getInflatedStatus() == InflateStatusEnum.AFTER_INFLATE.getCode()) {
                //膨胀后券
                couponListMap.put(String.valueOf(couponItem.getCouponCode()), Lists.newArrayList(couponItem));
            } else if(couponItem.getCouponType() == FREE_COUPON.getCode()) {
                //免费券
                couponListMap.put(String.valueOf(couponItem.getCouponCode()), Lists.newArrayList(couponItem));
            } else {
                String key = couponItem.getApplyId() + couponItem.getCouponType() + couponItem.getCouponName() + couponItem.getCouponAmount() + couponItem.getThresholdDesc() + couponItem.getCouponValidTimeText();
                if(couponListMap.get(key) == null) {
                    couponListMap.put(key, Lists.newArrayList(couponItem));
                } else {
                    List<CouponItemInfoDTO> couponItemInfos = couponListMap.get(key);
                    couponItemInfos.add(couponItem);
                }
            }
        }

        List<CouponItemInfoDTO> result = Lists.newArrayList();

        for(Map.Entry<String, List<CouponItemInfoDTO>> entry : couponListMap.entrySet()) {
            List<CouponItemInfoDTO> list = entry.getValue();
            CouponItemInfoDTO couponItem = list.get(0);
            couponItem.setCouponNum(list.size());
            result.add(couponItem);
        }

        return result;
    }

    private PromoActivityInfoVO buildIdlePromo(final ProductDetailPageRequest request,
                                               final SkuDefaultSelect skuDefaultSelect,
                                               final ShopInfo shopInfo,
                                               final ShopIdMapper shopIdMapper,
                                               final ProductBaseInfo dealGroupBase,
                                               final CityIdMapper cityIdMapper,
                                               final PriceDisplayDTO idlePromoPrice) {
        try {
            if (idlePromoPrice == null || CollectionUtils.isEmpty(idlePromoPrice.getUsedPromos())) {
                return null;
            }

            PromoActivityInfoVO promoActivityInfoVO = new PromoActivityInfoVO();
            promoActivityInfoVO.setBonusType("限时");
            String consumeTimeDesc = idlePromoPrice.getUsedPromos().get(0).getConsumeTimeDesc() == null ? "" : idlePromoPrice.getUsedPromos().get(0).getConsumeTimeDesc();
            String price = PriceHelper.dropLastZero(idlePromoPrice.getPrice());
            String text = String.format("限时特惠，%s ¥%s/次", consumeTimeDesc, price);
            promoActivityInfoVO.setText(text);

            promoActivityInfoVO.setStyle(0);
            promoActivityInfoVO.setLeadUrl(UrlHelper.getIdleHoursBuyUrl(
                    request, skuDefaultSelect, shopInfo, shopIdMapper, dealGroupBase, cityIdMapper.getMtCityId()
            ));
            String shortText = idlePromoPrice.getUsedPromos().get(0).getIdentity() == null ? null : idlePromoPrice.getUsedPromos().get(0).getIdentity().getPromoTypeDesc();
            if (StringUtils.isBlank(shortText)) {
                String promoAmount = PriceHelper.dropLastZero(idlePromoPrice.getPromoAmount());
                shortText = String.format("工作日立减%s元", promoAmount);
            }
            promoActivityInfoVO.setShortText(shortText);

            return promoActivityInfoVO;
        } catch (Exception e) {
            log.error("buildIdlePromo error, ", e);
        }
        return null;
    }

    private PromoActivityInfoVO buildBuyMoreReduction( PriceDisplayDTO normalPrice) {
        try {
            PriceDisplayDTO price = normalPrice;
            if (price == null || CollectionUtils.isEmpty(price.getMorePromos())) {
                return null;
            }

            PromoDTO buyMoreReduction = null;
            for (PromoDTO promoDTO : price.getMorePromos()) {
                if (promoDTO.getIdentity() != null && "BUY_MORE_REDUCTION".equals(promoDTO.getIdentity().getPromoShowType())) {
                    buyMoreReduction = promoDTO;
                    break;
                }
            }

            if (buyMoreReduction == null) {
                return null;
            }

            PromoActivityInfoVO promoActivityInfoVO = new PromoActivityInfoVO();
            promoActivityInfoVO.setBonusType("促销");
            promoActivityInfoVO.setText(buyMoreReduction.getExtendDesc());
            promoActivityInfoVO.setStyle(0);
            promoActivityInfoVO.setShortText(buyMoreReduction.getTag());

            return promoActivityInfoVO;
        } catch (Exception e) {
            log.error("buildBuyMoreReduction error, ", e);
        }
        return null;
    }

    private PromoActivityInfoVO buildBuyMoreDiscount(PriceDisplayDTO normalPrice) {
        try {
            PriceDisplayDTO price = normalPrice;
            if (price == null || CollectionUtils.isEmpty(price.getMorePromos())) {
                return null;
            }

            PromoDTO buyMoreDiscount = null;
            for (PromoDTO promoDTO : price.getMorePromos()) {
                if (promoDTO.getIdentity() != null && "BUY_MORE_DISCOUNT".equals(promoDTO.getIdentity().getPromoShowType())) {
                    buyMoreDiscount = promoDTO;
                    break;
                }
            }
            if (buyMoreDiscount == null) {
                return null;
            }

            PromoActivityInfoVO promoActivityInfoVO = new PromoActivityInfoVO();
            promoActivityInfoVO.setBonusType("促销");
            promoActivityInfoVO.setText(buyMoreDiscount.getExtendDesc());
            promoActivityInfoVO.setStyle(0);
            promoActivityInfoVO.setShortText(buyMoreDiscount.getTag());

            return promoActivityInfoVO;
        } catch (Exception e) {
            log.error("buildBuyMoreDiscount error, ", e);
        }
        return null;
    }

    private PromoActivityInfoVO buildPintuanActivity(ShopInfo shopInfo, ProductDetailPageRequest request, PriceDisplayDTO normalPrice, PinProductBrief pinProductBrief, BigDecimal pinPoolPromoAmount) {
        try {
            if (!BuyButtonHelper.isValidPinPool(request.getProductId(),
                    pinProductBrief, pinPoolPromoAmount,
                    normalPrice)) {
                return null;
            }

            Integer pinPersonNum = pinProductBrief.getPinPersonNum();
            String pinPriceStr = PriceHelper.dropLastZero(pinProductBrief.getPrice().setScale(2, RoundingMode.CEILING));
            String preStr = String.format("%s人团 ¥%s元/人，", pinPersonNum, pinPriceStr);

            String shortText = String.format("特惠%s人团", pinPersonNum);

            if (normalPrice != null && normalPrice.getPrice() != null) {
                String minusPrice = normalPrice.getPrice().subtract(new BigDecimal(pinPriceStr))
                        .setScale(2, RoundingMode.CEILING).stripTrailingZeros().toPlainString();
                BigDecimal minusPriceDecimal = new BigDecimal(minusPrice);
                if (minusPriceDecimal.compareTo(BigDecimal.ZERO) < 0) {
                    // minusPrice是负数, 说明拼团价格比正常价格还高
                    return null;
                }
                String fullStr = preStr + "每人多省" + minusPrice + "元";

                PromoActivityInfoVO promoActivityInfoVO = new PromoActivityInfoVO();
                promoActivityInfoVO.setBonusType("拼团");
                promoActivityInfoVO.setText(fullStr);
                promoActivityInfoVO.setStyle(0);
                promoActivityInfoVO.setLeadUrl(UrlHelper.getAppUrl(shopInfo, request, pinProductBrief.getUrl(), request.getClientTypeEnum().isMtClientType()));
                promoActivityInfoVO.setShortText(shortText);

                return promoActivityInfoVO;
            }
            return null;
        } catch (Exception e) {
            log.error("buildPintuanActivity error, ", e);
        }
        return null;
    }

    private void couponFilter(PromoDetailModule promoDetailModule) {
        try {
            if(CollectionUtils.isNotEmpty(promoDetailModule.getCouponList()) && null != promoDetailModule.getInflateCounpon() && CollectionUtils.isNotEmpty(promoDetailModule.getInflateCounpon().getCouponIds())) {
                List<DztgCouponInfo> couponList = promoDetailModule.getCouponList();
                List<String> couponIds = promoDetailModule.getInflateCounpon().getCouponIds();
                couponList.removeIf(dztgCouponInfo -> dztgCouponInfo.getCouponId() != null && couponIds.contains(dztgCouponInfo.getCouponId().toString()));

                promoDetailModule.setCouponList(couponList);
            }
        } catch (Exception e) {
            Cat.logError(e);
        }
    }
    // **************************** //
    // 获取最佳优惠神券的PromoDTO
    private List<PromoDTO> getBestMagicCouponPromoDTO(PriceDisplayDTO dealPromDisplayDTO) {
        List<PromoDTO> usedPromos = dealPromDisplayDTO.getUsedPromos();
        if (CollectionUtils.isEmpty(usedPromos)) {
            return null;
        }
        return usedPromos.stream()
                .filter(promoDTO -> PromoTagEnum.MAGICAL_MEMBER_PLATFORM_COUPON.getType()
                        .equals(promoDTO.getIdentity().getPromoShowType()))
                .collect(Collectors.toList());
    }

    // 构建最佳优惠神券
    public CouponItemPackageDTO buildBestMagicCouponItems(PriceDisplayDTO dealPromDisplayDTO, long userId) {
        return buildCouponItemPackageDTO(dealPromDisplayDTO, userId, true);
    }

    // 构建其他神券信息
    public CouponItemPackageDTO buildOtherMagicCouponItems(PriceDisplayDTO dealPromDisplayDTO, long userId) {
        return buildCouponItemPackageDTO(dealPromDisplayDTO, userId, false);
    }

    private CouponItemPackageDTO buildCouponItemPackageDTO(PriceDisplayDTO dealPromDisplayDTO, long userId, boolean isBestMagicCoupon) {
        Map<Integer, List<PromoDTO>> pricePromoInfoMap = dealPromDisplayDTO.getPricePromoInfoMap();
        if (MapUtils.isEmpty(pricePromoInfoMap)) {
            return null;
        }
        // 获取神券列表信息
        List<PromoDTO> magicCouponPromos = pricePromoInfoMap.get(PricePromoInfoTypeEnum.MAGICAL_MEMBER.getType());
        if (CollectionUtils.isEmpty(magicCouponPromos)) {
            return null;
        }
        // 获取最佳优惠神券的PromoDTO
        List<PromoDTO> bestMagicCouponPromoDTO = getBestMagicCouponPromoDTO(dealPromDisplayDTO);
        List<PromoDTO> matchedMagicCouponPromos = null;
        if (isBestMagicCoupon) {
            // isBestMagicCoupon=true则查找在最佳优惠中的神券
            matchedMagicCouponPromos = magicCouponPromos.stream()
                    .filter(magicPromo -> isBestMagicCoupon(bestMagicCouponPromoDTO, magicPromo))
                    .collect(Collectors.toList());
        } else {
            // 否则查找不在最佳优惠中的神券
            matchedMagicCouponPromos = magicCouponPromos.stream()
                    .filter(magicPromo -> !isBestMagicCoupon(bestMagicCouponPromoDTO, magicPromo))
                    .collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(matchedMagicCouponPromos)) {
            return null;
        }
        // 构建神券优惠券
        List<CouponItemInfoDTO> couponItems = matchedMagicCouponPromos.stream()
            .map(magicPromo -> buildCouponItem(magicPromo, userId))
            .collect(Collectors.toList());
        //券聚合（不聚合领塞、膨后的券）
        List<CouponItemInfoDTO> result = aggregateCouponProcess(couponItems, userId);
        // 券排序
        couponItems =  result.stream()
                .sorted(Comparator.comparing(CouponItemInfoDTO::getCanInflate)
                        .reversed()
                        .thenComparing(CouponItemInfoDTO::getQueryInflateFlag, Comparator.comparingInt(c -> c))
                        .thenComparing(CouponItemInfoDTO::getAmount, (price1,price2) -> -price1.compareTo(price2))
                        .thenComparing(CouponItemInfoDTO::getThresholdAmount)
                        .thenComparing(CouponItemInfoDTO::getCouponType)
                        .thenComparing(CouponItemInfoDTO::getEndTime))
                .collect(Collectors.toList());
        // 构建神券ID
        List<String> couponIds = couponItems.stream().map(CouponItemInfoDTO::getCouponCode).collect(Collectors.toList());
        CouponItemPackageDTO couponItemPackageDTO = new CouponItemPackageDTO();
        couponItemPackageDTO.setCouponItems(couponItems);
        couponItemPackageDTO.setCouponIds(couponIds);
        return couponItemPackageDTO;
    }

    public List<CouponModule> buildNormalCoupons(List<PromoDTO> promoDTOS, String mrnVersion) {
        if (CollectionUtils.isEmpty(promoDTOS)) {
            return null;
        }
        List<CouponModule> couponModules = new ArrayList<>();
        for (PromoDTO promoDTO : promoDTOS) {
            String couponTypeName = PromoHelper.convertSourceTag(promoDTO);
            CouponTypeEnum couponTypeEnum = CouponTypeEnum.ofDesc(couponTypeName);
            CouponIcon couponIcon = buildCouponIcon(couponTypeName);
            CouponModule couponModule = buildNormalCoupon(promoDTO, couponTypeEnum, couponIcon, mrnVersion);
            couponModules.add(couponModule);
        }
        return couponModules;
    }

    // 构造买赠活动
    public List<CouponModule> buildBuyGiftActivity(PromotionDetailParams promotionDetailParams) {
        List<DealGift> dealGifts = promotionDetailParams.getDealGifts();
        if (CollectionUtils.isEmpty(dealGifts)) {
            return null;
        }
        CouponIcon couponIcon = buildCouponIcon("赠品");
        couponIcon.setBackgroundStartColor("#FF7000");
        couponIcon.setBackgroundEndColor("#FF4B10");
        couponIcon.setTextColor("#FFFFFF");
        List<CouponModule> result = Lists.newArrayList();
        for (DealGift dealGift : dealGifts) {
            CouponModule couponModule = new CouponModule();
            // 买赠标题
            CouponTitle couponTitle = new CouponTitle();
            couponTitle.setContent(dealGift.getTitle());
            couponModule.setCouponTitle(Lists.newArrayList(couponTitle));
            couponModule.setCouponStatus(CouponStatusEnum.NOT_SHOW.getCode());
            CouponTitle couponTag = new CouponTitle();
            couponTag.setContent(dealGift.getProductTag());
            couponModule.setCouponTag(couponTag);
            // 买赠规则
            String buyGiveGiftUseRule = getBuyGiveGiftUseRule(dealGift.getValidTimeDesc(), dealGift.getUseRule());
            if (StringUtils.isNotBlank(buyGiveGiftUseRule)) {
                CouponTitle couponSubTitle = new CouponTitle();
                couponSubTitle.setContent(buyGiveGiftUseRule);
                couponSubTitle.setColor("#666666");
                couponModule.setCouponSubTitle(Lists.newArrayList(couponSubTitle));
            }
            // 买赠份数
            couponModule.setCouponNum(dealGift.getCouponNum());
            // 券主图
            couponModule.setCouponAvatar(dealGift.getThumbnail());
            // 券样式
            couponModule.setCouponStyle(CouponStyleEnum.BIG_CARD.getCode());
            // 券类型
            couponModule.setCouponType(CouponTypeEnum.BUY_GIVE_ACTIVITY.getCode());
            // 券icon
            couponModule.setCouponIcon(couponIcon);
            // 活动ID
            couponModule.setApplyId(String.valueOf(dealGift.getActivityId()));
            couponModule.setPromotionType(PromotionTypeEnum.BUY_GIVE.getCode());
            // 构造买赠领券栏
            String labelDesc = String.format("赠%s", dealGift.getTitle());
            PromotionBarModule promotionBarModule = buildPromotionBarModule(null, labelDesc);
            couponModule.setPromotionBarModule(promotionBarModule);
            result.add(couponModule);
        }
        return result;
    }

    // 构造下单返礼活动
    public List<CouponModule> buildOrderGiftActivity(PromotionDetailParams promotionDetailParams) {
        List<PromoReturnInfo> promoReturnActivities = promotionDetailParams.getPromoReturnActivities();
        if (CollectionUtils.isEmpty(promoReturnActivities)) {
            return null;
        }
        CouponIcon couponIcon = buildSimpleStyleCouponIcon("返礼");
        List<CouponModule> result = Lists.newArrayList();
        for (PromoReturnInfo promoReturnActivity : promoReturnActivities) {
            // 如果不是消费返礼，则跳过
            if (!(Objects.equals(promoReturnActivity.getBonusType(), "1") || Objects.equals(promoReturnActivity.getBonusType(), "消费返礼"))) {
                continue;
            }
            CouponModule couponModule = new CouponModule();
            couponModule.setCouponType(CouponTypeEnum.ORDER_GIFT_ACTIVITY.getCode());
            couponModule.setCouponStyle(CouponStyleEnum.SIMPLE_STYLE.getCode());
            // 券图标
            couponModule.setCouponIcon(couponIcon);
            // 券标题
            CouponTitle couponTitle = new CouponTitle();
            couponTitle.setContent(promoReturnActivity.getText());
            couponModule.setCouponTitle(Lists.newArrayList(couponTitle));
            couponModule.setPromotionType(PromotionTypeEnum.PROMOTION_ACTIVITY.getCode());
            // 返礼领券栏
            PromotionBarModule promotionBarModule = buildPromotionBarModule(null, promoReturnActivity.getText());
            couponModule.setPromotionBarModule(promotionBarModule);
            result.add(couponModule);
        }
        return result;
    }

    // 构造下单返券活动
    public List<CouponModule> buildOrderCouponActivity(PromotionDetailParams promotionDetailParams) {
        List<PromoReturnInfo> promoReturnActivities = promotionDetailParams.getPromoReturnActivities();
        if (CollectionUtils.isEmpty(promoReturnActivities)) {
            return null;
        }
        CouponIcon couponIcon = buildSimpleStyleCouponIcon("返券");
        List<CouponModule> result = Lists.newArrayList();
        for (PromoReturnInfo promoReturnActivity : promoReturnActivities) {
            // 如果不是下单返券，则跳过
            if (!(Objects.equals(promoReturnActivity.getBonusType(), "3") || Objects.equals(promoReturnActivity.getBonusType(), "下单返券"))) {
                continue;
            }
            CouponModule couponModule = new CouponModule();
            couponModule.setCouponType(CouponTypeEnum.ORDER_GIFT_ACTIVITY.getCode());
            couponModule.setCouponStyle(CouponStyleEnum.SIMPLE_STYLE.getCode());
            // 券图标
            couponModule.setCouponIcon(couponIcon);
            // 券标题
            CouponTitle couponTitle = new CouponTitle();
            couponTitle.setContent(PromoHelper.getTextFromJson(promoReturnActivity.getText(),"text"));
            couponTitle.setColor(PromoHelper.getColorFromJson(promoReturnActivity.getText(),"textcolor"));

            couponModule.setCouponTitle(Lists.newArrayList(couponTitle));
            couponModule.setPromotionType(PromotionTypeEnum.PROMOTION_ACTIVITY.getCode());
            // 返券领券栏
            PromotionBarModule promotionBarModule = buildPromotionBarModule( null, promoReturnActivity.getText());
            couponModule.setPromotionBarModule(promotionBarModule);
            result.add(couponModule);
        }
        return result;
    }

    // 构造券icon
    private CouponIcon buildCouponIcon(String text) {
        return buildCouponIcon(text, "#FF4B10", "#FFEFEF", "#FFEFEF", 9, 0, 9, 0);
    }

    private CouponIcon buildSimpleStyleCouponIcon(String text) {
        return buildCouponIcon(text, "#FFFFFF", "#FF5500", "#FF5500", 3, 3, 3, 3);
    }

    // 构造券icon
    private CouponIcon buildCouponIcon(String text, String textColor, String backgroundStartColor, String backgroundEndColor, int borderTopLeftRadius, int borderTopRightRadius, int borderBottomRightRadius, int borderBottomLeftRadius) {
        CouponIcon couponIcon = new CouponIcon();
        couponIcon.setText(text);
        couponIcon.setTextColor(textColor);
        couponIcon.setBackgroundStartColor(backgroundStartColor);
        couponIcon.setBackgroundEndColor(backgroundEndColor);
        couponIcon.setBorderTopLeftRadius(borderTopLeftRadius);
        couponIcon.setBorderTopRightRadius(borderTopRightRadius);
        couponIcon.setBorderBottomRightRadius(borderBottomRightRadius);
        couponIcon.setBorderBottomLeftRadius(borderBottomLeftRadius);
        return couponIcon;
    }

    /**
     * 构造普通优惠券
     * @param promoDTO 优惠券信息
     * @param couponTypeEnum 优惠券类型
     * @param couponIcon 优惠券图标
     * @return 返回构造的优惠券模块
     */
    public CouponModule buildNormalCoupon(PromoDTO promoDTO, CouponTypeEnum couponTypeEnum, CouponIcon couponIcon, String mrnVersion) {
        if (Objects.isNull(promoDTO) || Objects.isNull(promoDTO.getIdentity())) {
            return null;
        }
        // 排除神券
        if (Objects.equals(promoDTO.getIdentity().getPromoShowType(), CouponTypeEnum.MAGIC_COUPON.getShowType())
                || Objects.equals(promoDTO.getIdentity().getPromoShowType(), CouponTypeEnum.MEMBER_CARD.getShowType())
                || Objects.equals(promoDTO.getIdentity().getPromoShowType(), CouponTypeEnum.NEW_MEMBER_CARD.getShowType())) {
            return null;
        }
        CouponModule couponModule = new CouponModule();
        // 券状态
        if (promoDTO.isCanAssign()) {
            couponModule.setCouponStatus(CouponStatusEnum.NOT_ASSIGN.getCode());
        } else {
            couponModule.setCouponStatus(CouponStatusEnum.ASSIGNED.getCode());
        }
        // 券icon
        couponModule.setCouponIcon(couponIcon);
        couponModule.setExposure(false);
        // 抵扣券
        couponModule.setCouponValueType(0);
        // 券标题
        CouponTitle couponTitle = new CouponTitle();
        couponTitle.setContent(promoDTO.getExtendDesc());
        couponModule.setCouponTitle(Lists.newArrayList(couponTitle));
        // 券额度
        List<CouponAmount> couponAmounts = buildCouponAmount(promoDTO);
        couponModule.setCouponAmount(couponAmounts);
        // 券门槛内容、颜色、大小
        CouponUsageThreshold couponUsageThreshold = new CouponUsageThreshold();
        couponUsageThreshold.setThreshold(promoDTO.getPriceLimitDesc());
        couponUsageThreshold.setColor("#FF4B10");
        couponUsageThreshold.setFontSize(10);
        couponModule.setCouponUsageThreshold(couponUsageThreshold);
        // 券批次ID
        couponModule.setApplyId(promoDTO.getCouponGroupId());
        // 券ID
        couponModule.setCouponId(promoDTO.getCouponId());
        couponModule.setCouponType(couponTypeEnum.getCode());
        couponModule.setCouponStyle(CouponStyleEnum.BIG_CARD.getCode());
        // 券副标题
        if (StringUtils.isNotBlank(promoDTO.getUseTimeDesc())) {
            CouponTitle couponSubTitle = new CouponTitle();
            couponSubTitle.setContent(promoDTO.getUseTimeDesc());
            // 券副标题颜色、大小
            couponSubTitle.setColor("#666666");
            couponModule.setCouponSubTitle(Lists.newArrayList(couponSubTitle));
        }
        // 特殊处理政府消费券
        // 政府金融券- N选1的券包密钥
        if (couponTypeEnum == CouponTypeEnum.GOVERNMENT_CONSUMER_COUPON) {
            String financeExtSecretKey = PromoInfoHelper.getFinanceExtPackageSecretKey(promoDTO);
            if (org.apache.commons.lang3.StringUtils.isNotBlank(financeExtSecretKey)) {
                String nSelectOneMrnVersion = com.sankuai.dzshoppingguide.product.detail.domain.utils.LionConfigUtils.getNSelectOneGovernmentCouponMrnVersion();
                if (VersionUtils.isLessThanOrEqual(mrnVersion, nSelectOneMrnVersion)) {
                    return null;
                }
                couponModule.setPackageSecretKey(financeExtSecretKey);
            }
            if (Objects.equals(CouponAssignStatusEnum.UN_ASSIGNED.getCode(), promoDTO.getCouponAssignStatus())) {
                couponModule.setCouponStatus(CouponStatusEnum.NOT_ASSIGN.getCode());
                List<Long> leafIds = leafRepository.batchGenFinancialConsumeSerialId(1);
                if (CollectionUtils.isNotEmpty(leafIds)) {
                    couponModule.setCouponId(String.valueOf(leafIds.get(0)));
                }
            } else {
                couponModule.setCouponStatus(CouponStatusEnum.ASSIGNED.getCode());
            }
        }
        couponModule.setPromotionType(PromotionTypeEnum.COUPON.getCode());
        // 构建领券栏数据
        PromotionBarModule promotionBarModule = buildPromotionBarModule( null, promoDTO.getIdentity().getPromoTypeDesc());
        couponModule.setPromotionBarModule(promotionBarModule);
        return couponModule;
    }

    private PromotionBarModule buildPromotionBarModule(String barLabelTheme, String barLabelDesc) {
        if (StringUtils.isBlank(barLabelTheme) && StringUtils.isBlank(barLabelDesc)) {
            return null;
        }
        PromotionBarModule promotionBarModule = new PromotionBarModule();
        promotionBarModule.setLabelTheme(barLabelTheme);
        promotionBarModule.setLabelDesc(barLabelDesc);
        return promotionBarModule;
    }

    private List<CouponAmount> buildCouponAmount(PromoDTO promoDTO) {
        List<CouponAmount> couponAmountList = Lists.newArrayList();
        CouponAmount couponAmount = new CouponAmount();
        couponAmount.setAmount(promoDTO.getCouponValueText());
        couponAmount.setColor("#FF4B10");
        couponAmount.setFontSize(30);
        couponAmountList.add(couponAmount);
        if (promoDTO.getCouponValueType() == 4) {
            CouponAmount discount = new CouponAmount();
            discount.setAmount("折");
            discount.setColor("#FF4B10");
            discount.setFontSize(16);
            couponAmountList.add(discount);
        } else {
            CouponAmount symbol = new CouponAmount();
            symbol.setAmount("元");
            symbol.setColor("#FF4B10");
            symbol.setFontSize(16);
            couponAmountList.add(symbol);
        }
        return couponAmountList;
    }

    private List<CouponAmount> buildCouponAmount(PromoCouponInfo promoCouponInfo) {
        List<CouponAmount> couponAmountList = Lists.newArrayList();
        CouponAmount couponAmount = new CouponAmount();
        couponAmount.setAmount(promoCouponInfo.getAmount());
        couponAmount.setColor("#FF4B10");
        couponAmount.setFontSize(30);
        couponAmountList.add(couponAmount);
        CouponAmount discount = new CouponAmount();
        discount.setAmount(promoCouponInfo.getAmountCornerMark());
        discount.setColor("#FF4B10");
        discount.setFontSize(16);
        couponAmountList.add(discount);
        return couponAmountList;
    }

    private boolean isBestMagicCoupon(List<PromoDTO> bestMagicCouponPromoDTO, PromoDTO couponPromo) {
        if (CollectionUtils.isEmpty(bestMagicCouponPromoDTO) || Objects.isNull(couponPromo)) {
            return false;
        }
        return bestMagicCouponPromoDTO.stream()
                .anyMatch(bestMagicPromo -> Objects.equals(bestMagicPromo.getCouponId(), couponPromo.getCouponId()));
    }

    // 构建神券优惠券
    public static CouponItemInfoDTO buildCouponItem(PromoDTO promoDTO, long userId) {
        CouponItemInfoDTO couponItem = new CouponItemInfoDTO();
        couponItem.setEndTime(promoDTO.getEndTime().getTime());
        // 券码
        couponItem.setCouponCode(promoDTO.getCouponId());
        // 券批次id
        couponItem.setApplyId(promoDTO.getCouponGroupId());
        Pair<String, String> pair = getValidTimeText(promoDTO.getStartTime(), promoDTO.getEndTime());
        if (pair != null) {
            // 券到期时间文案
            couponItem.setCouponValidTimeText(pair.getKey());
            // 到期有效时间
            couponItem.setValidTime(pair.getValue());
        }

        // 券金额
        couponItem.setCouponAmount(promoDTO.getAmount().toPlainString());
        couponItem.setAmount(promoDTO.getAmount());
        // 券名称
        couponItem.setCouponName(promoDTO.getCouponTitle());
        // 券状态 1-有效 2-无效 （根据开始和结束时间判断）
        couponItem.setValidStatus(getCouponValidStatus(promoDTO.getStartTime(), promoDTO.getEndTime()));
        // 付费神券/免费神券
        couponItem.setCouponType(getCouponType(promoDTO.getPromotionExplanatoryTags()));
        // 营销扩展信息
        Map<String, String> promotionOtherInfoMap = promoDTO.getPromotionOtherInfoMap();
        // 膨胀前金额
        if (promoDTO.getMinConsumptionAmount() != null) {
            couponItem.setOriginalRequiredAmount(
                    promoDTO.getMinConsumptionAmount().multiply(new BigDecimal("100")).longValue());
        }

        Map<String, String> promotionDisplayTextMap = promoDTO.getPromotionDisplayTextMap();

        if (MapUtils.isNotEmpty(promotionDisplayTextMap)) {
            // 顶部icon链接
            couponItem.setLogoIconUrl(promotionDisplayTextMap.get(PromotionTextEnum.topLeftIcon.getValue()));
            // 新顶部icon链接
            couponItem.setTopLeftNewIconInfo(
                    promotionDisplayTextMap.get(PromotionTextEnum.topLeftNewIconInfo.getValue()));

            // 膨胀按钮文案
            couponItem.setCouponButtonText(
                    promotionDisplayTextMap.get(PromotionTextEnum.promotionButtonText.getValue()));
        }

        // 膨胀后金额
        couponItem.setOriginalReduceAmount(promoDTO.getAmount().multiply(BigDecimal.valueOf(100)).longValue());

        // 资产类型
        couponItem.setAssetType(
                Integer.parseInt(promotionOtherInfoMap.get(PromotionPropertyEnum.ASSET_TYPE.getValue())));

        if (couponItem.getAssetType() == 1) {
            // 到家券用这个券id
            couponItem.setCouponCode(promotionOtherInfoMap.get(PromotionPropertyEnum.TSP_COUPON_ID.getValue()));
            couponItem.setApplyId(promotionOtherInfoMap.get(PromotionPropertyEnum.TSP_COUPON_GROUP_ID.getValue()));
        }

        // 业务线
        couponItem.setBizLine(promotionOtherInfoMap.get(PromotionPropertyEnum.NIB_BIZ_LINE.getValue()));

        // 实膨参数透传
        couponItem.setBizToken(promotionOtherInfoMap.get(PromotionPropertyEnum.BIZ_TOKEN.getValue()));

        // 券是否可膨胀
        boolean canInflate = Boolean
                .parseBoolean(promotionOtherInfoMap.get(PromotionPropertyEnum.CAN_INFLATE.getValue()));
        couponItem.setCanInflate(
                canInflate ? CanInflateEnum.CAN_INFLATE.getCode() : CanInflateEnum.CAN_NOT_INFLATE.getCode());

        // 券是否已膨胀
        boolean afterInflate = Boolean
                .parseBoolean(promotionOtherInfoMap.get(PromotionPropertyEnum.AFTER_INFLATE.getValue()));
        couponItem.setInflatedStatus(
                afterInflate ? InflateStatusEnum.AFTER_INFLATE.getCode() : InflateStatusEnum.NO_INFLATE.getCode());

        if (afterInflate) {
            // 已膨胀后的神券就是普通券，门槛取基础信息字段
            couponItem.setThresholdDesc(buildCouponConditionText(promoDTO.getMinConsumptionAmount()));
            couponItem.setThresholdAmount(promoDTO.getMinConsumptionAmount());
        } else {
            if (canInflate) {
                // 未膨胀券的神券，
                if (null != promotionOtherInfoMap
                        .get(PromotionPropertyEnum.AFTER_INFLATE_REQUIRE_AMOUNT.getValue())) {
                    couponItem.setThresholdDesc(promoDTO.getPriceLimitDesc());
                    couponItem.setThresholdAmount(new BigDecimal(promotionOtherInfoMap
                            .get(PromotionPropertyEnum.AFTER_INFLATE_REQUIRE_AMOUNT.getValue())));
                }
                if (null != promotionOtherInfoMap.get(PromotionPropertyEnum.MAX_INFLATE_MONEY.getValue())) {
                    // 最高膨胀至XX
                    couponItem.setCouponDesc(buildCouponDesc(BigDecimal.valueOf(Double.parseDouble(
                            promotionOtherInfoMap.get(PromotionPropertyEnum.MAX_INFLATE_MONEY.getValue())) / 100.0)
                            .stripTrailingZeros()));
                }
            } else {
                couponItem.setThresholdDesc(buildCouponConditionText(promoDTO.getMinConsumptionAmount()));
                couponItem.setThresholdAmount(promoDTO.getMinConsumptionAmount());
            }
        }
        if (promotionOtherInfoMap.get(PromotionPropertyEnum.QUERY_INFLATE_FLAG.getValue()) != null
                && "true".equals(promotionOtherInfoMap.get(PromotionPropertyEnum.QUERY_INFLATE_FLAG.getValue()))) {
            // 1标识券已锁价
            couponItem.setQueryInflateFlag(1);
        }
        // 券聚合数量
        if (LionConfigUtils.hitCouponAggregateSwitch(userId)) {
            couponItem.setCouponNum(getCouponNum(promotionOtherInfoMap));
        }
        return couponItem;
    }

    private String getBuyGiveGiftUseRule(String timeDesc, String useRule) {
        if (StringUtils.isBlank(timeDesc) && StringUtils.isBlank(useRule)) {
            return null;
        }
        List<String> useRules = Lists.newArrayList(timeDesc, useRule)
                .stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        String timeAndUseRule = String.join("，", useRules);
        return String.format("规则：%s", timeAndUseRule);
    }

    // 构建商家拼团模块
    public CouponModule buildMerchantGroupBuyCouponModule(PromotionDetailParams promotionDetailParams) {
        ProductDetailPageRequest pageRequest = promotionDetailParams.getPageRequest();
        CouponModule couponModule = new CouponModule();
        // 拼团图标
        CouponIcon couponIcon = buildCouponIcon("拼团活动");
        couponModule.setCouponIcon(couponIcon);
        couponModule.setCouponType(CouponTypeEnum.MERCHANT_PIN_TUAN.getCode());
        couponModule.setCouponStyle(CouponStyleEnum.MERCHANT_PINTUAN.getCode());
        // 拼团标题
        PinProductBrief pinProductBrief = promotionDetailParams.getPinProductBrief();
        Integer pinPersonNum = pinProductBrief.getPinPersonNum();
        String pinPriceStr = PriceHelper.dropLastZero(pinProductBrief.getPrice().setScale(2, RoundingMode.CEILING));
        CouponTitle couponTitle1 = new CouponTitle();
        couponTitle1.setContent(String.format("%s人团，拼成到手", pinPersonNum));
        CouponTitle couponTitle2 = new CouponTitle();
        couponTitle2.setContent(pinPriceStr);
        couponTitle2.setColor("#FF4B10");
        CouponTitle couponTitle3 = new CouponTitle();
        couponTitle3.setContent("元");
        couponModule.setCouponTitle(Lists.newArrayList(couponTitle1, couponTitle2, couponTitle3));
        // 副标题
        CouponTitle couponSubTitle = new CouponTitle();
        couponSubTitle.setContent("未成团自动退款");
        couponModule.setCouponSubTitle(Lists.newArrayList(couponSubTitle));
        // 副标题icon
        CouponSubTitleIcon couponSubTitleIcon = new CouponSubTitleIcon();
        Icon icon = new Icon();
        icon.setIcon("https://p0.meituan.net/dztgdetailimages/1be58df81580af07199e9587856fe9321396.png");
        icon.setIconWidth(12);
        icon.setIconHeight(12);
        couponSubTitleIcon.setIcon(icon);
        couponSubTitleIcon.setPopUpContent(buildPopUpContentModule());
        couponModule.setCouponSubTitleIcon(couponSubTitleIcon);
        // 拼团按钮
        ActionButton couponButton = new ActionButton();
        couponButton.setButtonText("发起拼团");
        couponButton.setTextColor("#FFFFFF");
        couponButton.setButtonStartColor("#FF7700");
        couponButton.setButtonEndColor("#FF4B10");
        String jumpUrl = UrlHelper.getAppUrl(promotionDetailParams.getShopInfo(), pageRequest, pinProductBrief.getUrl(),
                pageRequest.getClientTypeEnum().isMtClientType());
        couponButton.setJumpUrl(jumpUrl);
        couponModule.setActionButton(couponButton);
        // 拼团步骤
        couponModule.setStep(buildMerchantPinTuanStep());
        couponModule.setPromotionType(PromotionTypeEnum.MERCHANT_PIN_TUAN.getCode());
        // 拼团领券栏
        String barLabelDesc = String.format("特惠%s人团", pinPersonNum);
        PromotionBarModule promotionBarModule = buildPromotionBarModule("拼团", barLabelDesc);
        couponModule.setPromotionBarModule(promotionBarModule);
        return couponModule;
    }

    private MerchantPinTuanStep buildMerchantPinTuanStep() {
        MerchantPinTuanStep merchantPinTuanStep = new MerchantPinTuanStep();
        List<MerchantPinTuanStepItem> items = buildMerchantPinTuanStepItems();
        merchantPinTuanStep.setItems(items);
        return merchantPinTuanStep;
    }

    private List<MerchantPinTuanStepItem> buildMerchantPinTuanStepItems() {
        List<MerchantPinTuanStepItem> result = Lists.newArrayList();
        MerchantPinTuanStepItem item1 = new MerchantPinTuanStepItem();
        // icon图标
        item1.setIcon("https://p1.meituan.net/dztgdetailimages/a56aafff1965d4b48351ff9c148e7c5e626.png");
        item1.setText("点击[发起拼团]");
        result.add(item1);
        MerchantPinTuanStepItem item2 = new MerchantPinTuanStepItem();
        item2.setIcon("https://p0.meituan.net/dztgdetailimages/2ea8b007556da0ebfd5d8a516218ae90524.png");
        item2.setText("分享给好友");
        result.add(item2);
        MerchantPinTuanStepItem item3 = new MerchantPinTuanStepItem();
        item3.setIcon("https://p0.meituan.net/dztgdetailimages/be870dce7a719ea49722b2cab22c446e535.png");
        item3.setText("人满成团");
        result.add(item3);
        return result;
    }

    private PopUpContentModule buildPopUpContentModule() {
        PopUpContentModule popUpContent = new PopUpContentModule();
        popUpContent.setTitle("拼团规则");
        PopUpContentItem popUpContentItem1 = new PopUpContentItem();
        popUpContentItem1.setItemTitle("发起拼团：");
        popUpContentItem1.setItemContent("选中商品后点击“发起拼团”并完成支付");
        PopUpContentItem popUpContentItem2 = new PopUpContentItem();
        popUpContentItem2.setItemTitle("邀请好友拼团：");
        popUpContentItem2.setItemContent("支付后将购买的商品分享到微信群或朋友圈，邀请好友一起拼团");
        PopUpContentItem popUpContentItem3 = new PopUpContentItem();
        popUpContentItem3.setItemTitle("拼团成功：");
        popUpContentItem3.setItemContent("参与拼团的好友满足拼团人数后，拼团成功，获得券码");
        PopUpContentItem popUpContentItem4 = new PopUpContentItem();
        popUpContentItem4.setItemTitle("到店核销：");
        popUpContentItem4.setItemContent("使用券码到店内进行消费");
        PopUpContentItem popUpContentItem5 = new PopUpContentItem();
        popUpContentItem5.setItemTitle("关于拼团时间：");
        popUpContentItem5.setItemContent("拼团需在24小时内邀请好友参团，超时即拼团失败并自动退款");
        PopUpContentItem popUpContentItem6 = new PopUpContentItem();
        popUpContentItem6.setItemTitle("到时自动成团：");
        popUpContentItem6.setItemContent("部分拼团支持到时自动成团，可在拼团超时前自动成团");
        PopUpContentItem popUpContentItem7 = new PopUpContentItem();
        popUpContentItem7.setItemTitle("关于使用：");
        popUpContentItem7.setItemContent("可以和好友分别单独消费，也可以一起到店消费");
        popUpContent.setContent(Lists.newArrayList(popUpContentItem1, popUpContentItem2, popUpContentItem3,
                popUpContentItem4, popUpContentItem5, popUpContentItem6, popUpContentItem7));
        return popUpContent;
    }

    // 构建闲时优惠模块
    public CouponModule buildIdleTimeCoupon(PriceDisplayDTO idlePriceDisplayDTO,
                                            PromotionDetailParams promotionDetailParams) {
        // 闲时优惠虽然在更多优惠中，但取的是usedPromos中的数据
        if (CollectionUtils.isEmpty(idlePriceDisplayDTO.getUsedPromos())) {
            return null;
        }
        CouponModule couponModule = new CouponModule();
        couponModule.setCouponType(CouponTypeEnum.IDLE_TIME_COUPON.getCode());
        couponModule.setCouponStyle(CouponStyleEnum.SIMPLE_STYLE.getCode());
        // 券icon
        CouponIcon couponIcon = buildSimpleStyleCouponIcon("限时");
        couponModule.setCouponIcon(couponIcon);
        // 券标题
        String consumeTimeDesc = idlePriceDisplayDTO.getUsedPromos().get(0).getConsumeTimeDesc() == null ? "" : idlePriceDisplayDTO.getUsedPromos().get(0).getConsumeTimeDesc();
        String price = PriceHelper.dropLastZero(idlePriceDisplayDTO.getPrice());
        String text = String.format("限时特惠，%s ¥%s/次", consumeTimeDesc, price);
        CouponTitle couponTitle = new CouponTitle();
        couponTitle.setContent(text);
        couponModule.setCouponTitle(Lists.newArrayList(couponTitle));
        couponModule.setPromotionType(PromotionTypeEnum.PROMOTION_ACTIVITY.getCode());
        // 限时特惠领券栏
        String shortText = idlePriceDisplayDTO.getUsedPromos().get(0).getIdentity() == null ? null : idlePriceDisplayDTO.getUsedPromos().get(0).getIdentity().getPromoTypeDesc();
        if (StringUtils.isBlank(shortText)) {
            String promoAmount = PriceHelper.dropLastZero(idlePriceDisplayDTO.getPromoAmount());
            shortText = String.format("工作日立减%s元", promoAmount);
        }
        int mtCityId = Optional.ofNullable(promotionDetailParams.getCityIdMapper()).map(CityIdMapper::getMtCityId).orElse(0);
        String idleHoursBuyUrl = UrlHelper.getIdleHoursBuyUrl(
                promotionDetailParams.getPageRequest(),
                promotionDetailParams.getSkuDefaultSelect(),
                promotionDetailParams.getShopInfo(),
                promotionDetailParams.getShopIdMapper(),
                promotionDetailParams.getProductBaseInfo(),
                mtCityId
        );
        ActionButton actionButton = new ActionButton();
        actionButton.setJumpUrl(idleHoursBuyUrl);
        couponModule.setActionButton(actionButton);
        couponModule.setCouponStatus(CouponStatusEnum.JUMP_URL.getCode());
        PromotionBarModule promotionBarModule = buildPromotionBarModule(null, shortText);
        couponModule.setPromotionBarModule(promotionBarModule);
        return couponModule;
    }

    // 构建多买多减模块
    public CouponModule buildBuyMoreReduction(PromotionDetailParams promotionDetailParams) {
        PriceDisplayDTO normalPriceDisplay = promotionDetailParams.getNormalPriceDisplayDTO();
        if (CollectionUtils.isEmpty(normalPriceDisplay.getMorePromos())) {
            return null;
        }
        PromoDTO morePromoDTO = normalPriceDisplay.getMorePromos()
            .stream()
            .filter(promoDTO -> Objects.equals(promoDTO.getIdentity().getPromoShowType(), CouponTypeEnum.BUY_MORE_REDUCTION.getShowType()))
            .findFirst()
            .orElse(null);
        if (Objects.isNull(morePromoDTO)) {
            return null;
        }
        CouponModule couponModule = new CouponModule();
        couponModule.setCouponType(CouponTypeEnum.BUY_MORE_REDUCTION.getCode());
        couponModule.setCouponStyle(CouponStyleEnum.SIMPLE_STYLE.getCode());
        // 券icon
        CouponIcon couponIcon = buildSimpleStyleCouponIcon("促销");
        couponModule.setCouponIcon(couponIcon);
        CouponTitle couponTitle = new CouponTitle();
        couponTitle.setContent(morePromoDTO.getExtendDesc());
        couponModule.setCouponTitle(Lists.newArrayList(couponTitle));
        couponModule.setPromotionType(PromotionTypeEnum.PROMOTION_ACTIVITY.getCode());
        // 多买多减领券栏
        PromotionBarModule promotionBarModule = buildPromotionBarModule(null, morePromoDTO.getTag());
        couponModule.setPromotionBarModule(promotionBarModule);
        return couponModule;
    }

    // 构建多买多折模块
    public CouponModule buildBuyMoreDiscount(PromotionDetailParams promotionDetailParams) {
        PriceDisplayDTO normalPriceDisplay = promotionDetailParams.getNormalPriceDisplayDTO();
        if (CollectionUtils.isEmpty(normalPriceDisplay.getMorePromos())) {
            return null;
        }
        PromoDTO morePromoDTO = normalPriceDisplay.getMorePromos()
            .stream()
            .filter(promoDTO -> Objects.equals(promoDTO.getIdentity().getPromoShowType(), CouponTypeEnum.BUY_MORE_DISCOUNT.getShowType()))
            .findFirst()
            .orElse(null);
        if (Objects.isNull(morePromoDTO)) {
            return null;
        }
        CouponModule couponModule = new CouponModule();
        couponModule.setCouponType(CouponTypeEnum.BUY_MORE_DISCOUNT.getCode());
        couponModule.setCouponStyle(CouponStyleEnum.SIMPLE_STYLE.getCode());
        // 券icon
        CouponIcon couponIcon = buildSimpleStyleCouponIcon("促销");
        couponModule.setCouponIcon(couponIcon);
        CouponTitle couponTitle = new CouponTitle();
        couponTitle.setContent(morePromoDTO.getExtendDesc());
        couponModule.setCouponTitle(Lists.newArrayList(couponTitle));
        couponModule.setPromotionType(PromotionTypeEnum.PROMOTION_ACTIVITY.getCode());
        // 多买多减领券栏
        PromotionBarModule promotionBarModule = buildPromotionBarModule(null, morePromoDTO.getTag());
        couponModule.setPromotionBarModule(promotionBarModule);
        return couponModule;
    }

    public CouponModule buildMemberCard(PromotionDetailParams promotionDetailParams, boolean bestPromoModule) {
        // 如果发生了比价，则使用比价后的，如果未发生比价，则使用原始价格模型
        ProductPriceReturnValue productPromoPriceReturnValue = promotionDetailParams.getProductPromoPriceReturnValue();
        if (Objects.isNull(productPromoPriceReturnValue)) {
            return null;
        }
        PriceDisplayDTO dealPromDisplayDTO = productPromoPriceReturnValue.getFinalPriceDisplayDTO();
        if (Objects.isNull(dealPromDisplayDTO) || CollectionUtils.isEmpty(dealPromDisplayDTO.getUsedPromos())) {
            return null;
        }
        PromoDTO memberCardPromoDTO = getMemberCardPromoDTO(dealPromDisplayDTO);
        // 如果最佳优惠中没有会员 或者 查不到会员卡的信息，则不展示
        if (Objects.isNull(memberCardPromoDTO) || Objects.isNull(promotionDetailParams.getMemberCardDiscount())) {
            return null;
        }
        MemberCardDiscountReturnValue memberCardDiscount = promotionDetailParams.getMemberCardDiscount();
        // 构造会员卡领券栏
        PromotionBarModule promotionBarModule = buildMemberCardPromotionBar(memberCardDiscount);
        // 如果已经是会员，仅在领券栏展示
        if (memberCardDiscount.isMember()) {
            CouponModule couponModule = new CouponModule();
            couponModule.setCouponType(CouponTypeEnum.MEMBER_CARD.getCode());
            couponModule.setCouponStyle(CouponStyleEnum.BIG_CARD.getCode());
            couponModule.setPromotionBarModule(promotionBarModule);
            return couponModule;
        }
        CouponModule couponModule = new CouponModule();
        couponModule.setCouponType(CouponTypeEnum.MEMBER_CARD.getCode());
        couponModule.setCouponStyle(CouponStyleEnum.BIG_CARD.getCode());
        // 券icon
        CouponIcon couponIcon = buildCouponIcon("会员卡", "#443734", "#FED6BF", "#E89C73", 9, 0, 9, 0);
        couponModule.setCouponIcon(couponIcon);
        // 券标题
        CouponTitle couponTitle = new CouponTitle();
        couponTitle.setContent("会员专属折扣");
        couponModule.setCouponTitle(Lists.newArrayList(couponTitle));

        // 按钮
        ActionButton actionButton = new ActionButton();
        if (memberCardDiscount.getMemberCardTypeEnum() == MemberChargeTypeEnum.FREE) {
            // 副标题
            CouponTitle couponSubTitle = new CouponTitle();
            couponSubTitle.setContent("开通后立享优惠");
            couponModule.setCouponSubTitle(Lists.newArrayList(couponSubTitle));
            // 优惠金额
            CouponAmount couponAmount = new CouponAmount();
            couponAmount.setAmount(memberCardDiscount.getMemberCardAmount());
            couponAmount.setFontSize(24);
            couponAmount.setColor("#8E3C12");
            CouponAmount amountUnit = new CouponAmount();
            amountUnit.setAmount("¥");
            amountUnit.setFontSize(14);
            amountUnit.setColor("#8E3C12");
            couponModule.setCouponAmount(Lists.newArrayList(amountUnit, couponAmount));

            actionButton.setButtonText("免费领取");
            actionButton.setTextColor("#8E3C12");
            actionButton.setJumpUrl(memberCardDiscount.getMemberCardUrl());
            actionButton.setButtonStartColor("#FED6BF");
            actionButton.setButtonEndColor("#E89C73");
            couponModule.setCouponStatus(CouponStatusEnum.JUMP_URL.getCode());
        } else if (memberCardDiscount.getMemberCardTypeEnum() == MemberChargeTypeEnum.CHARGE) {
            // 副标题
            CouponTitle couponSubTitle = new CouponTitle();
            couponSubTitle.setContent(memberCardDiscount.getValidPeriod());
            couponModule.setCouponSubTitle(Lists.newArrayList(couponSubTitle));
            // 折扣
            CouponAmount couponAmount = new CouponAmount();
            couponAmount.setAmount(memberCardDiscount.getMemberCardDiscount());
            couponAmount.setFontSize(24);
            couponAmount.setColor("#8E3C12");
            CouponAmount amountUnit = new CouponAmount();
            amountUnit.setAmount("折");
            amountUnit.setFontSize(14);
            amountUnit.setColor("#8E3C12");
            couponModule.setCouponAmount(Lists.newArrayList(couponAmount, amountUnit));

            actionButton.setButtonText(String.format("￥%s开通", memberCardDiscount.getOpenCardFee()));
            actionButton.setJumpUrl(memberCardDiscount.getMemberCardUrl());
            actionButton.setButtonStartColor("#FED6BF");
            actionButton.setButtonEndColor("#E89C73");
            actionButton.setTextColor("#8E3C12");
            couponModule.setCouponStatus(CouponStatusEnum.JUMP_URL.getCode());
        }
        couponModule.setActionButton(actionButton);
        if (memberCardDiscount.getMemberCardTypeEnum() == MemberChargeTypeEnum.FREE) {
            couponModule.setPromotionType(PromotionTypeEnum.FREE_MERCHANT_MEMBER_CARD.getCode());
        } else {
            couponModule.setPromotionType(PromotionTypeEnum.PAY_MERCHANT_MEMBER_CARD.getCode());
        }
        // 如果已开通会员，则不展示按钮
        if (memberCardDiscount.isMember()) {
            couponModule.setCouponStatus(CouponStatusEnum.NOT_SHOW.getCode());
            couponModule.setActionButton(null);
        }
        couponModule.setPromotionBarModule(promotionBarModule);
        return couponModule;
    }

    private PromotionBarModule buildMemberCardPromotionBar(MemberCardDiscountReturnValue memberCardDiscount) {
        if (Objects.isNull(memberCardDiscount)) {
            return null;
        }
        // 会员领券栏
        String barLabelTheme = "";
        String barLabelDesc = "";
        // 免费会员展示「会员优惠｜xx元」
        if (memberCardDiscount.getMemberCardTypeEnum() == MemberChargeTypeEnum.FREE) {
            barLabelTheme = "会员优惠";
            barLabelDesc = String.format("%s元", memberCardDiscount.getMemberCardAmount());
        } else if (memberCardDiscount.isMember()) {
            barLabelTheme = "会员优惠";
            barLabelDesc = String.format("已享%s折", memberCardDiscount.getMemberCardDiscount());
        } else {
            barLabelTheme = "开通会员";
            barLabelDesc = String.format("再享%s折", memberCardDiscount.getMemberCardDiscount());
        }
        return buildPromotionBarModule(barLabelTheme, barLabelDesc);
    }

    public CouponModule buildMagicCouponPackage(PromotionDetailParams promotionDetailParams) {
        PriceDisplayDTO dealPromDisplayDTO = promotionDetailParams.getDealPromDisplayDTO();
        Map<String, String> extendDisplayInfoMap = dealPromDisplayDTO.getExtendDisplayInfo();
        if(MapUtils.isEmpty(extendDisplayInfoMap)) {
            return null;
        }
        String walletInfo = extendDisplayInfoMap.get(ExtendDisplayInfoKeyEnum.COUPON_WALLET_INFO.getKey());
        if (StringUtils.isBlank(walletInfo)) {
            return null;
        }
        List<String> couponGroupIdList = buildCouponGroupIdList(walletInfo);
        if (CollectionUtils.isEmpty(couponGroupIdList)) {
            return null;
        }
        CouponModule couponModule = new CouponModule();
        couponModule.setCouponType(CouponTypeEnum.MAGIC_COUPON_PACKAGE.getCode());
        couponModule.setCouponStyle(CouponStyleEnum.BIG_CARD.getCode());
        couponModule.setCouponGroupIdList(couponGroupIdList);
        couponModule.setMmcPkgResult(toMmcPkgResult(extendDisplayInfoMap.get(ExtendDisplayInfoKeyEnum.COMMON_EXPERIMENT_ID_SET.getKey())));
        return couponModule;
    }

    // 构造曝光券
    public List<CouponModule> buildExposureCoupon(PromotionDetailParams promotionDetailParams) {
        List<PromoExposureInfo> promoExposureInfoList = promotionDetailParams.getExposureCoupons();
        if (CollectionUtils.isEmpty(promoExposureInfoList)) {
            return null;
        }
        List<CouponModule> result = Lists.newArrayList();
        for (PromoExposureInfo promoExposureInfo : promoExposureInfoList) {
            CouponModule couponModule = new CouponModule();
            // 曝光券类型
            couponModule.setCouponType(CouponTypeEnum.EXPOSURE_COUPON.getCode());
            couponModule.setCouponStyle(CouponStyleEnum.BIG_CARD.getCode());
            String flowId = Objects.nonNull(promoExposureInfo.getFlowId()) ? String.valueOf(promoExposureInfo.getFlowId()) : StringUtils.EMPTY;
            couponModule.setFlowId(flowId);
            String resourceActivityId = Objects.nonNull(promoExposureInfo.getResourceActivityId()) ? String.valueOf(promoExposureInfo.getResourceActivityId()) : StringUtils.EMPTY;
            couponModule.setResourceActivityId(resourceActivityId);
            String activityId = Objects.nonNull(promoExposureInfo.getActivityId()) ? String.valueOf(promoExposureInfo.getActivityId()) : StringUtils.EMPTY;
            couponModule.setActivityId(activityId);
            couponModule.setMaterialId(promoExposureInfo.getMaterialId());
            couponModule.setRowKey(promoExposureInfo.getRowKey());
            CouponAmount symbol = new CouponAmount();
            symbol.setAmount("¥");
            symbol.setColor("#FF4B10");
            symbol.setFontSize(16);
            CouponAmount couponAmount = new CouponAmount();
            couponAmount.setAmount(promoExposureInfo.getAmount());
            couponAmount.setColor("#FF4B10");
            couponAmount.setFontSize(30);
            couponModule.setCouponAmount(Lists.newArrayList(symbol, couponAmount));
            // 券名称
            CouponTitle couponTitle = new CouponTitle();
            couponTitle.setContent(promoExposureInfo.getTitle());
            couponModule.setCouponTitle(Lists.newArrayList(couponTitle));
            // 券类型值，用于领券
            couponModule.setCouponValueType(promoExposureInfo.getCouponType());
            // 副标题
            CouponTitle couponSubTitle = new CouponTitle();
            couponSubTitle.setContent(promoExposureInfo.getSubTitle());
            couponModule.setCouponSubTitle(Lists.newArrayList(couponSubTitle));
            // 领取状态
            couponModule.setCouponStatus(promoExposureInfo.getCanAssign() ? CouponStatusEnum.ASSIGNED.getCode() : CouponStatusEnum.COUNTDOWN.getCode());
            // 使用结束时间
            couponModule.setUseEndTime(promoExposureInfo.getUseEndTime());
            // 是否是活动曝光券
            couponModule.setExposure(true);
            couponModule.setPromotionType(PromotionTypeEnum.COUPON.getCode());
            // 曝光券领券栏
            PromotionBarModule promotionBarModule = buildPromotionBarModule(null, promoExposureInfo.getTitle());
            couponModule.setPromotionBarModule(promotionBarModule);
            result.add(couponModule);
        }
        return result;
    }

    // 根据券平台返回的优惠券构建更多券列表
    public List<CouponModule> buildNormalCoupon(List<PromoCouponInfo> optAndFinancialCoupons, PriceDisplayDTO dealPromDisplayDTO) {
        if (Objects.nonNull(dealPromDisplayDTO.getUsedPromos())) {
            List<String> usedCouponGroupIds = Optional.of(dealPromDisplayDTO.getUsedPromos().stream()
                    .map(PromoDTO::getCouponGroupId)
                    .collect(Collectors.toList()))
                    .orElse(Lists.newArrayList());
            // 过滤出不在最佳优惠中的优惠券
            optAndFinancialCoupons = optAndFinancialCoupons.stream()
                    .filter(Objects::nonNull)
                    .filter(coupon -> Objects.isNull(coupon.getCouponGroupId()) && !usedCouponGroupIds.contains(String.valueOf(coupon.getCouponGroupId())))
                    .collect(Collectors.toList());
        }
        // 过滤已领取的优惠券
        return optAndFinancialCoupons.stream()
                .map(this::buildCouponModule4OptAndFinancialCoupon)
                .filter(coupon -> !Objects.equals(coupon.getCouponStatus(), CouponStatusEnum.ASSIGNED.getCode()))
                .collect(Collectors.toList());
    }

    // 构造普通券
    private CouponModule buildCouponModule4OptAndFinancialCoupon(PromoCouponInfo optAndFinancialCoupon) {
        CouponModule couponModule = new CouponModule();
        // 券批次ID
        couponModule.setApplyId(String.valueOf(optAndFinancialCoupon.getCouponGroupId()));
        // 券名称
        CouponTitle couponTitle = new CouponTitle();
        couponTitle.setContent(optAndFinancialCoupon.getTitle());
        couponModule.setCouponTitle(Lists.newArrayList(couponTitle));
        // 券额度
        List<CouponAmount> couponAmounts = buildCouponAmount(optAndFinancialCoupon);
        couponModule.setCouponAmount(couponAmounts);
        // 券门槛
        CouponUsageThreshold couponThreshold = new CouponUsageThreshold();
        couponThreshold.setThreshold(optAndFinancialCoupon.getAmountDesc());
        couponModule.setCouponUsageThreshold(couponThreshold);
        // 券icon
        String couponTypeName = StringUtils.isBlank(optAndFinancialCoupon.getSourceTag()) ? "优惠券" : optAndFinancialCoupon.getSourceTag();
        CouponIcon couponIcon = buildCouponIcon(couponTypeName);
        couponModule.setCouponIcon(couponIcon);
        // 券类型
        CouponTypeEnum couponTypeEnum = CouponTypeEnum.ofDesc(couponTypeName);
        couponModule.setCouponType(couponTypeEnum.getCode());
        // 券类型值，用于前端领取优惠券
        couponModule.setCouponValueType(optAndFinancialCoupon.getCouponType());
        // 券状态
        if (Objects.equals(optAndFinancialCoupon.getStatus(), 0)) {
            couponModule.setCouponStatus(CouponStatusEnum.NOT_ASSIGN.getCode());
        } else {
            couponModule.setCouponStatus(CouponStatusEnum.ASSIGNED.getCode());
        }
        // 是否是活动曝光券
        couponModule.setExposure(false);
        // 领券按钮
        if (Objects.nonNull(optAndFinancialCoupon.getPromoCouponButton())) {
            PromoCouponButton promoCouponButton = optAndFinancialCoupon.getPromoCouponButton();
            ActionButton actionButton = new ActionButton();
            actionButton.setButtonText(promoCouponButton.getTitle());
            actionButton.setJumpUrl(promoCouponButton.getClickUrl());
            // 点击跳转
            if (Objects.equals(promoCouponButton.getActionType(), 1) && StringUtils.isNotBlank(promoCouponButton.getClickUrl())) {
                couponModule.setCouponStatus(CouponStatusEnum.JUMP_URL.getCode());
            }
            couponModule.setActionButton(actionButton);
        }
        couponModule.setPromotionType(PromotionTypeEnum.COUPON.getCode());
        // 普通优惠券领券栏
        PromotionBarModule promotionBarModule = buildPromotionBarModule(null, optAndFinancialCoupon.getTitle());
        couponModule.setPromotionBarModule(promotionBarModule);
        return couponModule;
    }

    /**
     * 获取会员卡优惠
     * promoType=16表示商家会员优惠（免费+付费）
     * @param priceDisplayDTO 价格展示DTO
     * @return 返回会员卡优惠DTO
     */
    private PromoDTO getMemberCardPromoDTO(PriceDisplayDTO priceDisplayDTO) {
         if (Objects.isNull(priceDisplayDTO) || CollectionUtils.isEmpty(priceDisplayDTO.getUsedPromos())) {
             return null;
         }
         List<String> memberCardPromoShowTypes = Lists.newArrayList("MEMBER_BENEFITS", "NEW_MEMBER_BENEFITS");
         return priceDisplayDTO.getUsedPromos().stream()
                .filter(promoDTO -> Objects.nonNull(promoDTO.getIdentity())
                        && promoDTO.getIdentity().getPromoType() == 16 && memberCardPromoShowTypes.contains(promoDTO.getIdentity().getPromoShowType()))
                .findFirst()
                .orElse(null);
    }
}
