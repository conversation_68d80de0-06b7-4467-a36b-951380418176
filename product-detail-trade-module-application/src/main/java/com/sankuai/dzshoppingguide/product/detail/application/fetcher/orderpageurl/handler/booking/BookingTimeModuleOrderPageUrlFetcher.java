package com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.handler.booking;

import com.dianping.dztrade.enums.BizCodeEnum;
import com.dianping.tuangu.dztg.usercenter.api.enums.CreateOrderPageSourceEnum;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.BaseOrderPageUrlFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.ext.param.request.ExtParamBuilderRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.FinalPriceFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.FetcherDAGStarter;
import com.sankuai.dzshoppingguide.product.detail.application.utils.LionConfigUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

@Fetcher(
        previousLayerDependencies = {
                FetcherDAGStarter.class,
                FinalPriceFetcher.class
        }
)
@Slf4j
public class BookingTimeModuleOrderPageUrlFetcher extends BaseOrderPageUrlFetcher {

    @Override
    protected boolean isContinue() {
        return LionConfigUtils.getOneProductMultiSwitch();
    }

    @Override
    protected Map<String, String> buildCustomExtParams() {
        return new HashMap<>();
    }

    @Override
    protected void fulfillExtParamBuilderRequest(final ExtParamBuilderRequest builderRequest) {
    }

    @Override
    protected String getPageSourceType() {
        return CreateOrderPageSourceEnum.GROUP_DETAIL_TIME_SELECT.getType();
    }


    @Override
    protected int getBizType() {
        // 参考 https://km.sankuai.com/collabpage/2711196576
        return BizCodeEnum.unified_book.getUniBizType();
    }

}
