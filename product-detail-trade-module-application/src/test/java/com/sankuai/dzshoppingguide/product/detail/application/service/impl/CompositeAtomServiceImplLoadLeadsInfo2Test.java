package com.sankuai.dzshoppingguide.product.detail.application.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.dianping.csc.center.engine.access.dto.AccessRequestDTO;
import com.dianping.csc.center.engine.access.dto.AccessResponseDTO;
import com.dianping.csc.center.engine.access.service.AccessInService;
import com.dianping.deal.sales.common.datatype.IResponse;
import com.dianping.deal.sales.common.datatype.SalesDisplayInfoDTO;
import com.dianping.deal.sales.common.datatype.SalesDisplayQueryRequest;
import com.dianping.deal.sales.common.datatype.SalesSubjectParam;
import com.dianping.deal.sales.display.api.service.SalesDisplayQueryService;
import com.dianping.gm.marketing.times.card.api.dto.CardResponse;
import com.dianping.gmkt.scene.api.delivery.dto.req.QueryExposureResourcesReqDTO;
import com.dianping.gmkt.scene.api.delivery.dto.res.DeliveryCommonResponse;
import com.dianping.gmkt.scene.api.delivery.dto.res.ResourceExposureResponseDTO;
import com.dianping.gmkt.scene.api.delivery.enums.DeliveryErrorCode;
import com.dianping.gmkt.scene.api.delivery.service.ResourcesExposureService;
import com.dianping.pay.promo.display.api.PromoDisplayService;
import com.dianping.pay.promo.display.api.dto.PromoDisplayDTO;
import com.dianping.pay.promo.display.api.dto.QueryPromoDisplayRequest;
import com.dianping.pigeon.remoting.common.domain.CallMethod;
import com.dianping.pigeon.remoting.common.exception.RpcException;
import com.dianping.product.shelf.common.dto.activity.ActivityDetailDTO;
import com.dianping.product.shelf.common.request.activities.ActivityProductQueryRequest;
import com.dianping.product.shelf.query.api.ActivityShelfQueryService;
import com.dianping.spc.common.Response;
import com.dianping.tgc.open.entity.BatchExProxyCouponRequest;
import com.dianping.tgc.open.entity.BatchExProxyCouponResponseDTO;
import com.dianping.tgc.open.v2.TGCGetCouponComponentQueryService;
import com.dianping.tpfun.product.api.sku.pintuan.PinFacadeService;
import com.dianping.tpfun.product.api.sku.pintuan.dto.PinProductBrief;
import com.dianping.tpfun.product.api.sku.pintuan.request.GetPinProductBriefReq;
import com.dianping.tpfun.product.api.sku.pintuan.request.GetPinProductIdOptional;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.SettableFuture;
import com.meituan.inf.xmdlog.XMDLogFormat;
import com.meituan.mdp.boot.starter.pigeon.annotation.MdpPigeonClient;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftClient;
import com.meituan.service.mobile.mtthrift.callback.OctoThriftCallback;
import com.meituan.service.mobile.mtthrift.netty.ContextStore;
import com.sankuai.clr.content.process.gateway.thrift.api.LeadsQueryGatewayService;
import com.sankuai.clr.content.process.gateway.thrift.dto.leads.LoadLeadsInfoReqDTO;
import com.sankuai.clr.content.process.gateway.thrift.dto.leads.LoadLeadsInfoRespDTO;
import com.sankuai.clr.content.process.thrift.dto.leads.dto.LeadsItemDTO;
import com.sankuai.clr.content.process.thrift.dto.leads.dto.LeadsLogDTO;
import com.sankuai.clr.content.process.thrift.dto.leads.dto.LeadsPromotionDTO;
import com.sankuai.clr.content.process.thrift.dto.leads.dto.LeadsTermsDTO;
import com.sankuai.dealuser.price.display.api.PriceDisplayService;
import com.sankuai.dealuser.price.display.api.model.BatchPriceRequest;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PriceResponse;
import com.sankuai.dzcard.navigation.api.DzCardExposureService;
import com.sankuai.dzcard.navigation.api.DzCardQueryService;
import com.sankuai.dzcard.navigation.api.dto.CardHoldStatusDTO;
import com.sankuai.dzcard.navigation.api.dto.DzCardQueryRequest;
import com.sankuai.dzcard.navigation.api.dto.FindDCCardHoldStatusLiteReqDTO;
import com.sankuai.dzcard.navigation.api.dto.QueryCardInfoDTO;
import com.sankuai.dzcard.navigation.api.dto.QueryDiscountCardReq;
import com.sankuai.dzim.cliententry.ClientEntryService;
import com.sankuai.dzim.cliententry.dto.ClientEntryDTO;
import com.sankuai.dzim.cliententry.dto.ClientEntryReqDTO;
import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
import com.sankuai.dzshoppingguide.product.detail.application.service.dto.ShopMemberDetailV;
import com.sankuai.dzshoppingguide.product.detail.application.service.dto.ShopMemberQueryResponse;
import com.sankuai.dzshoppingguide.product.detail.domain.utils.PigeonCallbackUtils;
import com.sankuai.dzshoppingguide.product.detail.domain.utils.ThriftAsyncUtils;
import com.sankuai.dztheme.deal.DealSkuService;
import com.sankuai.dztheme.deal.req.SkuOptionBatchRequest;
import com.sankuai.dztheme.deal.res.DealSkuSummaryDTO;
import com.sankuai.dztheme.massagebook.theme.ReserveThemeQueryService;
import com.sankuai.dztheme.massagebook.theme.req.ReserveQueryRequest;
import com.sankuai.dztheme.massagebook.theme.res.ReserveQueryResponse;
import com.sankuai.general.product.query.center.client.builder.model.DealGroupDisplayShopBuilder;
import com.sankuai.general.product.query.center.client.builder.model.DealGroupIdBuilder;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResponse;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResult;
import com.sankuai.general.product.query.center.client.service.DealGroupQueryService;
import com.sankuai.meituan.charity.merchant.main.sdk.WelfareDocFacade;
import com.sankuai.meituan.charity.merchant.main.sdk.dto.request.DzVpoiReq;
import com.sankuai.meituan.charity.merchant.main.sdk.dto.response.DzProductDocResp;
import com.sankuai.merchantcard.timescard.exposure.TimesCardNavigationService;
import com.sankuai.merchantcard.timescard.exposure.dto.CardSummaryBarDTO;
import com.sankuai.merchantcard.timescard.exposure.res.QueryDealGroupCardBarSummaryRequest;
import com.sankuai.mktplay.center.mkt.play.center.client.PlayCenterService;
import com.sankuai.mktplay.center.mkt.play.center.client.PlayExecuteRequest;
import com.sankuai.mktplay.center.mkt.play.center.client.PlayExecuteResponse;
import com.sankuai.mktplay.center.mkt.play.center.client.ScenePlayExecuteRequest;
import com.sankuai.mpmctmember.query.common.enums.MemberInterestCodeEnum;
import com.sankuai.mpmctmember.query.common.enums.MemberInterestSubjectTypeEnum;
import com.sankuai.mpmctmember.query.common.enums.MemberQueryFieldEnum;
import com.sankuai.mpmctmember.query.common.enums.UserMemberTypeEnum;
import com.sankuai.mpmctmember.query.thrift.api.MemberInterestQueryService;
import com.sankuai.mpmctmember.query.thrift.api.MemberPlanQueryService;
import com.sankuai.newdzcard.supply.dto.DzCardDTO;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import org.apache.thrift.TException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;

@ExtendWith(MockitoExtension.class)
class CompositeAtomServiceImplLoadLeadsInfo2Test {

    @Mock
    private LeadsQueryGatewayService leadsQueryGatewayServiceFuture;

    @InjectMocks
    private CompositeAtomServiceImpl compositeAtomService;

    private SettableFuture<LoadLeadsInfoRespDTO> settableFuture;

    @BeforeEach
    void setUp() {
        // Reset ContextStore and create new SettableFuture before each test
        ContextStore.removeFuture();
        settableFuture = SettableFuture.create();
        ContextStore.setFuture(settableFuture);
    }

    /**
     * 测试正常流程，服务调用成功并返回预期结果
     */
    @Test
    void testLoadLeadsInfoNormal() throws Throwable {
        // arrange
        LoadLeadsInfoReqDTO request = new LoadLeadsInfoReqDTO();
        LoadLeadsInfoRespDTO expectedResponse = new LoadLeadsInfoRespDTO();
        expectedResponse.setTitle("Test Title");
        when(leadsQueryGatewayServiceFuture.loadLeadsInfo(any(LoadLeadsInfoReqDTO.class))).thenReturn(expectedResponse);
        // act
        CompletableFuture<LoadLeadsInfoRespDTO> future = compositeAtomService.loadLeadsInfo(request);
        settableFuture.set(expectedResponse);
        // assert
        assertNotNull(future);
        assertEquals(expectedResponse, future.get());
        verify(leadsQueryGatewayServiceFuture).loadLeadsInfo(request);
    }

    /**
     * 测试返回结果为null的情况
     */
    @Test
    void testLoadLeadsInfoWhenResponseIsNull() throws Throwable {
        // arrange
        LoadLeadsInfoReqDTO request = new LoadLeadsInfoReqDTO();
        when(leadsQueryGatewayServiceFuture.loadLeadsInfo(any(LoadLeadsInfoReqDTO.class))).thenReturn(null);
        // act
        CompletableFuture<LoadLeadsInfoRespDTO> future = compositeAtomService.loadLeadsInfo(request);
        settableFuture.set(null);
        // assert
        assertNotNull(future);
        assertNull(future.get());
        verify(leadsQueryGatewayServiceFuture).loadLeadsInfo(request);
    }
}
