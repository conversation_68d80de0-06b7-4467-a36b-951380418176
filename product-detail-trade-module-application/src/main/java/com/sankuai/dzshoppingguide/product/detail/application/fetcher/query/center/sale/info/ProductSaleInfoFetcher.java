package com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sale.info;

import com.alibaba.fastjson.JSON;
import com.dianping.cat.Cat;
import com.dianping.lion.client.Lion;
import com.google.common.collect.Maps;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dz.product.detail.RequestSourceEnum;
import com.sankuai.dz.product.detail.gateway.spi.enums.ProductTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.cepinpool.CostEffectivePinPoolFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.cepinpool.dto.CostEffectivePinTuan;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategoryFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.live.LiveInfoDTO;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.live.LiveInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.membercard.card.integration.IntegrationMemberCard;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.membercard.card.integration.IntegrationMemberCardFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttrFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sale.info.enums.ProductSaleStatusEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sale.info.enums.ProductSaleTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sale.info.exception.ProductSaleInfoBuildException;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.stock.common.CommonStock;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.stock.common.CommonStockFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.service.dto.ShopMemberDetailV;
import com.sankuai.dzshoppingguide.product.detail.application.utils.VersionUtils;
import com.sankuai.general.product.query.center.client.dto.DealGroupBasicDTO;
import com.sankuai.general.product.query.center.client.enums.DealGroupStatusEnum;
import com.sankuai.general.product.query.center.client.enums.TradeTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.time.DateUtils;

import java.text.ParseException;
import java.util.*;
import java.util.concurrent.CompletableFuture;

import static com.sankuai.dzshoppingguide.product.detail.domain.utils.LionConstants.DZTGDETAIL_APPKEY;

/**
 * @Author: guangyujie
 * @Date: 2025/4/2 14:19
 */
@Fetcher(
        previousLayerDependencies = {
                ProductAttrFetcher.class,
                ProductCategoryFetcher.class,
                ProductBaseInfoFetcher.class,
                CostEffectivePinPoolFetcher.class,
                IntegrationMemberCardFetcher.class,
                CommonStockFetcher.class,
                LiveInfoFetcher.class
        }
)
@Slf4j
public class ProductSaleInfoFetcher extends NormalFetcherContext<ProductSaleInfo> {

    private ProductAttr productAttr;
    private ProductCategory productCategory;
    private ProductBaseInfo productBaseInfo;
    private CostEffectivePinTuan costEffectivePinTuan;//特团拼团信息
    private IntegrationMemberCard integrationMemberCard;
    private CommonStock commonStock;
    private LiveInfoDTO liveInfoDTO;

    @Override
    protected CompletableFuture<ProductSaleInfo> doFetch() throws Exception {
        try {
            productAttr = getDependencyResult(ProductAttrFetcher.class);
            productCategory = getDependencyResult(ProductCategoryFetcher.class);
            productBaseInfo = getDependencyResult(ProductBaseInfoFetcher.class);
            costEffectivePinTuan = getDependencyResult(CostEffectivePinPoolFetcher.class);
            integrationMemberCard = getDependencyResult(IntegrationMemberCardFetcher.class);
            commonStock = getDependencyResult(CommonStockFetcher.class);
            liveInfoDTO = getDependencyResult(LiveInfoFetcher.class);
            if (Objects.isNull(productBaseInfo)) {
                return CompletableFuture.completedFuture(ProductSaleInfo.getDefaultProductSaleInfo());
            }
            ProductSaleInfo saleInfo;
            if (isPreviewDeal()) {
                saleInfo = new ProductSaleInfo(ProductSaleTypeEnum.PREVIEW_DEAL, ProductSaleStatusEnum.OFFLINE);
            } else if (Optional.ofNullable(liveInfoDTO).map(LiveInfoDTO::isLiveSourceProduct).orElse(false)) {
                saleInfo = buildLiveSaleInfo();
            } else if (isMemberExclusive()) {
                saleInfo = buildMemberExclusiveSaleInfo();
            } else if (isPrePayDeal()) {
                saleInfo = new ProductSaleInfo(ProductSaleTypeEnum.PREPAY_DEAL, calculateBaseProductSaleStatus());
            } else if (isLeadsDeal()) {
                saleInfo = new ProductSaleInfo(ProductSaleTypeEnum.LEADS_DEAL, calculateBaseProductSaleStatus());
            } else if (isFitnessCrossDeal()) {
                saleInfo = new ProductSaleInfo(ProductSaleTypeEnum.FITNESS_CROSS_DEAL, calculateBaseProductSaleStatus());
            } else if (isFreeReservationDeal()) {
                saleInfo = new ProductSaleInfo(ProductSaleTypeEnum.FREE_RESERVATION_DEAL, calculateBaseProductSaleStatus());
            } else if (isCostEffectivePin()) {
                saleInfo = buildCostEffectivePinSaleInfo();
            } else {
                saleInfo = new ProductSaleInfo(ProductSaleTypeEnum.COMMON_DEAL, calculateBaseProductSaleStatus());
            }
            doLog(saleInfo);
            return CompletableFuture.completedFuture(saleInfo);
        } catch (Throwable throwable) {
            log.error("ProductSaleStatusFetcher,request:{}", JSON.toJSONString(request),
                    new ProductSaleInfoBuildException(String.format("构建团单售卖信息失败,throwable:%s,msg:%s",
                            throwable.getClass().getSimpleName(), throwable.getMessage()
                    ))
            );
            //兜底默认是普通团单且可以交易
            return CompletableFuture.completedFuture(ProductSaleInfo.getDefaultProductSaleInfo());
        }
    }

    private boolean isPreviewDeal() {
        if (Objects.isNull(productBaseInfo) || Objects.isNull(productBaseInfo.getBasic())) {
            return false;
        }
        return RequestSourceEnum.MERCHANT_PREVIEW.getSource().equals(request.getPageSource())
                || productBaseInfo.getBasic().getStatus() == DealGroupStatusEnum.PREVIEW.getCode();
    }

    private boolean isMemberExclusive() {
        if (VersionUtils.isLessThanOrEqual(request.getShepherdGatewayParam().getMrnVersion(), "0.5.2")) {
            return false;
        }
        if (integrationMemberCard == null
                || integrationMemberCard.getMemberCardInterest() == null) {
            return false;
        }
        return integrationMemberCard.getMemberCardInterest().isDealMemberExclusive();
    }

    /**
     * 交易保障团单 && pay_method == 1
     */
    private boolean isPrePayDeal() {
        if (productAttr == null) {
            return false;
        }
        boolean isTradeAssurance = "1".equals(productAttr.getProductAttrFirstValue("is_trade_assurance"));
        boolean isPayMethod = "1".equals(productAttr.getProductAttrFirstValue("pay_method"));
        return isTradeAssurance && isPayMethod;
    }

    private boolean isLeadsDeal() {
        if (Objects.isNull(productCategory)) {
            return false;
        }
        String categoryId = String.valueOf(productCategory.getProductSecondCategoryId());
        String key = String.format("%s.%s", productCategory.getProductSecondCategoryId(), productCategory.getProductThirdCategoryId());
        List<String> leadsDealCats = Lion.getList(DZTGDETAIL_APPKEY, "com.sankuai.dzu.tpbase.dztgdetailweb.leads.deal.cate.config", String.class, Collections.emptyList());
        if (CollectionUtils.isEmpty(leadsDealCats)) {
            return false;
        }
        return leadsDealCats.contains(key) || leadsDealCats.contains(categoryId);
    }

    /**
     * 判断是否是健身通商品
     */
    private boolean isFitnessCrossDeal() {
        if (productAttr == null) {
            return false;
        }
        return "fitnessPass".equals(productAttr.getProductAttrFirstValue("dealGroupFitnessPassConfig"));
    }

    private boolean isFreeReservationDeal() {
        int tradeType = Optional.ofNullable(productBaseInfo)
                .map(ProductBaseInfo::getBasic)
                .map(DealGroupBasicDTO::getTradeType)
                .orElse(0);
        return tradeType == TradeTypeEnum.RESERVATION.getCode();
    }

    private boolean isCostEffectivePin() {
        return Optional.ofNullable(costEffectivePinTuan)
                .map(CostEffectivePinTuan::isCePinTuanScene)
                .orElse(false);
    }

    private ProductSaleInfo buildLiveSaleInfo() throws ParseException {
        ProductSaleStatusEnum saleStatus = calculateBaseProductSaleStatus();
        if (saleStatus != ProductSaleStatusEnum.ONLINE) {
            //如果当前商品已经不可交易了就直接返回
            return new ProductSaleInfo(ProductSaleTypeEnum.LIVE_SOURCE_DEAL, saleStatus);
        }
        if (liveInfoDTO.getLiveId() <= 0 && liveInfoDTO.isOnlyLiveChannel()) {
            //如果不是直播渠道，但是商品是直播渠道专属，则不能购买
            return new ProductSaleInfo(ProductSaleTypeEnum.LIVE_SOURCE_DEAL, ProductSaleStatusEnum.LIVE_EXCLUSIVE);
        }
        if (!liveInfoDTO.isAllowSelling()) {
            //直播侧商品不可售
            return new ProductSaleInfo(ProductSaleTypeEnum.LIVE_SOURCE_DEAL, ProductSaleStatusEnum.BEFORE_SALE_BEGIN_TIME);
        }
        return new ProductSaleInfo(ProductSaleTypeEnum.LIVE_SOURCE_DEAL, ProductSaleStatusEnum.ONLINE);
    }

    private ProductSaleInfo buildMemberExclusiveSaleInfo() throws ParseException {
        ProductSaleStatusEnum saleStatus = calculateBaseProductSaleStatus();
        if (saleStatus != ProductSaleStatusEnum.ONLINE) {
            //如果当前商品已经不可交易了就直接返回
            return new ProductSaleInfo(ProductSaleTypeEnum.MEMBER_EXCLUSIVE_DEAL, saleStatus);
        }
        //否则需要判断是否是会员，是会员可以交易，不是会员不能交易
        boolean isMember = Optional.ofNullable(integrationMemberCard)
                .map(IntegrationMemberCard::getMemberCardInterest)
                .map(ShopMemberDetailV::isMember)
                .orElse(false);
        if (isMember) {
            return new ProductSaleInfo(ProductSaleTypeEnum.MEMBER_EXCLUSIVE_DEAL, ProductSaleStatusEnum.ONLINE);
        } else {
            return new ProductSaleInfo(ProductSaleTypeEnum.MEMBER_EXCLUSIVE_DEAL, ProductSaleStatusEnum.MEMBER_EXCLUSIVE);
        }
    }

    private ProductSaleInfo buildCostEffectivePinSaleInfo() throws ParseException {
        ProductSaleStatusEnum saleStatus = calculateBaseProductSaleStatus();
        Map<ProductSaleTypeEnum, ProductSaleStatusEnum> saleStatusMap = new HashMap<>();
        saleStatusMap.put(ProductSaleTypeEnum.COST_EFFECTIVE_PIN_TUAN, saleStatus);
        saleStatusMap.put(ProductSaleTypeEnum.COMMON_DEAL, saleStatus);
        return new ProductSaleInfo(saleStatusMap);
    }

    private void doLog(final ProductSaleInfo saleInfo) {
        for (Map.Entry<ProductSaleTypeEnum, ProductSaleStatusEnum> entry : saleInfo.getSaleStatus().entrySet()) {
            Map<String, String> tags = Maps.newHashMap();
            tags.put("saleType", entry.getKey().name());
            tags.put("saleStatus", entry.getValue().name());
            if (productCategory != null) {
                tags.put("category2", String.valueOf(productCategory.getProductSecondCategoryId()));
            }
            Cat.logMetricForCount("ProductSaleInfo", tags);
        }
    }

    /**
     * 只校验商品状态、售卖时间、基础库存
     */
    private ProductSaleStatusEnum calculateBaseProductSaleStatus() throws ParseException {
        if (productBaseInfo == null || productBaseInfo.getBasic() == null) {
            return ProductSaleStatusEnum.ONLINE;
        }
        Date beginDate = productBaseInfo.getBasic().getBeginSaleDate() != null ?
                DateUtils.parseDate(productBaseInfo.getBasic().getBeginSaleDate(), "yyyy-MM-dd HH:mm:ss") : null;
        Date endDate = productBaseInfo.getBasic().getEndSaleDate() != null ?
                DateUtils.parseDate(productBaseInfo.getBasic().getEndSaleDate(), "yyyy-MM-dd HH:mm:ss") : null;
        ProductSaleStatusEnum saleStatus = ProductSaleStatusEnum.ONLINE;
        if (beginDate != null && beginDate.compareTo(new Date()) > 0) {
            saleStatus = ProductSaleStatusEnum.BEFORE_SALE_BEGIN_TIME;//尚未开始
        } else if (endDate != null && endDate.compareTo(new Date()) < 0) {
            saleStatus = ProductSaleStatusEnum.AFTER_SALE_END_TIME;//已结束
        } else if (productBaseInfo.getBasic().getStatus() == DealGroupStatusEnum.OFFLINE.getCode()) {
            saleStatus = ProductSaleStatusEnum.OFFLINE;//已下线
        } else if (productBaseInfo.getBasic().getStatus() == DealGroupStatusEnum.PREVIEW.getCode()) {
            saleStatus = ProductSaleStatusEnum.OFFLINE;//预览团单已下线
        } else if (commonStock != null) {
            boolean isSoldOut = commonStock.getStock()
                    .map(stock -> request.getClientTypeEnum().isMtClientType() ? stock.getIsMtSoldOut() : stock.getIsDpSoldOut())
                    .orElse(false);
            if (isSoldOut) {
                saleStatus = ProductSaleStatusEnum.SOLD_OUT;
            }
        }
        return saleStatus;
    }

}
