package com.sankuai.dzshoppingguide.product.detail.application.utils;

import com.sankuai.dealuser.price.display.api.enums.PromoTypeEnum;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import com.sankuai.dzshoppingguide.product.detail.spi.promo.vo.DealDetailPromoDetailExtraInfo;
import com.sankuai.dzshoppingguide.product.detail.spi.promo.vo.DealBestPromoDetail;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Component
public class ButtonStyleHelper {

    protected Logger logger = LoggerFactory.getLogger(ButtonStyleHelper.class);


    public void entertainmentMemberPromoDetailComplete(PromoDTO promoDTO, DealBestPromoDetail bestPromoDetail) {
        try {
            if (isMemberOrDiscountPromoDto(promoDTO)) {
                BigDecimal promoDiscount = promoDTO.getPromoDiscount();
                BigDecimal discountRateWithinTen = promoDiscount.multiply(new BigDecimal(10)).stripTrailingZeros();

                bestPromoDetail.setIconUrl("https://p1.meituan.net/travelcube/86bb081f1be80d382eedefc7ca9b08264615.png");
                if (promoDTO.getIdentity().getPromoType() == PromoTypeEnum.DISCOUNT_CARD.getType()){
                    bestPromoDetail.setPromoName("会员享" + discountRateWithinTen + "折优惠");
                } else if(promoDTO.getIdentity().getPromoType() == PromoTypeEnum.MEMBER_DAY.getType()) {
                    bestPromoDetail.setPromoName("会员日享" + discountRateWithinTen + "折优惠");
                }
                buildPromoDetailExtraInfo(bestPromoDetail, promoDiscount, discountRateWithinTen);
            }

        } catch (Exception e) {
            logger.error("zuLiaoPromoDetailComplete error!", e);
        }
    }

    private boolean isMemberOrDiscountPromoDto(PromoDTO promoDTO) {
        return (promoDTO.getIdentity().getPromoType() == PromoTypeEnum.DISCOUNT_CARD.getType()
                || (promoDTO.getIdentity().getPromoType() == PromoTypeEnum.MEMBER_DAY.getType()))
                && promoDTO.getAmount() != null && promoDTO.getPromoDiscount() != null;
    }

    private void buildPromoDetailExtraInfo(DealBestPromoDetail bestPromoDetail, BigDecimal promoDiscount, BigDecimal discountRateWithinTen) {
        DealDetailPromoDetailExtraInfo promoDetailExtraInfo = new DealDetailPromoDetailExtraInfo();
        promoDetailExtraInfo.setPromoDetailExtraInfoType(1);
        promoDetailExtraInfo.setDiscountRateWithinOne(PriceHelper.dropLastZero(promoDiscount));
        promoDetailExtraInfo.setDiscountRateWithinTen(PriceHelper.dropLastZero(discountRateWithinTen));
        bestPromoDetail.setPromoDetailExtraInfo(promoDetailExtraInfo);
    }

}
