package com.sankuai.dzshoppingguide.product.detail.application.fetcher.sku;

import com.google.common.collect.Lists;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.FetcherDAGStarter;
import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
import com.sankuai.dztheme.deal.req.SkuOptionBatchRequest;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @create 2025/5/28 10:41
 */
@Fetcher(
        previousLayerDependencies = {FetcherDAGStarter.class}
)
@Slf4j
public class DealSkuSummaryFetcher extends NormalFetcherContext<DealSkuSummaryReturnValue> {

    @Autowired
    private CompositeAtomService compositeAtomService;
    
    @Override
    protected CompletableFuture<DealSkuSummaryReturnValue> doFetch(){
        try{
            return compositeAtomService.batchQuerySummary(buildRequest()).thenApply(res -> {
                DealSkuSummaryReturnValue returnValue = new DealSkuSummaryReturnValue();
                if (MapUtils.isNotEmpty(res)) {
                    return returnValue.setDealSkuSummaryDTO(res.getOrDefault(request.getProductId(), null));
                }
                return returnValue;
            });
        }catch (Exception e){
            log.error("DealSkuSummaryFetcher.doFetch error:", e);
        }
        return CompletableFuture.completedFuture(null);
    }
    
    private SkuOptionBatchRequest buildRequest(){
        boolean isMt = request.getClientTypeEnum().isMtClientType();
        IdTypeEnum idType = isMt ? IdTypeEnum.MT : IdTypeEnum.DP;
        SkuOptionBatchRequest skuOptionBatchRequest = new SkuOptionBatchRequest();
        skuOptionBatchRequest.setDealGroupIds(Lists.newArrayList(request.getProductId()));
        skuOptionBatchRequest.setDealGroupIdType(idType.getCode());
        return skuOptionBatchRequest;
    }
}
