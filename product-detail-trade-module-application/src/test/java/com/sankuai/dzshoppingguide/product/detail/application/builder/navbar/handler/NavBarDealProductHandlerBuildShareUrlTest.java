package com.sankuai.dzshoppingguide.product.detail.application.builder.navbar.handler;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.sankuai.dz.product.detail.RequestCustomParamEnum;
import com.sankuai.dz.product.detail.gateway.spi.dto.ShepherdGatewayParam;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.enums.MobileOSTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.best.shop.ShopInfo;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class NavBarDealProductHandlerBuildShareUrlTest {

    private NavBarDealProductHandler handler;

    @Mock
    private ProductDetailPageRequest request;

    @Mock
    private ShopIdMapper idMapper;

    @Mock
    private ShopInfo shopInfo;

    @Mock
    private DpPoiDTO dpPoiDTO;

    @Mock
    private ShepherdGatewayParam shepherdGatewayParam;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        handler = new NavBarDealProductHandler();
    }

    /**
     * 测试MT_APP客户端类型和IOS操作系统类型的分享URL生成
     */
    @Test
    void testBuildShareUrlMTAppWithIOS() throws Throwable {
        // arrange
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.MT_APP);
        when(request.getMobileOSType()).thenReturn(MobileOSTypeEnum.IOS);
        when(request.getProductId()).thenReturn(12345L);
        when(request.getShepherdGatewayParam()).thenReturn(shepherdGatewayParam);
        when(shepherdGatewayParam.getAppVersion()).thenReturn("10.0.0");
        when(request.getCustomParam(RequestCustomParamEnum.uuid)).thenReturn("test-uuid");
        when(request.getPageSource()).thenReturn("test-source");
        // act
        String result = handler.buildShareUrl(request, idMapper, shopInfo);
        // assert
        assertNotNull(result);
        assertTrue(result.contains("meituan.com/deal/12345.html"));
        assertTrue(result.contains("A$iosB"));
        assertTrue(result.contains("utm_medium=iosweb"));
        assertTrue(result.contains("source=test-source"));
    }

    /**
     * 测试MT_WX客户端类型和ANDROID操作系统类型的分享URL生成
     */
    @Test
    void testBuildShareUrlMTWxWithAndroid() throws Throwable {
        // arrange
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.MT_WX);
        when(request.getMobileOSType()).thenReturn(MobileOSTypeEnum.ANDROID);
        when(request.getProductId()).thenReturn(12345L);
        when(request.getShepherdGatewayParam()).thenReturn(shepherdGatewayParam);
        when(shepherdGatewayParam.getAppVersion()).thenReturn("10.0.0");
        when(request.getCustomParam(RequestCustomParamEnum.uuid)).thenReturn("test-uuid");
        when(request.getPageSource()).thenReturn("test-source");
        // act
        String result = handler.buildShareUrl(request, idMapper, shopInfo);
        // assert
        assertNotNull(result);
        assertTrue(result.contains("meituan.com/deal/12345.html"));
        assertTrue(result.contains("A$androidB"));
        assertTrue(result.contains("utm_medium=androidweb"));
        assertTrue(result.contains("source=test-source"));
    }

    /**
     * 测试DP_APP客户端类型和IOS操作系统类型且有shopUuid的分享URL生成
     */
    @Test
    void testBuildShareUrlDPAppWithIOSAndShopUuid() throws Throwable {
        // arrange
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.DP_APP);
        when(request.getMobileOSType()).thenReturn(MobileOSTypeEnum.IOS);
        when(request.getProductId()).thenReturn(12345L);
        when(request.getPoiId()).thenReturn(67890L);
        when(request.getPageSource()).thenReturn("test-source");
        when(shopInfo.getDpPoiDTO()).thenReturn(dpPoiDTO);
        when(dpPoiDTO.getUuid()).thenReturn("test-uuid");
        // act
        String result = handler.buildShareUrl(request, idMapper, shopInfo);
        // assert
        assertNotNull(result);
        assertTrue(result.contains("dianping.com/tuan/deal/12345"));
        assertTrue(result.contains("shopId=67890"));
        assertTrue(result.contains("shopUuid=test-uuid"));
        assertTrue(result.contains("source=test-source"));
    }

    /**
     * 测试DP_WX客户端类型和IOS操作系统类型但无shopUuid的分享URL生成
     */
    @Test
    void testBuildShareUrlDPWxWithIOSAndNoShopUuid() throws Throwable {
        // arrange
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.DP_WX);
        when(request.getMobileOSType()).thenReturn(MobileOSTypeEnum.IOS);
        when(request.getProductId()).thenReturn(12345L);
        when(request.getPoiId()).thenReturn(67890L);
        when(request.getPageSource()).thenReturn("test-source");
        when(shopInfo.getDpPoiDTO()).thenReturn(null);
        // act
        String result = handler.buildShareUrl(request, idMapper, shopInfo);
        // assert
        assertNotNull(result);
        assertTrue(result.contains("dianping.com/tuan/deal/12345"));
        assertTrue(result.contains("shopId=67890"));
        assertTrue(result.contains("shopUuid="));
        assertTrue(result.contains("source=test-source"));
    }

    /**
     * 测试DP_APP客户端类型和ANDROID操作系统类型的分享URL生成
     */
    @Test
    void testBuildShareUrlDPAppWithAndroid() throws Throwable {
        // arrange
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.DP_APP);
        when(request.getMobileOSType()).thenReturn(MobileOSTypeEnum.ANDROID);
        when(request.getProductId()).thenReturn(12345L);
        when(request.getPageSource()).thenReturn("test-source");
        // act
        String result = handler.buildShareUrl(request, idMapper, shopInfo);
        // assert
        assertNotNull(result);
        assertTrue(result.contains("dianping.com/tuan/deal/12345"));
        assertTrue(result.contains("utm_source=appshare"));
        assertTrue(result.contains("source=test-source"));
    }

    /**
     * 测试未知客户端类型的分享URL生成
     */
    @Test
    void testBuildShareUrlUnknownClientType() throws Throwable {
        // arrange
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.UNKNOWN);
        // act
        String result = handler.buildShareUrl(request, idMapper, shopInfo);
        // assert
        assertNull(result);
    }

    /**
     * 测试未知移动操作系统类型的分享URL生成
     */
    @Test
    void testBuildShareUrlUnknownMobileOSType() throws Throwable {
        // arrange
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.MT_APP);
        when(request.getMobileOSType()).thenReturn(MobileOSTypeEnum.UNKNOWN);
        when(request.getProductId()).thenReturn(12345L);
        when(request.getShepherdGatewayParam()).thenReturn(shepherdGatewayParam);
        when(shepherdGatewayParam.getAppVersion()).thenReturn("10.0.0");
        when(request.getCustomParam(RequestCustomParamEnum.uuid)).thenReturn("test-uuid");
        when(request.getPageSource()).thenReturn("test-source");
        // act
        String result = handler.buildShareUrl(request, idMapper, shopInfo);
        // assert
        assertNotNull(result);
        assertTrue(result.contains("meituan.com/deal/12345.html"));
        assertTrue(result.contains("utm_source=appshare"));
        assertTrue(result.contains("source=test-source"));
    }

    /**
     * 测试空shopInfo的分享URL生成
     */
    @Test
    void testBuildShareUrlWithNullShopInfo() throws Throwable {
        // arrange
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.DP_APP);
        when(request.getMobileOSType()).thenReturn(MobileOSTypeEnum.IOS);
        when(request.getProductId()).thenReturn(12345L);
        when(request.getPoiId()).thenReturn(67890L);
        when(request.getPageSource()).thenReturn("test-source");
        ShopInfo nullShopInfo = new ShopInfo();
        nullShopInfo.setDpPoiDTO(null);
        // act
        String result = handler.buildShareUrl(request, idMapper, nullShopInfo);
        // assert
        assertNotNull(result);
        assertTrue(result.contains("dianping.com/tuan/deal/12345"));
        assertTrue(result.contains("shopId=67890"));
        assertTrue(result.contains("shopUuid="));
        assertTrue(result.contains("source=test-source"));
    }

    /**
     * 测试空request的分享URL生成
     */
    @Test
    void testBuildShareUrlWithNullRequest() throws Throwable {
        // arrange
        // No setup needed for null request
        // act & assert
        assertThrows(NullPointerException.class, () -> handler.buildShareUrl(null, idMapper, shopInfo));
    }
}
