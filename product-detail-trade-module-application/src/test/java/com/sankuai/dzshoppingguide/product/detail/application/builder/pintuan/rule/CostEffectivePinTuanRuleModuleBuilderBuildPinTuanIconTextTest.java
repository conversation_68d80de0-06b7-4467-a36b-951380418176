package com.sankuai.dzshoppingguide.product.detail.application.builder.pintuan.rule;

import static org.junit.jupiter.api.Assertions.*;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.pintuan.ProductDetailGuaranteeItem;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * Unit tests for {@link CostEffectivePinTuanRuleModuleBuilder#buildPinTuanIconText}
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("CostEffectivePinTuanRuleModuleBuilder buildPinTuanIconText Tests")
public class CostEffectivePinTuanRuleModuleBuilderBuildPinTuanIconTextTest {

    @InjectMocks
    private CostEffectivePinTuanRuleModuleBuilder builder;

    /**
     * Test scenario: Build PinTuan icon text with valid text and icon values
     * Expected: Returns ProductDetailGuaranteeItem with correctly set text and icon
     */
    @Test
    @DisplayName("Should create guarantee item with valid text and icon")
    public void testBuildPinTuanIconText_WithValidValues() {
        // arrange
        String expectedText = "Sample Text";
        String expectedIcon = "sample_icon.png";
        // act
        ProductDetailGuaranteeItem result = builder.buildPinTuanIconText(expectedText, expectedIcon);
        // assert
        assertNotNull(result, "Result should not be null");
        assertEquals(expectedText, result.getText(), "Text should match input value");
        assertEquals(expectedIcon, result.getIcon(), "Icon should match input value");
    }

    /**
     * Test scenario: Build PinTuan icon text with empty strings
     * Expected: Returns ProductDetailGuaranteeItem with empty strings for text and icon
     */
    @Test
    @DisplayName("Should create guarantee item with empty strings")
    public void testBuildPinTuanIconText_WithEmptyStrings() {
        // arrange
        String expectedText = "";
        String expectedIcon = "";
        // act
        ProductDetailGuaranteeItem result = builder.buildPinTuanIconText(expectedText, expectedIcon);
        // assert
        assertNotNull(result, "Result should not be null");
        assertEquals(expectedText, result.getText(), "Text should be empty string");
        assertEquals(expectedIcon, result.getIcon(), "Icon should be empty string");
    }

    /**
     * Test scenario: Build PinTuan icon text with null values
     * Expected: Returns ProductDetailGuaranteeItem with null values for text and icon
     */
    @Test
    @DisplayName("Should create guarantee item with null values")
    public void testBuildPinTuanIconText_WithNullValues() {
        // arrange
        String expectedText = null;
        String expectedIcon = null;
        // act
        ProductDetailGuaranteeItem result = builder.buildPinTuanIconText(expectedText, expectedIcon);
        // assert
        assertNotNull(result, "Result should not be null");
        assertNull(result.getText(), "Text should be null");
        assertNull(result.getIcon(), "Icon should be null");
    }

    /**
     * Test scenario: Build PinTuan icon text with mixed null and valid values
     * Expected: Returns ProductDetailGuaranteeItem with one null value and one valid value
     */
    @Test
    @DisplayName("Should create guarantee item with mixed null and valid values")
    public void testBuildPinTuanIconText_WithMixedValues() {
        // arrange
        String expectedText = "Sample Text";
        String expectedIcon = null;
        // act
        ProductDetailGuaranteeItem result = builder.buildPinTuanIconText(expectedText, expectedIcon);
        // assert
        assertNotNull(result, "Result should not be null");
        assertEquals(expectedText, result.getText(), "Text should match input value");
        assertNull(result.getIcon(), "Icon should be null");
    }
}
