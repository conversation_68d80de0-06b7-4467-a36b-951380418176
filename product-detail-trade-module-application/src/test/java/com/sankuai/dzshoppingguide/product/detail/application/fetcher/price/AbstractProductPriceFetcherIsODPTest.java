package com.sankuai.dzshoppingguide.product.detail.application.fetcher.price;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.*;
import com.sankuai.dealuser.price.display.api.model.BatchPriceRequest;
import com.sankuai.dealuser.price.display.api.model.ClientEnv;
import com.sankuai.dealuser.price.display.api.model.ProductIdentity;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.dto.ProductPriceReturnValue;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
@DisplayName("AbstractProductPriceFetcher isODP Method Tests")
class AbstractProductPriceFetcherIsODPTest {

    @InjectMocks
    private TestableAbstractProductPriceFetcher fetcher;

    // Create a concrete implementation for testing
    private static class TestableAbstractProductPriceFetcher extends AbstractProductPriceFetcher<ProductPriceReturnValue> {

        @Override
        protected void postProcessPriceRequest(ClientEnv clientEnv, List<ProductIdentity> productIdentities, Map<String, String> extension) {
        }

        @Override
        protected CompletableFuture<ProductPriceReturnValue> doFetch() {
            return null;
        }
    }

    /**
     * Test valid ODP scenario with MT_APP client type
     */
    @Test
    @DisplayName("Should return true when client type is MT_APP and page source is ODP")
    void testIsODP_MtApp_WithODPSource() {
        // arrange
        ClientTypeEnum clientType = ClientTypeEnum.MT_APP;
        String pageSource = "odp";
        // act
        boolean result = fetcher.isODP(clientType, pageSource);
        // assert
        assertTrue(result);
    }

    /**
     * Test valid ODP scenario with DP_APP client type
     */
    @Test
    @DisplayName("Should return true when client type is DP_APP and page source is ODP")
    void testIsODP_DpApp_WithODPSource() {
        // arrange
        ClientTypeEnum clientType = ClientTypeEnum.DP_APP;
        String pageSource = "odp";
        // act
        boolean result = fetcher.isODP(clientType, pageSource);
        // assert
        assertTrue(result);
    }

    /**
     * Test invalid scenario with unsupported client type
     */
    @Test
    @DisplayName("Should return false when client type is unsupported")
    void testIsODP_UnsupportedClientType() {
        // arrange
        ClientTypeEnum clientType = ClientTypeEnum.MT_PC;
        String pageSource = "odp";
        // act
        boolean result = fetcher.isODP(clientType, pageSource);
        // assert
        assertFalse(result);
    }

    /**
     * Test invalid scenario with non-ODP page source
     */
    @Test
    @DisplayName("Should return false when page source is not ODP")
    void testIsODP_NonODPSource() {
        // arrange
        ClientTypeEnum clientType = ClientTypeEnum.MT_APP;
        String pageSource = "homepage";
        // act
        boolean result = fetcher.isODP(clientType, pageSource);
        // assert
        assertFalse(result);
    }

    /**
     * Test null client type scenario
     */
    @Test
    @DisplayName("Should return false when client type is null")
    void testIsODP_NullClientType() {
        // arrange
        String pageSource = "odp";
        // act
        boolean result = fetcher.isODP(null, pageSource);
        // assert
        assertFalse(result);
    }

    /**
     * Test null page source scenario
     */
    @Test
    @DisplayName("Should return false when page source is null")
    void testIsODP_NullPageSource() {
        // arrange
        ClientTypeEnum clientType = ClientTypeEnum.MT_APP;
        // act
        boolean result = fetcher.isODP(clientType, null);
        // assert
        assertFalse(result);
    }

    /**
     * Test empty page source scenario
     */
    @Test
    @DisplayName("Should return false when page source is empty")
    void testIsODP_EmptyPageSource() {
        // arrange
        ClientTypeEnum clientType = ClientTypeEnum.MT_APP;
        // act
        boolean result = fetcher.isODP(clientType, "");
        // assert
        assertFalse(result);
    }

    /**
     * Test both null inputs scenario
     */
    @Test
    @DisplayName("Should return false when both inputs are null")
    void testIsODP_BothNull() {
        // arrange & act
        boolean result = fetcher.isODP(null, null);
        // assert
        assertFalse(result);
    }
}
