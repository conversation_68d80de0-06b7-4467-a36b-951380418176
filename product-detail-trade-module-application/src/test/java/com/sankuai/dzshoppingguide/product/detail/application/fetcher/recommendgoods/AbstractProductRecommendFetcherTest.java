package com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommendgoods;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.dianping.martgeneral.recommend.api.entity.RecommendParameters;
import com.dianping.martgeneral.recommend.api.enums.PlatformEnum;
import com.sankuai.dz.product.detail.RequestCustomParamEnum;
import com.sankuai.dz.product.detail.gateway.spi.dto.CustomParam;
import com.sankuai.dz.product.detail.gateway.spi.dto.ShepherdGatewayParam;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * Test class for AbstractProductRecommendFetcher
 */
@ExtendWith(MockitoExtension.class)
public class AbstractProductRecommendFetcherTest {

    @InjectMocks
    private TestableAbstractProductRecommendFetcher fetcher;

    @Mock
    private ProductDetailPageRequest request;

    @Mock
    private ShepherdGatewayParam shepherdGatewayParam;

    @Mock
    private CustomParam customParam;

    /**
     * Test case for DP platform without user ID and default page settings
     */
    @Test
    public void testBuildRecommendParameters_DPPlatform_NoUserIdDefaultPageSettings() throws Throwable {
        // arrange
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.DP_APP);
        when(request.getCityId()).thenReturn(2);
        when(request.getUserLat()).thenReturn(39.90);
        when(request.getUserLng()).thenReturn(116.40);
        when(request.getUserId()).thenReturn(0L);
        when(request.getShepherdGatewayParam()).thenReturn(shepherdGatewayParam);
        when(shepherdGatewayParam.getDeviceId()).thenReturn("dp-device-id");
        when(request.getCustomParam()).thenReturn(null);
        // act
        RecommendParameters result = fetcher.buildRecommendParameters();
        // assert
        assertEquals(PlatformEnum.DP, result.getPlatformEnum());
        assertEquals(2, result.getCityId());
        assertEquals(39.90, result.getLat());
        assertEquals(116.40, result.getLng());
        assertNull(result.getOriginUserId());
        assertEquals("dp-device-id", result.getDpid());

    }

    /**
     * Test case for building biz parameters with default settings
     */
    @Test
    public void testBuildRecommendParameters_WithBizParamsAndDefaultSettings() throws Throwable {
        // arrange
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.MT_APP);
        when(request.getCityId()).thenReturn(4);
        when(request.getUserLat()).thenReturn(23.45);
        when(request.getUserLng()).thenReturn(113.20);
        when(request.getShepherdGatewayParam()).thenReturn(shepherdGatewayParam);
        when(shepherdGatewayParam.getDeviceId()).thenReturn("test-device-id");
        when(request.getCustomParam()).thenReturn(null);
        Map<String, Object> expectedBizParams = new HashMap<>();
        expectedBizParams.put("testKey", "testValue");
        // act
        RecommendParameters result = fetcher.buildRecommendParameters();
        // assert
        assertNotNull(result.getBizParams());
        assertEquals(expectedBizParams.get("testKey"), result.getBizParams().get("testKey"));
        assertEquals(10, result.getPageSize());
        assertEquals(1, result.getPageNumber());
        assertEquals(4, result.getCityId());
        assertEquals(23.45, result.getLat());
        assertEquals(113.20, result.getLng());
        assertEquals("test-device-id", result.getUuid());
        assertEquals(1, result.getBizId());
    }

    /**
     * Helper class for testing abstract class
     */
    private static class TestableAbstractProductRecommendFetcher extends AbstractProductRecommendFetcher {

        @Override
        Map<String, Object> buildBizParams() {
            Map<String, Object> params = new HashMap<>();
            params.put("testKey", "testValue");
            return params;
        }

        @Override
        int getBizId() {
            return 1;
        }

        @Override
        int getMinQueryNum() {
            return 3;
        }

        @Override
        protected CompletableFuture<ProductRecommendResult> doFetch() throws Exception {
            return null;
        }
    }
}
