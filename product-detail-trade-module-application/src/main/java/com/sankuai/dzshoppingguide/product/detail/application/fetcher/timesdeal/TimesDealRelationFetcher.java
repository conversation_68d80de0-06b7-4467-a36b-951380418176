package com.sankuai.dzshoppingguide.product.detail.application.fetcher.timesdeal;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.ComponentFetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.response.FetcherResponse;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.ComponentFetcherContext;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.QueryCenterAggregateFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.QueryCenterAggregateReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.timesdeal.dto.TimesDealRelation;
import com.sankuai.general.product.query.center.client.builder.model.DealGroupCombineBuilder;
import com.sankuai.general.product.query.center.client.builder.requset.QueryByDealGroupIdRequestBuilder;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.combine.CombineItemDTO;
import com.sankuai.general.product.query.center.client.enums.CombineTypeEnum;
import com.sankuai.general.product.query.center.client.enums.IdTypeEnum;
import org.apache.commons.collections.CollectionUtils;

import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/13 11:44
 */
@ComponentFetcher(
        aggregateFetcher = QueryCenterAggregateFetcher.class
        // ,
        // fillRequestDependencies = {
        //         ShopIdMapperFetcher.class
        // }
)
public class TimesDealRelationFetcher extends ComponentFetcherContext<
        QueryByDealGroupIdRequestBuilder,
        QueryCenterAggregateReturnValue,
        TimesDealRelation> {
    @Override
    public void fulfillRequest(QueryByDealGroupIdRequestBuilder queryByDealGroupIdRequestBuilder) {
        boolean isMt = request.getClientTypeEnum().isMtClientType();

        // ShopIdMapper shopIdMapper = getDependencyResult(ShopIdMapperFetcher.class);
        // long mtShopId = Optional.ofNullable(shopIdMapper).map(ShopIdMapper::getMtBestShopId).orElse(0L);
        // long dpShopId = Optional.ofNullable(shopIdMapper).map(ShopIdMapper::getDpBestShopId).orElse(0L);

        IdTypeEnum idTypeEnum = isMt ? IdTypeEnum.MT : IdTypeEnum.DP;
        queryByDealGroupIdRequestBuilder
                .combine(DealGroupCombineBuilder
                        .builder(idTypeEnum)
                        .combineTypeEnum(CombineTypeEnum.COUNT_CARD_RELATION)
                        // todo 这个地方的shopId如果获取不到话对查询的结果会有什么影响
                        .shopId(request.getPoiId()));
    }

    @Override
    protected FetcherResponse<TimesDealRelation> mapResult(FetcherResponse<QueryCenterAggregateReturnValue> aggregateResult) {
        Optional<DealGroupDTO> dealGroupDTOOptional = Optional.ofNullable(aggregateResult)
                .map(FetcherResponse::getReturnValue)
                .map(QueryCenterAggregateReturnValue::getDealGroupDTO);

        if (!dealGroupDTOOptional.isPresent()) {
            return FetcherResponse.succeed(null);//聚合查询失败已经报错了，这里无需再报错
        }
        DealGroupDTO dealGroupDTO = dealGroupDTOOptional.get();

        if (CollectionUtils.isEmpty(dealGroupDTO.getCombines())) {
            return FetcherResponse.succeed(null);
        }
        Set<Long> combineDealIds = dealGroupDTO.getCombines().stream().filter(combineDTO -> CollectionUtils.isNotEmpty(combineDTO.getCombineItems()))
                .flatMap(combineDTO -> combineDTO.getCombineItems().stream()).map(CombineItemDTO::getCombineItemId)
                .collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(combineDealIds)) {
            return FetcherResponse.succeed(null);
        }
        combineDealIds.add(request.getProductId());
        return FetcherResponse.succeed(TimesDealRelation.builder().combineDealIds(combineDealIds).build());
    }
}
