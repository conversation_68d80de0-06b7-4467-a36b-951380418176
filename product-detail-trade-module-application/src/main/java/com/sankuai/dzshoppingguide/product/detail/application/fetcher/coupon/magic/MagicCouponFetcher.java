package com.sankuai.dzshoppingguide.product.detail.application.fetcher.coupon.magic;

import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dzshoppingguide.product.detail.application.builder.pricebar.builder.enums.CouponGuideTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.pricebar.component.InflateCouponTipsComponent;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.ProductPromoPriceFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.dto.ProductPriceReturnValue;
import com.sankuai.nibmkt.promotion.api.query.model.magicalmember.response.MMCTooltipTextDTO;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @date 2025-04-24
 * @desc
 */
@Fetcher(
        previousLayerDependencies = {
                ProductPromoPriceFetcher.class
        }
)
public class MagicCouponFetcher extends NormalFetcherContext<MagicCouponInfo> {
    @Override
    protected CompletableFuture<MagicCouponInfo> doFetch() throws Exception {
        ProductPriceReturnValue promoPriceReturnValue = getDependencyResult(ProductPromoPriceFetcher.class);
        if (Objects.isNull(promoPriceReturnValue) || Objects.isNull(promoPriceReturnValue.getPriceDisplayDTO())) {
            return CompletableFuture.completedFuture(null);
        }
        PriceDisplayDTO promoPriceDisplayDTO = promoPriceReturnValue.getPriceDisplayDTO();
        Map<String, String> extendDisplayInfoMap = promoPriceDisplayDTO.getExtendDisplayInfo();
        MMCTooltipTextDTO mmcTooltipTextDTO = InflateCouponTipsComponent.getMMCTooltipTextDTO(extendDisplayInfoMap);
        CouponGuideTypeEnum couponGuideTypeEnum = InflateCouponTipsComponent.getCouponGuideTypeEnum(promoPriceDisplayDTO.getUsedPromos(), promoPriceDisplayDTO.getPrice(), mmcTooltipTextDTO);
        MagicCouponInfo magicCouponInfo = new MagicCouponInfo();
        magicCouponInfo.setCouponGuideTypeEnum(couponGuideTypeEnum);
        return CompletableFuture.completedFuture(magicCouponInfo);
    }
}
