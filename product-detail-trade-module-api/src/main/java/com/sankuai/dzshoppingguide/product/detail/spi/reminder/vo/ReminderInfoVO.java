package com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo;

import com.dianping.mobile.framework.annotation.MobileDo;

import java.io.Serializable;

@MobileDo(id = 0x6dba)
public class ReminderInfoVO implements Serializable {
    /**
    * 前缀Icon
    */
    @MobileDo.MobileField(key = 0x7a3c)
    private Icon prefixIcon;

    /**
    * 后缀Icon
    */
    @MobileDo.MobileField(key = 0x26c8)
    private Icon suffixIcon;

    /**
    * 字体颜色
    */
    @MobileDo.MobileField(key = 0x2efe)
    private String fontColor;

    /**
    * 字体大小
    */
    @MobileDo.MobileField(key = 0xb53a)
    private String fontSize;

    /**
    * 文本内容
    */
    @MobileDo.MobileField(key = 0x451b)
    private String text;

    public Icon getPrefixIcon() {
        return prefixIcon;
    }

    public void setPrefixIcon(Icon prefixIcon) {
        this.prefixIcon = prefixIcon;
    }

    public Icon getSuffixIcon() {
        return suffixIcon;
    }

    public void setSuffixIcon(Icon suffixIcon) {
        this.suffixIcon = suffixIcon;
    }

    public String getFontColor() {
        return fontColor;
    }

    public void setFontColor(String fontColor) {
        this.fontColor = fontColor;
    }

    public String getFontSize() {
        return fontSize;
    }

    public void setFontSize(String fontSize) {
        this.fontSize = fontSize;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }
}