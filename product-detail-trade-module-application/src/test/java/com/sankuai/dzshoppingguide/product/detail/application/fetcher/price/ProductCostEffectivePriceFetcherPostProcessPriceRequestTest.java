package com.sankuai.dzshoppingguide.product.detail.application.fetcher.price;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.Mockito.*;
import com.sankuai.dealuser.price.display.api.enums.DzPriceSceneEnum;
import com.sankuai.dealuser.price.display.api.enums.ExtensionKeyEnum;
import com.sankuai.dealuser.price.display.api.model.ClientEnv;
import com.sankuai.dealuser.price.display.api.model.ProductIdentity;
import com.sankuai.dz.product.detail.RequestCustomParamEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class ProductCostEffectivePriceFetcherPostProcessPriceRequestTest {

    @Mock
    private ProductDetailPageRequest request;

    // Test-specific subclass to access protected members
    private static class TestProductCostEffectivePriceFetcher extends ProductCostEffectivePriceFetcher {

        public void setRequest(ProductDetailPageRequest request) {
            this.request = request;
        }
    }

    /**
     * Test base case where only DzPriceScene is set
     */
    @Test
    public void testPostProcessPriceRequest_BaseCase() throws Throwable {
        // arrange
        TestProductCostEffectivePriceFetcher fetcher = new TestProductCostEffectivePriceFetcher();
        fetcher.setRequest(request);
        ClientEnv clientEnv = new ClientEnv();
        List<ProductIdentity> productIdentityList = new ArrayList<>();
        Map<String, String> extension = new HashMap<>();
        when(request.getCustomParam(RequestCustomParamEnum.pintuanActivityId)).thenReturn(null);
        when(request.getCustomParam(RequestCustomParamEnum.orderGroupId)).thenReturn(null);
        // act
        fetcher.postProcessPriceRequest(clientEnv, productIdentityList, extension);
        // assert
        assertEquals(String.valueOf(DzPriceSceneEnum.COST_EFFECTIVE_PIN_TUAN_DEDUCTION.getCode()), extension.get(ExtensionKeyEnum.DzPriceScene.getDesc()));
    }

    /**
     * Test case with pintuanActivityId parameter
     */
    @Test
    public void testPostProcessPriceRequest_WithPintuanActivityId() throws Throwable {
        // arrange
        TestProductCostEffectivePriceFetcher fetcher = new TestProductCostEffectivePriceFetcher();
        fetcher.setRequest(request);
        ClientEnv clientEnv = new ClientEnv();
        List<ProductIdentity> productIdentityList = new ArrayList<>();
        ProductIdentity identity = new ProductIdentity();
        identity.setExtParams(new HashMap<>());
        productIdentityList.add(identity);
        Map<String, String> extension = new HashMap<>();
        String pintuanActivityId = "123456";
        when(request.getCustomParam(RequestCustomParamEnum.pintuanActivityId)).thenReturn(pintuanActivityId);
        when(request.getCustomParam(RequestCustomParamEnum.orderGroupId)).thenReturn(null);
        // act
        fetcher.postProcessPriceRequest(clientEnv, productIdentityList, extension);
        // assert
        assertEquals(String.valueOf(DzPriceSceneEnum.COST_EFFECTIVE_PIN_TUAN_DEDUCTION.getCode()), extension.get(ExtensionKeyEnum.DzPriceScene.getDesc()));
        assertEquals(pintuanActivityId, productIdentityList.get(0).getExtParams().get(ExtensionKeyEnum.PinTuanActivityId.getDesc()));
    }

    /**
     * Test case with orderGroupId parameter
     */
    @Test
    public void testPostProcessPriceRequest_WithOrderGroupId() throws Throwable {
        // arrange
        TestProductCostEffectivePriceFetcher fetcher = new TestProductCostEffectivePriceFetcher();
        fetcher.setRequest(request);
        ClientEnv clientEnv = new ClientEnv();
        List<ProductIdentity> productIdentityList = new ArrayList<>();
        ProductIdentity identity = new ProductIdentity();
        identity.setExtParams(new HashMap<>());
        productIdentityList.add(identity);
        Map<String, String> extension = new HashMap<>();
        String orderGroupId = "789012";
        when(request.getCustomParam(RequestCustomParamEnum.pintuanActivityId)).thenReturn(null);
        when(request.getCustomParam(RequestCustomParamEnum.orderGroupId)).thenReturn(orderGroupId);
        // act
        fetcher.postProcessPriceRequest(clientEnv, productIdentityList, extension);
        // assert
        assertEquals(String.valueOf(DzPriceSceneEnum.COST_EFFECTIVE_PIN_TUAN_DEDUCTION.getCode()), extension.get(ExtensionKeyEnum.DzPriceScene.getDesc()));
        assertEquals(orderGroupId, productIdentityList.get(0).getExtParams().get(ExtensionKeyEnum.ShareToken.getDesc()));
    }

    /**
     * Test case with both pintuanActivityId and orderGroupId parameters
     */
    @Test
    public void testPostProcessPriceRequest_WithBothParameters() throws Throwable {
        // arrange
        TestProductCostEffectivePriceFetcher fetcher = new TestProductCostEffectivePriceFetcher();
        fetcher.setRequest(request);
        ClientEnv clientEnv = new ClientEnv();
        List<ProductIdentity> productIdentityList = new ArrayList<>();
        ProductIdentity identity = new ProductIdentity();
        identity.setExtParams(new HashMap<>());
        productIdentityList.add(identity);
        Map<String, String> extension = new HashMap<>();
        String pintuanActivityId = "123456";
        String orderGroupId = "789012";
        when(request.getCustomParam(RequestCustomParamEnum.pintuanActivityId)).thenReturn(pintuanActivityId);
        when(request.getCustomParam(RequestCustomParamEnum.orderGroupId)).thenReturn(orderGroupId);
        // act
        fetcher.postProcessPriceRequest(clientEnv, productIdentityList, extension);
        // assert
        assertEquals(String.valueOf(DzPriceSceneEnum.COST_EFFECTIVE_PIN_TUAN_DEDUCTION.getCode()), extension.get(ExtensionKeyEnum.DzPriceScene.getDesc()));
        assertEquals(pintuanActivityId, productIdentityList.get(0).getExtParams().get(ExtensionKeyEnum.PinTuanActivityId.getDesc()));
        assertEquals(orderGroupId, productIdentityList.get(0).getExtParams().get(ExtensionKeyEnum.ShareToken.getDesc()));
    }

    /**
     * Test case with empty productIdentityList
     */
    @Test
    public void testPostProcessPriceRequest_EmptyProductIdentityList() throws Throwable {
        // arrange
        TestProductCostEffectivePriceFetcher fetcher = new TestProductCostEffectivePriceFetcher();
        fetcher.setRequest(request);
        ClientEnv clientEnv = new ClientEnv();
        List<ProductIdentity> productIdentityList = new ArrayList<>();
        Map<String, String> extension = new HashMap<>();
        String pintuanActivityId = "123456";
        String orderGroupId = "789012";
        when(request.getCustomParam(RequestCustomParamEnum.pintuanActivityId)).thenReturn(pintuanActivityId);
        when(request.getCustomParam(RequestCustomParamEnum.orderGroupId)).thenReturn(orderGroupId);
        // act
        fetcher.postProcessPriceRequest(clientEnv, productIdentityList, extension);
        // assert
        assertEquals(String.valueOf(DzPriceSceneEnum.COST_EFFECTIVE_PIN_TUAN_DEDUCTION.getCode()), extension.get(ExtensionKeyEnum.DzPriceScene.getDesc()));
    }
}
