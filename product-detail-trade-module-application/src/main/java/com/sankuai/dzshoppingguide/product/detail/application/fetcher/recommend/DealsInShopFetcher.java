package com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommend;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommend.dto.DpDealsInShopReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommend.dto.DealsInShopReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * @Author: zhangyuan103
 * @Date: 2025/5/23
 * @Description 商户下的所有团单id，区分平台
 */
@Fetcher(previousLayerDependencies = {DpDealsInShopFetcher.class})
@Slf4j
public class DealsInShopFetcher extends NormalFetcherContext<DealsInShopReturnValue> {
    @Resource
    CompositeAtomService compositeAtomService;

    @Override
    protected CompletableFuture<DealsInShopReturnValue> doFetch() throws Exception {
        boolean isMt = request.getClientTypeEnum().isMtClientType();
        DpDealsInShopReturnValue dpDealIdReturnValue = getDependencyResult(DpDealsInShopFetcher.class);
        if (dpDealIdReturnValue == null || CollectionUtils.isEmpty(dpDealIdReturnValue.getDpAllDealIds())) {
            return CompletableFuture.completedFuture(null);
        }
        if (isMt) {
            List<Long> dpAllDealIds = dpDealIdReturnValue.getDpAllDealIds();
            return compositeAtomService
                    .convertDpGroupIdsToMtGroupIds(dpAllDealIds.subList(0, Math.min(dpAllDealIds.size(), 100)))
                    .thenApply(dp2mtDealIds -> {
                        List<Long> mtDealGroupIds = new ArrayList<>(dp2mtDealIds.values());
                        return new DealsInShopReturnValue(mtDealGroupIds);
                    });
        }
        return CompletableFuture.completedFuture(new DealsInShopReturnValue(dpDealIdReturnValue.getDpAllDealIds()));
    }
}
