package com.sankuai.dzshoppingguide.product.detail.spi.dtos;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025-03-28
 */
@Data
@MobileDo(id = 0x459c)
@TypeDoc(description = "商品最佳优惠算价公式")
public class ProductBestPromoFormula implements Serializable {

    @FieldDoc(description = "神会员黑盒红包价格字体颜色")
    @MobileField(key = 0x663)
    private String blackBoxPriceColor;

    @FieldDoc(description = "神会员黑盒红包价格")
    @MobileField(key = 0x5d5b)
    private String blackBoxPrice;

    @FieldDoc(description = "券是否已膨胀")
    @MobileField(key = 0x53ac)
    private boolean afterInflate;

    @FieldDoc(description = "神会员白盒红包图标")
    @MobileField(key = 0x188c)
    private Icon whiteBoxIcon;

    @FieldDoc(description = "神会员黑盒红包图标")
    @MobileField(key = 0x508d)
    private Icon blackBoxIcon;

    @FieldDoc(description = "弹窗说明")
    @MobileField(key = 0xd63e)
    private String priceDesc;

    @FieldDoc(description = "价格")
    @MobileField(key = 0xb716)
    private String price;

    @FieldDoc(description = "价格展示颜色")
    @MobileField(key = 0x54a6)
    private String priceColor;

    @FieldDoc(description = "价格符号，¥")
    @MobileField(key = 0x647d)
    private String priceSymbol;

    @FieldDoc(description = "优惠标签描述")
    @MobileField(key = 0xcc07)
    private String promoDesc;

}