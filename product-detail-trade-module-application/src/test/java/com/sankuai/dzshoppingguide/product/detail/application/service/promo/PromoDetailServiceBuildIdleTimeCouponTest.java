package com.sankuai.dzshoppingguide.product.detail.application.service.promo;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.dianping.deal.detail.dto.DealGroupDTO;
import com.dianping.product.shelf.common.dto.activity.ActivityDetailDTO;
import com.dianping.tgc.open.entity.PromoExposureInfo;
import com.google.common.collect.Lists;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.enums.ProductTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.promodetail.dto.PromotionDetailParams;
import com.sankuai.dzshoppingguide.product.detail.application.builder.promodetail.enums.CouponStatusEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.promodetail.enums.CouponStyleEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.promodetail.enums.CouponTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.LeadsInfoFetcher.LeadsInfoResult;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.cepinpool.dto.CostEffectivePinTuan;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.dealgift.DealGiftResult;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.utils.DealUtils;
import com.sankuai.dzshoppingguide.product.detail.application.utils.context.DealCtxUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.Icon;
import com.sankuai.dzshoppingguide.product.detail.spi.promo.vo.CouponModule;
import com.sankuai.dzshoppingguide.product.detail.spi.promo.vo.DealGift;
import com.sankuai.dzshoppingguide.product.detail.spi.promo.vo.PromoDetailModule;
import com.sankuai.general.product.query.center.client.dto.DealGroupCategoryDTO;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.sankuai.dealuser.price.display.api.model.PromoIdentity;

@ExtendWith(MockitoExtension.class)
class PromoDetailServiceBuildIdleTimeCouponTest {

    private PromoDetailService promoDetailService;

    @InjectMocks
    private PromoDetailService service;

    @BeforeEach
    void setUp() {
        promoDetailService = new PromoDetailService();
    }

    /**
     * Test when PriceDisplayDTO is null should throw NullPointerException
     */
    @Test
    public void testBuildIdleTimeCouponNullInput() throws Throwable {
        // arrange
        PriceDisplayDTO idlePriceDisplayDTO = null;
        // act & assert
        assertThrows(NullPointerException.class, () -> {
            promoDetailService.buildIdleTimeCoupon(idlePriceDisplayDTO, new PromotionDetailParams());
        });
    }

    /**
     * Test when usedPromos is null should return null
     */
    @Test
    public void testBuildIdleTimeCouponNullUsedPromos() throws Throwable {
        // arrange
        PriceDisplayDTO idlePriceDisplayDTO = new PriceDisplayDTO();
        idlePriceDisplayDTO.setUsedPromos(null);
        idlePriceDisplayDTO.setPrice(new BigDecimal("100.00"));
        // act
        CouponModule result = promoDetailService.buildIdleTimeCoupon(idlePriceDisplayDTO, new PromotionDetailParams());
        // assert
        assertNull(result);
    }

    /**
     * Test when usedPromos is empty should return null
     */
    @Test
    public void testBuildIdleTimeCouponEmptyUsedPromos() throws Throwable {
        // arrange
        PriceDisplayDTO idlePriceDisplayDTO = new PriceDisplayDTO();
        idlePriceDisplayDTO.setUsedPromos(Lists.newArrayList());
        idlePriceDisplayDTO.setPrice(new BigDecimal("100.00"));
        // act
        CouponModule result = promoDetailService.buildIdleTimeCoupon(idlePriceDisplayDTO, new PromotionDetailParams());
        // assert
        assertNull(result);
    }

    /**
     * Test buildExposureCoupon with null exposure coupons list.
     */
    @Test
    void testBuildExposureCoupon_NullExposureCoupons() throws Throwable {
        // arrange
        PromotionDetailParams params = new PromotionDetailParams();
        params.setExposureCoupons(null);
        // act
        java.util.List<CouponModule> result = promoDetailService.buildExposureCoupon(params);
        // assert
        assertNull(result, "Result should be null when exposure coupons list is null.");
    }

    /**
     * Test buildExposureCoupon with empty exposure coupons list.
     */
    @Test
    void testBuildExposureCoupon_EmptyExposureCoupons() throws Throwable {
        // arrange
        PromotionDetailParams params = new PromotionDetailParams();
        params.setExposureCoupons(Lists.<PromoExposureInfo>newArrayList());
        // act
        java.util.List<CouponModule> result = promoDetailService.buildExposureCoupon(params);
        // assert
        assertNull(result, "Result should be null when exposure coupons list is empty.");
    }

    /**
     * Test buildExposureCoupon with non-empty exposure coupons list.
     */
    @Test
    void testBuildExposureCoupon_NonEmptyExposureCoupons() throws Throwable {
        // arrange
        PromotionDetailParams params = new PromotionDetailParams();
        java.util.List<PromoExposureInfo> exposureCoupons = Lists.newArrayList();
        PromoExposureInfo info = Mockito.mock(PromoExposureInfo.class);
        Mockito.when(info.getFlowId()).thenReturn(1L);
        Mockito.when(info.getResourceActivityId()).thenReturn(2L);
        Mockito.when(info.getActivityId()).thenReturn(3L);
        Mockito.when(info.getMaterialId()).thenReturn("material");
        Mockito.when(info.getRowKey()).thenReturn("rowKey");
        Mockito.when(info.getAmount()).thenReturn("100");
        Mockito.when(info.getTitle()).thenReturn("Coupon Title");
        Mockito.when(info.getSubTitle()).thenReturn("Sub Title");
        Mockito.when(info.getCanAssign()).thenReturn(true);
        Mockito.when(info.getUseEndTime()).thenReturn(123456789L);
        exposureCoupons.add(info);
        params.setExposureCoupons(exposureCoupons);
        // act
        java.util.List<CouponModule> result = promoDetailService.buildExposureCoupon(params);
        // assert
        assertNotNull(result, "Result should not be null when exposure coupons list is non-empty.");
        assertEquals(1, result.size(), "Result list size should match the input list size.");
        CouponModule module = result.get(0);
        assertEquals(CouponTypeEnum.EXPOSURE_COUPON.getCode(), module.getCouponType(), "Coupon type should be EXPOSURE_COUPON.");
        assertEquals(CouponStyleEnum.BIG_CARD.getCode(), module.getCouponStyle(), "Coupon style should be BIG_CARD.");
        assertEquals(CouponStatusEnum.ASSIGNED.getCode(), module.getCouponStatus(), "Coupon status should be ASSIGNED when canAssign is true.");
    }

    /**
     * Test buildExposureCoupon with non-empty exposure coupons list and canAssign false.
     */
    @Test
    void testBuildExposureCoupon_NonEmptyExposureCoupons_CanAssignFalse() throws Throwable {
        // arrange
        PromotionDetailParams params = new PromotionDetailParams();
        java.util.List<PromoExposureInfo> exposureCoupons = Lists.newArrayList();
        PromoExposureInfo info = Mockito.mock(PromoExposureInfo.class);
        Mockito.when(info.getFlowId()).thenReturn(1L);
        Mockito.when(info.getResourceActivityId()).thenReturn(2L);
        Mockito.when(info.getActivityId()).thenReturn(3L);
        Mockito.when(info.getMaterialId()).thenReturn("material");
        Mockito.when(info.getRowKey()).thenReturn("rowKey");
        Mockito.when(info.getAmount()).thenReturn("100");
        Mockito.when(info.getTitle()).thenReturn("Coupon Title");
        Mockito.when(info.getSubTitle()).thenReturn("Sub Title");
        Mockito.when(info.getCanAssign()).thenReturn(false);
        Mockito.when(info.getUseEndTime()).thenReturn(123456789L);
        exposureCoupons.add(info);
        params.setExposureCoupons(exposureCoupons);
        // act
        java.util.List<CouponModule> result = promoDetailService.buildExposureCoupon(params);
        // assert
        assertNotNull(result, "Result should not be null when exposure coupons list is non-empty.");
        assertEquals(1, result.size(), "Result list size should match the input list size.");
        CouponModule module = result.get(0);
        assertEquals(CouponStatusEnum.COUNTDOWN.getCode(), module.getCouponStatus(), "Coupon status should be COUNTDOWN when canAssign is false.");
    }

    /**
     * Test buildExposureCoupon with non-empty exposure coupons list and multiple coupons.
     */
    @Test
    void testBuildExposureCoupon_MultipleCoupons() throws Throwable {
        // arrange
        PromotionDetailParams params = new PromotionDetailParams();
        java.util.List<PromoExposureInfo> exposureCoupons = Lists.newArrayList();
        PromoExposureInfo info1 = Mockito.mock(PromoExposureInfo.class);
        Mockito.when(info1.getFlowId()).thenReturn(1L);
        Mockito.when(info1.getResourceActivityId()).thenReturn(2L);
        Mockito.when(info1.getActivityId()).thenReturn(3L);
        Mockito.when(info1.getMaterialId()).thenReturn("material1");
        Mockito.when(info1.getRowKey()).thenReturn("rowKey1");
        Mockito.when(info1.getAmount()).thenReturn("100");
        Mockito.when(info1.getTitle()).thenReturn("Coupon Title 1");
        Mockito.when(info1.getSubTitle()).thenReturn("Sub Title 1");
        Mockito.when(info1.getCanAssign()).thenReturn(true);
        Mockito.when(info1.getUseEndTime()).thenReturn(123456789L);
        exposureCoupons.add(info1);
        PromoExposureInfo info2 = Mockito.mock(PromoExposureInfo.class);
        Mockito.when(info2.getFlowId()).thenReturn(4L);
        Mockito.when(info2.getResourceActivityId()).thenReturn(5L);
        Mockito.when(info2.getActivityId()).thenReturn(6L);
        Mockito.when(info2.getMaterialId()).thenReturn("material2");
        Mockito.when(info2.getRowKey()).thenReturn("rowKey2");
        Mockito.when(info2.getAmount()).thenReturn("200");
        Mockito.when(info2.getTitle()).thenReturn("Coupon Title 2");
        Mockito.when(info2.getSubTitle()).thenReturn("Sub Title 2");
        Mockito.when(info2.getCanAssign()).thenReturn(false);
        Mockito.when(info2.getUseEndTime()).thenReturn(987654321L);
        exposureCoupons.add(info2);
        params.setExposureCoupons(exposureCoupons);
        // act
        java.util.List<CouponModule> result = promoDetailService.buildExposureCoupon(params);
        // assert
        assertNotNull(result, "Result should not be null when exposure coupons list is non-empty.");
        assertEquals(2, result.size(), "Result list size should match the input list size.");
        assertEquals(CouponStatusEnum.ASSIGNED.getCode(), result.get(0).getCouponStatus(), "First coupon status should be ASSIGNED.");
        assertEquals(CouponStatusEnum.COUNTDOWN.getCode(), result.get(1).getCouponStatus(), "Second coupon status should be COUNTDOWN.");
    }

    @Test
    public void testConvertPromoTag_NullPromoDTO() throws Throwable {
        assertNull(service.convertPromoTag(null));
    }

    @Test
    public void testConvertPromoTag_NullIdentity() throws Throwable {
        PromoDTO promoDTO = new PromoDTO();
        promoDTO.setIdentity(null);
        assertEquals("团购优惠", service.convertPromoTag(promoDTO));
    }

    @Test
    public void testConvertPromoTag_NullPromoShowType() throws Throwable {
        PromoIdentity identity = mock(PromoIdentity.class);
        when(identity.getPromoShowType()).thenReturn(null);
        PromoDTO promoDTO = new PromoDTO();
        promoDTO.setIdentity(identity);
        assertEquals("团购优惠", service.convertPromoTag(promoDTO));
    }

    @Test
    public void testConvertPromoTag_DiscountSellMedical() throws Throwable {
        PromoIdentity identity = mock(PromoIdentity.class);
        when(identity.getPromoShowType()).thenReturn("DISCOUNT_SELL");
        when(identity.getPromoTypeDesc()).thenReturn("商家立减");
        PromoDTO promoDTO = new PromoDTO();
        promoDTO.setIdentity(identity);
        // 由于无法mock静态方法，这里只测试基本逻辑
        String result = service.convertPromoTag(promoDTO);
        assertTrue(result.equals("特惠促销") || result.equals("商家立减"));
    }

    @Test
    public void testConvertPromoTag_DiscountSellNormal() throws Throwable {
        PromoIdentity identity = mock(PromoIdentity.class);
        when(identity.getPromoShowType()).thenReturn("DISCOUNT_SELL");
        when(identity.getPromoTypeDesc()).thenReturn("普通促销");
        PromoDTO promoDTO = new PromoDTO();
        promoDTO.setIdentity(identity);
        assertEquals("特惠促销", service.convertPromoTag(promoDTO));
    }

    @Test
    public void testConvertPromoTag_MtSubsidy() throws Throwable {
        PromoIdentity identity = mock(PromoIdentity.class);
        when(identity.getPromoShowType()).thenReturn("MT_SUBSIDY");
        PromoDTO promoDTO = new PromoDTO();
        promoDTO.setIdentity(identity);
        assertEquals("美团补贴", service.convertPromoTag(promoDTO));
    }

    @Test
    public void testConvertPromoTag_NewCustomerDiscount() throws Throwable {
        PromoIdentity identity = mock(PromoIdentity.class);
        when(identity.getPromoShowType()).thenReturn("NEW_CUSTOMER_DISCOUNT");
        PromoDTO promoDTO = new PromoDTO();
        promoDTO.setIdentity(identity);
        assertEquals("新客特惠", service.convertPromoTag(promoDTO));
    }

    @Test
    public void testConvertPromoTag_MemberBenefits() throws Throwable {
        PromoIdentity identity = mock(PromoIdentity.class);
        when(identity.getPromoShowType()).thenReturn("MEMBER_BENEFITS");
        PromoDTO promoDTO = new PromoDTO();
        promoDTO.setIdentity(identity);
        assertEquals("会员优惠", service.convertPromoTag(promoDTO));
    }

    @Test
    public void testConvertPromoTag_NewMemberBenefits() throws Throwable {
        PromoIdentity identity = mock(PromoIdentity.class);
        when(identity.getPromoShowType()).thenReturn("NEW_MEMBER_BENEFITS");
        PromoDTO promoDTO = new PromoDTO();
        promoDTO.setIdentity(identity);
        assertEquals("新会员优惠", service.convertPromoTag(promoDTO));
    }

    @Test
    public void testConvertPromoTag_PlatformCoupon() throws Throwable {
        PromoIdentity identity = mock(PromoIdentity.class);
        when(identity.getPromoShowType()).thenReturn("PLATFORM_COUPON");
        PromoDTO promoDTO = new PromoDTO();
        promoDTO.setIdentity(identity);
        assertEquals("美团券", service.convertPromoTag(promoDTO));
    }

    @Test
    public void testConvertPromoTag_MerchantCoupon() throws Throwable {
        PromoIdentity identity = mock(PromoIdentity.class);
        when(identity.getPromoShowType()).thenReturn("MERCHANT_COUPON");
        PromoDTO promoDTO = new PromoDTO();
        promoDTO.setIdentity(identity);
        assertEquals("商家券", service.convertPromoTag(promoDTO));
    }

    @Test
    public void testConvertPromoTag_GovernmentConsumeCoupon() throws Throwable {
        PromoIdentity identity = mock(PromoIdentity.class);
        when(identity.getPromoShowType()).thenReturn("GOVERNMENT_CONSUME_COUPON");
        PromoDTO promoDTO = new PromoDTO();
        promoDTO.setIdentity(identity);
        assertEquals("政府消费券", service.convertPromoTag(promoDTO));
    }

    @Test
    public void testConvertPromoTag_DealPromo() throws Throwable {
        PromoIdentity identity = mock(PromoIdentity.class);
        when(identity.getPromoShowType()).thenReturn("DEAL_PROMO");
        PromoDTO promoDTO = new PromoDTO();
        promoDTO.setIdentity(identity);
        assertEquals("团购优惠", service.convertPromoTag(promoDTO));
    }

    @Test
    public void testConvertPromoTag_PresalePromo() throws Throwable {
        PromoIdentity identity = mock(PromoIdentity.class);
        when(identity.getPromoShowType()).thenReturn("PRESALE_PROMO");
        PromoDTO promoDTO = new PromoDTO();
        promoDTO.setIdentity(identity);
        assertEquals("预售优惠", service.convertPromoTag(promoDTO));
    }

    @Test
    public void testConvertPromoTag_MagicalMemberPlatformCoupon() throws Throwable {
        PromoIdentity identity = mock(PromoIdentity.class);
        when(identity.getPromoShowType()).thenReturn("MAGICAL_MEMBER_PLATFORM_COUPON");
        PromoDTO promoDTO = new PromoDTO();
        promoDTO.setIdentity(identity);
        assertEquals("神券", service.convertPromoTag(promoDTO));
    }

    @Test
    public void testConvertPromoTag_SecondKill() throws Throwable {
        PromoIdentity identity = mock(PromoIdentity.class);
        when(identity.getPromoShowType()).thenReturn("SECOND_KILL");
        PromoDTO promoDTO = new PromoDTO();
        promoDTO.setIdentity(identity);
        assertEquals("限时秒杀", service.convertPromoTag(promoDTO));
    }

    @Test
    public void testConvertPromoTag_StateSubsidiesPromo() throws Throwable {
        PromoIdentity identity = mock(PromoIdentity.class);
        when(identity.getPromoShowType()).thenReturn("STATE_SUBSIDIES_PROMO");
        PromoDTO promoDTO = new PromoDTO();
        promoDTO.setIdentity(identity);
        assertEquals("国家补贴", service.convertPromoTag(promoDTO));
    }

    @Test
    public void testConvertPromoTag_UnknownPromoShowType() throws Throwable {
        PromoIdentity identity = mock(PromoIdentity.class);
        when(identity.getPromoShowType()).thenReturn("UNKNOWN_TYPE");
        PromoDTO promoDTO = new PromoDTO();
        promoDTO.setIdentity(identity);
        assertEquals("团购优惠", service.convertPromoTag(promoDTO));
    }
}
