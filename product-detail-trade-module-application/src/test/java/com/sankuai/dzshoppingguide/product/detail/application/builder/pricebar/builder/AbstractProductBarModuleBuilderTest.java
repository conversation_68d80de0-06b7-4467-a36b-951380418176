package com.sankuai.dzshoppingguide.product.detail.application.builder.pricebar.builder;

import static org.junit.jupiter.api.Assertions.*;

import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.sale.ProductSaleReturnValue;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DetailPriceVO;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DetailSaleVO;
import com.sankuai.dzshoppingguide.product.detail.spi.pricebar.vo.ProductPriceBarModuleVO;
import java.math.BigDecimal;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * Unit tests for AbstractProductBarModuleBuilder.calcDiscount method
 */
@ExtendWith(MockitoExtension.class)
public class AbstractProductBarModuleBuilderTest {

    @InjectMocks
    private TestProductBarModuleBuilder builder;

    private static class TestProductBarModuleBuilder extends AbstractProductBarModuleBuilder<ProductPriceBarModuleVO> {

        @Override
        public ProductPriceBarModuleVO doBuild() {
            return new ProductPriceBarModuleVO();
        }

        @Override
        protected DetailPriceVO buildPriceVO(ProductPriceParam param) {
            return new DetailPriceVO();
        }

        @Override
        public DetailSaleVO buildSaleInfo(ProductSaleReturnValue productSaleReturnValue) {
            return new DetailSaleVO();
        }

        protected DetailPriceVO buildPriceVO(PriceDisplayDTO normalPriceDisplayDTO, PriceDisplayDTO dealPromoPriceDisplayDTO, ProductCategory productCategory, ProductBaseInfo productBaseInfo) {
            return new DetailPriceVO();
        }
    }

    /**
     * Test normal discount calculation case
     * Expected: Returns correct discount value (8.0) for 80/100
     */
    @Test
    public void testCalcDiscount_NormalCase() {
        // arrange
        BigDecimal finalPrice = new BigDecimal("80.00");
        BigDecimal marketPrice = new BigDecimal("100.00");
        // act
        BigDecimal result = builder.calcDiscount(finalPrice, marketPrice);
        // assert
        assertEquals(0, new BigDecimal("8.0").compareTo(result), "Expected discount should be 8.0 for 80/100");
    }

    /**
     * Test null input handling
     * Expected: Returns null when finalPrice is null
     */
    @Test
    public void testCalcDiscount_NullFinalPrice() {
        // arrange
        BigDecimal finalPrice = null;
        BigDecimal marketPrice = new BigDecimal("100.00");
        // act
        BigDecimal result = builder.calcDiscount(finalPrice, marketPrice);
        // assert
        assertNull(result, "Result should be null when finalPrice is null");
    }

    /**
     * Test zero market price handling
     * Expected: Returns null when marketPrice is zero
     */
    @Test
    public void testCalcDiscount_ZeroMarketPrice() {
        // arrange
        BigDecimal finalPrice = new BigDecimal("80.00");
        BigDecimal marketPrice = BigDecimal.ZERO;
        // act
        BigDecimal result = builder.calcDiscount(finalPrice, marketPrice);
        // assert
        assertNull(result, "Result should be null when marketPrice is zero");
    }

    /**
     * Test precise rounding case
     * Expected: Returns correctly rounded value (6.7) for 66.66/100
     */
    @Test
    public void testCalcDiscount_PreciseRounding() {
        // arrange
        BigDecimal finalPrice = new BigDecimal("66.66");
        BigDecimal marketPrice = new BigDecimal("100.00");
        // act
        BigDecimal result = builder.calcDiscount(finalPrice, marketPrice);
        // assert
        assertEquals(0, new BigDecimal("6.7").compareTo(result), "Expected discount should be 6.7 for 66.66/100");
    }

    /**
     * Test small decimal values
     * Expected: Returns correct small value calculation
     */
    @Test
    public void testCalcDiscount_SmallDecimals() {
        // arrange
        BigDecimal finalPrice = new BigDecimal("0.01");
        BigDecimal marketPrice = new BigDecimal("0.02");
        // act
        BigDecimal result = builder.calcDiscount(finalPrice, marketPrice);
        // assert
        assertEquals(0, new BigDecimal("5.0").compareTo(result), "Expected discount should be 5.0 for 0.01/0.02");
    }

    /**
     * Test large number handling
     * Expected: Returns correct calculation for large numbers
     */
    @Test
    public void testCalcDiscount_LargeNumbers() {
        // arrange
        BigDecimal finalPrice = new BigDecimal("9999999.99");
        BigDecimal marketPrice = new BigDecimal("10000000.00");
        // act
        BigDecimal result = builder.calcDiscount(finalPrice, marketPrice);
        // assert
        assertEquals(0, new BigDecimal("10.0").compareTo(result), "Expected discount should be 10.0 for large numbers ratio");
    }

    /**
     * Test when final price is greater than market price
     * Expected: Returns correct value above 10
     */
    @Test
    public void testCalcDiscount_FinalPriceGreaterThanMarket() {
        // arrange
        BigDecimal finalPrice = new BigDecimal("120.00");
        BigDecimal marketPrice = new BigDecimal("100.00");
        // act
        BigDecimal result = builder.calcDiscount(finalPrice, marketPrice);
        // assert
        assertEquals(0, new BigDecimal("12.0").compareTo(result), "Expected discount should be 12.0 for 120/100");
    }
}
