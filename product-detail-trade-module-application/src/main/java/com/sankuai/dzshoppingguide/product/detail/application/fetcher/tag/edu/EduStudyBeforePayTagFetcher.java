package com.sankuai.dzshoppingguide.product.detail.application.fetcher.tag.edu;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftClient;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.ProductNormalPriceFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.dto.ProductPriceReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.domain.utils.ThriftAsyncUtils;
import com.sankuai.fincreditpay.access.api.lecheck.service.CreditpayInstallmentTagRemoteService;
import com.sankuai.fincreditpay.access.api.lecheck.service.module.CreditPayInstallmentDetailRequestDTO;
import com.sankuai.fincreditpay.access.api.lecheck.service.module.CreditPayInstallmentDetailResponseDTO;
import com.sankuai.fincreditpay.access.api.lecheck.service.module.DealInfoDTO;
import com.sankuai.fincreditpay.access.api.lecheck.service.module.DealInfoMenuDTO;
import com.sankuai.meituan.fincreditpay.common.enums.LecheckPayTypeEnum;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

import static com.sankuai.dzshoppingguide.product.detail.application.utils.PriceUtils.convertPriceToCents;

/**
 * @Author: guangyujie
 * @Date: 2025/2/11 19:45
 */
@Fetcher(
        previousLayerDependencies = {ProductNormalPriceFetcher.class}
)
@Slf4j
public class EduStudyBeforePayTagFetcher extends NormalFetcherContext<EduStudyBeforePayBannerTag> {

    @MdpThriftClient(
            timeout = 1000, testTimeout = 5000,
            remoteServerPort = 9012,
            remoteAppKey = "com.sankuai.fincreditpay.access",
            async = true
    )
    private CreditpayInstallmentTagRemoteService creditpayInstallmentTagRemoteService;

    private static final String PRE_AUTH_BANNER = "pre_auth_banner";

    private static final String MTD_ZAXX = "MTDZaxx";

    @Override
    protected CompletableFuture<EduStudyBeforePayBannerTag> doFetch() {
        ProductPriceReturnValue normalPriceReturnValue = getDependencyResult(ProductNormalPriceFetcher.class);
        try {
            CreditPayInstallmentDetailRequestDTO requestDTO = buildCreditPayInstallmentDetailRequestDTO(normalPriceReturnValue);
            if (Objects.isNull(requestDTO)) {
                return CompletableFuture.completedFuture(null);
            }
            creditpayInstallmentTagRemoteService.getInstallmentDetail(requestDTO);
            CompletableFuture<CreditPayInstallmentDetailResponseDTO> future = ThriftAsyncUtils.getThriftFuture();
            return future.thenApply(this::buildResult);
        } catch (Exception e) {
            log.error("EduStudyBeforePayTagFetcher,request:{}", JSON.toJSONString(this.request), e);
        }
        return CompletableFuture.completedFuture(null);
    }

    private CreditPayInstallmentDetailRequestDTO buildCreditPayInstallmentDetailRequestDTO(ProductPriceReturnValue normalPriceReturnValue) {
        if (Objects.isNull(normalPriceReturnValue)) {
            return null;
        }
        CreditPayInstallmentDetailRequestDTO  requestDTO = new CreditPayInstallmentDetailRequestDTO();
        requestDTO.setUserId(request.getUserId());
        requestDTO.setScene(MTD_ZAXX);
        requestDTO.setPayAmountOfFen(convertPriceToCents(normalPriceReturnValue.getPriceDisplayDTO().getPrice()));
        requestDTO.setDealInfo(buildDealInfo(normalPriceReturnValue));
        return requestDTO;
    }

    private DealInfoDTO buildDealInfo(ProductPriceReturnValue normalPriceReturnValue) {
        DealInfoDTO dealInfoDTO = new DealInfoDTO();
        dealInfoDTO.setCity((long) request.getCityId());
        dealInfoDTO.setOutlet_id(request.getPoiId());
        dealInfoDTO.setPoiId(request.getPoiId());

        List<DealInfoMenuDTO> menus = Lists.newArrayList();
        DealInfoMenuDTO menu = new DealInfoMenuDTO();
        menu.setDealId(request.getProductId());
        menu.setMenu_id(request.getSkuId());
        menu.setMenu_price(String.valueOf(normalPriceReturnValue.getPriceDisplayDTO().getPrice()));
        menu.setLecheckPayType(LecheckPayTypeEnum.PRE_AUTH);

        menus.add(menu);
        dealInfoDTO.setMenus(menus);
        return dealInfoDTO;
    }

    private EduStudyBeforePayBannerTag buildResult(CreditPayInstallmentDetailResponseDTO responseDTO) {
        if (invalidResponse(responseDTO)){
            return null;
        }
        Map<String, DisplayPointDTO> displayPoints = parseDisplayPoints(responseDTO.getDisplayPoints());
        return getEduStudyBeforePayBannerTag(displayPoints);
    }

    private boolean invalidResponse(CreditPayInstallmentDetailResponseDTO responseDTO){
        if (Objects.isNull(responseDTO)) {
            log.info("EduStudyBeforePayTagFetcher,responseDTO is null,request:{}", JSON.toJSONString(this.request));
            return true;
        }
        if (!responseDTO.getStatus()) {
            log.info("EduStudyBeforePayTagFetcher,responseDTO status false,request:{}", JSON.toJSONString(this.request));
            return true;
        }
        return false;
    }

    private Map<String, DisplayPointDTO> parseDisplayPoints(String displayPoints){
        if (StringUtils.isBlank(displayPoints)){
            return Maps.newHashMap();
        }
        try {
            return JSON.parseObject(displayPoints, new TypeReference<Map<String, DisplayPointDTO>>() {});
        } catch (Exception e) {
            log.error("EduStudyBeforePayTagFetcher,parseDisplayPoints error,displayPoints:{}", displayPoints, e);
        }
        return Maps.newHashMap();
    }

    private EduStudyBeforePayBannerTag getEduStudyBeforePayBannerTag(Map<String, DisplayPointDTO> displayPoints) {
        if (MapUtils.isEmpty(displayPoints)){
            return null;
        }

        DisplayPointDTO displayPointDTO = displayPoints.get(PRE_AUTH_BANNER);
        if (Objects.isNull(displayPointDTO) || StringUtils.isBlank(displayPointDTO.getContent())) {
            return null;
        }
        
        try {
            return JSON.parseObject(displayPointDTO.getContent(), new TypeReference<EduStudyBeforePayBannerTag>() {});
        } catch (Exception e) {
            log.error("EduStudyBeforePayTagFetcher,getEduStudyBeforePayBannerTag error,displayPointDTO:{}", 
                    JSON.toJSONString(displayPointDTO), e);
        }
        return null;
    }

    @Data
    static class DisplayPointDTO {
        /**
         * 点位类型
         */
        private String pointType;
        /**
         * 内容类型
         */
        private String contentType;
        /**
         * 内容
         */
        private String content;
    }

}
