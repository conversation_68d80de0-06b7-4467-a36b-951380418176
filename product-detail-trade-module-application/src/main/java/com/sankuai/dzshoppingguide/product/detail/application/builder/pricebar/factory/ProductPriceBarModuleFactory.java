package com.sankuai.dzshoppingguide.product.detail.application.builder.pricebar.factory;

import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.BuilderTypeEnum;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.extend.BaseBuilderFactory;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.extend.BaseVariableBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.pricebar.builder.impl.MonthlySubscriptionProductPriceBarModuleBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.pricebar.builder.impl.ProductPriceBarModuleBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttrFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.FetcherDAGStarter;
import com.sankuai.dzshoppingguide.product.detail.application.utils.TimesDealUtil;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.pricebar.vo.ProductPriceBarModuleVO;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2025/04/02
 */
@Builder(
        builderType = BuilderTypeEnum.BUILDER_FACTORY,
        moduleKey = ModuleKeyConstants.PRICE_SALE_BAR_MODULE,
        startFetcher = FetcherDAGStarter.class,
        dependentFetchers = {
                FetcherDAGStarter.class,
                ProductBaseInfoFetcher.class,
                ProductAttrFetcher.class
        }
)
@Slf4j
public class ProductPriceBarModuleFactory extends BaseBuilderFactory<ProductPriceBarModuleVO> {

    @Override
    protected Class<? extends BaseVariableBuilder> selectVariableBuilder() {
        ProductBaseInfo productBaseInfo = getDependencyResult(ProductBaseInfoFetcher.class);
        ProductAttr productAttr = getDependencyResult(ProductAttrFetcher.class);
        // 次卡->连续包月
        if(TimesDealUtil.isMonthlySubscription(productBaseInfo, productAttr)){
            return MonthlySubscriptionProductPriceBarModuleBuilder.class;
        }
        return ProductPriceBarModuleBuilder.class;
    }

}
