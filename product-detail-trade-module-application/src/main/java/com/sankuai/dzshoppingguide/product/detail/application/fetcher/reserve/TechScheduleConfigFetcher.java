package com.sankuai.dzshoppingguide.product.detail.application.fetcher.reserve;

import com.dianping.technician.common.api.domain.Environment;
import com.dianping.technician.common.api.domain.Operator;
import com.dianping.technician.common.api.enums.PlatformEnum;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.google.common.collect.Lists;
import com.meituan.inf.xmdlog.XMDLogFormat;
import com.sankuai.athena.inf.AthenaInf;
import com.sankuai.athena.inf.rpc.RpcClient;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dz.product.detail.gateway.spi.enums.ProductTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.shopid.ShopIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.shopid.ShopIdMapperFetcher;
import com.sankuai.technician.techshop.dto.TechShopRelationDTO;
import com.sankuai.technician.techshop.enums.TechShopRelationStatusEnum;
import com.sankuai.technician.techshop.query.TechShopRelationPageQuery;
import com.sankuai.technician.techshop.service.TechShopRelationQueryService;
import com.sankuai.technician.trade.api.schedule.dto.TechScheduleConfigDTO;
import com.sankuai.technician.trade.api.schedule.enums.ExternalTypeEnum;
import com.sankuai.technician.trade.api.schedule.enums.TechScheduleTypeEnum;
import com.sankuai.technician.trade.api.schedule.query.TechScheduleConfigQuery;
import com.sankuai.technician.trade.api.schedule.service.ability.TechScheduleConfigQueryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/2/7 17:17
 */
@Fetcher(previousLayerDependencies = {ShopIdMapperFetcher.class})
@Slf4j
/**
 * com.sankuai.dzviewscene.product.shelf.options.prehandler.BookShopConfigPreResourceQueryOpt#getTechStockShopConfig
 */
public class TechScheduleConfigFetcher extends NormalFetcherContext<TechScheduleConfig> {
    @RpcClient(url = "com.sankuai.technician.techshop.service.TechShopRelationQueryService")
    private TechShopRelationQueryService techShopRelationQueryService;

    @RpcClient(url = "com.sankuai.technician.trade.api.schedule.service.ability.TechScheduleConfigQueryService")
    private TechScheduleConfigQueryService techScheduleConfigQueryService;

    @Override
    protected CompletableFuture<TechScheduleConfig> doFetch() {
        if (request.getProductTypeEnum() != ProductTypeEnum.RESERVE) {
            return CompletableFuture.completedFuture(null);
        }
        ShopIdMapper shopIdMapper = getDependencyResult(ShopIdMapperFetcher.class);
        if (request == null || request.getShepherdGatewayParam() == null || shopIdMapper == null) {
            return CompletableFuture.completedFuture(null);
        }
        int platform = request.getPlatformEnum().getCode();
        // 查门店下所有技师的排班配置
        TechShopRelationPageQuery query = buildTechShopRelationPageQuery(shopIdMapper.getDpBestShopId());
        return queryShopTechRelation(query).thenCompose(techIds -> {
            if (CollectionUtils.isEmpty(techIds)) {
                return CompletableFuture.completedFuture(null);
            }
            return queryTechScheduleConfig(
                    buildTechScheduleConfigQuery(techIds, platform, shopIdMapper.getDpBestShopId()));
        }).thenApply(res -> {
            if (CollectionUtils.isEmpty(res)) {
                return null;
            }
            return new TechScheduleConfig(res);
        });
    }

    private TechShopRelationPageQuery buildTechShopRelationPageQuery(long dpShopId) {
        return TechShopRelationPageQuery.builder().pageNo(1).pageSize(1000)
                .statuses(Lists.newArrayList(TechShopRelationStatusEnum.BIND.getStatus()))
                .shopIdsLong(Lists.newArrayList(dpShopId)).build();
    }

    private TechScheduleConfigQuery buildTechScheduleConfigQuery(List<Integer> techIds, int platform, long dpShopId) {
        TechScheduleConfigQuery techScheduleConfigQuery = new TechScheduleConfigQuery();
        techScheduleConfigQuery.setTechIds(techIds);
        techScheduleConfigQuery.setExternalId(String.valueOf(dpShopId));
        techScheduleConfigQuery.setTechScheduleType(TechScheduleTypeEnum.MASSGAE.getCode());
        techScheduleConfigQuery.setExternalType(ExternalTypeEnum.DP_SHOP.getValue());
        techScheduleConfigQuery
                .setEnvironment(Environment.builder().platform(PlatformEnum.getByCode(platform)).build());
        techScheduleConfigQuery.setOperator(Operator.builder().build());
        return techScheduleConfigQuery;
    }

    public CompletableFuture<List<Integer>> queryShopTechRelation(TechShopRelationPageQuery req) {
        return AthenaInf.getRpcCompletableFuture(techShopRelationQueryService.paginateTechShopRelations(req))
                .exceptionally(e -> {
                    log.error(XMDLogFormat.build().putTag("scene", "themeAtomService")
                            .putTag("method", "queryShopTechRelation").message(
                                    String.format("queryShopTechRelation error, request : %s", JsonCodec.encode(req))));
                    return null;
                }).thenApply(res -> {
                    if (res == null || CollectionUtils.isEmpty(res.getData())) {
                        return Lists.newArrayList();
                    }
                    return res.getData().stream().filter(Objects::nonNull).map(TechShopRelationDTO::getTechnicianId)
                            .collect(Collectors.toList());
                });
    }

    public CompletableFuture<List<TechScheduleConfigDTO>> queryTechScheduleConfig(TechScheduleConfigQuery request) {
        if (request == null || CollectionUtils.isEmpty(request.getTechIds())) {
            return CompletableFuture.completedFuture(null);
        }
        List<CompletableFuture<List<TechScheduleConfigDTO>>> futures = new ArrayList<>();
        Lists.partition(request.getTechIds(), 20).forEach(batch -> {
            TechScheduleConfigQuery batchReq = buildBatchTechScheduleConfigQuery(batch, request);
            CompletableFuture<List<TechScheduleConfigDTO>> future = AthenaInf
                    .getRpcCompletableFuture(techScheduleConfigQueryService.queryTechScheduleConfig(batchReq))
                    .exceptionally(e -> {
                        log.error(XMDLogFormat.build().putTag("scene", "themeAtomService")
                                .putTag("method", "queryTechRelatedSchedule").message(String.format(
                                        "queryTechRelatedSchedule error, request : %s", JsonCodec.encode(request))));
                        return null;
                    }).thenApply(response -> {
                        if (response == null || response.getData() == null
                                || CollectionUtils.isEmpty(response.getData().getTechScheduleConfigList())) {
                            return null;
                        }
                        return response.getData().getTechScheduleConfigList();
                    });
            futures.add(future);
        });
        return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .thenApply(v -> futures.stream().map(CompletableFuture::join).filter(Objects::nonNull)
                        .flatMap(Collection::stream).collect(Collectors.toList()));
    }

    private TechScheduleConfigQuery buildBatchTechScheduleConfigQuery(List<Integer> batchTechIds,
            TechScheduleConfigQuery request) {
        TechScheduleConfigQuery query = new TechScheduleConfigQuery();
        query.setTechIds(batchTechIds);
        query.setExternalId(request.getExternalId());
        query.setExternalType(request.getExternalType());
        query.setTechScheduleType(request.getTechScheduleType());
        query.setEnvironment(request.getEnvironment());
        query.setOperator(request.getOperator());
        return query;
    }
}
