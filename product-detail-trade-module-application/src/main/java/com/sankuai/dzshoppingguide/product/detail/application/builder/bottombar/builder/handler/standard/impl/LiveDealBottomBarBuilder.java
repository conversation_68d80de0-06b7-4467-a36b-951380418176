package com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.builder.handler.standard.impl;

import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.BuilderTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.builder.handler.standard.StandardBottomBarBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.component.trade.block.buy.button.DefaultTradeButtonComponent;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.component.trade.block.buy.button.StandardTradeButtonComponent;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sale.info.enums.ProductSaleStatusEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sale.info.enums.ProductSaleTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.FetcherDAGStarter;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.ProductBottomBarVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.block.trade.StandardTradeBlockVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.component.trade.button.StandardTradeButtonVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.scene.StandardTradeBottomBarVO;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;

import java.util.Optional;

/**
 * @Author: guangyujie
 * @Date: 2025/4/8 17:18
 */
@Builder(
        builderType = BuilderTypeEnum.VARIABLE_BUILDER,
        moduleKey = ModuleKeyConstants.BOTTOM_BAR,
        startFetcher = FetcherDAGStarter.class,
        dependentFetchers = {
        }
)
@Slf4j
public class LiveDealBottomBarBuilder extends StandardBottomBarBuilder {

    private ProductSaleStatusEnum saleStatus;

    @Override
    protected void prepareData() {
        saleStatus = Optional.ofNullable(productSaleInfo)
                .map(saleInfo -> saleInfo.getSaleStatus(ProductSaleTypeEnum.LIVE_SOURCE_DEAL))
                .orElse(ProductSaleStatusEnum.ONLINE);
    }

    @Override
    protected ProductBottomBarVO buildProductBottomBarVO() {
        StandardTradeBottomBarVO standardTradeBottomBarVO = new StandardTradeBottomBarVO();
        //直播场景没有左侧快捷入口
        standardTradeBottomBarVO.setRightBottomBar(buildStandardTradeBlock());
        return standardTradeBottomBarVO;
    }

    /**
     * 交易按钮区域
     */
    private @NotNull StandardTradeBlockVO buildStandardTradeBlock() {
        StandardTradeButtonVO button = StandardTradeButtonComponent.build(
                request, productCategory, normalPriceReturnValue, orderPageUrl, purchaseCouponReturnValue, saleStatus
        );
        if (button == null) {  //最终兜底
            button = DefaultTradeButtonComponent.build(request, orderPageUrl);
        }
        return StandardTradeBlockVO.buildSingleButtonStyle(button);
    }

}
