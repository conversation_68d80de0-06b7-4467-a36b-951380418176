package com.sankuai.dzshoppingguide.product.detail.application.builder.navbar.handler;

import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.constants.Constants;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.best.shop.ShopInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.dto.ProductPriceReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.utils.UrlHelper;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.ShareModuleMiniProgramConfig;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

/**
 * @Author: <EMAIL>
 * @Date: 2025/3/5
 */
@Component(Constants.NAV_BAR_RESERVE_PRODUCT_HANDLER)
public class NavBarReserveProductHandler implements NavBarProductTypeHandler {
    @Override
    public String buildImage(String image) {
        return StringUtils.isBlank(image) ? "" : image + "@200w_200h_1e_1c_1l";
    }

    @Override
    public ShareModuleMiniProgramConfig buildMiniProgramConfig(ProductDetailPageRequest request, ShopIdMapper idMapper) {
        // 预订暂无小程序不下发
        return null;
    }

    @Override
    public String buildShareUrl(ProductDetailPageRequest request, ShopIdMapper idMapper, ShopInfo shopInfo) {
        return UrlHelper.getShareUrl(request, idMapper, shopInfo);
    }

    @Override
    public String buildTitle(ProductDetailPageRequest request, String title, ProductPriceReturnValue costEffectivePrice) {
        return title;
    }

    @Override
    public String buildDesc(ProductDetailPageRequest request, ProductBaseInfo baseInfo) {
        return StringUtils.EMPTY;
    }
}
