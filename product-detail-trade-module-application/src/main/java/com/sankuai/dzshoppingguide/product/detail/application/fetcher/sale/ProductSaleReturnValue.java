package com.sankuai.dzshoppingguide.product.detail.application.fetcher.sale;

import com.dianping.deal.sales.common.datatype.SalesDisplayInfoDTO;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025-03-07
 * @desc 商品售卖返回值
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProductSaleReturnValue extends FetcherReturnValueDTO {
    private SalesDisplayInfoDTO saleDisplayDTO;
}
