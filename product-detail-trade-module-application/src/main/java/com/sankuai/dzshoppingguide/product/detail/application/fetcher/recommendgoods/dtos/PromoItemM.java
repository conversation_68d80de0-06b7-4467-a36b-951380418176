package com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommendgoods.dtos;

import com.sankuai.dzshoppingguide.product.detail.application.fetcher.reserve.dto.PromoItemTextM;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * @auther: liweilong06
 * @date: 2021/5/3 下午3:27
 */
@Data
public class PromoItemM {

    /**
     * 优惠的ID
     */
    private long promoId;

    /**
     * 优惠的类型Id
     */
    private int promoTypeCode;

    /**
     * 优惠类型，如：商家立减
     */
    private String promoType;

    /**
     * 优惠描述
     */
    private String desc;

    /**
     * 优惠金额标签
     */
    private String promoTag;

    /**
     * 优惠价格
     */
    private BigDecimal promoPrice;

    /**
     * 是否新客
     */
    private boolean isNewUser;

    /**
     * 优惠类型（新老客）https://km.sankuai.com/collabpage/1651440412#id-PromoDTO
     */
    private String promoNewOldIdentity;

    /**
     * 是否可领优惠
     */
    private boolean canAssign;

    /**
     * 使用的优惠类型列表，一条优惠条目可能是多个优惠融合而成, com.sankuai.dztheme.generalproduct.enums.PromoItemTypeEnum
     */
    private List<Integer> usedPromoTypes;

    /**
     * 券金额门槛,优惠弹窗使用
     */
    private String priceLimitDesc;

    /**
     * 券使用时间描述，优惠弹窗使用
     */
    private String useTimeDesc;

    /**
     * 优惠来源类型
     */
    private int sourceType;

    /**
     * 优惠折扣
     */
    private BigDecimal promoDiscount;

    /**
     * 优惠图标
     */
    private String icon;

    /**
     * 优惠标识，当数据源为价格服务时透传价格服务的标识，其他业务可自定义
     */
    private String promoIdentity;

    /**
     * 券批次id
     */
    private String couponGroupId;

    /**
     * 神券领用状态  NO_STATUS(1, "无领用状态"),ASSIGNED(2, "已领"),UN_ASSIGNED(3, "未领");
     */
    private Integer couponAssignStatus;

    /**
     * COUPON_PURCHASE(1, "领券购");
     */
    private Integer promoUseType;

    /**
     * 优惠展示类型
     */
    private String promoShowType;

    /**
     * 优惠名称，用于弹层
     */
    private String promoName;

    /**
     * 立减售卖渠道列表
     */
    private List<String> reductionSaleChannels;

    private List<Integer> promotionExplanatoryTags;

    private String validTimeText;

    private String thresholdDesc;

    private String promoLabel;


    private BigDecimal minConsumptionAmount;

    private String subTitle;

    /**
     * 最佳优惠类型描述
     */
    private String promoDivideTypeDesc;

    private String couponTitle;

    /**
     * 券金额
     */
    private BigDecimal amount;

    /**
     * 券id
     */
    private String couponId;

    /**
     * 优惠结束时间
     */
    private long endTime;

    /**
     * 优惠剩余库存
     */
    private int remainStock;

    /**
     * 优惠当日结束时间,用于长期优惠中每日分时段的优惠时间计算
     */
    private long effectiveEndTime;


    /**
     * 透传的营销扩展信息
     * 扩展字段枚举参考：{@link com.sankuai.nibmkt.promotion.api.common.enums.PromotionPropertyEnum}
     */
    private Map<String, String> promotionOtherInfoMap;

    /**
     * 优惠文案展示相关信息，纯展示用
     */
    private PromoItemTextM promoItemText;

}
