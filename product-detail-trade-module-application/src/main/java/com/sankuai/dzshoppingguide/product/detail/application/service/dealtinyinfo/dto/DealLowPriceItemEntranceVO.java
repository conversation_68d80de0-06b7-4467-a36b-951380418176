package com.sankuai.dzshoppingguide.product.detail.application.service.dealtinyinfo.dto;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

/**
 * <AUTHOR>
 * @date 2023-10-10
 * @desc 同款低价商品入口是否展示的返回信息
 */
@TypeDoc(description = "同款低价商品入口是否展示的返回信息")
@MobileDo(id = 0xfbc3)
public class DealLowPriceItemEntranceVO {
    @FieldDoc(description = "是否展示同款低价入口")
    @MobileDo.MobileField(key = 0xa985)
    private boolean isShowLowPriceDealList;

    public boolean isShowLowPriceDealList() {
        return isShowLowPriceDealList;
    }

    public void setShowLowPriceDealList(boolean showLowPriceDealList) {
        isShowLowPriceDealList = showLowPriceDealList;
    }
}
