package com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.extend.cepintuanshare;

import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.BottomBarActionDataVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.enums.BottomBarActionTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * @Author: guangyujie
 * @Date: 2025/3/16 15:41
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CEPinTuanShareActionVO extends BottomBarActionDataVO {

    private final int actionType = BottomBarActionTypeEnum.CE_PIN_TUAN_SHARE.getCode();

    /**
     * 分享信息
     */
    private CEPinTuanShareData cePinTuanShareData;

}
