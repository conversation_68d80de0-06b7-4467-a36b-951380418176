package com.sankuai.dzshoppingguide.product.detail.application.fetcher.producttheme;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSONValidator;
import com.sankuai.dz.product.detail.gateway.spi.dto.CustomParam;
import com.sankuai.dz.product.detail.gateway.spi.dto.ShepherdGatewayParam;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.enums.PlatformEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.cityId.CityIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.best.shop.ShopInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommendgoods.dtos.ProductM;
import com.sankuai.dztheme.deal.dto.DealProductDTO;
import com.sankuai.dztheme.deal.req.DealProductRequest;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import java.lang.reflect.Field;
import java.util.*;

import org.apache.commons.collections.MapUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class AbstractProductThemeFetcherBuildQueryExtParamsTest {

    @InjectMocks
    private AbstractProductThemeFetcher fetcher = new AbstractProductThemeFetcher() {

        @Override
        boolean enableNewService() {
            return false;
        }

        @Override
        DealProductRequest buildDealProductRequest(List<ProductM> products, Map<Long, Long> dealId2ShopId) {
            return null;
        }


    };

    @Mock
    private ProductDetailPageRequest request;

    @Mock
    private CityIdMapper cityIdMapper;

    @Mock
    private ShopIdMapper shopIdMapper;

    @Mock
    private ShopInfo shopInfo;

    @Mock
    private ShepherdGatewayParam shepherdGatewayParam;

    @BeforeEach
    public void setup() throws Exception {
        Field requestField = AbstractProductThemeFetcher.class.getSuperclass().getSuperclass().getDeclaredField("request");
        requestField.setAccessible(true);
        requestField.set(fetcher, request);
        fetcher.cityIdMapper = cityIdMapper;
        fetcher.shopIdMapper = shopIdMapper;
        fetcher.shopInfo = shopInfo;
        // Basic mocks needed for all tests
        when(request.getShepherdGatewayParam()).thenReturn(shepherdGatewayParam);
        when(request.getPlatformEnum()).thenReturn(PlatformEnum.DP);
        when(request.getDpUserId()).thenReturn(1234L);
        when(request.getMtUserId()).thenReturn(5678L);
        when(cityIdMapper.getDpCityId()).thenReturn(456);
        when(cityIdMapper.getMtCityId()).thenReturn(789);
        when(shopIdMapper.getDpBestShopId()).thenReturn(123L);
        when(shopIdMapper.getMtBestShopId()).thenReturn(789L);
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.DP_APP);
    }

    /**
     * Test empty dealIds list
     */
    @Test
    public void testBuildQueryExtParamsWithEmptyDealIds() throws Throwable {
        // arrange
        List<Integer> dealIds = Collections.emptyList();
        // act
        Map<String, Object> result = fetcher.buildQueryExtParams(dealIds, MapUtils.EMPTY_MAP);
        // assert
        assertNotNull(result);
        assertEquals(Collections.emptyList(), result.get("dealIds"));
        assertEquals(123, result.get("dpShopId"));
        assertEquals(123L, result.get("dpShopIdForLong"));
    }

    /**
     * Test normal case with MT client type
     */
    @Test
    public void testBuildQueryExtParamsWithMTClientType() throws Throwable {
        // arrange
        List<Integer> dealIds = Arrays.asList(1, 2);
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.MT_APP);
        when(request.getPlatformEnum()).thenReturn(PlatformEnum.MT);
        // act
        Map<String, Object> result = fetcher.buildQueryExtParams(dealIds, MapUtils.EMPTY_MAP);
        // assert
        assertNotNull(result);
        // MT city ID
        assertEquals(789, result.get("cityId"));
        assertEquals(789L, result.get("shopIdForLong"));
    }

    /**
     * Test normal case with DP client type
     */
    @Test
    public void testBuildQueryExtParamsWithDPClientType() throws Throwable {
        // arrange
        List<Integer> dealIds = Arrays.asList(1, 2);
        // act
        Map<String, Object> result = fetcher.buildQueryExtParams(dealIds, MapUtils.EMPTY_MAP);
        // assert
        assertNotNull(result);
        // DP city ID
        assertEquals(456, result.get("cityId"));
        assertEquals(123L, result.get("shopIdForLong"));
    }

    /**
     * Test cost_effective page source with valid pass_param
     */
    @Test
    public void testBuildQueryExtParamsWithCostEffectiveValidPassParam() throws Throwable {
        // arrange
        List<Integer> dealIds = Arrays.asList(1, 2);
        when(request.getPageSource()).thenReturn("cost_effective");
        CustomParam customParam = new CustomParam();
        customParam.addParam("pass_param", "{\"valid\":\"json\"}");
        when(request.getCustomParam()).thenReturn(customParam);
        // act
        Map<String, Object> result = fetcher.buildQueryExtParams(dealIds, MapUtils.EMPTY_MAP);
        // assert
        assertNotNull(result);
        assertTrue(((String) result.get("pageSource")).contains("cost_effective"));
        assertEquals(2, result.get("themeReqSource"));
    }

    /**
     * Test cost_effective page source with invalid pass_param
     */
    @Test
    public void testBuildQueryExtParamsWithCostEffectiveInvalidPassParam() throws Throwable {
        // arrange
        List<Integer> dealIds = Arrays.asList(1, 2);
        when(request.getPageSource()).thenReturn("cost_effective");
        CustomParam customParam = new CustomParam();
        customParam.addParam("pass_param", "invalid-json");
        when(request.getCustomParam()).thenReturn(customParam);
        // act
        Map<String, Object> result = fetcher.buildQueryExtParams(dealIds, MapUtils.EMPTY_MAP);
        // assert
        assertNotNull(result);
        assertTrue(((String) result.get("pageSource")).contains("cost_effective"));
        assertEquals("invalid-json", ((String) result.get("pageSource")).split("pass_param=")[1]);
    }

    /**
     * Test lemarketing page source
     */
    @Test
    public void testBuildQueryExtParamsWithLemarketingSource() throws Throwable {
        // arrange
        List<Integer> dealIds = Arrays.asList(1, 2);
        when(request.getPageSource()).thenReturn("lemarketing");
        // act
        Map<String, Object> result = fetcher.buildQueryExtParams(dealIds, MapUtils.EMPTY_MAP);
        // assert
        assertNotNull(result);
        assertEquals("source=lemarketing", result.get("pageSource"));
    }

    /**
     * Test with shop info having UUID
     */
    @Test
    public void testBuildQueryExtParamsWithShopUUID() throws Throwable {
        // arrange
        List<Integer> dealIds = Arrays.asList(1, 2);
        DpPoiDTO dpPoiDTO = new DpPoiDTO();
        dpPoiDTO.setUuid("test-uuid");
        when(shopInfo.getDpPoiDTO()).thenReturn(dpPoiDTO);
        // act
        Map<String, Object> result = fetcher.buildQueryExtParams(dealIds, MapUtils.EMPTY_MAP);
        // assert
        assertNotNull(result);
        assertEquals("test-uuid", result.get("shopUuid"));
    }

    /**
     * Test with null shop info
     */
    @Test
    public void testBuildQueryExtParamsWithNullShopInfo() throws Throwable {
        // arrange
        List<Integer> dealIds = Arrays.asList(1, 2);
        fetcher.shopInfo = null;
        // act
        Map<String, Object> result = fetcher.buildQueryExtParams(dealIds, MapUtils.EMPTY_MAP);
        // assert
        assertNotNull(result);
        assertNull(result.get("shopUuid"));
    }

    /**
     * Test with all location parameters
     */
    @Test
    public void testBuildQueryExtParamsWithLocationParams() throws Throwable {
        // arrange
        List<Integer> dealIds = Arrays.asList(1, 2);
        when(request.getUserLat()).thenReturn(31.2304);
        when(request.getUserLng()).thenReturn(121.4737);
        // act
        Map<String, Object> result = fetcher.buildQueryExtParams(dealIds, MapUtils.EMPTY_MAP);
        // assert
        assertNotNull(result);
        assertEquals(31.2304, result.get("lat"));
        assertEquals(121.4737, result.get("lng"));
    }
}
