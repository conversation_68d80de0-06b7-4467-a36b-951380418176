package com.sankuai.dzshoppingguide.product.detail.spi.reminder.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dz.product.detail.gateway.spi.vo.AbstractModuleVO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@MobileDo(id = 0xf1b4)
@Data
public class BookingDetailsReminderInfoVO extends AbstractModuleVO implements Serializable {
    /**
     * 须知条文案
     */
    @MobileDo.MobileField(key = 0xd34e)
    private ReminderInfoLayerVO layer;

    /**
     * 须知条文案详情
     */
    @MobileDo.MobileField(key = 0x8535)
    private List<ReminderInfoVO> contents;

    /**
     * 标题
     */
    @MobileDo.MobileField(key = 0x24cc)
    private String title;

    @Override
    public String getModuleKey() {
        return ModuleKeyConstants.REMINDER_INFO;
    }
}