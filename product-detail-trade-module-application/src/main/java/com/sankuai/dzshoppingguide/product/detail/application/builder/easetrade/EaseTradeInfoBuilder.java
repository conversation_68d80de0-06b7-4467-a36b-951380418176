package com.sankuai.dzshoppingguide.product.detail.application.builder.easetrade;

import com.google.common.collect.Lists;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.extend.BaseBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.creditpay.ExposeResult;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.creditpay.UserAndProductCreditPayFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttrFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.FetcherDAGStarter;
import com.sankuai.dzshoppingguide.product.detail.spi.easetrade.vo.DetailTextVO;
import com.sankuai.dzshoppingguide.product.detail.spi.easetrade.vo.EaseTradeInfoVO;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

@Builder(
        moduleKey = ModuleKeyConstants.MODULE_DETAIL_DEAL_EASE_TRADE_INFO,
        startFetcher = FetcherDAGStarter.class,
        dependentFetchers = {
                UserAndProductCreditPayFetcher.class,
                ProductAttrFetcher.class
        }
)
@Slf4j
public class EaseTradeInfoBuilder extends BaseBuilder<EaseTradeInfoVO> {


    @Override
    public EaseTradeInfoVO doBuild() {
        ExposeResult exposeResult = getDependencyResult(UserAndProductCreditPayFetcher.class);
        ProductAttr productAttr = getDependencyResult(ProductAttrFetcher.class);
        String payMethod = productAttr.getProductAttrFirstValue("pay_method");
        boolean productSupport = StringUtils.equals("4", payMethod);

        if (exposeResult != null && exposeResult.isSupportExpose() && productSupport) {
            EaseTradeInfoVO easeTradeInfoVO = new EaseTradeInfoVO();
            easeTradeInfoVO.setData(Lists.newArrayList(new DetailTextVO("先用后付 · ", "10", "#34C759", "500"),
                    new DetailTextVO("0元下单，到店用1次付1次，交易更安心", "10", "#34C759", "400")));
            // 原始颜色后追加2位透明度
            easeTradeInfoVO.setBackgroundColorStart("#00B3001A");
            easeTradeInfoVO.setBackgroundColorEnd("#00B30000");
            return easeTradeInfoVO;
        }
        return null;
    }

}
