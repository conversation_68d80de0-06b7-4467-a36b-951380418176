package com.sankuai.dzshoppingguide.product.detail.application.utils.enums;

import lombok.Getter;

/**
 * 须知条场景枚举
 * 
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/3/11 13:08
 */
@Getter
public enum MemberCardSceneEnum {
    PREMIUM_MEMBER_CARD(1, "付费商家会员"),
    DEAL_DISCOUNT_CARD(2, "团详折扣卡"),
    RESERVE_DISCOUNT_CARD(3, "预定折扣卡");

    /**
     * 场景顺序,不要修改已有的场景的顺序,场景执行器是按照顺序执行的,命中任意一个即返回结果
     * order越小,优先级越高
     */
    private final int order;
    private final String des;

    MemberCardSceneEnum(int order, String des) {
        this.order = order;
        this.des = des;
    }
}
