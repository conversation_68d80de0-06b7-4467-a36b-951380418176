package com.sankuai.dzshoppingguide.product.detail.application.builder.membercard.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import org.apache.commons.collections4.CollectionUtils;

import com.dianping.cat.util.StringUtils;
import com.google.common.collect.Lists;
import com.sankuai.dealuser.price.display.api.enums.PromoTypeEnum;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.BuilderTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.membercard.basebuilder.BaseAbstractMemberCardBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.membercard.card.integration.IntegrationMemberCard;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.membercard.card.integration.IntegrationMemberCardFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.PremiumMemberPriceFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.dto.ProductPriceReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.FetcherDAGStarter;
import com.sankuai.dzshoppingguide.product.detail.application.service.dto.ShopMemberDetailV;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DiscountCardContents;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.CardTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.membercard.vo.BasicCardModuleVO;
import com.sankuai.dzshoppingguide.product.detail.spi.membercard.vo.PremiumMemberCardModuleVO;
import com.sankuai.mpmctmember.query.common.enums.MemberChargeTypeEnum;
import com.sankuai.mpmctmember.query.thrift.dto.MemberInterestDetailDTO;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/3/16 14:04
 */
@Builder(
        builderType = BuilderTypeEnum.VARIABLE_BUILDER,
        moduleKey = ModuleKeyConstants.MEMBER_CARD,
        startFetcher = FetcherDAGStarter.class,
        dependentFetchers = {
                PremiumMemberPriceFetcher.class,
                IntegrationMemberCardFetcher.class
        }
)
public class PremiumMemberCardBuilder extends BaseAbstractMemberCardBuilder {

    @Override
    public BasicCardModuleVO doBuild() {
        ProductPriceReturnValue priceReturnValue = getDependencyResult(PremiumMemberPriceFetcher.class);
        IntegrationMemberCard integrationMemberCard = getDependencyResult(IntegrationMemberCardFetcher.class);

        if (integrationMemberCard == null) {
            return null;
        }

        if (!integrationMemberCard.isPremiumMemberCard()) {
            return null;
        }

        ShopMemberDetailV shopMemberDetailV = integrationMemberCard.getMemberCardInterest();

        if (shopMemberDetailV == null || priceReturnValue == null) {
            return null;
        }
        if (shopMemberDetailV.getOriginalResult() == null) {
            return null;
        }

        MemberInterestDetailDTO originalResult = shopMemberDetailV.getOriginalResult();

        // 商品关联客户所关联的会员卡为付费会员卡
        if (!Objects.equals(MemberChargeTypeEnum.CHARGE.getCode(), originalResult.getChargeType())) {
            return null;
        }

        // 会员落地页的链接为空
        if (StringUtils.isBlank(originalResult.getMemberPageUrl())) {
            return null;
        }

        // 当前用户不是付费会员或会员身份不在有效期内
        if (shopMemberDetailV.isMember() || shopMemberDetailV.isNewMember()) {
            return null;
        }

        // 商品有被设置适用于所有会员的会员价立减，且活动进行中，且会员价为最优价
        if (CollectionUtils.isEmpty(originalResult.getInterests())) {
            // 无会员优惠
            return null;
        }

        PriceDisplayDTO premiumCardNormalPriceDTO = priceReturnValue.getOriginPriceDisplayDTO();
        PriceDisplayDTO premiumCardPromoPriceDTO = priceReturnValue.getPriceDisplayDTO();
        // description = "价格优惠信息集合,对应代理侧不同优惠的查询策略，Key @see PricePromoInfoTypeEnum"
        if (premiumCardNormalPriceDTO == null || premiumCardPromoPriceDTO == null
                || CollectionUtils.isEmpty(premiumCardPromoPriceDTO.getUsedPromos())) {
            return null;
        }
        // 查询所有的优惠中是否包含商家会员优惠,如包含说明会员价为最优价
        PromoDTO promoDTO = premiumCardPromoPriceDTO.getUsedPromos().stream()
                .filter(promo -> promo.getIdentity() != null
                        && promo.getIdentity().getPromoType() == PromoTypeEnum.MERCHANT_MEMBER.getType())
                .findFirst().orElse(null);
        if (promoDTO == null) {
            return null;
        }

        // 价格不能倒挂
        BigDecimal premiumCardNormalPrice = Optional.ofNullable(premiumCardNormalPriceDTO.getPrice())
                .orElse(BigDecimal.ZERO);
        BigDecimal premiumCardPromoPrice = Optional.ofNullable(premiumCardPromoPriceDTO.getPrice())
                .orElse(BigDecimal.ZERO);
        if (premiumCardNormalPrice.compareTo(premiumCardPromoPrice) <= 0) {
            return null;
        }

        PremiumMemberCardModuleVO premiumMemberCardModuleVO = new PremiumMemberCardModuleVO();
        premiumMemberCardModuleVO.setCardType(CardTypeEnum.PREMIUM_MEMBER_CARD.getCode());
        premiumMemberCardModuleVO.setContents(buildContents(premiumCardPromoPrice, premiumCardNormalPrice));
        premiumMemberCardModuleVO.setJumpUrl(originalResult.getMemberPageUrl());
        premiumMemberCardModuleVO.setBackgroundColor("#FFEDDE");
        premiumMemberCardModuleVO
                .setPrefixIcon("https://p0.meituan.net/dealproduct/18acc242c09a2e2c1ad1abee202ed3521601.png");
        premiumMemberCardModuleVO
                .setSuffixIcon("https://p0.meituan.net/dealproduct/9385c3035492d55b7354ce5bbdea7009380.png");
        premiumMemberCardModuleVO.setDealPriceWithNormal(getPriceStr(premiumCardNormalPrice));
        premiumMemberCardModuleVO.setDealPriceWithPromo(getPriceStr(premiumCardPromoPrice));
        premiumMemberCardModuleVO.setShow(isShowCard(premiumMemberCardModuleVO));
        premiumMemberCardModuleVO.setCardNo(String.valueOf(Optional.ofNullable(originalResult.getPlanId()).orElse(0L)));
        premiumMemberCardModuleVO.setHasCard(false);
        return premiumMemberCardModuleVO;
    }

    private boolean isShowCard(PremiumMemberCardModuleVO cardModuleVO) {
        if (Objects.isNull(cardModuleVO)) {
            return false;
        }
        return org.apache.commons.lang3.StringUtils.isNotBlank(cardModuleVO.getDealPriceWithNormal())
                && org.apache.commons.lang3.StringUtils.isNotBlank(cardModuleVO.getDealPriceWithPromo())
                && new BigDecimal(cardModuleVO.getDealPriceWithPromo())
                        .compareTo(new BigDecimal(cardModuleVO.getDealPriceWithNormal())) <= 0;

    }

    private String getPriceStr(BigDecimal price) {
        return Optional.ofNullable(price)
                .map(item -> item.setScale(2, RoundingMode.UP).stripTrailingZeros().toPlainString())
                .orElse(org.apache.commons.lang.StringUtils.EMPTY);
    }

    private List<DiscountCardContents> buildContents(BigDecimal promoPrice, BigDecimal normalPrice) {
        String savedMoney = normalPrice.subtract(promoPrice).setScale(2, RoundingMode.UP).stripTrailingZeros()
                .toPlainString();
        String promoPriceStr = promoPrice.setScale(2, RoundingMode.UP).stripTrailingZeros().toPlainString();

        DiscountCardContents content1 = new DiscountCardContents();
        content1.setContent("开通会员，本单");
        content1.setFontColor("#8e3c12");
        content1.setFontSize(12);

        DiscountCardContents content2 = new DiscountCardContents();
        content2.setContent(promoPriceStr);
        content2.setFontColor("#FF4B10");
        content2.setFontSize(12);

        DiscountCardContents content3 = new DiscountCardContents();
        content3.setContent("元，多省");
        content3.setFontColor("#8e3c12");
        content3.setFontSize(12);

        DiscountCardContents content4 = new DiscountCardContents();
        content4.setContent(savedMoney);
        content4.setFontColor("#FF4B10");
        content4.setFontSize(12);

        DiscountCardContents content5 = new DiscountCardContents();
        content5.setContent("元");
        content5.setFontColor("#8e3c12");
        content5.setFontSize(12);

        return Lists.newArrayList(content1, content2, content3, content4, content5);
    }
}
