package com.sankuai.dzshoppingguide.product.detail.application.utils;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.sankuai.dzshoppingguide.product.detail.application.utils.model.PlayActivityModel;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class PlayCenterWrapperBuildPlayActivityModelList1Test {

    /**
     * Test case: input array is null
     * Expected: return empty list
     */
    @Test
    public void testBuildPlayActivityModelListNullArray() throws Throwable {
        // arrange
        JSONArray array = null;
        // act
        List<PlayActivityModel> result = PlayCenterWrapper.buildPlayActivityModelList(array);
        // assert
        assertNotNull(result);
        assertEquals(0, result.size());
    }

    /**
     * Test case: input array is empty
     * Expected: return empty list
     */
    @Test
    public void testBuildPlayActivityModelListEmptyArray() throws Throwable {
        // arrange
        JSONArray array = new JSONArray();
        // act
        List<PlayActivityModel> result = PlayCenterWrapper.buildPlayActivityModelList(array);
        // assert
        assertNotNull(result);
        assertEquals(0, result.size());
    }

    /**
     * Test case: array contains null object
     * Expected: return empty list
     */
    @Test
    public void testBuildPlayActivityModelListNullObj() throws Throwable {
        // arrange
        JSONArray array = new JSONArray();
        JSONObject obj = new JSONObject();
        array.add(obj);
        // act
        List<PlayActivityModel> result = PlayCenterWrapper.buildPlayActivityModelList(array);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertNull(result.get(0).getPlayInfo());
    }

    /**
     * Test case: array contains object without playInfo
     * Expected: return list with one element with null playInfo
     */
    @Test
    public void testBuildPlayActivityModelListNoPlayInfo() throws Throwable {
        // arrange
        JSONArray array = new JSONArray();
        JSONObject obj = new JSONObject();
        array.add(obj);
        // act
        List<PlayActivityModel> result = PlayCenterWrapper.buildPlayActivityModelList(array);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertNull(result.get(0).getPlayInfo());
    }

    /**
     * Test case: array contains valid object with complete playInfo
     * Expected: return list with one element containing valid playInfo
     */
    @Test
    public void testBuildPlayActivityModelListWithPlayInfo() throws Throwable {
        // arrange
        JSONArray array = new JSONArray();
        JSONObject obj = new JSONObject();
        JSONObject playInfo = new JSONObject();
        playInfo.put("title", "test");
        playInfo.put("subTitle", "subTest");
        playInfo.put("activityId", 1L);
        playInfo.put("startTime", 1000L);
        playInfo.put("endTime", 2000L);
        playInfo.put("materialInfoList", new JSONArray());
        playInfo.put("taskInfoList", new JSONArray());
        obj.put("playInfo", playInfo);
        array.add(obj);
        // act
        List<PlayActivityModel> result = PlayCenterWrapper.buildPlayActivityModelList(array);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertNotNull(result.get(0).getPlayInfo());
        assertEquals("test", result.get(0).getPlayInfo().getTitle());
        assertEquals("subTest", result.get(0).getPlayInfo().getSubTitle());
        assertEquals(1L, result.get(0).getPlayInfo().getActivityId());
        assertEquals(1000L, result.get(0).getPlayInfo().getStartTime());
        assertEquals(2000L, result.get(0).getPlayInfo().getEndTime());
        assertNotNull(result.get(0).getPlayInfo().getMaterialInfoList());
        assertNotNull(result.get(0).getPlayInfo().getTaskInfoList());
    }
}
