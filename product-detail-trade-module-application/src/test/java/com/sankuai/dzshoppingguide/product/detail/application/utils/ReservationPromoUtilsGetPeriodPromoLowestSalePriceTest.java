package com.sankuai.dzshoppingguide.product.detail.application.utils;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.reserve.dto.PeriodPriceM;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.reserve.dto.ProductItemM;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ReservationPromoUtilsGetPeriodPromoLowestSalePriceTest {

    @Test(expected = NullPointerException.class)
    public void testGetPeriodPromoLowestSalePriceProductItemMIsNull() throws Throwable {
        ProductItemM productItemM = null;
        ReservationPromoUtils.getPeriodPromoLowestSalePrice(productItemM);
    }

    @Test
    public void testGetPeriodPromoLowestSalePricePeriodPriceMIsEmpty() throws Throwable {
        ProductItemM productItemM = new ProductItemM();
        productItemM.setOriginalSalePrice(new BigDecimal("100"));
        BigDecimal result = ReservationPromoUtils.getPeriodPromoLowestSalePrice(productItemM);
        assertEquals(new BigDecimal("100"), result);
    }

    @Test
    public void testGetPeriodPromoLowestSalePricePeriodPromoPricesIsNull() throws Throwable {
        ProductItemM productItemM = new ProductItemM();
        PeriodPriceM periodPriceM = new PeriodPriceM();
        productItemM.setPeriodPriceM(Arrays.asList(periodPriceM));
        BigDecimal result = ReservationPromoUtils.getPeriodPromoLowestSalePrice(productItemM);
        assertEquals(productItemM.getOriginalSalePrice(), result);
    }

    @Test
    public void testGetPeriodPromoLowestSalePriceCanBookIsFalse() throws Throwable {
        ProductItemM productItemM = new ProductItemM();
        PeriodPriceM periodPriceM = new PeriodPriceM();
        periodPriceM.setCanBook(false);
        productItemM.setPeriodPriceM(Arrays.asList(periodPriceM));
        BigDecimal result = ReservationPromoUtils.getPeriodPromoLowestSalePrice(productItemM);
        assertEquals(productItemM.getOriginalSalePrice(), result);
    }

    @Test
    public void testGetPeriodPromoLowestSalePriceNormal() throws Throwable {
        ProductItemM productItemM = new ProductItemM();
        PeriodPriceM periodPriceM = new PeriodPriceM();
        periodPriceM.setCanBook(true);
        periodPriceM.setPeriodPromoPrices(new com.sankuai.dzshoppingguide.product.detail.application.fetcher.reserve.dto.PeriodPromoPriceM());
        periodPriceM.getPeriodPromoPrices().setPeriodPromoPrice(new BigDecimal("80"));
        productItemM.setPeriodPriceM(Arrays.asList(periodPriceM));
        BigDecimal result = ReservationPromoUtils.getPeriodPromoLowestSalePrice(productItemM);
        assertEquals(new BigDecimal("80"), result);
    }
}
