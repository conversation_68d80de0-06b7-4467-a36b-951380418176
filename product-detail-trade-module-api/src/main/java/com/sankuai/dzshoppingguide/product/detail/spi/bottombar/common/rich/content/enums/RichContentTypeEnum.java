package com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.rich.content.enums;

import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.rich.content.*;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: guang<PERSON>jie
 * @Date: 2025/3/16 15:12
 */
@Getter
public enum RichContentTypeEnum {

    TEXT(1, "文字", TextRichContentVO.class),
    PIC(2, "图片", PicRichContentVO.class),
    LINK(3, "跳转", LinkRichContentVO.class),
    COUNT_DOWN(4, "倒计时", CountDownRichContentVO.class),
    MARGIN_LEFT(5, "边距", MarginLeftRichContentVO.class);

    private final int code;

    private final String desc;

    private final Class<? extends RichContentVO> richContentVOClass;

    RichContentTypeEnum(int code, String desc, Class<? extends RichContentVO> richContentVOClass) {
        this.code = code;
        this.desc = desc;
        this.richContentVOClass = richContentVOClass;
    }

    public static RichContentTypeEnum fromCode(int code) {
        for (RichContentTypeEnum value : RichContentTypeEnum.values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        throw new IllegalArgumentException("无效code:" + code);
    }

    public static boolean containsCode(int code) {
        for (RichContentTypeEnum value : RichContentTypeEnum.values()) {
            if (value.getCode() == code) {
                return true;
            }
        }
        return false;
    }

    public static Map<Integer, String> buildCodeDescMap() {
        return Arrays.stream(RichContentTypeEnum.values()).collect(Collectors.toMap(
                RichContentTypeEnum::getCode,
                RichContentTypeEnum::getDesc
        ));
    }

    public static Map<String, String> buildNameDescMap() {
        return Arrays.stream(RichContentTypeEnum.values()).collect(Collectors.toMap(
                RichContentTypeEnum::name,
                RichContentTypeEnum::getDesc
        ));
    }

}