package com.sankuai.dzshoppingguide.product.detail.application.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

@Slf4j
public class DateAndTimeUtils {
    /**
     * 将时分数据转化为今天对应的long型日期时间
     */
    public static long convertPeriodFormatterStrToLongTime(String timeString, int compareWithToday) {
        DateFormat dateFormat = new SimpleDateFormat("HH:mm");
        try {
            return dateFormat.parse(timeString).getTime() + 8 * 60 * 60 * 1000 + convertZeroLongTime(compareWithToday);
        } catch (Exception e) {
            log.error("DateAndTimeUtils#convertToLongTime error, timeString = {}", timeString, e);
            return 0L;
        }
    }

    /**
     * 将"周几"转化为将来最近的日期0点时间
     */
    public static long weekToDateStartTime(String week) {
        Calendar calendar = Calendar.getInstance();
        int today = calendar.get(Calendar.DAY_OF_WEEK) > 1 ? calendar.get(Calendar.DAY_OF_WEEK) - 1 : 7;
        int target = Integer.parseInt(week);
        int days = target - today;
        days = days < 0 ? days + 7 : days;
        calendar.add(Calendar.DAY_OF_YEAR, days);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTimeInMillis();
    }

    /**
     * 将分钟转换为long型日期时间
     */
    public static long convertToLongTime(int count, int calendarUnit) {
        if (Calendar.MINUTE == calendarUnit) {
            return count * 60 * 1000;
        }
        if (Calendar.HOUR == calendarUnit) {
            return count * 60 * 60 * 1000;
        }
        return 0;
    }

    /**
     * 要找到一个大于等于给定long类型日期时间的最近的可以整除库存粒度的long类型日期时间
     */
    public static long convertToNearestBigLongTime(long originalTimeInMillis, int interval) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(originalTimeInMillis);
        calendar.getGreatestMinimum(Calendar.MINUTE);
        int minutes = calendar.get(Calendar.MINUTE);
        int remainder = minutes % interval;
        if (remainder != 0) {
            int minutesToAdd = interval - remainder;
            calendar.add(Calendar.MINUTE, minutesToAdd);
        }

        return calendar.getTimeInMillis() - calendar.getTimeInMillis() % (60 * 1000);
    }

    /**
     * 要找到一个小于等于给定long类型日期时间的最近的可以整除30分钟的long类型日期时间
     */
    public static long convertToNearestSmallerLongTime(long originalTimeInMillis, int interval) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(originalTimeInMillis);
        calendar.getGreatestMinimum(Calendar.MINUTE);
        int minutes = calendar.get(Calendar.MINUTE);
        int remainder = minutes % interval;
        if (remainder != 0) {
            int minutesToSubtract = remainder;
            calendar.add(Calendar.MINUTE, -minutesToSubtract);
        }

        return calendar.getTimeInMillis() - calendar.getTimeInMillis() % (60 * 1000);
    }

    /**
     * compareWithToday = 0，获取今天0点的日期时间，compareWithToday = 1, 获取明天0点的日期时间
     */
    public static long convertZeroLongTime(int compareWithToday) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_YEAR, compareWithToday);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime().getTime();
    }

    public static long convertZeroLongTimeWithDateTime(long timeInMillis) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(timeInMillis);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime().getTime();
    }

    /**
     * 将long型日期时间转换为今天的HH:mm格式数据
     */
    public static String convertFormatDate(long periodStartTime) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("HH:mm");
        return simpleDateFormat.format(periodStartTime);
    }

    /**
     * 将今天的HH:mm格式数据转换成将long型日期时间
     */
    public static long convertFormatDateToLongTime(String formatDate) {
        if (StringUtils.isBlank(formatDate)) {
            return 0;
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("HH:mm");
        try {
            return simpleDateFormat.parse(formatDate).getTime();
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 获取当天是周几，星期天-星期六：1-7
     */
    public static int convertWeekDay() {
        Calendar calendar = Calendar.getInstance();
        int num = calendar.get(Calendar.DAY_OF_WEEK);
        return num > 1 ? num - 1 : 7;
    }

    /**
     * 获取对应日期时间是本周的第几天
     */
    public static int convertWeekDay(long timeInMillis) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(timeInMillis);
        int num = calendar.get(Calendar.DAY_OF_WEEK);
        return num > 1 ? num - 1 : 7;
    }

    /**
     * 日期时间加dayNum天，可以为负数
     */
    public static long addDay(long now, int dayNum) {
        long oneDay = 24 * 60 * 60 * 1000;
        return now + oneDay * dayNum;
    }

    /**
     * 日期时间加minuteNum分钟，可以为负数
     */
    public static long addMinutes(long dateTime, int minuteNum) {
        long oneMinute = 60 * 1000;
        return dateTime + oneMinute * minuteNum;
    }

    public static boolean isAcrossDay(long dateTime) {
        long tomorrowZeroLongTime = DateAndTimeUtils.convertZeroLongTime(1);
        return tomorrowZeroLongTime <= dateTime;
    }

    public static boolean isBetweenInPeriod(long time, long begin, long end) {
        return begin <= time && time <= end;
    }

    /**
     * 获取最近7天的零点时间集合
     */
    public static List<Long> getRecentSevenDayZeroTime() {
        List<Long> recentTimeList = new ArrayList<>();
        long begin = DateAndTimeUtils.convertZeroLongTime(0);
        recentTimeList.add(begin);

        for (int i = 1; i < 7; i++) {
            recentTimeList.add(DateAndTimeUtils.addDay(begin, i));
        }
        return recentTimeList;
    }

    public static String dateTime2StringDate(DateTime dateTime) {
        if (Objects.isNull(dateTime)) {
            return null;
        }
        Date date = new Date(dateTime.getMillis());
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        return simpleDateFormat.format(date);
    }
}
