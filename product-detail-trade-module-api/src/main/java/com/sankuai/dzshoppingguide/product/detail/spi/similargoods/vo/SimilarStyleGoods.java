package com.sankuai.dzshoppingguide.product.detail.spi.similargoods.vo;

import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.sankuai.dz.product.detail.gateway.spi.vo.AbstractModuleVO;
import com.sankuai.dzshoppingguide.product.detail.spi.similargoods.vo.dtos.DzProductVO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

import static com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants.MODULE_DETAIL_DEAL_SIMILAR_STYLE_GOODS;

/**
 * 同款好价
 * https://mobile.sankuai.com/studio/model/info/42725
 */
@Data
public class SimilarStyleGoods extends AbstractModuleVO implements Serializable {
    /**
     * 整个模块名称
     */
    @MobileField
    private String moduleTitle;

    /**
     * 浮层中的相似好价的模块名
     */
    @MobileField
    private String popUpModuleTitle;

    /**
     * 浮层总标题
     */
    @MobileField
    private String popUpTitle;

    /**
     * 当前商品信息
     */
    @MobileField
    private DealTinyInfoVO currentDealInfo;

    /**
     * 商品列表
     */
    @MobileField
    private List<DzProductVO> productItems;

    /**
     * 是否有下一页
     */
    @MobileField
    private boolean hasNext;

    /**
     * 总数
     */
    @MobileField
    private long totalCount;

    /**
     * 更多商品文案
     */
    @MobileField
    private String moreText;


    @Override
    public String getModuleKey() {
        return MODULE_DETAIL_DEAL_SIMILAR_STYLE_GOODS;
    }
}
