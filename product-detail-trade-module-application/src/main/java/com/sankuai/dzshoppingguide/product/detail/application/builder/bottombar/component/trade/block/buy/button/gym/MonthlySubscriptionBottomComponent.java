package com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.component.trade.block.buy.button.gym;

import com.alibaba.fastjson.JSON;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.component.trade.block.utils.TradeButtonBuildUtils;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.exception.TradeButtonBuildFatalException;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sale.info.enums.ProductSaleStatusEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.enums.OpenTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.extend.buy.BuyActionVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.component.trade.button.StandardTradeButtonVO;
import lombok.extern.slf4j.Slf4j;
import javax.annotation.Nullable;

import static com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.constant.ButtonStyleConstant.STANDARD_TRADE_BUTTON_STYLE;

/**
 * <AUTHOR>
 * @date 2025/05/10
 * 次卡->连续包月
 */
@Slf4j
public class MonthlySubscriptionBottomComponent {

    public static StandardTradeButtonVO build(final ProductDetailPageRequest request,
                                              @Nullable final String orderPageUrl) {
        try {
            return TradeButtonBuildUtils.buildTradeButtonVO(
                    ProductSaleStatusEnum.ONLINE,
                    "立即抢购",
                    "在线付首月 次月自动扣款",
                    STANDARD_TRADE_BUTTON_STYLE,
                    new BuyActionVO(OpenTypeEnum.redirect, orderPageUrl, null)
            );
        } catch (Throwable throwable) {
            log.error("MonthlySubscriptionBottomComponent.build,request:{}",
                    JSON.toJSONString(request), new TradeButtonBuildFatalException(throwable));
            return null;
        }
    }
}
