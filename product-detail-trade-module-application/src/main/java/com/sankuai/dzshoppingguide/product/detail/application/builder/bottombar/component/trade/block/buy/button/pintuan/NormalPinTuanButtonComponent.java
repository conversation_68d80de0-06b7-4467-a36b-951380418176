package com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.component.trade.block.buy.button.pintuan;

import com.alibaba.fastjson.JSON;
import com.dianping.tpfun.product.api.sku.pintuan.dto.PinProductBrief;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.component.trade.block.utils.TradeButtonBuildUtils;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.exception.BottomBarFatalException;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.exception.TradeButtonBuildFatalException;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.pinpool.PinTuanPromoDisplayResult;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sale.info.enums.ProductSaleStatusEnum;
import com.sankuai.dzshoppingguide.product.detail.application.utils.NetUtils;
import com.sankuai.dzshoppingguide.product.detail.application.utils.PriceHelper;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.enums.OpenTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.extend.redirect.SimpleRedirectActionVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.component.trade.button.StandardTradeButtonVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;

import static com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.constant.ButtonStyleConstant.NORMAL_PIN_TUAN_BUTTON_STYLE;

/**
 * @Author: guangyujie
 * @Date: 2025/3/16 19:32
 */
@Slf4j
public class NormalPinTuanButtonComponent {

    public static StandardTradeButtonVO build(final ProductDetailPageRequest request,
                                              final PinProductBrief pinProductBrief,
                                              final PriceDisplayDTO normalPrice,
                                              final PinTuanPromoDisplayResult pinTuanPromoDisplayResult,
                                              final ProductSaleStatusEnum saleStatus) {
        try {
            if (request == null || !isValidPinPool(pinProductBrief, normalPrice, pinTuanPromoDisplayResult)) {
                return null;
            }
            String priceRuleTitle = String.format("%s人拼团", pinProductBrief.getPinPersonNum());
            String pricePerPerson = "￥" + PriceHelper.dropLastZero(pinProductBrief.getPrice().setScale(2, RoundingMode.CEILING));
            return TradeButtonBuildUtils.buildTradeButtonVO(
                    saleStatus,
                    String.format("拼团 %s", pricePerPerson),
                    priceRuleTitle,
                    NORMAL_PIN_TUAN_BUTTON_STYLE,
                    new SimpleRedirectActionVO(OpenTypeEnum.redirect, getAppUrl(request, pinProductBrief.getUrl()))
            );
        } catch (Throwable throwable) {
            log.error("NormalPinTuanComponent.build,request:{}",
                    JSON.toJSONString(request), new TradeButtonBuildFatalException(throwable));
            return null;
        }
    }

    private static boolean isValidPinPool(final PinProductBrief pinProductBrief,
                                          final PriceDisplayDTO normalPrice,
                                          final PinTuanPromoDisplayResult pinTuanPromoDisplayResult) {
        if (pinProductBrief == null || pinProductBrief.getProductId() <= 0) {
            return false;
        }
        if (pinProductBrief.isSoldOut() || pinProductBrief.getPrice() == null || pinProductBrief.getPrice().doubleValue() <= 0) {
            return false;
        }
        if (StringUtils.isNotEmpty(pinProductBrief.getUrl())) {
            BigDecimal pinPoolPrice = pinProductBrief.getPrice();
            if (pinTuanPromoDisplayResult != null && pinTuanPromoDisplayResult.getPinPoolPromoAmount() != null
                    && pinTuanPromoDisplayResult.getPinPoolPromoAmount().compareTo(BigDecimal.ZERO) > 0) {
                pinPoolPrice = pinPoolPrice.subtract(pinTuanPromoDisplayResult.getPinPoolPromoAmount()).setScale(1, RoundingMode.CEILING);
            }
            return !isDaoGua(pinPoolPrice, normalPrice);
        }
        return false;
    }

    public static String getAppUrl(final ProductDetailPageRequest request,
                                   final String shareUrl) {
        if (StringUtils.isEmpty(shareUrl)) {
            log.error("拼团的shareUrl为空", new BottomBarFatalException("拼团的shareUrl为空:" + request.getProductId()));
            return null;
        }
        if (request.getClientTypeEnum().isMtClientType()) {
            if (request.getClientTypeEnum().isInWxXCX()) {
                String miniAppUrl = shareUrl + "&product=mtwxapp";
                return "/index/pages/h5/h5?f_token=1&f_openId=1&weburl=" + NetUtils.encode(miniAppUrl);
            }
            return "imeituan://www.meituan.com/web?url=" + NetUtils.encode(shareUrl);
        } else {
            if (request.getClientTypeEnum().isInWxXCX()) {
                return "/pages/webview/webview?url=" + NetUtils.encode(shareUrl);
            }
            return "dianping://web?url=" + NetUtils.encode(shareUrl);
        }
    }

    /**
     * 是否倒挂
     */
    private static boolean isDaoGua(final BigDecimal pinPoolPrice,
                                    final PriceDisplayDTO normalPrice) {
        if (normalPrice == null) {
            return false;
        }
        return pinPoolPrice.compareTo(normalPrice.getPrice()) >= 0;
    }

}
