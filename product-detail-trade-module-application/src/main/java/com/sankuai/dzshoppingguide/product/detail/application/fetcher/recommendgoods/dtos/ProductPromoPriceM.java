package com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommendgoods.dtos;

import com.sankuai.dztheme.deal.dto.DealPromoItemDTO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * Created by float.lu on 2020/9/8.
 */
@Data
public class ProductPromoPriceM {
    /**
     * 商品id
     */
    private long productId;

    /**
     * 优惠类型, 如:玩乐卡, 普通折扣卡, 会员日折扣卡, 优惠, 立减,com.sankuai.dztheme.deal.enums.PromoTypeEnum
     */
    private int promoType;

    /**
     * skuId
     */
    private int skuId;

    /**
     * skuIdL
     */
    private long skuIdL;

    /**
     * 资源ID
     */
    private long resourceId;

    /**
     * 优惠后价格
     */
    private BigDecimal promoPrice;

    /**
     * 优惠的价格标签, 如: 已省80
     */
    private String promoTag;

    /**
     * 短优惠文案
     */
    private String shortPromoTag;

    /**
     * 优惠金额
     */
    private BigDecimal promoAmount;

    /**
     * 优惠文案前缀，比如：已优惠
     */
    private String promoTagPrefix;

    /**
     * 优惠之后价格标签
     */
    private String promoPriceTag;

    /**
     * 划线价
     */
    private String marketPrice;

    /**
     * 计算公式 = 1 - ((basePrice - promoPrice) / basePrice)
     */
    private BigDecimal discount;

    /**
     * 折扣标签
     */
    private String discountTag;

    /**
     * 可用时间
     */
    private String availableTime;

    /**
     * 用户是否持有卡
     */
    private boolean userHasCard;

    /**
     * 总共优惠多少
     */
    private BigDecimal totalPromoPrice;

    /**
     * 总共优惠多少
     */
    private String totalPromoPriceTag;

    /**
     * 优惠细节
     */
    private List<PromoItemM> promoItemList;

    /**
     * 原始优惠细节
     */
    private List<PromoItemM> originalPromoItems;

    /**
     * 券信息列表
     */
    private List<ProductCouponM> coupons;

    /**
     * 开始时间，单位是毫秒
     */
    private long startTime;

    /**
     * 结束时间，单位是毫秒
     */
    private long endTime;

    /**
     * 优惠需满足数量
     */
    private int promoQuantityLimit;

    /**
     * 优惠icon
     */
    private String icon;

    /**
     * 优惠文案类型
     *
     * @see com.sankuai.dealuser.price.display.api.enums.PromoTagTypeEnum
     */
    private Integer promoTagType;

    /**
     * 优惠文案展示类型,null=老样式,1=老样式,3=新样式
     */
    private Integer promoTextShowType;

    /**
     *
     * @return
     */
    private Map<String, String> extendDisplayInfo;

    /**
     * 国补商品划线价
     */
    private BigDecimal nationalSubsidyMarketPrice;

    /**
     * 国补价
     */
    private BigDecimal nationalSubsidyPrice;

    /**
     * 优惠icon文案
     */
    private String iconText;

    /**
     * 团购次卡交易类型时有值：单次优惠后价格
     */
    private BigDecimal singlePrice;

    /**
     * 补充展示价格列表
     * key 参考 {@link com.sankuai.dealuser.price.display.api.enums.PricePromoInfoTypeEnum}
     */
    private Map<Integer, List<DealPromoItemDTO>> pricePromoInfoMap;


}
