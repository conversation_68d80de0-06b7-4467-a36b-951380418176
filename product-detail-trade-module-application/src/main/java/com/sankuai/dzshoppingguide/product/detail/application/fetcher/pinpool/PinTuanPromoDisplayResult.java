package com.sankuai.dzshoppingguide.product.detail.application.fetcher.pinpool;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @create 2025/3/11 10:59
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PinTuanPromoDisplayResult extends FetcherReturnValueDTO {
    private String pinPoolPromoDesc; //拼团优惠信息
    private BigDecimal pinPoolPromoAmount = BigDecimal.ZERO; //拼团优惠信息
}
