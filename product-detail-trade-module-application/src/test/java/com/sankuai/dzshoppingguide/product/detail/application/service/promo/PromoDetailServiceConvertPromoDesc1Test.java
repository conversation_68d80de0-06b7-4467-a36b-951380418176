package com.sankuai.dzshoppingguide.product.detail.application.service.promo;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.sankuai.dealuser.price.display.api.enums.ExtendDisplayInfoKeyEnum;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import com.sankuai.dealuser.price.display.api.model.PromoIdentity;
import com.sankuai.dealuser.price.display.api.model.PromoTextDTO;
import com.sankuai.dzshoppingguide.product.detail.application.builder.promodetail.dto.PromotionDetailParams;
import com.sankuai.dzshoppingguide.product.detail.application.builder.promodetail.enums.CouponStyleEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.promodetail.enums.CouponTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.membercard.card.discount.MemberCardDiscountReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.service.promo.PromoDetailService;
import com.sankuai.dzshoppingguide.product.detail.spi.promo.vo.CouponModule;
import com.sankuai.dzshoppingguide.product.detail.spi.promo.vo.PromoDetailModule;
import com.sankuai.mpmctmember.query.common.enums.MemberChargeTypeEnum;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class PromoDetailServiceConvertPromoDesc1Test {

    private final PromoDetailService promoDetailService = new PromoDetailService();

    /**
     * 测试当所有参数都为null时返回默认值"团购优惠"
     */
    @Test
    public void testConvertPromoDescWhenAllParamsAreNull() throws Throwable {
        // act
        String result = promoDetailService.convertPromoDesc(null, null, null);
        // assert
        assertEquals("团购优惠", result);
    }

    /**
     * 测试当extendDesc为空时返回默认值"团购优惠"
     */
    @Test
    public void testConvertPromoDescWhenExtendDescIsBlank() throws Throwable {
        // arrange
        PromoDTO promoDTO = mock(PromoDTO.class);
        PromoDetailModule module = mock(PromoDetailModule.class);
        when(promoDTO.getExtendDesc()).thenReturn("");
        // act
        String result = promoDetailService.convertPromoDesc(promoDTO, module, "tag");
        // assert
        assertEquals("团购优惠", result);
    }

    /**
     * 测试当promoShowType为空时返回默认值"团购优惠"
     */
    @Test
    public void testConvertPromoDescWhenPromoShowTypeIsBlank() throws Throwable {
        // arrange
        PromoDTO promoDTO = mock(PromoDTO.class);
        PromoIdentity identity = mock(PromoIdentity.class);
        PromoDetailModule module = mock(PromoDetailModule.class);
        when(promoDTO.getExtendDesc()).thenReturn("优惠描述");
        when(promoDTO.getIdentity()).thenReturn(identity);
        when(identity.getPromoShowType()).thenReturn("");
        // act
        String result = promoDetailService.convertPromoDesc(promoDTO, module, "tag");
        // assert
        assertEquals("团购优惠", result);
    }

    /**
     * 测试当不是神券类型且promoTextDTO为null时返回去除标签后的描述
     */
    @Test
    public void testConvertPromoDescWhenNotMagicalMemberAndPromoTextDTOIsNull() throws Throwable {
        // arrange
        PromoDTO promoDTO = mock(PromoDTO.class);
        PromoIdentity identity = mock(PromoIdentity.class);
        PromoDetailModule module = mock(PromoDetailModule.class);
        when(promoDTO.getExtendDesc()).thenReturn("标签，优惠描述");
        when(promoDTO.getIdentity()).thenReturn(identity);
        when(identity.getPromoShowType()).thenReturn("OTHER_TYPE");
        when(promoDTO.getPromoTextDTO()).thenReturn(null);
        // act
        String result = promoDetailService.convertPromoDesc(promoDTO, module, "标签");
        // assert
        assertEquals("优惠描述", result);
    }

    /**
     * 测试当不是神券类型且promoTextDTO不为null但promoDivideType不在couponList中时返回去除标签后的描述
     */
    @Test
    public void testConvertPromoDescWhenNotMagicalMemberAndPromoDivideTypeNotInCouponList() throws Throwable {
        // arrange
        PromoDTO promoDTO = mock(PromoDTO.class);
        PromoIdentity identity = mock(PromoIdentity.class);
        PromoTextDTO textDTO = mock(PromoTextDTO.class);
        PromoDetailModule module = mock(PromoDetailModule.class);
        when(promoDTO.getExtendDesc()).thenReturn("标签，优惠描述");
        when(promoDTO.getIdentity()).thenReturn(identity);
        when(identity.getPromoShowType()).thenReturn("OTHER_TYPE");
        when(promoDTO.getPromoTextDTO()).thenReturn(textDTO);
        when(textDTO.getPromoDivideType()).thenReturn("OTHER_TYPE");
        // act
        String result = promoDetailService.convertPromoDesc(promoDTO, module, "标签");
        // assert
        assertEquals("优惠描述", result);
    }

    /**
     * 测试当不是神券类型且promoTextDTO不为null且promoDivideType在couponList中时返回去除标签后的title
     */
    @Test
    public void testConvertPromoDescWhenNotMagicalMemberAndPromoDivideTypeInCouponList() throws Throwable {
        // arrange
        PromoDTO promoDTO = mock(PromoDTO.class);
        PromoIdentity identity = mock(PromoIdentity.class);
        PromoTextDTO textDTO = mock(PromoTextDTO.class);
        PromoDetailModule module = mock(PromoDetailModule.class);
        when(promoDTO.getExtendDesc()).thenReturn("标签，优惠描述");
        when(promoDTO.getIdentity()).thenReturn(identity);
        when(identity.getPromoShowType()).thenReturn("OTHER_TYPE");
        when(promoDTO.getPromoTextDTO()).thenReturn(textDTO);
        when(textDTO.getPromoDivideType()).thenReturn("GOVERNMENT_CONSUME_COUPON");
        when(textDTO.getTitle()).thenReturn("标签，优惠标题");
        // act
        String result = promoDetailService.convertPromoDesc(promoDTO, module, "标签");
        // assert
        assertEquals("优惠标题", result);
    }

    /**
     * 测试当是神券类型但minConsumptionAmount或amount为null时返回extendDesc
     */
    @Test
    public void testConvertPromoDescWhenMagicalMemberAndAmountIsNull() throws Throwable {
        // arrange
        PromoDTO promoDTO = mock(PromoDTO.class);
        PromoIdentity identity = mock(PromoIdentity.class);
        PromoDetailModule module = mock(PromoDetailModule.class);
        when(promoDTO.getExtendDesc()).thenReturn("神券描述");
        when(promoDTO.getIdentity()).thenReturn(identity);
        when(identity.getPromoShowType()).thenReturn("MAGICAL_MEMBER_PLATFORM_COUPON");
        when(promoDTO.getMinConsumptionAmount()).thenReturn(null);
        when(promoDTO.getAmount()).thenReturn(new BigDecimal("10"));
        // act
        String result = promoDetailService.convertPromoDesc(promoDTO, module, "标签");
        // assert
        assertEquals("神券描述", result);
    }

    /**
     * 测试当是神券类型且minConsumptionAmount为0时返回"无门槛"
     */
    @Test
    public void testConvertPromoDescWhenMagicalMemberAndMinConsumptionAmountIsZero() throws Throwable {
        // arrange
        PromoDTO promoDTO = mock(PromoDTO.class);
        PromoIdentity identity = mock(PromoIdentity.class);
        PromoDetailModule module = mock(PromoDetailModule.class);
        when(promoDTO.getExtendDesc()).thenReturn("神券描述");
        when(promoDTO.getIdentity()).thenReturn(identity);
        when(identity.getPromoShowType()).thenReturn("MAGICAL_MEMBER_PLATFORM_COUPON");
        when(promoDTO.getMinConsumptionAmount()).thenReturn(BigDecimal.ZERO);
        when(promoDTO.getAmount()).thenReturn(new BigDecimal("10"));
        // act
        String result = promoDetailService.convertPromoDesc(promoDTO, module, "标签");
        // assert
        assertEquals("无门槛", result);
    }

    /**
     * 测试当是神券类型且有门槛时返回格式化字符串
     */
    @Test
    public void testConvertPromoDescWhenMagicalMemberAndHasThreshold() throws Throwable {
        // arrange
        PromoDTO promoDTO = mock(PromoDTO.class);
        PromoIdentity identity = mock(PromoIdentity.class);
        PromoDetailModule module = mock(PromoDetailModule.class);
        when(promoDTO.getExtendDesc()).thenReturn("神券描述");
        when(promoDTO.getIdentity()).thenReturn(identity);
        when(identity.getPromoShowType()).thenReturn("MAGICAL_MEMBER_PLATFORM_COUPON");
        when(promoDTO.getMinConsumptionAmount()).thenReturn(new BigDecimal("100"));
        when(promoDTO.getAmount()).thenReturn(new BigDecimal("10"));
        // act
        String result = promoDetailService.convertPromoDesc(promoDTO, module, "标签");
        // assert
        assertEquals("满100元减10元", result);
    }

    /**
     * 测试当发生异常时返回extendDesc或默认值
     */
    @Test
    public void testConvertPromoDescWhenExceptionOccurs() throws Throwable {
        // arrange
        PromoDTO promoDTO = mock(PromoDTO.class);
        PromoIdentity identity = mock(PromoIdentity.class);
        PromoDetailModule module = mock(PromoDetailModule.class);
        when(promoDTO.getExtendDesc()).thenReturn("优惠描述");
        when(promoDTO.getIdentity()).thenReturn(identity);
        when(identity.getPromoShowType()).thenThrow(new RuntimeException("模拟异常"));
        // act
        String result = promoDetailService.convertPromoDesc(promoDTO, module, "标签");
        // assert
        assertEquals("优惠描述", result);
    }

    /**
     * 测试当发生异常且extendDesc为null时返回默认值
     */
    @Test
    public void testConvertPromoDescWhenExceptionOccursAndExtendDescIsNull() throws Throwable {
        // arrange
        PromoDTO promoDTO = mock(PromoDTO.class);
        PromoIdentity identity = mock(PromoIdentity.class);
        PromoDetailModule module = mock(PromoDetailModule.class);
        when(promoDTO.getExtendDesc()).thenReturn(null);
        // act
        String result = promoDetailService.convertPromoDesc(promoDTO, module, "标签");
        // assert
        assertEquals("团购优惠", result);
    }

    /**
     * Test when extendDisplayInfoMap is null
     */
    @Test
    public void testBuildMagicCouponPackageWhenExtendDisplayInfoMapIsNull() throws Throwable {
        // arrange
        PromotionDetailParams params = new PromotionDetailParams();
        PriceDisplayDTO priceDisplayDTO = new PriceDisplayDTO();
        priceDisplayDTO.setExtendDisplayInfo(null);
        params.setDealPromDisplayDTO(priceDisplayDTO);
        // act
        CouponModule result = promoDetailService.buildMagicCouponPackage(params);
        // assert
        assertNull(result);
    }

    /**
     * Test when extendDisplayInfoMap is empty
     */
    @Test
    public void testBuildMagicCouponPackageWhenExtendDisplayInfoMapIsEmpty() throws Throwable {
        // arrange
        PromotionDetailParams params = new PromotionDetailParams();
        PriceDisplayDTO priceDisplayDTO = new PriceDisplayDTO();
        priceDisplayDTO.setExtendDisplayInfo(Collections.emptyMap());
        params.setDealPromDisplayDTO(priceDisplayDTO);
        // act
        CouponModule result = promoDetailService.buildMagicCouponPackage(params);
        // assert
        assertNull(result);
    }

    /**
     * Test when walletInfo is blank
     */
    @Test
    public void testBuildMagicCouponPackageWhenWalletInfoIsBlank() throws Throwable {
        // arrange
        PromotionDetailParams params = new PromotionDetailParams();
        PriceDisplayDTO priceDisplayDTO = new PriceDisplayDTO();
        Map<String, String> extendInfo = new HashMap<>();
        extendInfo.put(ExtendDisplayInfoKeyEnum.COUPON_WALLET_INFO.getKey(), "");
        priceDisplayDTO.setExtendDisplayInfo(extendInfo);
        params.setDealPromDisplayDTO(priceDisplayDTO);
        // act
        CouponModule result = promoDetailService.buildMagicCouponPackage(params);
        // assert
        assertNull(result);
    }

    /**
     * Test when walletInfo is invalid JSON
     */
    @Test
    public void testBuildMagicCouponPackageWhenWalletInfoIsInvalidJson() throws Throwable {
        // arrange
        PromotionDetailParams params = new PromotionDetailParams();
        PriceDisplayDTO priceDisplayDTO = new PriceDisplayDTO();
        Map<String, String> extendInfo = new HashMap<>();
        extendInfo.put(ExtendDisplayInfoKeyEnum.COUPON_WALLET_INFO.getKey(), "invalid json");
        priceDisplayDTO.setExtendDisplayInfo(extendInfo);
        params.setDealPromDisplayDTO(priceDisplayDTO);
        // act
        CouponModule result = promoDetailService.buildMagicCouponPackage(params);
        // assert
        assertNull(result);
    }

    /**
     * Test when walletInfo has no couponPackageList
     */
    @Test
    public void testBuildMagicCouponPackageWhenNoCouponPackageList() throws Throwable {
        // arrange
        PromotionDetailParams params = new PromotionDetailParams();
        PriceDisplayDTO priceDisplayDTO = new PriceDisplayDTO();
        Map<String, String> extendInfo = new HashMap<>();
        JSONObject json = new JSONObject();
        extendInfo.put(ExtendDisplayInfoKeyEnum.COUPON_WALLET_INFO.getKey(), json.toJSONString());
        priceDisplayDTO.setExtendDisplayInfo(extendInfo);
        params.setDealPromDisplayDTO(priceDisplayDTO);
        // act
        CouponModule result = promoDetailService.buildMagicCouponPackage(params);
        // assert
        assertNull(result);
    }

    /**
     * Test when walletInfo has empty couponPackageList
     */
    @Test
    public void testBuildMagicCouponPackageWhenEmptyCouponPackageList() throws Throwable {
        // arrange
        PromotionDetailParams params = new PromotionDetailParams();
        PriceDisplayDTO priceDisplayDTO = new PriceDisplayDTO();
        Map<String, String> extendInfo = new HashMap<>();
        JSONObject json = new JSONObject();
        json.put("couponPackageList", new JSONArray());
        extendInfo.put(ExtendDisplayInfoKeyEnum.COUPON_WALLET_INFO.getKey(), json.toJSONString());
        priceDisplayDTO.setExtendDisplayInfo(extendInfo);
        params.setDealPromDisplayDTO(priceDisplayDTO);
        // act
        CouponModule result = promoDetailService.buildMagicCouponPackage(params);
        // assert
        assertNull(result);
    }

    /**
     * Test when walletInfo has valid couponPackageList with couponPackageIds
     */
    @Test
    public void testBuildMagicCouponPackageWhenValidCouponPackageList() throws Throwable {
        // arrange
        PromotionDetailParams params = new PromotionDetailParams();
        PriceDisplayDTO priceDisplayDTO = new PriceDisplayDTO();
        Map<String, String> extendInfo = new HashMap<>();
        JSONObject json = new JSONObject();
        JSONArray packageList = new JSONArray();
        JSONObject package1 = new JSONObject();
        package1.put("couponPackageId", "pkg1");
        packageList.add(package1);
        json.put("couponPackageList", packageList);
        extendInfo.put(ExtendDisplayInfoKeyEnum.COUPON_WALLET_INFO.getKey(), json.toJSONString());
        priceDisplayDTO.setExtendDisplayInfo(extendInfo);
        params.setDealPromDisplayDTO(priceDisplayDTO);
        // act
        CouponModule result = promoDetailService.buildMagicCouponPackage(params);
        // assert
        assertNotNull(result);
        assertEquals(CouponTypeEnum.MAGIC_COUPON_PACKAGE.getCode(), result.getCouponType());
        assertEquals(CouponStyleEnum.BIG_CARD.getCode(), result.getCouponStyle());
        assertEquals(1, result.getCouponGroupIdList().size());
        assertEquals("pkg1", result.getCouponGroupIdList().get(0));
    }

    /**
     * Test when walletInfo has multiple couponPackageIds
     */
    @Test
    public void testBuildMagicCouponPackageWhenMultipleCouponPackageIds() throws Throwable {
        // arrange
        PromotionDetailParams params = new PromotionDetailParams();
        PriceDisplayDTO priceDisplayDTO = new PriceDisplayDTO();
        Map<String, String> extendInfo = new HashMap<>();
        JSONObject json = new JSONObject();
        JSONArray packageList = new JSONArray();
        JSONObject package1 = new JSONObject();
        package1.put("couponPackageId", "pkg1");
        JSONObject package2 = new JSONObject();
        package2.put("couponPackageId", "pkg2");
        packageList.add(package1);
        packageList.add(package2);
        json.put("couponPackageList", packageList);
        extendInfo.put(ExtendDisplayInfoKeyEnum.COUPON_WALLET_INFO.getKey(), json.toJSONString());
        priceDisplayDTO.setExtendDisplayInfo(extendInfo);
        params.setDealPromDisplayDTO(priceDisplayDTO);
        // act
        CouponModule result = promoDetailService.buildMagicCouponPackage(params);
        // assert
        assertNotNull(result);
        assertEquals(2, result.getCouponGroupIdList().size());
        assertTrue(result.getCouponGroupIdList().contains("pkg1"));
        assertTrue(result.getCouponGroupIdList().contains("pkg2"));
    }
}
