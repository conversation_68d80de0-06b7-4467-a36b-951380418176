package com.sankuai.dzshoppingguide.product.detail.application.builder.bookingtime;

import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.extend.BaseBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.abtest.AbTestFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.abtest.AbTestReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.bookingtime.DealBookingTimeFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.bookingtime.DealBookingTimeReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.bookingtime.ShopBookingFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.bookingtime.ShopBookingReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.category.ProductCategoryFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.OrderPageUrlResult;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.handler.booking.BookingTimeModuleOrderPageUrlFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sale.info.ProductSaleInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sale.info.ProductSaleInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.utils.DateTimeUtils;
import com.sankuai.dzshoppingguide.product.detail.application.utils.MultiTradeUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.bookingtime.vo.BookingTimeModuleVO;
import com.sankuai.spt.statequery.api.dto.BaseStateDTO;
import com.sankuai.spt.statequery.api.dto.TimeSliceDTO;
import com.sankuai.spt.statequery.api.enums.TimeSliceLabelEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.jetbrains.annotations.Nullable;

import java.time.LocalDateTime;

@Slf4j
public abstract class AbstractBookingTimeModuleBuilder extends BaseBuilder<BookingTimeModuleVO> {

    protected DealBookingTimeReturnValue dealBookingTimeReturnValue;
    protected ShopBookingReturnValue shopBookingReturnValue;
    protected ProductCategory productCategory;
    protected ProductBaseInfo productBaseInfo;
    protected OrderPageUrlResult orderPageUrlResult;
    protected AbTestReturnValue abTestReturnValue;
    private boolean saleStatus;
    protected boolean fromMarketingSource;
    protected static final long MASSAGE_MIN_CARD_COUNT = 12;

    protected void init() {
        shopBookingReturnValue = getDependencyResult(ShopBookingFetcher.class);
        dealBookingTimeReturnValue = getDependencyResult(DealBookingTimeFetcher.class);
        productCategory = getDependencyResult(ProductCategoryFetcher.class);
        productBaseInfo = getDependencyResult(ProductBaseInfoFetcher.class);
        orderPageUrlResult = getDependencyResult(BookingTimeModuleOrderPageUrlFetcher.class);
        abTestReturnValue = getDependencyResult(AbTestFetcher.class);
        fromMarketingSource = MultiTradeUtils.isFromMarketingSales(request);
        saleStatus = getDependencyResult(ProductSaleInfoFetcher.class, ProductSaleInfo.class).map(ProductSaleInfo::isOnlineForSale).orElse(false);
    }

    /**
     * 是否展示时间选择
     */
    protected boolean isShow() {
        if (productCategory == null || productCategory.getProductSecondCategoryId() <= 0 || productBaseInfo == null) {
            return false;
        }
        // 商品类目判断
        if (!MultiTradeUtils.isMultiTradeCategory(request)) {
            return false;
        }
        // 是否支持展示一品多态
        if (!MultiTradeUtils.showMultiTradeFeature(productBaseInfo.getBasic(), shopBookingReturnValue, abTestReturnValue, request)) {
            return false;
        }
        // 商品库存判断
        if (!saleStatus) {
            return false;
        }
        // 商品可预订时间片判断
        if (dealBookingTimeReturnValue == null || dealBookingTimeReturnValue.getBaseStateQueryResultDTO() == null) {
            return false;
        }
        return CollectionUtils.isNotEmpty(dealBookingTimeReturnValue.getBaseStateQueryResultDTO().getBaseStates());
    }

    protected BookingStateInfo getBookingState(TimeSliceDTO timeSliceDTO) {
        // 不需要校验库存状态，库存为0也可能可订，因此一切以available结果为准
        if (!timeSliceDTO.isAvailable() || CollectionUtils.isEmpty(timeSliceDTO.getLabels())) {
            return new BookingStateInfo("订满", "#C2C2C2");
        }
        if (timeSliceDTO.getLabels().contains(TimeSliceLabelEnum.RESIDUE_ONLY_N) && timeSliceDTO.getResidue() != null && timeSliceDTO.getResidue() > 0) {
            return new BookingStateInfo(String.format("剩余%d", timeSliceDTO.getResidue()), "#555555");
        }
        if (timeSliceDTO.getLabels().contains(TimeSliceLabelEnum.RESIDUE_TIGHT)) {
            return new BookingStateInfo("紧张", "#FF4B10");
        }
        if (timeSliceDTO.getLabels().contains(TimeSliceLabelEnum.RESIDUE_SUFFICIENT)) {
            return new BookingStateInfo("空闲", "#555555");
        }
        return null;
    }

    protected @Nullable BaseStateDTO getBaseStateDTO(String offsetDate) {
        return dealBookingTimeReturnValue.getBaseStateQueryResultDTO()
                .getBaseStates()
                .stream()
                .filter(s -> offsetDate.equals(s.getDay()))
                .findFirst()
                .orElse(null);
    }

    protected String getWeekText(int offset) {
        switch (offset) {
            case -1:
                return "凌晨";
            case 0:
                return "今天";
            case 1:
                return "明天";
            default:
                return DateTimeUtils.getWeekStr(LocalDateTime.now().plusDays(offset));
        }
    }

    private boolean isValidShop() {
        if (shopBookingReturnValue == null || shopBookingReturnValue.getShopBookInfoQueryResultDTO() == null) {
            return false;
        }
        if (CollectionUtils.isEmpty(shopBookingReturnValue.getShopBookInfoQueryResultDTO().getShopBookInfos()) || shopBookingReturnValue.getShopBookInfoQueryResultDTO().getShopBookInfos().get(0) == null) {
            return false;
        }
        return shopBookingReturnValue.getShopBookInfoQueryResultDTO().getShopBookInfos().get(0).getDealgroupSupportBook();
    }

    @Data
    @AllArgsConstructor
    protected static class BookingStateInfo {
        private String text;
        private String color;
    }

}
