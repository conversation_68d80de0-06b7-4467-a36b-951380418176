package com.sankuai.dzshoppingguide.product.detail.application.utils.model;

import com.sankuai.mktplay.center.mkt.play.center.client.play.entity.ActiveRequestUnitV2;
import lombok.Data;

import java.util.List;

/**
 * @author: w<PERSON><PERSON><PERSON><PERSON>
 * @create: 2024-08-07
 * @description: 根据任务选单玩法活动模型
 */
@Data
public class FilterPlayActivityModel {
    /**
     * 活动ID
     */
    private String activityId;

    /**
     * 副标题
     */
    private String subTitle;

    /**
     * 玩法PlayId
     */
    private Long playId;

    /**
     * 素材信息
     */
    private List<MaterialInfoModel> materialInfoList;

    /**
     * 任务信息
     */
    private TaskInfoModel taskInfo;

    /**
     * 团单匹配记录
     */
    private List<ActiveRequestUnitV2> activeRequestUnit;
}
