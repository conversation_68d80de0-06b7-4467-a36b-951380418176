package com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.component.trade.block.banner;

import com.alibaba.fastjson.JSON;
import com.dianping.lion.facade.LionFacade;
import com.google.common.collect.Lists;
import com.sankuai.dealuser.price.display.api.enums.CouponAssignStatusEnum;
import com.sankuai.dealuser.price.display.api.enums.PromoTypeEnum;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.banner.BottomBarTopBannerVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.backgroud.BottomBarBackgroundVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.rich.content.CountDownRichContentVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.rich.content.MarginLeftRichContentVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.rich.content.PicRichContentVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.rich.content.TextRichContentVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.rich.content.enums.TextStyleEnum;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Set;


/**
 * @Author: guangyujie
 * @Date: 2025/3/16 16:47
 */
@Slf4j
public class CouponBannerComponent {

    private static final long MILLISECONDS_OF_DAY = 24 * 60 * 60 * 1000;

    private static final String COUPON_INFO = "https://p0.meituan.net/ingee/da8c587fba577ab8d535a2c3a5ab20773446.png";

    public static BottomBarTopBannerVO build(final ProductDetailPageRequest request,
                                             final ProductCategory productCategory,
                                             final PriceDisplayDTO dealPromoPrice) {
        try {
            if (productCategory == null || dealPromoPrice == null) {
                return null;
            }
            if (!isCouponBar(productCategory.getProductSecondCategoryId())) {
                return null;
            }
            CouponBannerInfo couponBannerInfo = buildBanner(dealPromoPrice);
            if (!couponBannerInfo.isShow()) {
                return null;
            }
            return buildCouponBanner(couponBannerInfo);
        } catch (Throwable throwable) {
            log.error("CouponBannerComponent.build,request:{}", JSON.toJSONString(request), throwable);
            return null;
        }
    }

    /**
     * 判断是否是券后信息透传样式的底bar，影响了1. 横幅展示券信息 2. 按钮根据券的领取状态修改文案 3. 价格区根据券的领取状态修改文案
     */
    private static boolean isCouponBar(int categoryId) {
        Set<Integer> couponCategoryIds = LionFacade
                .getSet("com.sankuai.dzu.tpbase.dztgdetailweb.coupon.bar", Integer.TYPE, Collections.emptySet());
        if (CollectionUtils.isEmpty(couponCategoryIds)) {
            return false;
        }
        return couponCategoryIds.contains(categoryId);
    }

    /**
     * 构造券横幅信息，其中有已领取的券则used设为true，否则设为false
     */
    private static @NotNull CouponBannerInfo buildBanner(PriceDisplayDTO dealPromoPrice) {
        CouponBannerInfo couponBannerInfo = new CouponBannerInfo();
        if (CollectionUtils.isEmpty(dealPromoPrice.getUsedPromos())) {
            couponBannerInfo.setShow(false);
            return couponBannerInfo;
        }
        List<PromoDTO> couponPromos = dealPromoPrice.getUsedPromos();
        BigDecimal totalAmount = BigDecimal.ZERO;
        boolean show = false;
        boolean used = false;
        Date endTime = new Date(Long.MAX_VALUE);
        //普通消费券和政府消费券判断领取方式不一样，时间取结束时间最早的
        for (PromoDTO couponPromo : couponPromos) {
            if (couponPromo.getIdentity().getPromoType() == PromoTypeEnum.COUPON.getType()) {
                show = true;
                if (!couponPromo.isCanAssign()) {
                    used = true;
                }
                totalAmount = totalAmount.add(couponPromo.getAmount());
                if (couponPromo.getEndTime() != null && couponPromo.getEndTime().before(endTime)) {
                    endTime = couponPromo.getEndTime();
                }
            } else if (couponPromo.getIdentity().getPromoType() == PromoTypeEnum.GOVERNMENT_CONSUME_COUPON.getType()) {
                show = true;
                if (couponPromo.getCouponAssignStatus() == CouponAssignStatusEnum.ASSIGNED.getCode()) {
                    used = true;
                }
                totalAmount = totalAmount.add(couponPromo.getAmount());
                if (couponPromo.getEndTime() != null && couponPromo.getEndTime().before(endTime)) {
                    endTime = couponPromo.getEndTime();
                }
            }
        }
        couponBannerInfo.setShow(show);
        couponBannerInfo.setValid(used);
        couponBannerInfo.setAmount(totalAmount);
        if (endTime.before(new Date(Long.MAX_VALUE))) {
            couponBannerInfo.setEndTime(endTime);
        }
        return couponBannerInfo;
    }

        private static BottomBarTopBannerVO buildCouponBanner(CouponBannerInfo couponBannerInfo) {
        BottomBarTopBannerVO bannerVO = new BottomBarTopBannerVO();
        bannerVO.setBackground(BottomBarBackgroundVO.buildLevelGradientColor(
                Lists.newArrayList("#FFE0E6", "#FEE7E1"),
                "https://img.meituan.net/beautyimg/bb5697d8da0279075b4f5f272f09266c260.png"
        ));
        bannerVO.add(new PicRichContentVO(36, 26, COUPON_INFO))
                .add(new MarginLeftRichContentVO(8))
                .add(buildDefaultText("你有"))
                .add(buildSpecialText(String.valueOf(couponBannerInfo.getAmount())))
                .add(buildDefaultText("元专属优惠券"));
        long now = System.currentTimeMillis();
        if (couponBannerInfo.isValid() && couponBannerInfo.getEndTime().getTime() > now) {
            Date sevenDaysLater = new Date(now + 7L * MILLISECONDS_OF_DAY);
            Date oneDayLater = new Date(now + MILLISECONDS_OF_DAY);
            if (couponBannerInfo.getEndTime().before(oneDayLater)) { //只有24小时之内才设置倒计时时间戳
                bannerVO.add(buildDefaultText("，下单立享，仅剩"));
                bannerVO.add(new CountDownRichContentVO(
                        couponBannerInfo.getEndTime().getTime(), TextStyleEnum.Bold, 20, "#FF4B10"
                ));
            } else if (couponBannerInfo.getEndTime().before(sevenDaysLater)) {
                bannerVO.add(buildDefaultText("，下单立享，仅剩"));
                int days = (int) ((couponBannerInfo.getEndTime().getTime() - now) / MILLISECONDS_OF_DAY);
                bannerVO.add(buildSpecialText(String.valueOf(days)));
                bannerVO.add(buildDefaultText("天 "));
            } else {
                bannerVO.add(buildDefaultText("，下单立享"));
            }
        } else {
            bannerVO.add(buildDefaultText("，点击下单领取可立享优惠"));
        }
        return bannerVO;
    }

    private static TextRichContentVO buildDefaultText(String text) {
        return new TextRichContentVO(
                text, TextStyleEnum.Default, 24, "#222222"
        );
    }

    private static TextRichContentVO buildSpecialText(String text) {
        return new TextRichContentVO(
                text, TextStyleEnum.Bold, 24, "#FF4B10"
        );
    }


    @Data
    private static class CouponBannerInfo {

        /**
         * 适用券金额总和
         */
        private BigDecimal amount;

        /**
         * 优惠券到期时间，取到期时间最早的
         */
        private Date endTime;

        /**
         * 券是否已领取
         */
        private boolean valid;

        /**
         * 是否展示横幅
         */
        private boolean show;

    }

}
