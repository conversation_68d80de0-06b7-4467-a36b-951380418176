package com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.rich.content;

import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.rich.content.enums.RichContentTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.rich.content.enums.TextStyleEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * @Author: guangyujie
 * @Date: 2025/3/16 15:12
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class TextRichContentVO extends RichContentVO {

    /**
     * 文案
     */
    private String text;

    /**
     * 下划线
     */
    private boolean underline;

    /**
     * 中划线
     */
    private boolean strikeThrough;

    /**
     * Bold=加粗，Default=默认
     */
    private String textStyle;

    private int textSize;

    private String textColor;

    private String backGroundColor;

    public TextRichContentVO(String text, TextStyleEnum textStyle, int textSize, String textColor) {
        this.text = text;
        this.textStyle = textStyle.name();
        this.textSize = textSize / 2;
        this.textColor = textColor;
    }

    @Override
    public int getType() {
        return RichContentTypeEnum.TEXT.getCode();
    }
}
