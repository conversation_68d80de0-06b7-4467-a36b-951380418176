package com.sankuai.dzshoppingguide.product.detail.application.fetcher.price;

import com.sankuai.dealuser.price.display.api.enums.RequestSceneEnum;
import com.sankuai.dealuser.price.display.api.model.BatchPriceRequest;
import com.sankuai.dealuser.price.display.api.model.ClientEnv;
import com.sankuai.dealuser.price.display.api.model.ProductIdentity;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.membercard.card.entrance.MemberEntranceResult;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.membercard.card.entrance.ShopMemberEntranceFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.dto.ProductPriceReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.defaultselect.SkuDefaultSelect;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.defaultselect.SkuDefaultSelectFetcher;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

@Fetcher(
        previousLayerDependencies = {
                ShopMemberEntranceFetcher.class,
                SkuDefaultSelectFetcher.class
        }
)
public class PremiumMemberPriceFetcher extends AbstractProductPriceFetcher<ProductPriceReturnValue> {

    @Override
    protected CompletableFuture<ProductPriceReturnValue> doFetch() {
        MemberEntranceResult memberEntranceResult = getDependencyResult(ShopMemberEntranceFetcher.class);
        if (memberEntranceResult == null || !memberEntranceResult.isHasEntrance()) {
            return CompletableFuture.completedFuture(null);
        }
        SkuDefaultSelect skuDefaultSelect = getDependencyResult(SkuDefaultSelectFetcher.class);
        CompletableFuture<ProductPriceReturnValue> normalPriceCf = buildNormalPriceDisplayReturnValue(request, skuDefaultSelect);
        CompletableFuture<ProductPriceReturnValue> memberPriceCf = buildMemberPriceDisplayReturnValue(request, skuDefaultSelect);
        return CompletableFuture.allOf(normalPriceCf, memberPriceCf).thenApply(aVoid -> {
            ProductPriceReturnValue priceReturnValue = new ProductPriceReturnValue();
            priceReturnValue.setOriginPriceDisplayDTO(Optional.ofNullable(normalPriceCf.join()).map(ProductPriceReturnValue::getPriceDisplayDTO).orElse(null));
            priceReturnValue.setPriceDisplayDTO(Optional.ofNullable(memberPriceCf.join()).map(ProductPriceReturnValue::getPriceDisplayDTO).orElse(null));
            return priceReturnValue;
        });
    }

    private CompletableFuture<ProductPriceReturnValue> buildNormalPriceDisplayReturnValue(ProductDetailPageRequest pageRequest, SkuDefaultSelect skuDefaultSelect) {
        BatchPriceRequest priceRequest = buildBatchPriceRequest(pageRequest, skuDefaultSelect);
        priceRequest.setScene(RequestSceneEnum.PROMO_DETAIL_DESC_WithDealPromo.getScene());
        return getProductPriceDisplayDTO(priceRequest).thenApply(ProductPriceReturnValue::new);
    }

    private CompletableFuture<ProductPriceReturnValue> buildMemberPriceDisplayReturnValue(ProductDetailPageRequest pageRequest, SkuDefaultSelect skuDefaultSelect) {
        BatchPriceRequest priceRequest = buildBatchPriceRequest(pageRequest, skuDefaultSelect);
        priceRequest.setScene(RequestSceneEnum.PROMO_DETAIL_DESC_WithBestPrice.getScene());
        return getProductPriceDisplayDTO(priceRequest).thenApply(ProductPriceReturnValue::new);
    }

    @Override
    protected void postProcessPriceRequest(ClientEnv clientEnv, List<ProductIdentity> productIdentities, Map<String, String> extension) {

    }
}
