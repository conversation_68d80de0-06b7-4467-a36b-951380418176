package com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.ext.param.handler;

import com.sankuai.dz.product.detail.RequestCustomParamEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.enums.OrderPageExtParamEnums;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.ext.param.BaseSingleExtParamBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.ext.param.request.ExtParamBuilderRequest;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * @Author: guangyujie
 * @Date: 2025/3/12 17:23
 */
@Component
public class PassParamExtParamBuilder extends BaseSingleExtParamBuilder {

    @Override
    public OrderPageExtParamEnums buildKey() {
        return OrderPageExtParamEnums.pass_param;
    }

    @Override
    protected String doBuildExtParam(final ProductDetailPageRequest request,
                                     final ExtParamBuilderRequest builderRequest) throws Exception {
        String pass_param = request.getCustomParam(RequestCustomParamEnum.pass_param);//todo
        if (StringUtils.isBlank(pass_param) || pass_param.equals("undefined")) {
            return null;
        }
        return pass_param;
    }

}
