package com.sankuai.dzshoppingguide.product.detail.application.service.dto;

import com.sankuai.mpmctmember.query.thrift.dto.MemberInterestDetailDTO;
import lombok.Data;

/**
 * @Author: guangyujie
 * @Date: 2024/8/12 19:29
 */
@Data
public class ShopMemberDetailV {

    /**
     * 会员卡id
     */
    private long planId;

    /**
     * 是否是会员
     */
    private boolean member;

    /**
     * 是否是新会员
     */
    private boolean newMember;

    /**
     * 该团单是否只供新会员购买
     */
    private boolean dealOnlyForNewMember;

    /**
     * 团单是否会员专属
     */
    private boolean dealMemberExclusive;

    /**
     * 商家会员原始请求结果
     */
    private MemberInterestDetailDTO originalResult;

    public boolean isSkipWhenNotNewMemberButDealIsOnlyForNewMember() {
        return isMember() && !isNewMember() && isDealOnlyForNewMember();
    }

}
