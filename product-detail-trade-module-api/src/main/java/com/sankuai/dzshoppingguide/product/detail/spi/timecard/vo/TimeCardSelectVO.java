package com.sankuai.dzshoppingguide.product.detail.spi.timecard.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.dz.product.detail.gateway.spi.vo.AbstractModuleVO;
import lombok.Data;

import java.util.List;

import static com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants.MODULE_DETAIL_DEAL_TIME_CARD;

/**
 * https://mobile.sankuai.com/studio/model/info/43118
 */
@Data
@TypeDoc(description = "关联次卡模块")
public class TimeCardSelectVO extends AbstractModuleVO {
    /**
     * 总数
     */
    @MobileDo.MobileField
    private int totalCount;

    /**
     * 次卡列表
     */
    @MobileField
    private List<TimeCardItemVO> cardList;

    @Override
    public String getModuleKey() {
        return MODULE_DETAIL_DEAL_TIME_CARD;
    }

    public TimeCardSelectVO(List<TimeCardItemVO> cardList) {
        this.cardList = cardList;
    }
}
