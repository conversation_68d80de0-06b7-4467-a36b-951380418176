package com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ProductAttrTest {

    /**
     * 测试 getProductAttr 方法，当 skuAttr 为 null 时，应返回 Optional.empty()
     */
    @Test
    public void testGetProductAttrWhenSkuAttrIsNull() throws Throwable {
        // arrange
        Map<String, AttrDTO> skuAttr = null;
        ProductAttr productAttr = new ProductAttr(skuAttr);
        // act
        Optional<AttrDTO> result = productAttr.getProductAttr("attrName");
        // assert
        assertFalse(result.isPresent());
    }

    /**
     * 测试 getProductAttr 方法，当 skuAttr 不为 null，但 attrName 在 skuAttr 中不存在时，应返回 Optional.empty()
     */
    @Test
    public void testGetProductAttrWhenAttrNameNotInSkuAttr() throws Throwable {
        // arrange
        Map<String, AttrDTO> skuAttr = new HashMap<>();
        skuAttr.put("otherAttrName", new AttrDTO());
        ProductAttr productAttr = new ProductAttr(skuAttr);
        // act
        Optional<AttrDTO> result = productAttr.getProductAttr("attrName");
        // assert
        assertFalse(result.isPresent());
    }

    /**
     * 测试 getProductAttr 方法，当 skuAttr 不为 null，且 attrName 在 skuAttr 中存在时，应返回 Optional 包装的 AttrDTO 对象
     */
    @Test
    public void testGetProductAttrWhenAttrNameInSkuAttr() throws Throwable {
        // arrange
        Map<String, AttrDTO> skuAttr = new HashMap<>();
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setName("attrName");
        skuAttr.put("attrName", attrDTO);
        ProductAttr productAttr = new ProductAttr(skuAttr);
        // act
        Optional<AttrDTO> result = productAttr.getProductAttr("attrName");
        // assert
        assertTrue(result.isPresent());
        assertEquals("attrName", result.get().getName());
    }
}
