package com.sankuai.dzshoppingguide.product.detail.application.utils;

import com.dianping.lion.client.Lion;
import com.meituan.mdp.boot.starter.MdpContextUtils;
import com.sankuai.dz.product.detail.RequestCustomParamEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bookingtime.enums.MultiTradeTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.abtest.AbTestReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.bookingtime.ShopBookingReturnValue;
import com.sankuai.general.product.query.center.client.dto.DealGroupBasicDTO;
import com.sankuai.general.product.query.center.client.enums.TradeTypeEnum;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.List;
import java.util.Map;
import java.util.Optional;

import static com.sankuai.dzshoppingguide.product.detail.application.constants.LionConstants.*;
import static com.sankuai.dzshoppingguide.product.detail.application.constants.LionConstants.NONE_MULTI_TRADE_TYPE_MARKETING_SOURCE;

public class MultiTradeUtils {

    /**
     * 是否来自支持一品多态定义的营销场域
     */
    public static boolean isFromMarketingSales(ProductDetailPageRequest request) {
        List<String> marketingSource = Lion.getList(MdpContextUtils.getAppKey(), MULTI_TRADE_TYPE_MARKETING_SOURCE, String.class);
        return CollectionUtils.isNotEmpty(marketingSource) && marketingSource.contains(request.getFromPage());
    }

    /**
     * 是否来自不支持一品多态定义的营销场域
     */
    private static boolean isFromNoneMarketingSales(ProductDetailPageRequest request) {
        List<String> marketingSource = Lion.getList(MdpContextUtils.getAppKey(), NONE_MULTI_TRADE_TYPE_MARKETING_SOURCE, String.class);
        return CollectionUtils.isNotEmpty(marketingSource) && marketingSource.contains(request.getFromPage());
    }

    /**
     * 是否为支持一品多态类目
     */
    public static boolean isMultiTradeCategory(ProductDetailPageRequest request) {
        Map<String, CategoryConfig> categoryConfigMap = Lion.getMap(MdpContextUtils.getAppKey(), SUPPORT_MULTI_TRADE_CATEGORY, CategoryConfig.class);
        if (MapUtils.isEmpty(categoryConfigMap) ) {
            return false;
        }
        CategoryConfig config = categoryConfigMap.get(String.valueOf(getCategoryId(request)));
        if (config == null) {
            return false;
        }
        int serviceTypeId = getServiceTypeId(request);
        return config.all || serviceTypeId == 0 || config.getServiceTypeIds().contains(serviceTypeId);
    }

    /**
     * 获取一品多态实验类型
     */
    public static MultiTradeTypeEnum getMultiTradeType(ProductDetailPageRequest request, AbTestReturnValue abTestReturnValue) {
        if (isFromNoneMarketingSales(request)) {
            return MultiTradeTypeEnum.DEFAULT;
        }
        String expName = getExpName(request.getClientTypeEnum().isMtClientType(), getCategoryId(request), request);
        String expResult = Optional.ofNullable(abTestReturnValue).map(a -> a.getAbTestExpResult(expName)).orElse("");
        return MultiTradeTypeEnum.getMultiTradeEnum(expResult);
    }

    private static int getCategoryId(ProductDetailPageRequest request) {
       return NumberUtils.toInt(request.getCustomParam(RequestCustomParamEnum.productSecondCategoryId), 0);
    }

    private static int getServiceTypeId(ProductDetailPageRequest request) {
        return NumberUtils.toInt(request.getCustomParam(RequestCustomParamEnum.productThirdCategoryId), 0);
    }

    /**
     * 是否一品多态商品
     */
    public static boolean isMultiTradeGoods(DealGroupBasicDTO dealGroupBasicDTO) {
        return dealGroupBasicDTO != null && CollectionUtils.isNotEmpty(dealGroupBasicDTO.getTradeTypes()) && dealGroupBasicDTO.getTradeTypes().contains(TradeTypeEnum.RESERVATION_PAY.getCode());
    }

    /**
     * 是否一品多态门店
     */
    private static boolean isMultiTradeShop(ShopBookingReturnValue shopBookingReturnValue) {
        return shopBookingReturnValue != null && shopBookingReturnValue.getShopBookInfoQueryResultDTO() != null
                && CollectionUtils.isNotEmpty(shopBookingReturnValue.getShopBookInfoQueryResultDTO().getShopBookInfos()) && shopBookingReturnValue.getShopBookInfoQueryResultDTO().getShopBookInfos().get(0) != null
                && shopBookingReturnValue.getShopBookInfoQueryResultDTO().getShopBookInfos().get(0).getDealgroupSupportBook();
    }

    /**
     * 展示一品多态特性
     * 商品是一品多态 & 门店是一品多态 & 满足一品多态渠道 & 命中一品多态实验
     *
     */
    public static boolean showMultiTradeFeature(DealGroupBasicDTO dealGroupBasicDTO, ShopBookingReturnValue shopBookingReturnValue, AbTestReturnValue abTestReturnValue, ProductDetailPageRequest request) {
        return isMultiTradeGoods(dealGroupBasicDTO)
                && isMultiTradeShop(shopBookingReturnValue)
                && isValidSource(dealGroupBasicDTO, request)
                && passMultiTradeExp(request, abTestReturnValue);
    }

    /**
     * 支持一品多态的渠道
     */
    private static boolean isValidSource(DealGroupBasicDTO dealGroupBasicDTO, ProductDetailPageRequest request) {
        if (isFromNoneMarketingSales(request)) {
            return false;
        }
        // 足疗 + KTV营销场
        return dealGroupBasicDTO.getCategoryId() == 303 || (dealGroupBasicDTO.getCategoryId() == 301 && isFromMarketingSales(request));
    }


    /**
     * 命中一品多态实验
     */
    private static boolean passMultiTradeExp(ProductDetailPageRequest request, AbTestReturnValue abTestReturnValue) {
        MultiTradeTypeEnum multiTradeType = getMultiTradeType(request, abTestReturnValue);
        return multiTradeType.isShowMultiTradeFeature();
    }

    private static String getExpName(boolean isMt, int categoryId, ProductDetailPageRequest request) {
        return String.format("%s_%d_%s_MultiTrade", isMt ? "Mt" : "Dp", categoryId, isFromMarketingSales(request) ? "Sales" : "Main");
    }

    @Data
    public static class CategoryConfig {
        /**
         * 是否全部三级分类支持
         */
        private boolean all = true;
        /**
         * 当all=false时，仅serviceTypeIds下支持一品多态
         */
        private List<Integer> serviceTypeIds;
    }
}
