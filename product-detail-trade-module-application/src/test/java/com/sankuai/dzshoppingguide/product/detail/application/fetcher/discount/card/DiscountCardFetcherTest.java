// package com.sankuai.dzshoppingguide.product.detail.application.fetcher.discount.card;
//
// import com.dianping.pigeon.remoting.common.codec.json.JacksonUtils;
// import com.google.common.collect.Maps;
// import com.sankuai.dz.product.detail.gateway.spi.dto.CustomParam;
// import com.sankuai.dz.product.detail.gateway.spi.dto.ShepherdGatewayParam;
// import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
// import org.junit.Test;
// import org.mockito.internal.util.collections.Sets;
// import org.springframework.test.util.ReflectionTestUtils;
//
// import java.util.Map;
//
// /**
//  * @Author: wb_wangxiaoguang02
//  * @Date: 2025/2/11 16:07
//  */
// public class DiscountCardFetcherTest {
//     @Test
//     public void fetchDiscountCard() {
//         ProductDetailPageRequest request = new ProductDetailPageRequest();
//         request.setModuleKeys(Sets.newSet("module_detail_discountcard"));
//         request.setProductId(423627757);
//         request.setProductType(2);
//         request.setCityId(10);
//         request.setUserLng(121.5289);
//         request.setUserLat(31.2713);
//         request.setGpsCoordinateType(1);
//         request.setPoiId(604616051L);
//
//         ShepherdGatewayParam shepherdGatewayParam = new ShepherdGatewayParam();
//         shepherdGatewayParam.setMtUserId(5080419182L);
//         shepherdGatewayParam.setUserId(5080419182L);
//         shepherdGatewayParam.setMt(true);
//         shepherdGatewayParam.setDpid("0000000000000C4B1905D845240C586F7ECBEF9F89108A169846878973102544");
//         shepherdGatewayParam.setUnionid("66737d2ab1c14fc6aca9243bb1982700a172244690622860466");
//         shepherdGatewayParam.setClient("ios");
//         shepherdGatewayParam.setAppVersion("12.28.402");
//         shepherdGatewayParam.setMtsiflag("0");
//         shepherdGatewayParam.setPlatform("mt");
//
//         request.setShepherdGatewayParam(shepherdGatewayParam);
//         Map<String, String> customParams = Maps.newHashMap();
//         customParams.put("productFirstCategoryId","1");
//         customParams.put("productSecondCategoryId","80303");
//         customParams.put("productThirdCategoryId","3");
//         CustomParam customParam = new CustomParam();
//         ReflectionTestUtils.setField(customParam, "customParams", customParams);
//         request.setCustomParam(customParam);
//
//
//         String serialize = JacksonUtils.serialize(request);
//         System.out.println("serialize = " + serialize);
//
//
//     }
//
// }