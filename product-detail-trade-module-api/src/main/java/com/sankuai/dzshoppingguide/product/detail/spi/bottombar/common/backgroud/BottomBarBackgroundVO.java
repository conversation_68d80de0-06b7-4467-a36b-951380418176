package com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.backgroud;

import com.google.common.collect.Lists;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.backgroud.enums.BackgroundTypeEnum;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: guang<PERSON>jie
 * @Date: 2025/3/16 15:06
 */
@Data
@NoArgsConstructor
public class BottomBarBackgroundVO implements Serializable {

    /**
     * @see BackgroundTypeEnum
     */
    private int type;

    /**
     * 纯色or渐变色开始色，type=1or3有效
     */
    private List<String> colors;

    /**
     * 边框颜色
     */
    private String borderColor;

    /**
     * 边框宽度
     */
    private int borderWidth;

    /**
     * 图片url，type=2有效
     */
    private String image;

    /**
     * 箭头图片url，type=2有效，目前默认指向购买按钮
     */
    private String arrowImage;

    public static BottomBarBackgroundVO buildSingleColor(String color) {
        BottomBarBackgroundVO buttonBackgroundVO = new BottomBarBackgroundVO();
        buttonBackgroundVO.setType(BackgroundTypeEnum.COLOR.getCode());
        buttonBackgroundVO.setColors(Lists.newArrayList(color));
        return buttonBackgroundVO;
    }

    public static BottomBarBackgroundVO buildLevelGradientColor(List<String> colors) {
        BottomBarBackgroundVO buttonBackgroundVO = new BottomBarBackgroundVO();
        buttonBackgroundVO.setType(BackgroundTypeEnum.LEVEL_GRADIENT_COLOR.getCode());
        buttonBackgroundVO.setColors(colors);
        return buttonBackgroundVO;
    }

    public static BottomBarBackgroundVO buildLevelGradientColor(List<String> colors, String arrowImage) {
        BottomBarBackgroundVO bottomBarBackgroundVO = buildLevelGradientColor(colors);
        bottomBarBackgroundVO.setArrowImage(arrowImage);
        return bottomBarBackgroundVO;
    }

    public static BottomBarBackgroundVO buildVerticalGradientColor(List<String> colors) {
        BottomBarBackgroundVO buttonBackgroundVO = new BottomBarBackgroundVO();
        buttonBackgroundVO.setType(BackgroundTypeEnum.VERTICAL_GRADIENT_COLOR.getCode());
        buttonBackgroundVO.setColors(colors);
        return buttonBackgroundVO;
    }

    public static BottomBarBackgroundVO buildSingleColorWithBorder(String color, String borderColor, int borderWidth) {
        BottomBarBackgroundVO buttonBackgroundVO = new BottomBarBackgroundVO();
        buttonBackgroundVO.setType(BackgroundTypeEnum.COLOR.getCode());
        buttonBackgroundVO.setColors(Lists.newArrayList(color));
        buttonBackgroundVO.setBorderColor(borderColor);
        buttonBackgroundVO.setBorderWidth(borderWidth);
        return buttonBackgroundVO;
    }

}
