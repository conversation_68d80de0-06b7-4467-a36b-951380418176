package com.sankuai.dzshoppingguide.product.detail.spi.promo.vo;

import java.io.Serializable;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import lombok.Data;

@TypeDoc(description = "动作触发点按钮")
@Data
@MobileDo(id = 0xa393)
public class ActionButton implements Serializable {

    @FieldDoc(description = "跳转链接")
    @MobileField(key = 0x774e)
    private String jumpUrl;

    @FieldDoc(description = "渐变结束颜色")
    @MobileField(key = 0x37ca)
    private String buttonEndColor;

    @FieldDoc(description = "渐变开始颜色")
    @MobileField(key = 0x2a3a)
    private String buttonStartColor;

    @FieldDoc(description = "按钮文案")
    @MobileField(key = 0xe221)
    private String buttonText;

    @FieldDoc(description = "按钮文字颜色")
    @MobileField(key = 0xeead)
    private String textColor;
}
