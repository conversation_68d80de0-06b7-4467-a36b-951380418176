package com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.builder.handler.zero.reserve;

import com.dianping.deal.detail.enums.PlatformEnum;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.BuilderTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.builder.BaseBottomBarBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.component.trade.block.utils.TradeButtonBuildUtils;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.shopid.ShopIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.shopid.ShopIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttrFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sale.info.enums.ProductSaleStatusEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.reserve.ReserveStatusOfReservationDealFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.reserve.ReserveStatusOfReservationDealResult;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.FetcherDAGStarter;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.ProductBuyBarModule;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.enums.OpenTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.extend.none.DoNothingActionVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.extend.redirect.SimpleRedirectActionVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.block.trade.StandardTradeBlockVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.component.trade.button.StandardTradeButtonVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.bottom.scene.StandardTradeBottomBarVO;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.general.product.query.center.client.dto.PriceDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Optional;

import static com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.constant.ButtonStyleConstant.STANDARD_TRADE_BUTTON_STYLE;

/**
 * @Author: guangyujie
 * @Date: 2025/3/19 22:59
 */
@Slf4j
@Builder(
        builderType = BuilderTypeEnum.VARIABLE_BUILDER,
        moduleKey = ModuleKeyConstants.BOTTOM_BAR,
        startFetcher = FetcherDAGStarter.class,
        dependentFetchers = {
                ShopIdMapperFetcher.class,
                ReserveStatusOfReservationDealFetcher.class,
                ProductBaseInfoFetcher.class,
                ProductAttrFetcher.class
        }
)
public class VaccineFreeReserveBottomBarBuilder extends BaseBottomBarBuilder {

    @Override
    public ProductBuyBarModule buildBuyBarModule() {
        boolean isCanReserve = getDependencyResult(ReserveStatusOfReservationDealFetcher.class, ReserveStatusOfReservationDealResult.class)
                .map(ReserveStatusOfReservationDealResult::isCanReserve)
                .orElse(false);
        StandardTradeButtonVO button = isCanReserve ? buildCanReserveButton() : buildCanNotReserveButton();
        StandardTradeBottomBarVO standardTradeBottomBarVO = new StandardTradeBottomBarVO();
        standardTradeBottomBarVO.setRightBottomBar(StandardTradeBlockVO.buildSingleButtonStyle(button));
        return ProductBuyBarModule.buildWithoutBanner(standardTradeBottomBarVO);
    }

    private StandardTradeButtonVO buildCanReserveButton() {
        String price = getDependencyResult(ProductBaseInfoFetcher.class, ProductBaseInfo.class)
                .map(ProductBaseInfo::getPrice)
                .map(PriceDTO::getSalePrice)
                .orElse(null);
        // key:"retailPriceStyle" value:"1"
        // * 1：正常展示 2：隐藏 3：起价
        String retailPriceStyle = getDependencyResult(ProductAttrFetcher.class, ProductAttr.class)
                .map(attr -> attr.getProductAttrFirstValue("retailPriceStyle"))
                .orElse(null);
        String subTitle = "到门诊支付";
        if ((StringUtils.isBlank(retailPriceStyle) || "1".equals(retailPriceStyle)) //retailPriceStyle为null正常展示
                && StringUtils.isNotBlank(price)) {
            subTitle = "到门诊支付¥" + price;
        }
        return TradeButtonBuildUtils.buildTradeButtonVO(
                ProductSaleStatusEnum.ONLINE,
                "预约接种",
                subTitle,
                STANDARD_TRADE_BUTTON_STYLE,
                new SimpleRedirectActionVO(OpenTypeEnum.redirect, getReserveFillingUrl())
        );
    }

    public String getReserveFillingUrl() {
        ShopIdMapper shopIdMapper = getDependencyResult(ShopIdMapperFetcher.class);
        if (request.getClientTypeEnum().isMtClientType()) {
            String mtUrl = "imeituan://www.meituan.com/mrn?mrn_biz=gc&mrn_entry=vg-mrn-reserve&mrn_component=reservesubmit";
            return mtUrl + "&bizCode=1037"
                    + "&platform=" + PlatformEnum.MEI_TUAN.getType()
                    + "&mtShopId=" + Optional.ofNullable(shopIdMapper).map(ShopIdMapper::getMtBestShopId).orElse(0L)
                    + "&dpDealGroupId=" + request.getProductId()
                    + "&mtDealGroupId=" + request.getProductId()
                    + "&pageType=1"
                    + "&entranceCode=3"
                    + "&bizSourceId=51027";
        } else {
            String dpUrl = "dianping://mrn?mrn_biz=gc&mrn_entry=vg-mrn-reserve&mrn_component=reservesubmit";
            return dpUrl + "&bizCode=1037"
                    + "&platform=" + PlatformEnum.DIAN_PING.getType()
                    + "&dpShopId=" + Optional.ofNullable(shopIdMapper).map(ShopIdMapper::getDpBestShopId).orElse(0L)
                    + "&dpDealGroupId=" + request.getProductId()
                    + "&mtDealGroupId=" + request.getProductId()
                    + "&pageType=1"
                    + "&entranceCode=3"
                    + "&bizSourceId=51027";
        }
    }

    private StandardTradeButtonVO buildCanNotReserveButton() {
        return TradeButtonBuildUtils.buildTradeButtonVO(
                ProductSaleStatusEnum.OFFLINE,
                "抱歉，近60天已约满",
                STANDARD_TRADE_BUTTON_STYLE,
                new DoNothingActionVO()
        );
    }

    protected ProductBuyBarModule buildDefaultBuyBarModule() {
        StandardTradeBottomBarVO standardTradeBottomBarVO = new StandardTradeBottomBarVO();
        standardTradeBottomBarVO.setRightBottomBar(StandardTradeBlockVO.buildSingleButtonStyle(
                TradeButtonBuildUtils.buildTradeButtonVO(
                        ProductSaleStatusEnum.ONLINE,
                        "预约接种",
                        STANDARD_TRADE_BUTTON_STYLE,
                        new SimpleRedirectActionVO(OpenTypeEnum.redirect, getReserveFillingUrl())
                )
        ));
        return ProductBuyBarModule.buildWithoutBanner(standardTradeBottomBarVO);
    }

}
