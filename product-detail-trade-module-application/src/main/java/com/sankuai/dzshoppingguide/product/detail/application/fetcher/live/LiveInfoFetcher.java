package com.sankuai.dzshoppingguide.product.detail.application.fetcher.live;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftClient;
import com.meituan.mdp.boot.starter.thrift.constant.MdpCallType;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dz.product.detail.RequestCustomParamEnum;
import com.sankuai.dz.product.detail.RequestSourceEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.dto.ProductIdDTO;
import com.sankuai.dzshoppingguide.product.detail.domain.utils.ThriftAsyncUtils;
import com.sankuai.general.product.query.center.client.dto.SaleChannelAggregationDTO;
import com.sankuai.general.product.query.center.client.dto.SaleChannelDTO;
import com.sankuai.mlive.goods.trade.api.request.QueryGoodsAllowSellingInfoRequest;
import com.sankuai.mlive.goods.trade.api.response.QueryGoodsAllowSellingInfoResponse;
import com.sankuai.mlive.goods.trade.api.tservice.GoodsAllowSellingTService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.net.URLDecoder;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * @Author: guangyujie
 * @Date: 2025/4/7 22:39
 */
@Fetcher(
        previousLayerDependencies = {
                ProductBaseInfoFetcher.class
        }
)
@Slf4j
public class LiveInfoFetcher extends NormalFetcherContext<LiveInfoDTO> {

    @MdpThriftClient(remoteAppKey = "com.sankuai.mlive.goods.trade", timeout = 1000, callType = MdpCallType.ASYNC)
    private GoodsAllowSellingTService goodsAllowSellingTService;

    @Override
    protected CompletableFuture<LiveInfoDTO> doFetch() throws Exception {
        LiveInfoDTO liveInfoDTO = new LiveInfoDTO();
        liveInfoDTO.setOnlyLiveChannel(isOnlyLiveChannel());
        if (!RequestSourceEnum.fromLive(request.getPageSource())) {
            return CompletableFuture.completedFuture(liveInfoDTO);
        }
        initLiveInfoDTO(liveInfoDTO);
        if (liveInfoDTO.getLiveId() <= 0) {
            return CompletableFuture.completedFuture(liveInfoDTO);
        }
        liveInfoDTO.setGoodsType(getGoodsType());
        // 直播场景只能用美团id
        long mtProductId = getDependencyResult(ProductBaseInfoFetcher.class, ProductBaseInfo.class)
                .map(ProductBaseInfo::getProductIdDTO)
                .map(ProductIdDTO::getMtProductId)
                .orElse(0L);
        if (mtProductId > 0 && liveInfoDTO.getGoodsType() > 0 && liveInfoDTO.getLiveId() > 0) {
            QueryGoodsAllowSellingInfoRequest queryRequest = new QueryGoodsAllowSellingInfoRequest();
            queryRequest.setGoodsId(String.valueOf(mtProductId));
            queryRequest.setGoodsType(liveInfoDTO.getGoodsType());
            queryRequest.setLiveId(liveInfoDTO.getLiveId());
            goodsAllowSellingTService.queryGoodsAllowSellingInfo(queryRequest);
            CompletableFuture<QueryGoodsAllowSellingInfoResponse> goodsSellingInfoDTOCF = ThriftAsyncUtils.getThriftFuture();
            return goodsSellingInfoDTOCF.thenApply(response -> {
                liveInfoDTO.setGoodsSellingInfoDTO(
                        Optional.ofNullable(response)
                                .map(QueryGoodsAllowSellingInfoResponse::getData)
                                .orElse(null)
                );
                return liveInfoDTO;
            });
        }
        return CompletableFuture.completedFuture(liveInfoDTO);
    }

    private void initLiveInfoDTO(final LiveInfoDTO liveInfoDTO) {
        try {
            String passParam = request.getCustomParam(RequestCustomParamEnum.pass_param);
            if (StringUtils.isBlank(passParam)) {
                return;
            }
            String decodedPassParam = URLDecoder.decode(passParam, "UTF-8");
            String promoteChannelInfo = JSON.parseObject(decodedPassParam).getString("PROMOTE_CHANNEL_INFO");
            if (StringUtils.isBlank(promoteChannelInfo)) {
                return;
            }
            String promoteExtend = JSON.parseObject(promoteChannelInfo).getString("promoteExtend");
            if (StringUtils.isBlank(promoteExtend)) {
                return;
            }
            JSONObject promoteExtendJsonObject = JSON.parseObject(promoteExtend);
            if (promoteExtendJsonObject == null) {
                return;
            }
            liveInfoDTO.setLiveId(NumberUtils.toLong(promoteExtendJsonObject.getString("mLiveId")));
            liveInfoDTO.setAnchorId(promoteExtendJsonObject.getString("influencerId"));
            liveInfoDTO.setLiveSecKillActiveId(promoteExtendJsonObject.getString("seckillId"));
        } catch (Exception e) {
            log.error("initLiveInfoDTO fail,request:{}", JSON.toJSONString(request), e);
        }
    }

    private int getGoodsType() {
        String dealParam = request.getCustomParam(RequestCustomParamEnum.dealparam);
        Map<String, String> paraMap = JSON.parseObject(dealParam, new TypeReference<Map<String, String>>() {
        });
        if (MapUtils.isEmpty(paraMap)) {
            return 0;
        }
        String goodsTypeId = paraMap.get("goodsTypeId");
        if (StringUtils.isBlank(goodsTypeId)) {
            return 0;
        }
        return NumberUtils.toInt(goodsTypeId, 0);
    }

    /**
     * 检查售卖渠道，判断是否是仅直播渠道品
     */
    private boolean isOnlyLiveChannel() {
        SaleChannelAggregationDTO saleChannelAggregationDTO = getDependencyResult(ProductBaseInfoFetcher.class, ProductBaseInfo.class)
                .map(ProductBaseInfo::getSaleChannelAggregation)
                .orElse(null);
        // 如果没有获取到SaleChannelAggregation().getSupportChannels()，则代表支持全渠道
        if (saleChannelAggregationDTO == null
                || CollectionUtils.isEmpty(saleChannelAggregationDTO.getSupportChannels())) {
            return false;
        }
        List<Long> channelNos = saleChannelAggregationDTO.getSupportChannels()
                .stream()
                .map(SaleChannelDTO::getChannelNo)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(channelNos)) {
            // 直播渠道： 直播（非餐）、到综私域直播
            if (channelNos.size() == 1
                    && (10003L == channelNos.get(0) || 10017L == channelNos.get(0))) {
                return true;
            }
            if (channelNos.size() == 2
                    && channelNos.contains(10003L)
                    && channelNos.contains(10017L)) {
                return true;
            }
        }
        return false;
    }

}
