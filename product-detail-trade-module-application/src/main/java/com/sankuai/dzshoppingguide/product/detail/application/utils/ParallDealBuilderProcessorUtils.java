package com.sankuai.dzshoppingguide.product.detail.application.utils;

import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 优惠减负三期辅助工具类
 * https://km.sankuai.com/collabpage/2622579745
 */
public class ParallDealBuilderProcessorUtils {

    /**
     * 格式化优惠规则字符串，满足特定的拆分逻辑。
     * 1.优先满足价格不被拆行的要求。
     * 2.标点符号不在第一行
     *
     * @param input           输入的优惠规则字符串。
     * @param maxLines        最多显示的行数（例如：10行）。
     * @param maxCharsPerLine 每行的最大字符数（例如：5个字符）。
     * @return 格式化后的字符串列表。
     */
    public static List<String> formatToRichText(String input, int maxLines, int maxCharsPerLine) {
        if (input == null || StringUtils.isEmpty(input)) {
            return new ArrayList<>();
        }

        List<String> formattedLines = new ArrayList<>();
        StringBuilder currentLine = new StringBuilder();

        for (int index = 0; index < input.length() && formattedLines.size() < maxLines; ) {
            char currentChar = input.charAt(index);

            // 处理价格表达式
            if (Character.isDigit(currentChar)) {
                String price = extractPrice(input, index);
                index += price.length();

                if (currentLine.length() + price.length() > maxCharsPerLine) {
                    addLine(formattedLines, currentLine, maxLines);
                }
                currentLine.append(price);
                continue;
            }

            // 处理标点符号
            if (isPunctuation(currentChar)) {
                if (currentLine.length() + 1 > maxCharsPerLine) {
                    addLine(formattedLines, currentLine, maxLines);
                }
                handlePunctuation(formattedLines, currentLine, currentChar);
                index++;
                continue;
            }

            // 处理普通字符
            if (currentLine.length() + 1 > maxCharsPerLine) {
                addLine(formattedLines, currentLine, maxLines);
            }
            currentLine.append(currentChar);
            index++;
        }

        if (currentLine.length() > 0 && formattedLines.size() < maxLines) {
            formattedLines.add(currentLine.toString());
        }

        return formattedLines;
    }

    private static String extractPrice(String input, int startIndex) {
        StringBuilder priceBuilder = new StringBuilder();
        int index = startIndex;

        while (index < input.length() && (Character.isDigit(input.charAt(index)) || input.charAt(index) == '.')) {
            priceBuilder.append(input.charAt(index));
            index++;
        }

        if (index < input.length() && input.charAt(index) == '元') {
            priceBuilder.append('元');
        }

        return priceBuilder.toString();
    }

    private static void handlePunctuation(List<String> formattedLines, StringBuilder currentLine, char currentChar) {
        if (currentLine.length() == 0 && !formattedLines.isEmpty()) {
            String lastLine = formattedLines.remove(formattedLines.size() - 1);
            lastLine = lastLine + currentChar;
            formattedLines.add(lastLine);
        } else {
            currentLine.append(currentChar);
        }
    }

    private static void addLine(List<String> formattedLines, StringBuilder currentLine, int maxLines) {
        if (currentLine.length() > 0) {
            formattedLines.add(currentLine.toString());
            currentLine.setLength(0);
        }
    }

    private static boolean isPunctuation(char ch) {
        String chinesePunctuation = "，。？！；：、‘’“”·（）《》【】";
        String englishPunctuation = "!\"#$%&'()*+,-./:;<=>?@[]\\^_`{|}~";
        return chinesePunctuation.indexOf(ch) != -1 || englishPunctuation.indexOf(ch) != -1;
    }
}
