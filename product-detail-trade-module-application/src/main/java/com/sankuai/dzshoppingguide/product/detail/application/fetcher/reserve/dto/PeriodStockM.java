package com.sankuai.dzshoppingguide.product.detail.application.fetcher.reserve.dto;

import lombok.Data;

import java.util.List;

/**
 * 时段库存
 * 
 * <AUTHOR>
 * @date 2023/3/20
 */
@Data
public class PeriodStockM {
    /**
     * 库存有效期开始时间
     */
    private long beginTime;

    /**
     * 库存剩余量
     */
    private long stock;

    /**
     * 库存关联真实资源id信息
     */
    private List<String> resourceIds;

    /**
     * 库存粒度, 单位：分钟
     */
    private int stockGranularity;
}
