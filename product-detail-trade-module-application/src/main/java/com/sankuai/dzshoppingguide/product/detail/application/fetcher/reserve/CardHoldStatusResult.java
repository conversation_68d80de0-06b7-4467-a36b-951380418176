package com.sankuai.dzshoppingguide.product.detail.application.fetcher.reserve;

import com.sankuai.dzcard.navigation.api.dto.CardHoldStatusDTO;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CardHoldStatusResult extends FetcherReturnValueDTO {

    private CardHoldStatusDTO cardHoldStatusDTO;

}
