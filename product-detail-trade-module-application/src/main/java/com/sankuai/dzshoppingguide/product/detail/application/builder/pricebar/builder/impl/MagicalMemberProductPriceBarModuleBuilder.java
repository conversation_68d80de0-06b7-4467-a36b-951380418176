package com.sankuai.dzshoppingguide.product.detail.application.builder.pricebar.builder.impl;

import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.BuilderTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.pricebar.builder.AbstractProductBarModuleBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.FetcherDAGStarter;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.pricebar.vo.ProductPriceBarModuleVO;

/**
 * <AUTHOR>
 * @date 2025-03-03
 * @desc 商品详情页价格条构造器
 * 当使用factory模式时，需要有两个builder类，否则服务启动会报错
 */
@Builder(
        builderType = BuilderTypeEnum.VARIABLE_BUILDER,
        moduleKey = ModuleKeyConstants.PRICE_SALE_BAR_MODULE,
        startFetcher = FetcherDAGStarter.class,
        dependentFetchers = {
                FetcherDAGStarter.class
        }
)
public class MagicalMemberProductPriceBarModuleBuilder extends AbstractProductBarModuleBuilder<ProductPriceBarModuleVO> {

    @Override
    public ProductPriceBarModuleVO doBuild() {
        return null;
    }

}