package com.sankuai.dzshoppingguide.product.detail.application.fetcher.timesdeal.dto;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;

import java.util.Set;

/**
 * <AUTHOR>
 * @date 2025/6/13 11:45
 */
@EqualsAndHashCode(callSuper = true)
@Getter
@Builder
public class TimesDealRelation extends FetcherReturnValueDTO {
    private Set<Long> combineDealIds;
}
