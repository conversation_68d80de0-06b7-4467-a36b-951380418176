package com.sankuai.dzshoppingguide.product.detail.application.utils;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;
import com.sankuai.dzshoppingguide.product.detail.application.constants.DealAttrKeys;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * Test cases for DealAttrHelper.onlyVerificationOne method
 */
@ExtendWith(MockitoExtension.class)
public class DealAttrHelperTest {

    @Mock
    private AttrDTO mockAttrDTO;

    /**
     * Test case for null input
     */
    @Test
    public void testOnlyVerificationOne_NullInput() {
        // arrange
        List<AttrDTO> attrs = null;
        // act
        boolean result = DealAttrHelper.onlyVerificationOne(attrs);
        // assert
        assertFalse(result);
    }

    /**
     * Test case for empty list input
     */
    @Test
    public void testOnlyVerificationOne_EmptyList() {
        // arrange
        List<AttrDTO> attrs = Collections.emptyList();
        // act
        boolean result = DealAttrHelper.onlyVerificationOne(attrs);
        // assert
        assertFalse(result);
    }

    /**
     * Test case for list without the required attribute
     */
    @Test
    public void testOnlyVerificationOne_NoMatchingAttribute() {
        // arrange
        when(mockAttrDTO.getName()).thenReturn("OTHER_KEY");
        List<AttrDTO> attrs = Collections.singletonList(mockAttrDTO);
        // act
        boolean result = DealAttrHelper.onlyVerificationOne(attrs);
        // assert
        assertFalse(result);
    }

    /**
     * Test case for matching attribute with empty value list
     */
    @Test
    public void testOnlyVerificationOne_EmptyValue() {
        // arrange
        when(mockAttrDTO.getName()).thenReturn(DealAttrKeys.SINGLE_VERIFICATION_QUANTITY_DESC);
        when(mockAttrDTO.getValue()).thenReturn(Collections.emptyList());
        List<AttrDTO> attrs = Collections.singletonList(mockAttrDTO);
        // act
        boolean result = DealAttrHelper.onlyVerificationOne(attrs);
        // assert
        assertFalse(result);
    }

    /**
     * Test case for matching attribute with different value
     */
    @Test
    public void testOnlyVerificationOne_DifferentValue() {
        // arrange
        when(mockAttrDTO.getName()).thenReturn(DealAttrKeys.SINGLE_VERIFICATION_QUANTITY_DESC);
        when(mockAttrDTO.getValue()).thenReturn(Collections.singletonList("different value"));
        List<AttrDTO> attrs = Collections.singletonList(mockAttrDTO);
        // act
        boolean result = DealAttrHelper.onlyVerificationOne(attrs);
        // assert
        assertFalse(result);
    }

    /**
     * Test case for matching attribute with correct value
     */
    @Test
    public void testOnlyVerificationOne_MatchingValue() {
        // arrange
        when(mockAttrDTO.getName()).thenReturn(DealAttrKeys.SINGLE_VERIFICATION_QUANTITY_DESC);
        when(mockAttrDTO.getValue()).thenReturn(Collections.singletonList("单次到店仅可核销一次，仅能一人使用"));
        List<AttrDTO> attrs = Collections.singletonList(mockAttrDTO);
        // act
        boolean result = DealAttrHelper.onlyVerificationOne(attrs);
        // assert
        assertTrue(result);
    }

    /**
     * Test case for multiple attributes including the matching one
     */
    @Test
    public void testOnlyVerificationOne_MultipleAttributes() {
        // arrange
        AttrDTO mockAttrDTO2 = org.mockito.Mockito.mock(AttrDTO.class);
        when(mockAttrDTO.getName()).thenReturn("OTHER_KEY");
        when(mockAttrDTO2.getName()).thenReturn(DealAttrKeys.SINGLE_VERIFICATION_QUANTITY_DESC);
        when(mockAttrDTO2.getValue()).thenReturn(Collections.singletonList("单次到店仅可核销一次，仅能一人使用"));
        List<AttrDTO> attrs = Arrays.asList(mockAttrDTO, mockAttrDTO2);
        // act
        boolean result = DealAttrHelper.onlyVerificationOne(attrs);
        // assert
        assertTrue(result);
    }

    /**
     * Test case: attributeDtoList is null
     * Expected: Should return empty list
     */
    @Test
    public void testGetAttributeValuesV2_NullList() {
        // arrange
        List<AttrDTO> attributeDtoList = null;
        String key = "testKey";
        // act
        List<String> result = DealAttrHelper.getAttributeValuesV2(attributeDtoList, key);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * Test case: attributeDtoList is empty
     * Expected: Should return empty list
     */
    @Test
    public void testGetAttributeValuesV2_EmptyList() {
        // arrange
        List<AttrDTO> attributeDtoList = new ArrayList<>();
        String key = "testKey";
        // act
        List<String> result = DealAttrHelper.getAttributeValuesV2(attributeDtoList, key);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * Test case: attributeDtoList contains matching key
     * Expected: Should return the value list of matching attribute
     */
    @Test
    public void testGetAttributeValuesV2_MatchingKey() {
        // arrange
        List<String> expectedValues = Arrays.asList("value1", "value2");
        when(mockAttrDTO.getName()).thenReturn("testKey");
        when(mockAttrDTO.getValue()).thenReturn(expectedValues);
        List<AttrDTO> attributeDtoList = Collections.singletonList(mockAttrDTO);
        String key = "testKey";
        // act
        List<String> result = DealAttrHelper.getAttributeValuesV2(attributeDtoList, key);
        // assert
        assertEquals(expectedValues, result);
    }

    /**
     * Test case: attributeDtoList doesn't contain matching key
     * Expected: Should return empty list
     */
    @Test
    public void testGetAttributeValuesV2_NoMatchingKey() {
        // arrange
        when(mockAttrDTO.getName()).thenReturn("differentKey");
        List<AttrDTO> attributeDtoList = Collections.singletonList(mockAttrDTO);
        String key = "testKey";
        // act
        List<String> result = DealAttrHelper.getAttributeValuesV2(attributeDtoList, key);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * Test case: attributeDtoList contains multiple attributes
     * Expected: Should return the value list of first matching attribute
     */
    @Test
    public void testGetAttributeValuesV2_MultipleAttributes() {
        // arrange
        List<String> expectedValues = Arrays.asList("value1", "value2");
        AttrDTO mockAttr1 = org.mockito.Mockito.mock(AttrDTO.class);
        when(mockAttr1.getName()).thenReturn("testKey");
        when(mockAttr1.getValue()).thenReturn(expectedValues);
        AttrDTO mockAttr2 = org.mockito.Mockito.mock(AttrDTO.class);
        List<AttrDTO> attributeDtoList = Arrays.asList(mockAttr1, mockAttr2);
        String key = "testKey";
        // act
        List<String> result = DealAttrHelper.getAttributeValuesV2(attributeDtoList, key);
        // assert
        assertEquals(expectedValues, result);
    }

    /**
     * Test case: attributeDtoList contains attribute with null name
     * Expected: Should return empty list
     */
    @Test
    public void testGetAttributeValuesV2_NullAttributeName() {
        // arrange
        when(mockAttrDTO.getName()).thenReturn(null);
        List<AttrDTO> attributeDtoList = Collections.singletonList(mockAttrDTO);
        String key = "testKey";
        // act
        List<String> result = DealAttrHelper.getAttributeValuesV2(attributeDtoList, key);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * Test case: attributeDtoList contains attribute with null value list
     * Expected: Should return null value list when key matches
     */
    @Test
    public void testGetAttributeValuesV2_NullValueList() {
        // arrange
        when(mockAttrDTO.getName()).thenReturn("testKey");
        when(mockAttrDTO.getValue()).thenReturn(null);
        List<AttrDTO> attributeDtoList = Collections.singletonList(mockAttrDTO);
        String key = "testKey";
        // act
        List<String> result = DealAttrHelper.getAttributeValuesV2(attributeDtoList, key);
        // assert
        assertEquals(null, result);
    }

    /**
     * Test validAttr method with null attrs list
     */
    @Test
    public void testValidAttrWithNullAttrsList() {
        // arrange
        List<AttrDTO> attrs = null;
        String attrName = "testAttr";
        String attrValue = "testValue";
        // act
        boolean result = DealAttrHelper.validAttr(attrs, attrName, attrValue);
        // assert
        assertFalse(result);
    }

    /**
     * Test validAttr method with empty attrs list
     */
    @Test
    public void testValidAttrWithEmptyAttrsList() {
        // arrange
        List<AttrDTO> attrs = Collections.emptyList();
        String attrName = "testAttr";
        String attrValue = "testValue";
        // act
        boolean result = DealAttrHelper.validAttr(attrs, attrName, attrValue);
        // assert
        assertFalse(result);
    }

    /**
     * Test validAttr method with null attrValue
     */
    @Test
    public void testValidAttrWithNullAttrValue() {
        // arrange
        List<AttrDTO> attrs = new ArrayList<>();
        attrs.add(new AttrDTO());
        String attrName = "testAttr";
        String attrValue = null;
        // act
        boolean result = DealAttrHelper.validAttr(attrs, attrName, attrValue);
        // assert
        assertFalse(result);
    }

    /**
     * Test validAttr method with empty attrValue
     */
    @Test
    public void testValidAttrWithEmptyAttrValue() {
        // arrange
        List<AttrDTO> attrs = new ArrayList<>();
        attrs.add(new AttrDTO());
        String attrName = "testAttr";
        String attrValue = "";
        // act
        boolean result = DealAttrHelper.validAttr(attrs, attrName, attrValue);
        // assert
        assertFalse(result);
    }

    /**
     * Test validAttr method with attribute not found in the list
     */
    @Test
    public void testValidAttrWithAttributeNotFound() {
        // arrange
        List<AttrDTO> attrs = new ArrayList<>();
        AttrDTO attr = new AttrDTO();
        attr.setName("otherAttr");
        attr.setValue(Collections.singletonList("otherValue"));
        attrs.add(attr);
        String attrName = "testAttr";
        String attrValue = "testValue";
        // act
        boolean result = DealAttrHelper.validAttr(attrs, attrName, attrValue);
        // assert
        assertFalse(result);
    }

    /**
     * Test validAttr method with attribute found but empty value
     */
    @Test
    public void testValidAttrWithAttributeFoundEmptyValue() {
        // arrange
        List<AttrDTO> attrs = new ArrayList<>();
        AttrDTO attr = new AttrDTO();
        attr.setName("testAttr");
        attr.setValue(Collections.emptyList());
        attrs.add(attr);
        String attrName = "testAttr";
        String attrValue = "testValue";
        // act
        boolean result = DealAttrHelper.validAttr(attrs, attrName, attrValue);
        // assert
        assertFalse(result);
    }

    /**
     * Test validAttr method with attribute found and matching value
     */
    @Test
    public void testValidAttrWithAttributeFoundMatchingValue() {
        // arrange
        List<AttrDTO> attrs = new ArrayList<>();
        AttrDTO attr = new AttrDTO();
        attr.setName("testAttr");
        attr.setValue(Collections.singletonList("testValue"));
        attrs.add(attr);
        String attrName = "testAttr";
        String attrValue = "testValue";
        // act
        boolean result = DealAttrHelper.validAttr(attrs, attrName, attrValue);
        // assert
        assertTrue(result);
    }

    /**
     * Test validAttr method with attribute found but non-matching value
     */
    @Test
    public void testValidAttrWithAttributeFoundNonMatchingValue() {
        // arrange
        List<AttrDTO> attrs = new ArrayList<>();
        AttrDTO attr = new AttrDTO();
        attr.setName("testAttr");
        attr.setValue(Collections.singletonList("otherValue"));
        attrs.add(attr);
        String attrName = "testAttr";
        String attrValue = "testValue";
        // act
        boolean result = DealAttrHelper.validAttr(attrs, attrName, attrValue);
        // assert
        assertFalse(result);
    }

    /**
     * Test validAttr method with multiple attributes in the list
     */
    @Test
    public void testValidAttrWithMultipleAttributes() {
        // arrange
        List<AttrDTO> attrs = new ArrayList<>();
        AttrDTO attr1 = new AttrDTO();
        attr1.setName("attr1");
        attr1.setValue(Collections.singletonList("value1"));
        attrs.add(attr1);
        AttrDTO attr2 = new AttrDTO();
        attr2.setName("attr2");
        attr2.setValue(Collections.singletonList("value2"));
        attrs.add(attr2);
        String attrName = "attr2";
        String attrValue = "value2";
        // act
        boolean result = DealAttrHelper.validAttr(attrs, attrName, attrValue);
        // assert
        assertTrue(result);
    }

    /**
     * Test validAttr method with attribute having multiple values
     */
    @Test
    public void testValidAttrWithMultipleAttributeValues() {
        // arrange
        List<AttrDTO> attrs = new ArrayList<>();
        AttrDTO attr = new AttrDTO();
        attr.setName("testAttr");
        attr.setValue(Arrays.asList("value1", "value2", "value3"));
        attrs.add(attr);
        String attrName = "testAttr";
        String attrValue = "value1";
        // act
        boolean result = DealAttrHelper.validAttr(attrs, attrName, attrValue);
        // assert
        assertTrue(result);
    }
}
