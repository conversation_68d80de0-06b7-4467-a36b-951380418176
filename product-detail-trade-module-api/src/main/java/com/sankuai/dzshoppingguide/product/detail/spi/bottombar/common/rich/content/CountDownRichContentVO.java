package com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.rich.content;

import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.rich.content.enums.RichContentTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.rich.content.enums.TextStyleEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * @Author: guangyujie
 * @Date: 2025/3/16 15:15
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class CountDownRichContentVO extends RichContentVO {

    /**
     * 倒计时时间戳
     */
    private long endTime;

    /**
     * Bold=加粗，Default=默认
     */
    private String textStyle;

    private int textSize;

    private String textColor;

    public CountDownRichContentVO(long endTime, TextStyleEnum textStyle, int textSize, String textColor) {
        this.endTime = endTime;
        this.textStyle = textStyle.name();
        this.textSize = textSize / 2;
        this.textColor = textColor;
    }

    @Override
    public int getType() {
        return RichContentTypeEnum.COUNT_DOWN.getCode();
    }

}
