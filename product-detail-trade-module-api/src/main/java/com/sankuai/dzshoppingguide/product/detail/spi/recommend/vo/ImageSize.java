package com.sankuai.dzshoppingguide.product.detail.spi.recommend.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: zhangyuan103
 * @Date: 2025/5/19
 */
@Data
@Builder
@TypeDoc(description = "图片尺寸")
@MobileDo(id = 0xb4c5)
public class ImageSize implements Serializable {
    @FieldDoc(description = "图片高度")
    @MobileDo.MobileField(key = 0x261f)
    private int height;

    @FieldDoc(description = "图片宽度")
    @MobileDo.MobileField(key = 0x2b78)
    private int width;

}
