package com.sankuai.dzshoppingguide.product.detail.application.utils.enums;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.dianping.cat.Cat;
import com.sankuai.dzshoppingguide.product.detail.application.utils.GsonUtils;
import java.util.HashMap;
import java.util.Map;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * Unit tests for DealParamEnum.codeOf method
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("DealParamEnum CodeOf Tests")
public class DealParamEnumTest {

    /**
     * Test scenario: Successfully retrieve MLIVE_INFO enum when providing valid code 1
     * Expected: Returns MLIVE_INFO enum instance
     */
    @Test
    @DisplayName("Should return MLIVE_INFO when code is 1")
    public void testCodeOf_WhenCodeIsValid_ReturnMLiveInfo() throws Throwable {
        // arrange
        int validCode = 1;
        // act
        DealParamEnum result = DealParamEnum.codeOf(validCode);
        // assert
        assertEquals(DealParamEnum.MLIVE_INFO, result);
    }

    /**
     * Test scenario: Attempt to retrieve enum with non-existent code
     * Expected: Throws UnsupportedOperationException with appropriate message
     */
    @Test
    @DisplayName("Should throw exception when code is invalid")
    public void testCodeOf_WhenCodeIsInvalid_ThrowException() throws Throwable {
        // arrange
        int invalidCode = 999;
        // act & assert
        UnsupportedOperationException exception = assertThrows(UnsupportedOperationException.class, () -> DealParamEnum.codeOf(invalidCode));
        assertEquals("DealParamEnum has no code of " + invalidCode, exception.getMessage());
    }

    /**
     * Test scenario: Attempt to retrieve enum with zero code
     * Expected: Throws UnsupportedOperationException with appropriate message
     */
    @Test
    @DisplayName("Should throw exception when code is zero")
    public void testCodeOf_WhenCodeIsZero_ThrowException() throws Throwable {
        // arrange
        int zeroCode = 0;
        // act & assert
        UnsupportedOperationException exception = assertThrows(UnsupportedOperationException.class, () -> DealParamEnum.codeOf(zeroCode));
        assertEquals("DealParamEnum has no code of " + zeroCode, exception.getMessage());
    }

    /**
     * Test scenario: Attempt to retrieve enum with negative code
     * Expected: Throws UnsupportedOperationException with appropriate message
     */
    @Test
    @DisplayName("Should throw exception when code is negative")
    public void testCodeOf_WhenCodeIsNegative_ThrowException() throws Throwable {
        // arrange
        int negativeCode = -1;
        // act & assert
        UnsupportedOperationException exception = assertThrows(UnsupportedOperationException.class, () -> DealParamEnum.codeOf(negativeCode));
        assertEquals("DealParamEnum has no code of " + negativeCode, exception.getMessage());
    }

    /**
     * Test case for null dealParam
     */
    @Test
    public void testIsMLiveSourceWithNullDealParam() throws Throwable {
        // arrange
        String dealParam = null;
        // act
        boolean result = DealParamEnum.isMLiveSource(dealParam);
        // assert
        assertFalse(result);
    }

    /**
     * Test case for empty dealParam
     */
    @Test
    public void testIsMLiveSourceWithEmptyDealParam() throws Throwable {
        // arrange
        String dealParam = "";
        // act
        boolean result = DealParamEnum.isMLiveSource(dealParam);
        // assert
        assertFalse(result);
    }

    /**
     * Test case for valid dealParam with mlive source
     */
    @Test
    public void testIsMLiveSourceWithValidMLiveSource() throws Throwable {
        // arrange
        String dealParam = "{\"MLIVE_INFO\":\"{\\\"mlivesource\\\":\\\"mlive\\\"}\"}";
        // act
        boolean result = DealParamEnum.isMLiveSource(dealParam);
        // assert
        assertTrue(result);
    }

    /**
     * Test case for valid dealParam without mlive source
     */
    @Test
    public void testIsMLiveSourceWithValidNonMLiveSource() throws Throwable {
        // arrange
        String dealParam = "{\"MLIVE_INFO\":\"{\\\"mlivesource\\\":\\\"other\\\"}\"}";
        // act
        boolean result = DealParamEnum.isMLiveSource(dealParam);
        // assert
        assertFalse(result);
    }

    /**
     * Test case for dealParam without MLIVE_INFO
     */
    @Test
    public void testIsMLiveSourceWithoutMLiveInfo() throws Throwable {
        // arrange
        String dealParam = "{\"OTHER_INFO\":\"some value\"}";
        // act
        boolean result = DealParamEnum.isMLiveSource(dealParam);
        // assert
        assertFalse(result);
    }

    /**
     * Test case for exception handling
     */
    @Test
    public void testIsMLiveSourceWithException() throws Throwable {
        // arrange
        String dealParam = "invalid json";
        // act
        boolean result = DealParamEnum.isMLiveSource(dealParam);
        // assert
        assertFalse(result);
    }
}
