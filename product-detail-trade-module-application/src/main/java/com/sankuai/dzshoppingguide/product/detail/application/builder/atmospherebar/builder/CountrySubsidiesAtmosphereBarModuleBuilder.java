package com.sankuai.dzshoppingguide.product.detail.application.builder.atmospherebar.builder;

import com.dianping.cat.Cat;
import com.dianping.gmkt.scene.api.delivery.dto.res.DealGroupPromotionDTO;
import com.dianping.gmkt.scene.api.delivery.dto.res.ResourceExposureResponseDTO;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.BuilderTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.pricebar.builder.AbstractProductBarModuleBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.atmosphere.ProductAtmosphereFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.atmosphere.ProductAtmosphereReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.category.ProductCategoryFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.ProductNormalPriceFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.ProductPromoPriceFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.dto.ProductPriceReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.attr.SkuAttr;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.attr.SkuAttrFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.sale.ProductSaleFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.sale.ProductSaleReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.FetcherDAGStarter;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.statesubsidies.CountrySubsidiesQualificationDTO;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.statesubsidies.CountrySubsidiesQualificationFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.tag.guarantee.ProductGuaranteeTagInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.tag.guarantee.SkuGuaranteeTagFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.model.CountrySubsidiesConfig;
import com.sankuai.dzshoppingguide.product.detail.application.utils.LionConfigUtils;
import com.sankuai.dzshoppingguide.product.detail.application.utils.PriceUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.atmospherebar.vo.ProductAtmosphereModuleVO;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.AtmosphereInfoDetailVO;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DetailPriceVO;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DetailSaleVO;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.nib.price.operation.api.guarantee.common.dto.TagDTO;
import com.sankuai.statesubsidies.c.thrift.constant.QualificationStatusEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025-03-10
 * @desc 国家补贴氛围条
 */
@Builder(
        builderType = BuilderTypeEnum.VARIABLE_BUILDER,
        moduleKey = ModuleKeyConstants.ATMOSPHERE_PRICE_SALE_BAR_MODULE,
        startFetcher = FetcherDAGStarter.class,
        dependentFetchers = {
                ProductAtmosphereFetcher.class,
                ProductNormalPriceFetcher.class,
                ProductSaleFetcher.class,
                ProductPromoPriceFetcher.class,
                ProductCategoryFetcher.class,
                ProductBaseInfoFetcher.class,
                CountrySubsidiesQualificationFetcher.class,
                SkuGuaranteeTagFetcher.class,
                SkuAttrFetcher.class
        }
)
public class CountrySubsidiesAtmosphereBarModuleBuilder extends AbstractProductBarModuleBuilder<ProductAtmosphereModuleVO> {

    @Override
    public ProductAtmosphereModuleVO doBuild() {
        boolean subsidiesCity = isCountrySubsidiesCity(request.getGpsCityId(), request.getClientTypeEnum().isMtClientType());
        // 非国补定位城市，不展示氛围
        if (!subsidiesCity) {
            return null;
        }
        ProductPriceReturnValue normalPriceReturnValue = getDependencyResult(ProductNormalPriceFetcher.class);
        ProductPriceReturnValue promoPriceReturnValue = getDependencyResult(ProductPromoPriceFetcher.class);
        ProductSaleReturnValue productSaleReturnValue = getDependencyResult(ProductSaleFetcher.class);
        ProductAtmosphereReturnValue productAtmosphereReturnValue = getDependencyResult(ProductAtmosphereFetcher.class);
        ProductCategory productCategory = getDependencyResult(ProductCategoryFetcher.class);
        ProductBaseInfo productBaseInfo = getDependencyResult(ProductBaseInfoFetcher.class);
        CountrySubsidiesQualificationDTO subsidiesQualificationDTO = getDependencyResult(CountrySubsidiesQualificationFetcher.class);
        ProductGuaranteeTagInfo productGuaranteeTagInfo = getDependencyResult(SkuGuaranteeTagFetcher.class);
        SkuAttr skuAttr = getDependencyResult(SkuAttrFetcher.class);
        TagDTO stateSubsidiesTag = productGuaranteeTagInfo.getStateSubsidies();
        BigDecimal stateSubsidiesPrice = getStateSubsidiesPrice(normalPriceReturnValue);
        // 没有国补标签、国补价、国补资格时，不展示国补氛围
        if (Objects.isNull(stateSubsidiesTag) || Objects.isNull(subsidiesQualificationDTO)
                || Objects.isNull(stateSubsidiesPrice) || stateSubsidiesPrice.compareTo(BigDecimal.ZERO) <= 0) {
            return null;
        }
        ResourceExposureResponseDTO exposureResource = productAtmosphereReturnValue.getResourceExposureResponse();
        if (Objects.isNull(exposureResource) || CollectionUtils.isEmpty(exposureResource.getMaterialsDTOList())
                || Objects.isNull(exposureResource.getMaterialsDTOList().get(0))
                || Objects.isNull(exposureResource.getMaterialsDTOList().get(0).getDealGroupPromotionDTO())) {
            return null;
        }
        DealGroupPromotionDTO dealGroupPromotionDTO = exposureResource.getMaterialsDTOList().get(0).getDealGroupPromotionDTO();
        ProductAtmosphereModuleVO result = new ProductAtmosphereModuleVO();
        // 氛围条
        AtmosphereInfoDetailVO atmosphereDetailVO = buildAtmosphereDetailVO(dealGroupPromotionDTO, exposureResource.getActivityId(), subsidiesQualificationDTO);
        result.setAtmosphere(atmosphereDetailVO);
        // 销量
        DetailSaleVO saleVO = buildSaleInfo(productSaleReturnValue);
        result.setSale(saleVO);
        // 价格
        ProductPriceParam priceParam = ProductPriceParam.builder()
                .normalPriceReturnValue(normalPriceReturnValue)
                .dealPromoPriceReturnValue(promoPriceReturnValue)
                .productCategory(productCategory)
                .productBaseInfo(productBaseInfo)
                .subsidiesQualificationDTO(subsidiesQualificationDTO)
                .isAtmosphere(result.getAtmosphere() != null)
                .skuAttr(skuAttr)
                .build();
        DetailPriceVO priceVO = buildPriceVO(priceParam);
        if (Objects.isNull(priceVO)) {
            return null;
        }

        QualificationStatusEnum qualificationStatusEnum = Optional.of(subsidiesQualificationDTO)
                .map(CountrySubsidiesQualificationDTO::getQualificationStatusEnum)
                .orElse(QualificationStatusEnum.UNBIND);
        // 价格营销点、折扣标签样式
        putPriceStyle(priceVO, saleVO, normalPriceReturnValue.getPriceDisplayDTO(), qualificationStatusEnum, dealGroupPromotionDTO);
        result.setPrice(priceVO);
        Cat.logMetricForCount("CountrySubsidiesProductHasAtmosphere");
        return result;
    }

    private boolean isCountrySubsidiesCity(Integer gpsCityId, boolean isMt) {
        CountrySubsidiesConfig config = LionConfigUtils.getCountrySubsidiesConfig();

        if (Objects.isNull(gpsCityId) || Objects.isNull(config)) {
            return false;
        }
        
        return isMt ? 
            isValidCityInList(gpsCityId, config.getMtCityIds()) :
            isValidCityInList(gpsCityId, config.getDpCityIds());
    }
    
    private boolean isValidCityInList(Integer cityId, List<Integer> cityList) {
        return CollectionUtils.isNotEmpty(cityList) && cityList.contains(cityId);
    }

    private BigDecimal getStateSubsidiesPrice(ProductPriceReturnValue normalPriceReturnValue) {
        if (Objects.isNull(normalPriceReturnValue) || Objects.isNull(normalPriceReturnValue.getPriceDisplayDTO())) {
            return null;
        }
        return normalPriceReturnValue.getPriceDisplayDTO().getStateSubsidiesPrice();
    }

    private void putPriceStyle(DetailPriceVO priceVO, DetailSaleVO saleVO, PriceDisplayDTO normalPriceDisplayDTO,
                               QualificationStatusEnum qualificationStatusEnum, DealGroupPromotionDTO dealGroupPromotionDTO) {
        // 如果不是国补氛围，则不展示国补特有数据
        if (!Objects.equals(dealGroupPromotionDTO.getType(), 102)) {
            return;
        }
        // 已领取
        BigDecimal stateSubsidiesPrice = normalPriceDisplayDTO.getStateSubsidiesPrice();
        if (qualificationStatusEnum == QualificationStatusEnum.BIND) {
            // 已领取国补资格，展示国补价、折扣、门市价
            String finalPrice = PriceUtils.buildFinalPrice(stateSubsidiesPrice);
            priceVO.setFinalPrice(finalPrice);
            priceVO.setMarketPrice(PriceUtils.buildMarketPrice(normalPriceDisplayDTO.getMarketPrice()));
            BigDecimal discount = calcDiscount(normalPriceDisplayDTO.getPrice(), normalPriceDisplayDTO.getMarketPrice());
            if (Objects.nonNull(discount)) {
                priceVO.setDiscountTag(discount.toPlainString() + "折");
                // 折扣标签样式
                priceVO.setDiscountTagTextColor("#00A72D");
                priceVO.setDiscountTagBackgroundColor("#FFFFFF");
            }
            // 不展示销量
            saleVO.setSaleTag("国补价");
            return;
        }
        // 未领取
        priceVO.setDiscountTag(String.format("国补价¥%s", stateSubsidiesPrice));
        priceVO.setMarketPrice(StringUtils.EMPTY);
        // 折扣标签样式
        priceVO.setDiscountTagTextColor("#00A72D");
        priceVO.setDiscountTagBackgroundColor("#FFFFFF");
    }

    private AtmosphereInfoDetailVO buildAtmosphereDetailVO(DealGroupPromotionDTO dealGroupPromotionDTO, Long activityId, CountrySubsidiesQualificationDTO qualificationDTO) {
        AtmosphereInfoDetailVO atmosphereDetailVO = new AtmosphereInfoDetailVO();
        // 氛围条背景图
        atmosphereDetailVO.setBackgroundImage(dealGroupPromotionDTO.getPicUrl());
        // 氛围条活动ID
        atmosphereDetailVO.setActivityId(String.valueOf(activityId));
        // 氛围条类型
        atmosphereDetailVO.setType(dealGroupPromotionDTO.getType());
        // 如果不是国补氛围，则不展示国补特有数据
        if (!Objects.equals(dealGroupPromotionDTO.getType(), 102)) {
            return atmosphereDetailVO;
        }
        String subTitle = "";
        if (Objects.nonNull(qualificationDTO)) {
            QualificationStatusEnum qualificationStatusEnum = qualificationDTO.getQualificationStatusEnum();
            if (qualificationStatusEnum == QualificationStatusEnum.UNBIND) {
                subTitle = "去领取";
            } else {
                subTitle = "已领取";
            }
        }
        // 氛围副标题
        atmosphereDetailVO.setSubTitle(subTitle);
        atmosphereDetailVO.setSubTitleColor("#FFFFFF");
        // 跳转链接
        atmosphereDetailVO.setClickUrl(buildStateSubsidiesUrl());
        return atmosphereDetailVO;
    }

    // 构造国家补贴会场跳链
    private String buildStateSubsidiesUrl() {
        Map<String, String> stateSubsidiesUrlMap = LionConfigUtils.getCountrySubsidiesUrl();
        if (request.getClientTypeEnum().isMtClientType()) {
            return stateSubsidiesUrlMap.get("mt");
        }
        return stateSubsidiesUrlMap.get("dp");
    }
}
