package com.sankuai.dzshoppingguide.product.detail.spi.dtos;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: <EMAIL>
 * @Date: 2025/2/10
 */
@Data
@TypeDoc(description = "分享模块小程序配置")
@MobileDo(id = 0xcd9b)
public class ShareModuleMiniProgramConfig implements Serializable {
//    @FieldDoc(description = "小程序图片链接")
//    @MobileDo.MobileField(key = 0xaca1)
//    private String image;

    @FieldDoc(description = "小程序路径")
    @MobileDo.MobileField(key = 0x6411)
    private String path;

    @FieldDoc(description = "小程序id")
    @MobileDo.MobileField(key = 0x9b96)
    private String miniProgramId;
}
