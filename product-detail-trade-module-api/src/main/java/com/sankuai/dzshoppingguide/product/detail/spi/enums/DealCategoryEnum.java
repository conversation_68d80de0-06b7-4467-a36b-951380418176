package com.sankuai.dzshoppingguide.product.detail.spi.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @author: created by hang.yu on 2023/10/10 16:54
 */
@Getter
@AllArgsConstructor
public enum DealCategoryEnum {

    UNKNOWN(0L, "UNKNOWN"),
    /**
     * 足浴
     */
    MASSAGE(303L, "massageCategoryStrategyImpl"),

    /**
     * 洗浴
     */
    BATH(304L, "bathCategoryStrategyImpl"),

    /**
     * ktv
     */
    KTV(301L, "ktvCategoryStrategyImpl"),

    /**
     * 教育-考研
     */
    EDU_1210(1210L, "eduCategoryStrategyImpl"),
    /**
     * 教育-考公考编
     */
    EDU_1205(1205L, "eduCategoryStrategyImpl"),
    /**
     * 教育-夜校
     */
    EDU_1226(1226L, "eduCategoryStrategyImpl"),
    /**
     * 电动车
     */
    ELECTRIC_VEHICLE(716L, "electricVehicleCategoryStrategy"),

    DENTISTRY(506L, "dentistryCategoryStrategyImpl"),

    /**
     * 造型彩妆
     */
    MAKEUP_STYLE(908L, "makeupStyleCategoryStrategyImpl"),

    /**
     * 民族服饰
     */
    ETHNIC_CLOTHING_PHOTO(910L, "photoCategoryStrategyImpl"),
    /**
     * 个性写真
     */
    PERSONALIZED_PHOTO(504L, "photoCategoryStrategyImpl"),
    /**
     * 证件照
     */
    CERTIFICATE_PHOTO(2001L, "photoCategoryStrategyImpl"),
    /**
     * 孕婴童摄影
     */
    BABY_PHOTO(1004L, "photoCategoryStrategyImpl"),
    /**
     * 形象照
     */
    PORTRAIT_PHOTO(2003L, "photoCategoryStrategyImpl"),
    /**
     * 团体照
     */
    GROUP_PHOTO(2005L, "photoCategoryStrategyImpl"),
    /**
     * 自拍
     */
    SELF_PHOTO(921L, "photoCategoryStrategyImpl"),
    /**
     * 其他摄影
     */
    OTHER_PHOTO(2006L, "photoCategoryStrategyImpl"),
    /**
     * 商业摄影
     */
    COMMERCIAL_PHOTO(456L, "photoCategoryStrategyImpl"),
    ;

    private final Long dealCategoryId;

    private final String strategyName;


    public static DealCategoryEnum getEnumByDealCategoryId(Long dealCategoryId) {
        for (DealCategoryEnum value : DealCategoryEnum.values()) {
            if (value.getDealCategoryId().equals(dealCategoryId)) {
                return value;
            }
        }
        return null;
    }

}
