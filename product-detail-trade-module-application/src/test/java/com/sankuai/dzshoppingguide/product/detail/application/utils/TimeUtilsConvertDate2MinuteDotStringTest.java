package com.sankuai.dzshoppingguide.product.detail.application.utils;

import static org.junit.jupiter.api.Assertions.*;
import java.text.SimpleDateFormat;
import java.util.Date;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class TimeUtilsConvertDate2MinuteDotStringTest {

    private static final Logger log = LoggerFactory.getLogger(TimeUtilsConvertDate2MinuteDotStringTest.class);

    /**
     * Tests the convertDate2MinuteDotString method when date is null, it should return null.
     */
    @Test
    public void testConvertDate2MinuteDotStringWhenDateIsNull() throws Throwable {
        // arrange
        Date date = null;
        // act
        String result = TimeUtils.convertDate2MinuteDotString(date);
        // assert
        assertNull(result);
    }
}
