package com.sankuai.dzshoppingguide.product.detail.application.fetcher.dealproduct;

import com.dianping.tuangu.dztg.usercenter.api.enums.CreateOrderPageSourceEnum;
import com.dianping.vc.enums.VCClientTypeEnum;
import com.dianping.vc.enums.VCPlatformEnum;
import com.google.common.collect.Maps;
import com.sankuai.dealuser.price.display.api.enums.RequestSceneEnum;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.best.shop.ShopInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.best.shop.ShopInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.sku.DealSkuSummaryFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.sku.DealSkuSummaryReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
import com.sankuai.dztheme.deal.req.DealProductRequest;
import com.sankuai.dztheme.deal.res.DealSkuSummaryDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;


/**
 * <AUTHOR>
 * @create 2025/5/28 11:35
 */
@Fetcher(
        previousLayerDependencies = {
                ShopIdMapperFetcher.class,
                ShopInfoFetcher.class,
                DealSkuSummaryFetcher.class})
@Slf4j
public class DealProductFetcher extends NormalFetcherContext<DealProductReturnValue> {
    /**
     * 页面来源-购物车未选中
     */
    private static final String PAGE_SOURCE_SHOP_CAR_NO_SELECT = "shopcarnoselect";

    /**
     * 页面来源-团详
     */
    private static final String PAGE_SOURCE_DEAL = "deal";

    private static final String PAGE_SOURCE_SHOP_CAR_SELECT = "shopcarselect";
    /**
     * 综团比价-购物车未选中&团详场景
     */
    private static final String DZ_TINY_INFO_DEAL_PLAN_ID = "10002451";

    /**
     * 综团比价-购物车选中场景
     */
    private static final String DZ_TINY_INFO_SHOP_CAR_SELECT_PLAN_ID = "10002465";

    @Autowired
    private CompositeAtomService compositeAtomService;

    private DealSkuSummaryReturnValue dealSkuSummaryReturnValue = null;
    protected ShopIdMapper shopIdMapper = null;
    protected ShopInfo shopInfo = null;
    @Override
    protected CompletableFuture<DealProductReturnValue> doFetch() throws Exception {
        try{
            dealSkuSummaryReturnValue = getDependencyResult(DealSkuSummaryFetcher.class);
            shopInfo = getDependencyResult(ShopInfoFetcher.class);
            shopIdMapper =  getDependencyResult(ShopIdMapperFetcher.class);

            int skuId = getSkuId(dealSkuSummaryReturnValue);
            String shopUuid = shopInfo.getDpPoiDTO().getUuid();
            long shopId = request.getClientTypeEnum().isMtClientType() ? shopIdMapper.getMtBestShopId() : shopIdMapper.getDpBestShopId();

            return compositeAtomService.queryDealProductTheme(buildRequest(skuId, shopId, shopUuid)).thenApply(res->{
                if (Objects.nonNull(res)) {
                    DealProductReturnValue dealProductReturnValue = new DealProductReturnValue();
                    dealProductReturnValue.setDealProductResult(res);
                    return dealProductReturnValue;
                }
                return null;
            });
        }catch (Exception e){
            log.error("DealProductFetcher.doFetch error:", e);
        }
        return CompletableFuture.completedFuture(null);
    }

    private DealProductRequest buildRequest(int skuId, long shopId, String shopUuid) {
        boolean isMt = request.getClientTypeEnum().isMtClientType();
        DealProductRequest dealProductRequest = new DealProductRequest();
        List<Integer> dealIds = Collections.singletonList(Math.toIntExact(request.getProductId()));
        dealProductRequest.setProductIds(dealIds);

        if (Objects.equals(request.getPageSource(), PAGE_SOURCE_SHOP_CAR_SELECT)) {
            dealProductRequest.setPlanId(DZ_TINY_INFO_SHOP_CAR_SELECT_PLAN_ID);
        }else {
            dealProductRequest.setPlanId(DZ_TINY_INFO_DEAL_PLAN_ID);
        }
        // 扩展参数
        Map<String, Object> extParams = Maps.newHashMap();
        extParams.put("dealIds", dealIds);
        extParams.put("clientType", isMt ? VCClientTypeEnum.MT_APP.getCode() : VCClientTypeEnum.DP_APP.getCode());
        extParams.put("platform", isMt ? VCPlatformEnum.MT.getType() : VCPlatformEnum.DP.getType());
        extParams.put("deviceId", request.getShepherdGatewayParam().getDeviceId());
        extParams.put("unionId", request.getShepherdGatewayParam().getUnionid());
        extParams.put("shopUuid", shopUuid);
        extParams.put("userId", request.getUserId());//已确认判断平台后再使用
        extParams.put("shopId", (int)shopId);
        extParams.put("shopIdForLong", shopId);
        extParams.put("appVersion", request.getShepherdGatewayParam().getAppVersion());
        Map<Integer, Integer> dealId2ShopIdMap = Maps.newHashMap();
        Map<Integer, Long> dealId2ShopIdForLong = Maps.newHashMap();
        dealId2ShopIdMap.put((int) request.getProductId(), (int)shopId);
        dealId2ShopIdForLong.put((int) request.getProductId(), shopId);
        extParams.put("dealId2ShopId", dealId2ShopIdMap);
        extParams.put("dealId2ShopIdForLong",dealId2ShopIdForLong);
        Map<Integer, Integer> dealId2SkuIdMap = Maps.newHashMap();
        dealId2SkuIdMap.put(Math.toIntExact(request.getProductId()), skuId);
        extParams.put("dealId2SkuId", dealId2SkuIdMap);
        extParams.put("scene", "comparePopup");
        extParams.put("needPriceTrend", false);
        extParams.put("cityId", request.getCityId());
        extParams.put("lat", request.getUserLat());
        extParams.put("lng", request.getUserLng());
        extParams.put("coordType", "GCJ02");
        // 查询团详提单页需要此参数，与方法入参pageSource无关
        extParams.put("pageSource", CreateOrderPageSourceEnum.DEAL_GROUP_DETAIL.getType());
        if (Objects.equals(request.getPageSource(), PAGE_SOURCE_DEAL)) {
            extParams.put("directPromoSceneCode", RequestSceneEnum.PROMO_DETAIL_DESC_WithDealPromo.getScene());
        } else if (Objects.equals(request.getPageSource(), PAGE_SOURCE_SHOP_CAR_NO_SELECT)) {
            extParams.put("directPromoSceneCode", RequestSceneEnum.BUY_CAR_SINGLE_PRODUCT.getScene());
        }
        dealProductRequest.setExtParams(extParams);
        return dealProductRequest;
    }

    private int getSkuId(DealSkuSummaryReturnValue dealSkuSummaryReturnValue){
        if (Objects.isNull(dealSkuSummaryReturnValue)) {
            return 0;
        }
        DealSkuSummaryDTO dealSkuSummaryDTO = dealSkuSummaryReturnValue.getDealSkuSummaryDTO();
        int skuId = Objects.nonNull(dealSkuSummaryDTO) && Objects.nonNull(dealSkuSummaryDTO.getDefaultSkuId())  ? Math.toIntExact(dealSkuSummaryDTO.getDefaultSkuId()) : 0;
        return skuId;
    }

}
