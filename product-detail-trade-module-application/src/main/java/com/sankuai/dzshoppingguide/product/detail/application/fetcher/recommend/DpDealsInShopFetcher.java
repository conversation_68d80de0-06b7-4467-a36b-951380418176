package com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommend;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.shopid.ShopIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.idmapper.shopid.ShopIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommend.dto.DpDealsInShopReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.FetcherDAGStarter;
import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;

import javax.annotation.Resource;
import java.util.concurrent.CompletableFuture;

/**
 * @Author: zhangyuan103
 * @Date: 2025/5/19
 * @Description: 根据点评门店id获取点评团单id列表
 */
@Fetcher(previousLayerDependencies = {FetcherDAGStarter.class, ShopIdMapperFetcher.class})
@Slf4j
public class DpDealsInShopFetcher extends NormalFetcherContext<DpDealsInShopReturnValue> {
    @Resource
    private CompositeAtomService compositeAtomService;

    @Override
    protected CompletableFuture<DpDealsInShopReturnValue> doFetch() throws Exception {
        boolean isMt = request.getClientTypeEnum().isMtClientType();
        ShopIdMapper shopIdMapper = getDependencyResult(ShopIdMapperFetcher.class);
        long dpShopId = shopIdMapper.getDpBestShopId();
        return compositeAtomService.batchQuerySaleDealGroupId(dpShopId, isMt).thenApply((shopId2DealIdsMap) -> MapUtils.isEmpty(shopId2DealIdsMap)
                ? null : new DpDealsInShopReturnValue(shopId2DealIdsMap.entrySet().iterator().next().getValue()));
    }
}
