package com.sankuai.dzshoppingguide.product.detail.application.service.promo;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.dianping.tgc.open.entity.PromoCouponButton;
import com.dianping.tgc.open.entity.PromoCouponInfo;
import com.google.common.collect.Lists;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import com.sankuai.dzshoppingguide.product.detail.application.builder.promodetail.dto.PromotionDetailParams;
import com.sankuai.dzshoppingguide.product.detail.application.builder.promodetail.enums.CouponStatusEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.promodetail.enums.CouponStyleEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.promodetail.enums.PromotionTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.promo.vo.CouponModule;
import com.sankuai.dzshoppingguide.product.detail.spi.promo.vo.DealGift;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class PromoDetailServiceBuildNormalCoupon1Test {

    @InjectMocks
    private PromoDetailService service;

    @Mock
    private PromoDetailModuleBuilderService promoDetailModuleBuilderService;

    private PromotionDetailParams params;

    private DealGift dealGift;

    @BeforeEach
    void setUp() {
        params = new PromotionDetailParams();
        dealGift = new DealGift();
    }

    /**
     * Test when input coupon list is empty
     */
    @Test
    public void testBuildNormalCouponEmptyCouponList() throws Throwable {
        // arrange
        PriceDisplayDTO priceDisplay = new PriceDisplayDTO();
        // act
        List<CouponModule> result = service.buildNormalCoupon(Collections.emptyList(), priceDisplay);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * Test when all coupons are already assigned (status = 1)
     */
    @Test
    public void testBuildNormalCouponAllAssignedCoupons() throws Throwable {
        // arrange
        PriceDisplayDTO priceDisplay = new PriceDisplayDTO();
        PromoCouponInfo coupon1 = new PromoCouponInfo();
        // ASSIGNED
        coupon1.setStatus(1);
        coupon1.setCouponGroupId(1L);
        coupon1.setCouponType(0);
        coupon1.setTitle("Test Coupon 1");
        PromoCouponInfo coupon2 = new PromoCouponInfo();
        // ASSIGNED
        coupon2.setStatus(1);
        coupon2.setCouponGroupId(2L);
        coupon2.setCouponType(0);
        coupon2.setTitle("Test Coupon 2");
        // act
        List<CouponModule> result = service.buildNormalCoupon(Arrays.asList(coupon1, coupon2), priceDisplay);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * Test when coupon has jump URL action
     */
    @Test
    public void testBuildNormalCouponWithJumpUrlAction() throws Throwable {
        // arrange
        PriceDisplayDTO priceDisplay = new PriceDisplayDTO();
        PromoCouponButton button = new PromoCouponButton();
        button.setActionType(1);
        button.setClickUrl("http://test.com");
        button.setTitle("Click Me");
        PromoCouponInfo coupon = new PromoCouponInfo();
        coupon.setStatus(0);
        coupon.setCouponGroupId(1L);
        coupon.setCouponType(0);
        coupon.setTitle("Test Coupon");
        coupon.setPromoCouponButton(button);
        // act
        List<CouponModule> result = service.buildNormalCoupon(Collections.singletonList(coupon), priceDisplay);
        // assert
        assertEquals(1, result.size());
        assertEquals(CouponStatusEnum.JUMP_URL.getCode(), result.get(0).getCouponStatus());
        assertEquals("Click Me", result.get(0).getActionButton().getButtonText());
        assertEquals("http://test.com", result.get(0).getActionButton().getJumpUrl());
    }

    /**
     * Test when multiple valid coupons are provided
     */
    @Test
    public void testBuildNormalCouponMultipleValidCoupons() throws Throwable {
        // arrange
        PriceDisplayDTO priceDisplay = new PriceDisplayDTO();
        PromoCouponInfo coupon1 = new PromoCouponInfo();
        coupon1.setStatus(0);
        coupon1.setCouponGroupId(1L);
        coupon1.setCouponType(0);
        coupon1.setTitle("Coupon 1");
        PromoCouponInfo coupon2 = new PromoCouponInfo();
        coupon2.setStatus(0);
        coupon2.setCouponGroupId(2L);
        coupon2.setCouponType(0);
        coupon2.setTitle("Coupon 2");
        // act
        List<CouponModule> result = service.buildNormalCoupon(Arrays.asList(coupon1, coupon2), priceDisplay);
        // assert
        assertEquals(2, result.size());
        assertEquals("1", result.get(0).getApplyId());
        assertEquals("Coupon 1", result.get(0).getCouponTitle().get(0).getContent());
        assertEquals("2", result.get(1).getApplyId());
        assertEquals("Coupon 2", result.get(1).getCouponTitle().get(0).getContent());
    }

    /**
     * Test when dealGifts list is null
     */
    @Test
    public void testBuildBuyGiftActivity_NullDealGifts() throws Throwable {
        // arrange
        params.setDealGifts(null);
        // act
        java.util.List<CouponModule> result = service.buildBuyGiftActivity(params);
        // assert
        assertNull(result);
    }

    /**
     * Test when dealGifts list is empty
     */
    @Test
    public void testBuildBuyGiftActivity_EmptyDealGifts() throws Throwable {
        // arrange
        params.setDealGifts(new ArrayList<>());
        // act
        java.util.List<CouponModule> result = service.buildBuyGiftActivity(params);
        // assert
        assertNull(result);
    }

    /**
     * Test with single dealGift containing all properties
     */
    @Test
    public void testBuildBuyGiftActivity_SingleCompleteGift() throws Throwable {
        // arrange
        dealGift.setTitle("Test Gift");
        dealGift.setProductTag("Gift Tag");
        dealGift.setValidTimeDesc("Valid Time");
        dealGift.setUseRule("Use Rule");
        dealGift.setThumbnail("thumbnail.jpg");
        dealGift.setCouponNum(2);
        dealGift.setActivityId(123L);
        params.setDealGifts(Lists.newArrayList(dealGift));
        // act
        java.util.List<CouponModule> result = service.buildBuyGiftActivity(params);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        CouponModule module = result.get(0);
        assertEquals("Test Gift", module.getCouponTitle().get(0).getContent());
        assertEquals("Gift Tag", module.getCouponTag().getContent());
        assertEquals("规则：Valid Time，Use Rule", module.getCouponSubTitle().get(0).getContent());
        assertEquals("#666666", module.getCouponSubTitle().get(0).getColor());
        assertEquals(2, module.getCouponNum());
        assertEquals("thumbnail.jpg", module.getCouponAvatar());
        assertEquals(CouponStyleEnum.BIG_CARD.getCode(), module.getCouponStyle());
        assertEquals(CouponStatusEnum.NOT_SHOW.getCode(), module.getCouponStatus());
        assertEquals("123", module.getApplyId());
        assertEquals(PromotionTypeEnum.BUY_GIVE.getCode(), module.getPromotionType());
        assertNotNull(module.getPromotionBarModule());
        assertEquals("赠Test Gift", module.getPromotionBarModule().getLabelDesc());
    }

    /**
     * Test with single dealGift containing minimal required properties
     */
    @Test
    public void testBuildBuyGiftActivity_SingleMinimalGift() throws Throwable {
        // arrange
        dealGift.setTitle("Test Gift");
        dealGift.setActivityId(123L);
        params.setDealGifts(Lists.newArrayList(dealGift));
        // act
        java.util.List<CouponModule> result = service.buildBuyGiftActivity(params);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        CouponModule module = result.get(0);
        assertEquals("Test Gift", module.getCouponTitle().get(0).getContent());
        assertNull(module.getCouponSubTitle());
        assertEquals(CouponStatusEnum.NOT_SHOW.getCode(), module.getCouponStatus());
        assertEquals("123", module.getApplyId());
        assertNotNull(module.getPromotionBarModule());
        assertEquals("赠Test Gift", module.getPromotionBarModule().getLabelDesc());
    }

    /**
     * Test with multiple dealGifts
     */
    @Test
    public void testBuildBuyGiftActivity_MultipleDealGifts() throws Throwable {
        // arrange
        DealGift gift1 = new DealGift();
        gift1.setTitle("Gift 1");
        gift1.setActivityId(1L);
        DealGift gift2 = new DealGift();
        gift2.setTitle("Gift 2");
        gift2.setActivityId(2L);
        params.setDealGifts(Lists.newArrayList(gift1, gift2));
        // act
        java.util.List<CouponModule> result = service.buildBuyGiftActivity(params);
        // assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("Gift 1", result.get(0).getCouponTitle().get(0).getContent());
        assertEquals("Gift 2", result.get(1).getCouponTitle().get(0).getContent());
    }

    /**
     * Test coupon icon properties
     */
    @Test
    public void testBuildBuyGiftActivity_CouponIconProperties() throws Throwable {
        // arrange
        dealGift.setTitle("Test Gift");
        dealGift.setActivityId(123L);
        params.setDealGifts(Lists.newArrayList(dealGift));
        // act
        java.util.List<CouponModule> result = service.buildBuyGiftActivity(params);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        CouponModule module = result.get(0);
        assertNotNull(module.getCouponIcon());
        assertEquals("赠品", module.getCouponIcon().getText());
        assertEquals("#FFFFFF", module.getCouponIcon().getTextColor());
        assertEquals("#FF7000", module.getCouponIcon().getBackgroundStartColor());
        assertEquals("#FF4B10", module.getCouponIcon().getBackgroundEndColor());
    }
}
