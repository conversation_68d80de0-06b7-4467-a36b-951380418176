package com.sankuai.dzshoppingguide.product.detail.application.constants;


import com.google.common.collect.Lists;

import java.util.List;

/**
 * @Author: z<PERSON><PERSON><PERSON><PERSON><EMAIL>
 * @Date: 2025/2/12
 */
public class Constants {
    // 收藏相关
    public static final int DP_RESERVE_FAVOR_BIZ_TYPE = 100;
    public static final int DP_DEAL_FAVOR_BIZ_TYPE = 7;
    public static final int MT_FAVOR_COLL_TYPE = 1;
    public static final short MT_RESERVE_FAVOR_SOURCE_TYPE = 45;
    public static final short MT_DEAL_FAVOR_SOURCE_TYPE = 0;

    // 加价相关
    public static final String OVER_NIGHT_SERVICE_EXIST_KEY = "overNightService-exist";
    public static final String STAY_OVER_START_TIME_KEY = "stayOverStartTime";
    public static final String STAY_OVER_END_TIME_KEY = "stayOverEndTime";
    public static final String STAY_OVER_SERVICE_PREFIX = "起止时间";
    public static final String FREE_BREAKFAST_KEY = "freeBreakfast";
    public static final String FREE_BREAKFAST_TRUE_KEY = "含早餐";
    public static final String FREE_BREAKFAST_TRUE_VALUE = "含早餐，";
    public static final String FREE_BREAKFAST_FALSE_VALUE = "不含早餐，";
    public static final String STAY_OVER_DESC = "stayOverDesc";
    public static final String TECHNICIAN_LEVEL_RULE = "technicianLevelRule";
    public static final String GRADE_EXPLANATION = "gradeExplanation";   // 技师描述信息
    public static final String SERVICE_TITLE = "serviceTitle"; // 技师等级
    public static final String TECHNICIAN_COUNT = "technicianCount";    // 技师数量

    // 过夜
    public static final String OVER_NIGHT_SERVICE_TYPE = "OvernightServices";
    public static final String OVER_NIGHT_SERVICE_TYPE_PAY = "可付费过夜";

    // 导航栏相关
    public static final String NAV_BAR_DEAL_PRODUCT_HANDLER = "navBarDealProductHandler";
    public static final String NAV_BAR_RESERVE_PRODUCT_HANDLER = "navBarReserveProductHandler";

    // 多sku选择相关
    public static final String PRICING_METHOD = "pricingMethod";
    public static final String TIME_SEGMENT_PRICING = "2";
    public static final String SEGMENT_TYPE = "segmentType";
//    public static final String DAY_NIGHT_RANGE = "1"; // 白天/夜间档位
//    public static final String WEEKDAY_WEEKEND_RANGE = "2"; // 工作日/周末档位
    public static final String SELL_DIFFERENT_GRADES = "SellDifferentGrades";
    public static final String WEEKEND = "周末";
    public static final String WEEKDAY = "工作日";
    public static final String NIGHT_RANGE = "夜间档";
    public static final String DAY_RANGE = "白天档";
    public static final List<String> DAY_NIGHT_RANGE_LIST = Lists.newArrayList("白天档", "夜间档");
    public static final List<String> WEEKDAY_WEEKEND_RANGE = Lists.newArrayList("工作日", "周末");
    // 神券优惠感知流量标识
    public static final String MAGIC_COUPON_ENHANCEMENT_FLOW_FLAG = "magicCouponEnhancement";
    // 国家补贴流量标识
    public static final String COUNTRY_SUBSIDIES = "countrySubsidies";
    // 次卡-交易连续包月
    public static final String MONTHLY_SUBSCRIPTION = "monthlySubscription";
}
