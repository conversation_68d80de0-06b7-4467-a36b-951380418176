package com.sankuai.dzshoppingguide.product.detail.application.fetcher.tying;

import static org.junit.jupiter.api.Assertions.*;
import com.dianping.deal.sales.common.datatype.SalesDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.ExtPriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.TyingSaleDTO;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class TyingInfoPaddingFetcherTest {

// ExtPriceTypeEnum.Tying_Sale
private static final int TYING_SALE_TYPE = 3;

// TyingSaleTypeEnum.DEAL_GROUP
private static final int DEAL_GROUP_TYPE = 1;

@InjectMocks
private TyingInfoPaddingFetcher tyingInfoPaddingFetcher;

/**
 * Test case for null priceDisplayDTO
 */
@Test
public void testBuildDealCombinationInfoPrice_NullPriceDisplay() throws Throwable {
// arrange
Map<Integer, CombinationDealInfo> bindingDealCombinationInfoMap = new HashMap<>();
// act
tyingInfoPaddingFetcher.buildDealCombinationInfoPrice(null, bindingDealCombinationInfoMap);
// assert
assertTrue(bindingDealCombinationInfoMap.isEmpty());
}

/**
 * Test case for empty extPrices list
 */
@Test
public void testBuildDealCombinationInfoPrice_EmptyExtPrices() throws Throwable {
// arrange
PriceDisplayDTO priceDisplayDTO = new PriceDisplayDTO();
Map<Integer, CombinationDealInfo> bindingDealCombinationInfoMap = new HashMap<>();
// act
tyingInfoPaddingFetcher.buildDealCombinationInfoPrice(priceDisplayDTO, bindingDealCombinationInfoMap);
// assert
assertTrue(bindingDealCombinationInfoMap.isEmpty());
}

/**
 * Test case for non-matching extPriceType (not Tying_Sale)
 */
@Test
public void testBuildDealCombinationInfoPrice_NonMatchingExtPriceType() throws Throwable {
// arrange
PriceDisplayDTO priceDisplayDTO = new PriceDisplayDTO();
List<ExtPriceDisplayDTO> extPrices = new ArrayList<>();
ExtPriceDisplayDTO extPriceDisplayDTO = new ExtPriceDisplayDTO();
// Not Tying_Sale type
extPriceDisplayDTO.setExtPriceType(1);
extPrices.add(extPriceDisplayDTO);
priceDisplayDTO.setExtPrices(extPrices);
Map<Integer, CombinationDealInfo> bindingDealCombinationInfoMap = new HashMap<>();
CombinationDealInfo info = new CombinationDealInfo();
bindingDealCombinationInfoMap.put(1, info);
// act
tyingInfoPaddingFetcher.buildDealCombinationInfoPrice(priceDisplayDTO, bindingDealCombinationInfoMap);
// assert
assertNull(bindingDealCombinationInfoMap.get(1).getExtPrice());
assertNull(bindingDealCombinationInfoMap.get(1).getExtPricePromoAmount());
}

/**
 * Test case for empty tyingSaleDTOS list
 */
@Test
public void testBuildDealCombinationInfoPrice_EmptyTyingSaleDTOS() throws Throwable {
// arrange
ExtPriceDisplayDTO extPriceDisplayDTO = new ExtPriceDisplayDTO();
extPriceDisplayDTO.setExtPriceType(TYING_SALE_TYPE);
extPriceDisplayDTO.setExtPrice(new BigDecimal("100"));
extPriceDisplayDTO.setExtPricePromoAmount(new BigDecimal("10"));
extPriceDisplayDTO.setTyingSaleDTOS(new ArrayList<>());
PriceDisplayDTO priceDisplayDTO = new PriceDisplayDTO();
List<ExtPriceDisplayDTO> extPrices = new ArrayList<>();
extPrices.add(extPriceDisplayDTO);
priceDisplayDTO.setExtPrices(extPrices);
Map<Integer, CombinationDealInfo> bindingDealCombinationInfoMap = new HashMap<>();
CombinationDealInfo info = new CombinationDealInfo();
bindingDealCombinationInfoMap.put(1, info);
// act
tyingInfoPaddingFetcher.buildDealCombinationInfoPrice(priceDisplayDTO, bindingDealCombinationInfoMap);
// assert
CombinationDealInfo resultInfo = bindingDealCombinationInfoMap.get(1);
assertNotNull(resultInfo);
assertNull(resultInfo.getExtPrice());
assertNull(resultInfo.getExtPricePromoAmount());
assertEquals(0L, resultInfo.getTyingSaleSkuId());
}

/**
 * Test case for bindingDealId not in map
 */
@Test
public void testBuildDealCombinationInfoPrice_BindingDealIdNotInMap() throws Throwable {
// arrange
TyingSaleDTO tyingSaleDTO = new TyingSaleDTO();
tyingSaleDTO.setTyingSaleType(DEAL_GROUP_TYPE);
tyingSaleDTO.setTyingSaleProductId(999);
List<TyingSaleDTO> tyingSaleDTOS = new ArrayList<>();
tyingSaleDTOS.add(tyingSaleDTO);
ExtPriceDisplayDTO extPriceDisplayDTO = new ExtPriceDisplayDTO();
extPriceDisplayDTO.setExtPriceType(TYING_SALE_TYPE);
extPriceDisplayDTO.setTyingSaleDTOS(tyingSaleDTOS);
PriceDisplayDTO priceDisplayDTO = new PriceDisplayDTO();
List<ExtPriceDisplayDTO> extPrices = new ArrayList<>();
extPrices.add(extPriceDisplayDTO);
priceDisplayDTO.setExtPrices(extPrices);
Map<Integer, CombinationDealInfo> bindingDealCombinationInfoMap = new HashMap<>();
// act
tyingInfoPaddingFetcher.buildDealCombinationInfoPrice(priceDisplayDTO, bindingDealCombinationInfoMap);
// assert
assertNull(bindingDealCombinationInfoMap.get(999));
}
}
