package com.sankuai.dzshoppingguide.product.detail.application.fetcher.tag.guarantee;

import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.category.ProductCategoryFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.ProductPromoPriceFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.dto.ProductPriceReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
import com.sankuai.nib.price.operation.api.common.dto.SessionContextDTO;
import com.sankuai.nib.price.operation.api.guarantee.common.dto.GuaranteeObjectQueryDTO;
import com.sankuai.nib.price.operation.api.guarantee.common.dto.TagDTO;
import com.sankuai.nib.price.operation.api.guarantee.guide.standard.dto.ObjectGuaranteeTagDTO;
import com.sankuai.nib.price.operation.api.guarantee.guide.standard.dto.QueryTagOptionDTO;
import com.sankuai.nib.price.operation.api.guarantee.guide.standard.request.BatchQueryGuaranteeTagRequest;
import com.sankuai.nib.price.operation.common.guarantee.enums.GuaranteeTagNameEnum;
import com.sankuai.nib.sp.common.enums.Owner;
import com.sankuai.nib.sp.common.enums.TradeType;
import org.apache.commons.collections4.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletableFuture;

import static com.sankuai.nib.price.operation.common.guarantee.enums.GuaranteeTypeEnum.ANXIN_LEARNING_GUARANTEE;
import static com.sankuai.nib.price.operation.common.guarantee.enums.GuaranteeTypeEnum.STATE_SUBSIDIES;
import static com.sankuai.nib.price.operation.common.guarantee.enums.ObjectTypeEnum.SKU;
import static com.sankuai.nib.price.operation.common.guarantee.enums.ReturnModeEnum.PRIORITY_SORT_ONE_GROUP_BY_GUARANTEE_TYPE;

/**
 * <AUTHOR>
 * @date 2025-03-12
 * @desc 商品保障标签
 */
@Fetcher(
        previousLayerDependencies = {
                ProductCategoryFetcher.class,
                ProductPromoPriceFetcher.class
        }
)
public class SkuGuaranteeTagFetcher extends NormalFetcherContext<ProductGuaranteeTagInfo> {

    @Resource
    private CompositeAtomService compositeAtomService;

    @Override
    protected CompletableFuture<ProductGuaranteeTagInfo> doFetch() {
        ProductPriceReturnValue promoPriceReturnValue = getDependencyResult(ProductPromoPriceFetcher.class);
        ProductCategory productCategory = getDependencyResult(ProductCategoryFetcher.class);
        SessionContextDTO sessionContextDTO = buildSessionContextDTO(productCategory);
        long skuId = getSkuId(promoPriceReturnValue);
        BatchQueryGuaranteeTagRequest guaranteeTagRequest = buildQueryGuaranteeTagRequest(skuId);
        return compositeAtomService.batchQueryGuaranteeTag(sessionContextDTO, guaranteeTagRequest).thenApply(result -> {
            if (CollectionUtils.isEmpty(result)) {
                return new ProductGuaranteeTagInfo();
            }
            ProductGuaranteeTagInfo productGuaranteeTagInfo = new ProductGuaranteeTagInfo();
            TagDTO stateSubsidiesTag = getStateSubsidies(result);
            productGuaranteeTagInfo.setStateSubsidies(stateSubsidiesTag);
            // 安心学
            TagDTO anXinLearningTag = getAnXinLearning(result);
            productGuaranteeTagInfo.setAnXinLearning(anXinLearningTag);
            return productGuaranteeTagInfo;
        });
    }

    private long getSkuId(ProductPriceReturnValue promoPriceReturnValue) {
        if (Objects.isNull(promoPriceReturnValue) || Objects.isNull(promoPriceReturnValue.getPriceDisplayDTO())) {
            return 0L;
        }
        PriceDisplayDTO priceDisplayDTO = promoPriceReturnValue.getPriceDisplayDTO();
        return Objects.nonNull(priceDisplayDTO.getIdentity()) ? priceDisplayDTO.getIdentity().getSkuId() : 0L;
    }

    private TagDTO getStateSubsidies(List<ObjectGuaranteeTagDTO> guaranteeTags) {
        return guaranteeTags.stream()
                .filter(Objects::nonNull)
                .map(ObjectGuaranteeTagDTO::getTags)
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .filter(tag -> Objects.equals(GuaranteeTagNameEnum.STATE_SUBSIDIES_GUARANTEE.getCode(), tag.getCode()))
                .findFirst()
                .orElse(null);
    }

    private TagDTO getAnXinLearning(List<ObjectGuaranteeTagDTO> guaranteeTags) {
        return guaranteeTags.stream()
                .filter(Objects::nonNull)
                .map(ObjectGuaranteeTagDTO::getTags)
                .filter(Objects::nonNull)
                .flatMap(Collection::stream)
                .filter(tag -> Objects.equals(GuaranteeTagNameEnum.ANXIN_LEARNING_GUARANTEE.getCode(), tag.getCode()))
                .findFirst()
                .orElse(null);
    }

    private BatchQueryGuaranteeTagRequest buildQueryGuaranteeTagRequest(long skuId) {
        BatchQueryGuaranteeTagRequest guaranteeTagRequest = new BatchQueryGuaranteeTagRequest();
        guaranteeTagRequest.setObjects(getObjects(skuId));
        guaranteeTagRequest.setGuaranteeTypes(getGuaranteeTypes());
        guaranteeTagRequest.setQueryTagOption(getQueryTagOption());
        return guaranteeTagRequest;
    }

    public QueryTagOptionDTO getQueryTagOption(){
        QueryTagOptionDTO queryTagOptionDTO=new QueryTagOptionDTO();
        queryTagOptionDTO.setReturnMode(PRIORITY_SORT_ONE_GROUP_BY_GUARANTEE_TYPE.getCode());
        return queryTagOptionDTO;
    }

    private Set<Integer> getGuaranteeTypes() {
        Set<Integer> guaranteeTypes = new HashSet<>();
        // 国家补贴
        guaranteeTypes.add(STATE_SUBSIDIES.getCode());
        // 安心学
        guaranteeTypes.add(ANXIN_LEARNING_GUARANTEE.getCode());
        return guaranteeTypes;
    }

    public Set<GuaranteeObjectQueryDTO> getObjects(long skuId) {
        if (skuId <= 0L) {
            return new HashSet<>();
        }
        Set<GuaranteeObjectQueryDTO> objects = new HashSet<>();
        GuaranteeObjectQueryDTO object = new GuaranteeObjectQueryDTO();
        object.setObjectId(String.valueOf(skuId));
        object.setObjectType(SKU.getCode());
        objects.add(object);
        return objects;
    }

    private SessionContextDTO buildSessionContextDTO(ProductCategory productCategory) {
        SessionContextDTO sessionContext = new SessionContextDTO();
        sessionContext.setOwner(Owner.NIB_GENERAL.getValue());
        sessionContext.setTradeType(TradeType.GROUPBUY_PAY.getCode());
        return sessionContext;
    }
}
