package com.sankuai.dzshoppingguide.product.detail.application.builder.makeup.detail;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.dealuser.price.display.api.model.AdjustPriceSelectionDTO;
import com.sankuai.dealuser.price.display.api.model.AdjustPriceSelectionItemDTO;
import com.sankuai.dealuser.price.display.api.model.AdjustPriceSelectionOptionDTO;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.MarkupModuleItem;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class MarkupDetailModuleBuilderTest {

    @InjectMocks
    private MarkupDetailModuleBuilder markupDetailModuleBuilder;

    @Mock
    private AdjustPriceSelectionDTO selectionDTO;

    @Mock
    private AdjustPriceSelectionItemDTO itemDTO;

    @Mock
    private AdjustPriceSelectionOptionDTO optionDTO;

    @Test
    public void testAssembleModuleItemListWhenModuleItemMapIsEmpty() throws Throwable {
        Map<String, MarkupModuleItem> moduleItemMap = new HashMap<>();
        List<MarkupModuleItem> result = markupDetailModuleBuilder.assembleModuleItemList(moduleItemMap, selectionDTO, true);
        assertEquals(0, result.size());
    }

    @Test
    public void testAssembleModuleItemListWhenSelectionDTOIsEmpty() throws Throwable {
        Map<String, MarkupModuleItem> moduleItemMap = new HashMap<>();
        moduleItemMap.put("key", new MarkupModuleItem());
        List<MarkupModuleItem> result = markupDetailModuleBuilder.assembleModuleItemList(moduleItemMap, selectionDTO, true);
        assertEquals(0, result.size());
    }

    @Test
    public void testAssembleModuleItemListWhenItemKeyNotInModuleItemMap() throws Throwable {
        Map<String, MarkupModuleItem> moduleItemMap = new HashMap<>();
        moduleItemMap.put("key", new MarkupModuleItem());
        when(selectionDTO.getAdjustPriceSelectionItems()).thenReturn(new ArrayList<>());
        List<MarkupModuleItem> result = markupDetailModuleBuilder.assembleModuleItemList(moduleItemMap, selectionDTO, true);
        assertEquals(0, result.size());
    }

    @Test
    public void testAssembleModuleItemListWhenOptionsIsEmpty() throws Throwable {
        Map<String, MarkupModuleItem> moduleItemMap = new HashMap<>();
        moduleItemMap.put("key", new MarkupModuleItem());
        when(selectionDTO.getAdjustPriceSelectionItems()).thenReturn(new ArrayList<>());
        List<MarkupModuleItem> result = markupDetailModuleBuilder.assembleModuleItemList(moduleItemMap, selectionDTO, true);
        assertEquals(0, result.size());
    }

    @Test
    public void testAssembleModuleItemListWhenOptionsIsNotEmpty() throws Throwable {
        Map<String, MarkupModuleItem> moduleItemMap = new HashMap<>();
        moduleItemMap.put("key", new MarkupModuleItem());
        when(selectionDTO.getAdjustPriceSelectionItems()).thenReturn(new ArrayList<>());
        List<MarkupModuleItem> result = markupDetailModuleBuilder.assembleModuleItemList(moduleItemMap, selectionDTO, true);
        assertEquals(0, result.size());
    }
}
