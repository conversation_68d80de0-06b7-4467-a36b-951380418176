package com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommendgoods.dtos;

import lombok.Data;

import java.util.Set;

/**
 * Created by float.lu on 2020/9/8.
 */
@Data
public class ProductCouponM {
    /**
     * 购买返券标签
     */
    private String couponTag;

    /**
     * 券ID
     */
    private long couponId;

    /**
     * 分流位id
     */
    private long flowId;

    /**
     * 投放券rowKey
     */
    private String rowKey;

    /**
     * 投放活动id
     */
    private long resourceActivityId;

    /**
     * 投放活动券发券专用-活动id
     */
    private long activityId;

    /**
     * 投放活动券发券专用-物料id
     */
    private String materialId;

    /**
     * 总库存
     */
    private int totalStock;

    /**
     * 总剩余库存
     */
    private int remainStock;

    /**
     * 是否可领
     */
    private Boolean canAssign;

    /**
     * 时间描述
     */
    private String timeDesc;

    /**
     * 时间副描述
     */
    private String timeSubDesc;

    /**
     * 券有效期结束时间
     */
    private Long useEndTime;

    /**
     * 券批次id
     */
    private String couponGroupId;

    /**
     * 券适用描述
     */
    private String couponSuitDesc;

    /**
     * 是否新客专属
     */
    private Boolean freshExclusive;

    /**
     * 适用的SkuId
     */
    private Set<Long> availableRowIds;

    /**
     * 券名
     */
    private String title;

    /**
     * 券门槛描述
     */
    private String couponThresholdDesc;

    /**
     * 券金额
     */
    private String amount;

    /**
     * 券类型
     */
    private Integer couponType;
}
