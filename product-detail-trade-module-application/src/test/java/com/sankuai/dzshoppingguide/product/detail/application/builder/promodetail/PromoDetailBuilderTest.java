package com.sankuai.dzshoppingguide.product.detail.application.builder.promodetail;

import com.dianping.lion.client.Lion;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.BaseFetcherContext;
import com.sankuai.dz.product.detail.gateway.spi.dto.ShepherdGatewayParam;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.LeadsInfoFetcher.LeadsInfoResult;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.cepinpool.dto.CostEffectivePinTuan;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.cityId.CityIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.dealgroup.DealGroupIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.coupon.CouponResult;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.dealgift.DealGiftResult;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.pinpool.PricePinPoolResult;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.pinpool.PromoDisplayResult;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.best.shop.ShopInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.dto.ProductPriceReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.defaultselect.SkuDefaultSelect;
import com.sankuai.dzshoppingguide.product.detail.application.service.promo.PromoDetailService;
import com.sankuai.dzshoppingguide.product.detail.domain.utils.LionConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.promo.vo.PromoDetailVO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class PromoDetailBuilderTest {

    private TestablePromoDetailBuilder promoDetailBuilder;

    @Mock
    private PromoDetailService promoDetailService;

    @Mock
    private ProductDetailPageRequest request;

    @Mock
    private ShepherdGatewayParam shepherdGatewayParam;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.openMocks(this);
        promoDetailBuilder = new TestablePromoDetailBuilder();
        ReflectionTestUtils.setField(promoDetailBuilder, "promoDetailService", promoDetailService);
        ReflectionTestUtils.setField(promoDetailBuilder, "request", request);
        setupBasicMocks();
    }

    private void setupBasicMocks() {
        // Mock base dependencies
        mockDependencyResult(ProductBaseInfo.class);
        mockDependencyResult(ShopInfo.class);
        mockDependencyResult(CouponResult.class);
        mockDependencyResult(LeadsInfoResult.class);
        mockDependencyResult(PricePinPoolResult.class);
        mockDependencyResult(DealGroupIdMapper.class);
        mockDependencyResult(ShopIdMapper.class);
        mockDependencyResult(CityIdMapper.class);
        mockDependencyResult(PromoDisplayResult.class);
        mockDependencyResult(SkuDefaultSelect.class);
        mockDependencyResult(DealGiftResult.class);
        mockDependencyResult(CostEffectivePinTuan.class);
        mockDependencyResult(ProductPriceReturnValue.class);
    }

    private <T extends FetcherReturnValueDTO> void mockDependencyResult(Class<T> resultClass) {
        T mockResult = mock(resultClass);
        promoDetailBuilder.addMockResult(resultClass, mockResult);
    }

    private void mockLionConfig() {
        try (MockedStatic<Lion> lionMock = mockStatic(Lion.class)) {
            lionMock.when(() -> Lion.getString(eq("com.sankuai.dzu.tpbase.dztgdetailweb"), eq(LionConstants.MAGICAL_DP_MRN_MIN_VERSION), eq("0.5.11"))).thenReturn("0.5.11");
        }
    }


    /**
     * Test case for null mrnVersion
     * Expected: Should return false when mrnVersion is null
     */
    @Test
    public void testIsMagicMemberValidWithNullVersion() throws Throwable {
        // arrange
        String mrnVersion = null;
        // act
        boolean result = PromoDetailBuilder.isMagicMemberValid(mrnVersion);
        // assert
        assertFalse(result);
    }

    /**
     * Test case for empty mrnVersion
     * Expected: Should return false when mrnVersion is empty
     */
    @Test
    public void testIsMagicMemberValidWithEmptyVersion() throws Throwable {
        // arrange
        String mrnVersion = "";
        // act
        boolean result = PromoDetailBuilder.isMagicMemberValid(mrnVersion);
        // assert
        assertFalse(result);
    }

    /**
     * Test case for mrnVersion less than minimum version
     * Expected: Should return false when mrnVersion is less than minimum version
     */
    @Test
    public void testIsMagicMemberValidWithLesserVersion() throws Throwable {
        // arrange
        String mrnVersion = "0.5.10";
        // act
        boolean result = PromoDetailBuilder.isMagicMemberValid(mrnVersion);
        // assert
        assertFalse(result);
    }

    /**
     * Test case for mrnVersion equal to minimum version
     * Expected: Should return true when mrnVersion equals minimum version
     */
    @Test
    public void testIsMagicMemberValidWithEqualVersion() throws Throwable {
        // arrange
        String mrnVersion = "0.5.11";
        // act
        boolean result = PromoDetailBuilder.isMagicMemberValid(mrnVersion);
        // assert
        assertTrue(result);
    }

    /**
     * Test case for mrnVersion greater than minimum version
     * Expected: Should return true when mrnVersion is greater than minimum version
     */
    @Test
    public void testIsMagicMemberValidWithGreaterVersion() throws Throwable {
        // arrange
        String mrnVersion = "0.5.12";
        // act
        boolean result = PromoDetailBuilder.isMagicMemberValid(mrnVersion);
        // assert
        assertTrue(result);
    }

    /**
     * Test case for mrnVersion with blank spaces
     * Expected: Should return false when mrnVersion contains only blank spaces
     */
    @Test
    public void testIsMagicMemberValidWithBlankVersion() throws Throwable {
        // arrange
        String mrnVersion = "   ";
        // act
        boolean result = PromoDetailBuilder.isMagicMemberValid(mrnVersion);
        // assert
        assertFalse(result);
    }

    private static class TestablePromoDetailBuilder extends PromoDetailBuilder {

        private final Map<Class<?>, Object> mockResults = new HashMap<>();

        public <T extends FetcherReturnValueDTO> void addMockResult(Class<T> clazz, T result) {
            mockResults.put(clazz, result);
        }

        @SuppressWarnings("unchecked")
        @Override
        protected <Result extends FetcherReturnValueDTO> Result getDependencyResult(Class<? extends BaseFetcherContext> dependencyFetcherClass) {
            // Return the mock result for the corresponding result type
            for (Map.Entry<Class<?>, Object> entry : mockResults.entrySet()) {
                if (entry.getValue().getClass().equals(dependencyFetcherClass) || entry.getKey().isAssignableFrom(dependencyFetcherClass)) {
                    return (Result) entry.getValue();
                }
            }
            return null;
        }
    }
}
