package com.sankuai.dzshoppingguide.product.detail.application.utils;

import com.dianping.cat.Cat;
import com.dianping.lion.common.util.JsonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import com.sankuai.nibmkt.promotion.api.common.enums.PromotionPropertyEnum;
import lombok.extern.slf4j.Slf4j;
import java.math.BigDecimal;
import java.util.*;

@Slf4j
public class PromoInfoHelper {

    /**
     * 政府消费券券包密钥
     */
    private static final String PACKAGE_SECRET_KEY = "packageSecretKey";

    private static BigDecimal toYuan(String couponValue) {
        Cat.logEvent("INVALID_METHOD_2", "com.dianping.mobile.mapi.dztgdetail.util.PromoInfoHelper.toYuan(java.lang.String)");
        return new BigDecimal(couponValue).divide(BigDecimal.valueOf(100), 2, BigDecimal.ROUND_HALF_UP);
    }

    public static String getFinanceExtJson(PromoDTO coupon) {
        if (Objects.isNull(coupon)) {
            return org.apache.commons.lang.StringUtils.EMPTY;
        }
        Map<String, String> promotionOtherInfoMap = coupon.getPromotionOtherInfoMap();
        if (org.apache.commons.collections.MapUtils.isNotEmpty(promotionOtherInfoMap) && promotionOtherInfoMap.containsKey(PromotionPropertyEnum.FINANCE_EXT.getValue())) {
            return promotionOtherInfoMap.get(PromotionPropertyEnum.FINANCE_EXT.getValue());
        }
        return org.apache.commons.lang.StringUtils.EMPTY;
    }

    public static String getFinanceExtPackageSecretKey(PromoDTO coupon) {
        String financeExtJson = PromoInfoHelper.getFinanceExtJson(coupon);
        if (org.apache.commons.lang.StringUtils.isNotBlank(financeExtJson)) {
            Map<String, String> financeExtMap = JsonUtils.fromJson(financeExtJson, new TypeReference<Map<String, String>>() {});
            return financeExtMap.getOrDefault(PACKAGE_SECRET_KEY, org.apache.commons.lang.StringUtils.EMPTY);
        }
        return org.apache.commons.lang.StringUtils.EMPTY;
    }


}
