package com.sankuai.dzshoppingguide.product.detail.application.fetcher.consult;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @create 2025/3/18 20:26
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DetailIMResult extends FetcherReturnValueDTO {
    private String imUrl; //Im聊天地址
}
