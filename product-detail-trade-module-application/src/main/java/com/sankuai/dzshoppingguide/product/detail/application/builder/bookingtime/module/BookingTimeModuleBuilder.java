package com.sankuai.dzshoppingguide.product.detail.application.builder.bookingtime.module;

import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bookingtime.AbstractBookingTimeModuleBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bookingtime.DealBookingDayOffsetConstant;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bookingtime.enums.MoreActionEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bookingtime.enums.MultiTradeBizEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bookingtime.enums.MultiTradeSourceTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.abtest.AbTestFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.bookingtime.DealBookingTimeFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.bookingtime.ShopBookingFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.category.ProductCategoryFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.handler.booking.BookingTimeModuleOrderPageUrlFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sale.info.ProductSaleInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.FetcherDAGStarter;
import com.sankuai.dzshoppingguide.product.detail.application.utils.DateTimeUtils;
import com.sankuai.dzshoppingguide.product.detail.application.utils.JsonUtils;
import com.sankuai.dzshoppingguide.product.detail.application.utils.MultiTradeUtils;
import com.sankuai.dzshoppingguide.product.detail.spi.bookingtime.vo.BookingDateTab;
import com.sankuai.dzshoppingguide.product.detail.spi.bookingtime.vo.BookingTimeCardVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bookingtime.vo.BookingTimeItemVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bookingtime.vo.BookingTimeModuleVO;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.spt.statequery.api.dto.BaseStateDTO;
import com.sankuai.spt.statequery.api.dto.TimeSliceDTO;
import com.sankuai.spt.statequery.api.enums.SubjectDayLabelEnum;
import com.sankuai.spt.statequery.api.enums.TimeSliceLabelEnum;
import com.sankuai.spt.statequery.api.enums.TimeSliceRecallStrategyEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Builder(
        moduleKey = ModuleKeyConstants.MODULE_DETAIL_BOOKING_TIME,
        startFetcher = FetcherDAGStarter.class,
        dependentFetchers = {
                ProductBaseInfoFetcher.class,
                ProductCategoryFetcher.class,
                ShopBookingFetcher.class,
                DealBookingTimeFetcher.class,
                BookingTimeModuleOrderPageUrlFetcher.class,
                AbTestFetcher.class,
                ProductSaleInfoFetcher.class,
        }
)
@Slf4j
public class BookingTimeModuleBuilder extends AbstractBookingTimeModuleBuilder {

    @Override
    public BookingTimeModuleVO doBuild() {
        init();
        if (!isShow()) {
            return null;
        }
        try {
            switch (productCategory.getProductSecondCategoryId()) {
                case 301:
                    return getKtvModule();
                case 303:
                    return getMassageModule();
                default:
                    return null;
            }
        } catch (Exception e) {
            log.error("buildModule failed, request={}", JsonUtils.toJson(request), e);
        }
        return null;
    }

    //KTV————————————————————————————————————————————————————————————————————
    private BookingTimeModuleVO getKtvModule() {
        // 三级类目判断
        MultiTradeUtils.isMultiTradeCategory(request);
        if (!fromMarketingSource) {
            return null;
        }
        BookingTimeModuleVO timeModuleVO = new BookingTimeModuleVO();
        timeModuleVO.setTitle("可订时间");
        timeModuleVO.setTabList(buildKtvModuleDateTab());
        if (CollectionUtils.isEmpty(timeModuleVO.getTabList())) {
            return null;
        }
        timeModuleVO.setMoreText("更多时间");
        timeModuleVO.setMoreAction(MoreActionEnum.POPUP.getValue());
        timeModuleVO.setBiz(MultiTradeBizEnum.KTV.getValue());
        timeModuleVO.setSourceType(fromMarketingSource ? MultiTradeSourceTypeEnum.SALES.getValue() : MultiTradeSourceTypeEnum.MAIN.getValue());
        return timeModuleVO;
    }

    private List<BookingDateTab> buildKtvModuleDateTab() {
        BookingDateTab bookingDateTab = new BookingDateTab();
        bookingDateTab.setTimeCardList(
                DealBookingDayOffsetConstant.BEFORE_DAWN_BUILDER_DAY_OFFSETS.stream()
                        .map(this::buildKtvTimeCard)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList())
        );
        if (CollectionUtils.isEmpty(bookingDateTab.getTimeCardList())) {
            return null;
        }
        return Lists.newArrayList(bookingDateTab);
    }

    private BookingTimeCardVO buildKtvTimeCard(int offset) {
        BookingTimeCardVO bookingTimeCardVO = new BookingTimeCardVO();
        BookingTimeItemVO weekInfo = BookingTimeItemVO.builder().content(getWeekText(offset)).contentColor("#111111").contentSize("12").contentWeight("400").build();
        BookingTimeItemVO dateInfo = BookingTimeItemVO.builder().content(DateTimeUtils.getMonthDate(LocalDateTime.now().plusDays(offset))).contentColor("#111111").contentSize("14").contentWeight("500").build();
        BookingStateInfo dayStockText = getKtvDayStockText(offset);
        if (dayStockText == null) {
            return null;
        }
        BookingTimeItemVO bookingState = BookingTimeItemVO.builder()
                .content(dayStockText.getText())
                .contentColor(dayStockText.getColor())
                .contentSize("12")
                .contentWeight("400")
                .build();
        bookingTimeCardVO.setBookingInfo(Lists.newArrayList(weekInfo, dateInfo, bookingState));
        bookingTimeCardVO.setClickable(false);
        bookingTimeCardVO.setClickUrl("");
        return bookingTimeCardVO;
    }

    private BookingStateInfo getKtvDayStockText(int offset) {
        List<TimeSliceDTO> timeSlices = new ArrayList<>();
        Set<SubjectDayLabelEnum> labels = new HashSet<>();
        BaseStateDTO todayBaseStateDTO = getBaseStateDTO(DateTimeUtils.getOffsetDate(offset));
        if (todayBaseStateDTO != null && CollectionUtils.isNotEmpty(todayBaseStateDTO.getTimeSlices())) {
            timeSlices.addAll(todayBaseStateDTO.getTimeSlices().stream().filter(
                    timeSliceDTO -> timeSliceDTO.getRecallStrategies().contains(
                            TimeSliceRecallStrategyEnum.TODAY
                    )
            ).collect(Collectors.toList()));
            labels = todayBaseStateDTO.getLabels();
        }
        //是否仅展示明日召回的跨天时间片
        boolean isOnlyDisplayTomorrowTimeSlices = CollectionUtils.isEmpty(timeSlices);
        BaseStateDTO tomorrowBaseStateDTO = getBaseStateDTO(DateTimeUtils.getOffsetDate(offset + 1));
        if (tomorrowBaseStateDTO != null && CollectionUtils.isNotEmpty(tomorrowBaseStateDTO.getTimeSlices())) {
            timeSlices.addAll(tomorrowBaseStateDTO.getTimeSlices().stream().filter(
                    timeSliceDTO -> timeSliceDTO.getRecallStrategies().contains(
                            TimeSliceRecallStrategyEnum.YESTERDAY
                    )
            ).collect(Collectors.toList()));
            if (CollectionUtils.isEmpty(labels)) {//如果今日数据取不到labels，那就取明日
                labels = tomorrowBaseStateDTO.getLabels();
            }
        }
        if (CollectionUtils.isEmpty(timeSlices)){
            //如果当天没有时间片,则直接不展示
            return null;
        }
        boolean hasValidTimeSlice = timeSlices.stream().anyMatch(TimeSliceDTO::isAvailable);
        if (!hasValidTimeSlice){//如果当天没有可用的时间片,则展示已订满
            return new BookingStateInfo("已订满", "#555555");
        }
        if (CollectionUtils.isEmpty(labels)) {//异常情况,如果拿不到聚合的labels则兜底展示包房紧张
            return new BookingStateInfo("包房紧张", "#555555");
        }
        // 库存真实性高
        if (labels.contains(SubjectDayLabelEnum.DISPLAY_EARLIEST_AVAILABLE_TIME)) {
            return timeSlices.stream()
                    .filter(TimeSliceDTO::isAvailable)
                    .findFirst()
                    .map(timeSliceDTO -> {
                        if (timeSliceDTO.getRecallStrategies().contains(TimeSliceRecallStrategyEnum.TODAY)) {
                            return new BookingStateInfo(String.format("%s可订", timeSliceDTO.getStartTime()), "#555555");
                        } else if (timeSliceDTO.getRecallStrategies().contains(TimeSliceRecallStrategyEnum.YESTERDAY)) {
                            return new BookingStateInfo(String.format("次日%s可订", timeSliceDTO.getStartTime()), "#555555");
                        } else {
                            Cat.logError(new IllegalAccessException("走到这里需要排查下游服务or产品功能升级但没有兼顾商品详情页"));
                            return new BookingStateInfo(String.format("%s可订", timeSliceDTO.getStartTime()), "#555555");
                        }
                    }).orElse(null);
        }
        // 库存真实性低
        return labels.contains(SubjectDayLabelEnum.DAY_RESIDUE_SUFFICIENT) ?
                new BookingStateInfo("包房充足", "#555555") :
                new BookingStateInfo("包房紧张", "#555555");
    }
    //KTV————————————————————————————————————————————————————————————————————

    //足疗————————————————————————————————————————————————————————————————————
    private BookingTimeModuleVO getMassageModule() {
        BookingTimeModuleVO timeModuleVO = new BookingTimeModuleVO();
        timeModuleVO.setTitle("可订时间");
        timeModuleVO.setTabList(buildMassageModuleDateTab());
        if (CollectionUtils.isEmpty(timeModuleVO.getTabList())) {
            return null;
        }
        if (fromMarketingSource) {
            timeModuleVO.setMoreText("全部时间");
            timeModuleVO.setMoreAction(MoreActionEnum.POPUP.getValue());
        } else {
            timeModuleVO.setMoreText("全部时间");
            timeModuleVO.setMoreUrl(orderPageUrlResult != null ? orderPageUrlResult.getUrl() : "");
            timeModuleVO.setMoreAction(MoreActionEnum.REDIRECT.getValue());
        }
        timeModuleVO.setBiz(MultiTradeBizEnum.MASSAGE.getValue());
        timeModuleVO.setSourceType(fromMarketingSource ? MultiTradeSourceTypeEnum.SALES.getValue() : MultiTradeSourceTypeEnum.MAIN.getValue());
        return timeModuleVO;
    }

    private List<BookingDateTab> buildMassageModuleDateTab() {
        BookingDateTab bookingDateTab = new BookingDateTab();
        List<BookingTimeCardVO> timeCardVOList = Lists.newArrayList();
        for (int offset : DealBookingDayOffsetConstant.DAY_OFFSETS) {
            timeCardVOList.addAll(getMassageModuleSingleDate(offset));
            if (timeCardVOList.size() > MASSAGE_MIN_CARD_COUNT) {
                break;
            }
        }
        if (CollectionUtils.isEmpty(timeCardVOList)) {
            return null;
        }
        bookingDateTab.setTimeCardList(timeCardVOList);
        return Lists.newArrayList(bookingDateTab);
    }

    private List<BookingTimeCardVO> getMassageModuleSingleDate(int offset) {
        String weekStr = getMassageWeekText(offset);
        String offsetDate = DateTimeUtils.getOffsetDate(offset);
        List<BookingTimeCardVO> timeCardVOList = Lists.newArrayList();
        BaseStateDTO baseStateDTO = getBaseStateDTO(offsetDate);
        if (baseStateDTO == null || CollectionUtils.isEmpty(baseStateDTO.getTimeSlices())) {
            return Lists.newArrayList();
        }
        List<BookingTimeCardVO> allSlice = baseStateDTO.getTimeSlices().stream().map(t -> buildMassageModuleTimeCard(weekStr, t, offset)).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(allSlice)) {
            timeCardVOList.addAll(allSlice);
        }
        return timeCardVOList;
    }

    private BookingTimeCardVO buildMassageModuleTimeCard(String weekStr, TimeSliceDTO timeSliceDTO, int offset) {
        // 过滤不可用
        if (!timeSliceDTO.isAvailable()) {
            return null;
        }
        BookingTimeCardVO bookingTimeCardVO = new BookingTimeCardVO();
        BookingTimeItemVO timeInfo = BookingTimeItemVO.builder().content(getMassageTimeText(weekStr, timeSliceDTO)).contentColor("#111111").contentSize("14").contentWeight("500").build();
        BookingStateInfo stockState = getBookingState(timeSliceDTO);
        if (stockState == null) {
            return null;
        }
        BookingTimeItemVO bookingState = BookingTimeItemVO.builder()
                .content(stockState.getText())
                .contentColor(stockState.getColor())
                .contentSize("12")
                .contentWeight("400")
                .build();
        bookingTimeCardVO.setBookingInfo(Lists.newArrayList(timeInfo, bookingState));
        bookingTimeCardVO.setClickable(true);
        bookingTimeCardVO.setClickUrl(getMassageTimeSliceUrl(timeSliceDTO, offset));
        return bookingTimeCardVO;
    }

    private String getMassageTimeText(String weekStr, TimeSliceDTO timeSliceDTO) {
        boolean directToShop = CollectionUtils.isNotEmpty(timeSliceDTO.getLabels()) && timeSliceDTO.getLabels().contains(TimeSliceLabelEnum.START_NOW);
        if (directToShop) {
            return "立即到店";
        }
        return String.format("%s %s", weekStr, timeSliceDTO.getStartTime());
    }

    private String getMassageWeekText(int offset) {
        switch (offset) {
            case 0:
                return "";
            case 1:
                return "明天";
            default:
                return DateTimeUtils.getCrossWeekStr(LocalDateTime.now().plusDays(offset));
        }
    }

    private String getMassageTimeSliceUrl(TimeSliceDTO timeSliceDTO, int dayOffset) {
        if (orderPageUrlResult == null || StringUtils.isBlank(orderPageUrlResult.getUrl())) {
            return "";
        }
        long timestamp = getTimestamp(dayOffset, timeSliceDTO.getStartTime());
        String url = orderPageUrlResult.getUrl();
        if (url.contains("&expectedbooktime=")) {
            // 替换已有的 expectedbooktime 参数
            url = url.replaceAll("(&expectedbooktime=)\\d*", "$1" + timestamp);
            return url;
        } else {
            // 直接拼接
            return String.format("%s&expectedbooktime=%d", url, timestamp);
        }
    }

    /**
     * 计算时间戳
     *
     * @param dayOffset 与今天的偏移量
     * @param timeStr   格式HH:mm
     * @return
     */
    private long getTimestamp(int dayOffset, String timeStr) {
        // 取当前日期加 dayOffset 天
        LocalDateTime date = LocalDateTime.now().plusDays(dayOffset);
        // 解析 timeStr，格式为 HH:mm
        String[] parts = timeStr.split(":");
        int hour = Integer.parseInt(parts[0]);
        int minute = Integer.parseInt(parts[1]);
        // 拼成完整的 LocalDateTime
        LocalDateTime dateTime = date.withHour(hour).withMinute(minute).withSecond(0).withNano(0);
        // 转为时间戳（毫秒）
        return dateTime.atZone(java.time.ZoneId.systemDefault()).toEpochSecond() * 1000;
    }

    //足疗————————————————————————————————————————————————————————————————————

}
