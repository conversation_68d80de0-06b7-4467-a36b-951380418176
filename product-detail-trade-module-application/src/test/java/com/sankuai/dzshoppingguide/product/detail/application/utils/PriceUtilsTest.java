package com.sankuai.dzshoppingguide.product.detail.application.utils;

import static org.junit.jupiter.api.Assertions.*;
import java.math.BigDecimal;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.NullSource;
import org.junit.jupiter.params.provider.ValueSource;

/**
 * Unit tests for {@link PriceUtils#buildMarketPrice(BigDecimal)}
 */
@DisplayName("PriceUtils unit tests")
class PriceUtilsTest {

    /**
     * Test when input is null should return null
     */
    @Test
    @DisplayName("buildMarketPrice with null input should return null")
    void testBuildMarketPriceWithNullInput() {
        // arrange
        final BigDecimal input = null;
        // act
        final String result = PriceUtils.buildMarketPrice(input);
        // assert
        assertNull(result, "Null input should return null");
    }

    /**
     * Test when input is zero or negative should return null
     */
    @ParameterizedTest
    @ValueSource(strings = { "0", "-0.01", "-1", "-100.50" })
    @DisplayName("buildMarketPrice with zero or negative input should return null")
    void testBuildMarketPriceWithZeroOrNegativeInput(String value) {
        // arrange
        final BigDecimal input = new BigDecimal(value);
        // act
        final String result = PriceUtils.buildMarketPrice(input);
        // assert
        assertNull(result, "Zero or negative input should return null");
    }

    /**
     * Test with positive input should return stripped trailing zeros string
     */
    @ParameterizedTest
    @ValueSource(strings = { "1.00", "1.0", "1", "123.4500", "0.100", "999999999999999999.9900" })
    @DisplayName("buildMarketPrice with positive input should return stripped trailing zeros")
    void testBuildMarketPriceWithPositiveInput(String value) {
        // arrange
        final BigDecimal input = new BigDecimal(value);
        final String expected = input.stripTrailingZeros().toPlainString();
        // act
        final String result = PriceUtils.buildMarketPrice(input);
        // assert
        assertEquals(expected, result, "Positive input should return stripped trailing zeros string");
    }

    /**
     * Test with very small positive input
     */
    @Test
    @DisplayName("buildMarketPrice with very small positive input")
    void testBuildMarketPriceWithVerySmallPositiveInput() {
        // arrange
        final BigDecimal input = new BigDecimal("0.00000001");
        // act
        final String result = PriceUtils.buildMarketPrice(input);
        // assert
        assertEquals("0.00000001", result, "Very small positive input should be handled correctly");
    }

    /**
     * Test with very large positive input
     */
    @Test
    @DisplayName("buildMarketPrice with very large positive input")
    void testBuildMarketPriceWithVeryLargePositiveInput() {
        // arrange
        final BigDecimal input = new BigDecimal("999999999999999999.99");
        // act
        final String result = PriceUtils.buildMarketPrice(input);
        // assert
        assertEquals("999999999999999999.99", result, "Very large positive input should be handled correctly");
    }

    /**
     * Test with integer input should return without decimal point
     */
    @Test
    @DisplayName("buildMarketPrice with integer input should return without decimal point")
    void testBuildMarketPriceWithIntegerInput() {
        // arrange
        final BigDecimal input = new BigDecimal("123.00");
        // act
        final String result = PriceUtils.buildMarketPrice(input);
        // assert
        assertEquals("123", result, "Integer input should return without decimal point");
    }
}
