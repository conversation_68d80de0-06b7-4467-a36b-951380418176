package com.sankuai.dzshoppingguide.product.detail.spi.dtos;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/2/5 10:44
 */
@Data
@TypeDoc(description = "通用属性VO")
@MobileDo(id = 0x580e)
public class HighlightsModuleAttrVO implements Serializable {

    @FieldDoc(description = "属性值")
    @MobileDo.MobileField(key = 0x97dd)
    private String value;

    @FieldDoc(description = "属性名")
    @MobileDo.MobileField(key = 0x7ab8)
    private String name;
}
