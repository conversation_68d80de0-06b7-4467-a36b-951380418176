package com.sankuai.dzshoppingguide.product.detail.spi.similargoods.vo.dtos;

import com.dianping.mobile.framework.annotation.MobileDo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@MobileDo(id = 0x36a0)
public class DzProductExtraAttrVo implements Serializable {
    /**
    * 打点信息Lab
    */
    @MobileDo.MobileField(key = 0x90b4)
    private String labs;

    /**
    * 场次开始时间是否在10点到23点间
    */
    @MobileDo.MobileField(key = 0xbe2f)
    private String specialStartTime;

    /**
    * 是否由一个订单拼成
    */
    @MobileDo.MobileField(key = 0x8eff)
    private String oneOrder;

    /**
    * 周六周日操作
    */
    @MobileDo.MobileField(key = 0x5410)
    private String weekendUpdate;

    /**
    * 工作日操作
    */
    @MobileDo.MobileField(key = 0x6329)
    private String workdayUpdate;

    /**
    * 商户操作时间
    */
    @MobileDo.MobileField(key = 0x55d3)
    private String shopUpdateTime;

    /**
    * 商户参与拼场类型
    */
    @MobileDo.MobileField(key = 0x4e26)
    private String poolJoinType;

    /**
    * 剧本时长
    */
    @MobileDo.MobileField(key = 0xc4d0)
    private String duration;

    /**
    * 是否关联剧本标品
    */
    @MobileDo.MobileField(key = 0xf416)
    private String relateSpu;

    /**
    * 未得分原因
    */
    @MobileDo.MobileField(key = 0x50e3)
    private String scoreReason;

    /**
    * 拼场得分
    */
    @MobileDo.MobileField(key = 0xf2a4)
    private String poolScore;

    /**
    * 最低开场人数
    */
    @MobileDo.MobileField(key = 0x1de1)
    private int poolMinNum;

    /**
    * 拼场的线下人数
    */
    @MobileDo.MobileField(key = 0x7154)
    private int poolOfflineNum;

    /**
    * 拼场的开始时间
    */
    @MobileDo.MobileField(key = 0xafa1)
    private String pinStartTime;

    /**
    * 场次的开始时间
    */
    @MobileDo.MobileField(key = 0x3795)
    private String poolStartTime;

    /**
    * 关联订单id
    */
    @MobileDo.MobileField(key = 0xa13a)
    private List<String> orders;

    /**
    * 拼场跳转链接
    */
    @MobileDo.MobileField(key = 0x74eb)
    private String generalPinJumpUrl;

    /**
    * 对应日期
    */
    @MobileDo.MobileField(key = 0xcaff)
    private long skuDate;

    /**
    *
    */
    @MobileDo.MobileField(key = 0x9724)
    private int productId;

    /**
    * skuId
    */
    @MobileDo.MobileField(key = 0xb231)
    private int itemId;

   }