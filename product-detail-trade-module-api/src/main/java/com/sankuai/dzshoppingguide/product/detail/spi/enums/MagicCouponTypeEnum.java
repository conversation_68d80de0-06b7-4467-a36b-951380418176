package com.sankuai.dzshoppingguide.product.detail.spi.enums;

/**
 * 功能描述:  <p>
 *
 * <AUTHOR> lintao<PERSON><PERSON> <p>
 * @version 2024/3/20
 * @since mapi-dztgdetail-web
 */
public enum MagicCouponTypeEnum {

    UN_KNOWN(0, "未识别"),
    PAY_COUPON(1, "付费神券"),
    FREE_COUPON(2, "免费神券");

    final private int code;
    final private String name;

    MagicCouponTypeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    public int getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }

}
