package com.sankuai.dzshoppingguide.product.detail.application.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.dianping.pigeon.remoting.common.exception.RpcException;
import com.dianping.pigeon.remoting.invoker.util.InvokerHelper;
import com.dianping.spc.common.Response;
import com.dianping.tgc.open.entity.BatchExProxyCouponRequest;
import com.dianping.tgc.open.entity.BatchExProxyCouponResponseDTO;
import com.dianping.tgc.open.v2.TGCGetCouponComponentQueryService;
import com.sankuai.dzshoppingguide.product.detail.domain.utils.PigeonCallbackUtils;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.dzshoppingguide.product.detail.domain.utils.ThriftAsyncUtils;
import com.sankuai.mpproduct.idservice.api.model.GroupIdDTO;
import com.sankuai.mpproduct.idservice.api.request.MtGroupIdConvertRequest;
import com.sankuai.mpproduct.idservice.api.response.GroupIdConvertResponse;
import com.sankuai.mpproduct.idservice.api.service.IdService;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.mockito.MockedStatic;

@ExtendWith(MockitoExtension.class)
public class CompositeAtomServiceImplQueryExcludeProxyCouponList1Test {

    @Mock
    private TGCGetCouponComponentQueryService couponComponentQueryServiceFuture;

    @InjectMocks
    private CompositeAtomServiceImpl compositeAtomService;

    @Captor
    private ArgumentCaptor<BatchExProxyCouponRequest> requestCaptor;

    private BatchExProxyCouponRequest validRequest;

    private BatchExProxyCouponResponseDTO validResponse;

    @Mock
    private IdService idService;

    @BeforeEach
    void setUp() {
        validRequest = new BatchExProxyCouponRequest();
        validRequest.setPlatform(1);
        validRequest.setClientType(1);
        validRequest.setMtUserId(123L);
        validRequest.setCityId(10);
        validRequest.setMtShopId(456L);
        validRequest.setBizType(1);
        validRequest.setBizId(789L);
        validResponse = new BatchExProxyCouponResponseDTO();
    }

    /**
     * 测试正常流程 - 服务调用成功返回有效结果
     */
    @Test
    public void testQueryExcludeProxyCouponListSuccessWithValidResponse() throws Throwable {
        // arrange
        Response<BatchExProxyCouponResponseDTO> mockResponse = Response.success(validResponse);
        doAnswer((InvocationOnMock invocation) -> {
            InvokerHelper.getCallback().onSuccess(mockResponse);
            return null;
        }).when(couponComponentQueryServiceFuture).queryExcludeProxyCouponList(any());
        // act
        CompletableFuture<BatchExProxyCouponResponseDTO> result = compositeAtomService.queryExcludeProxyCouponList(validRequest);
        // assert
        verify(couponComponentQueryServiceFuture).queryExcludeProxyCouponList(requestCaptor.capture());
        assertEquals(validRequest, requestCaptor.getValue());
        assertNotNull(result);
    }

    /**
     * 测试正常流程 - 服务调用成功但返回空结果
     */
    @Test
    public void testQueryExcludeProxyCouponListSuccessWithNullResponse() throws Throwable {
        // arrange
        Response<BatchExProxyCouponResponseDTO> mockResponse = Response.success(null);
        doAnswer((InvocationOnMock invocation) -> {
            InvokerHelper.getCallback().onSuccess(mockResponse);
            return null;
        }).when(couponComponentQueryServiceFuture).queryExcludeProxyCouponList(any());
        // act
        CompletableFuture<BatchExProxyCouponResponseDTO> result = compositeAtomService.queryExcludeProxyCouponList(validRequest);
        // assert
        verify(couponComponentQueryServiceFuture).queryExcludeProxyCouponList(requestCaptor.capture());
        assertEquals(validRequest, requestCaptor.getValue());
        assertNotNull(result);
    }

    /**
     * 测试异常流程 - 服务调用抛出异常
     */
    @Test
    public void testQueryExcludeProxyCouponListWithException() throws Throwable {
        // arrange
        RpcException exception = new RpcException("RPC调用失败");
        doAnswer((InvocationOnMock invocation) -> {
            InvokerHelper.getCallback().onFailure(exception);
            return null;
        }).when(couponComponentQueryServiceFuture).queryExcludeProxyCouponList(any());
        // act
        CompletableFuture<BatchExProxyCouponResponseDTO> result = compositeAtomService.queryExcludeProxyCouponList(validRequest);
        // assert
        verify(couponComponentQueryServiceFuture).queryExcludeProxyCouponList(requestCaptor.capture());
        assertEquals(validRequest, requestCaptor.getValue());
        assertNotNull(result);
    }

    /**
     * 测试异常流程 - 服务返回结果但result为null
     */
    @Test
    public void testQueryExcludeProxyCouponListWithNullResult() throws Throwable {
        // arrange
        Response<BatchExProxyCouponResponseDTO> mockResponse = new Response<>(true, 200);
        mockResponse.setResult(null);
        doAnswer((InvocationOnMock invocation) -> {
            InvokerHelper.getCallback().onSuccess(mockResponse);
            return null;
        }).when(couponComponentQueryServiceFuture).queryExcludeProxyCouponList(any());
        // act
        CompletableFuture<BatchExProxyCouponResponseDTO> result = compositeAtomService.queryExcludeProxyCouponList(validRequest);
        // assert
        verify(couponComponentQueryServiceFuture).queryExcludeProxyCouponList(requestCaptor.capture());
        assertEquals(validRequest, requestCaptor.getValue());
        assertNotNull(result);
    }

    /**
     * 测试异常流程 - 服务返回结果但result.isSuccess为false
     */
    @Test
    public void testQueryExcludeProxyCouponListWithFailedResponse() throws Throwable {
        // arrange
        Response<BatchExProxyCouponResponseDTO> mockResponse = new Response<>(false, 500, "Internal Error");
        mockResponse.setResult(validResponse);
        doAnswer((InvocationOnMock invocation) -> {
            InvokerHelper.getCallback().onSuccess(mockResponse);
            return null;
        }).when(couponComponentQueryServiceFuture).queryExcludeProxyCouponList(any());
        // act
        CompletableFuture<BatchExProxyCouponResponseDTO> result = compositeAtomService.queryExcludeProxyCouponList(validRequest);
        // assert
        verify(couponComponentQueryServiceFuture).queryExcludeProxyCouponList(requestCaptor.capture());
        assertEquals(validRequest, requestCaptor.getValue());
        assertNotNull(result);
    }

    /**
     * 测试边界情况 - 请求参数为null
     */
    @Test
    public void testQueryExcludeProxyCouponListWithNullRequest() throws Throwable {
        // arrange
        doAnswer((InvocationOnMock invocation) -> {
            InvokerHelper.getCallback().onSuccess(Response.success(null));
            return null;
        }).when(couponComponentQueryServiceFuture).queryExcludeProxyCouponList(any());
        // act
        CompletableFuture<BatchExProxyCouponResponseDTO> result = compositeAtomService.queryExcludeProxyCouponList(null);
        // assert
        verify(couponComponentQueryServiceFuture).queryExcludeProxyCouponList(null);
        assertNotNull(result);
    }

    @Test
    public void testConvertMtGroupIdsToDpGroupIds_NormalCase() throws Throwable {
        // arrange
        List<Long> mtGroupIds = Lists.newArrayList(1L, 2L);
        Map<Long, GroupIdDTO> convertResult = new HashMap<>();
        convertResult.put(1L, new GroupIdDTO(100L, 1));
        convertResult.put(2L, new GroupIdDTO(200L, 1));
        GroupIdConvertResponse response = new GroupIdConvertResponse();
        response.setSuccess(true);
        response.setGroupIdConvertResult(convertResult);
        ArgumentCaptor<MtGroupIdConvertRequest> requestCaptor = ArgumentCaptor.forClass(MtGroupIdConvertRequest.class);
        when(idService.convertMtGroupIdsToDpGroupIds(requestCaptor.capture())).thenReturn(null);
        // act
        CompletableFuture<Map<Long, Long>> resultFuture = compositeAtomService.convertMtGroupIdsToDpGroupIds(mtGroupIds);
        // assert
        verify(idService).convertMtGroupIdsToDpGroupIds(any(MtGroupIdConvertRequest.class));
        assertEquals(mtGroupIds, requestCaptor.getValue().getMtGroupIds());
        // Since we can't mock static methods, we can only verify the service was called correctly
        assertNotNull(resultFuture);
    }

    @Test
    public void testConvertMtGroupIdsToDpGroupIds_UnsuccessfulResponse() throws Throwable {
        // arrange
        List<Long> mtGroupIds = Lists.newArrayList(1L, 2L);
        GroupIdConvertResponse response = new GroupIdConvertResponse();
        response.setSuccess(false);
        ArgumentCaptor<MtGroupIdConvertRequest> requestCaptor = ArgumentCaptor.forClass(MtGroupIdConvertRequest.class);
        when(idService.convertMtGroupIdsToDpGroupIds(requestCaptor.capture())).thenReturn(null);
        // act
        CompletableFuture<Map<Long, Long>> resultFuture = compositeAtomService.convertMtGroupIdsToDpGroupIds(mtGroupIds);
        // assert
        verify(idService).convertMtGroupIdsToDpGroupIds(any(MtGroupIdConvertRequest.class));
        assertEquals(mtGroupIds, requestCaptor.getValue().getMtGroupIds());
        assertNotNull(resultFuture);
    }

    @Test
    public void testConvertMtGroupIdsToDpGroupIds_NullResponse() throws Throwable {
        // arrange
        List<Long> mtGroupIds = Lists.newArrayList(1L, 2L);
        when(idService.convertMtGroupIdsToDpGroupIds(any())).thenReturn(null);
        // act
        CompletableFuture<Map<Long, Long>> resultFuture = compositeAtomService.convertMtGroupIdsToDpGroupIds(mtGroupIds);
        // assert
        verify(idService).convertMtGroupIdsToDpGroupIds(any(MtGroupIdConvertRequest.class));
        assertNotNull(resultFuture);
    }

    @Test
    public void testConvertMtGroupIdsToDpGroupIds_ServiceException() throws Throwable {
        // arrange
        List<Long> mtGroupIds = Lists.newArrayList(1L, 2L);
        when(idService.convertMtGroupIdsToDpGroupIds(any())).thenThrow(new RuntimeException("Service error"));
        // act
        CompletableFuture<Map<Long, Long>> resultFuture = compositeAtomService.convertMtGroupIdsToDpGroupIds(mtGroupIds);
        // assert
        verify(idService).convertMtGroupIdsToDpGroupIds(any(MtGroupIdConvertRequest.class));
        assertNotNull(resultFuture);
        Map<Long, Long> result = resultFuture.get();
        assertTrue(result.isEmpty());
    }

    @Test
    public void testConvertMtGroupIdsToDpGroupIds_PartialMappings() throws Throwable {
        // arrange
        List<Long> mtGroupIds = Lists.newArrayList(1L, 2L, 3L);
        Map<Long, GroupIdDTO> convertResult = new HashMap<>();
        convertResult.put(1L, new GroupIdDTO(100L, 1));
        convertResult.put(3L, new GroupIdDTO(300L, 1));
        GroupIdConvertResponse response = new GroupIdConvertResponse();
        response.setSuccess(true);
        response.setGroupIdConvertResult(convertResult);
        ArgumentCaptor<MtGroupIdConvertRequest> requestCaptor = ArgumentCaptor.forClass(MtGroupIdConvertRequest.class);
        when(idService.convertMtGroupIdsToDpGroupIds(requestCaptor.capture())).thenReturn(null);
        // act
        CompletableFuture<Map<Long, Long>> resultFuture = compositeAtomService.convertMtGroupIdsToDpGroupIds(mtGroupIds);
        // assert
        verify(idService).convertMtGroupIdsToDpGroupIds(any(MtGroupIdConvertRequest.class));
        assertEquals(mtGroupIds, requestCaptor.getValue().getMtGroupIds());
        assertNotNull(resultFuture);
    }
}
