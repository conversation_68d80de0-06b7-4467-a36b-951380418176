package com.sankuai.dzshoppingguide.product.detail.application.service.dto;

import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * @Author: guangyujie
 * @Date: 2024/8/12 20:40
 */
@Data
public class ShopMemberQueryResponse {

    /**
     * 是否走的新接口
     */
    private boolean newInterface;

    /**
     * 团单关联的会员优惠信息
     */
    private Map<Long, ShopMemberDetailV> dpDealId_shopMemberDetail_map = new HashMap<>();

    public ShopMemberQueryResponse(final boolean newInterface,
                                   final Map<Long, ShopMemberDetailV> dpDealId_shopMemberDetail_map) {
        this.newInterface = newInterface;
        this.dpDealId_shopMemberDetail_map = dpDealId_shopMemberDetail_map;
    }

    public ShopMemberQueryResponse(final boolean newInterface) {
        this.newInterface = newInterface;
    }

}
