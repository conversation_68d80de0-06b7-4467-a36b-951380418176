package com.sankuai.dzshoppingguide.product.detail.application.fetcher.cepinpool;

import com.alibaba.fastjson.JSON;
import com.dianping.lion.client.Lion;
import com.dianping.lion.common.util.JsonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.sankuai.dealuser.price.display.api.enums.PromoTypeEnum;
import com.sankuai.dealuser.price.display.api.enums.PromotionExplanatoryTagEnum;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dz.product.detail.RequestCustomParamEnum;
import com.sankuai.dzshoppingguide.product.detail.application.constants.LionConstants;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.cepinpool.dto.*;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.ProductCostEffectivePriceFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.dto.ProductPriceReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.model.MaterialInfoDO;
import com.sankuai.nibmkt.promotion.api.common.enums.PromotionPropertyEnum;
import com.sankuai.nibmkt.promotion.api.common.enums.daozong.GroupStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.jetbrains.annotations.Nullable;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;


/**
 * @Author: guangyujie
 * @Date: 2025/3/13 16:42
 */
@Fetcher(
        previousLayerDependencies = {ProductCostEffectivePriceFetcher.class}
)
@Slf4j
public class CostEffectivePinPoolFetcher extends NormalFetcherContext<CostEffectivePinTuan> {

    public static final String RULE_INFO_KEY = "shareHelpRuleDetail";

    @Override
    protected CompletableFuture<CostEffectivePinTuan> doFetch() {
        ProductPriceReturnValue costEffectivePrice = getDependencyResult(ProductCostEffectivePriceFetcher.class);
        PriceDisplayDTO cePinTuanPrice = Optional.ofNullable(costEffectivePrice)
                .map(ProductPriceReturnValue::getPriceDisplayDTO).orElse(null);
        if (cePinTuanPrice == null) {
            return CompletableFuture.completedFuture(null);
        }
        return CompletableFuture.completedFuture(fillCostEffectivePinTuanParams(cePinTuanPrice));
    }

    private final CostEffectivePinTuan costEffectivePinTuan = new CostEffectivePinTuan();

    private CostEffectivePinTuan fillCostEffectivePinTuanParams(PriceDisplayDTO cePinTuanPrice) {
        if (Objects.isNull(cePinTuanPrice)) {
            return null;
        }
        if (CollectionUtils.isEmpty(cePinTuanPrice.getUsedPromos())) {
            return null;
        }
        CePinTuanPassParamConfig pinTuanPassParamConfig = Lion.getBean(LionConstants.COST_EFFECTIVE_PIN_TUAN_CONFIG, CePinTuanPassParamConfig.class);
        String sceneTypeStr = request.getCustomParam(RequestCustomParamEnum.sceneType);
        costEffectivePinTuan.setSceneType(NumberUtils.toInt(StringUtils.isNotBlank(sceneTypeStr) ? sceneTypeStr : "0", 0));
        costEffectivePinTuan.setPinTuanActivityId(request.getCustomParam(RequestCustomParamEnum.pintuanActivityId));
        costEffectivePinTuan.setShareToken(request.getCustomParam(RequestCustomParamEnum.orderGroupId));
        costEffectivePinTuan.setPinTuanPassParamConfig(pinTuanPassParamConfig);
        List<PromoDTO> promoDTOS = cePinTuanPrice.getUsedPromos();
        // 是否含有拼团优惠
        PromoDTO promoDTO = promoDTOS.stream().filter(this::matchPinTuan).findFirst().orElse(null);
        costEffectivePinTuan.setCePinTuanScene(promoDTO != null);
        // 获取促销ID
        if (Objects.nonNull(promoDTO) && Objects.nonNull(promoDTO.getIdentity())) {
            costEffectivePinTuan.setPromotionId(promoDTO.getIdentity().getPromoId());
        }

        // 获取拼团状态
        String groupStatusStr = getPromoOtherInfo(promoDTOS, PromotionPropertyEnum.GROUP_STATUS);
        if (StringUtils.isNotEmpty(groupStatusStr)) {
            costEffectivePinTuan.setPinTuanOpened(String.valueOf(GroupStatusEnum.PROCESSING.getValue()).equals(groupStatusStr));
        }

        String shareHelpBaseInfoStr = getPromoOtherInfo(promoDTOS, PromotionPropertyEnum.SHARE_HELP_BASE_INFO);
        ShareHelpBaseInfo shareHelpBaseInfo;
        if (StringUtils.isNotEmpty(shareHelpBaseInfoStr)) {
            shareHelpBaseInfo = JSON.parseObject(shareHelpBaseInfoStr, ShareHelpBaseInfo.class);
            costEffectivePinTuan.setGroupSuccCountMin(shareHelpBaseInfo.getGroupSuccCountMin());
        }

        String shareHelpInfoListStr = getPromoOtherInfo(promoDTOS, PromotionPropertyEnum.SHARE_HELP_INFO_LIST);
        List<ShareHelpInfoList> shareHelpInfoList = null;
        if (StringUtils.isNotEmpty(shareHelpInfoListStr)) {
            shareHelpInfoList = JsonUtils.fromJson(shareHelpInfoListStr, new TypeReference<List<ShareHelpInfoList>>() {
            });
        }
        List<String> avatars = Lists.newArrayList();
        if (Objects.nonNull(shareHelpInfoList)) {
            ShareHelpInfoList shareHelpInfo = shareHelpInfoList.get(0);
            costEffectivePinTuan.setExpireTime(CollectionUtils.isNotEmpty(shareHelpInfo.getShareRecordUserInfos()) ? shareHelpInfo.getShareRecordUserInfos().get(0).getExpireTime() : null);
            costEffectivePinTuan.setHelpSuccCountMin(shareHelpInfo.getHelpSuccCountMin());

            // 主客态
            costEffectivePinTuan.setActivePinTuan(isActivePinTuan(shareHelpInfo));
            // 已拼&待拼
            costEffectivePinTuan.setInPinTuan(isInPinTuan(shareHelpInfo));
            costEffectivePinTuan.setShareToken(shareHelpInfo.getShareToken());
            costEffectivePinTuan.setHasHelpCount(shareHelpInfo.getHasHelpCount());
            CollectionUtils.emptyIfNull(shareHelpInfo.getShareRecordUserInfos())
                    .stream()
                    .map(s -> s.getUserDisplayContent().getAvatar()).filter(StringUtils::isNotEmpty)
                    .forEach(avatars::add);
            CollectionUtils.emptyIfNull(shareHelpInfo.getHelpRecordUserInfos())
                    .stream()
                    .map(s -> s.getUserDisplayContent().getAvatar())
                    .filter(StringUtils::isNotEmpty).forEach(avatars::add);
            costEffectivePinTuan.setAvatars(fillAvatars(avatars));
        }

        String materialInfoStr = getPromoOtherInfo(promoDTOS, PromotionPropertyEnum.MATERIAL_INFO_LIST);
        List<MaterialInfoDO> materialInfoDOList = null;
        if (StringUtils.isNotEmpty(materialInfoStr)) {
            materialInfoDOList = JsonUtils.fromJson(materialInfoStr, new TypeReference<List<MaterialInfoDO>>() {
            });
        }
        costEffectivePinTuan.setRuleInfoPOS(getRuleInfoPOS(materialInfoDOList));

        // 获取拼团活动ID
        String pinTuanActivityId = getPromoOtherInfo(promoDTOS, PromotionPropertyEnum.PINTUAN_ACTIVITY_ID);
        costEffectivePinTuan.setPinTuanActivityId(pinTuanActivityId);

        // 获取是否新客
        String limitNewCustomJoin = getPromoOtherInfo(promoDTOS, PromotionPropertyEnum.LIMIT_NEW_CUSTOMER_JOIN);
        costEffectivePinTuan.setLimitNewCustomJoin("true".equals(limitNewCustomJoin));
        return costEffectivePinTuan;
    }

    private static @Nullable String getPromoOtherInfo(List<PromoDTO> promoDTOS, PromotionPropertyEnum groupStatus) {
        return promoDTOS.stream()
                .filter(pro -> MapUtils.isNotEmpty(pro.getPromotionOtherInfoMap())
                        && pro.getPromotionOtherInfoMap().containsKey(groupStatus.getValue()))
                .map(promo -> promo.getPromotionOtherInfoMap().get(groupStatus.getValue()))
                .findFirst().orElse(null);
    }

    private boolean matchPinTuan(PromoDTO usedPromo) {
        if (CollectionUtils.isEmpty(usedPromo.getPromotionExplanatoryTags())) {
            return false;
        }
        return PromoTypeEnum.NORMAL_PROMO.getType() == usedPromo.getIdentity().getPromoType() && usedPromo.getPromotionExplanatoryTags().contains(PromotionExplanatoryTagEnum.PIN_TUAN_DEDUCTION.getCode());
    }

    /**
     * 判断主客态
     */
    private boolean isActivePinTuan(ShareHelpInfoList shareHelpInfoList) {
        if (Objects.isNull(shareHelpInfoList.getShareHelpType4User())) {
            return false;
        }

        // 主态
        return 1 == shareHelpInfoList.getShareHelpType4User();
    }

    /**
     * 是不是正在拼团中
     */
    private boolean isInPinTuan(ShareHelpInfoList shareHelpInfoList) {
        if (Objects.isNull(shareHelpInfoList.getShareHelpType4User())) {
            return false;
        }

        // 主态+客态已拼
        return Lists.newArrayList(1, 2).contains(shareHelpInfoList.getShareHelpType4User());
    }

    private List<String> fillAvatars(List<String> avatars) {
        int needMemberCount = costEffectivePinTuan.getNeedMemberCount();
        if (needMemberCount > 0) {
            for (int i = 0; i < needMemberCount; i++) {
                avatars.add(costEffectivePinTuan.getPinTuanPassParamConfig().getDefaultAvatar());
            }
        }
        return avatars;
    }

    private List<RuleInfoPO> getRuleInfoPOS(List<MaterialInfoDO> materialInfoDO) {
        if (materialInfoDO == null) {
            return new ArrayList<>();
        }
        return materialInfoDO
                .stream()
                .filter(m -> RULE_INFO_KEY.equals(m.getFieldKey()) && StringUtils.isNotEmpty(m.getFieldName()) && StringUtils.isNotEmpty(m.getFieldValue()))
                .sorted((o1, o2) -> {
                    try {
                        long s1 = NumberUtils.isParsable(o1.getFieldDesc()) ? Long.parseLong(o1.getFieldDesc()) : Long.MAX_VALUE;
                        long s2 = NumberUtils.isParsable(o2.getFieldDesc()) ? Long.parseLong(o2.getFieldDesc()) : Long.MAX_VALUE;
                        if (s1 < s2) {
                            return -1;
                        } else if (s1 == s2) {
                            return 0;
                        }
                        return 1;
                    } catch (Exception e) {
                        log.error("unknown error", e);
                    }
                    return 0;
                })
                .map(m -> {
                    RuleInfoPO ruleInfo = new RuleInfoPO();
                    ruleInfo.setTitle(m.getFieldName());
                    ruleInfo.setContent(m.getFieldValue());
                    return ruleInfo;
                }).collect(Collectors.toList());

    }


}
