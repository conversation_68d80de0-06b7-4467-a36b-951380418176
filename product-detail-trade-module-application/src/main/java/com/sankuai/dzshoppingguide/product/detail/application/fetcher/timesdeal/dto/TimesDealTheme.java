package com.sankuai.dzshoppingguide.product.detail.application.fetcher.timesdeal.dto;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommendgoods.dtos.ProductM;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/14 13:20
 */
@EqualsAndHashCode(callSuper = true)
@Getter
@Builder
public class TimesDealTheme extends FetcherReturnValueDTO {
    private List<ProductM> productMS;
}
