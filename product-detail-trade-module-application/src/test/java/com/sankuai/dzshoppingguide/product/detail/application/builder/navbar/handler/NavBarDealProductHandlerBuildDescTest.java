package com.sankuai.dzshoppingguide.product.detail.application.builder.navbar.handler;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.general.product.query.center.client.dto.PriceDTO;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * Test cases for NavBarDealProductHandler#buildDesc method
 */
@ExtendWith(MockitoExtension.class)
public class NavBarDealProductHandlerBuildDescTest {

    @InjectMocks
    private NavBarDealProductHandler handler;

    @Mock
    private ProductDetailPageRequest request;

    @Mock
    private ProductBaseInfo baseInfo;

    @Mock
    private PriceDTO price;

    /**
     * Test buildDesc with MT_APP client type and valid price
     */
    @Test
    public void testBuildDesc_MtApp_WithValidPrice() throws Throwable {
        // arrange
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.MT_APP);
        when(baseInfo.getPrice()).thenReturn(price);
        when(price.getSalePrice()).thenReturn("99.9");
        // act
        String result = handler.buildDesc(request, baseInfo);
        // assert
        assertEquals("仅售99.9元", result);
    }

    /**
     * Test buildDesc with MT_WX client type and valid price
     */
    @Test
    public void testBuildDesc_MtWx_WithValidPrice() throws Throwable {
        // arrange
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.MT_WX);
        when(baseInfo.getPrice()).thenReturn(price);
        when(price.getSalePrice()).thenReturn("100.0");
        // act
        String result = handler.buildDesc(request, baseInfo);
        // assert
        assertEquals("仅售100.0元", result);
    }

    /**
     * Test buildDesc with MT_APP client type and null price object
     */
    @Test
    public void testBuildDesc_MtApp_WithNullPrice() throws Throwable {
        // arrange
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.MT_APP);
        when(baseInfo.getPrice()).thenReturn(null);
        // act
        String result = handler.buildDesc(request, baseInfo);
        // assert
        assertEquals("仅售0.0元", result);
    }

    /**
     * Test buildDesc with MT_WX client type and null sale price
     */
    @Test
    public void testBuildDesc_MtWx_WithNullSalePrice() throws Throwable {
        // arrange
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.MT_WX);
        when(baseInfo.getPrice()).thenReturn(price);
        when(price.getSalePrice()).thenReturn(null);
        // act
        String result = handler.buildDesc(request, baseInfo);
        // assert
        assertEquals("仅售0.0元", result);
    }

    /**
     * Test buildDesc with other client type
     */
    @Test
    public void testBuildDesc_OtherClientType() throws Throwable {
        // arrange
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.DP_APP);
        // act
        String result = handler.buildDesc(request, baseInfo);
        // assert
        assertEquals(StringUtils.EMPTY, result);
    }
}
