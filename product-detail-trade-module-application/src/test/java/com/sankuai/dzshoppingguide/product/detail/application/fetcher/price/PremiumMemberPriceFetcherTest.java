package com.sankuai.dzshoppingguide.product.detail.application.fetcher.price;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.membercard.card.entrance.MemberEntranceResult;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.dto.ProductPriceReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.defaultselect.SkuDefaultSelect;

import java.util.concurrent.CompletableFuture;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class PremiumMemberPriceFetcherTest {

    @Mock
    private MemberEntranceResult memberEntranceResult;

    @Mock
    private SkuDefaultSelect skuDefaultSelect;

    /**
     * 包装类，用于控制测试行为
     */
    static class PremiumMemberPriceFetcherWrapper extends PremiumMemberPriceFetcher {

        private final MemberEntranceResult memberEntranceResult;

        private final SkuDefaultSelect skuDefaultSelect;

        private final CompletableFuture<ProductPriceReturnValue> normalPriceFuture;

        private final CompletableFuture<ProductPriceReturnValue> memberPriceFuture;

        public PremiumMemberPriceFetcherWrapper(MemberEntranceResult memberEntranceResult, SkuDefaultSelect skuDefaultSelect, CompletableFuture<ProductPriceReturnValue> normalPriceFuture, CompletableFuture<ProductPriceReturnValue> memberPriceFuture) {
            this.memberEntranceResult = memberEntranceResult;
            this.skuDefaultSelect = skuDefaultSelect;
            this.normalPriceFuture = normalPriceFuture;
            this.memberPriceFuture = memberPriceFuture;
        }

        @Override
        protected CompletableFuture<ProductPriceReturnValue> doFetch() {
            if (memberEntranceResult == null || !memberEntranceResult.isHasEntrance()) {
                return CompletableFuture.completedFuture(null);
            }
            return CompletableFuture.allOf(normalPriceFuture, memberPriceFuture).thenApply(aVoid -> {
                ProductPriceReturnValue result = new ProductPriceReturnValue();
                result.setOriginPriceDisplayDTO(normalPriceFuture.join().getPriceDisplayDTO());
                result.setPriceDisplayDTO(memberPriceFuture.join().getPriceDisplayDTO());
                return result;
            });
        }
    }

    /**
     * 测试当 MemberEntranceResult 没有入口时，doFetch() 返回 null
     */
    @Test
    public void testDoFetchWhenNoEntrance() throws Throwable {
        // arrange
        when(memberEntranceResult.isHasEntrance()).thenReturn(false);
        PremiumMemberPriceFetcherWrapper fetcher = new PremiumMemberPriceFetcherWrapper(memberEntranceResult, null, null, null);
        // act
        CompletableFuture<ProductPriceReturnValue> result = fetcher.doFetch();
        // assert
        assertNull(result.get());
        verify(memberEntranceResult).isHasEntrance();
    }

    /**
     * 测试当 MemberEntranceResult 有入口时，doFetch() 返回合并后的价格结果
     */
    @Test
    public void testDoFetchWhenHasEntrance() throws Throwable {
        // arrange
        when(memberEntranceResult.isHasEntrance()).thenReturn(true);
        ProductPriceReturnValue normalPriceReturnValue = new ProductPriceReturnValue();
        PriceDisplayDTO normalPriceDTO = new PriceDisplayDTO();
        normalPriceReturnValue.setPriceDisplayDTO(normalPriceDTO);
        ProductPriceReturnValue memberPriceReturnValue = new ProductPriceReturnValue();
        PriceDisplayDTO memberPriceDTO = new PriceDisplayDTO();
        memberPriceReturnValue.setPriceDisplayDTO(memberPriceDTO);
        PremiumMemberPriceFetcherWrapper fetcher = new PremiumMemberPriceFetcherWrapper(memberEntranceResult, skuDefaultSelect, CompletableFuture.completedFuture(normalPriceReturnValue), CompletableFuture.completedFuture(memberPriceReturnValue));
        // act
        CompletableFuture<ProductPriceReturnValue> result = fetcher.doFetch();
        // assert
        ProductPriceReturnValue mergedResult = result.get();
        assertNotNull(mergedResult);
        assertEquals(normalPriceDTO, mergedResult.getOriginPriceDisplayDTO());
        assertEquals(memberPriceDTO, mergedResult.getPriceDisplayDTO());
        verify(memberEntranceResult).isHasEntrance();
    }

    /**
     * 测试当获取依赖结果时抛出异常，doFetch() 抛出异常
     */
    @Test
    public void testDoFetchWhenDependencyThrowsException() throws Throwable {
        // arrange
        PremiumMemberPriceFetcherWrapper fetcher = new PremiumMemberPriceFetcherWrapper(null, null, null, null);
        // act & assert
        CompletableFuture<ProductPriceReturnValue> future = fetcher.doFetch();
        assertDoesNotThrow(() -> assertNull(future.get()));
    }
}
