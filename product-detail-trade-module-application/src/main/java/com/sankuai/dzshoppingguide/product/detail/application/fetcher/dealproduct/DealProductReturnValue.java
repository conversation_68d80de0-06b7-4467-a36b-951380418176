package com.sankuai.dzshoppingguide.product.detail.application.fetcher.dealproduct;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import com.sankuai.dztheme.deal.res.DealProductResult;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @create 2025/5/28 11:36
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DealProductReturnValue extends FetcherReturnValueDTO {
    private DealProductResult dealProductResult;
}
