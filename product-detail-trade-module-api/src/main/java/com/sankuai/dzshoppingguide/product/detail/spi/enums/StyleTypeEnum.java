package com.sankuai.dzshoppingguide.product.detail.spi.enums;

/**
 * <AUTHOR>
 * @date 2023/5/11
 */
public enum StyleTypeEnum {

    NORMAL(0, "普通样式"),
    SHOPPING_CART(1,"加入购物车样式"),
    CAPSULE(2, "胶囊样式"),
    PINTUAN_WAITING_TWO_BUTTONS(3, "待开团双按钮"),
    PINTUAN_WEAK_ACTIVE_TWO_BUTTONS(4, "弱化主态双按钮"),
    PINTUAN_ENHANCE_PASSIVE_JOIN_SINGLE_BUTTON(5, "强化&弱化客态待参团"),
    PINTUAN_ENHANCE_SINGLE_BUTTON(6, "强化主态单按钮"),
    BUY_BUTTON_REPLACE_SHARE(7, "购买按钮替换左侧分享按钮")
    ;


    public int code;
    public String desc;

    private StyleTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
