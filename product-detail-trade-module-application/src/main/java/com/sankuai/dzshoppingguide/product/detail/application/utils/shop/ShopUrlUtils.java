package com.sankuai.dzshoppingguide.product.detail.application.utils.shop;

import com.dianping.lion.client.Lion;
import com.google.common.collect.Maps;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapper;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.jetbrains.annotations.NotNull;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Map;

@Slf4j
public class ShopUrlUtils {

    private static final String MT_MINI_PREFIX = "/index/pages/h5/h5?f_token=1&weburl=";
    private static final String DP_MINI_PREFIX = "/pages/webview/webview?url=";
    private static final String SHOP_LIST_URL_SCHEMA = "shop.list.url.schema";
    private static final String SHOP_DETAIL_URL_SCHEMA = "shop.detail.url.schema";

    private static final String MMC_USE = "mmcuse";

    private static final String MMC_INFLATE = "mmcinflate";

    private static final String MMC_BUY = "mmcbuy";

    private static final String MMC_FREE = "mmcfree";

    private static final String OFFLINE_CODE = "offlinecode";


    public static String getShopDetailUrl(@NotNull final ShopIdMapper shopIdMapper,
                                          @NotNull final ProductDetailPageRequest pageRequest) {
        long shopId = pageRequest.getClientTypeEnum().isMtClientType() ?
                shopIdMapper.getMtBestShopId() : shopIdMapper.getDpBestShopId();
        return String.format("%s%s", getPrefix(pageRequest), getDetailUrl(shopId, pageRequest));
    }

    public static String getShopListUrl(ProductDetailPageRequest pageRequest) {
        return String.format("%s%s", getPrefix(pageRequest), getListUrl(pageRequest));
    }

    private static String getPrefix(ProductDetailPageRequest pageRequest) {
        if (pageRequest.getClientTypeEnum() == ClientTypeEnum.DP_XCX) {
            return DP_MINI_PREFIX;
        }
        if (pageRequest.getClientTypeEnum() == ClientTypeEnum.MT_XCX) {
            return MT_MINI_PREFIX;
        }
        return "";
    }

    private static String getDetailUrl(final long shopId,
                                       final ProductDetailPageRequest pageRequest) {
        ShopConfig config = Lion.getBean("com.sankuai.dzshoppingguide.detail.commonmodule", SHOP_DETAIL_URL_SCHEMA, ShopConfig.class);
        if (config == null) {
            return "";
        }
        if (!usingBackendUrl(pageRequest)) {
            return "";
        }
        String pageSchema = getPageUrl(pageRequest, config);
        String url = buildDetailParams(pageRequest, shopId, pageSchema);
        return pageRequest.getClientTypeEnum().isInWxXCX() ?  urlEncode(url) : url;
    }

    private static String getListUrl(ProductDetailPageRequest pageRequest) {
        ShopConfig config = Lion.getBean("com.sankuai.dzu.tpbase.dztgdetailweb", SHOP_LIST_URL_SCHEMA, ShopConfig.class);
        if (config == null) {
            return "";
        }
        if (!usingBackendUrl(pageRequest)) {
            return "";
        }
        String page = getPageUrl(pageRequest, config);
        String params = buildListParams(pageRequest, pageRequest.getPoiId(), config);
        String url = String.format("%s%s", page, params);
        return pageRequest.getClientTypeEnum().isInWxXCX() ?  urlEncode(url) : url;
    }

    private static boolean usingBackendUrl(ProductDetailPageRequest pageRequest) {
        return pageRequest.getClientTypeEnum().isInApp() || pageRequest.getClientTypeEnum().isInWxXCX();
    }

    private static String buildDetailParams(ProductDetailPageRequest pageRequest, long shopId, String schema) {
        if (StringUtils.isBlank(schema)) {
            return "";
        }
        Map<String, String> paramsMap = Maps.newHashMap();
        paramsMap.put("shopid", String.valueOf(shopId));
        paramsMap.put("channelType", getStr(pageRequest.getPageSource()));
        paramsMap.put("mmcinflate", getFromCustomParams(MMC_INFLATE, pageRequest));
        paramsMap.put("mmcuse", getFromCustomParams(MMC_USE, pageRequest));
        paramsMap.put("mmcbuy", getFromCustomParams(MMC_BUY, pageRequest));
        paramsMap.put("mmcfree", getFromCustomParams(MMC_FREE, pageRequest));
        paramsMap.put("offlinecode", getFromCustomParams(OFFLINE_CODE, pageRequest));
        String url = "";
        for (Map.Entry<String, String> entry : paramsMap.entrySet()) {
            url = StringUtils.replace(schema, String.format("{%s}", entry.getKey()), getStr(entry.getValue()));
            schema = url;
        }
        return url;
    }

    private static String buildListParams(ProductDetailPageRequest pageRequest, Long shopId, ShopConfig config) {
        String schema = config.getParamsSchema();
        if (StringUtils.isBlank(schema)) {
            return "";
        }
        Map<String, String> paramsMap = Maps.newHashMap();
        paramsMap.put("dealid", String.valueOf(pageRequest.getProductId()));
        paramsMap.put("poiid", String.valueOf(shopId));
        paramsMap.put("frompage", getStr(pageRequest.getPageSource()));
        paramsMap.put("cityId", "");
        paramsMap.put("locatedCityId", String.valueOf(pageRequest.getGpsCityId()));
        paramsMap.put("lat", String.valueOf(pageRequest.getUserLat()));
        paramsMap.put("lng", String.valueOf(pageRequest.getUserLng()));
        paramsMap.put("mmcinflate", getFromCustomParams(MMC_INFLATE, pageRequest));
        paramsMap.put("mmcuse", getFromCustomParams(MMC_USE, pageRequest));
        paramsMap.put("mmcbuy", getFromCustomParams(MMC_BUY, pageRequest));
        paramsMap.put("mmcfree", getFromCustomParams(MMC_FREE, pageRequest));
        paramsMap.put("offlinecode", getFromCustomParams(OFFLINE_CODE, pageRequest));
        String url = "";
        for (Map.Entry<String, String> entry : paramsMap.entrySet()) {
            url = StringUtils.replace(schema, String.format("{%s}", entry.getKey()), StringUtils.isNotBlank(entry.getValue()) ? entry.getValue() : "");
            schema = url;
        }
        return url;
    }

    private static String getStr(String value) {
        return StringUtils.isBlank(value) ? "" : value;
    }

    private static String getFromCustomParams(String key, ProductDetailPageRequest pageRequest) {
        if (pageRequest.getCustomParam() == null || MapUtils.isEmpty(pageRequest.getCustomParam().getCustomParams())) {
            return "";
        }
        String value = pageRequest.getCustomParam().getCustomParams().get(key);
        return getStr(value);
    }

    private static String getPageUrl(ProductDetailPageRequest pageRequest, ShopConfig config) {
        boolean isMt = pageRequest.getClientTypeEnum().isMtClientType();
        boolean isApp = pageRequest.getClientTypeEnum().isInApp();
        return isApp ? (isMt ? config.getMtAppUrl() : config.getDpAppUrl()) : (isMt ? config.getMtMiniApp() : config.getDpMiniApp());
    }

    public static String urlEncode(String origin) {
        try {
            return URLEncoder.encode(origin, StandardCharsets.UTF_8.name());
        } catch (UnsupportedEncodingException e) {
            log.error("urlEncode error, origin={}", origin, e);
        }
        return "";
    }

    @Data
    private static class ShopConfig {
        private String paramsSchema;

        private String mtAppUrl;
        private String dpAppUrl;
        private String mtMiniApp;
        private String dpMiniApp;
    }
}
