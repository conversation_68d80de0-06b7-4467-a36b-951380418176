package com.sankuai.dzshoppingguide.product.detail.application.utils;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.cepinpool.dto.CostEffectivePinTuan;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class CostEffectivePinTuanUtilsPinTuanOpenedTest {

    @Mock
    private CostEffectivePinTuan mockCostEffectivePinTuan;

    public static boolean pinTuanOpened(CostEffectivePinTuan costEffectivePinTuan) {
        return costEffectivePinTuan.isCePinTuanScene() && costEffectivePinTuan.isPinTuanOpened();
    }

    /**
     * Test case where both isCePinTuanScene and isPinTuanOpened return true
     */
    @Test
    public void testPinTuanOpenedBothTrue() {
        // arrange
        CostEffectivePinTuan costEffectivePinTuan = mock(CostEffectivePinTuan.class);
        when(costEffectivePinTuan.isCePinTuanScene()).thenReturn(true);
        when(costEffectivePinTuan.isPinTuanOpened()).thenReturn(true);
        // act
        boolean result = CostEffectivePinTuanUtils.pinTuanOpened(costEffectivePinTuan);
        // assert
        assertTrue(result);
    }

    /**
     * Test case where isCePinTuanScene is true but isPinTuanOpened is false
     */
    @Test
    public void testPinTuanOpenedSceneTrueOpenedFalse() {
        // arrange
        CostEffectivePinTuan costEffectivePinTuan = mock(CostEffectivePinTuan.class);
        when(costEffectivePinTuan.isCePinTuanScene()).thenReturn(true);
        when(costEffectivePinTuan.isPinTuanOpened()).thenReturn(false);
        // act
        boolean result = CostEffectivePinTuanUtils.pinTuanOpened(costEffectivePinTuan);
        // assert
        assertFalse(result);
    }

    /**
     * Test case where isCePinTuanScene is false but isPinTuanOpened is true
     */
    @Test
    public void testPinTuanOpenedSceneFalseOpenedTrue() {
        // arrange
        CostEffectivePinTuan costEffectivePinTuan = mock(CostEffectivePinTuan.class);
        when(costEffectivePinTuan.isCePinTuanScene()).thenReturn(false);
        // act
        boolean result = CostEffectivePinTuanUtils.pinTuanOpened(costEffectivePinTuan);
        // assert
        assertFalse(result);
    }

    /**
     * Test case where both isCePinTuanScene and isPinTuanOpened return false
     */
    @Test
    public void testPinTuanOpenedBothFalse() {
        // arrange
        CostEffectivePinTuan costEffectivePinTuan = mock(CostEffectivePinTuan.class);
        when(costEffectivePinTuan.isCePinTuanScene()).thenReturn(false);
        // act
        boolean result = CostEffectivePinTuanUtils.pinTuanOpened(costEffectivePinTuan);
        // assert
        assertFalse(result);
    }

    /**
     * Test case where the costEffectivePinTuan parameter is null
     */
    @Test
    public void testPinTuanOpenedNullParameter() {
        // act & assert
        assertThrows(NullPointerException.class, () -> CostEffectivePinTuanUtils.pinTuanOpened(null));
    }

    /**
     * Test scenario: Input parameter is null
     * Expected: Should return false
     */
    @Test
    @DisplayName("Should return false when input is null")
    public void testIsCePinTuaScene_NullInput() {
        // arrange
        CostEffectivePinTuan costEffectivePinTuan = null;
        // act
        boolean result = CostEffectivePinTuanUtils.isCePinTuaScene(costEffectivePinTuan);
        // assert
        assertFalse(result, "Should return false when input is null");
    }

    /**
     * Test scenario: CostEffectivePinTuan with isCePinTuanScene set to true
     * Expected: Should return true
     */
    @Test
    @DisplayName("Should return true when isCePinTuanScene is true")
    public void testIsCePinTuaScene_WithTrueScene() {
        // arrange
        CostEffectivePinTuan costEffectivePinTuan = mock(CostEffectivePinTuan.class);
        when(costEffectivePinTuan.isCePinTuanScene()).thenReturn(true);
        // act
        boolean result = CostEffectivePinTuanUtils.isCePinTuaScene(costEffectivePinTuan);
        // assert
        assertTrue(result, "Should return true when isCePinTuanScene is true");
    }

    /**
     * Test scenario: CostEffectivePinTuan with isCePinTuanScene set to false
     * Expected: Should return false
     */
    @Test
    @DisplayName("Should return false when isCePinTuanScene is false")
    public void testIsCePinTuaScene_WithFalseScene() {
        // arrange
        CostEffectivePinTuan costEffectivePinTuan = mock(CostEffectivePinTuan.class);
        when(costEffectivePinTuan.isCePinTuanScene()).thenReturn(false);
        // act
        boolean result = CostEffectivePinTuanUtils.isCePinTuaScene(costEffectivePinTuan);
        // assert
        assertFalse(result, "Should return false when isCePinTuanScene is false");
    }

    /**
     * Test case: Input is null
     * Expected: Should return false
     */
    @Test
    @DisplayName("Should return false when input is null")
    public void testEnhancedStyle_NullInput() throws Throwable {
        // arrange
        CostEffectivePinTuan costEffectivePinTuan = null;
        // act
        boolean result = CostEffectivePinTuanUtils.enhancedStyle(costEffectivePinTuan);
        // assert
        assertFalse(result);
    }

    /**
     * Test case: SceneType is 56
     * Expected: Should return true
     */
    @Test
    @DisplayName("Should return true when sceneType is 56")
    public void testEnhancedStyle_SceneType56() throws Throwable {
        // arrange
        when(mockCostEffectivePinTuan.getSceneType()).thenReturn(56);
        // act
        boolean result = CostEffectivePinTuanUtils.enhancedStyle(mockCostEffectivePinTuan);
        // assert
        assertTrue(result);
    }

    /**
     * Test case: SceneType is not 56
     * Expected: Should return false
     */
    @Test
    @DisplayName("Should return false when sceneType is not 56")
    public void testEnhancedStyle_SceneTypeNot56() throws Throwable {
        // arrange
        when(mockCostEffectivePinTuan.getSceneType()).thenReturn(55);
        // act
        boolean result = CostEffectivePinTuanUtils.enhancedStyle(mockCostEffectivePinTuan);
        // assert
        assertFalse(result);
    }

    /**
     * Test case: SceneType is null
     * Expected: Should return false
     */
    @Test
    @DisplayName("Should return false when sceneType is null")
    public void testEnhancedStyle_SceneTypeNull() throws Throwable {
        // arrange
        // Corrected to return 0 instead of null
        when(mockCostEffectivePinTuan.getSceneType()).thenReturn(0);
        // act
        boolean result = CostEffectivePinTuanUtils.enhancedStyle(mockCostEffectivePinTuan);
        // assert
        assertFalse(result);
    }

    /**
     * Test case: Using real object with sceneType 56
     * Expected: Should return true
     */
    @Test
    @DisplayName("Should return true when using real object with sceneType 56")
    public void testEnhancedStyle_RealObject_SceneType56() throws Throwable {
        // arrange
        CostEffectivePinTuan costEffectivePinTuan = new CostEffectivePinTuan();
        costEffectivePinTuan.setSceneType(56);
        // act
        boolean result = CostEffectivePinTuanUtils.enhancedStyle(costEffectivePinTuan);
        // assert
        assertTrue(result);
    }
}
