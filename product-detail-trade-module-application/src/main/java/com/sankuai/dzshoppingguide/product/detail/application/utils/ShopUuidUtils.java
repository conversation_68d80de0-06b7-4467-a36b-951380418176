package com.sankuai.dzshoppingguide.product.detail.application.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
@Slf4j
public final class ShopUuidUtils {

    private ShopUuidUtils() {
    }

    private static final Pattern SHOP_ID_PATTERN = Pattern.compile("(?<=shopid=)(\\w+)");

    public static String filterUrl(String url, String dpShopUuid) {
        if (StringUtils.isBlank(url) || url.contains("shopuuid")) {
            return url;
        }

        Matcher m = SHOP_ID_PATTERN.matcher(url);
        if (m.find()) {
            if (StringUtils.isNotBlank(dpShopUuid)) {
                url += "&shopuuid=" + dpShopUuid;
            }
        }

        return url;
    }
}
