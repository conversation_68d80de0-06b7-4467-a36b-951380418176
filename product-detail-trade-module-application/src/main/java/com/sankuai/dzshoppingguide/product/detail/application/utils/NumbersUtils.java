package com.sankuai.dzshoppingguide.product.detail.application.utils;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024-01-16
 * @desc 数字类型工具类
 */
public class NumbersUtils {
    public static boolean greaterThanZero(Integer num) {
        return Objects.nonNull(num) && num > 0;
    }

    public static boolean greaterThanZero(Long num) {
        return Objects.nonNull(num) && num > 0L;
    }

    public static boolean lessThanAndEqualZero(Integer num) {
        return Objects.isNull(num) || num <= 0;
    }

    public static boolean lessThanAndEqualZero(Long num) {
        return Objects.isNull(num) || num <= 0L;
    }

    public static Long toLong(Long value) {
        return Objects.isNull(value) ? 0L : value;
    }

    public static double toDouble(Double value) {
        return Objects.isNull(value) ? 0.0 : value;
    }
}
