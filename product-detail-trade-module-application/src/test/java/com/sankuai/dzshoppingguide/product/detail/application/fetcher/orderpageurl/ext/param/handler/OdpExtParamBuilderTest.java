package com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.ext.param.handler;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.google.common.collect.Lists;
import com.sankuai.dz.product.detail.RequestCustomParamEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.enums.OrderPageExtParamEnums;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.orderpageurl.ext.param.request.ExtParamBuilderRequest;
import com.sankuai.dzshoppingguide.product.detail.application.utils.GsonUtils;
import java.util.HashMap;
import java.util.Map;
import org.apache.commons.collections4.MapUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * Unit tests for {@link OdpExtParamBuilder} buildKey() method
 */
@DisplayName("OdpExtParamBuilder buildKey() Tests")
@ExtendWith(MockitoExtension.class)
class OdpExtParamBuilderTest {

    @InjectMocks
    private OdpExtParamBuilder odpExtParamBuilder;

    @Mock
    private ProductDetailPageRequest request;

    @Mock
    private ExtParamBuilderRequest builderRequest;

    /**
     * Test that buildKey() returns the correct enum value 'dealextparam'
     *
     * Scenario: When buildKey() is called, it should always return OrderPageExtParamEnums.dealextparam
     *
     * Expected: Method returns the expected enum value
     */
    @Test
    @DisplayName("should return dealextparam enum when buildKey is called")
    public void testBuildKeyReturnsDealextparamEnum() {
        // arrange
        OdpExtParamBuilder builder = new OdpExtParamBuilder();
        // act
        OrderPageExtParamEnums result = builder.buildKey();
        // assert
        assertNotNull(result, "Returned enum should not be null");
        assertEquals(OrderPageExtParamEnums.dealextparam, result, "buildKey() should return OrderPageExtParamEnums.dealextparam");
        assertSame(OrderPageExtParamEnums.dealextparam, result, "buildKey() should return the same instance of dealextparam enum");
    }

    /**
     * Test that buildKey() returns a non-null value
     *
     * Scenario: Verify the method never returns null
     *
     * Expected: Method returns a non-null enum value
     */
    @Test
    @DisplayName("should never return null when buildKey is called")
    public void testBuildKeyReturnsNonNull() {
        // arrange
        OdpExtParamBuilder builder = new OdpExtParamBuilder();
        // act
        OrderPageExtParamEnums result = builder.buildKey();
        // assert
        assertNotNull(result, "buildKey() should never return null");
    }

    /**
     * Test that buildKey() returns an instance of OrderPageExtParamEnums
     *
     * Scenario: Verify the return type is correct
     *
     * Expected: Method returns an instance of OrderPageExtParamEnums
     */
    @Test
    @DisplayName("should return OrderPageExtParamEnums type when buildKey is called")
    public void testBuildKeyReturnsCorrectType() {
        // arrange
        OdpExtParamBuilder builder = new OdpExtParamBuilder();
        // act
        OrderPageExtParamEnums result = builder.buildKey();
        // assert
        assertTrue(OrderPageExtParamEnums.class.isInstance(result), "buildKey() should return an instance of OrderPageExtParamEnums");
    }

    /**
     * Test when extParam is null
     */
    @Test
    public void testDoBuildExtParamWhenExtParamIsNull() throws Throwable {
        // arrange
        when(request.getCustomParam(RequestCustomParamEnum.extparam)).thenReturn(null);
        // act
        String result = odpExtParamBuilder.doBuildExtParam(request, builderRequest);
        // assert
        assertNull(result);
        verify(request, times(1)).getCustomParam(RequestCustomParamEnum.extparam);
    }

    /**
     * Test when extParam is empty string
     */
    @Test
    public void testDoBuildExtParamWhenExtParamIsEmptyString() throws Throwable {
        // arrange
        when(request.getCustomParam(RequestCustomParamEnum.extparam)).thenReturn("");
        // act
        String result = odpExtParamBuilder.doBuildExtParam(request, builderRequest);
        // assert
        assertNull(result);
        verify(request, times(1)).getCustomParam(RequestCustomParamEnum.extparam);
    }

    /**
     * Test when extParam is valid JSON but empty map
     */
    @Test
    public void testDoBuildExtParamWhenExtParamIsEmptyMap() throws Throwable {
        // arrange
        when(request.getCustomParam(RequestCustomParamEnum.extparam)).thenReturn("{}");
        // act
        String result = odpExtParamBuilder.doBuildExtParam(request, builderRequest);
        // assert
        assertNull(result);
        verify(request, times(1)).getCustomParam(RequestCustomParamEnum.extparam);
    }

    /**
     * Test when extParam contains no REMAIN_EXT_PARAM_KEYS
     */
    @Test
    public void testDoBuildExtParamWhenExtParamContainsNoRemainKeys() throws Throwable {
        // arrange
        when(request.getCustomParam(RequestCustomParamEnum.extparam)).thenReturn("{\"key1\":\"value1\",\"key2\":\"value2\"}");
        // act
        String result = odpExtParamBuilder.doBuildExtParam(request, builderRequest);
        // assert
        assertEquals("{}", result);
        verify(request, times(1)).getCustomParam(RequestCustomParamEnum.extparam);
    }

    /**
     * Test when extParam contains one REMAIN_EXT_PARAM_KEY
     */
    @Test
    public void testDoBuildExtParamWhenExtParamContainsOneRemainKey() throws Throwable {
        // arrange
        when(request.getCustomParam(RequestCustomParamEnum.extparam)).thenReturn("{\"odpflowinfo\":\"value1\",\"key2\":\"value2\"}");
        // act
        String result = odpExtParamBuilder.doBuildExtParam(request, builderRequest);
        // assert
        assertNotNull(result);
        assertTrue(result.contains("\"odpflowinfo\""));
        assertTrue(result.contains("\"value1\""));
        assertFalse(result.contains("\"key2\""));
        verify(request, times(1)).getCustomParam(RequestCustomParamEnum.extparam);
    }

    /**
     * Test when extParam contains some REMAIN_EXT_PARAM_KEYS
     */
    @Test
    public void testDoBuildExtParamWhenExtParamContainsSomeRemainKeys() throws Throwable {
        // arrange
        when(request.getCustomParam(RequestCustomParamEnum.extparam)).thenReturn("{\"odpflowinfo\":\"value1\",\"odpLaunchId\":\"value2\",\"key3\":\"value3\"}");
        // act
        String result = odpExtParamBuilder.doBuildExtParam(request, builderRequest);
        // assert
        assertNotNull(result);
        assertTrue(result.contains("\"odpflowinfo\""));
        assertTrue(result.contains("\"odpLaunchId\""));
        assertTrue(result.contains("\"value1\""));
        assertTrue(result.contains("\"value2\""));
        assertFalse(result.contains("\"key3\""));
        verify(request, times(1)).getCustomParam(RequestCustomParamEnum.extparam);
    }

    /**
     * Test when extParam contains all REMAIN_EXT_PARAM_KEYS
     */
    @Test
    public void testDoBuildExtParamWhenExtParamContainsAllRemainKeys() throws Throwable {
        // arrange
        when(request.getCustomParam(RequestCustomParamEnum.extparam)).thenReturn("{\"odpflowinfo\":\"value1\",\"odpLaunchId\":\"value2\"," + "\"odpFloorId\":\"value3\",\"odpChannelType\":\"value4\"}");
        // act
        String result = odpExtParamBuilder.doBuildExtParam(request, builderRequest);
        // assert
        assertNotNull(result);
        assertTrue(result.contains("\"odpflowinfo\""));
        assertTrue(result.contains("\"odpLaunchId\""));
        assertTrue(result.contains("\"odpFloorId\""));
        assertTrue(result.contains("\"odpChannelType\""));
        assertTrue(result.contains("\"value1\""));
        assertTrue(result.contains("\"value2\""));
        assertTrue(result.contains("\"value3\""));
        assertTrue(result.contains("\"value4\""));
        verify(request, times(1)).getCustomParam(RequestCustomParamEnum.extparam);
    }

    /**
     * Test when extParam contains all REMAIN_EXT_PARAM_KEYS and additional keys
     */
    @Test
    public void testDoBuildExtParamWhenExtParamContainsAllRemainKeysAndMore() throws Throwable {
        // arrange
        when(request.getCustomParam(RequestCustomParamEnum.extparam)).thenReturn("{\"odpflowinfo\":\"value1\",\"odpLaunchId\":\"value2\"," + "\"odpFloorId\":\"value3\",\"odpChannelType\":\"value4\"," + "\"extraKey1\":\"extra1\",\"extraKey2\":\"extra2\"}");
        // act
        String result = odpExtParamBuilder.doBuildExtParam(request, builderRequest);
        // assert
        assertNotNull(result);
        assertTrue(result.contains("\"odpflowinfo\""));
        assertTrue(result.contains("\"odpLaunchId\""));
        assertTrue(result.contains("\"odpFloorId\""));
        assertTrue(result.contains("\"odpChannelType\""));
        assertFalse(result.contains("\"extraKey1\""));
        assertFalse(result.contains("\"extraKey2\""));
        verify(request, times(1)).getCustomParam(RequestCustomParamEnum.extparam);
    }
}
