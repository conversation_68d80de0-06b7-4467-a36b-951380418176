package com.sankuai.dzshoppingguide.product.detail.spi.navbar.vo;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.NavBarItem;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.NavBarShareModuleVO;
import com.sankuai.dzshoppingguide.product.detail.spi.dtos.DetailNavBarStatusInfo;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dz.product.detail.gateway.spi.vo.AbstractModuleVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2025/2/8
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TypeDoc(description = "导航栏模块")
@MobileDo(id = 0x478b)
public class NavigationBarVO extends AbstractModuleVO {
    @Override
    public String getModuleKey() {
        return ModuleKeyConstants.NAVIGATION_BAR;
    }

    @FieldDoc(description = "导航栏信息列表")
    @MobileDo.MobileField(key = 0xdd53)
    private List<NavBarItem> navbar;

    @FieldDoc(description = "商品详情页分享模块")
    @MobileDo.MobileField(key = 0x495a)
    private NavBarShareModuleVO share;

    @FieldDoc(description = "状态信息")
    @MobileDo.MobileField(key = 0x761b)
    private DetailNavBarStatusInfo statusInfo;
}
