package com.sankuai.dzshoppingguide.product.detail.application.utils;

import com.dianping.cat.Cat;
import com.sankuai.dzshoppingguide.product.detail.application.utils.exception.PriceException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.Objects;
import java.util.Set;

@Slf4j
public class PriceHelper {

    private static final DecimalFormat PRICE_FORMAT = new DecimalFormat("0.##");

    public static String priceFormat(double price){
        Cat.logEvent("INVALID_METHOD_1", "com.dianping.mobile.mapi.dztgdetail.helper.PriceHelper.priceFormat(double)");
        return PRICE_FORMAT.format(price);
    }


    //整数的情况下，去掉小数位
    public static String format(BigDecimal price) {
        if (new BigDecimal(price.intValue()).compareTo(price) == 0) { //判断是否为整数
            BigDecimal newPrice = price.setScale(0, RoundingMode.FLOOR);
            return newPrice.toString();
        }
        return price.toString();
    }

    //去掉小数位
    public static String formatPrice(BigDecimal price) {
        return price.stripTrailingZeros().toPlainString();
    }

    public static String dropLastZero(BigDecimal price) {
        if (Objects.isNull(price)) {
            throw new PriceException("price is null");
        }
        //判断是否为整数
        if (new BigDecimal(price.intValue()).compareTo(price) == 0) {
            BigDecimal newPrice = price.setScale(0, RoundingMode.FLOOR);
            return newPrice.toString();
        }
        DecimalFormat df = new DecimalFormat("0.##");
        return df.format(price);
    }

    /**
     * 计算折扣率（保留一位小数 + 向上取整）
     */
    public static String calcDiscountRate(BigDecimal dealGroupPrice, BigDecimal promoPrice) {
        if (dealGroupPrice == null || promoPrice == null
                || dealGroupPrice.compareTo(BigDecimal.ZERO) == 0
                || promoPrice.compareTo(BigDecimal.ZERO) == 0
                || promoPrice.compareTo(dealGroupPrice) == 0
                || dealGroupPrice.compareTo(promoPrice) < 0) {
            return null;
        }
        return promoPrice.divide(dealGroupPrice, 2, RoundingMode.CEILING)
                .multiply(new BigDecimal(10))
                .stripTrailingZeros()
                .toPlainString();
    }

    /**
     * 计算折扣率（保留一位小数 + 向上取整）
     */
    public static BigDecimal calcDiscountRateBigDecimal(BigDecimal marketPrice, BigDecimal finalPrice) {
        if (marketPrice == null || finalPrice == null || marketPrice.compareTo(BigDecimal.ZERO) == 0
                || finalPrice.compareTo(BigDecimal.ZERO) == 0 || finalPrice.compareTo(marketPrice) == 0
                || marketPrice.compareTo(finalPrice) < 0) {
            return null;
        }
        return finalPrice.divide(marketPrice, 2, RoundingMode.CEILING).multiply(new BigDecimal(10)).setScale(1, RoundingMode.CEILING);
    }




    /**
     * 使用"?"隐藏价格第n位，可隐藏多位
     * @param price
     * @return
     */
    public static String hidePriceWithQuestionMark(String price, Set<Integer> posSet) {
        if (StringUtils.isBlank(price) || CollectionUtils.isEmpty(posSet)) {
            return price;
        }
        // 价格小于10直接设置为?
        try {
            String priceStr = price.replace(",", "");
            double priceValue = Double.parseDouble(priceStr);
            if (priceValue < 10) {
                return "?";
            }
        } catch (NumberFormatException e) {
            log.error("[PriceHelper] hidePriceWithQuestionMark error", e);
            return price;
        }
        StringBuilder res = new StringBuilder(price);
        int numCnt = 0;
        for (int i = 0; i < price.length(); i++)  {
            char c = price.charAt(i);
            if (c != '.' && c != ',') {
                numCnt++;
                // 需要隐藏的位置，设置为"?"
                if (posSet.contains(numCnt)) {
                    res.setCharAt(i, '?');
                }
            }
        }
        return res.toString();
    }
}
