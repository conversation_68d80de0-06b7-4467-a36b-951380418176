package com.sankuai.dzshoppingguide.product.detail.application.fetcher.welfare;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import com.sankuai.meituan.charity.merchant.main.sdk.dto.response.DzProductDocResp;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author: <EMAIL>
 * @Date: 2025/3/9
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class WelfareDocInfo extends FetcherReturnValueDTO {
    private DzProductDocResp dzProductDocResp;
}
