package com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.extend.none;

import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.BottomBarActionDataVO;
import com.sankuai.dzshoppingguide.product.detail.spi.bottombar.action.enums.BottomBarActionTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author: guangyujie
 * @Date: 2025/3/16 15:36
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class DoNothingActionVO extends BottomBarActionDataVO {

    private final int actionType = BottomBarActionTypeEnum.NOTHING.getCode();

}
