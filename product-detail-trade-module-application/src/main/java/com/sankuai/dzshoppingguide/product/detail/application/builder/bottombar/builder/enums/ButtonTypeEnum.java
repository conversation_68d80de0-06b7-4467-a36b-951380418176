package com.sankuai.dzshoppingguide.product.detail.application.builder.bottombar.builder.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: guang<PERSON><PERSON>e
 * @Date: 2025/3/12 05:20
 */
@Getter
public enum ButtonTypeEnum {

    NORMAL_DEAL(1, "普通团单"),
    MEMBER(2, "商家会员"),
    COST_EFFECTIVE_PINTUAN(3, "特价团购拼团"),
    COMMON_PINTUAN(4, "拼团"),
    MEMBER_ONLY(5, "会员专属团购领取会员按钮");

    private final int code;

    private final String desc;

    ButtonTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ButtonTypeEnum fromCode(int code) {
        for (ButtonTypeEnum value : ButtonTypeEnum.values()) {
            if (value.getCode() == code) {
                return value;
            }
        }
        throw new IllegalArgumentException("无效code:" + code);
    }

    public static boolean containsCode(int code) {
        for (ButtonTypeEnum value : ButtonTypeEnum.values()) {
            if (value.getCode() == code) {
                return true;
            }
        }
        return false;
    }

    public static Map<Integer, String> buildCodeDescMap() {
        return Arrays.stream(ButtonTypeEnum.values()).collect(Collectors.toMap(
                ButtonTypeEnum::getCode,
                ButtonTypeEnum::getDesc
        ));
    }

    public static Map<String, String> buildNameDescMap() {
        return Arrays.stream(ButtonTypeEnum.values()).collect(Collectors.toMap(
                ButtonTypeEnum::name,
                ButtonTypeEnum::getDesc
        ));
    }

}