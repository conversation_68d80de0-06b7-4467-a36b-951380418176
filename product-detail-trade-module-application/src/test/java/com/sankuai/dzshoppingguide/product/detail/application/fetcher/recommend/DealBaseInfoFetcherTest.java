package com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommend;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.BaseFetcherContext;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommend.dto.DealGroupDTOReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommend.dto.RecommendInfoReturnValue;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResponse;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResult;
import com.sankuai.general.product.query.center.client.service.DealGroupQueryService;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import org.apache.thrift.TException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.mockito.junit.*;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

@RunWith(PowerMockRunner.class)
@PrepareForTest(DealBaseInfoFetcher.class)
class DealBaseInfoFetcherTest {

    private DealBaseInfoFetcher dealBaseInfoFetcher;

    private DealGroupQueryService dealGroupQueryServiceAsync;

    @BeforeEach
    void setUp() throws Exception {
        dealBaseInfoFetcher = PowerMockito.spy(new DealBaseInfoFetcher());
        dealGroupQueryServiceAsync = mock(DealGroupQueryService.class);
        // Set request
        ProductDetailPageRequest request = new ProductDetailPageRequest();
        request.setClientType(ClientTypeEnum.MT_APP.getCode());
        PowerMockito.field(DealBaseInfoFetcher.class, "request").set(dealBaseInfoFetcher, request);
        // Set service
        PowerMockito.field(DealBaseInfoFetcher.class, "dealGroupQueryServiceAsync").set(dealBaseInfoFetcher, dealGroupQueryServiceAsync);
    }

    /**
     * Test when dependency result is null
     */
    @Test
    public void testDoFetch_DependencyResultIsNull() throws Throwable {
        // arrange
        PowerMockito.doReturn(null).when(dealBaseInfoFetcher, "getDependencyResult", RecommendInfoInShopFetcher.class);
        // act
        CompletableFuture<DealGroupDTOReturnValue> result = dealBaseInfoFetcher.doFetch();
        // assert
        assertNull(result.get());
    }

    /**
     * Test when dependency result has empty sortedDealGroupIds
     */
    @Test
    public void testDoFetch_EmptyDealGroupIds() throws Throwable {
        // arrange
        RecommendInfoReturnValue mockResult = mock(RecommendInfoReturnValue.class);
        when(mockResult.getSortedDealGroupIds()).thenReturn(null);
        PowerMockito.doReturn(mockResult).when(dealBaseInfoFetcher, "getDependencyResult", RecommendInfoInShopFetcher.class);
        // act
        CompletableFuture<DealGroupDTOReturnValue> result = dealBaseInfoFetcher.doFetch();
        // assert
        assertNull(result.get());
    }


}
