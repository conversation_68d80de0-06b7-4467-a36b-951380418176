package com.sankuai.dzshoppingguide.product.detail.application.fetcher.atmosphere;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.best.shop.ShopInfo;
import com.sankuai.sinai.data.api.dto.DpPoiBackCategoryDTO;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.sinai.data.api.dto.MtPoiDTO;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.apache.commons.lang3.math.NumberUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class ProductAtmosphereFetcherGetSecondPoiCategoryIds1Test {

    @Spy
    @InjectMocks
    private ProductAtmosphereFetcher productAtmosphereFetcher;

    /**
     * Helper method to create DpPoiBackCategoryDTO
     */
    private DpPoiBackCategoryDTO createDpPoiBackCategoryDTO(int categoryId, int level) {
        DpPoiBackCategoryDTO category = new DpPoiBackCategoryDTO();
        category.setCategoryId(categoryId);
        category.setCategoryLevel(level);
        return category;
    }


}
