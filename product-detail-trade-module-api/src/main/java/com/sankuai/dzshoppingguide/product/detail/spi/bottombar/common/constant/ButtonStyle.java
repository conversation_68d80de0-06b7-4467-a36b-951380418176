package com.sankuai.dzshoppingguide.product.detail.spi.bottombar.common.constant;

import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

/**
 * @Author: guangyujie
 * @Date: 2025/3/16 20:30
 */
@Getter
public class ButtonStyle {

    private final String titleColor;

    private final List<String> backGroundColors;

    public String getBackgroundSingleColor() {
        if (CollectionUtils.isNotEmpty(backGroundColors)) {
            return backGroundColors.get(0);
        }
        return "#FFFFFF";
    }

    public ButtonStyle(String titleColor, List<String> backGroundColors) {
        this.titleColor = titleColor;
        this.backGroundColors = backGroundColors;
    }

}
