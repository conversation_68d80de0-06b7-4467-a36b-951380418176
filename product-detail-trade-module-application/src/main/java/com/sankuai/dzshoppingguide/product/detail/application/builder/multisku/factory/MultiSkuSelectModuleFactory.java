package com.sankuai.dzshoppingguide.product.detail.application.builder.multisku.factory;

import com.sankuai.dz.api.module.arrange.framework.builder.metadata.annotation.Builder;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.enums.BuilderTypeEnum;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.extend.BaseBuilderFactory;
import com.sankuai.dz.api.module.arrange.framework.builder.metadata.spi.service.extend.BaseVariableBuilder;
import com.sankuai.dz.product.detail.RequestCustomParamEnum;
import com.sankuai.dzshoppingguide.product.detail.application.builder.multisku.builder.DigitalApplianceMultiSkuSelectModuleBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.builder.multisku.builder.DefaultMultiSkuSelectModuleBuilder;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.FetcherDAGStarter;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.tag.guarantee.SkuGuaranteeTagFetcher;
import com.sankuai.dzshoppingguide.product.detail.spi.enums.ModuleKeyConstants;
import com.sankuai.dzshoppingguide.product.detail.spi.multisku.vo.MultiSkuSelectVO;
import lombok.extern.slf4j.Slf4j;

import java.util.Objects;

import static com.sankuai.dzshoppingguide.product.detail.application.constants.Constants.COUNTRY_SUBSIDIES;

/**
 * <AUTHOR>
 * @date 2025-04-22
 */
@Builder(
        builderType = BuilderTypeEnum.BUILDER_FACTORY,
        moduleKey = ModuleKeyConstants.MODULE_DETAIL_DEAL_MULTI_SKU_SELECT,
        startFetcher = FetcherDAGStarter.class,
        dependentFetchers = {
                FetcherDAGStarter.class,
                SkuGuaranteeTagFetcher.class
        }
)
@Slf4j
public class MultiSkuSelectModuleFactory extends BaseBuilderFactory<MultiSkuSelectVO> {
    @Override
    protected Class<? extends BaseVariableBuilder> selectVariableBuilder() {
        String flowFlag = request.getCustomParam(RequestCustomParamEnum.flowFlag);
        if (Objects.equals(COUNTRY_SUBSIDIES, flowFlag)) {
            return DigitalApplianceMultiSkuSelectModuleBuilder.class;
        }
        return DefaultMultiSkuSelectModuleBuilder.class;
    }
}
