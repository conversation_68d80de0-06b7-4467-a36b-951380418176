package com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommendgoods.dtos;

import com.dianping.vc.sdk.codec.JsonCodec;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommendgoods.enums.ProductStatusEnums;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.timesdeal.dto.OrderUserM;
import com.sankuai.dztheme.deal.dto.TagDTO;
import com.sankuai.dztheme.deal.res.DealProductSpuDTO;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @create 2025/5/21 19:46
 */
@Data
public class ProductM {
    /**
     * item id, 对List<ProductM> products于货架来说, 是商品ID, 跟着平台走
     */
    private long productId;

    /**
     * 适用门店数量
     */
    private int shopNum;

    /**
     * 门店ID
     */
    private List<Long> shopIds;

    /**
     * 商品状态
     * @see ProductStatusEnums
     */
    private int productStatus;

    /**
     * 区分商品类型
     */
    private int productType;

    private int order;

    /**
     * 商品类目
     */
    private int categoryId;

    /**
     * 商品类型
     */
    private int spuType;

    /**
     * 商品类目名称
     */
    private String categoryName;

    /**
     * 标题
     */
    private String title;

    /**
     * 商品图片
     */
    private String picUrl;

    /**
     * 跳转链接
     */
    private String jumpUrl;

    /**
     * 下单链接
     */
    private String orderUrl;

    /**
     * 标准服务spu下单链接
     */
    private String spuOrderUrl;

    /**
     * 跳转文案
     */
    private String jumpText;

    /**
     * 商品描述
     */
    private String productDesc;

    /**
     * 是否可以预订
     */
    private Boolean available;


    /**
     * SPU
     */
    private DealProductSpuDTO spuM;

    private List<DealProductSpuDTO> spuMList;


    ///////////////////////////销量信息///////////////////////////

    /**
     * 销量
     */
    private ProductSaleM sale;

    /**
     * 销量
     */
    private List<ProductSaleM> sales;

    /**
     * 库存
     */
    private ProductStockM stock;

    ///////////////////////////价格信息///////////////////////////


    /**
     * 售卖价格, 包含起字等
     */
    private String basePriceTag;

    /**
     * 商品原始售卖价格的描述, 如起、XX节课
     */
    private String basePriceDesc;

    /**
     * 商品售卖价格
     */
    private BigDecimal basePrice;

    /**
     * 市场价格
     */
    private String marketPrice;

    /**
     * 市场折扣
     */
    private String marketPromoDiscount;



    /**
     * 融合优惠价格
     */
    private List<ProductPromoPriceM> promoPrices;

    ///////////////////////////升级售卖价格///////////////////////////

    /**
     * 拼团售卖价格
     */
    private ProductPriceM pinPrice;

    /**
     * 次卡售卖价格
     */
    private ProductPriceM cardPrice;

    /**
     * 优惠价
     */
    private BigDecimal payPrice;

    //购物车价格
    private BigDecimal shopCarPrice;

    ///////////////////////////售卖信息///////////////////////////

    /**
     * 购买信息
     */
    private String purchase;

    ///////////////////////////返券///////////////////////////

    /**
     * 返券
     */
    private List<ProductCouponM> coupons;

    ///////////////////////////商品参与的活动列表///////////////////////////

    /**
     * 商品活动
     */
    private List<ProductActivityM> activities;

    ///////////////////////////其他信息///////////////////////////

    /**
     * 点评门店ID
     */
    private List<Long> dpShopIds;

    /**
     * 城市下spu对应团单数量
     */
    private Integer spuCityDealNum;

    /**
     * 产品标签
     */
    private List<String> productTags;

    /**
     * 商品本身售卖或者使用的开始时间
     */
    private long beginDate;

    /**
     * 商品本身售卖或者使用的终止时间
     */
    private long endDate;

    /**
     * 扩展属性
     */
    private List<AttrM> extAttrs;

    /**
     * 评价和星级信息
     */
    private ReviewM review;

    /**
     * 商品关联门店
     */
    private List<ShopM> shopMs;


    private String shopName;

    private String discountTag;

    /**
     * 商品标签列表，包括标签ID、标签类型、标签名称等信息
     */
    private List<TagDTO> productTagList;

    /**
     * 团购交易类型
     */
    private Integer tradeType;

    /**
     * 到手价
     */
    private BigDecimal salePrice;

    /**
     * 团购次卡C端表达优化新老样式控制开关
     */
    private boolean expressOptimize;

    /**
     * 团购次卡C端表达优化,营销标签,例如: 性价比高 或者 先用后付 优先级:先用后付 > 性价比高
     */
    private int promoTagType;

    /**
     * EXPOSED用户可先用后付
     * EXPOSED：建议曝光，UNEXPOSED：建议不曝光
     */
    private String exposure;

    /**
     * 订单用户信息列表
     */
    private List<OrderUserM> orderUsers;

    /**
     * 根据属性名获取属性值
     *
     * @param attrName
     * @return
     */
    public String getAttr(String attrName) {
        if (CollectionUtils.isEmpty(this.getExtAttrs())) {
            return null;
        }
        AttrM attrM = this.getExtAttrs().stream()
                .filter(Objects::nonNull)
                .filter(attr -> attr.getName().equals(attrName))
                .findFirst()
                .orElse(null);
        return attrM == null ? null: attrM.getValue();
    }

    /**
     * 根据优惠类型获取优惠价格
     *
     * @param promoType
     * @return
     */
    public ProductPromoPriceM getPromo(int promoType) {
        if (CollectionUtils.isEmpty(this.getPromoPrices())) {
            return null;
        }
        return this.getPromoPrices().stream().filter(productPromoPriceM -> productPromoPriceM.getPromoType() == promoType).findFirst().orElse(null);
    }

    /**
     * 是否为团购次卡
     */
    public boolean isTimesDeal() {
        // 19-团购次卡
        return Objects.equals(tradeType, 19);
    }

    @JsonIgnore // 优化性能，禁止序列化时将此方法作为属性加载
    public List<Integer> getRelatedTimesDeal() {
        String dealRelatedTimesDealAttr = getAttr("dealRelatedTimesDealAttr");
        if (StringUtils.isBlank(dealRelatedTimesDealAttr)) {
            return Lists.newArrayList();
        }
        return JsonCodec.decode(dealRelatedTimesDealAttr, new TypeReference<List<Integer>>() {
        });
    }

}
