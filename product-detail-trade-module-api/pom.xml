<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.sankuai.dzshoppingguide.detail</groupId>
        <artifactId>product-detail-trade-module</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>product-detail-trade-module-api</artifactId>
    <version>${product-detail-trade-module-api.version}</version>
    <packaging>jar</packaging>
    <name>product-detail-trade-module-api</name>

    <properties>
        <java.version>8</java.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.sankuai.dz</groupId>
            <artifactId>product-detail-gateway-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-pearl</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.pearl</groupId>
            <artifactId>pearl</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.dealuser</groupId>
            <artifactId>price-display-api</artifactId>
            <version>0.0.191</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.sankuai.nibscp.common</groupId>
            <artifactId>flow-identify-sdk</artifactId>
            <version>1.0.18</version>
        </dependency>
        <!--拼团-->
        <dependency>
            <groupId>com.dianping.tpfun</groupId>
            <artifactId>sku-aggregate-api</artifactId>
            <version>1.8.114</version>
            <exclusions>
                <exclusion>
                    <artifactId>validation-api</artifactId>
                    <groupId>javax.validation</groupId>

                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.mktplay</groupId>
            <artifactId>mkt-play-center-client</artifactId>
            <version>1.0.4</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.clr</groupId>
            <artifactId>clr-content-process-thrift</artifactId>
            <version>0.0.71</version>
            <exclusions>
                <exclusion>
                    <groupId>com.meituan.mdp.boot</groupId>
                    <artifactId>mdp-boot-starter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.sankuai.mpmctstock</groupId>
                    <artifactId>mpmctstock-core-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.sankuai.clr</groupId>
                    <artifactId>clr-content-core-thrift</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.sankuai.leads</groupId>
                    <artifactId>clrbase-common</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.scala-lang</groupId>
            <artifactId>scala-library</artifactId>
        </dependency>
    </dependencies>

</project>
