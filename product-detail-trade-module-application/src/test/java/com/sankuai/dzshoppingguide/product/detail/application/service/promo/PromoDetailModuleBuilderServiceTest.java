package com.sankuai.dzshoppingguide.product.detail.application.service.promo;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.dianping.vc.sdk.codec.JsonCodec;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PromoDTO;
import com.sankuai.dealuser.price.display.api.model.PromoIdentity;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.spi.promo.vo.DealBestPromoDetail;
import com.sankuai.dzshoppingguide.product.detail.spi.promo.vo.DealGift;
import com.sankuai.dzshoppingguide.product.detail.spi.promo.vo.PromoActivityInfoVO;
import com.sankuai.dzshoppingguide.product.detail.spi.promo.vo.PromoDetailModule;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
public class PromoDetailModuleBuilderServiceTest {

    private final PromoDetailModuleBuilderService service = new PromoDetailModuleBuilderService();

    /**
     * Test case for null input module
     */
    @Test
    public void testBuildPromoAbstractList_NullModule() throws Throwable {
        List<String> result = service.buildPromoAbstractList(null, ClientTypeEnum.MT_APP, null, null);
        assertNull(result);
    }

    /**
     * Test case for empty promo details
     */
    @Test
    public void testBuildPromoAbstractList_EmptyPromoDetails() throws Throwable {
        PromoDetailModule module = new PromoDetailModule();
        List<String> result = service.buildPromoAbstractList(module, ClientTypeEnum.MT_APP, null, null);
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test case for member promo in APP client
     */
    @Test
    public void testBuildPromoAbstractList_MemberPromoInApp() throws Throwable {
        PromoDetailModule module = new PromoDetailModule();
        DealBestPromoDetail memberPromo = new DealBestPromoDetail();
        memberPromo.setPromoName("会员专享95折");
        module.setBestPromoDetails(Collections.singletonList(memberPromo));
        List<String> result = service.buildPromoAbstractList(module, ClientTypeEnum.MT_APP, null, null);
        assertNotNull(result);
        assertEquals(1, result.size());
        String jsonStr = result.get(0);
        List<Map<String, Object>> decodedResult = JsonCodec.decode(jsonStr, List.class);
        assertNotNull(decodedResult);
        assertFalse(decodedResult.isEmpty());
        String text = (String) decodedResult.get(0).get("text");
        assertEquals("会员专享95折", text);
    }

    /**
     * Test case for normal price fallback
     */
    @Test
    public void testBuildPromoAbstractList_NormalPriceFallback() throws Throwable {
        PromoDetailModule module = new PromoDetailModule();
        PriceDisplayDTO normalPrice = new PriceDisplayDTO();
        PromoDTO promoDTO = new PromoDTO();
        PromoIdentity identity = new PromoIdentity(1L, 1, "满100减10");
        promoDTO.setIdentity(identity);
        promoDTO.setAmount(new BigDecimal("10.00"));
        normalPrice.setUsedPromos(Collections.singletonList(promoDTO));
        List<String> result = service.buildPromoAbstractList(module, ClientTypeEnum.MT_APP, normalPrice, null);
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("满100减10", result.get(0));
    }

    /**
     * Test case for gifts in APP client
     */
    @Test
    public void testBuildPromoAbstractList_GiftsInApp() throws Throwable {
        PromoDetailModule module = new PromoDetailModule();
        DealGift gift = new DealGift();
        gift.setTitle("精美礼品");
        List<String> result = service.buildPromoAbstractList(module, ClientTypeEnum.MT_APP, null, Collections.singletonList(gift));
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("赠精美礼品", result.get(0));
    }

    /**
     * Test case for gifts in non-APP client
     */
    @Test
    public void testBuildPromoAbstractList_GiftsInNonApp() throws Throwable {
        PromoDetailModule module = new PromoDetailModule();
        DealGift gift = new DealGift();
        gift.setTitle("精美礼品");
        List<String> result = service.buildPromoAbstractList(module, ClientTypeEnum.MT_PC, null, Collections.singletonList(gift));
        assertNotNull(result);
        assertEquals(1, result.size());
        String jsonStr = result.get(0);
        List<Map<String, Object>> decodedResult = JsonCodec.decode(jsonStr, List.class);
        assertNotNull(decodedResult);
        assertFalse(decodedResult.isEmpty());
        String text = (String) decodedResult.get(0).get("text");
        assertEquals("赠精美礼品", text);
    }

    /**
     * Test case for promo activities
     */
    @Test
    public void testBuildPromoAbstractList_PromoActivities() throws Throwable {
        PromoDetailModule module = new PromoDetailModule();
        PromoActivityInfoVO activity = new PromoActivityInfoVO();
        activity.setShortText("限时特惠");
        module.setPromoActivityList(Collections.singletonList(activity));
        List<String> result = service.buildPromoAbstractList(module, ClientTypeEnum.MT_APP, null, null);
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("限时特惠", result.get(0));
    }

    /**
     * Test case for custom activity prefix
     */
    @Test
    public void testBuildPromoAbstractList_CustomActivityPrefix() throws Throwable {
        PromoDetailModule module = new PromoDetailModule();
        DealGift gift = new DealGift();
        gift.setTitle("礼品");
        gift.setCustomerActivityPrefix("新人专享");
        List<String> result = service.buildPromoAbstractList(module, ClientTypeEnum.MT_APP, null, Collections.singletonList(gift));
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("新人专享礼品", result.get(0));
    }

    /**
     * Test case for promo sorting
     */
    @Test
    public void testBuildPromoAbstractList_PromoSorting() throws Throwable {
        PromoDetailModule module = new PromoDetailModule();
        List<DealBestPromoDetail> promos = new ArrayList<>();
        DealBestPromoDetail promo1 = new DealBestPromoDetail();
        promo1.setPromoName("返现5元");
        DealBestPromoDetail promo2 = new DealBestPromoDetail();
        promo2.setPromoName("会员专享");
        DealBestPromoDetail promo3 = new DealBestPromoDetail();
        promo3.setPromoName("新客立减");
        promos.addAll(Arrays.asList(promo1, promo2, promo3));
        module.setBestPromoDetails(promos);
        List<String> result = service.buildPromoAbstractList(module, ClientTypeEnum.MT_APP, null, null);
        assertNotNull(result);
        assertEquals(3, result.size());
        // Convert all results to plain text for comparison
        List<String> plainTexts = new ArrayList<>();
        for (String promo : result) {
            if (promo.startsWith("[")) {
                List<Map<String, Object>> decodedPromo = JsonCodec.decode(promo, List.class);
                plainTexts.add((String) decodedPromo.get(0).get("text"));
            } else {
                plainTexts.add(promo);
            }
        }
        // Verify all promos are present
        assertTrue(plainTexts.stream().anyMatch(text -> text.contains("会员")), "Member promo should be present");
        assertTrue(plainTexts.stream().anyMatch(text -> text.contains("新客")), "New user promo should be present");
        assertTrue(plainTexts.stream().anyMatch(text -> text.contains("返现")), "Return promo should be present");
    }

    /**
     * Test case for multiple gifts
     */
    @Test
    public void testBuildPromoAbstractList_MultipleGifts() throws Throwable {
        PromoDetailModule module = new PromoDetailModule();
        List<DealGift> gifts = new ArrayList<>();
        DealGift gift1 = new DealGift();
        gift1.setTitle("礼品1");
        DealGift gift2 = new DealGift();
        gift2.setTitle("礼品2");
        gifts.addAll(Arrays.asList(gift1, gift2));
        List<String> result = service.buildPromoAbstractList(module, ClientTypeEnum.MT_APP, null, gifts);
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("赠礼品1", result.get(0));
        assertEquals("赠礼品2", result.get(1));
    }

    /**
     * Test case for empty gift title
     */
    @Test
    public void testBuildPromoAbstractList_EmptyGiftTitle() throws Throwable {
        PromoDetailModule module = new PromoDetailModule();
        DealGift gift = new DealGift();
        gift.setTitle("");
        List<String> result = service.buildPromoAbstractList(module, ClientTypeEnum.MT_APP, null, Collections.singletonList(gift));
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test case for null input
     */
    @Test
    public void testBuildPromoAbstractList_NullInput() throws Throwable {
        List<String> result = service.buildPromoAbstractList(null, ClientTypeEnum.MT_APP, null, null);
        assertNull(result);
    }

    /**
     * Test case for member promo in APP client
     */
    @Test
    public void testBuildPromoAbstractList_WithMemberPromoForApp() throws Throwable {
        PromoDetailModule module = new PromoDetailModule();
        DealBestPromoDetail memberPromo = new DealBestPromoDetail();
        memberPromo.setPromoName("会员优惠");
        module.setBestPromoDetails(Collections.singletonList(memberPromo));
        List<String> result = service.buildPromoAbstractList(module, ClientTypeEnum.MT_APP, null, null);
        assertNotNull(result);
        assertEquals(1, result.size());
        String jsonStr = result.get(0);
        List<Map<String, Object>> decodedResult = JsonCodec.decode(jsonStr, List.class);
        assertNotNull(decodedResult);
        assertFalse(decodedResult.isEmpty());
        String text = (String) decodedResult.get(0).get("text");
        assertEquals("会员优惠", text);
    }

    /**
     * Test case for normal price fallback
     */
    @Test
    public void testBuildPromoAbstractList_WithNormalPriceFallback() throws Throwable {
        PromoDetailModule module = new PromoDetailModule();
        PriceDisplayDTO normalPrice = new PriceDisplayDTO();
        List<PromoDTO> usedPromos = new ArrayList<>();
        PromoDTO promoDTO = new PromoDTO();
        PromoIdentity identity = new PromoIdentity(1L, 1, "测试优惠");
        promoDTO.setIdentity(identity);
        promoDTO.setAmount(new BigDecimal("10.00"));
        usedPromos.add(promoDTO);
        normalPrice.setUsedPromos(usedPromos);
        List<String> result = service.buildPromoAbstractList(module, ClientTypeEnum.MT_APP, normalPrice, null);
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("测试优惠", result.get(0));
    }

    /**
     * Test case for gifts in APP client
     */
    @Test
    public void testBuildPromoAbstractList_WithGiftsForApp() throws Throwable {
        PromoDetailModule module = new PromoDetailModule();
        List<DealGift> gifts = new ArrayList<>();
        DealGift gift = new DealGift();
        gift.setTitle("赠品测试");
        gifts.add(gift);
        List<String> result = service.buildPromoAbstractList(module, ClientTypeEnum.MT_APP, null, gifts);
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("赠赠品测试", result.get(0));
    }

    /**
     * Test case for gifts in non-APP client
     */
    @Test
    public void testBuildPromoAbstractList_WithGiftsForNonApp() throws Throwable {
        PromoDetailModule module = new PromoDetailModule();
        List<DealGift> gifts = new ArrayList<>();
        DealGift gift = new DealGift();
        gift.setTitle("赠品测试");
        gifts.add(gift);
        List<String> result = service.buildPromoAbstractList(module, ClientTypeEnum.MT_PC, null, gifts);
        assertNotNull(result);
        assertEquals(1, result.size());
        String jsonStr = result.get(0);
        List<Map<String, Object>> decodedResult = JsonCodec.decode(jsonStr, List.class);
        assertNotNull(decodedResult);
        assertFalse(decodedResult.isEmpty());
        String text = (String) decodedResult.get(0).get("text");
        assertEquals("赠赠品测试", text);
    }

    /**
     * Test case for activities
     */
    @Test
    public void testBuildPromoAbstractList_WithActivities() throws Throwable {
        PromoDetailModule module = new PromoDetailModule();
        List<PromoActivityInfoVO> activities = new ArrayList<>();
        PromoActivityInfoVO activity = new PromoActivityInfoVO();
        activity.setShortText("活动优惠");
        activities.add(activity);
        module.setPromoActivityList(activities);
        List<String> result = service.buildPromoAbstractList(module, ClientTypeEnum.MT_APP, null, null);
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("活动优惠", result.get(0));
    }

    @Test
    public void testBuildPromoAbstractList_WithCustomerActivityPrefix() throws Throwable {
        // Setup
        PromoDetailModule module = new PromoDetailModule();
        List<DealGift> gifts = new ArrayList<>();
        DealGift gift = new DealGift();
        gift.setTitle("赠品");
        gift.setCustomerActivityPrefix("定制前缀");
        gifts.add(gift);
        // Test
        List<String> result = service.buildPromoAbstractList(module, ClientTypeEnum.MT_APP, null, gifts);
        // Verify
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("定制前缀赠品", result.get(0));
    }

    @Test
    public void test1() {
        List<String> decode = JsonCodec.decode(jsonData, List.class);

        final List<String> promoOrder = Arrays.asList("会员", "秒杀", "补贴", "新客", "特惠", "券", "减", "团购优惠", "工作日", "人团", "返");

        List<String> sortedPromoAbstractListBest = decode.stream()
                .filter(StringUtils::isNotBlank)
                .sorted((promo1, promo2) -> {
                    int index1 = getIndex(promo1, promoOrder);
                    int index2 = getIndex(promo2, promoOrder);
                    return Integer.compare(index1, index2);
                })
                .collect(Collectors.toList());

        System.out.println(JsonCodec.encode(sortedPromoAbstractListBest));
    }
    
    @Test
    public void test3() {
        String encode = JSON.toJSONString("会员");
        List<String> decode = JSON.parseArray(jsonData, String.class);
        String s = decode.get(1);
        boolean contains = s.contains(encode);
        System.out.println("contains = " + contains);

        
    }
    
    @Test
    public void test4() {
        String encode = StringEscapeUtils.escapeJava("会员");
        List<String> decode = JsonCodec.decode(jsonData, List.class);
        String s = decode.get(1);
        boolean contains = s.contains(encode);
        System.out.println("contains = " + contains);
    }

    private int getIndex(String promoName, List<String> promoOrder) {
        if (StringUtils.isEmpty(promoName) || CollectionUtils.isEmpty(promoOrder)) {
            return Integer.MAX_VALUE;
        }
        // 识别 promoName 是否有经过Unicode编码
        // 正则表达式匹配严格格式的Unicode转义序列
        Pattern pattern = Pattern.compile("\\\\u[0-9a-fA-F]{4}");
        Matcher matcher = pattern.matcher(promoName);

        if (matcher.find()) {
            // List<String> afterEncodePromoOrder = promoOrder.stream().map(JsonCodec::encode).collect(Collectors.toList());
            Map<String,String> decodePromoName = null;
            try {
                decodePromoName = JSONObject.parseObject(promoName, new TypeReference<Map<String, String>>() {});
            } catch (Exception e) {
                // log.error("parse richText error,promoName:{}",promoName,e);
                decodePromoName = new HashMap<>();
            }
            String target = decodePromoName.getOrDefault("text","");
            return promoOrder.stream().filter(target::contains).findFirst().map(promoOrder::indexOf).orElse(Integer.MAX_VALUE);
        } else {
            return promoOrder.stream().filter(promoName::contains).findFirst().map(promoOrder::indexOf).orElse(Integer.MAX_VALUE);
        }
    }

    private String jsonData = "[\"\\u56e2\\u8d2d\\u4f18\\u60e033\\u5143\",\"[{\\\"containercolor\\\":\\\"#FFEDDE\\\",\\\"textsize\\\":10,\\\"underline\\\":false,\\\"textstyle\\\":\\\"Bold\\\",\\\"textcolor\\\":\\\"#8e3c13\\\",\\\"strikethrough\\\":false,\\\"text\\\":\\\"\\\\u65b0\\\\u4f1a\\\\u5458\\\\u4f18\\\\u60e021\\\\u5143\\\",\\\"customize\\\":true}]\"]";
}
