package com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.sku;

import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.dto.ProductPriceReturnValue;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2025/3/1
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SkuPriceReturnValue extends ProductPriceReturnValue {
    /**
     * sku报价返回结果
     */
    private List<PriceDisplayDTO> priceDisplayDTOList;
}
