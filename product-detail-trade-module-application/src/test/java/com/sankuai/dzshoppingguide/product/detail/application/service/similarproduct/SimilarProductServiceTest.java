package com.sankuai.dzshoppingguide.product.detail.application.service.similarproduct;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.product.detail.RequestCustomParamEnum;
import com.sankuai.dz.product.detail.gateway.spi.dto.CustomParam;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import com.dianping.cat.Cat;
import org.mockito.InjectMocks;
import org.mockito.MockedStatic;
import com.sankuai.dz.product.detail.gateway.spi.enums.ClientTypeEnum;
import com.sankuai.dzshoppingguide.product.detail.application.service.similarproduct.dtos.DealRepurchaseConfig;
import java.util.Arrays;
import java.util.Collections;
import org.mockito.Mock;
import org.mockito.junit.*;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommendgoods.dtos.ProductM;
import java.util.List;
import org.apache.commons.collections4.CollectionUtils;
import static org.mockito.ArgumentMatchers.any;
import com.dianping.lion.client.Lion;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommendgoods.dtos.AttrM;
import java.util.HashMap;
import java.util.Map;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

@ExtendWith(MockitoExtension.class)
public class SimilarProductServiceTest {

    @InjectMocks
    private SimilarProductService similarProductService;

    @Mock
    private ProductDetailPageRequest request;

    private final SimilarProductService service = new SimilarProductService();

    /**
     * 测试当businessType等于"purchase_limit"时返回true
     */
    @Test
    public void testIsLimitPurchaseBizWhenBusinessTypeIsPurchaseLimit() throws Throwable {
        // arrange
        ProductDetailPageRequest request = mock(ProductDetailPageRequest.class);
        CustomParam customParam = new CustomParam();
        customParam.addParam(RequestCustomParamEnum.businessType.name(), "purchase_limit");
        when(request.getCustomParam(RequestCustomParamEnum.businessType)).thenReturn("purchase_limit");
        SimilarProductService service = new SimilarProductService();
        // act
        boolean result = service.isLimitPurchaseBiz(request);
        // assert
        assertTrue(result);
    }

    /**
     * 测试当businessType不等于"purchase_limit"时返回false
     */
    @Test
    public void testIsLimitPurchaseBizWhenBusinessTypeIsNotPurchaseLimit() throws Throwable {
        // arrange
        ProductDetailPageRequest request = mock(ProductDetailPageRequest.class);
        when(request.getCustomParam(RequestCustomParamEnum.businessType)).thenReturn("other_value");
        SimilarProductService service = new SimilarProductService();
        // act
        boolean result = service.isLimitPurchaseBiz(request);
        // assert
        assertFalse(result);
    }

    /**
     * 测试当businessType为null时返回false
     */
    @Test
    public void testIsLimitPurchaseBizWhenBusinessTypeIsNull() throws Throwable {
        // arrange
        ProductDetailPageRequest request = mock(ProductDetailPageRequest.class);
        when(request.getCustomParam(RequestCustomParamEnum.businessType)).thenReturn(null);
        SimilarProductService service = new SimilarProductService();
        // act
        boolean result = service.isLimitPurchaseBiz(request);
        // assert
        assertFalse(result);
    }

    /**
     * 测试当customParam为null时返回false
     */
    @Test
    public void testIsLimitPurchaseBizWhenCustomParamIsNull() throws Throwable {
        // arrange
        ProductDetailPageRequest request = mock(ProductDetailPageRequest.class);
        when(request.getCustomParam(RequestCustomParamEnum.businessType)).thenReturn(null);
        SimilarProductService service = new SimilarProductService();
        // act
        boolean result = service.isLimitPurchaseBiz(request);
        // assert
        assertFalse(result);
    }

    /**
     * 测试当request为null时抛出NullPointerException
     */
    @Test
    public void testIsLimitPurchaseBizWhenRequestIsNull() throws Throwable {
        // arrange
        SimilarProductService service = new SimilarProductService();
        // act & assert
        assertThrows(NullPointerException.class, () -> {
            service.isLimitPurchaseBiz(null);
        });
    }

    /**
     * 测试当businessType为空字符串时返回false
     */
    @Test
    public void testIsLimitPurchaseBizWhenBusinessTypeIsEmpty() throws Throwable {
        // arrange
        ProductDetailPageRequest request = mock(ProductDetailPageRequest.class);
        when(request.getCustomParam(RequestCustomParamEnum.businessType)).thenReturn("");
        SimilarProductService service = new SimilarProductService();
        // act
        boolean result = service.isLimitPurchaseBiz(request);
        // assert
        assertFalse(result);
    }

    @Test
    public void testIsLimitedWhenBothParametersAreNull() throws Throwable {
        // arrange
        Integer maxPerUser = null;
        Integer orderCount = null;
        // act
        boolean result = similarProductService.isLimited(maxPerUser, orderCount);
        // assert
        assertFalse(result);
    }

    @Test
    public void testIsLimitedWhenMaxPerUserIsZero() throws Throwable {
        // arrange
        Integer maxPerUser = 0;
        Integer orderCount = 5;
        // act
        boolean result = similarProductService.isLimited(maxPerUser, orderCount);
        // assert
        assertFalse(result);
    }

    @Test
    public void testIsLimitedWhenMaxPerUserIsOne() throws Throwable {
        // arrange
        Integer maxPerUser = 1;
        Integer orderCount = 0;
        // act
        boolean result = similarProductService.isLimited(maxPerUser, orderCount);
        // assert
        assertTrue(result);
    }

    @Test
    public void testIsLimitedWhenMaxPerUserGreaterThanOneAndOrderCountLess() throws Throwable {
        // arrange
        Integer maxPerUser = 3;
        Integer orderCount = 2;
        // act
        boolean result = similarProductService.isLimited(maxPerUser, orderCount);
        // assert
        assertFalse(result);
    }

    @Test
    public void testIsLimitedWhenMaxPerUserGreaterThanOneAndOrderCountEqual() throws Throwable {
        // arrange
        Integer maxPerUser = 3;
        Integer orderCount = 3;
        // act
        boolean result = similarProductService.isLimited(maxPerUser, orderCount);
        // assert
        assertTrue(result);
    }

    @Test
    public void testIsLimitedWhenMaxPerUserGreaterThanOneAndOrderCountGreater() throws Throwable {
        // arrange
        Integer maxPerUser = 3;
        Integer orderCount = 4;
        // act
        boolean result = similarProductService.isLimited(maxPerUser, orderCount);
        // assert
        assertTrue(result);
    }

    @Test
    public void testIsLimitedWhenMaxPerUserIsNull() throws Throwable {
        // arrange
        Integer maxPerUser = null;
        Integer orderCount = 5;
        // act
        boolean result = similarProductService.isLimited(maxPerUser, orderCount);
        // assert
        assertFalse(result);
    }

    @Test
    public void testIsLimitedWhenOrderCountIsNull() throws Throwable {
        // arrange
        Integer maxPerUser = 3;
        Integer orderCount = null;
        // act
        boolean result = similarProductService.isLimited(maxPerUser, orderCount);
        // assert
        assertFalse(result);
    }

    @Test
    public void testEnableRepurchaseWhenConfigIsNull() throws Throwable {
        // arrange
        int categoryId = 1;
        // act
        boolean result = service.enableRepurchase(request, categoryId, null);
        // assert
        assertFalse(result);
    }

    @Test
    public void testEnableRepurchaseWhenSwitchDisabled() throws Throwable {
        // arrange
        int categoryId = 1;
        DealRepurchaseConfig config = new DealRepurchaseConfig();
        config.setEnableSwitch(false);
        // act
        boolean result = service.enableRepurchase(request, categoryId, config);
        // assert
        assertFalse(result);
    }

    @Test
    public void testEnableRepurchaseWhenAllPassEnabled() throws Throwable {
        // arrange
        int categoryId = 1;
        DealRepurchaseConfig config = new DealRepurchaseConfig();
        config.setEnableSwitch(true);
        config.setAllPass(true);
        // act
        boolean result = service.enableRepurchase(request, categoryId, config);
        // assert
        assertTrue(result);
    }

    @Test
    public void testEnableRepurchaseMtClientWithMatchingCategoryAndCity() throws Throwable {
        // arrange
        int categoryId = 1;
        int cityId = 100;
        DealRepurchaseConfig config = new DealRepurchaseConfig();
        config.setEnableSwitch(true);
        config.setCategoryIds(Arrays.asList(1, 2, 3));
        config.setMtCityIds(Arrays.asList(100, 200));
        // MT_APP
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.fromCode(200));
        when(request.getCityId()).thenReturn(cityId);
        // act
        boolean result = service.enableRepurchase(request, categoryId, config);
        // assert
        assertTrue(result);
    }

    @Test
    public void testEnableRepurchaseDpClientWithMatchingCategoryAndCity() throws Throwable {
        // arrange
        int categoryId = 2;
        int cityId = 200;
        DealRepurchaseConfig config = new DealRepurchaseConfig();
        config.setEnableSwitch(true);
        config.setCategoryIds(Arrays.asList(1, 2, 3));
        config.setDpCityIds(Arrays.asList(200, 300));
        // DP_APP
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.fromCode(100));
        when(request.getCityId()).thenReturn(cityId);
        // act
        boolean result = service.enableRepurchase(request, categoryId, config);
        // assert
        assertTrue(result);
    }

    @Test
    public void testEnableRepurchaseWithMatchingCategoryOnly() throws Throwable {
        // arrange
        int categoryId = 3;
        DealRepurchaseConfig config = new DealRepurchaseConfig();
        config.setEnableSwitch(true);
        config.setCategoryIds(Arrays.asList(1, 2, 3));
        config.setMtCityIds(Collections.emptyList());
        config.setDpCityIds(Collections.emptyList());
        // MT_APP
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.fromCode(200));
        when(request.getCityId()).thenReturn(100);
        // act
        boolean result = service.enableRepurchase(request, categoryId, config);
        // assert
        assertTrue(result);
    }

    @Test
    public void testEnableRepurchaseWithMatchingCityOnly() throws Throwable {
        // arrange
        int categoryId = 5;
        int cityId = 300;
        DealRepurchaseConfig config = new DealRepurchaseConfig();
        config.setEnableSwitch(true);
        config.setCategoryIds(Collections.emptyList());
        config.setDpCityIds(Arrays.asList(300, 400));
        // DP_APP
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.fromCode(100));
        when(request.getCityId()).thenReturn(cityId);
        // act
        boolean result = service.enableRepurchase(request, categoryId, config);
        // assert
        assertTrue(result);
    }

    @Test
    public void testEnableRepurchaseWithNoMatchingCategoryOrCity() throws Throwable {
        // arrange
        int categoryId = 5;
        int cityId = 500;
        DealRepurchaseConfig config = new DealRepurchaseConfig();
        config.setEnableSwitch(true);
        config.setCategoryIds(Arrays.asList(1, 2, 3));
        config.setDpCityIds(Arrays.asList(300, 400));
        // DP_APP
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.fromCode(100));
        when(request.getCityId()).thenReturn(cityId);
        // act
        boolean result = service.enableRepurchase(request, categoryId, config);
        // assert
        assertFalse(result);
    }

    @Test
    public void testEnableRepurchaseWithEmptyCategoryAndCityIds() throws Throwable {
        // arrange
        int categoryId = 1;
        DealRepurchaseConfig config = new DealRepurchaseConfig();
        config.setEnableSwitch(true);
        config.setCategoryIds(Collections.emptyList());
        config.setDpCityIds(Collections.emptyList());
        config.setMtCityIds(Collections.emptyList());
        // DP_APP
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.fromCode(100));
        when(request.getCityId()).thenReturn(100);
        // act
        boolean result = service.enableRepurchase(request, categoryId, config);
        // assert
        assertFalse(result);
    }

    @Test
    public void testEnableRepurchaseWithNullCategoryIds() throws Throwable {
        // arrange
        int categoryId = 1;
        int cityId = 100;
        DealRepurchaseConfig config = new DealRepurchaseConfig();
        config.setEnableSwitch(true);
        config.setCategoryIds(null);
        config.setDpCityIds(Arrays.asList(100, 200));
        // DP_APP
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.fromCode(100));
        when(request.getCityId()).thenReturn(cityId);
        // act
        boolean result = service.enableRepurchase(request, categoryId, config);
        // assert
        // Changed to assertTrue as null categoryIds means only city check applies
        assertTrue(result);
    }

    @Test
    public void testEnableRepurchaseWithMtClientAndNullMtCityIds() throws Throwable {
        // arrange
        int categoryId = 1;
        DealRepurchaseConfig config = new DealRepurchaseConfig();
        config.setEnableSwitch(true);
        config.setCategoryIds(Arrays.asList(1, 2, 3));
        config.setMtCityIds(null);
        // MT_APP
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.fromCode(200));
        when(request.getCityId()).thenReturn(100);
        // act
        boolean result = service.enableRepurchase(request, categoryId, config);
        // assert
        // Changed to assertTrue as null cityIds means only category check applies
        assertTrue(result);
    }

    @Test
    public void testFilterProductsNullInputs() throws Throwable {
        // arrange
        ProductM originalProduct = null;
        List<ProductM> recommendProducts = null;
        // act
        List<ProductM> result = similarProductService.filterProducts(request, originalProduct, recommendProducts);
        // assert
        assertNull(result);
    }

    @Test
    public void testFilterProductsEmptyRecommendList() throws Throwable {
        // arrange
        ProductM originalProduct = new ProductM();
        originalProduct.setProductId(1L);
        List<ProductM> recommendProducts = Collections.emptyList();
        // act
        List<ProductM> result = similarProductService.filterProducts(request, originalProduct, recommendProducts);
        // assert
        assertTrue(CollectionUtils.isEmpty(result));
    }

    @Test
    public void testFilterProductsNullPageSource() throws Throwable {
        // arrange
        ProductM originalProduct = new ProductM();
        originalProduct.setProductId(1L);
        ProductM product1 = new ProductM();
        product1.setProductId(2L);
        ProductM product2 = new ProductM();
        product2.setProductId(3L);
        List<ProductM> recommendProducts = Arrays.asList(product1, product2);
        when(request.getPageSource()).thenReturn(null);
        // act
        List<ProductM> result = similarProductService.filterProducts(request, originalProduct, recommendProducts);
        // assert
        assertEquals(2, result.size());
        assertTrue(result.containsAll(recommendProducts));
    }

    @Test
    public void testFilterProductsNormalCase() throws Throwable {
        // arrange
        ProductM originalProduct = new ProductM();
        originalProduct.setProductId(1L);
        ProductM product1 = new ProductM();
        product1.setProductId(2L);
        ProductM product2 = new ProductM();
        product2.setProductId(3L);
        ProductM product3 = new ProductM();
        // same as original
        product3.setProductId(1L);
        List<ProductM> recommendProducts = Arrays.asList(product1, product2, product3);
        when(request.getPageSource()).thenReturn("test_source");
        // Mock repurchaseNotLimitedFilter to return true for product1, false for product2
        SimilarProductService spyService = spy(similarProductService);
        doReturn(true).when(spyService).repurchaseNotLimitedFilter(request, product1);
        doReturn(false).when(spyService).repurchaseNotLimitedFilter(request, product2);
        // act
        List<ProductM> result = spyService.filterProducts(request, originalProduct, recommendProducts);
        // assert
        assertEquals(1, result.size());
        assertEquals(2L, result.get(0).getProductId());
    }

    @Test
    public void testFilterProductsAllFilteredOut() throws Throwable {
        // arrange
        ProductM originalProduct = new ProductM();
        originalProduct.setProductId(1L);
        ProductM product1 = new ProductM();
        product1.setProductId(2L);
        ProductM product2 = new ProductM();
        product2.setProductId(3L);
        List<ProductM> recommendProducts = Arrays.asList(product1, product2);
        when(request.getPageSource()).thenReturn("test_source");
        // Mock repurchaseNotLimitedFilter to return false for all
        SimilarProductService spyService = spy(similarProductService);
        doReturn(false).when(spyService).repurchaseNotLimitedFilter(any(), any());
        // act
        List<ProductM> result = spyService.filterProducts(request, originalProduct, recommendProducts);
        // assert
        assertTrue(CollectionUtils.isEmpty(result));
    }

    @Test
    public void testRepurchaseNotLimitedFilterNotLimitPurchaseBiz() throws Throwable {
        // arrange
        ProductDetailPageRequest request = mock(ProductDetailPageRequest.class);
        when(request.getCustomParam(any())).thenReturn("normal_business");
        ProductM product = new ProductM();
        // act
        boolean result = similarProductService.repurchaseNotLimitedFilter(request, product);
        // assert
        assertTrue(result);
    }

    @Test
    public void testRepurchaseNotLimitedFilterRepurchaseNotEnabled() throws Throwable {
        // arrange
        ProductDetailPageRequest request = mock(ProductDetailPageRequest.class);
        when(request.getCustomParam(any())).thenReturn("purchase_limit");
        when(request.getCityId()).thenReturn(1);
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.MT_APP);
        ProductM product = new ProductM();
        product.setCategoryId(100);
        // act
        boolean result = similarProductService.repurchaseNotLimitedFilter(request, product);
        // assert
        assertTrue(result);
    }

    @Test
    public void testRepurchaseNotLimitedFilterNoAttributes() throws Throwable {
        // arrange
        ProductDetailPageRequest request = mock(ProductDetailPageRequest.class);
        when(request.getCustomParam(any())).thenReturn("purchase_limit");
        when(request.getCityId()).thenReturn(1);
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.MT_APP);
        ProductM product = new ProductM();
        product.setCategoryId(100);
        product.setExtAttrs(Collections.emptyList());
        // act
        boolean result = similarProductService.repurchaseNotLimitedFilter(request, product);
        // assert
        assertTrue(result);
    }

    @Test
    public void testRepurchaseNotLimitedFilterLimitedProduct() throws Throwable {
        // arrange
        ProductDetailPageRequest request = mock(ProductDetailPageRequest.class);
        when(request.getCustomParam(any())).thenReturn("purchase_limit");
        when(request.getCityId()).thenReturn(1);
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.MT_APP);
        ProductM product = new ProductM();
        product.setCategoryId(100);
        AttrM buyRuleAttr = new AttrM("dealGroupBuyRuleAttr", "{\"maxPerUser\":1}");
        AttrM orderCountAttr = new AttrM("dealUserOrderCountAttr", "{\"userOrderCount\":0}");
        product.setExtAttrs(Arrays.asList(buyRuleAttr, orderCountAttr));
        // act
        boolean result = similarProductService.repurchaseNotLimitedFilter(request, product);
        // assert
        assertTrue(result);
    }

    @Test
    public void testRepurchaseNotLimitedFilterNotLimitedProduct() throws Throwable {
        // arrange
        ProductDetailPageRequest request = mock(ProductDetailPageRequest.class);
        when(request.getCustomParam(any())).thenReturn("purchase_limit");
        when(request.getCityId()).thenReturn(1);
        when(request.getClientTypeEnum()).thenReturn(ClientTypeEnum.MT_APP);
        ProductM product = new ProductM();
        product.setCategoryId(100);
        AttrM buyRuleAttr = new AttrM("dealGroupBuyRuleAttr", "{\"maxPerUser\":10}");
        AttrM orderCountAttr = new AttrM("dealUserOrderCountAttr", "{\"userOrderCount\":5}");
        product.setExtAttrs(Arrays.asList(buyRuleAttr, orderCountAttr));
        // act
        boolean result = similarProductService.repurchaseNotLimitedFilter(request, product);
        // assert
        assertTrue(result);
    }

    @Test
    public void testRepurchaseNotLimitedFilterNullRequest() throws Throwable {
        // arrange
        ProductM product = new ProductM();
        // act & assert
        assertThrows(NullPointerException.class, () -> {
            similarProductService.repurchaseNotLimitedFilter(null, product);
        });
    }

    @Test
    public void testRepurchaseNotLimitedFilterNullProduct() throws Throwable {
        // arrange
        ProductDetailPageRequest request = mock(ProductDetailPageRequest.class);
        when(request.getCustomParam(any())).thenReturn("purchase_limit");
        // act & assert
        assertThrows(NullPointerException.class, () -> {
            similarProductService.repurchaseNotLimitedFilter(request, null);
        });
    }

    @Test
    public void testIsLimitedByStr_MaxPerUser1_ReturnsTrue() throws Throwable {
        // arrange
        String buyRuleAttrStr = "{\"maxPerUser\":1}";
        String userOrderCountAttrStr = "{\"userOrderCount\":0}";
        // act
        boolean result = service.isLimitedByStr(buyRuleAttrStr, userOrderCountAttrStr);
        // assert
        assertTrue(result);
    }

    @Test
    public void testIsLimitedByStr_OrderCountExceedsMax_ReturnsTrue() throws Throwable {
        // arrange
        String buyRuleAttrStr = "{\"maxPerUser\":2}";
        String userOrderCountAttrStr = "{\"userOrderCount\":2}";
        // act
        boolean result = service.isLimitedByStr(buyRuleAttrStr, userOrderCountAttrStr);
        // assert
        assertTrue(result);
    }

    @Test
    public void testIsLimitedByStr_OrderCountBelowMax_ReturnsFalse() throws Throwable {
        // arrange
        String buyRuleAttrStr = "{\"maxPerUser\":2}";
        String userOrderCountAttrStr = "{\"userOrderCount\":1}";
        // act
        boolean result = service.isLimitedByStr(buyRuleAttrStr, userOrderCountAttrStr);
        // assert
        assertFalse(result);
    }

    @Test
    public void testIsLimitedByStr_BuyRuleBlank_ReturnsFalse() throws Throwable {
        // arrange
        String buyRuleAttrStr = "";
        String userOrderCountAttrStr = "{\"userOrderCount\":1}";
        // act
        boolean result = service.isLimitedByStr(buyRuleAttrStr, userOrderCountAttrStr);
        // assert
        assertFalse(result);
    }

    @Test
    public void testIsLimitedByStr_UserOrderCountBlank_ReturnsFalse() throws Throwable {
        // arrange
        String buyRuleAttrStr = "{\"maxPerUser\":2}";
        String userOrderCountAttrStr = "";
        // act
        boolean result = service.isLimitedByStr(buyRuleAttrStr, userOrderCountAttrStr);
        // assert
        assertFalse(result);
    }

    @Test
    public void testIsLimitedByStr_NullInputs_ReturnsFalse() throws Throwable {
        // arrange
        String buyRuleAttrStr = null;
        String userOrderCountAttrStr = null;
        // act
        boolean result = service.isLimitedByStr(buyRuleAttrStr, userOrderCountAttrStr);
        // assert
        assertFalse(result);
    }

    @Test
    public void testIsLimitedByStr_MaxPerUser0_ReturnsFalse() throws Throwable {
        // arrange
        String buyRuleAttrStr = "{\"maxPerUser\":0}";
        String userOrderCountAttrStr = "{\"userOrderCount\":1}";
        // act
        boolean result = service.isLimitedByStr(buyRuleAttrStr, userOrderCountAttrStr);
        // assert
        assertFalse(result);
    }

    @Test
    public void testIsNotLimitedByStr_BuyRuleNull() throws Throwable {
        // arrange
        String buyRuleAttrStr = null;
        String userOrderCountAttrStr = "{\"userOrderCount\":1}";
        // act & assert
        try {
            boolean result = service.isNotLimitedByStr(buyRuleAttrStr, userOrderCountAttrStr);
            assertTrue(result);
        } catch (Exception e) {
            fail("Should not throw exception");
        }
    }

    @Test
    public void testIsNotLimitedByStr_BuyRuleEmpty() throws Throwable {
        // arrange
        String buyRuleAttrStr = "";
        String userOrderCountAttrStr = "{\"userOrderCount\":1}";
        // act & assert
        try {
            boolean result = service.isNotLimitedByStr(buyRuleAttrStr, userOrderCountAttrStr);
            assertTrue(result);
        } catch (Exception e) {
            fail("Should not throw exception");
        }
    }

    @Test
    public void testIsNotLimitedByStr_MaxPerUserIs1() throws Throwable {
        // arrange
        String buyRuleAttrStr = "{\"maxPerUser\":1}";
        String userOrderCountAttrStr = "{\"userOrderCount\":0}";
        // act & assert
        try {
            boolean result = service.isNotLimitedByStr(buyRuleAttrStr, userOrderCountAttrStr);
            assertFalse(result);
        } catch (Exception e) {
            fail("Should not throw exception");
        }
    }

    @Test
    public void testIsNotLimitedByStr_MaxPerUserIs0() throws Throwable {
        // arrange
        String buyRuleAttrStr = "{\"maxPerUser\":0}";
        String userOrderCountAttrStr = "{\"userOrderCount\":1}";
        // act & assert
        try {
            boolean result = service.isNotLimitedByStr(buyRuleAttrStr, userOrderCountAttrStr);
            assertTrue(result);
        } catch (Exception e) {
            fail("Should not throw exception");
        }
    }

    @Test
    public void testIsNotLimitedByStr_OrderCountLessThanMax() throws Throwable {
        // arrange
        String buyRuleAttrStr = "{\"maxPerUser\":3}";
        String userOrderCountAttrStr = "{\"userOrderCount\":2}";
        // act & assert
        try {
            boolean result = service.isNotLimitedByStr(buyRuleAttrStr, userOrderCountAttrStr);
            assertTrue(result);
        } catch (Exception e) {
            fail("Should not throw exception");
        }
    }

    @Test
    public void testIsNotLimitedByStr_OrderCountEqualsMax() throws Throwable {
        // arrange
        String buyRuleAttrStr = "{\"maxPerUser\":3}";
        String userOrderCountAttrStr = "{\"userOrderCount\":3}";
        // act & assert
        try {
            boolean result = service.isNotLimitedByStr(buyRuleAttrStr, userOrderCountAttrStr);
            assertFalse(result);
        } catch (Exception e) {
            fail("Should not throw exception");
        }
    }

    @Test
    public void testIsNotLimitedByStr_OrderCountGreaterThanMax() throws Throwable {
        // arrange
        String buyRuleAttrStr = "{\"maxPerUser\":3}";
        String userOrderCountAttrStr = "{\"userOrderCount\":4}";
        // act & assert
        try {
            boolean result = service.isNotLimitedByStr(buyRuleAttrStr, userOrderCountAttrStr);
            assertFalse(result);
        } catch (Exception e) {
            fail("Should not throw exception");
        }
    }

    @Test
    public void testIsNotLimitedByStr_UserOrderCountNull() throws Throwable {
        // arrange
        String buyRuleAttrStr = "{\"maxPerUser\":3}";
        String userOrderCountAttrStr = null;
        // act & assert
        try {
            boolean result = service.isNotLimitedByStr(buyRuleAttrStr, userOrderCountAttrStr);
            assertTrue(result);
        } catch (Exception e) {
            fail("Should not throw exception");
        }
    }

    @Test
    public void testIsNotLimitedByStr_UserOrderCountEmpty() throws Throwable {
        // arrange
        String buyRuleAttrStr = "{\"maxPerUser\":3}";
        String userOrderCountAttrStr = "";
        // act & assert
        try {
            boolean result = service.isNotLimitedByStr(buyRuleAttrStr, userOrderCountAttrStr);
            assertTrue(result);
        } catch (Exception e) {
            fail("Should not throw exception");
        }
    }

    @Test
    public void testIsNotLimitedByStr_UserOrderCountMissingField() throws Throwable {
        // arrange
        String buyRuleAttrStr = "{\"maxPerUser\":3}";
        String userOrderCountAttrStr = "{\"someOtherField\":null}";
        // act & assert
        try {
            boolean result = service.isNotLimitedByStr(buyRuleAttrStr, userOrderCountAttrStr);
            assertTrue(result);
        } catch (Exception e) {
            fail("Should not throw exception");
        }
    }
}
