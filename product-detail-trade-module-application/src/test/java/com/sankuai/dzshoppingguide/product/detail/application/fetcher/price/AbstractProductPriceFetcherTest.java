package com.sankuai.dzshoppingguide.product.detail.application.fetcher.price;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.dianping.lion.client.Lion;
import com.dianping.lion.common.util.JsonUtils;
import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.base.Objects;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.sankuai.dealuser.price.display.api.model.BatchPriceRequest;
import com.sankuai.dealuser.price.display.api.model.ClientEnv;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.PriceResponse;
import com.sankuai.dealuser.price.display.api.model.ProductIdentity;
import com.sankuai.dz.product.detail.RequestCustomParamEnum;
import com.sankuai.dz.product.detail.gateway.spi.dto.CustomParam;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.dto.ProductPriceReturnValue;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.attr.ProductAttr;
import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
import com.sankuai.dzshoppingguide.product.detail.application.utils.VersionUtils;
import com.sankuai.dzshoppingguide.product.detail.domain.utils.LionConstants;
import com.sankuai.general.product.query.center.client.dto.AttrDTO;
import com.sankuai.sinai.data.api.dto.DpPoiBackCategoryDTO;
import com.sankuai.sinai.data.api.dto.MtPoiDTO;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Spy;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class AbstractProductPriceFetcherTest {

    @Mock
    private ProductDetailPageRequest request;

    @InjectMocks
    private AbstractProductPriceFetcher<ProductPriceReturnValue> fetcher = new AbstractProductPriceFetcher<ProductPriceReturnValue>() {

        @Override
        protected void postProcessPriceRequest(ClientEnv clientEnv, List<ProductIdentity> productIdentities, Map<String, String> extension) {
            // Implementation not needed for testing buildExtParams
        }

        @Override
        protected CompletableFuture<ProductPriceReturnValue> doFetch() {
            // Implementation not needed for testing buildExtParams
            return null;
        }
    };

    @Mock
    private CompositeAtomService compositeAtomService;

    @InjectMocks
    private TestProductPriceFetcher testFetcher;

    private BatchPriceRequest priceRequest;

    private static final long POI_ID = 123L;

    private MockedStatic<Lion> lionMock;

    private static final String APP_KEY = "com.sankuai.dzu.tpbase.dztgdetailweb";

    private static final String MIN_VERSION = "0.5.11";

    @BeforeEach
    void setUp() {
        priceRequest = new BatchPriceRequest();
    }

    public static boolean isMagicMemberValid(String mrnVersion) {
        if (StringUtils.isBlank(mrnVersion)) {
            return false;
        }
        return VersionUtils.isGreaterThanOrEqual(mrnVersion, Lion.getString("com.sankuai.dzu.tpbase.dztgdetailweb", LionConstants.MAGICAL_DP_MRN_MIN_VERSION, "0.5.11"));
    }

    /**
     * Test case for null extParam
     */
    @Test
    public void testBuildExtParamsWithNullParam() throws Throwable {
        // arrange
        when(request.getCustomParam(RequestCustomParamEnum.extparam)).thenReturn(null);
        // act
        Map<String, String> result = fetcher.buildExtParams();
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test case for empty extParam
     */
    @Test
    public void testBuildExtParamsWithEmptyParam() throws Throwable {
        // arrange
        when(request.getCustomParam(RequestCustomParamEnum.extparam)).thenReturn("");
        // act
        Map<String, String> result = fetcher.buildExtParams();
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test case for valid JSON extParam
     */
    @Test
    public void testBuildExtParamsWithValidJson() throws Throwable {
        // arrange
        Map<String, String> expectedMap = new HashMap<>();
        expectedMap.put("key1", "value1");
        expectedMap.put("key2", "value2");
        String validJson = JsonUtils.toJson(expectedMap);
        when(request.getCustomParam(RequestCustomParamEnum.extparam)).thenReturn(validJson);
        // act
        Map<String, String> result = fetcher.buildExtParams();
        // assert
        assertNotNull(result);
        assertEquals(expectedMap, result);
        assertEquals("value1", result.get("key1"));
        assertEquals("value2", result.get("key2"));
    }

    /**
     * Test case for invalid JSON extParam
     */
    @Test
    public void testBuildExtParamsWithInvalidJson() throws Throwable {
        // arrange
        String invalidJson = "{invalid:json}";
        when(request.getCustomParam(RequestCustomParamEnum.extparam)).thenReturn(invalidJson);
        // act
        Map<String, String> result = fetcher.buildExtParams();
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test when preSaleTag contains "true", should return true
     */
    @Test
    public void testIsPreSaleProduct_WhenPreSaleTagContainsTrue_ShouldReturnTrue() throws Throwable {
        // arrange
        Map<String, AttrDTO> attrMap = new HashMap<>();
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setValue(Collections.singletonList("true"));
        attrMap.put("preSaleTag", attrDTO);
        ProductAttr productAttr = new ProductAttr(attrMap);
        // act
        boolean result = fetcher.isPreSaleProduct(productAttr);
        // assert
        assertTrue(result);
    }

    /**
     * Test when preSaleTag contains other value, should return false
     */
    @Test
    public void testIsPreSaleProduct_WhenPreSaleTagContainsOtherValue_ShouldReturnFalse() throws Throwable {
        // arrange
        Map<String, AttrDTO> attrMap = new HashMap<>();
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setValue(Collections.singletonList("false"));
        attrMap.put("preSaleTag", attrDTO);
        ProductAttr productAttr = new ProductAttr(attrMap);
        // act
        boolean result = fetcher.isPreSaleProduct(productAttr);
        // assert
        assertFalse(result);
    }

    /**
     * Test when preSaleTag attribute is not present, should return false
     */
    @Test
    public void testIsPreSaleProduct_WhenPreSaleTagNotPresent_ShouldReturnFalse() throws Throwable {
        // arrange
        Map<String, AttrDTO> attrMap = new HashMap<>();
        ProductAttr productAttr = new ProductAttr(attrMap);
        // act
        boolean result = fetcher.isPreSaleProduct(productAttr);
        // assert
        assertFalse(result);
    }

    /**
     * Test when preSaleTag value list is empty, should return false
     */
    @Test
    public void testIsPreSaleProduct_WhenPreSaleTagValueListEmpty_ShouldReturnFalse() throws Throwable {
        // arrange
        Map<String, AttrDTO> attrMap = new HashMap<>();
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setValue(Collections.emptyList());
        attrMap.put("preSaleTag", attrDTO);
        ProductAttr productAttr = new ProductAttr(attrMap);
        // act
        boolean result = fetcher.isPreSaleProduct(productAttr);
        // assert
        assertFalse(result);
    }

    /**
     * Test when preSaleTag is empty string, should return false
     */
    @Test
    public void testIsPreSaleProduct_WhenPreSaleTagIsEmpty_ShouldReturnFalse() throws Throwable {
        // arrange
        Map<String, AttrDTO> attrMap = new HashMap<>();
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setValue(Collections.singletonList(""));
        attrMap.put("preSaleTag", attrDTO);
        ProductAttr productAttr = new ProductAttr(attrMap);
        // act
        boolean result = fetcher.isPreSaleProduct(productAttr);
        // assert
        assertFalse(result);
    }

    /**
     * Test when preSaleTag is blank string, should return false
     */
    @Test
    public void testIsPreSaleProduct_WhenPreSaleTagIsBlank_ShouldReturnFalse() throws Throwable {
        // arrange
        Map<String, AttrDTO> attrMap = new HashMap<>();
        AttrDTO attrDTO = new AttrDTO();
        attrDTO.setValue(Collections.singletonList("   "));
        attrMap.put("preSaleTag", attrDTO);
        ProductAttr productAttr = new ProductAttr(attrMap);
        // act
        boolean result = fetcher.isPreSaleProduct(productAttr);
        // assert
        assertFalse(result);
    }

    /**
     * Test when ProductAttr is null, should throw NullPointerException
     */
    @Test
    public void testIsPreSaleProduct_WhenProductAttrIsNull_ShouldThrowNPE() throws Throwable {
        // arrange
        ProductAttr productAttr = null;
        // act & assert
        assertThrows(NullPointerException.class, () -> {
            fetcher.isPreSaleProduct(productAttr);
        });
    }

    // Helper test implementation class
    private static class TestProductPriceFetcher extends AbstractProductPriceFetcher<ProductPriceReturnValue> {

        private long testPoiId;

        public void setTestPoiId(long poiId) {
            ProductDetailPageRequest request = new ProductDetailPageRequest();
            request.setPoiId(poiId);
            this.request = request;
        }

        @Override
        protected CompletableFuture<ProductPriceReturnValue> doFetch() {
            return null;
        }

        @Override
        protected void postProcessPriceRequest(ClientEnv clientEnv, List<ProductIdentity> productIdentities, Map<String, String> extension) {
        }
    }

    /**
     * Test successful case with valid data
     */
    @Test
    public void testGetSkuPriceDisplayDTO_Success() throws Throwable {
        // arrange
        List<PriceDisplayDTO> expectedPriceList = new ArrayList<>();
        expectedPriceList.add(new PriceDisplayDTO());
        Map<Long, List<PriceDisplayDTO>> dataMap = new HashMap<>();
        dataMap.put(POI_ID, expectedPriceList);
        PriceResponse<Map<Long, List<PriceDisplayDTO>>> response = mock(PriceResponse.class);
        when(response.isSuccess()).thenReturn(true);
        when(response.getData()).thenReturn(dataMap);
        when(compositeAtomService.batchQueryPriceWithResponse(priceRequest)).thenReturn(CompletableFuture.completedFuture(response));
        testFetcher.setTestPoiId(POI_ID);
        // act
        CompletableFuture<List<PriceDisplayDTO>> future = testFetcher.getSkuPriceDisplayDTO(priceRequest);
        // assert
        assertNotNull(future);
        assertEquals(expectedPriceList, future.join());
    }

    /**
     * Test case when response is null
     */
    @Test
    public void testGetSkuPriceDisplayDTO_NullResponse() throws Throwable {
        // arrange
        when(compositeAtomService.batchQueryPriceWithResponse(priceRequest)).thenReturn(CompletableFuture.completedFuture(null));
        testFetcher.setTestPoiId(POI_ID);
        // act
        CompletableFuture<List<PriceDisplayDTO>> future = testFetcher.getSkuPriceDisplayDTO(priceRequest);
        // assert
        assertNotNull(future);
        assertNull(future.join());
    }

    /**
     * Test case when response is not successful
     */
    @Test
    public void testGetSkuPriceDisplayDTO_FailedResponse() throws Throwable {
        // arrange
        PriceResponse<Map<Long, List<PriceDisplayDTO>>> response = mock(PriceResponse.class);
        when(response.isSuccess()).thenReturn(false);
        when(compositeAtomService.batchQueryPriceWithResponse(priceRequest)).thenReturn(CompletableFuture.completedFuture(response));
        testFetcher.setTestPoiId(POI_ID);
        // act
        CompletableFuture<List<PriceDisplayDTO>> future = testFetcher.getSkuPriceDisplayDTO(priceRequest);
        // assert
        assertNotNull(future);
        assertNull(future.join());
    }

    /**
     * Test case when data map is empty
     */
    @Test
    public void testGetSkuPriceDisplayDTO_EmptyDataMap() throws Throwable {
        // arrange
        PriceResponse<Map<Long, List<PriceDisplayDTO>>> response = mock(PriceResponse.class);
        when(response.isSuccess()).thenReturn(true);
        when(response.getData()).thenReturn(new HashMap<>());
        when(compositeAtomService.batchQueryPriceWithResponse(priceRequest)).thenReturn(CompletableFuture.completedFuture(response));
        testFetcher.setTestPoiId(POI_ID);
        // act
        CompletableFuture<List<PriceDisplayDTO>> future = testFetcher.getSkuPriceDisplayDTO(priceRequest);
        // assert
        assertNotNull(future);
        assertNull(future.join());
    }

    /**
     * Test case when data map doesn't contain poiId
     */
    @Test
    public void testGetSkuPriceDisplayDTO_NoMatchingPoiId() throws Throwable {
        // arrange
        Map<Long, List<PriceDisplayDTO>> dataMap = new HashMap<>();
        // Different POI_ID
        dataMap.put(999L, new ArrayList<>());
        PriceResponse<Map<Long, List<PriceDisplayDTO>>> response = mock(PriceResponse.class);
        when(response.isSuccess()).thenReturn(true);
        when(response.getData()).thenReturn(dataMap);
        when(compositeAtomService.batchQueryPriceWithResponse(priceRequest)).thenReturn(CompletableFuture.completedFuture(response));
        testFetcher.setTestPoiId(POI_ID);
        // act
        CompletableFuture<List<PriceDisplayDTO>> future = testFetcher.getSkuPriceDisplayDTO(priceRequest);
        // assert
        assertNotNull(future);
        assertNull(future.join());
    }

    /**
     * Test case: When mtPoiDTO is null
     * Expected: Should return empty set
     */
    @Test
    public void testGetMtPoiCategoryIds_WhenMtPoiDTOIsNull() throws Throwable {
        // arrange
        MtPoiDTO mtPoiDTO = null;
        // act
        Set<Integer> result = fetcher.getMtPoiCategoryIds(mtPoiDTO);
        // assert
        assertNotNull(result, "Result should not be null");
        assertTrue(result.isEmpty(), "Result should be empty");
    }

    /**
     * Test case: When dpBackCategoryList is null
     * Expected: Should return empty set
     */
    @Test
    public void testGetMtPoiCategoryIds_WhenDpBackCategoryListIsNull() throws Throwable {
        // arrange
        MtPoiDTO mtPoiDTO = mock(MtPoiDTO.class);
        when(mtPoiDTO.getDpBackCategoryList()).thenReturn(null);
        // act
        Set<Integer> result = fetcher.getMtPoiCategoryIds(mtPoiDTO);
        // assert
        assertNotNull(result, "Result should not be null");
        assertTrue(result.isEmpty(), "Result should be empty");
    }

    /**
     * Test case: When dpBackCategoryList is empty
     * Expected: Should return empty set
     */
    @Test
    public void testGetMtPoiCategoryIds_WhenDpBackCategoryListIsEmpty() throws Throwable {
        // arrange
        MtPoiDTO mtPoiDTO = mock(MtPoiDTO.class);
        when(mtPoiDTO.getDpBackCategoryList()).thenReturn(Lists.newArrayList());
        // act
        Set<Integer> result = fetcher.getMtPoiCategoryIds(mtPoiDTO);
        // assert
        assertNotNull(result, "Result should not be null");
        assertTrue(result.isEmpty(), "Result should be empty");
    }

    /**
     * Test case: When dpBackCategoryList contains valid categories
     * Expected: Should return set of category IDs
     */
    @Test
    public void testGetMtPoiCategoryIds_WhenDpBackCategoryListHasValidCategories() throws Throwable {
        // arrange
        MtPoiDTO mtPoiDTO = mock(MtPoiDTO.class);
        DpPoiBackCategoryDTO category1 = mock(DpPoiBackCategoryDTO.class);
        DpPoiBackCategoryDTO category2 = mock(DpPoiBackCategoryDTO.class);
        when(category1.getCategoryId()).thenReturn(1);
        when(category2.getCategoryId()).thenReturn(2);
        when(mtPoiDTO.getDpBackCategoryList()).thenReturn(Lists.newArrayList(category1, category2));
        // act
        Set<Integer> result = fetcher.getMtPoiCategoryIds(mtPoiDTO);
        // assert
        assertNotNull(result, "Result should not be null");
        assertEquals(2, result.size(), "Result should contain 2 categories");
        assertTrue(result.contains(1), "Result should contain category ID 1");
        assertTrue(result.contains(2), "Result should contain category ID 2");
    }

    /**
     * Test case: When dpBackCategoryList contains duplicate category IDs
     * Expected: Should return set of unique category IDs
     */
    @Test
    public void testGetMtPoiCategoryIds_WhenDpBackCategoryListHasDuplicateCategories() throws Throwable {
        // arrange
        MtPoiDTO mtPoiDTO = mock(MtPoiDTO.class);
        DpPoiBackCategoryDTO category1 = mock(DpPoiBackCategoryDTO.class);
        DpPoiBackCategoryDTO category2 = mock(DpPoiBackCategoryDTO.class);
        when(category1.getCategoryId()).thenReturn(1);
        when(category2.getCategoryId()).thenReturn(1);
        when(mtPoiDTO.getDpBackCategoryList()).thenReturn(Lists.newArrayList(category1, category2));
        // act
        Set<Integer> result = fetcher.getMtPoiCategoryIds(mtPoiDTO);
        // assert
        assertNotNull(result, "Result should not be null");
        assertEquals(1, result.size(), "Result should contain 1 unique category");
        assertTrue(result.contains(1), "Result should contain category ID 1");
    }

    /**
     * Test case: Input version is null
     * Expected: Should return false
     */
    @Test
    public void testIsMagicMemberValid_NullVersion() throws Throwable {
        // arrange
        String mrnVersion = null;
        // act
        boolean result = AbstractProductPriceFetcher.isMagicMemberValid(mrnVersion);
        // assert
        assertFalse(result);
    }

    /**
     * Test case: Input version is empty string
     * Expected: Should return false
     */
    @Test
    public void testIsMagicMemberValid_EmptyVersion() throws Throwable {
        // arrange
        String mrnVersion = "";
        // act
        boolean result = AbstractProductPriceFetcher.isMagicMemberValid(mrnVersion);
        // assert
        assertFalse(result);
    }

    /**
     * Test case: Input version is blank string
     * Expected: Should return false
     */
    @Test
    public void testIsMagicMemberValid_BlankVersion() throws Throwable {
        // arrange
        String mrnVersion = "   ";
        // act
        boolean result = AbstractProductPriceFetcher.isMagicMemberValid(mrnVersion);
        // assert
        assertFalse(result);
    }

    /**
     * Test case: Input version equals minimum version (0.5.11)
     * Expected: Should return true
     */
    @Test
    public void testIsMagicMemberValid_EqualVersion() throws Throwable {
        // arrange
        String mrnVersion = "0.5.11";
        // act
        boolean result = AbstractProductPriceFetcher.isMagicMemberValid(mrnVersion);
        // assert
        assertTrue(result);
    }

    /**
     * Test case: Input version greater than minimum version
     * Expected: Should return true
     */
    @Test
    public void testIsMagicMemberValid_GreaterVersion() throws Throwable {
        // arrange
        String mrnVersion = "0.5.12";
        // act
        boolean result = AbstractProductPriceFetcher.isMagicMemberValid(mrnVersion);
        // assert
        assertTrue(result);
    }

    /**
     * Test case: Input version less than minimum version
     * Expected: Should return false
     */
    @Test
    public void testIsMagicMemberValid_LessVersion() throws Throwable {
        // arrange
        String mrnVersion = "0.5.10";
        // act
        boolean result = AbstractProductPriceFetcher.isMagicMemberValid(mrnVersion);
        // assert
        assertFalse(result);
    }

    /**
     * Test case: Input version with different format but greater value
     * Expected: Should return true
     */
    @Test
    public void testIsMagicMemberValid_DifferentFormatGreaterVersion() throws Throwable {
        // arrange
        String mrnVersion = "0.5.11.1";
        // act
        boolean result = AbstractProductPriceFetcher.isMagicMemberValid(mrnVersion);
        // assert
        assertTrue(result);
    }

    /**
     * Test case: Input major version greater than minimum version
     * Expected: Should return true
     */
    @Test
    public void testIsMagicMemberValid_GreaterMajorVersion() throws Throwable {
        // arrange
        String mrnVersion = "1.0.0";
        // act
        boolean result = AbstractProductPriceFetcher.isMagicMemberValid(mrnVersion);
        // assert
        assertTrue(result);
    }
}
