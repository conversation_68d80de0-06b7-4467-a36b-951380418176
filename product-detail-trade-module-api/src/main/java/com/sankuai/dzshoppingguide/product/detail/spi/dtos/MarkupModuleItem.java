package com.sankuai.dzshoppingguide.product.detail.spi.dtos;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2025/3/3
 */
@Data
@TypeDoc(description = "加价项目信息")
@MobileDo(id = 0xb1bf)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MarkupModuleItem implements Serializable {

    @FieldDoc(description = "属性名称")
    @MobileDo.MobileField(key = 0x7ab8)
    private String name;

    @FieldDoc(description = "icon")
    @MobileDo.MobileField(key = 0x3c48)
    private String icon;

    @FieldDoc(description = "itemKey")
    @MobileDo.MobileField(key = 0x106e)
    private String itemKey;

    @FieldDoc(description = "属性信息列表")
    @MobileDo.MobileField(key = 0x2b82)
    private List<MarkupValueAttr> markupValueAttrList;
}
