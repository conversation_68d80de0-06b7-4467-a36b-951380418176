package com.sankuai.dzshoppingguide.product.detail.application.fetcher.bookingtime;

import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.dto.FetcherReturnValueDTO;
import com.sankuai.spt.statequery.api.dto.ShopBookInfoQueryResultDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ShopBookingReturnValue extends FetcherReturnValueDTO {

    private ShopBookInfoQueryResultDTO shopBookInfoQueryResultDTO;
}
