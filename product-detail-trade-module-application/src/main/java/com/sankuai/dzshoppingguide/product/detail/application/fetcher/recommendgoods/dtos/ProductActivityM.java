package com.sankuai.dzshoppingguide.product.detail.application.fetcher.recommendgoods.dtos;

import com.sankuai.dztheme.deal.dto.ActivityPicUrlDTO;
import com.sankuai.dztheme.deal.dto.enums.DealActivityTypeEnum;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Map;

/**
 * 商品活动
 *
 * Created by float.lu on 2020/9/15.
 */
@Data
public class ProductActivityM {

    /**
     * 倒计时展示控制标记（团详，商详需要关心）
     */
    private boolean timeDisplayFlag;

    /**
     * 活动剩余时间，单位：秒
     */
    private long remainingTime;

    /**
     * 距活动剩余时间，单位：毫秒
     */
    private long remainingTimeMillisecond;

    /**
     * 活动头图URL
     */
    private String url;

    /**
     * 活动图片对应的宽高比
     */
    private double urlAspectRadio;

    /**
     * 活动的标题/标签
     */
    private String lable;

    /**
     *是否为预热活动
     */
    private boolean isPreheat;

    /**
     * 活动图片列表
     */
    private List<String> urls;

    /**
     * 活动开始时间
     */
    private long activityBeginTime;

    /**
     * 活动结束时间
     */
    private long activityEndTime;

    /**
     * 货架活动类型，0：普通活动，1：完美季大促，参照：ShelfActivityTypeEnum
     */
    private Integer shelfActivityType;

    /**
     * 活动价格文案
     */
    private String activityPriceText;

    /**
     * 活动价格颜色
     */
    private String activityPriceColor;
    /**
     * 活动的跳转链接(用于弹窗预览链接)
     */
    private String jumpLink;
    /**
     * 跳转链接(用于跳转至链接)图片
     */
    private String linkImage;
    /**
     * 跳转链接(用于跳转至链接)
     */
    private String redirectLink;
    /**
     * slogan
     */
    private String slogan;
    /**
     * 活动页ID
     */
    private int pageId;

    /**
     * 活动关联的skuId
     */
    private int skuId;

    /**
     * 活动类型
     */
    private int activityType;

    /**
     * 活动主题背景图片url，有券
     */
    private String bgPicUrlWithCoupon;

    /**
     * 活动主题背景图片url，无券
     */
    private String bgPicUrlNoCoupon;

    /**
     * 商品详情页普通样式活动主题图(新版页面-大头图)
     */
    private String normalThemeBigPic;

    /**
     * 商品详情页普通样式活动主题图(新版页面-小头图)
     */
    private String normalThemeSmallPic;

    /**
     * 大头图场景图片url
     */
    private String bgPicUrlBigPic;

    /**
     * 大头图场景图片url
     */
    private String bgPicUrlSmallPic;

    /**
     * 是否有价格明细
     */
    private Boolean hasPriceDetail;

    /**
     * 售价样式活动主题图(有价格明细-大头图)
     */
    private String bgPicUrlPriceBigPic;

    /**
     * 售价样式活动主题图(有价格明细-小头图)
     */
    private String bgPicUrlPriceSmallPic;
    /**
     * 会场IP
     */
    private String activityIpPic;
    /**
     * 会场背景图
     */
    private String backGroundPic;

    /**
     * 广告胶囊ID
     */
    private String capsuleID;

    /**
     * 色变值列表
     */
    private List<String> colors;

    /**
     * 倒计时背景色
     */
    private String bgColor;

    /**
     * 倒计时文字颜色
     */
    private String textColor;

    /**
     * 优惠列表
     */
    private List<ProductPromoPriceM> promos;

    /**
     * 展示类型
     */
    private int displayType;

    /**
     * 媒体信息(图片或视频)
     */
    private MediaM media;

    /**
     *
     * 分场景需要处理的个性化属性MAP，按需使用
     */
    private Map<String, Object> activityExtraAttrs;

    /**
     * "广告位ID"
     */
    private String boothId;

    /**
     * "广告ID"
     */
    private String boothResourceId;

    /**
     * 营销的活动场景，参考营销枚举：ExposureActivitySceneEnum
     */
    private int activityScene;

    /**
     * 价格力时间配置
     * 营销枚举：com.dianping.gmkt.activity.api.enums.PriceStrengthTimeEnum
     */
    private int priceStrengthTime;

    /**
     * key为"picUrlKey", description = "图片跳转链接，参考 com.dianping.gmkt.activity.api.enums.ExposurePicUrlKeyEnum"
     */
    private Map<String, ActivityPicUrlDTO> activityPicUrlMap;

    /**
     * 曝光大促活动类型, 参考com.dianping.gmkt.activ.api.enums.ExposurePromotionTypeEnum
     */
    private int exposurePromotionType;

    /**
     * 查询指定类型的优惠价格
     * @param type
     * @return
     */
    public ProductPromoPriceM getPromoPrice(int type) {
        if (CollectionUtils.isEmpty(this.getPromos())) {
            return null;
        }
        return this.getPromos().stream().filter(productPromoPriceM -> productPromoPriceM.getPromoType() == type).findFirst().orElse(null);
    }

    public boolean ifMatchActivityType(DealActivityTypeEnum activityTypeEnum) {
        if (getShelfActivityType() == null || activityTypeEnum == null) {
            return false;
        }
        return activityTypeEnum.getType() == getShelfActivityType();
    }
}
