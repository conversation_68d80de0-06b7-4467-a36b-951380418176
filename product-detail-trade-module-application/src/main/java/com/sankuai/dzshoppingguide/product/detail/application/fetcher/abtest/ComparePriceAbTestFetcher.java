package com.sankuai.dzshoppingguide.product.detail.application.fetcher.abtest;

import com.dianping.lion.client.Lion;
import com.sankuai.douhu.absdk.bean.DouHuRequest;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategoryFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.FetcherDAGStarter;
import com.sankuai.dzshoppingguide.product.detail.domain.douhu.AbTestResult;
import com.sankuai.dzshoppingguide.product.detail.domain.douhu.DouHuService;
import com.sankuai.dzshoppingguide.product.detail.domain.utils.LionConfigUtils;
import com.sankuai.dzshoppingguide.product.detail.domain.utils.LionConstants;

import javax.annotation.Resource;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

@Fetcher(
    previousLayerDependencies = {
            FetcherDAGStarter.class,
            ProductCategoryFetcher.class,
    }
)
public class ComparePriceAbTestFetcher extends NormalFetcherContext<AbTestReturnValue> {

    @Resource
    private DouHuService douHuService;

    @Override
    protected CompletableFuture<AbTestReturnValue> doFetch() throws Exception {
        int productSecondCategoryId = getDependencyResult(ProductCategoryFetcher.class, ProductCategory.class)
                .map(ProductCategory::getProductSecondCategoryId)
                .orElse(0);

        boolean isMt = request.getClientTypeEnum().isMtClientType();
        boolean comparePriceSwitch = Lion.getBoolean("com.sankuai.dzu.tpbase.dztgdetailweb", LionConstants.COMPARE_PRICE_AB_TEST_SWITCH, false);
        if (!comparePriceSwitch) {
            return null;
        }
        Map<String, String> categoryId2ExpIdMap = LionConfigUtils.getComparePriceAssistantExpConfig();
        // 如mt304、dp304等
        String categoryId = isMt ? String.format("%s%s", "mt", productSecondCategoryId) : String.format("%s%s", "dp", productSecondCategoryId);
        if (!categoryId2ExpIdMap.containsKey(categoryId)) {
            return null;
        }
        String expId = categoryId2ExpIdMap.get(categoryId);
        // 综团比价AB实验上报
        String module = isMt ? "MtComparePrice" : "DpComparePrice";

        DouHuRequest douHuRequest = douHuService.buildDouHuRequest(request, expId);
        AbTestResult result = douHuService.getAbTestResult(douHuRequest);
        AbTestReturnValue returnValue = new AbTestReturnValue(null);
        returnValue.setAbTestResult(result);
        return CompletableFuture.completedFuture(returnValue);
    }

}
