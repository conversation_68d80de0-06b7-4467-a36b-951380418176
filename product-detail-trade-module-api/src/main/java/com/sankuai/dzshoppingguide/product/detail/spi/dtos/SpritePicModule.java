package com.sankuai.dzshoppingguide.product.detail.spi.dtos;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/2/5 10:14
 */
@Data
@TypeDoc(description = "雪碧图")
@MobileDo(id = 0x8e42)
@Builder
public class SpritePicModule implements Serializable {
    @FieldDoc(description = "雪碧图Url")
    @MobileDo.MobileField(key = 0xe52)
    private String spritePicUrl;

    @FieldDoc(description = "缩略图个数")
    @MobileDo.MobileField(key = 0x244c)
    private int totalCount;

    @FieldDoc(description = "默认固定行数")
    @MobileDo.MobileField(key = 0xb9db)
    private int row;

    @FieldDoc(description = "默认固定列数")
    @MobileDo.MobileField(key = 0x7c68)
    private int column;


    @FieldDoc(description = "单个雪碧图的尺寸")
    @MobileDo.MobileField(key = 0x6225)
    private ImagesSize spriteCellSize;

    @FieldDoc(description = "整张大图的尺寸")
    @MobileDo.MobileField(key = 0xf359)
    private ImagesSize allSpriteImageSize;
}
