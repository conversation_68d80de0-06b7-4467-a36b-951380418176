package com.sankuai.dzshoppingguide.product.detail.application.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.powermock.api.mockito.PowerMockito.mockStatic;
import static org.powermock.api.mockito.PowerMockito.when;
import com.google.common.util.concurrent.SettableFuture;
import com.meituan.service.mobile.mtthrift.netty.ContextStore;
import com.sankuai.clr.content.process.gateway.thrift.api.LeadsQueryGatewayService;
import com.sankuai.clr.content.process.gateway.thrift.dto.leads.LoadLeadsInfoReqDTO;
import com.sankuai.clr.content.process.gateway.thrift.dto.leads.LoadLeadsInfoRespDTO;
import com.sankuai.dzshoppingguide.product.detail.domain.utils.ThriftAsyncUtils;
import com.sankuai.mpmctmvacommon.resource.response.CommonRespDTO;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import org.apache.thrift.TException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.modules.junit4.PowerMockRunnerDelegate;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.util.ReflectionTestUtils;

@ExtendWith(MockitoExtension.class)
class CompositeAtomServiceImplLoadLeadsInfo1Test {

    @Mock
    private LeadsQueryGatewayService leadsQueryGatewayServiceFuture;

    @InjectMocks
    private CompositeAtomServiceImpl compositeAtomService;

    private SettableFuture<Object> settableFuture;

    @BeforeEach
    void setUp() {
        settableFuture = SettableFuture.create();
        ContextStore.setFuture(settableFuture);
    }

    @BeforeEach
    void tearDown() {
        ContextStore.removeFuture();
    }

    /**
     * 测试正常流程 - 服务调用成功返回响应
     */
    @Test
    void testLoadLeadsInfoNormal() throws Throwable {
        // arrange
        LoadLeadsInfoReqDTO request = new LoadLeadsInfoReqDTO();
        LoadLeadsInfoRespDTO expectedResponse = new LoadLeadsInfoRespDTO();
        CommonRespDTO commonResp = new CommonRespDTO();
        commonResp.setCode(0);
        commonResp.setMsg("success");
        expectedResponse.setCommonResp(commonResp);
        expectedResponse.setTitle("Test Title");
        when(leadsQueryGatewayServiceFuture.loadLeadsInfo(any(LoadLeadsInfoReqDTO.class))).thenReturn(expectedResponse);
        // act
        CompletableFuture<LoadLeadsInfoRespDTO> future = compositeAtomService.loadLeadsInfo(request);
        settableFuture.set(expectedResponse);
        // assert
        assertNotNull(future);
        LoadLeadsInfoRespDTO result = future.get();
        assertNotNull(result);
        assertEquals("Test Title", result.getTitle());
        assertEquals(0, result.getCommonResp().getCode());
        verify(leadsQueryGatewayServiceFuture).loadLeadsInfo(request);
    }

    /**
     * 测试边界场景 - 请求参数为null
     */
    @Test
    void testLoadLeadsInfoWithNullRequest() throws Throwable {
        // arrange
        LoadLeadsInfoRespDTO expectedResponse = new LoadLeadsInfoRespDTO();
        CommonRespDTO commonResp = new CommonRespDTO();
        commonResp.setCode(0);
        commonResp.setMsg("success");
        expectedResponse.setCommonResp(commonResp);
        when(leadsQueryGatewayServiceFuture.loadLeadsInfo(null)).thenReturn(expectedResponse);
        // act
        CompletableFuture<LoadLeadsInfoRespDTO> future = compositeAtomService.loadLeadsInfo(null);
        settableFuture.set(expectedResponse);
        // assert
        assertNotNull(future);
        LoadLeadsInfoRespDTO result = future.get();
        assertNotNull(result);
        assertEquals(expectedResponse, result);
        assertEquals(0, result.getCommonResp().getCode());
        verify(leadsQueryGatewayServiceFuture).loadLeadsInfo(null);
    }
}
