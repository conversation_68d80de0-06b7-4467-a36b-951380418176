package com.sankuai.dzshoppingguide.product.detail.spi.enums;

/**
 * @Author: wb_wangxiaoguang02
 * @Date: 2025/2/5 17:05
 */
public interface ModuleKeyConstants {

    /**
     * 通用数据模块for打点
     */
    String TRADE_COMMON_DATA = "module_trade_common_data";

    /**
     * 导航栏模块
     */
    String NAVIGATION_BAR = "module_detail_navigation_bar_normal";

    /**
     * 头图模块
     */
    String HEAD_PIC = "module_detail_head_pic_banner";

    /**
     * 折扣卡/付费上架会员卡模块
     */
    String MEMBER_CARD = "module_detail_discountcard";

    /**
     * 付费商家会员卡模块
     */
    String PREMIUM_MEMBER_CARD = "module_detail_deal_premium_member_card";

    /**
     * 免费商家会员卡
     */
    String FREE_MERCHANT_CARD = "module_detail_deal_free_member_card";

    /**
     * 标题模块
     */
    String TITLE = "module_detail_title";

    /**
     * 亮点模块
     */
    String HIGHLIGHTS = "module_detail_highlight";

    /**
     * 团购详情模块
     */
    String DEAL_DETAILS = "module_detail_structured_detail";

    /**
     * 服务设施
     */
    String DEAL_DETAIL_FACILITIES = "module_detail_service_facilities";


    /**
     * 图文详情模块
     */
    String DETAIL_IMAGE = "module_detail_image_text";

    /**
     * 须知条
     */
    String REMINDER_INFO = "module_detail_reminder_info_tag";

    /**
     * 须知条浮层-展开详情（与预定须知为同一module）
     */
    String REMINDER_INFO_INSTRUCTIONS = "module_detail_booking_instructions";

    /**
     * 保障条
     */
    String GUARANTEE_INFO = "module_detail_guarantee_info_tag";

    /**
     * 保障条浮层-展开详情
     */
    String GUARANTEE_INFO_TAG_POPUP = "module_detail_guarantee_info_tag_popup";

    /**
     * 美团预订评价
     * 前端采用NPM包形式接入，后端开发SPI中转接口
     */
    String MT_RESERVE_REVIEW_MODULE_V1 = "module_detail_mt_review_v1";

    /**
     * 点评预订店评价
     */
    String DP_RESERVE_SHOP_REVIEW_MODULE= "module_detail_dp_reserve_shop_review";

    /**
     * 多sku选择模块
     */
    String MODULE_DETAIL_DEAL_MULTI_SKU_SELECT = "module_detail_deal_multi_sku_select";

    /**
     * 商品详情页价格条模块
     */
    String PRICE_SALE_BAR_MODULE = "module_detail_deal_price_sale_bar";

    /**
     * 商品详情页氛围条模块
     */
    String ATMOSPHERE_PRICE_SALE_BAR_MODULE = "module_detail_deal_atmosphere_price_sale_bar";

    /**
     * 过夜服务模块
     */
    String OVER_NIGHT_SERVICE_MODULE = "module_detail_deal_over_night_service";

    /**
     * 加价详情
     */
    String MODULE_DETAIL_DEAL_MARKUP_DETAIL = "module_detail_deal_markup_detail";

    /**
     * 加价浮层
     */
    String MODULE_DETAIL_DEAL_MARKUP_LAYER = "module_detail_deal_markup_layer";

    /**
     * 加价选择条
     */
    String MODULE_DETAIL_DEAL_MARKUP_SELECT_BAR = "module_detail_deal_markup_select_bar";

    /**
     * 底Bar
     */
    String BOTTOM_BAR = "module_detail_deal_bottom_bar";

    /**
     * 公益商家
     */
    String WELFARE_INFO_MODULE = "module_detail_deal_welfare_info";
    /**
     * 优惠条tab
     */
    String PRICE_DISCOUNT_TAB_MODULE = "module_price_discount_tab";

    /**
     * 优惠明细
     */
    String PRICE_DISCOUNT_DETAIL_MODULE = "module_price_discount_detail";

    /**
     * 拼团规则
     */
    String MODULE_DETAIL_DEAL_PINTUAN_RULE = "module_detail_deal_pintuan_rule";

    /**
     * 团详IM模块
     */
    String MODULE_DETAIL_DEAL_CONSULT_MODULE = "module_detail_deal_consult_module";

    /**
     * 多买多省（搭售）
     */
    String MODULE_DETAIL_DEAL_TYING_SALE = "module_detail_deal_tying_sale";

    /**
     * 同款推荐
     */
    String MODULE_DETAIL_DEAL_SIMILAR_STYLE_GOODS= "module_detail_deal_similar_style_goods";

    /**
     * 时间选择模块
     */
    String MODULE_DETAIL_BOOKING_TIME = "module_detail_booking_time";

    /**
     * 时间选择浮层
     */
    String MODULE_DETAIL_BOOKING_TIME_POPUP = "module_detail_booking_time_popup";

    /**
     * 同店推荐模块
     */
    String MODULE_DETAIL_DEAL_IN_SHOP_RECOMMEND = "module_detail_deal_in_shop_recommend";

    /**
     * 跨店推荐模块
     */
    String MODULE_DETAIL_DEAL_CROSS_SHOP_RECOMMEND = "module_detail_deal_cross_shop_recommend";
    /**
     * 安心学月付标签模块
     */
    String EDU_STUDY_BEFORE_PAY= "module_study_before_pay_module";

    /**
     * 汇总页面AB结果
     */
    String PAGE_AB_RESULT = "page_ab_result";

    /**
     * 安心交易条(如先用后付提示条)
     */
    String MODULE_DETAIL_DEAL_EASE_TRADE_INFO = "module_detail_deal_ease_trade_Info";

    /**
     * 关联次卡模块
     */
    String MODULE_DETAIL_DEAL_TIME_CARD = "module_detail_deal_time_card";

}
