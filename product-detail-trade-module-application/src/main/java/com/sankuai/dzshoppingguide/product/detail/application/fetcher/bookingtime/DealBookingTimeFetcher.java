package com.sankuai.dzshoppingguide.product.detail.application.fetcher.bookingtime;

import com.google.common.collect.Maps;
import com.meituan.inf.xmdlog.XMDLogFormat;
import com.meituan.mdp.boot.starter.thrift.annotation.MdpThriftClient;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.spi.service.extend.NormalFetcherContext;
import com.sankuai.dzshoppingguide.product.detail.application.builder.bookingtime.DealBookingDayOffsetConstant;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.dealgroup.PlatformProductIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.dealgroup.PlatformProductIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapper;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.idmapper.shopid.ShopIdMapperFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.starter.FetcherDAGStarter;
import com.sankuai.dzshoppingguide.product.detail.application.utils.DateTimeUtils;
import com.sankuai.dzshoppingguide.product.detail.application.utils.JsonUtils;
import com.sankuai.dzshoppingguide.product.detail.application.utils.LionConfigUtils;
import com.sankuai.dzshoppingguide.product.detail.application.utils.MultiTradeUtils;
import com.sankuai.dzshoppingguide.product.detail.domain.utils.ThriftAsyncUtils;
import com.sankuai.spt.statequery.api.dto.BaseStateQueryItemDTO;
import com.sankuai.spt.statequery.api.enums.IdTypeEnum;
import com.sankuai.spt.statequery.api.enums.SubjectTypeEnum;
import com.sankuai.spt.statequery.api.request.QueryBaseStateRequest;
import com.sankuai.spt.statequery.api.response.QueryBaseStateResponse;
import com.sankuai.spt.statequery.api.service.BaseStateQueryService;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Fetcher(
        previousLayerDependencies = {FetcherDAGStarter.class, ShopIdMapperFetcher.class, PlatformProductIdMapperFetcher.class, ProductBaseInfoFetcher.class}
)
@Slf4j
public class DealBookingTimeFetcher extends NormalFetcherContext<DealBookingTimeReturnValue> {

    @MdpThriftClient(timeout = 2000, testTimeout = 5000, remoteAppKey = "com.sankuai.spt.statequery", async = true)
    private BaseStateQueryService baseStateQueryService;
    private ShopIdMapper shopIdMapper;
    private PlatformProductIdMapper platformProductIdMapper;

    @Override
    protected CompletableFuture<DealBookingTimeReturnValue> doFetch() throws Exception {
        shopIdMapper = getDependencyResult(ShopIdMapperFetcher.class);
        if (!LionConfigUtils.getOneProductMultiSwitch() || shopIdMapper == null || shopIdMapper.getMtBestShopId() <= 0L) {
            return CompletableFuture.completedFuture(null);
        }
        ProductBaseInfo productBaseInfo = getDependencyResult(ProductBaseInfoFetcher.class);
        if (!MultiTradeUtils.isMultiTradeGoods(Optional.ofNullable(productBaseInfo).map(ProductBaseInfo::getBasic).orElse(null))) {
            return CompletableFuture.completedFuture(null);
        }
        platformProductIdMapper = getDependencyResult(PlatformProductIdMapperFetcher.class);
        if (platformProductIdMapper == null || platformProductIdMapper.getPlatformProductId() <= 0L) {
            return CompletableFuture.completedFuture(null);
        }
        QueryBaseStateRequest stateRequest = buildRequest();
        baseStateQueryService.queryBaseState(stateRequest);
        CompletableFuture<QueryBaseStateResponse> cf = ThriftAsyncUtils.getThriftFuture();
        return cf.thenApply(v -> {
            if (!v.isSuccess() || v.getData() == null) {
                return null;
            }
            DealBookingTimeReturnValue dealBookingTimeReturnValue = new DealBookingTimeReturnValue();
            dealBookingTimeReturnValue.setBaseStateQueryResultDTO(v.getData());
            return dealBookingTimeReturnValue;
        }).exceptionally(e -> {
            log.error(XMDLogFormat.build()
                    .putTag("scene", "baseStateQueryService")
                    .putTag("method", "queryBaseState")
                    .message(String.format("queryBaseState error, stateRequest=%s", JsonUtils.toJson(stateRequest))), e);
            return null;
        });
    }

    private QueryBaseStateRequest buildRequest() {
        QueryBaseStateRequest stateRequest = new QueryBaseStateRequest();
        stateRequest.setQueryTime(DateTimeUtils.getCurrentDateTime());
        stateRequest.setQuerySubjectType(SubjectTypeEnum.PRODUCT_SHOP);
        stateRequest.setConditionMap(Maps.newConcurrentMap());
        stateRequest.setQueryItems(getQueryItems());
        return stateRequest;
    }

    private List<BaseStateQueryItemDTO> getQueryItems() {
        LocalDateTime now = LocalDateTime.now();
        return DealBookingDayOffsetConstant.FETCHER_DAY_OFFSETS.stream()
                .map(o -> getSingleDate(now, o))
                .collect(Collectors.toList());
    }

    private BaseStateQueryItemDTO getSingleDate(LocalDateTime now, int offset) {
        BaseStateQueryItemDTO baseStateQueryItemDTO = new BaseStateQueryItemDTO();
        Map<IdTypeEnum, Long> idTypeEnumLongMap = Maps.newHashMap();
        idTypeEnumLongMap.put(IdTypeEnum.PRODUCT_ID, platformProductIdMapper.getPlatformProductId());
        idTypeEnumLongMap.put(IdTypeEnum.SHOP_ID, shopIdMapper.getMtBestShopId());
        baseStateQueryItemDTO.setSubjectId(idTypeEnumLongMap);
        baseStateQueryItemDTO.setDay(DateTimeUtils.getDate(now.plusDays(offset)));
        return baseStateQueryItemDTO;
    }

}
