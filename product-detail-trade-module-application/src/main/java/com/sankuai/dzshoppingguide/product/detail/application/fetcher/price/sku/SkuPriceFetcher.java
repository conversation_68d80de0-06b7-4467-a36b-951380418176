package com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.sku;

import com.google.common.collect.Lists;
import com.sankuai.dealuser.price.display.api.enums.ProductTypeEnum;
import com.sankuai.dealuser.price.display.api.enums.RequestSceneEnum;
import com.sankuai.dealuser.price.display.api.model.BatchPriceRequest;
import com.sankuai.dealuser.price.display.api.model.ClientEnv;
import com.sankuai.dealuser.price.display.api.model.PriceDisplayDTO;
import com.sankuai.dealuser.price.display.api.model.ProductIdentity;
import com.sankuai.dz.api.module.arrange.framework.fetcher.metadata.annotation.Fetcher;
import com.sankuai.dz.product.detail.RequestCustomParamEnum;
import com.sankuai.dz.product.detail.gateway.spi.request.ProductDetailPageRequest;
import com.sankuai.dzshoppingguide.product.detail.application.constants.Constants;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategory;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.common.category.ProductCategoryFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.best.shop.ShopInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.poi.best.shop.ShopInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.price.AbstractProductPriceFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfo;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.base.ProductBaseInfoFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.defaultselect.SkuDefaultSelect;
import com.sankuai.dzshoppingguide.product.detail.application.fetcher.query.center.sku.defaultselect.SkuDefaultSelectFetcher;
import com.sankuai.dzshoppingguide.product.detail.application.service.CompositeAtomService;
import com.sankuai.dzshoppingguide.product.detail.application.utils.DealSkuUtils;
import com.sankuai.general.product.query.center.client.dto.deal.DealGroupDealDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: <EMAIL>
 * @Date: 2025/3/1
 */
@Fetcher(
        previousLayerDependencies = {
                ProductBaseInfoFetcher.class,
                ShopInfoFetcher.class,
                SkuDefaultSelectFetcher.class
        }
)
@Slf4j
public class SkuPriceFetcher extends AbstractProductPriceFetcher<SkuPriceReturnValue> {

    @Resource
    private CompositeAtomService compositeAtomService;

    @Override
    protected void postProcessPriceRequest(ClientEnv clientEnv, List<ProductIdentity> productIdentities, Map<String, String> extension) {
        // 清空商品ID列表
        productIdentities.clear();
        ProductBaseInfo productBaseInfo = getDependencyResult(ProductBaseInfoFetcher.class);
        List<Long> skuIds = getAllSkuIds(productBaseInfo);
        // 多SKU查询参数
        skuIds.forEach(skuId -> {
            ProductIdentity productIdentity = new ProductIdentity((int)request.getProductId(), ProductTypeEnum.DEAL.getType(), skuId.intValue());
            productIdentity.setExtParams(buildExtParams());
            productIdentities.add(productIdentity);
        });
    }

    @Override
    protected CompletableFuture<SkuPriceReturnValue> doFetch() {
        if (!SUPPORT_CLIENT_TYPE.contains(request.getClientTypeEnum())) {
            ProductBaseInfo productBaseInfo = getDependencyResult(ProductBaseInfoFetcher.class);
            List<PriceDisplayDTO> defaultPriceDisplayDTO = buildDefaultPriceDisplayList(productBaseInfo);
            return CompletableFuture.completedFuture(new SkuPriceReturnValue(defaultPriceDisplayDTO));
        }
        ProductCategory productCategory = getDependencyResult(ProductCategoryFetcher.class);
        SkuDefaultSelect skuDefaultSelect = getDependencyResult(SkuDefaultSelectFetcher.class);
        // 1. 分销场景
        if (isODP(request.getClientTypeEnum(), request.getPageSource())) {
            return buildODPPriceDisplayReturnValue(request, skuDefaultSelect);
        }
        // 2. 点评百度微信小程序
        if (isDpBaiduMapXcx(request.getClientTypeEnum())) {
            return buildDpBaiDuMapXcxPriceDisplayReturnValue(request, skuDefaultSelect);
        }
        // 3. 美团美播小程序
        if (isMtLiveXcx(request.getClientTypeEnum())) {
            return buildMtLiveMiniAppPriceDisplayReturnValue(request, skuDefaultSelect);
        }
        ShopInfo shopInfo = getDependencyResult(ShopInfoFetcher.class, ShopInfo.class).orElse(new ShopInfo());
        Set<Integer> poiCategoryIds = getPoiCategoryIds(request.getClientTypeEnum(), shopInfo);
        CompletableFuture<SkuPriceReturnValue> defaultSkuNormalPriceResult = buildDefaultNormalPriceDisplayReturnValue(request, skuDefaultSelect, poiCategoryIds, productCategory.getProductSecondCategoryId());
        // 门店类目和团单类目限制
        if (isMerchantMember(request.getClientTypeEnum(), productCategory.getProductSecondCategoryId())) {
            // 比价
            CompletableFuture<SkuPriceReturnValue> merchantMemberSkuNormalPriceResult = buildMerchantMemberPriceDisplayReturnValue(request, skuDefaultSelect);
            return CompletableFuture.allOf(defaultSkuNormalPriceResult, merchantMemberSkuNormalPriceResult).thenApply(v -> {
                SkuPriceReturnValue defaultNormalSkuPrice = defaultSkuNormalPriceResult.join();
                SkuPriceReturnValue merchantMemberNormalSkuPrice = merchantMemberSkuNormalPriceResult.join();
                // 比较各个sku，取较低价
                return getLowerPriceDisplayReturnValue(defaultNormalSkuPrice, merchantMemberNormalSkuPrice);
            });
        }
        return defaultSkuNormalPriceResult;
    }

    private SkuPriceReturnValue getLowerPriceDisplayReturnValue(SkuPriceReturnValue normalSkuPrice, SkuPriceReturnValue memberNormalSkuPrice) {
        if (Objects.isNull(normalSkuPrice) || CollectionUtils.isEmpty(normalSkuPrice.getPriceDisplayDTOList())) {
            return memberNormalSkuPrice;
        }
        if (Objects.isNull(memberNormalSkuPrice) || CollectionUtils.isEmpty(memberNormalSkuPrice.getPriceDisplayDTOList())) {
            return normalSkuPrice;
        }
        List<PriceDisplayDTO> result = Lists.newArrayList();
        // 比较sku价格
        Map<Long, PriceDisplayDTO> memberSkuId2PriceMap = getSkuId2PriceDisplayMap(memberNormalSkuPrice.getPriceDisplayDTOList());
        for (PriceDisplayDTO normalPriceDisplay : normalSkuPrice.getPriceDisplayDTOList()) {
            BigDecimal normalPrice = normalPriceDisplay.getPrice();
            long skuId = normalPriceDisplay.getIdentity().getSkuId();
            PriceDisplayDTO memberPriceDisplay = memberSkuId2PriceMap.get(skuId);
            BigDecimal memberPrice = Objects.nonNull(memberPriceDisplay) ? memberPriceDisplay.getPrice() : new BigDecimal(0);
            if (normalPrice.compareTo(memberPrice) <= 0) {
                result.add(normalPriceDisplay);
            } else {
                result.add(memberPriceDisplay);
            }
        }
        return new SkuPriceReturnValue(result);
    }

    private CompletableFuture<SkuPriceReturnValue> buildODPPriceDisplayReturnValue(ProductDetailPageRequest pageRequest, SkuDefaultSelect skuDefaultSelect) {
        BatchPriceRequest priceRequest = buildBatchPriceRequest(pageRequest, skuDefaultSelect);
        priceRequest.setScene(RequestSceneEnum.PROMO_DETAIL_DESC_RETAIL.getScene());
        return getSkuPriceDisplayDTO(priceRequest).thenApply(SkuPriceReturnValue::new);
    }

    private CompletableFuture<SkuPriceReturnValue> buildDpBaiDuMapXcxPriceDisplayReturnValue(ProductDetailPageRequest pageRequest, SkuDefaultSelect skuDefaultSelect) {
        BatchPriceRequest priceRequest = buildBatchPriceRequest(pageRequest, skuDefaultSelect);
        priceRequest.setScene(RequestSceneEnum.PROMO_DETAIL_NO_ITEM_DESC.getScene());
        return getSkuPriceDisplayDTO(priceRequest).thenApply(SkuPriceReturnValue::new);
    }

    private CompletableFuture<SkuPriceReturnValue> buildMtLiveMiniAppPriceDisplayReturnValue(ProductDetailPageRequest pageRequest, SkuDefaultSelect skuDefaultSelect) {
        BatchPriceRequest priceRequest = buildBatchPriceRequest(pageRequest, skuDefaultSelect);
        priceRequest.setScene(RequestSceneEnum.NO_PROMO.getScene());
        return getSkuPriceDisplayDTO(priceRequest).thenApply(SkuPriceReturnValue::new);
    }

    private CompletableFuture<SkuPriceReturnValue> buildDefaultNormalPriceDisplayReturnValue(ProductDetailPageRequest pageRequest, SkuDefaultSelect skuDefaultSelect, Set<Integer> poiCategoryIds, int secondCategoryId) {
        BatchPriceRequest priceRequest = buildBatchPriceRequest(pageRequest, skuDefaultSelect);
        if (isFootMassageScene(pageRequest.getClientTypeEnum(), poiCategoryIds, secondCategoryId)) {
            priceRequest.setScene(RequestSceneEnum.PROMO_DETAIL_DESC_WithDealPromo.getScene());
        } else {
            priceRequest.setScene(RequestSceneEnum.PROMO_DETAIL_NO_ITEM_DESC.getScene());
        }
        return getSkuPriceDisplayDTO(priceRequest).thenApply(SkuPriceReturnValue::new);
    }

    private CompletableFuture<SkuPriceReturnValue> buildMerchantMemberPriceDisplayReturnValue(ProductDetailPageRequest pageRequest, SkuDefaultSelect skuDefaultSelect) {
        BatchPriceRequest priceRequest = buildBatchPriceRequest(pageRequest, skuDefaultSelect);
        priceRequest.setScene(RequestSceneEnum.PROMO_DETAIL_NO_ITEM_DESC_WithMerchantMember.getScene());
        return getSkuPriceDisplayDTO(priceRequest).thenApply(SkuPriceReturnValue::new);
    }

    /**
     * 获取skuId2PriceDisplayMap
     * @param priceDisplayDTOS 价格信息列表
     * @return Map<Long, PriceDisplayDTO> skuId到价格信息的映射
     */
    private Map<Long, PriceDisplayDTO> getSkuId2PriceDisplayMap(List<PriceDisplayDTO> priceDisplayDTOS) {
        return priceDisplayDTOS
                .stream()
                .collect(Collectors.toMap(p -> (long)p.getIdentity().getSkuId(), Function.identity(), (x, y) -> x));
    }

    private List<PriceDisplayDTO> buildDefaultPriceDisplayList(ProductBaseInfo productBaseInfo) {
        if (Objects.isNull(productBaseInfo) || CollectionUtils.isEmpty(productBaseInfo.getDeals())) {
            return null;
        }
        List<PriceDisplayDTO> defaultPriceDisplayList = Lists.newArrayList();
        for (DealGroupDealDTO deal : productBaseInfo.getDeals()) {
            if (Objects.isNull(deal.getPrice()) || StringUtils.isBlank(deal.getPrice().getSalePrice())) {
                continue;
            }
            BigDecimal finalPrice = new BigDecimal(deal.getPrice().getSalePrice());
            BigDecimal marketPrice = new BigDecimal(deal.getPrice().getMarketPrice());
            PriceDisplayDTO priceDisplayDTO = new PriceDisplayDTO();
            priceDisplayDTO.setPrice(finalPrice);
            priceDisplayDTO.setMarketPrice(marketPrice);
            priceDisplayDTO.getIdentity().setSkuId(Math.toIntExact(deal.getDealId()));
            defaultPriceDisplayList.add(priceDisplayDTO);
        }
        return defaultPriceDisplayList;
    }

    private List<Long> getAllSkuIds(ProductBaseInfo baseInfo) {
        if (Objects.isNull(baseInfo) || CollectionUtils.isEmpty(baseInfo.getDeals())) {
            return Lists.newArrayList();
        }
        List<DealGroupDealDTO> dealGroupDealDTOS = Lists.newArrayList();
        // 如果是电子数码类
        String flowFlag = request.getCustomParam(RequestCustomParamEnum.flowFlag);
        if (Objects.equals(flowFlag, Constants.COUNTRY_SUBSIDIES)) {
            dealGroupDealDTOS = DealSkuUtils.getDigitalApplianceDealSkuList(baseInfo.getDeals());
        } else {
            dealGroupDealDTOS = DealSkuUtils.getValidDealSkuList(baseInfo.getDeals());
        }
        return dealGroupDealDTOS.stream().map(DealGroupDealDTO::getDealId).filter(Objects::nonNull).collect(Collectors.toList());
    }
}
