package com.sankuai.dzshoppingguide.product.detail.spi.dtos;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: <EMAIL>
 * @Date: 2025/2/8
 */
@Data
@TypeDoc(description = "商品详情页顶部导航栏项")
@MobileDo(id = 0x3a20)
public class NavBarItem implements Serializable {
    @FieldDoc(description = "导航项类型，1收藏 2分享 3更多 4首页 5消息  6门店 7举报")
    @MobileDo.MobileField(key = 0x8f0c)
    private int type;

    @FieldDoc(description = "嵌套型导航栏")
    @MobileDo.MobileField(key = 0xb627)
    private List<NavBarItem> popover;

    @FieldDoc(description = "点击导航图标跳转地址")
    @MobileDo.MobileField(key = 0x774e)
    private String jumpUrl;

    @FieldDoc(description = "导航栏项配置列表")
    @MobileDo.MobileField(key = 0xaf0a)
    private List<NavBarItemConf> conf;
}
