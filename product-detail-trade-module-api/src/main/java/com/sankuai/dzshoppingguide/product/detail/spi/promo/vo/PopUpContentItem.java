package com.sankuai.dzshoppingguide.product.detail.spi.promo.vo;

import java.io.Serializable;

import com.dianping.mobile.framework.annotation.MobileDo;
import com.dianping.mobile.framework.annotation.MobileDo.MobileField;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;

import lombok.Data;

/**
 * @desc 弹窗内容项
 */
@TypeDoc(description = "弹窗内容项")
@Data
@MobileDo(id = 0x202d06e0)
public class PopUpContentItem implements Serializable {
    /**
    * 内容
    */
    @FieldDoc(description = "内容")
    @MobileField(key = 0x5d02)
    private String itemContent;

    /**
    * 子标题
    */
    @FieldDoc(description = "子标题")
    @MobileField(key = 0xd4c5)
    private String itemTitle;
}
